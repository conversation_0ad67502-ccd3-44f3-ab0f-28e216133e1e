import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiMachineOeeDetail() {
  return {
    GetAllMachineOeeDetail: createApiFunction("get", "oee/trans_detail_machine/list"),
    GetAllActiveMachineOeeDetail: createApiFunction("get", "oee/trans_detail_machine/list-active"),
    GetMachineOeeDetailById: createApiFunction("post", "oee/trans_detail_machine/id"),
    GetMachineOeeDetailByIdHeader: createApiFunction("post", "oee/trans_detail_machine/id-header"),
    CreateMachineOeeDetail: createApiFunction("post", "oee/trans_detail_machine/create"),
    CreateBatchMachineOeeDetail: createApiFunction("post", "oee/trans_detail_machine/create-batch"),
    UpdateBatchMachineOeeDetail: createApiFunction("post", "oee/trans_detail_machine/update-batch"),
    EditMachineOeeDetail: createApiFunction("put", "oee/trans_detail_machine/edit"),
    EditStatusMachineOeeDetail: createApiFunction("put", "oee/trans_detail_machine/edit-status"),
  };
}
