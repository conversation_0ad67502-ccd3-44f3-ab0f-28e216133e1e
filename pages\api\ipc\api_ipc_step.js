import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_REAGEN}/v2/ipc/ipc_step/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiIpcStep() {
  return {
    getAllIpcStep: createApiFunction("get", "list"),
    getAllActiveIpcStep: createApiFunction("get", "list-active"),
    createIpcStep: createApiFunction("post", "create"),
    GetIpcStepById: createApiFunction("post", "id"),
    editIpcStep: createApiFunction("put", "edit"),
    editStatusIpcStep: createApiFunction("put", "edit-status"),
  };
}
