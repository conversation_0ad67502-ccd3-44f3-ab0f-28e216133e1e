import ipcApi from "@/pages/api/ipcApi";
import { faDownload, faFileDownload } from "@fortawesome/free-solid-svg-icons";
import Head from "next/head";
import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button, Stack, Table } from "rsuite";
import API_IPC from "@/pages/api/api_ipc";
import ChartDataLabels from "chartjs-plugin-datalabels";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
} from "chart.js";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
  ChartDataLabels
);

import { Line } from "react-chartjs-2";

export default function PdfPreview({
  ssr_batch_code,
  ssr_dashboarddata,
  ssr_reasondata,
}) {
  const [loading, setLoading] = useState(false);

  const [stateShowDownload, setStateShowDownload] = useState(true);
  const [showHardness, setShowHardness] = useState(true);
  const [showThickness, setShowThickness] = useState(true);
  const [clickDownload, setClickDownload] = useState(false);
  const [sessionAuth, setSessionAuth] = useState(null);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } 
  },[])

  let rowNumber = 1;

  const generatePDF = async () => {
    document.getElementById("headdownload").remove();
  
    // Delay 7 seconds before generating the PDF
    await new Promise((resolve) => setTimeout(resolve, 2000));
  
    const input = document.getElementById("printedDocument");
    const pdf = new jsPDF("p", "mm", "a4");
  
    const canvas = await html2canvas(input);
    const imageData = canvas.toDataURL("image/png");
  
    pdf.addImage(imageData, "PNG", 0, 0, 210, 297); // A4 size
    pdf.save(`Print Out IPC BatchCode ${ssr_batch_code}`);
  };
  

  const generatePDFHardness = async () => {
    setShowThickness(false) 
  };

  useEffect(() => {
    if (!showThickness) {
      // Execute the PDF generation logic after showThickness is set to false
      generatePDF();
    }
  }, [showThickness]);

 

  const generatePDFThickness = async () => {
    setShowHardness(false)   
  };

  useEffect(() => {
    if (!showHardness) {
      // Execute the PDF generation logic after showHardness is set to false
      generatePDF();
    }
  }, [showHardness]);

    useEffect(() => {
      // window.print()
      // if (stateClicked) {
      //   const status = !stateClicked
      //   setStateClicked(status)
      //   //document.getElementById("headdownload").append()
      // }
      // console.log("first")
      
      if (clickDownload) {
        setClickDownload(false)
        window.print()          
      }

      // console.log(ssr_dashboarddata)
      setStateShowDownload(true)
    }, [stateShowDownload]);
    


    const prepareHardnessChartData = (data) => {
      const labels = data.map((item) => item.info);
      const avg = data.map((item) => item.AVG_H);
      const max = data.map((item) => item.SPEC_MAX_H);
      const min = data.map((item) => item.SPEC_MIN_H);
  
      return {
        labels,
        datasets: [
          {
            label: "Avg Hardness",
            data: avg,
            backgroundColor: "#FF0000",
            borderColor: "#FF0000",
          },
          {
            label: "Max Spec Hardness",
            data: max,
            backgroundColor: "#0000FF",
            borderColor: "#0000FF",
          },
          {
            label: "Min Spec Hardness",
            data: min,
            backgroundColor: "#FFA500",
            borderColor: "#FFA500",
          },
        ],
      };
    };
  
    const LineHardnessChart = ({ chartData }) => {
      const options = {
        responsive: true,
        plugins: {
          legend: {
            position: "bottom",
          },
          title: {
            display: true,
            text: `${ssr_batch_code} IPC: HARDNESS`,
            padding: { 
              bottom: 50, 
              top: 20,
          },
          },
          datalabels: {
            anchor: "end",
            align: "end",
            color: function (context) {
              return context.dataset.backgroundColor;
            },
            font: {
              weight: "bold",
            },
            formatter: (value, context) => {
              const formattedValue = value.toLocaleString();
              return formattedValue;
            },
          },
        },
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      };
  
      return <Line data={chartData} options={options} />;
    };
  
    const prepareDiameterChartData = (data) => {
      const labels = data.map((item) => item.info);
      const avg = data.map((item) => item.AVG_D);
      const max = data.map((item) => item.SPEC_MAX_D);
      const min = data.map((item) => item.SPEC_MIN_D);
  
      return {
        labels,
        datasets: [
          {
            label: "Avg Diameter",
            data: avg,
            backgroundColor: "#FF0000",
            borderColor: "#FF0000",
          },
          {
            label: "Max Spec Diameter",
            data: max,
            backgroundColor: "#0000FF",
            borderColor: "#0000FF",
          },
          {
            label: "Min Spec Diameter",
            data: min,
            backgroundColor: "#FFA500",
            borderColor: "#FFA500",
          },
        ],
      };
    };
  
    const LineDiameterChart = ({ chartData }) => {
      const options = {
        responsive: true,
        plugins: {
          legend: {
            position: "bottom",
          },
          title: {
            display: true,
            text: `${ssr_batch_code} IPC: DIAMETER`,
            padding: { 
              bottom: 50, 
              top: 20,
          },
          },
          datalabels: {
            anchor: "end",
            align: "end",
            color: function (context) {
              return context.dataset.backgroundColor;
            },
            font: {
              weight: "bold",
            },
            formatter: (value, context) => {
              const formattedValue = value.toLocaleString();
              return formattedValue;
            },
          },
        },
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      };
  
      return <Line data={chartData} options={options} />;
    };
    
    const prepareThicknessChartData = (data) => {
      const labels = data.map((item) => item.info);
      const avg = data.map((item) => item.AVG_T);
      const max = data.map((item) => item.SPEC_MAX_T);
      const min = data.map((item) => item.SPEC_MIN_T);
  
      return {
        labels,
        datasets: [
          {
            label: "Avg Thickness",
            data: avg,
            backgroundColor: "#FF0000",
            borderColor: "#FF0000",
          },
          {
            label: "Max Spec Thickness",
            data: max,
            backgroundColor: "#0000FF",
            borderColor: "#0000FF",
          },
          {
            label: "Min Spec Thickness",
            data: min,
            backgroundColor: "#FFA500",
            borderColor: "#FFA500",
          },
        ],
      };
    };
  
    const LineThicknessChart = ({ chartData }) => {
      const options = {
        responsive: true,
        plugins: {
          legend: {
            position: "bottom",
          },
          title: {
            display: true,
            text: `${ssr_batch_code} IPC: THICKNESS`,
            padding: { 
              bottom: 50, 
              top: 20,
          },
          },
          datalabels: {
            anchor: "end",
            align: "end",
            color: function (context) {
              return context.dataset.backgroundColor;
            },
            font: {
              weight: "bold",
            },
            formatter: (value, context) => {
              const formattedValue = value.toLocaleString();
              return formattedValue;
            },
          },
        },
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      };
  
      return <Line data={chartData} options={options} />;
    };
  


  return (
    <>
      <Head>
        <title>
          Print out Reporting Measurement - In Process Control {ssr_batch_code}
        </title>
      </Head>
      <div>
      {stateShowDownload &&(
        <div
          id="headdownload"
          style={{
            width: "100%",
            padding: "1em",
            backgroundColor: "#2c2c30",
            boxShadow: "2px 2px 10px #6c6b75",
            position: "-webkit-sticky",
            position: "sticky",
            top: 0,
          }}
        >
          <Stack direction="horizontal" justifyContent="space-between" alignItems="center">
          {/* Left Section: Text */}
          <Stack>
            <p style={{ color: "white", fontSize: "1em", textAlign: "left" }}>
              Print Out Reporting Measurement - In Process Control ID : {ssr_batch_code}
            </p>
          </Stack>

          {/* Right Section: Buttons */}
          <Stack direction="vertical" spacing={4}>
            <Button title="Download" onClick={generatePDFHardness}>
              Print Hardness
            </Button>
            <Button title="Download" onClick={generatePDFThickness}>
              Print Thickness
            </Button>
            <Button title="Download" onClick={generatePDF}>
              Print All
            </Button>
          </Stack>
        </Stack>

        </div>
        )}
        <div style={{ width: "100%", backgroundColor: "#65656b" }}>
          <div
            id="printedDocument"
            style={{
              width: "100%",
              backgroundColor: "white",
              margin: "auto",
              padding: "1.5em",
            }}
          >
            <div>
              <img
                src="/Logo_kalbe_detail.png"
                alt="Logo_kalbe_detail.png"
                style={{ width: 150 }}
              />
            </div>
            <div style={{ marginBottom: "2em", marginTop: "2em" }}>
              <p
                style={{
                  textAlign: "center",
                  fontWeight: "bold",
                  fontSize: "2em",
                }}
              >
                REPORTING MEASUREMENT {ssr_batch_code}
              </p>
            </div>
            <div>

              {showHardness && (
                   <LineHardnessChart
                   chartData={prepareHardnessChartData(ssr_dashboarddata)}
                   plugins={[ChartDataLabels]}
                   loading={loading}
                 />
              )}
           

              {/* <LineDiameterChart
                chartData={prepareDiameterChartData(ssr_dashboarddata)}
                plugins={[ChartDataLabels]}
                loading={loading}
              /> */}

              {showThickness && (
                  <LineThicknessChart
                  chartData={prepareThicknessChartData(ssr_dashboarddata)}
                  plugins={[ChartDataLabels]}
                  loading={loading}
                />
              )}

            

              <br />
              {ssr_reasondata.length > 0 && (
               <div style={{ overflowX: "auto", width: "100%" }}>
               <table style={{ width: "100%", borderCollapse: "collapse" }}>
                 <thead>
                   <tr>
                     <th style={{ border: "1px solid #ddd", padding: "8px", textAlign: "center" }}>No</th>
                     <th style={{ border: "1px solid #ddd", padding: "8px", textAlign: "center" }}>Product Code</th>
                     <th style={{ border: "1px solid #ddd", padding: "8px", textAlign: "center" }}>Info</th>
                     <th style={{ border: "1px solid #ddd", padding: "8px", textAlign: "center" }}>Reason</th>
                     <th style={{ border: "1px solid #ddd", padding: "8px", textAlign: "center" }}>Operator</th>
                     <th style={{ border: "1px solid #ddd", padding: "8px", textAlign: "center" }}>Approval by</th>
                   </tr>
                 </thead>
                 <tbody>
                   {ssr_reasondata.map((rowData, index) => (
                     <tr key={index}>
                       <td style={{ border: "1px solid #ddd", padding: "8px", textAlign: "center" }}>{index + 1}</td>
                       <td style={{ border: "1px solid #ddd", padding: "8px" }}>{rowData.product_code}</td>
                       <td style={{ border: "1px solid #ddd", padding: "8px" }}>{rowData.info}</td>
                       <td style={{ border: "1px solid #ddd", padding: "8px" }}>{rowData.reason}</td>
                       <td style={{ border: "1px solid #ddd", padding: "8px" }}>{rowData.operator_created}</td>
                       <td style={{ border: "1px solid #ddd", padding: "8px" }}>{rowData.approve_details}</td>
                     </tr>
                   ))}
                 </tbody>
               </table>
             </div>
             
              )}  

              <br />
              <p>
                {`Print Out By : ${sessionAuth?.employee_id} - ${sessionAuth?.employee_name} | Printed Date: ${new Date().toLocaleDateString('en-GB')}`}
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export async function getServerSideProps(context) {

  const {getIpcDashboard, getIpcReason} = API_IPC()
  const { query } = context;
  const { ssr_batch_code } = query;



  const res = await getIpcDashboard()

  const resReason = await getIpcReason()

  let ssr_dashboarddata = []
  let ssr_reasondata = []
  // console.log("res", res)
  if (res.status == 200) {
    const filteredTransactions = res.data.filter(transaction => transaction.product_code === ssr_batch_code);
    ssr_dashboarddata = filteredTransactions
  }

  if (resReason.status == 200) {
    const filteredTransactionsReason = resReason.data.filter(transaction => transaction.product_code === ssr_batch_code);
    ssr_reasondata = filteredTransactionsReason
  }

  

  

  return {
    props: {
      ssr_batch_code,
      ssr_dashboarddata,
      ssr_reasondata
    },
  };
}
