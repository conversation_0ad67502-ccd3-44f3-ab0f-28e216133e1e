import MainContent from "@/components/layout/MainContent";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import ModuleApi from "../api/moduleApi";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";

function UserMapping({ allModule, allSelectedModule, emp_id, emp_name }) {
  const { PostTransmapApi } = ModuleApi();
  const router = useRouter();
  const MySwal = withReactContent(Swal);
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [moduleData, setModuleData] = useState([]);
  const [selectedModules, setSelectedModules] = useState([]);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  });

  useEffect(() => {
    // GetModuleData();
    if (allModule.length > 0) {
      setModuleData(allModule);
    }
    if (allSelectedModule) {
      const selectedData = allSelectedModule.map((item) => `${item}`);
      setSelectedModules([...selectedData]);
    }
  }, [allModule, allSelectedModule]);

  // Handler
  const checkboxHandler = (event) => {
    const selectedItem = event.target.value;
    const isChecked = event.target.checked;

    if (isChecked) {
      // Menambahkan nilai ke array jika checkbox tercentang
      setSelectedModules([...selectedModules, selectedItem]);
    } else {
      // Menghapus nilai dari array jika checkbox tidak tercentang
      const updatedModules = selectedModules.filter(
        (item) => item != selectedItem
      );
      setSelectedModules(updatedModules);
    }
  };

  // Handler awal checked
  const isChecked = (code) => {
    let result;
    for (let module of selectedModules) {
      if (code === module) {
        result = true;
        break;
      } else {
        result = false;
      }
    }
    return result;
  };

  // submit handler
  const submitUserMapping = async (event) => {
    event.preventDefault();
    setIsFormDisabled(true);

    if (selectedModules.length === 0) {
      setIsFormDisabled(false);
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please select Module.",
      });
      return;
    }

    const reqData = {
      employee_id: emp_id,
      module_code: selectedModules.map((item) => parseInt(item)),
    };

    const { Data } = await PostTransmapApi(reqData);
    if (Data) {
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "User Mapping inserted successfully.",
        allowOutsideClick: false,
        timer: 1000,
      });
      router.push("/user_management");
    } else {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "User Mapping insert FAILED.",
      });
      setIsFormDisabled(false);
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>User Mapping</title>
        </Head>
      </div>
      <ContainerLayout
        title="User Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <h4>Form User Mapping</h4>
          <div className="p-5">
            <form onSubmit={submitUserMapping}>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Employee ID
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  id="employeeId"
                  value={emp_id}
                  aria-describedby="validationServer03Feedback"
                  disabled={true}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="employee_name" className="form-label">
                  Employee Name
                </label>
                <input
                  type="text"
                  className="form-control mb-5"
                  id="employee_name"
                  value={emp_name}
                  aria-describedby="emailHelp"
                  disabled={true}
                />
              </div>
              <div className="mb-3">
                <label>Pilih module : </label>
                {moduleData.map((item) => (
                  <div class="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={isChecked(`${item.Module_Code}`)}
                      disabled={isFormDisabled}
                      value={`${item.Module_Code}`}
                      id={`${item.Module_Code}`}
                      onChange={checkboxHandler}
                    />
                    <label
                      class="form-check-label"
                      htmlFor={`${item.Module_Code}`}
                    >
                      {item.Module_Name}
                    </label>
                  </div>
                ))}
              </div>

              <button
                type="submit"
                disabled={isFormDisabled}
                className="btn btn-primary p-2"
              >
                OK
              </button>
              <button
                type="button"
                disabled={isFormDisabled}
                className="btn btn-dark mx-2 p-2"
                onClick={() => router.back()}
              >
                Cancel
              </button>
            </form>
          </div>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  const { emp_id, emp_name } = query;

  const { GetModuleAll, GetTransmapModuleApi } = ModuleApi();
  const { Data: allModuleData } = await GetModuleAll();
  let allModule = [];
  if (allModuleData != undefined) {
    allModule = allModuleData;
  }

  const { Data: allSelectedModuleData } = await GetTransmapModuleApi(emp_id);
  let allSelectedModule = [];
  if (allSelectedModuleData != undefined) {
    allSelectedModule = allSelectedModuleData;
  }

  return {
    props: {
      allSelectedModule,
      allModule,
      emp_id,
      emp_name,
    },
  };
}

export default UserMapping;
