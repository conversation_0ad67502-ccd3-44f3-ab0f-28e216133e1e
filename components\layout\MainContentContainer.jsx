import Header from "@/components/layout/Header";
import CustomizedAccordions from "@/components/CustomizedAccordion";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";

export default function MainContentContainer(props) {
  const session = useSession();
  const router = useRouter();

  if (session.status === "unauthenticated") router.push("/");

  if (session.status === "authenticated") {
    return (
      <>
        <div className="container-fluid p-4">
          <div className="row">{props.children}</div>
        </div>
      </>
    );
  }
}
