import React, { useState, useEffect, createContext } from "react";

// const SidebarMenuUserModuleContext = React.createContext({
//   // parentMenu: [],
//   // childMenu: [],
//   // menuIcon: "",
//   // setParentMenu: () => {},
//   // setChildMenu: () => {},
// });

export const SidebarMenuUserModuleData = createContext(null);

const SidebarMenuContext = ({ children }) => {
  const [parentMenu, setParentMenu] = useState([]);
  const [childMenu, setChildMenu] = useState([]);
  const [menuIcon, setMenuIcon] = useState("");

  return (
    <SidebarMenuUserModuleData.Provider
      value={{
        parentMenu,
        setParentMenu,
        childMenu,
        setChildMenu,
        menuIcon,
        setMenuIcon,
      }}
    >
      {children}
    </SidebarMenuUserModuleData.Provider>
  );
};

export default SidebarMenuContext;
// export const SidebarMenuUserModuleContextProvider = (props) => {
//   const [parentMenuData, setParentMenuData] = useState([]);
//   const [childMenuData, setChildMenuData] = useState([]);

//   const setupParentMenu = (value) => {
//     setParentMenuData([...value]);
//   };

//   const setupChildMenu = (value) => {
//     setChildMenuData([...value]);
//   };

//   return (
//     <SidebarMenuContext.Provider
//       value={{
//         parentMenu: parentMenuData,
//         childMenu: childMenuData,
//         setParentMenu: setupParentMenu,
//         setChildMenu: setupChildMenu,
//       }}
//     >
//       {props.children}
//     </SidebarMenuContext.Provider>
//   );
// };
