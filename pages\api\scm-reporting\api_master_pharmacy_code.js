import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/scm_reporting/master_pharmacy_code/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};

export default function MasterPharmacyCodeApi(){
    return {
        getAll: createApiFunction("get", "get/all"),
        getAllActive: createApiFunction("get", "get/all-active"),
        create: createApiFunction("post", "create"),
        update: createApiFunction("put", "update"),
        updateStatus: createApiFunction("put", "update/status"),
    }
}