import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Button,
  IconButton,
  Stack,
  Breadcrumb,
  Tag,
  Table,
  Panel,
  Input,
  InputGroup,
  InputNumber,
  Pagination,
  Modal,
  Form,
  useToaster,
  DatePicker,
  Divider,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";
import dateFormatterLong from "@/lib/function/date-formatter-long";
import dateTimeFormatter from "@/lib/function/date-time-formatter";
import dateFormatterDash from "@/lib/function/date-formatter-dash";

import API_EformRcvQc from "@/pages/api/e_form/api_eform_rcv_qc";

export default function EFormLogsheetMaterialPage() {
  const router = useRouter();
  const toaster = useToaster();
  const [props, setProps] = useState([]);
  const { HeaderCell, Cell, Column } = Table;

  const [moduleName, setModuleName] = useState("");
  const [rcvQcData, setRcvQcData] = useState([]);

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [selectedRcvQc, setSelectedRcvQc] = useState(null);

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [totalReceipt, setTotalReceipt] = useState(0);

  useEffect(() => {
    const fetchData = async () => {
      const res_rcv_qc = await API_EformRcvQc().getAll();
      setRcvQcData(res_rcv_qc.data || []);
    };

    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    console.log("dataLogin", dataLogin);
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("e-form/rcv_qc")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      fetchData();
    }
  }, []);

  const emptyFormValue = {
    receipt_date: null,
    supplier: null,
    no_pr: null,
    no_po: null,
    no_receipt: null,
    item_arrival_date: null,
    item_remarks_total_receipt: [
      { item_name: null, remarks: null, total_receipt: null },
    ],
    created_by: null,
    updated_by: null,
  };

  const handleItemNameChange = (index, value) => {
    const updatedItemRemarksTotalReceipt = [
      ...formValue.item_remarks_total_receipt,
    ];
    updatedItemRemarksTotalReceipt[index].item_name = value;
    setFormValue({
      ...formValue,
      item_remarks_total_receipt: updatedItemRemarksTotalReceipt,
    });
  };

  const handleRemarksChange = (index, value) => {
    const updatedItemRemarksTotalReceipt = [
      ...formValue.item_remarks_total_receipt,
    ];
    updatedItemRemarksTotalReceipt[index].remarks = value;
    setFormValue({
      ...formValue,
      item_remarks_total_receipt: updatedItemRemarksTotalReceipt,
    });
  };

  const handleTotalReceiptChange = (index, value) => {
    const updatedItemRemarksTotalReceipt = [
      ...formValue.item_remarks_total_receipt,
    ];
    updatedItemRemarksTotalReceipt[index].total_receipt = value;
    setFormValue({
      ...formValue,
      item_remarks_total_receipt: updatedItemRemarksTotalReceipt,
    });
  };

  const addItemRemarksTotalReceipt = () => {
    setFormValue({
      ...formValue,
      item_remarks_total_receipt: [
        ...formValue.item_remarks_total_receipt,
        { item_name: null, remarks: null, total_receipt: null },
      ],
    });
  };

  const removeItemRemarksTotalReceipt = (index) => {
    const updatedItemRemarksTotalReceipt = [
      ...formValue.item_remarks_total_receipt,
    ];
    updatedItemRemarksTotalReceipt.splice(index, 1);
    setFormValue({
      ...formValue,
      item_remarks_total_receipt: updatedItemRemarksTotalReceipt,
    });
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id_eform_rcv_qc",
    "receipt_date",
    "supplier",
    "item_name",
    "no_pr",
    "no_po",
    "no_receipt",
    "item_arrival_date",
    "remarks",
    "total_receipt",
    "created_at",
    "created_by",
    "updated_at",
    "updated_by",
  ];

  const datas =
    rcvQcData && rcvQcData.length > 0
      ? rcvQcData
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .concat(
                item.receipt_date
                  ? dateFormatterLong(item.receipt_date).toLowerCase()
                  : ""
              )
              .concat(
                item.item_arrival_date
                  ? dateFormatterLong(item.item_arrival_date).toLowerCase()
                  : ""
              )
              .concat(
                item.created_at
                  ? dateTimeFormatter(
                      item.created_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .concat(
                item.updated_at
                  ? dateTimeFormatter(
                      item.updated_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .some((val) => val?.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const searchData = rcvQcData
    ? rcvQcData.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];

  const handleAdd = async () => {
    console.log("formValue", formValue);

    try {
      let result = await API_EformRcvQc().add({
        ...formValue,
        item_remarks_total_receipt: formValue.item_remarks_total_receipt?.map(
          (item) => ({
            ...item,
            total_receipt:
              item.total_receipt !== null ? parseInt(item.total_receipt) : null,
          })
        ),
        created_by: props.employee_id,
      });
      if (result?.status !== 200) {
        throw "Failed adding E-Form RCV QC";
      }

      console.log("result", result);

      const res = await API_EformRcvQc().getAll();
      setRcvQcData(res.data);

      setShowAddModal(false);

      // Show the toast message
      toaster.push(Messages("success", "Success adding E-Form RCV QC!"), {
        placement: "topCenter",
        duration: 5000,
      });

      // Reset the form value
      setFormValue(emptyFormValue);
    } catch (error) {
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleEdit = async () => {
    try {
      let result = await API_EformRcvQc().edit({
        ...formValue,
        id_eform_rcv_qc: selectedRcvQc,
        updated_by: props.employee_id,
        total_receipt: parseInt(totalReceipt),
      });
      if (result?.status !== 200) {
        throw "Failed editing E-Form RCV QC";
      }

      console.log("result", result);

      const res = await API_EformRcvQc().getAll();
      setRcvQcData(res.data);
      setShowEditModal(false);

      // Show the toast message
      toaster.push(Messages("success", "Success editing E-Form RCV QC!"), {
        placement: "topCenter",
        duration: 5000,
      });

      // Reset the form value
      setFormValue(emptyFormValue);
    } catch (error) {
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleExportExcel = () => {
    if (rcvQcData.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }

    const data = rcvQcData.map((item) => ({
      ID: item.id_eform_rcv_qc,
      "Receipt Date": item.receipt_date
        ? dateFormatterLong(item.receipt_date)
        : "-",
      Supplier: item.supplier,
      "Item Name": item.item_name,
      "No PR": item.no_pr,
      "No PO": item.no_po,
      "No Receipt": item.no_receipt,
      "Item Arrival Date": item.item_arrival_date
        ? dateFormatterLong(item.item_arrival_date)
        : "-",
      Remarks: item.remarks,
      "Number of Receipt": item.total_receipt,
      "Created At": item.created_at
        ? dateTimeFormatter(item.created_at, "id-ID", "seconds")
        : "-",
      "Created By": item.created_by,
      "Updated At": item.updated_at
        ? dateTimeFormatter(item.updated_at, "id-ID", "seconds")
        : "-",
      "Updated By": item.updated_by,
    }));

    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const fileName = `E-Form RCV QC ${date}.xlsx`;

    XLSX.writeFile(wb, fileName);
  };
  return (
    <>
      <div>
        <Head>
          <title>E-Form RCV QC</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>E-Form</Breadcrumb.Item>
                  <Breadcrumb.Item active>RCV QC</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusIcon />}
                    appearance="primary"
                    onClick={() => {
                      setShowAddModal(true);
                    }}
                  >
                    Add
                  </IconButton>
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              data={datas}
              bordered
              cellBordered
              height={400}
              onRowClick={(rowData) => {
                console.log(rowData);
              }}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>

              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id_eform_rcv_qc" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Receipt Date</HeaderCell>
                <Cell dataKey="receipt_date">
                  {(rowData) => {
                    return rowData?.receipt_date
                      ? dateFormatterLong(rowData?.receipt_date)
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Supplier</HeaderCell>
                <Cell dataKey="supplier" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Item Name</HeaderCell>
                <Cell dataKey="item_name" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>No PR</HeaderCell>
                <Cell dataKey="no_pr" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>No PO</HeaderCell>
                <Cell dataKey="no_po" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>No Receipt</HeaderCell>
                <Cell dataKey="no_receipt" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Item Arrival Date</HeaderCell>
                <Cell dataKey="item_arrival_date">
                  {(rowData) => {
                    return rowData?.item_arrival_date
                      ? dateFormatterLong(rowData?.item_arrival_date)
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Remarks</HeaderCell>
                <Cell dataKey="remarks" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Number of Receipt</HeaderCell>
                <Cell dataKey="total_receipt" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created At</HeaderCell>
                <Cell dataKey="created_at">
                  {(rowData) => {
                    return rowData?.created_at
                      ? dateTimeFormatter(
                          rowData?.created_at,
                          "id-ID",
                          "seconds"
                        )
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell dataKey="created_by" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated At</HeaderCell>
                <Cell dataKey="updated_at">
                  {(rowData) => {
                    return rowData?.updated_at
                      ? dateTimeFormatter(
                          rowData?.updated_at,
                          "id-ID",
                          "seconds"
                        )
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>

              <Column width={80} fixed="right" align="center">
                <HeaderCell>...</HeaderCell>
                <Cell>
                  {(rowData) => {
                    function handleEditAction() {
                      setSelectedRcvQc(rowData.id_eform_rcv_qc);
                      setFormValue({
                        ...rowData,
                        receipt_date: rowData.receipt_date
                          ? new Date(rowData.receipt_date)
                          : null,
                        item_arrival_date: rowData.item_arrival_date
                          ? new Date(rowData.item_arrival_date)
                          : null,
                      });
                      setTotalReceipt(rowData.total_receipt);
                      setShowEditModal(true);
                    }
                    return (
                      <span>
                        <a
                          onClick={handleEditAction}
                          className="cursor-pointer"
                        >
                          Edit
                        </a>
                      </span>
                    );
                  }}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                total={searchKeyword ? searchData.length : rcvQcData.length}
                limitOptions={[10, 30, 50]}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Add Modal */}
        <Modal
          backdrop="static"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Add E-Form RCV QC</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Receipt Date</Form.ControlLabel>
                <DatePicker
                  oneTap
                  value={formValue.receipt_date}
                  format="dd-MM-yyyy"
                  onChange={(value) => {
                    setFormValue({ ...formValue, receipt_date: value });
                  }}
                  block
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Supplier</Form.ControlLabel>
                <Form.Control
                  placeholder="Supplier"
                  value={formValue.supplier}
                  onChange={(value) => {
                    setFormValue({ ...formValue, supplier: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No PR</Form.ControlLabel>
                <Form.Control
                  placeholder="No PR"
                  value={formValue.no_pr}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_pr: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No PO</Form.ControlLabel>
                <Form.Control
                  placeholder="No PO"
                  value={formValue.no_po}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_po: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No Receipt</Form.ControlLabel>
                <Form.Control
                  placeholder="No Receipt"
                  value={formValue.no_receipt}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_receipt: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Item Arrival Date</Form.ControlLabel>
                <DatePicker
                  oneTap
                  format="dd-MM-yyyy"
                  value={formValue.item_arrival_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, item_arrival_date: value });
                  }}
                  block
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Item Name and Remarks</Form.ControlLabel>
                <Panel bordered>
                  {formValue.item_remarks_total_receipt?.map((item, index) => (
                    <div key={index} className="flex flex-column gap-4">
                      <Form.Control
                        placeholder="Item Name"
                        value={item.item_name || ""}
                        onChange={(e) => handleItemNameChange(index, e)}
                      />
                      <Input
                        placeholder="Remarks"
                        as="textarea"
                        value={item.remarks || ""}
                        onChange={(e) => handleRemarksChange(index, e)}
                      />
                      <InputNumber
                        placeholder="Total Receipt"
                        value={item.total_receipt || ""}
                        onChange={(e) => handleTotalReceiptChange(index, e)}
                      />
                      <Button
                        color="red"
                        appearance="primary"
                        onClick={() => removeItemRemarksTotalReceipt(index)}
                        hidden={
                          formValue.item_remarks_total_receipt.length <= 1
                        }
                      >
                        Remove
                      </Button>
                      <Divider />
                    </div>
                  ))}
                  <Button
                    appearance="primary"
                    onClick={addItemRemarksTotalReceipt}
                    block
                  >
                    Add Item Name and Remarks
                  </Button>
                </Panel>
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowAddModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleAdd();
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Edit Modal */}
        <Modal
          backdrop="static"
          open={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Edit E-Form RCV QC</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Receipt Date</Form.ControlLabel>
                <DatePicker
                  oneTap
                  value={formValue.receipt_date}
                  format="dd-MM-yyyy"
                  onChange={(value) => {
                    setFormValue({ ...formValue, receipt_date: value });
                  }}
                  block
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Supplier</Form.ControlLabel>
                <Form.Control
                  placeholder="Supplier"
                  value={formValue.supplier}
                  onChange={(value) => {
                    setFormValue({ ...formValue, supplier: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No PR</Form.ControlLabel>
                <Form.Control
                  placeholder="No PR"
                  value={formValue.no_pr}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_pr: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No PO</Form.ControlLabel>
                <Form.Control
                  placeholder="No PO"
                  value={formValue.no_po}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_po: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No Receipt</Form.ControlLabel>
                <Form.Control
                  placeholder="No Receipt"
                  value={formValue.no_receipt}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_receipt: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Item Arrival Date</Form.ControlLabel>
                <DatePicker
                  oneTap
                  format="dd-MM-yyyy"
                  value={formValue.item_arrival_date}
                  onChange={(value) => {
                    setFormValue({
                      ...formValue,
                      item_arrival_date: value,
                    });
                  }}
                  block
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Item Name</Form.ControlLabel>
                <Form.Control
                  placeholder="Item Name"
                  value={formValue.item_name}
                  onChange={(value) => {
                    setFormValue({ ...formValue, item_name: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Remarks</Form.ControlLabel>
                <Form.Control
                  placeholder="Remarks"
                  value={formValue.remarks}
                  onChange={(value) => {
                    setFormValue({ ...formValue, remarks: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Total Receipt</Form.ControlLabel>
                <Form.Control
                  placeholder="Total Receipt"
                  value={totalReceipt}
                  onChange={(value) => {
                    setTotalReceipt(value);
                  }}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowEditModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleEdit();
              }}
              appearance="primary"
              type="submit"
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
