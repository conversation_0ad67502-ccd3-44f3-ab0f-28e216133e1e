import { useEffect, useState } from "react";
import Head from "next/head"
import { <PERSON><PERSON><PERSON>rumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, ButtonGroup } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import API_MasterDataReagenCapRow from "@/pages/api/master_data/api_master_data_rc_row";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import { useRouter } from "next/router";

export default function MasterDataReagenCapRowPage() {

    const [moduleName, setModuleName] = useState("");
    const { HeaderCell, Cell, Column } = Table;
    const [capRow, setCapRow] = useState([]);
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [props, setProps] = useState([]);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showAddModal, setShowAddModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const emptyFormValue = {
        row_level_desc: null
    }
    const [formValue, setFormValue] = useState(emptyFormValue);
    const toaster = useToaster();
    const [errors, setErrors] = useState({});
    const router = useRouter();

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const handleGetAllApi = async () => {
        const res = await API_MasterDataReagenCapRow().getAll();
        console.log(res);
        setCapRow(res.data ? res.data : []);
    };

    const filteredData = capRow
        .filter((rowData, i) => {
            const searchFields = [
                "id_row",
                "row_level_desc",
                "created_dt",
                "created_by",
                "created_name",
                "updated_dt",
                "updated_by",
                "updated_name",
                "deleted_dt",
                "deleted_by",
                "deleted_name",
                "is_active",
            ];

            const matchesSearch = searchFields.some((field) =>
                rowData[field]
                    ?.toString()
                    .toLowerCase()
                    .includes(searchKeyword.toLowerCase())
            );

            return matchesSearch;
        })

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    }

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : capRow.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("master_data/master_rc_rack")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            handleGetAllApi();
        }
    }, []);


    const [deleteFormValue, setDeleteFormValue] = useState({
        id_row: null,
        deleted_by: null,
        reason: "",
        row_level_desc:null,
      });


    const handleEditStatusCapRow = async (selectedIdRow) => {
        await API_MasterDataReagenCapRow().editStatus({
            id_row: selectedIdRow,
            deleted_by: props.employee_id,
            reason: deleteFormValue.reason
        });
        handleGetAllApi();
        setShowDeleteModal(false);
        setDeleteFormValue({});
    };

    const handleEditApi = async (selectedIdRow) => {
        try {
            const result = await API_MasterDataReagenCapRow().edit({
                ...formValue,
                id_row: selectedIdRow,
                updated_by: props.employee_id,
                updated_name: props.employee_id + "-" + props.employee_name,
            });

            if (result.status === 200) {
                handleGetAllApi();
                setShowEditModal(false);

                toaster.push(Messages("success", "Success editing Row Data!"), {
                    placement: "topCenter",
                    duration: 5000,
                });
                setFormValue(emptyFormValue);
            } else if (result.status === 400) {
                toaster.push(
                    Messages("error", `Error: "${result.message}". Please try again later!`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setFormValue(emptyFormValue);
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    };

    const handleAddApi = async () => {
        try {
            const result = await API_MasterDataReagenCapRow().create({
                ...formValue,
                created_by: props.employee_id,
                created_name: props.employee_id + "-" + props.employee_name,
            });

            if (result.status === 200) {
                handleGetAllApi();
                setShowAddModal(false);

                toaster.push(
                    Messages("success", "Success adding Row Capacity!"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setFormValue(emptyFormValue);
            } else if (result.status === 400) {
                toaster.push(
                    Messages("error", `Error: "${result.message}". Please try again later!`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setFormValue(emptyFormValue);
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    };

    const handleExportExcel = () => {

        if (capRow.length === 0) {
            toaster.push(Messages("error", "No data to export!"), {
                placement: "topCenter",
                duration: 5000,
            });
            return;
        }

        const headerMapping = {
            id_row: "ID",
            row_level_desc: "Row Level Description",
            created_dt: "Created Date",
            created_by: "Created By",
            created_name: "Created Name",
            updated_dt: "Updated Date",
            updated_by: "Updated By",
            updated_name: "Updated Name",
            deleted_dt: "Deleted Date",
            deleted_by: "Deleted By",
            deleted_name: "Deleted Name",
            is_active: "Status",
        };

        const formattedData = capRow.map((item) => {
            const formattedItem = {};
            for (const key in item) {
                if (headerMapping[key]) {
                    if (key === 'is_active') {
                        formattedItem[headerMapping[key]] = item[key] === 1 ? 'Active' : 'Inactive';
                    } else {
                        formattedItem[headerMapping[key]] = item[key];
                    }
                }
            }
            return formattedItem;
        });

        const ws = XLSX.utils.json_to_sheet(formattedData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "data");

        const date = dateFormatterDash(new Date());
        const filename = `Master Data Reagen Row Capacity ${date}.xlsx`;

        XLSX.writeFile(wb, filename);
    }

    const handleSubmit = (apiFunction) => {
        if (!formValue.row_level_desc) {
            setErrors({ row_level_desc: 'Row Level Description is required.' });
            return;
        }
        setErrors({});
        apiFunction();
    };

    const handleRackClick = () => {
        router.push("/user_module/master_data/master_rc_rack")
    }

    const handleFloorClick = () => {
        router.push("/user_module/master_data/master_rc_floor")
    }

    const handleRowClick = () => {
        router.push("/user_module/master_data/master_rc_row")
    }

    return (
        <>
            <div>
                <Head>
                    <title>Row Capacity</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Quality</Breadcrumb.Item>
                                    <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Row Capacity</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Master Row</h5>
                                <Stack className="flex gap-2">
                                    <ButtonGroup>
                                        <Button
                                            onClick={handleRackClick}
                                            appearance="primary">
                                            Master Rack
                                        </Button>
                                        <Button
                                            onClick={handleFloorClick}
                                            appearance="primary">
                                            Master Floor
                                        </Button>
                                        <Button
                                            appearance="primary"
                                            onClick={handleRowClick}
                                            active>
                                            Master Row
                                        </Button>
                                    </ButtonGroup>
                                </Stack>
                            </Stack>}>
                    </Panel>

                    <Panel
                        bordered
                        bodyFill
                        header={
                            <Stack justifyContent="space-between">
                                <div className="flex gap-2">
                                    <IconButton
                                        icon={<PlusRoundIcon />}
                                        appearance="primary"
                                        onClick={() => {
                                            setShowAddModal(true);
                                        }}>
                                        Add
                                    </IconButton>
                                    <IconButton
                                        icon={<FileDownloadIcon />}
                                        appearance="primary"
                                        onClick={handleExportExcel}
                                    >
                                        Download (.xlsx)
                                    </IconButton>
                                </div>

                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="search"
                                        value={searchKeyword}
                                        onChange={handleSearch}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}>
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                        }
                    >
                        <Table
                            bordered
                            cellBordered
                            height={400}
                            data={getPaginatedData(getFilteredData(), limit, page)}
                            sortColumn={sortColumn}
                            sortType={sortType}
                            onSortColumn={handleSortColumn}
                        >
                            <Column width={70} align="center" fixed>
                                <HeaderCell>No</HeaderCell>
                                <Cell>
                                    {(rowData, rowIndex) => {
                                        return rowIndex + 1 + limit * (page - 1);
                                    }}
                                </Cell>
                            </Column>
                            <Column width={70} align="center" sortable>
                                <HeaderCell>ID</HeaderCell>
                                <Cell dataKey="id_row" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Description</HeaderCell>
                                <Cell dataKey="row_level_desc" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Created Date</HeaderCell>
                                <Cell dataKey="created_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Created By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        `${rowData.created_by} - ${rowData.created_name}`
                                    )}
                                </Cell>
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Updated Date</HeaderCell>
                                <Cell dataKey="updated_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Updated By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        `${rowData.updated_by} - ${rowData.updated_name}`
                                    )}
                                </Cell>
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Deleted Date</HeaderCell>
                                <Cell dataKey="deleted_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Deleted By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        `${rowData.deleted_by} - ${rowData.deleted_name}`
                                    )}
                                </Cell>
                            </Column>
                            <Column width={100} sortable resizable>
                                <HeaderCell>Status</HeaderCell>
                                <Cell>
                                    {(rowData) => (
                                        <span
                                            style={{
                                                color: rowData.is_active === 1 ? "green" : "red",
                                            }}
                                        >
                                            {rowData.is_active === 1 ? "Active" : "Inactive"}
                                        </span>
                                    )}
                                </Cell>
                            </Column>
                            <Column width={100} fixed="right" align="center">
                                <HeaderCell>Action</HeaderCell>
                                <Cell style={{ padding: "8px" }}>
                                    {(rowData) => (
                                        <div>
                                            <Button
                                                appearance="link"
                                                disabled={rowData.is_active === 0}
                                                onClick={() => {
                                                    setShowEditModal(true);
                                                    setFormValue(rowData);
                                                }}
                                            >
                                                Edit
                                            </Button>
                                            <Button
                                                appearance="subtle"
                                                onClick={() =>
                                                    {
                                                        if (rowData.is_active === 1) {
                                                            setShowDeleteModal(true)
                                                            setDeleteFormValue({
                                                            ...deleteFormValue,
                                                            id_row: rowData.id_row,
                                                            deleted_by: props.employee_id,
                                                            reason:"",
                                                            row_level_desc: rowData.row_level_desc
                                                            })
                                                        }else{
                                                            handleEditStatusCapRow(rowData.id_row)    
                                                        }
                                                    }
                                                }
                                            >
                                                {rowData.is_active === 1 ? (
                                                    <TrashIcon style={{ fontSize: "16px" }} />
                                                ) : (
                                                    <ReloadIcon style={{ fontSize: "16px" }} />
                                                )}
                                            </Button>
                                        </div>
                                    )}
                                </Cell>
                            </Column>
                        </Table>
                        <div style={{ padding: 20 }}>
                            <Pagination
                                prev
                                next
                                first
                                last
                                ellipsis
                                boundaryLinks
                                maxButtons={5}
                                size="xs"
                                layout={["total", "-", "limit", "|", "pager", "skip"]}
                                limitOptions={[10, 30, 50]}
                                total={totalRowCount}
                                limit={limit}
                                activePage={page}
                                onChangePage={setPage}
                                onChangeLimit={handleChangeLimit}
                            />
                        </div>
                    </Panel>
                </div>

                {/* add modal */}
                <Modal
                    backdrop="static"
                    open={showAddModal}
                    onClose={() => {
                        setShowAddModal(false);
                        setFormValue(emptyFormValue);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Add Row</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Row Level Description</Form.ControlLabel>
                                <Form.Control
                                    name="row_level_desc"
                                    value={formValue.row_level_desc}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, row_level_desc: value })
                                    }}
                                />
                                {errors.row_level_desc && <p style={{ color: 'red' }}>{errors.row_level_desc}</p>}
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowAddModal(false);
                                setFormValue(emptyFormValue);
                                setErrors({});
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleSubmit(handleAddApi);
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Add
                        </Button>
                    </Modal.Footer>
                </Modal>

                {/* Edit Modal */}
                <Modal
                    backdrop="static"
                    open={showEditModal}
                    onClose={() => {
                        setShowEditModal(false);
                        setFormValue(emptyFormValue);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Edit Row</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Row Level Description</Form.ControlLabel>
                                <Form.Control
                                    name="row_level_desc"
                                    value={formValue.row_level_desc}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, row_level_desc: value })
                                    }}
                                />
                                {errors.row_level_desc && <p style={{ color: 'red' }}>{errors.row_level_desc}</p>}
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowEditModal(false);
                                setFormValue(emptyFormValue);
                                setErrors({});
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleSubmit(() => handleEditApi(formValue.id_row));
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Edit
                        </Button>
                    </Modal.Footer>
                </Modal>

                <Modal
                    backdrop="static"
                    open={showDeleteModal}
                    onClose={() => {
                        setShowDeleteModal(false);
                        setDeleteFormValue({});
                    }}
                    overflow={false}
                    >
                    <Modal.Header>
                        <Modal.Title>Delete Master Row {deleteFormValue.floor_level_desc}</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                        <Form.Group>
                            <Form.ControlLabel>Reason</Form.ControlLabel>
                            <Form.Control
                            name="reason"
                            value={deleteFormValue.reason}
                            onChange={(value) => {
                                setDeleteFormValue((prevFormValue) => ({
                                ...prevFormValue,
                                reason: value,
                                }));
                            }}
                            />
                        </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                        onClick={() => {
                            setShowDeleteModal(false);
                            setDeleteFormValue({});
                        }}
                        appearance="subtle"
                        >
                        Cancel
                        </Button>

                        <Button
                        onClick={() => {
                            handleEditStatusCapRow(deleteFormValue.id_row);
                        }}
                        appearance="primary"
                        type="submit"
                        >
                        Add
                        </Button>
                    </Modal.Footer>
                </Modal>
            </ContainerLayout>
        </>
    )
}
