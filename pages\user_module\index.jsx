import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import ContainerLayout from '@/components/layout/ContainerLayout';
import UserModuleData from './UserModuleData';
import Head from 'next/head';
import MenuApi from '../api/menuApi';
import { data } from 'autoprefixer';

export default function UserModule({ module_code, module_icon, module_name }) {
  const [dataShown, setDataShown] = useState(false);
  const [menuData, setMenuData] = useState([]);
  const [allMenuData, setAllMenuData] = useState([]);
  const [menuDataParent, setMenuDataParent] = useState([]);
  const router = useRouter();
  const { GetUserMenu } = MenuApi();

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    console.log("<PERSON>lai dataLogin : ", dataLogin);
    getData(); //men<PERSON><PERSON>an get data
    if (!dataLogin) {
      router.push('/');
    }

    if (
      module_name !== '' &&
      module_name !== null &&
      module_name !== undefined
    ) {
      localStorage.setItem('module_name', module_name);
    }
  }, []);

  //validasi memasangkan menu
  useEffect(() => {
    if (
      allMenuData != null &&
      allMenuData != undefined &&
      allMenuData.length > 0
    ) {
      userMenuDataHandler(allMenuData);
    }
  }, [allMenuData]);

  // Fungsi untuk mendapatkan nilai menuData dari component UserModuleData
  const userMenuDataHandler = (menuData) => {
    // Filter the parentMenu / childMenu and remove duplicate data
    const parentMenuSet = new Set();
    const parentMenu = menuData
      ?.filter((item) => item.Is_Parent === 1)
      .filter((item) => {
        const isUnique = !parentMenuSet.has(item.Id_Menu);
        if (isUnique) {
          parentMenuSet.add(item.Id_Menu);
        }
        return isUnique;
      });

    // const childMenuSet = new Set();
    // const childMenu = menuData
    //   ?.filter((item) => item.Is_Parent === 0)
    //   .filter((item) => {
    //     const isUnique = !childMenuSet.has(item.Id_Menu);
    //     if (isUnique) {
    //       parentMenuSet.add(item.Id_Menu);
    //     }
    //     return isUnique;
    //   });
    
    //const childMenuSet = new Set();
    const childMenuArr = []
    parentMenu.map(menu =>{
      menu.Child_menu.map(childMenu =>{
        //console.log("childmenu", childMenu)
        childMenuArr.push(childMenu)
      })
    })

    //console.log("parentmenu",childMenuArr)

    localStorage.setItem('parentMenu', JSON.stringify(parentMenu));
    localStorage.setItem('childMenu', JSON.stringify(childMenuArr));
    // localStorage.setItem("menuIcon", module_icon);

    setDataShown(true);
  };

  //mengambil data menjadi client side
  const getData = async () => {
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    if (!dataLogin) {
      router.push('/dashboard');
      return;
    }
    let userData = {
      employee_id: dataLogin.employee_id,
      module_code: parseInt(module_code),
    };
    let datas = await GetUserMenu(userData);
    const { Data } = datas;
    const { error } = datas;
    if (error) {
      router.push('/noMenuFound');
      return;
    }
    if (Data) {
      const parentData = Data.filter(item => item.Is_Parent == 1);
      let allMenuFinalData = [];
      let section = 1;
      const parentMap = parentData.map(itemParent => {
        let data = Data.filter(item => item.Menu_Link_Code.includes(itemParent.Menu_Link_Code) && item.Is_Parent != 1);
        data = data.sort((a, b) => a.Menu_Name.localeCompare(b.Menu_Name));
        data.push(itemParent);
        const result = data.map(itemFinal => {
          itemFinal.section = section;
          allMenuFinalData.push(itemFinal);
        });
        section++;
      });
      setAllMenuData(allMenuFinalData);
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Module {module_name}</title>
        </Head>
      </div>
      {/* <UserModuleData moduleCode={module_code} handler={userMenuDataHandler} /> */}
      {dataShown && (
        <ContainerLayout
          title="User Module"
          menuIcon={module_icon}
          moduleCode={module_code}
          menuData={allMenuData}
        >
          <div className="col-9 p-5">
            {menuDataParent ? (
              <h1>User Module Dashboard</h1>
            ) : (
              <h1>No Menu Data Found.</h1>
            )}
          </div>
        </ContainerLayout>
      )}
    </>
  );
}

export async function getServerSideProps({ query }) {
  const { module_code, module_name } = query;
  // const module_name = query.userModule[0];
  // const module_code = query.userModule[1];

  return {
    props: {
      module_code,
      module_name,
    },
  };
}
