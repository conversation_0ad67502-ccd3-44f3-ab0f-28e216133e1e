import { useRouter } from 'next/router';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import Head from 'next/head';
import ReviewerApproverApi from '@/pages/api/reviewerApprover';
import ParameterApi from '@/pages/api/parameterApi';
import MainContent from '@/components/layout/MainContent';
import { useState, useEffect } from 'react';
import {
  Form,
  Stack,
  Input,
  Panel,
  InputNumber,
  Dropdown,
  IconButton,
  TreePicker,
  Button,
  Checkbox,
  Divider,
  Tag,
  useToaster,
  Whisper,
  Tooltip
} from 'rsuite';
import Editor from '@/components/Editor';
import ProtocolApi from '@/pages/api/protocolApi';
import Table from '@/components/Table';
import Messages from "@/components/Messages";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMinus, faPlus } from '@fortawesome/free-solid-svg-icons';
import Trash  from '@rsuite/icons/Trash';

export default function EditProtocol({ noProtocol }) {
  const MySwal = withReactContent(Swal);
  const router = useRouter();
  let path = 'protocol/management/EditProtocol';
  const toaster = useToaster();
  let { data } = router.query;
  data ? (data = JSON.parse(data)) : data;
  const [breadcrumbsData, setBreadcrumbsData] = useState([]);
  const [cycleMonthData, setCycleMonthData] = useState([
    0, 3, 6, 9, 12, 18, 24, 36,
  ]);
  const [remarkChange, setRemarkChange] = useState('');
  const [idProtocol, setIdProtocol] = useState(0);
  const [t30selected, setT30Selected] = useState([0]);
  const [t40selected, setT40Selected] = useState([0]);
  const [moduleName, setModuleName] = useState('');
  const [tujuan, setTujuan] = useState('');
  const [protocolVersion, setProtocolVersion] = useState('');
  const [ruangLingkup, setRuangLingkup] = useState('');
  const [dokumenAcuan, setDokumenAcuan] = useState('');
  const [descValue, setDescValue] = useState('');
  const [absoluteValue, setAbsoluteValue] = useState();
  const [rangeMin, setRangeMin] = useState();
  const [rangeMax, setRangeMax] = useState();
  const [treePickerReviewerData, setTreePickerReviewerData] = useState([]);
  const [treePickerReviewerData2, setTreePickerReviewerData2] = useState([]);
  const [treePickerReviewerData3, setTreePickerReviewerData3] = useState([]);
  const [treePickerReviewerData4, setTreePickerReviewerData4] = useState([]);
  const [treePickerReviewerLock, setTreePickerReviewerLock] = useState([]);
  const [treePickerApproverData, setTreePickerApproverData] = useState([]);
  const [treePickerApproverData2, setTreePickerApproverData2] = useState([]);
  const [treePickerApproverData3, setTreePickerApproverData3] = useState([]);
  const [treePickerApproverData4, setTreePickerApproverData4] = useState([]);
  const [treePickerApproverDataLock, setTreePickerApproverDataLock] = useState([]);
  const [tanggungJawab, setTanggungJawab] = useState('');
  const [namaProduk, setNamaProduk] = useState('');
  const [kodeProduk, setKodeProduk] = useState('');
  const [nomorBatch, setNomorBatch] = useState('');
  const [departmentId, setDepartmentId] = useState("");
  const [nomorPPI, setNomorPPI] = useState('');
  const [batchSize, setBatchSize] = useState('');
  const [dibuatOleh, setDibuatOleh] = useState('');
  const [komposisiFormula, setKomposisiFormula] = useState('');
  const [additionalNotes, setAdditionalNotes] = useState('');
  const [approvalHistory, setApprovalHistory] = useState([]);
  const [treePickerData, setTreePickerData] = useState([]);
  const [selectedReviewer, setSelectedReviewer] = useState([]);
  const [selectedApprover, setSelectedApprover] = useState([]);
  const [createdBy, setCreatedBy] = useState('');
  const [reviewerOrder, setReviewerOrder] = useState([]);
  const [approverOrder, setApproverOrder] = useState([]);
  const [title, setTitle]= useState('')
  const [arrSelectedParameter, setArrSelectedParameter] = useState([
    {
      id_parameter: 0,
      department: 0,
      parameter_name: '',
      is_active: 0,
      input_method: 0,
      created_by: 0,
      method_desc: '',
    },
  ]);
  const [protocolDetailData, setProtocolDetailData] = useState([]);
  const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
  const { GetActiveReviewerByDepartmentId, GetActiveApproverByDepartmentId, GetActiveReviewerForNewProtocol, GetActiveApproverForNewProtocol, GetActiveReviewerForEditProtocol, GetActiveApproverForEditProtocol } =
    ReviewerApproverApi();
  const [inputFields, setInputFields] = useState([{
    order_number: 1,
    desc: '',
    value_reference: 0,
    min_value: 0,
    max_value: 0,
    id_parameter: 0,
    status_active: 1,
    unique_identifier: "",
    id_protocol: 0,
    input_Method_Desc: '',
  }]);
  const [newDataInputFields, setNewDataInputFields] = useState([]);
  const [valueSelectedParam, setValueSelectedParam] = useState([2, 8, 13, 7]);
  const [allParameterData, setAllParameterData] = useState([]);
  const [oldBatchNumber, setOldBatchNumber] = useState([])
  const [newBatchNumber, setNewBatchNumber] = useState([])
  const {
    GetProtocolDataByNoProtocol, UpdateProtocol
  } = ProtocolApi();
  const { GetAllParameter } = ParameterApi();

  // Handler awal checked
  const isChecked = (group, code) => {
    let result;
    if (group === 'T30') {
      for (let item of t30selected) {
        if (code === item) {
          result = true;
          break;
        } else {
          result = false;
        }
      }
    } else {
      for (let item of t40selected) {
        if (code === item) {
          result = true;
          break;
        } else {
          result = false;
        }
      }
    }
    return result;
  };

  const GetReviewer = async (idData) => {
    const { data: userData } = await GetActiveReviewerForEditProtocol(idData);

    if (userData !== null && userData !== undefined) {
      let treePickerData = [];
      userData.map((user) => {
        const data = {
          value: user.Employee_Id,
          label: `${user.Employee_Name} - [${user.Department}]`,
        };
        treePickerData.push(data);
      });
      setTreePickerReviewerData(treePickerData);
      setTreePickerReviewerData2(treePickerData);
      setTreePickerReviewerData3(treePickerData);
      setTreePickerReviewerData4(treePickerData);
      setTreePickerReviewerLock(treePickerData);
    }
  };

  const GetApprover = async (idData) => {
    const { data: userData } = await GetActiveApproverForEditProtocol(idData);

    if (userData !== null && userData !== undefined) {
      let treePickerData = [];
      userData.map((user) => {
        const data = {
          value: user.Employee_Id,
          label: `${user.Employee_Name} - [${user.Department}]`,
        };
        treePickerData.push(data);
      });

      setTreePickerApproverData(treePickerData);
      setTreePickerApproverData2(treePickerData);
      setTreePickerApproverData3(treePickerData);
      setTreePickerApproverData4(treePickerData);
      setTreePickerApproverDataLock(treePickerData);
    }
  };

  const GetAllParameterData = async () => {
    const { data: parameterData } = await GetAllParameter();
    if (parameterData !== null && parameterData !== undefined) {
      let treePickerDataStore = [];
      parameterData.map((parameter) => {
        const data = {
          value: parameter.Id_Parameter,
          label: `${parameter.Parameter_Name} - ${parameter.Method_Desc}`,
        };
        treePickerDataStore.push(data);
      });

      setTreePickerData(treePickerDataStore);
      setAllParameterData(parameterData);
    }
  };

  const GetProtocolData = async (noProtocol, userData, userDept) => {
    const inputData = {
      no_protocol: noProtocol,
      employee_id: userData,
      department: parseInt(userDept)
    };

    const { data: protocolData } = await GetProtocolDataByNoProtocol(inputData);

    if (protocolData !== null && protocolData !== undefined) {
      // Set Header Data
      setTitle(protocolData.Header_Data[0].Title)
      setIdProtocol(protocolData.Header_Data[0].Id_Protocol);
      setTujuan(protocolData.Header_Data[0].Desc_Purpose);
      setProtocolVersion(protocolData.Header_Data[0].Protocol_Version);
      setRuangLingkup(protocolData.Header_Data[0].Desc_Scope);
      setDokumenAcuan(protocolData.Header_Data[0].Desc_Document);
      setTanggungJawab(protocolData.Header_Data[0].Desc_Responsibilities);
      setNamaProduk(protocolData.Header_Data[0].Product_Name);
      setKodeProduk(protocolData.Header_Data[0].Product_Code);
      setNomorBatch(protocolData.Header_Data[0].Batch_No);
      setNomorPPI(protocolData.Header_Data[0].Ppi_No);
      setBatchSize(protocolData.Header_Data[0].Batch_Size);
      setDibuatOleh(protocolData.Header_Data[0].Manufactured_By);
      setKomposisiFormula(protocolData.Header_Data[0].Formula);
      setAdditionalNotes(protocolData.Header_Data[0].Additional_Notes);

      // Set T30 TimeFrame Data
      let t30DataList = [];
      protocolData.T30Data.map((item) => {
        t30DataList.push(item.Cycle_Month);
      });

      // Set T40 TimeFrame Data
      let t40DataList = [];
      protocolData.T40Data.map((item) => {
        t40DataList.push(item.Cycle_Month);
      });

      setT30Selected(t30DataList);
      setT40Selected(t40DataList);

      // Set Detail Data
      setProtocolDetailData(protocolData.Detail_Data);

      // Set input fields
      const inputFieldsContruct = protocolData.Detail_Data.map((item) => {
        let inputFieldsTemplate = {
          desc: "",
          id_parameter: 0,
          id_detail_protocol: 0,
          max_value: 0,
          min_value: 0,
          order_number: 0,
          value_reference: 0,
          unique_identifier: "",
          status_active: 1,
          input_Method_Desc: '',
        };
        if (item.Input_Method_Id == 1) {
          // Desc
          inputFieldsTemplate.desc = item.Desc;
        } else if (item.Input_Method_Id == 2) {
          // Absolute / Value Reference
          inputFieldsTemplate.value_reference = item.Value_Reference;
        } else {
          // Range
          inputFieldsTemplate.min_value = item.Min_Value;
          inputFieldsTemplate.max_value = item.Max_Value;
        }
        inputFieldsTemplate.id_parameter = item.Id_Parameter;
        inputFieldsTemplate.unique_identifier = item.Unique_Identifier;
        inputFieldsTemplate.id_protocol = item.Id_Protocol;
        inputFieldsTemplate.status_active = item.Status_Active;
        inputFieldsTemplate.order_number = item.Order_Number;
        inputFieldsTemplate.id_detail_protocol = item.Id_Detail;
        inputFieldsTemplate.input_Method_Desc = item.Input_Method_Desc
        return inputFieldsTemplate;
      });
      console.log(inputFieldsContruct)
      setInputFields(inputFieldsContruct);

      // Set Selected Parameter existing data
      const selectedParam = protocolData.Detail_Data.map(item => item.Id_Parameter);
      setValueSelectedParam(selectedParam);
      // Set reviewer order
      const reviewerOrderData = protocolData.ReviewerOrderNew.map(itemReviewer => itemReviewer.Employee_Id);
      setSelectedReviewer(reviewerOrderData);
      // Set approver order
      const approverOrderData = protocolData.ApproverOrderNew.map(itemReviewer => itemReviewer.Employee_Id);
      setSelectedApprover(approverOrderData);

      //set batch number
      setOldBatchNumber(protocolData.BatchNumber_Data)
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem('module_name');
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    // Validate dataLogin
    if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
      router.push('/');
      return;
    }
    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ''
    ) {
      router.push('/dashboard');
      return;
    }
    const asPathWithoutQuery = router.asPath.split('?')[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split('/')
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      asPathNestedRoutes[1],
      asPathNestedRoutes[2],
    ];
    const capitalizeFirstLetter = (str) => {
      return str.charAt(0).toUpperCase() + str.slice(1);
    };
    const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
      const words = item.split(/(?=[A-Z])/);
      return words.map((word) => capitalizeFirstLetter(word)).join(' ');
    });
    setBreadcrumbsData(breadCrumbsResult);
    setModuleName(moduleNameValue);
    GetProtocolData(noProtocol, dataLogin.employee_id, dataLogin.department);
    setDepartmentId(dataLogin.department);
    setCreatedBy(dataLogin.employee_id);
    const userDepartmentData = {
      id_department: dataLogin.department,
    };
    GetReviewer(userDepartmentData);
    GetApprover(userDepartmentData);
    GetAllParameterData();
  }, []);

  const t30ChangeHandler = (event) => {
    const isChecked = event.target.checked;
    const value = parseInt(event.target.value);
    if (isChecked) {
      setT30Selected((prevValue) => [...prevValue, value]);
    } else {
      const newData = t30selected.filter((item) => item !== value);
      setT30Selected(newData);
    }
  };

  const t40ChangeHandler = (event) => {
    const isChecked = event.target.checked;
    const value = parseInt(event.target.value);
    if (isChecked) {
      setT40Selected((prevValue) => [...prevValue, value]);
    } else {
      const newData = t40selected.filter((item) => item !== value);
      setT40Selected(newData);
    }
  };

  const addParameterPengujian = () => {
    let newField = {
      desc: '',
      value_reference: 0,
      id_detail_protocol: 0,
      min_value: 0,
      max_value: 0,
      id_parameter: 0,
      status_active: 1,
      id_protocol: 0
    };
    let newParameterField = {
      id_parameter: 0,
      department: 0,
      parameter_name: '',
      is_active: 0,
      input_method: 0,
      created_by: 0,
      method_desc: '',
    };
    setNewDataInputFields([...newDataInputFields, newField]);
    setArrSelectedParameter([...arrSelectedParameter, newParameterField]);
  };

  const removeParameterPengujian = () => {
    let dataInputField = [...newDataInputFields];
    let dataParameterField = [...arrSelectedParameter];
    if (dataInputField.length <= 0) {
      console.log('Cannot Delete !');
    } else {
      dataInputField.pop();
      dataParameterField.pop();
      setNewDataInputFields(dataInputField);
      setArrSelectedParameter(dataParameterField);
    }
  };

  const selectReviewerChangeHandler = (value, index) => {
    // setSelectedReviewer(value);
    let arrReviewer = [...selectedReviewer];
    const isExist = arrReviewer.includes(value);
    if (isExist) {
      return;
    }
    arrReviewer[index] = value != null ? value : null;
    setSelectedReviewer(arrReviewer);
    if (value == null) {
    } else {
      let arrDataUser = treePickerReviewerData.filter(item => item.value !== value);
      setTreePickerReviewerData2(arrDataUser);
      setTreePickerReviewerData3(arrDataUser);
    }
  };

  const selectReviewer2ChangeHandler = (value, index) => {
    // setSelectedReviewer(value);
    let arrReviewer = [...selectedReviewer];
    const isExist = arrReviewer.includes(value);
    if (isExist) {
      return;
    }
    arrReviewer[index] = value != null ? value : null;
    setSelectedReviewer(arrReviewer);
    if (value == null) {
    } else {
      let arrDataUser = treePickerReviewerData2.filter(item => item.value !== value);
      setTreePickerReviewerData3(arrDataUser);
    }
  };

  const selectReviewer3ChangeHandler = (value, index) => {
    // setSelectedReviewer(value);
    let arrReviewer = [...selectedReviewer];
    const isExist = arrReviewer.includes(value);
    if (isExist) {
      return;
    }
    arrReviewer[index] = value != null ? value : null;
    setSelectedReviewer(arrReviewer);
    if (value == null) {
    } else {
      let arrDataUser = treePickerReviewerData.filter(item => item.value !== value);
      setTreePickerReviewerData4(arrDataUser);
    }
  };
  const selectApproverChangeHandler = (value, index) => {
    // setSelectedReviewer(value);
    let arrApprover = [...selectedApprover];
    const isExist = arrApprover.includes(value);
    if (isExist) {
      return;
    }
    arrApprover[index] = value != null ? value : null;
    setSelectedApprover(arrApprover);
    if (value == null) {
    } else {
      let arrDataUser = treePickerApproverData.filter(item => item.value !== value);
      setTreePickerApproverData2(arrDataUser);
      setTreePickerApproverData3(arrDataUser);
    }
  };

  const selectApproverChangeHandler2 = (value, index) => {
    // setSelectedReviewer(value);
    let arrApprover = [...selectedApprover];
    const isExist = arrApprover.includes(value);
    if (isExist) {
      return;
    }
    arrApprover[index] = value != null ? value : null;
    setSelectedApprover(arrApprover);
    if (value == null) {
    } else {
      let arrDataUser = treePickerApproverData2.filter(item => item.value !== value);
      setTreePickerApproverData3(arrDataUser);
    }
  };

  const selectApproverChangeHandler3 = (value, index) => {
    // setSelectedReviewer(value);
    let arrApprover = [...selectedApprover];
    const isExist = arrApprover.includes(value);
    if (isExist) {
      return;
    }
    arrApprover[index] = value != null ? value : null;
    setSelectedApprover(arrApprover);
    if (value !== null) {
      let arrDataUser = treePickerApproverData.filter(item => item.value !== value);
      setTreePickerApproverData4(arrDataUser);
    }
  };

  const handleFormParameter = (index, v) => {
    // Jika parameter dihapus dari treepicker
    if (v === null) {
      const resetArrSelectedParameter = {
        id_parameter: 0,
        department: 0,
        parameter_name: '',
        is_active: 0,
        input_method: 0,
        created_by: 0,
        method_desc: '',
      };
      const resetInputFields = {
        desc: '',
        value_reference: 0,
        min_value: 0,
        max_value: 0,
        id_parameter: 0,
      };
      // Reset selected parameter
      let newArrSelectedParam = [...arrSelectedParameter];
      newArrSelectedParam[index] = resetArrSelectedParameter;
      setArrSelectedParameter(newArrSelectedParam);
      // Reset inputFields
      let newInputFields = [...newDataInputFields];
      newInputFields[index] = resetInputFields;
      setNewDataInputFields(newInputFields);
      return;
    }
    // Get chosen parameter data
    const parameter = allParameterData.filter(
      (item) => item.Id_Parameter === v,
    );
    // Validate no duplication of parameter in newDataInputFields or inputFields(old data)
    const idParameter = parameter[0].Id_Parameter;
    const dataInputFieldsOld = [...inputFields];
    const dataInputFieldsNew = [...newDataInputFields];
    const existingDataParameterNew = dataInputFieldsNew.filter(
      (item) => item.id_parameter == idParameter,
    );
    const existingDataParameterOld = dataInputFieldsOld.filter(
      (item) => item.id_parameter == idParameter,
    );
    if (existingDataParameterNew.length > 0 || existingDataParameterOld.length > 0) {
      toaster.push(
        Messages("warning", "Please use different parameter !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      return;
    }
    // Add new parameter
    let data = [...arrSelectedParameter];
    data[index] = parameter[0];
    setArrSelectedParameter(data);
    // Set id_parameter in inputFields
    dataInputFieldsNew[index].id_parameter = idParameter;
    setNewDataInputFields(dataInputFieldsNew);
  };

  const oldDataValueChange = (index, name, value) => {
    const data = [...inputFields];
    data[index][name] = value;
    setInputFields(data);
  };

  const oldStatusChangeHandler = (index) => {
    const data = [...inputFields];
    if (data[index].status_active == 1) {
      data[index].status_active = 0;
    } else {
      data[index].status_active = 1;
    }
    setInputFields(data);
  };

  const handleFormChange = (index, name, e) => {
    let data = [...newDataInputFields];
    data[index].order_number = index + 1;
    data[index][name] = parseFloat(e.target.value);
    setNewDataInputFields(data);
  };

  const handleFormDesc = (index, v) => {
    let data = [...newDataInputFields];
    // data.desc = v;
    data[index].order_number = index + 1;
    data[index].desc = v;
    setNewDataInputFields(data);
  };

  const submitHandler = async () => {
    setIsSubmitButtonDisabled(true);
    if (
      tujuan === '' ||
      ruangLingkup === '' ||
      dokumenAcuan === '' ||
      tanggungJawab === '' ||
      namaProduk === '' ||
      // nomorBatch === '' ||
      nomorPPI === '' ||
      batchSize === '' ||
      selectedReviewer.length === 0 ||
      selectedApprover.length === 0 ||
      remarkChange == ''
    ) {
      toaster.push(
        Messages("warning", "Please fill all required data !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      setIsSubmitButtonDisabled(false);
      return;
    }
    // Validate if parameter pengujian is not filled
    const isEmptyDataParameter = newDataInputFields.map(item => {
      if (item.desc == "" && item.min_value == 0 && item.max_value == 0 && item.value_reference == 0) {
        toaster.push(
          Messages("warning", "Please fill the parameter data !"),
          {
            placement: "topEnd",
            duration: 3000,
          }
        );
        setIsSubmitButtonDisabled(false);
        return;
      } else if (isNaN(item.min_value) || isNaN(item.max_value) || isNaN(item.value_reference)) {
        toaster.push(
          Messages("warning", "Invalid parameter data !"),
          {
            placement: "topEnd",
            duration: 3000,
          }
        );
        setIsSubmitButtonDisabled(false);
        return;
      }
    });
    // Validate if there is no reviewer or approver
    if (selectedReviewer.length == 0 || selectedApprover.length == 0) {
      toaster.push(
        Messages("warning", "Please select the reviewer/approver !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      setIsSubmitButtonDisabled(false);
      return;
    }
    // Validate reviewer flow data (sequential check)
    let reviewerDataFragment = '';
    const iteratingReviewerData = selectedReviewer.map(item => {
      if (item === null) {
        reviewerDataFragment += 0;
      } else {
        reviewerDataFragment += 1;
      }
    });
    if (reviewerDataFragment.includes('01')) {
      toaster.push(
        Messages("warning", "Reviewer selection must be sequential !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      setIsSubmitButtonDisabled(false);
      return;
    }
    if (!reviewerDataFragment.includes('1')) {
      toaster.push(
        Messages("warning", "Please select a reviewer !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      setIsSubmitButtonDisabled(false);
      return;
    }
    // Validate approver flow data (sequential check)
    let approverDataFragment = '';
    const iteratingApproverData = selectedApprover.map(item => {
      if (item === null) {
        approverDataFragment += 0;
      } else {
        approverDataFragment += 1;
      }
    });
    if (approverDataFragment.includes('01')) {
      toaster.push(
        Messages("warning", "Approver selection must be sequential !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      setIsSubmitButtonDisabled(false);
      return;
    }
    if (!approverDataFragment.includes('1')) {
      toaster.push(
        Messages("warning", "Please select an approver !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      setIsSubmitButtonDisabled(false);
      return;
    }
    // Validate reviewer data (remove null values)
    let filteredReviewer = selectedReviewer.filter(item => item !== null
    );
    // Validate approver data (remove null values)
    let filteredApprover = selectedApprover.filter(item => item !== null);
    // Constructing reviewer and approver data for request
    const reviewerArr = filteredReviewer.map(item => {
      const data = `"${item}"`;
      return data;
    });
    const approverArr = filteredApprover.map(item => {
      const data = `"${item}"`;
      return data;
    });
    // Contructing detail data (old)
    const detailDataOld = inputFields.map(item => {
      const newData = { no_protocol: noProtocol, ...item };
      return newData;
    });
    // Constructing detail data (new)
    const detailDataNew = newDataInputFields.map(item => {
      const newData = { no_protocol: noProtocol, ...item };
      return newData;
    });
    const dataEditProtocol = {
      no_protocol: noProtocol,
      employee_id: createdBy,
      department: departmentId,
      header_protocol: {
        id_protocol: parseInt(idProtocol),
        remark_change: remarkChange,
        protocol_version: parseInt(protocolVersion),
        product_name: namaProduk,
        no_protocol: noProtocol,
        department: departmentId,
        created_by: createdBy,
        desc_purpose: tujuan,
        desc_scope: ruangLingkup,
        desc_document: dokumenAcuan,
        desc_responsibilities: tanggungJawab,
        product_code: kodeProduk,
        batch_no: nomorBatch,
        ppi_no: nomorPPI,
        batch_size: batchSize,
        manufactured_by: dibuatOleh,
        formula: komposisiFormula,
        additional_notes: additionalNotes,
        reviewer: `[${reviewerArr}]`,
        approver: `[${approverArr}]`,
        title: title,
      },
      detail_protocol: detailDataNew,
      detail_protocol_old: detailDataOld,
      batch_number_old : oldBatchNumber,
      batch_number_new : newBatchNumber,
    };
    console.log(dataEditProtocol)
    setIsSubmitButtonDisabled(true);
    const { status, message } = await UpdateProtocol(dataEditProtocol);
    if (status === 200) {
      toaster.push(
        Messages("success", "Data update success !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      router.push('/user_module/protocol/management/BrowseProtocol');
      return;
    }
    toaster.push(
      Messages("warning", message),
      {
        placement: "topEnd",
        duration: 3000,
      }
    );
    setIsSubmitButtonDisabled(false);
    return;
  };

  const StatusOldBatchNumber = (index) =>{
    let oldData = [...oldBatchNumber]
    if (oldData[index].Status_active == 1) {
        oldData[index].Status_active = 0
    }else{
      oldData[index].Status_active = 1
    }
    setOldBatchNumber(oldData)
  }

   //control batchNumber
   const addNewBatchNumber = () =>{
    let newAddBatchNumber = {
      batch_number:''
    }

    setNewBatchNumber([...newBatchNumber, newAddBatchNumber])
  }

  const removeNewBatchNumber = () =>{
    let dataBatchNumber = [...newBatchNumber];
    if (dataBatchNumber.length <= 0) {
      console.log('Cannot Delete !');
    } else {
      dataBatchNumber.pop();
      setNewBatchNumber(dataBatchNumber);
    }
  }

  const removeNewBatchNumberIndex = (index) => {
    let dataBatchNumber = [...newBatchNumber];
    if (dataBatchNumber.length <= 0) {
      console.log('Cannot Delete !');
    } else {
      dataBatchNumber.splice(index, 1);
      setNewBatchNumber(dataBatchNumber);
    }
  }

  const handleNewBatchForm = (index, v) => {
    let data = [...newBatchNumber];
    // data.desc = v;
    data[index].batch_number = v;
    setNewBatchNumber(data);
  };

  return (
    <>
      <div>
        <Head>
          <title>Edit Protocol</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <MainContent>
          <ModuleContentHeader
            breadcrumbs={breadcrumbsData}
            module_name={moduleName}
          />

          <Form layout="horizontal">
            <Stack alignItems="flex-start" direction="column" spacing={9}>
            <div>
                <p>Title Protocol:</p>
                <Input
                value={title}
                onChange={v=>setTitle(v)}
                style={{ width: 224 }} 
                />
              </div>
              <Stack direction="column" alignItems="flex-start">
                <p className="font-bold">Tujuan : </p>
                <Editor contentValue={tujuan} valueHandler={content => setTujuan(content)} />
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p className="font-bold">Ruang Lingkup : </p>
                <Editor contentValue={ruangLingkup} valueHandler={content => setRuangLingkup(content)} />
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p className="font-bold">Dokumen Acuan : </p>
                <Editor contentValue={dokumenAcuan} valueHandler={content => setDokumenAcuan(content)} />
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p className="font-bold">Tanggung Jawab : </p>
                <Editor contentValue={tanggungJawab} valueHandler={content => setTanggungJawab(content)} />
              </Stack>
              <Stack
                direction="column"
                alignItems="flex-start"
                style={{ marginTop: '1rem' }}
              >
                <p className="font-bold">Rancangan Studi : </p>
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p>Nama Produk : </p>
                <Editor contentValue={namaProduk} valueHandler={content => setNamaProduk(content)} />
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p>Kode Produk : </p>
                <Editor contentValue={kodeProduk} valueHandler={content => setKodeProduk(content)} />
              </Stack>
              {/* <Stack direction="column" alignItems="flex-start">
                <p>Nomor Batch : </p>
                <Editor contentValue={nomorBatch} valueHandler={content => setNomorBatch(content)} />
              </Stack> */}
              <Stack direction="column" alignItems="flex-start">
                <p>Nomor PPI : </p>
                <Editor contentValue={nomorPPI} valueHandler={content => setNomorPPI(content)} />
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p>Batch Size : </p>
                <Editor contentValue={batchSize} valueHandler={content => setBatchSize(content)} />
              </Stack>
              <Form.Group
                controlId="dibuatOleh"
                style={{ marginBottom: '1em' }}
              >
                <Form.ControlLabel>
                  <p className="text-xs">Dibuat Oleh :</p>
                </Form.ControlLabel>
                <Form.Control
                  name="dibuatOleh"
                  type="text"
                  value={dibuatOleh}
                  readOnly={true}
                  required
                />
              </Form.Group>
            </Stack>

            <Form.Group
              controlId="komponenPenyimpanan"
              style={{ marginTop: '2em' }}
            >
              <Form.Group>
                <Form.ControlLabel>
                  <strong>Kondisi Penyimpanan :</strong>
                </Form.ControlLabel>
              </Form.Group>
              <table className="table" style={{ maxWidth: 546 }}>
                <thead>
                  <tr>
                    <th scope="col">No</th>
                    <th scope="col" style={{ minWidth: 250 }}>
                      Kriteria
                    </th>
                    {cycleMonthData.map((value) => (
                      <th scope="col" className="text-center">
                        {value}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td scope="col">1</td>
                    <td scope="col">T30 (30 &deg;C &plusmn; 2&deg;C / 75% &plusmn; 5% RH)</td>
                    {cycleMonthData.map((item) => (
                      <td scope="col">
                        <input
                          type="checkbox"
                          name="T30"
                          value={item}
                          onChange={t30ChangeHandler}
                          checked={isChecked('T30', parseInt(item))}
                          disabled
                        />
                      </td>
                    ))}
                  </tr>
                  <tr>
                    <td scope="col">2</td>
                    <td scope="col">T40 (40 &deg;C &plusmn; 2&deg;C / 75% &plusmn; 5% RH)</td>
                    {cycleMonthData.map((item) => (
                      <td scope="col">
                        <input
                          type="checkbox"
                          name="T40"
                          value={item}
                          onChange={t40ChangeHandler}
                          checked={isChecked('T40', parseInt(item))}
                          disabled
                        />
                      </td>
                    ))}
                  </tr>
                </tbody>
              </table>
            </Form.Group>

            <Form.Group controlId="komposisiFormula">
              <Stack>
                <label htmlFor="komposisiFormula">
                  <strong>Komposisi Formula :</strong>
                </label>
              </Stack>
              <Stack>
                <Editor contentValue={komposisiFormula} valueHandler={content => setKomposisiFormula(content)} />
              </Stack>
            </Form.Group>
            
            {oldBatchNumber != null && oldBatchNumber != undefined &&
            <Form.Group controlId="oldBatchNumberProtocol">            
            <Tag color='green' style={{ marginBottom: 2 }}>Old Batch Number</Tag>
              {oldBatchNumber.map((input,index) => {
              return (                
                  <Stack direction="column" alignItems="flex-start">
                      <Stack
                      style={{
                          padding: '0.5em',
                          border: '0.1em solid #adadad',
                          borderRadius: '5px 5px 5px 5px',
                          marginBottom: '0.5em',
                      }}
                      >
                        <Stack
                            direction="column"
                            alignItems="flex-start"
                            style={{ marginLeft: '0.5em' }}
                        >
                            <p>
                            <strong>Batch Number</strong>
                            </p>
                            {(
                                <Stack>
                                <Input
                                    value={input.Batch_number}
                                    as="textarea"
                                    type="text"
                                    required
                                    readOnly
                                    />
                                </Stack>
                            )}       
                        </Stack>
                        <Stack direction='column'
                            style={{ marginLeft: '0.5em' }}>
                            <p><strong>Is Active</strong></p>
                            <Checkbox checked={input.Status_active == 1} onChange={()=>StatusOldBatchNumber(index)}></Checkbox>
                        </Stack>
                      </Stack>                               
                  </Stack>         
              );
              })}
            </Form.Group>
            }

            <Form.Group controlId="newBatchNumberProtocol">
            <Tag color='orange' style={{ marginBottom: 2 }}>Added Parameter</Tag>
              <Stack direction="column" alignItems="flex-start">
                <p className="mb-2">
                  <strong>Batch Number :</strong>
                </p>
                <Stack spacing={3} style={{ marginBottom: '0.5em' }}>
                  <IconButton
                    icon={<FontAwesomeIcon icon={faPlus} />}
                    color="green"
                    appearance="primary"
                    circle
                    onClick={addNewBatchNumber}
                  />
                  <IconButton
                    icon={<FontAwesomeIcon icon={faMinus} />}
                    color="red"
                    appearance="primary"
                    circle
                    onClick={removeNewBatchNumber}
                  />
                </Stack>
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                {newBatchNumber.map((input, index) => {
                  return (
                    <Stack
                      style={{
                        padding: '0.5em',
                        border: '0.1em solid #adadad',
                        borderRadius: '5px 5px 5px 5px',
                        marginBottom: '0.5em',
                      }}
                    >
                      <Stack
                        direction="column"
                        alignItems="flex-start"
                        style={{ marginLeft: '0.5em' }}
                      >
                        <p>
                          <strong>Batch Number</strong>
                        </p>
                        {(
                            <Stack>
                              <Input
                                value={input.batch_number}
                                as="textarea"
                                onChange={(v) => {
                                   handleNewBatchForm(index, v);
                                  //console.log(v)
                                }}
                                type="text"
                                required
                                />

                              <IconButton size='sm' icon={<Trash  />} 
                                appearance='ghost'
                                color='red' 
                                style={{marginLeft:'5px'}}
                                title='Delete index' onClick={() => 
                                {
                                  removeNewBatchNumberIndex(index)           
                                }} 
                              />
                            </Stack>
                          )}
                           
                      </Stack>
                    </Stack>
                  );
                })}
              </Stack>
            </Form.Group>

            <Form.Group controlId="parameterPengujianSpesifikasi">
              <p className="mb-2">
                <strong>Parameter Pengujian & Spesifikasi :</strong>
              </p>

              <Stack direction="column" alignItems="flex-start">
                <Tag color='green' style={{ marginBottom: 2 }}>Old Parameter</Tag>
                {inputFields?.length > 0 &&
                  inputFields.map((item, indexInputFields) => {
                    return (
                      <>
                        <Stack
                          style={{
                            padding: '0.5em',
                            border: '0.1em solid #adadad',
                            borderRadius: '5px 5px 5px 5px',
                            marginBottom: '0.5em',
                          }}
                        >
                          <Stack direction="column" alignItems="flex-start">
                            <p>
                              <strong>Parameter</strong>
                            </p>
                            <TreePicker
                              data={treePickerData}
                              value={valueSelectedParam[indexInputFields] ? valueSelectedParam[indexInputFields] : ""}
                              defaultExpandAll
                              onChange={(v) => handleFormParameter(index, v)}
                              style={{ minWidth: 246 }}
                              readOnly
                            />
                          </Stack>
                          <Stack
                            direction="column"
                            alignItems="flex-start"
                            style={{ marginLeft: '0.5em' }}
                          >
                            <p>
                              <strong>Spesifikasi</strong>
                            </p>
                            {item.desc !== undefined &&
                              item.desc !== null && 
                              item.input_Method_Desc.toLowerCase() === 'description'&&
                              (
                                <Input
                                  value={item.desc}
                                  onChange={(value) => 
                                    oldDataValueChange(indexInputFields, "desc", value)
                                  }
                                />
                              )}
                            {item.value_reference !== undefined &&
                              item.value_reference !== null &&
                              item.value_reference !== 0 && 
                              item.input_Method_Desc.toLowerCase() === 'absolute'
                              && (
                                <InputNumber
                                  value={item.value_reference}
                                  onChange={(value) => {
                                    oldDataValueChange(indexInputFields, "value_reference", parseFloat(value));
                                  }}
                                />
                              )}
                            {item.min_value !== undefined &&
                              item.min_value !== null && 
                              item.input_Method_Desc.toLowerCase() === 'range'&&
                              (
                                <Stack>
                                  <InputNumber
                                    value={item.min_value}
                                    onChange={(value) => {
                                      oldDataValueChange(indexInputFields, "min_value", parseFloat(value));
                                    }}
                                  />{' '}
                                  -{' '}
                                  <InputNumber
                                    value={item.max_value}
                                    style={{ maxWidth: '7em' }}
                                    onChange={(value) => {
                                      oldDataValueChange(indexInputFields, "max_value", parseFloat(value));
                                    }}
                                  />
                                </Stack>
                              )}
                          </Stack>
                          <Stack direction='column'
                            style={{ marginLeft: '0.5em' }}>
                            <p><strong>Is Active</strong></p>
                            <Checkbox checked={item.status_active == 1} onChange={() => oldStatusChangeHandler(indexInputFields)}></Checkbox>
                          </Stack>
                        </Stack >
                      </>
                    );
                  })}
                {/* Button */}
                <Stack spacing={3} style={{ marginBottom: '0.5em' }}>
                  <IconButton
                    icon={<FontAwesomeIcon icon={faPlus} />}
                    color="green"
                    appearance="primary"
                    circle
                    onClick={addParameterPengujian}
                  />
                  <IconButton
                    icon={<FontAwesomeIcon icon={faMinus} />}
                    color="red"
                    appearance="primary"
                    circle
                    onClick={removeParameterPengujian}
                  />
                </Stack>
                {newDataInputFields.length > 0 && <Stack direction='column' alignItems='flex-start'>
                  <Tag color='orange' style={{ marginBottom: 2 }}>Added Parameter</Tag>
                  {newDataInputFields.map((input, index) => {
                    return (
                      <>
                        <Stack
                          style={{
                            padding: '0.5em',
                            border: '0.1em solid #adadad',
                            borderRadius: '5px 5px 5px 5px',
                            marginBottom: '0.5em',
                          }}
                        >
                          <Stack direction="column" alignItems="flex-start">
                            <p>
                              <strong>Parameter</strong>
                            </p>
                            <TreePicker
                              data={treePickerData}
                              defaultExpandAll
                              onChange={(v) => handleFormParameter(index, v)}
                              style={{ minWidth: 246 }}
                            />
                          </Stack>
                          <Stack
                            direction="column"
                            alignItems="flex-start"
                            style={{ marginLeft: '0.5em' }}
                          >
                            <p>
                              <strong>Spesifikasi</strong>
                            </p>
                            {arrSelectedParameter[index].Method_Desc ==
                              undefined && <Input disabled={true} />}
                            {arrSelectedParameter[index].Method_Desc !==
                              undefined &&
                              arrSelectedParameter[index].Method_Desc !== null &&
                              arrSelectedParameter[
                                index
                              ].Method_Desc.toLowerCase() === 'absolute' && (
                                <InputNumber
                                  value={absoluteValue}
                                  onChange={(v, e) =>
                                    handleFormChange(index, 'value_reference', e)
                                  }
                                  required
                                />
                              )}
                            {arrSelectedParameter[index].Method_Desc !==
                              undefined &&
                              arrSelectedParameter[index].Method_Desc !== null &&
                              arrSelectedParameter[
                                index
                              ].Method_Desc.toLowerCase() === 'description' && (
                                <Input
                                  onChange={(v) => {
                                    handleFormDesc(index, v);
                                  }}
                                  type="text"
                                  required
                                />
                              )}
                            {arrSelectedParameter[index].Method_Desc !==
                              undefined &&
                              arrSelectedParameter[index].Method_Desc !== null &&
                              arrSelectedParameter[
                                index
                              ].Method_Desc.toLowerCase() === 'range' && (
                                <Stack>
                                  <InputNumber
                                    value={rangeMin}
                                    onChange={(v, e) =>
                                      handleFormChange(index, 'min_value', e)
                                    }
                                    required
                                    style={{ maxWidth: '7em' }}
                                  />{' '}
                                  -{' '}
                                  <InputNumber
                                    value={rangeMax}
                                    onChange={(v, e) =>
                                      handleFormChange(index, 'max_value', e)
                                    }
                                    required
                                    style={{ maxWidth: '7em' }}
                                  />
                                </Stack>
                              )}
                          </Stack>
                        </Stack>
                      </>
                    );
                  })}
                </Stack>}

              </Stack>
            </Form.Group>

            <Form.Group controlId="keteranganTambahan">
              <Stack>
                <label htmlFor="keteranganTambahan">
                  <strong>Keterangan Tambahan :</strong>
                </label>
              </Stack>
              <Stack>
                <Editor contentValue={additionalNotes} valueHandler={content => setAdditionalNotes(content)} />
              </Stack>
            </Form.Group>

            <Form.Group controlId="Remark Change">
              <Stack>
                <label htmlFor="Remark Change">
                  <strong>Remark Change :</strong>
                </label>
              </Stack>
              <Stack>
                <Whisper trigger="focus" speaker={<Tooltip>Required</Tooltip>}>
                  <Input as="textarea" rows={3} size='lg' placeholder="Remark Change ..." value={remarkChange} onChange={value => setRemarkChange(value)} />
                </Whisper>
              </Stack>
            </Form.Group>

            <Form.Group controlId="pilihReviewer">
              <Stack>
                <label htmlFor="pilihReviewer">
                  <strong>Pilih Reviewer 1 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerReviewerData}
                  defaultExpandAll
                  onChange={value => selectReviewerChangeHandler(value, 0)}
                  value={selectedReviewer[0] ? selectedReviewer[0] : ""}
                  style={{ minWidth: 246 }}
                />
              </Stack>
            </Form.Group>
            <Form.Group controlId="pilihReviewer2">
              <Stack>
                <label htmlFor="pilihReviewer2">
                  <strong>Pilih Reviewer 2 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerReviewerData2}
                  defaultExpandAll
                  onChange={value => selectReviewer2ChangeHandler(value, 1)}
                  value={selectedReviewer[1] ? selectedReviewer[1] : ""}
                  style={{ minWidth: 246 }}
                  disabled={selectedReviewer[0] === null || selectedReviewer[0] === undefined || selectedReviewer[0] === ''}
                />
              </Stack>
            </Form.Group>
            <Form.Group controlId="pilihReviewer2">
              <Stack>
                <label htmlFor="pilihReviewer2">
                  <strong>Pilih Reviewer 3 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerReviewerData3}
                  defaultExpandAll
                  onChange={value => selectReviewer3ChangeHandler(value, 2)}
                  value={selectedReviewer[2] ? selectedReviewer[2] : ""}
                  style={{ minWidth: 246 }}
                  disabled={selectedReviewer[1] === null || selectedReviewer[1] === undefined || selectedReviewer[1] === ''}
                />
              </Stack>
            </Form.Group>
            <Form.Group controlId="pilihApprover">
              <Stack>
                <label htmlFor="pilihApprover">
                  <strong>Pilih Approver 1 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerApproverData}
                  defaultExpandAll
                  onChange={value => selectApproverChangeHandler(value, 0)}
                  value={selectedApprover[0] ? selectedApprover[0] : ""}
                  style={{ minWidth: 246 }}
                />
              </Stack>
            </Form.Group>
            <Form.Group controlId="pilihApprover">
              <Stack>
                <label htmlFor="pilihApprover">
                  <strong>Pilih Approver 2 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerApproverData2}
                  defaultExpandAll
                  onChange={value => selectApproverChangeHandler2(value, 1)}
                  value={selectedApprover[1] ? selectedApprover[1] : ""}
                  disabled={selectedApprover[0] === null || selectedApprover[0] === undefined || selectedApprover[0] === ''}
                  style={{ minWidth: 246 }}
                />
              </Stack>
            </Form.Group>
            <Form.Group controlId="pilihApprover">
              <Stack>
                <label htmlFor="pilihApprover">
                  <strong>Pilih Approver 3 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerApproverData3}
                  defaultExpandAll
                  onChange={value => selectApproverChangeHandler3(value, 2)}
                  value={selectedApprover[2] ? selectedApprover[2] : ""}
                  disabled={selectedApprover[1] === null || selectedApprover[1] === undefined || selectedApprover[1] === ''}
                  style={{ minWidth: 246 }}
                />
              </Stack>
            </Form.Group>

            <Form.Group>
              <Button
                onClick={submitHandler}
                appearance="primary"
                type="submit"
                disabled={isSubmitButtonDisabled}
              >
                Submit
              </Button>
              <Button
                appearance="default"
                onClick={() => router.back()}
                disabled={isSubmitButtonDisabled}
              >
                Cancel
              </Button>
            </Form.Group>
          </Form>

        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  const { noProtocol } = query;

  return {
    props: {
      noProtocol,
    },
  };
}
