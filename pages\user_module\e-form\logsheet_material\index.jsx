import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Button,
  IconButton,
  Stack,
  Breadcrumb,
  Tag,
  Table,
  Panel,
  Input,
  InputGroup,
  Pagination,
  Modal,
  Form,
  useToaster,
  DatePicker,
  SelectPicker,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";
import dateFormatterLong from "@/lib/function/date-formatter-long";
import dateTimeFormatter from "@/lib/function/date-time-formatter";
import dateFormatterDash from "@/lib/function/date-formatter-dash";

import API_EformLogsheetMaterial from "@/pages/api/e_form/api_eform_logsheet_material";
import API_EformLogsheetMaterialCriteria from "@/pages/api/e_form/api_eform_logsheet_material_criteria";

export default function EFormLogsheetMaterialPage() {
  const router = useRouter();
  const toaster = useToaster();
  const [props, setProps] = useState([]);
  const { HeaderCell, Cell, Column } = Table;

  const [moduleName, setModuleName] = useState("");
  const [elmData, setElmData] = useState([]);
  const [elmCriteria, setElmCriteria] = useState([]);

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [selectedElm, setSelectedElm] = useState(null);

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  useEffect(() => {
    const fetchData = async () => {
      const res_elm = await API_EformLogsheetMaterial().getAll();
      setElmData(res_elm.data || []);
      console.log("res_elm", res_elm);

      const res_elm_criteria =
        await API_EformLogsheetMaterialCriteria().getAll();
      setElmCriteria(res_elm_criteria.data || []);
      console.log("res_elm_criteria", res_elm_criteria);
    };

    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    console.log("dataLogin", dataLogin);
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("e-form/logsheet_material")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      fetchData();
    }
  }, []);

  const emptyFormValue = {
    date: null,
    id_criteria: null,
    item_sample_number: null,
    item_code: null,
    item_desc: null,
    lot_qc: null,
    rsr_printing_date: null,
    rsr_printing_initial: null,
    sampling_date: null,
    sampling_initial: null,
    raman_printing_date: null,
    raman_printing_initial: null,
    email_1: null,
    email_2: null,
    created_by: null,
    updated_by: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id",
    "date",
    "criteria",
    "item_sample_number",
    "item_code",
    "item_desc",
    "lot_qc",
    "rsr_printing_date",
    "rsr_printing_initial",
    "sampling_date",
    "sampling_initial",
    "raman_printing_date",
    "raman_printing_initial",
    "email_1",
    "email_2",
    "created_at",
    "created_by",
    "updated_at",
    "updated_by",
  ];

  const datas =
    elmData && elmData.length > 0
      ? elmData
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .concat(
                item.date ? dateFormatterLong(item.date).toLowerCase() : ""
              )
              .concat(
                item.created_at
                  ? dateTimeFormatter(
                      item.created_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .concat(
                item.updated_at
                  ? dateTimeFormatter(
                      item.updated_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .concat(
                item.rsr_printing_date
                  ? dateFormatterLong(item.rsr_printing_date).toLowerCase()
                  : ""
              )
              .concat(
                item.sampling_date
                  ? dateFormatterLong(item.sampling_date).toLowerCase()
                  : ""
              )
              .concat(
                item.raman_printing_date
                  ? dateFormatterLong(item.raman_printing_date).toLowerCase()
                  : ""
              )
              .some((val) => val?.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const dataSearch = elmData
    ? elmData.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];

  const handleAddElm = async () => {
    try {
      let result = await API_EformLogsheetMaterial().add({
        ...formValue,
        created_by: props.employee_id,
      });
      if (result?.status !== 200) {
        throw "Failed adding E-Form Logsheet Material";
      }

      const res = await API_EformLogsheetMaterial().getAll();
      setElmData(res.data);

      setShowAddModal(false);

      // Show the toast message
      toaster.push(
        Messages("success", "Success adding E-Form Logsheet Material!"),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );

      // Reset the form value
      setFormValue(emptyFormValue);
    } catch (error) {
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleEditElm = async () => {
    try {
      let result = await API_EformLogsheetMaterial().edit({
        ...formValue,
        id: selectedElm,
        updated_by: props.employee_id,
      });
      if (result?.status !== 200) {
        throw "Failed editing E-Form Logsheet Material";
      }

      console.log("result", result);

      const res = await API_EformLogsheetMaterial().getAll();
      setElmData(res.data);
      setShowEditModal(false);

      // Show the toast message
      toaster.push(
        Messages("success", "Success editing E-Form Logsheet Material!"),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );

      // Reset the form value
      setFormValue(emptyFormValue);
    } catch (error) {
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleExportExcel = () => {
    if (elmData.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }

    const data = elmData.map((item) => ({
      ID: item.id,
      Date: item.date ? dateFormatterDash(item.date) : "",
      Criteria: item.criteria,
      "Item Sample Number": item.item_sample_number,
      "Item Code": item.item_code,
      Description: item.item_desc,
      "Lot QC": item.lot_qc,
      "RSR Printing Date": item.rsr_printing_date
        ? dateFormatterDash(item.rsr_printing_date)
        : "",
      "RSR Printing Initial": item.rsr_printing_initial,
      "Sampling Date": item.sampling_date
        ? dateFormatterDash(item.sampling_date)
        : "",
      "Sampling Initial": item.sampling_initial,
      "Raman Printing Date": item.raman_printing_date
        ? dateFormatterDash(item.raman_printing_date)
        : "",
      "Raman Printing Initial": item.raman_printing_initial,
      "Email 1": item.email_1,
      "Email 2": item.email_2,
      "Created At": item.created_at
        ? dateTimeFormatter(item.created_at, "id-ID", "seconds")
        : "",
      "Created By": item.created_by,
      "Updated At": item.updated_at
        ? dateTimeFormatter(item.updated_at, "id-ID", "seconds")
        : "",
      "Updated By": item.updated_by,
    }));

    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const filename = `E-Form Logsheet Material ${date}.xlsx`;

    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <div>
        <Head>
          <title>E-Form Logsheet Material</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>E-Form</Breadcrumb.Item>
                  <Breadcrumb.Item active>Logsheet Material</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusIcon />}
                    appearance="primary"
                    onClick={() => {
                      setShowAddModal(true);
                    }}
                  >
                    Add
                  </IconButton>
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              data={datas}
              bordered
              cellBordered
              height={400}
              onRowClick={(rowData) => {
                console.log(rowData);
              }}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>

              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Date</HeaderCell>
                <Cell dataKey="date">
                  {(rowData) => {
                    return dateFormatterLong(rowData?.date);
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Criteria</HeaderCell>
                <Cell dataKey="criteria" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Item Sample Number</HeaderCell>
                <Cell dataKey="item_sample_number" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Item Code</HeaderCell>
                <Cell dataKey="item_code" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Description</HeaderCell>
                <Cell dataKey="item_desc" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Lot QC</HeaderCell>
                <Cell dataKey="lot_qc" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>RSR Printing Date</HeaderCell>
                <Cell dataKey="rsr_printing_date">
                  {(rowData) => {
                    return dateFormatterLong(rowData?.rsr_printing_date);
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>RSR Printing Initial</HeaderCell>
                <Cell dataKey="rsr_printing_initial" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Sampling Date</HeaderCell>
                <Cell dataKey="sampling_date">
                  {(rowData) => {
                    return dateFormatterLong(rowData?.sampling_date);
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Sampling Initial</HeaderCell>
                <Cell dataKey="sampling_initial" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Raman Printing Date</HeaderCell>
                <Cell dataKey="raman_printing_date">
                  {(rowData) => {
                    return dateFormatterLong(rowData?.raman_printing_date);
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Raman Printing Initial</HeaderCell>
                <Cell dataKey="raman_printing_initial" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Email 1</HeaderCell>
                <Cell dataKey="email_1" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Email 2</HeaderCell>
                <Cell dataKey="email_2" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created At</HeaderCell>
                <Cell dataKey="created_at">
                  {(rowData) => {
                    return dateTimeFormatter(
                      rowData?.created_at,
                      "id-ID",
                      "seconds"
                    );
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell dataKey="created_by" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated At</HeaderCell>
                <Cell dataKey="updated_at">
                  {(rowData) => {
                    return dateTimeFormatter(
                      rowData?.updated_at,
                      "id-ID",
                      "seconds"
                    );
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>

              <Column width={80} fixed="right" align="center">
                <HeaderCell>...</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <a
                      className="cursor-pointer"
                      onClick={() => {
                        setSelectedElm(rowData.id);
                        setFormValue({
                          ...rowData,
                          date: new Date(rowData.date),
                          rsr_printing_date: new Date(
                            rowData.rsr_printing_date
                          ),
                          sampling_date: new Date(rowData.sampling_date),
                          raman_printing_date: new Date(
                            rowData.raman_printing_date
                          ),
                        });
                        setShowEditModal(true);
                      }}
                    >
                      Edit
                    </a>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                total={searchKeyword ? dataSearch.length : elmData.length}
                limitOptions={[10, 30, 50]}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Add Modal */}
        <Modal
          backdrop="static"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Add E-Form Logsheet Material</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Date</Form.ControlLabel>
                <DatePicker
                  name="date"
                  value={formValue.date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, date: value });
                  }}
                  block
                  format="dd-MM-yyyy"
                  placement="auto"
                  oneTap
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Criteria</Form.ControlLabel>
                <SelectPicker
                  name="id_criteria"
                  value={formValue.id_criteria}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_criteria: value });
                  }}
                  block
                  data={elmCriteria}
                  labelKey="criteria"
                  valueKey="id"
                  label="Criteria"
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Item Sample Number</Form.ControlLabel>
                <Form.Control
                  name="item_sample_number"
                  value={formValue.item_sample_number}
                  onChange={(value) => {
                    setFormValue({ ...formValue, item_sample_number: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Item Code</Form.ControlLabel>
                <Form.Control
                  name="item_code"
                  value={formValue.item_code}
                  onChange={(value) => {
                    setFormValue({ ...formValue, item_code: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Description</Form.ControlLabel>
                <Input
                  name="item_desc"
                  value={formValue.item_desc}
                  onChange={(value) => {
                    setFormValue({ ...formValue, item_desc: value });
                  }}
                  as="textarea"
                  rows={3}
                  placeholder="Description"
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Lot QC</Form.ControlLabel>
                <Form.Control
                  name="lot_qc"
                  value={formValue.lot_qc}
                  onChange={(value) => {
                    setFormValue({ ...formValue, lot_qc: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>RSR Printing Date</Form.ControlLabel>
                <DatePicker
                  name="rsr_printing_date"
                  value={formValue.rsr_printing_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, rsr_printing_date: value });
                  }}
                  block
                  format="dd-MM-yyyy"
                  placement="auto"
                  oneTap
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>RSR Printing Initial</Form.ControlLabel>
                <Form.Control
                  name="rsr_printing_initial"
                  value={formValue.rsr_printing_initial}
                  onChange={(value) => {
                    setFormValue({ ...formValue, rsr_printing_initial: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Sampling Date</Form.ControlLabel>
                <DatePicker
                  name="sampling_date"
                  value={formValue.sampling_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, sampling_date: value });
                  }}
                  block
                  format="dd-MM-yyyy"
                  placement="auto"
                  oneTap
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Sampling Initial</Form.ControlLabel>
                <Form.Control
                  name="sampling_initial"
                  value={formValue.sampling_initial}
                  onChange={(value) => {
                    setFormValue({ ...formValue, sampling_initial: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Raman Printing Date</Form.ControlLabel>
                <DatePicker
                  name="raman_printing_date"
                  value={formValue.raman_printing_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, raman_printing_date: value });
                  }}
                  block
                  format="dd-MM-yyyy"
                  placement="auto"
                  oneTap
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Raman Printing Initial</Form.ControlLabel>
                <Form.Control
                  name="raman_printing_initial"
                  value={formValue.raman_printing_initial}
                  onChange={(value) => {
                    setFormValue({
                      ...formValue,
                      raman_printing_initial: value,
                    });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Email 1</Form.ControlLabel>
                <Form.Control
                  name="email_1"
                  value={formValue.email_1}
                  onChange={(value) => {
                    setFormValue({ ...formValue, email_1: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Email 2</Form.ControlLabel>
                <Form.Control
                  name="email_2"
                  value={formValue.email_2}
                  onChange={(value) => {
                    setFormValue({ ...formValue, email_2: value });
                  }}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowAddModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleAddElm();
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Edit Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setFormValue(emptyFormValue);
          }}
        >
          <Modal.Header>
            <Modal.Title>Edit E-Form Logsheet Material</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Date</Form.ControlLabel>
                <DatePicker
                  name="date"
                  value={formValue.date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, date: value });
                  }}
                  block
                  format="dd-MM-yyyy"
                  placement="auto"
                  oneTap
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Criteria</Form.ControlLabel>
                <SelectPicker
                  name="id_criteria"
                  value={formValue.id_criteria}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_criteria: value });
                  }}
                  block
                  data={elmCriteria}
                  labelKey="criteria"
                  valueKey="id"
                  label="Criteria"
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Item Sample Number</Form.ControlLabel>
                <Form.Control
                  name="item_sample_number"
                  value={formValue.item_sample_number}
                  onChange={(value) => {
                    setFormValue({ ...formValue, item_sample_number: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Item Code</Form.ControlLabel>
                <Form.Control
                  name="item_code"
                  value={formValue.item_code}
                  onChange={(value) => {
                    setFormValue({ ...formValue, item_code: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Description</Form.ControlLabel>
                <Input
                  name="item_desc"
                  value={formValue.item_desc}
                  onChange={(value) => {
                    setFormValue({ ...formValue, item_desc: value });
                  }}
                  as="textarea"
                  rows={3}
                  placeholder="Textarea"
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Lot QC</Form.ControlLabel>
                <Form.Control
                  name="lot_qc"
                  value={formValue.lot_qc}
                  onChange={(value) => {
                    setFormValue({ ...formValue, lot_qc: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>RSR Printing Date</Form.ControlLabel>
                <DatePicker
                  name="rsr_printing_date"
                  value={formValue.rsr_printing_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, rsr_printing_date: value });
                  }}
                  block
                  format="dd-MM-yyyy"
                  placement="auto"
                  oneTap
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>RSR Printing Initial</Form.ControlLabel>
                <Form.Control
                  name="rsr_printing_initial"
                  value={formValue.rsr_printing_initial}
                  onChange={(value) => {
                    setFormValue({ ...formValue, rsr_printing_initial: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Sampling Date</Form.ControlLabel>
                <DatePicker
                  name="sampling_date"
                  value={formValue.sampling_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, sampling_date: value });
                  }}
                  block
                  format="dd-MM-yyyy"
                  placement="auto"
                  oneTap
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Sampling Initial</Form.ControlLabel>
                <Form.Control
                  name="sampling_initial"
                  value={formValue.sampling_initial}
                  onChange={(value) => {
                    setFormValue({ ...formValue, sampling_initial: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Raman Printing Date</Form.ControlLabel>
                <DatePicker
                  name="raman_printing_date"
                  value={formValue.raman_printing_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, raman_printing_date: value });
                  }}
                  block
                  format="dd-MM-yyyy"
                  placement="auto"
                  oneTap
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Raman Printing Initial</Form.ControlLabel>
                <Form.Control
                  name="raman_printing_initial"
                  value={formValue.raman_printing_initial}
                  onChange={(value) => {
                    setFormValue({
                      ...formValue,
                      raman_printing_initial: value,
                    });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Email 1</Form.ControlLabel>
                <Form.Control
                  name="email_1"
                  value={formValue.email_1}
                  onChange={(value) => {
                    setFormValue({ ...formValue, email_1: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Email 2</Form.ControlLabel>
                <Form.Control
                  name="email_2"
                  value={formValue.email_2}
                  onChange={(value) => {
                    setFormValue({ ...formValue, email_2: value });
                  }}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowEditModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleEditElm();
              }}
              appearance="primary"
              type="submit"
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
