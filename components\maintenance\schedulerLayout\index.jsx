import React, { useState, useEffect } from 'react';
import { Panel, IconButton } from 'rsuite';
import PageTopIcon from '@rsuite/icons/PageTop';
import PageEndIcon from '@rsuite/icons/PageEnd';

// Sample helper: customize as needed
const getPriorityColor = (priority) => {
  const colors = {
    high: '#ff4d4f',
    medium: '#faad14',
    low: '#1890ff'
  };
  return colors[priority?.toLowerCase()] || '#d9d9d9';
};

// Sample date formatter: customize as needed
const formatDate = (dateStr) => {
  const date = new Date(dateStr);
  return date.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
};

export default function ScheduleCarouselComponent({ schedules }) {
  const [activeIndex, setActiveIndex] = useState(0);

  // Sort schedules by date then priority
  const sortedSchedules = [...schedules].sort((a, b) => {
    const dateA = new Date(a?.scheduled_start_date || 0);
    const dateB = new Date(b?.scheduled_start_date || 0);
    if (dateA - dateB !== 0) return dateA - dateB;

    const priorityOrder = { high: 1, medium: 2, low: 3 };
    const priorityA = priorityOrder[a?.priority_disp?.toLowerCase()] || 4;
    const priorityB = priorityOrder[b?.priority_disp?.toLowerCase()] || 4;
    return priorityA - priorityB;
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prev) =>
        prev === sortedSchedules.length - 1 ? 0 : prev + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, [sortedSchedules.length]);

  const nextSlide = () => {
    setActiveIndex((prev) => (prev === sortedSchedules.length - 1 ? 0 : prev + 1));
  };

  const prevSlide = () => {
    setActiveIndex((prev) => (prev === 0 ? sortedSchedules.length - 1 : prev - 1));
  };

  if (!sortedSchedules || sortedSchedules.length === 0) {
    return (
      <Panel bordered style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
        No upcoming schedules available
      </Panel>
    );
  }

  const currentItem = sortedSchedules[activeIndex];

  return (
    <div style={{ position: 'relative', height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Arrows */}
      {sortedSchedules.length > 1 && (
        <>
          <IconButton
            icon={<PageTopIcon />}
            onClick={prevSlide}
            circle
            size="sm"
            style={{
              position: 'absolute',
              left: -10,
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 2,
              backgroundColor: '#fff',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
            }}
          />
          <IconButton
            icon={<PageEndIcon />}
            onClick={nextSlide}
            circle
            size="sm"
            style={{
              position: 'absolute',
              right: -10,
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 2,
              backgroundColor: '#fff',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
            }}
          />
        </>
      )}
  
      <Panel
        key={activeIndex}
        bordered
        style={{
          flex: 1, // 🧠 this makes Panel take up all available space in column
          display: 'flex',
          flexDirection: 'column',
          marginBottom: '12px',
          borderRadius: '6px',
          borderLeft: `4px solid ${getPriorityColor(currentItem?.priority_disp)}`
        }}
        header={
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 12px',
              backgroundColor: '#f8f9fa'
            }}
          >
            <span style={{ fontWeight: '600', fontSize: '0.9rem' }}>
              Upcoming: {currentItem?.scheduled_start_date ? formatDate(currentItem.scheduled_start_date) : 'No date'}
            </span>
          </div>
        }
      >
        <div style={{ padding: '12px', flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
          <div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
              <span style={{ fontSize: '0.85rem' }}>
                Priority:{' '}
                <strong
                  style={{
                    color: getPriorityColor(currentItem?.priority_disp),
                    padding: '2px 8px',
                    backgroundColor: `${getPriorityColor(currentItem?.priority_disp)}20`,
                    borderRadius: '4px'
                  }}
                >
                  {currentItem?.priority_disp}
                </strong>
              </span>
            </div>
  
            <div style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '4px' }}>
              <span style={{ fontSize: '0.85rem', color: '#666' }}>
                {currentItem?.asset_description || 'No description available'}
              </span>
            </div>
          </div>
  
          {sortedSchedules.length > 1 && (
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                marginTop: '16px',
                gap: '6px'
              }}
            >
              {sortedSchedules.map((_, index) => (
                <div
                  key={index}
                  onClick={() => setActiveIndex(index)}
                  style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    backgroundColor:
                      index === activeIndex ? getPriorityColor(currentItem?.priority_disp) : '#ddd',
                    cursor: 'pointer',
                    transition: 'background-color 0.3s'
                  }}
                />
              ))}
            </div>
          )}
        </div>
      </Panel>
    </div>
  );
  
}
