import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/reason_code${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function LkvMasterReasonCode(){
    return{
        getAllMasterReason: createApiFunction("get", "/get/all"),
        getAllMasterReasonActive : createApiFunction("get", "/get/all/active"),
        createMasterReason: createApiFunction("post", "/create"),
        updateMasterReason: createApiFunction("put", "/edit"),
        updateStatusMasterReason: createApiFunction("put", "/active")
    }
}