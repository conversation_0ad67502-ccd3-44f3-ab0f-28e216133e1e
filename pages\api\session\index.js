import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/${url}`,
        data
    )
        .then((res) => {return res.data})
        .catch((err) => {return err.response.data});
    return response;
};

export default function SessionAPI(){
    return{
        logout: createApiFunction("post", "logout"),
    }
}