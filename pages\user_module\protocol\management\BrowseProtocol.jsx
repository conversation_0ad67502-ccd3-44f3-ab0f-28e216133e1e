import { useState, useEffect } from 'react';
import ProtocolApi from '@/pages/api/protocolApi';
import Head from 'next/head';
import ContainerLayout from '@/components/layout/ContainerLayout';
import MainContent from '@/components/layout/MainContent';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import { useRouter } from 'next/router';
import Messages from '@/components/Messages';
import Swal from 'sweetalert2';
import {
    Button,
    Stack,
    Breadcrumb,
    Tag,
    Table,
    Panel,
    Input,
    InputGroup,
    Pagination,
    Divider,
    Modal,
    Form,
    Schema,
    useToaster,
    DatePicker,
    DateRangePicker,
    SelectPicker,
    IconButton,
} from "rsuite";
import withReactContent from 'sweetalert2-react-content';
import { paginate } from '@/utils/paginate';
import SearchIcon from '@rsuite/icons/Search';
import FileDownloadIcon from '@rsuite/icons/FileDownload';
import CloseOutlineIcon from '@rsuite/icons/CloseOutline';
import EditIcon from '@rsuite/icons/Edit';
import TrashIcon from '@rsuite/icons/Trash';


export default function BrowseProtocol() {
    const router = useRouter();
    const MySwal = withReactContent(Swal);
    const toaster = useToaster();
    const [selectedNoProtocol, setSelectedNoProtocol] = useState('');
    const [allProtocolHeaderData, setAllProtocolHeaderData] = useState([]);
    const [protocolHeaderData, setProtocolHeaderData] = useState([]);
    const { GetProtocolDataByNoProtocol, GetAllProtocolHeaderDataByDepartmentId, GetDataIsProtocolIsInEditProcess, GetReportExcel, PutDeleteProtocol, PutPrintCounter } = ProtocolApi();
    const [userDept, setUserDept] = useState('');
    const [moduleName, setModuleName] = useState('');
    const [userLogin, setUserLogin] = useState(null);
    const [breadcrumbsData, setBreadcrumbsData] = useState([]);
    const { HeaderCell, Cell, Column } = Table;
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [searchKeyword, setSearchKeyword] = useState("");
    const [dateRange, setDateRange] = useState([])
    let path = 'protocol/management/BrowseProtocol';

    // data to be displayed in the table
    var filteredData = allProtocolHeaderData.filter(item => 
        item.No_Protocol.toLowerCase().includes(searchKeyword.toLowerCase()) 
        || item.Created_by.toLowerCase().includes(searchKeyword.toLowerCase()) 
        || item.Department_Name.toLowerCase().includes(searchKeyword.toLowerCase()) 
        || item.Protocol_Status.toLowerCase().includes(searchKeyword.toLowerCase())
        || item.Product_Name.toLowerCase().includes(searchKeyword.toLowerCase())
        );
    var datas = paginate(filteredData, page, limit);

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setSortColumn(sortColumn);
        setSortType(sortType);
    };

    const handleDateRange = (dateFrom, dateTo) =>{
       
        // dateFrom = Date(dateFrom)
        // dateTo = Date(dateTo)
        // const date_from = new Date(dateFrom).toLocaleDateString("en-CA")
        // const date_to = new Date(dateTo).toLocaleDateString("en-CA")
        //console.log('check',dateFrom == new Date('2024-01-25'))
        // const date_from = new Date('2024-01-25');
        // const date_to = new Date('2024-02-01');
        //console.log('[datefrom] ', date_from,'[dateto] ', date_to)
        console.log('[protocol data]',allProtocolHeaderData)

        const filteredData = allProtocolHeaderData.filter(item => {
            const createdDate = new Date(item.Created_Date);
        return createdDate >= dateFrom && createdDate <= dateTo;
        });

        console.log('[protocolHeaderFilter]',filteredData)
        //const formattedDate = format(parsedDateTo, 'MMMM d, yyyy');
        setDateRange([dateFrom, dateTo]);


        if ((dateFrom == '' && dateTo == '' )|| dateFrom === null || dateFrom === undefined|| dateTo === null || dateTo === undefined){
            setAllProtocolHeaderData(protocolHeaderData)
        }else{
            setAllProtocolHeaderData(filteredData)
        }
    }

    const buttonEditHandler = async (noProtocol, statusProtocol) => {
        const reqData = {
            no_protocol: noProtocol
        };
        if (statusProtocol == 'Active') {
            const { status, isInEdit, message } = await GetDataIsProtocolIsInEditProcess(reqData);
            if (status == 200) {
                if (isInEdit) {
                    toaster.push(
                        Messages("warning", message ? message : "Protocol sedang diedit !"),
                        {
                            placement: "topEnd",
                            duration: 3000,
                        }
                    );
                    return;
                }

                router.push({ pathname: '/user_module/protocol/management/EditProtocol', query: { noProtocol: noProtocol, userDept: parseInt(userDept) } });
            }
        } else {
            // If protocol reject
            router.push({ pathname: '/user_module/protocol/management/EditRejectedProtocol', query: { noProtocol: noProtocol, userDept: parseInt(userDept) } });
        }
    };

    const GetAllProtocolDataByDepartment = async (idDept) => {
        const reqData = {
            id_department: parseInt(idDept)
        };

        const { data: data } = await GetAllProtocolHeaderDataByDepartmentId(reqData);

        if (data !== null && data.length > 0) {
            const newData = data.map(item => {
                let status = '';
                if (item.Document_Status == 0) {
                    status = 'Reject';
                } else if (item.Document_Status == 1) {
                    status = 'Active';
                } else {
                    status = 'Waiting';
                }
                item.Product_Name = item.Product_Name.replace(/<[^>]*>/g, '');

                item.Protocol_Status = status;
                return item;
            });

            setAllProtocolHeaderData(newData);
            setProtocolHeaderData(newData)
        } else {
            setAllProtocolHeaderData([]);
            setProtocolHeaderData([])
        }
    };

    // No_protocol string `json:"no_protocol"`
    // Deleted_by  string `json:"deleted_by"`
    // Reason      string `json:"reason"
    const DeleteProtocol = async (no_protocol) => {
        const reqData = {
            no_protocol: no_protocol,
            deleted_by: userLogin?.employee_id,
            reason: "obsolete"
        };

        const res = await PutDeleteProtocol(reqData);

        if (res.status == 200) {
            console.log("success delete")
        }else{
            console.log("gagal delete", res.message)
        }

        // router.reload()
    };

    const UpdatePrintProtocol = async (no_protocol, dept) => {
        const reqData = {
            no_protocol: no_protocol,
        };

        const res = await PutPrintCounter(reqData);

        if (res.status == 200) {
            console.log("success print")
            router.push({ pathname: '/user_module/protocol/management/PrintProtocolDocReporting', query: { noProtocol: no_protocol, userDept:dept } });
        }else{
            console.log("gagal print", res.message)
        }

        // router.reload()
    };

    useEffect(() => {
        const moduleNameValue = localStorage.getItem('module_name');
        const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
        console.log("data login", dataLogin)
        if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
            router.push('/');
            return;
        }

        // validate access for this page
        if (!dataLogin.menu_link_code) {
            router.push('/dashboard');
            return;
        }
        const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
            item.includes(path),
        );
        if (validateUserAccess.length === 0) {
            router.push('/dashboard');
            return;
        }

        if (
            moduleNameValue === null ||
            moduleNameValue === undefined ||
            moduleNameValue === ''
        ) {
            router.push('/dashboard');
            return;
        }

        const asPathWithoutQuery = router.asPath.split('?')[0];
        const asPathNestedRoutes = asPathWithoutQuery
            .split('/')
            .filter((v) => v.length);
        let routerBreadCrumbsData = [
            moduleNameValue,
            asPathNestedRoutes[1],
            asPathNestedRoutes[2],
        ];
        const capitalizeFirstLetter = (str) => {
            return str.charAt(0).toUpperCase() + str.slice(1);
        };
        const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
            const words = item.split(/(?=[A-Z])/);
            return words.map((word) => capitalizeFirstLetter(word)).join(' ');
        });
        setBreadcrumbsData(breadCrumbsResult);
        setUserDept(dataLogin.department);
        setUserLogin(dataLogin)
        setModuleName(moduleNameValue);

        GetAllProtocolDataByDepartment(dataLogin.department);

    }, []);

    const GetExcel = async (no_protocol_input) =>{
        const reqData = {
            no_protocol: no_protocol_input
        };

        const { data: data } = await GetReportExcel(reqData);
    }

    return (
        <>
            <div>
                <Head>
                    <title>Browse Protocol</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <MainContent>
                    <ModuleContentHeader
                        breadcrumbs={breadcrumbsData}
                        module_name={moduleName}
                    />
                    {allProtocolHeaderData?.length > 0 ? (
                        <>
                            <Stack direction='row' justifyContent='flex-end'>
                            <InputGroup inside>
                                    <DateRangePicker                                    
                                    onChange={(value) => {
                                        console.log('[daterange]',dateRange)
                                        console.log('[0]',value?.[0])
                                        console.log('[1]',value?.[1])
                                        handleDateRange(value?.[0] || "",value?.[1] || "")
                                    }}
                                    block
                                    />
                                </InputGroup>
                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="Search"
                                        value={searchKeyword}
                                        onChange={value => {
                                            setSearchKeyword(value);
                                            setPage(1);
                                        }}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}
                                    >
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                            
                            <Panel>
                                <Table
                                    bordered
                                    cellBordered
                                    height={400}
                                    data={datas}
                                    sortColumn={sortColumn}
                                    sortType={sortType}
                                    onSortColumn={handleSortColumn}
                                >
                                    <Column width={60} align="center" fixed>
                                        <HeaderCell>No</HeaderCell>
                                        <Cell>
                                            {(rowData, rowIndex) => {
                                                return rowIndex + 1 + limit * (page - 1);
                                            }}
                                        </Cell>
                                    </Column>

                                    <Column width={150}>
                                        <HeaderCell>No Protocol</HeaderCell>
                                        <Cell dataKey="No_Protocol" />
                                    </Column>

                                    <Column width={50}>
                                        <HeaderCell>Versi</HeaderCell>
                                        <Cell dataKey="Protocol_Version" />
                                    </Column>
                                    
                                    <Column resizable width={150}>
                                        <HeaderCell>Produk</HeaderCell>
                                        <Cell dataKey="Product_Name" />
                                    </Column>
                                    
                                    <Column width={100}>
                                        <HeaderCell>Status</HeaderCell>
                                        {/* <Cell dataKey='Protocol_Status' /> */}
                                        <Cell>
                                            {(rowData) => {
                                                if (rowData.Is_active === 1) {
                                                    return rowData.Protocol_Status
                                                }else{
                                                    return "Deleted"
                                                }                                                
                                            }}
                                        </Cell>
                                    </Column>

                                    <Column width={150}>
                                        <HeaderCell>Tipe Protokol</HeaderCell>
                                        <Cell dataKey="Department_Name" />
                                    </Column>

                                    
                                    <Column width={150}>
                                        <HeaderCell>Diajukan Oleh</HeaderCell>
                                        <Cell dataKey="Created_by" />
                                    </Column>


                                    <Column width={150}>
                                        <HeaderCell>Print Amount</HeaderCell>
                                        <Cell dataKey="Print_counter" />
                                    </Column>

                                    

                                    <Column width={350} fixed="right">
                                        <HeaderCell>...</HeaderCell>

                                        <Cell style={{ padding: "6px" }}>
                                            {(rowData) => {
                                                if (rowData.Protocol_Status == 'Active') {
                                                    return <>
                                                        <IconButton size='sm' icon={<SearchIcon />} title='Tinjau dokumen' appearance='ghost' color='orange' onClick={() => {
                                                            router.push({
                                                                pathname:
                                                                    '/user_module/protocol/management/ViewProtocol',
                                                                query: { noProtocol: rowData.No_Protocol },
                                                            });
                                                        }} />
                                                        <Divider vertical />
                                                        <IconButton size='sm' icon={<FileDownloadIcon />} appearance='ghost' color='green' title='Print document' onClick={() => {
                                                            // router.push({ pathname: '/user_module/protocol/management/PrintProtocolDoc', query: { noProtocol: rowData.No_Protocol, userDept: parseInt(userDept) } });
                                                            router.push({ pathname: '/user_module/protocol/management/pdf/report', query: { noProtocol: rowData.No_Protocol, userDept: parseInt(userDept) } });
                                                        }} />
                                                        
                                                        <Divider vertical />
                                                        <IconButton size='sm' icon={<FileDownloadIcon />} appearance='ghost' color='blue' title='Print document report' onClick={() => 
                                                        {
                                                            UpdatePrintProtocol(rowData.No_Protocol,parseInt(userDept))
                                                            // router.push({ pathname: '/user_module/protocol/management/PrintProtocolDocReporting', query: { noProtocol: rowData.No_Protocol, userDept: parseInt(userDept) } });
                                                        }} />
                                                        <Divider vertical />
                                                        <IconButton size='sm' icon={<FileDownloadIcon />} appearance='ghost' color='violet' title='Print excel report' onClick={() => {
                                                            window.open(`${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getreportexcel?noprotocol=${rowData.No_Protocol}`);
                                                        }} />
                                                        <Divider vertical />
                                                        <IconButton size='sm' icon={<EditIcon />} appearance='ghost' color='red' title='Edit protocol' onClick={() => buttonEditHandler(rowData.No_Protocol, 'Active')} />
                                                        <Divider vertical />
                                                        <IconButton size='sm' icon={<TrashIcon />} appearance='ghost' color='red' title='Delete protocol' disabled={rowData.Is_active === 0} onClick={() => DeleteProtocol(rowData.No_Protocol)} />
                                                    </>;
                                                } else if (rowData.Protocol_Status == 'Reject') {
                                                    return <>
                                                        <IconButton size='sm' icon={<SearchIcon />} title='Tinjau dokumen' appearance='ghost' color='orange' onClick={() => {
                                                            router.push({
                                                                pathname:
                                                                    '/user_module/protocol/management/ViewProtocol',
                                                                query: { noProtocol: rowData.No_Protocol },
                                                            });
                                                        }} />
                                                        <Divider vertical />
                                                        <IconButton size='sm' icon={<FileDownloadIcon />} appearance='ghost' color='green' title='Print document' onClick={() => {
                                                            // router.push({ pathname: '/user_module/protocol/management/PrintProtocolDoc', query: { noProtocol: rowData.No_Protocol, userDept: parseInt(userDept) } });
                                                            router.push({ pathname: '/user_module/protocol/management/pdf/report', query: { noProtocol: rowData.No_Protocol, userDept: parseInt(userDept) } });
                                                        }} />
                                                        <Divider vertical />
                                                        <IconButton size='sm' icon={<EditIcon />} appearance='ghost' color='red' title='Edit protocol' onClick={() => buttonEditHandler(rowData.No_Protocol, 'Reject')} />
                                                        
                                                    </>;
                                                } else {
                                                    return <>
                                                        <IconButton size='sm' icon={<SearchIcon />} title='Tinjau dokumen' appearance='ghost' color='orange' onClick={() => {
                                                            router.push({
                                                                pathname:
                                                                    '/user_module/protocol/management/ViewProtocol',
                                                                query: { noProtocol: rowData.No_Protocol },
                                                            });
                                                        }} />
                                                        <Divider vertical />
                                                        <IconButton size='sm' icon={<FileDownloadIcon />} appearance='ghost' color='green' title='Print dokumen' onClick={() => {
                                                            // router.push({ pathname: '/user_module/protocol/management/PrintProtocolDoc', query: { noProtocol: rowData.No_Protocol, userDept: parseInt(userDept) } });
                                                            router.push({ pathname: '/user_module/protocol/management/pdf/report', query: { noProtocol: rowData.No_Protocol, userDept: parseInt(userDept) } });
                                                        }
                                                        } 
                                                        />
                                                    </>;
                                                }
                                            }}
                                        </Cell>
                                    </Column>
                                </Table>
                                <div style={{ padding: 20 }}>
                                    <Pagination
                                        prev
                                        next
                                        first
                                        last
                                        ellipsis
                                        boundaryLinks
                                        maxButtons={5}
                                        size="xs"
                                        layout={["total", "-", "limit", "|", "pager"]}
                                        // layout={["total", "-", "limit", "|", "pager", "skip"]}
                                        total={
                                            allProtocolHeaderData.length
                                        }
                                        limitOptions={[10, 30, 50]}
                                        limit={limit}
                                        activePage={page}
                                        onChangePage={setPage}
                                        onChangeLimit={handleChangeLimit}
                                    />
                                </div>
                            </Panel>
                        </>
                    ) : <Stack direction='row' justifyContent='center'>
                        <p className='font-semibold'>No data found.</p>
                    </Stack>
                    }
                </MainContent>
            </ContainerLayout>
        </>
    );
}
