import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import MainContent from "@/components/layout/MainContent";
import {
  Dropdown,
  Button,
  Modal,
  Placeholder,
  Form,
  Stack,
  DatePicker,
  Input,
} from "rsuite";
import { RemindIcon } from "@rsuite/icons";
import Table from "@/components/Table";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import * as faIcon from "@fortawesome/free-solid-svg-icons";
import ipcApi from "@/pages/api/ipcApi";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import {
  loadingAnimationNoClick,
  hideLoadingAnimation,
} from "@/components/SweetAlertLoading";
import UseTestApi from "@/pages/api/userApi";
import ModuleContentHeader from "@/components/ModuleContentHeader";
import APIIPCProductMA from "@/pages/api/ipc/api_ipc_ma";
import {
  faEdit,
  faMagnifyingGlass,
  faPrint,
  faSearch,
} from '@fortawesome/free-solid-svg-icons';
import APIIPCProductScale from "@/pages/api/ipc/api_ipc_scale";

export default function ApprovalMeasurementData() {
  const [employeeId, setEmployeeId] = useState("");
  const router = useRouter();
  const MySwal = withReactContent(Swal);
  const [titleValue, setTitleValue] = useState({});
  const [dropdownValue, setDropdownValue] = useState([]);
  const [interData, setInterData] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const {
    GetIpcHeaderByDate,
    GetMsReason,
    GetIpcDetailData,
    PostApprovalApi,
    ValidateApproval,
    GetIpcHeaderAllDataNeedApprove,
  } = ipcApi();
  const [formattedDateValue, setFormattedDateValue] = useState("");
  const [isRequest, setIsRequest] = useState(false);
  const [ipcHeaderData, setIpcHeaderData] = useState([]);
  const [ipcDetailData, setIpcDetailData] = useState([]);
  const [detailReason, setDetailReason] = useState([]);
  const [reasonData, setReasonData] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [password, setPassword] = useState("");
  const [moduleName, setModuleName] = useState("");
  var [breadcrumbsData, setBreadcrumbsData] = useState([]);
  //const breadcrumbsData = [moduleName, parentMenuName, childMenuName];

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");
    if (dataLogin === "" || dataLogin === null || dataLogin === undefined) {
      router.push("/");
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ""
    ) {
      router.push("/dashboard");
      return;
    }

    const asPathWithoutQuery = router.asPath.split("?")[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split("/")
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      `${asPathNestedRoutes[1]}`,
      `${asPathNestedRoutes[2]}`,
    ];
    setBreadcrumbsData(routerBreadCrumbsData);

    setModuleName(moduleNameValue);
    setEmployeeId(dataLogin.employee_id);
  }, []);

  useEffect(() => {
    if (isRequest) {
      if (formattedDateValue !== "") {
        GetIpcHeaderData(formattedDateValue);
      }
    }
  }, [isRequest, formattedDateValue]);

  useEffect(() => {
    // convert date
    const formattedDate = dateFormatter(selectedDate);
    setFormattedDateValue(formattedDate);
  }, [selectedDate]);


  useEffect(()=>{
    GetIpcNeedApprove()
  },[])

  const GetIpcHeaderData = async (date) => {
    const { Data } = await GetIpcHeaderByDate(date);
    if (Data) {
      const dateFormattedData = Data.map((item) => {
        const initialDate = item.Date;
        const formattedDate = initialDate.split("T")[0];
        return { ...item, Date: formattedDate };
      });
      setIpcHeaderData(dateFormattedData);
    } else {
      setIpcHeaderData([]);
      // MySwal.fire({
      //   icon: "error",
      //   title: "Oops...",
      //   text: "NO DATA FOUND in that specified date time !",
      // });
    }

    setIsRequest(false);
  };

  const GetIpcNeedApprove = async () => {
    const res = await APIIPCProductScale().getApproval();
   try {
    if (res.status) {
      const dateFormattedData = res.data.map((item) => {
        console.log(item)
        const initialDate = item.created_date;        ;
        const formattedDate = initialDate.split("T")[0];
        return { ...item, Date: formattedDate };
      });
      console.log(dateFormattedData)
      setIpcHeaderData(dateFormattedData || []);
    } else {
      setIpcHeaderData([]);
    }
   } catch (error) {
    console.log(error)
    setIpcHeaderData([]);
   }
  };

  let { data } = router.query;
  data = data ? JSON.parse(data) : null;

  const fetchScaleReason = async () => {
    const  res  = await APIIPCProductScale().getReasonScale();
    return res;
  };

  const detailReasonController = (detailData) => {
    // menangkap detail reason dari table
    if (detailData) {
      setDetailReason(detailData);
    }
  };

  const fetchIpcDetail = async (objectData) => {
    let reqData = {
      id_transaction_h: objectData.id_transaction_h,
    };

    const  res  = await APIIPCProductScale().getApprovalDetail(reqData);
    return res;
  };

  // Date change handler
  const dateFormatter = (date) => {
    const today = new Date(date);
    const year = `${today.getFullYear()}`;
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");
    const result = `${year}-${month}-${day}`;
    return result;
  };

  const handleClose = () => {
    setIsModalOpen(false);
  };

  const refreshHandler = (event) => {
    event.preventDefault();
    // validasi user sudah memilih date
    if (!selectedDate) {
      // MySwal.fire({
      //   icon: "error",
      //   title: "Oops...",
      //   text: "You have NOT selected any date time !",
      // });
      GetIpcNeedApprove()
      return;
    }

    setIsRequest(true);
  };

  const showMeasurementDetail = async (objectData) => {
    console.log(objectData)
    if (objectData.id_transaction_h) {
      MySwal.showLoading();
      const dataReason = await fetchScaleReason();
      const dataDetail = await fetchIpcDetail(objectData);

      console.log(dataDetail)
      console.log(dataReason)
      setReasonData(dataReason?.data || []);
      setIpcDetailData(dataDetail?.data || []);

      setIsModalOpen(true);
      MySwal.close();
      return;
    } else {
      MySwal.fire({
        title: "Error",
        icon: "warning",
        text: "No Data found.",
      });
      return;
    }
  };

  const submitApprovalHandler = async () => {
    const tmsAmount = ipcDetailData.filter(
      (item) => item.status === "TMS"
    );

    console.log(tmsAmount)
    console.log(detailReason)
    if (tmsAmount.length !== detailReason.length) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "You HAVE NOT set reasons for all TMS data !",
      });
      return;
    }

    if (password === "") {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please enter your password !",
      });
      return;
    }

    const reqData = {
      emp_id: employeeId,
      id_transaction: interData.id_transaction_h,
      reason_details: detailReason,
    };

    const validationApproval = {
      employee_id: employeeId,
      password: password,
    };

    const validateUser = await ValidateApproval(validationApproval);
    const { Data: userData } = validateUser;
    const { error } = validateUser;

    if (userData) {
      loadingAnimationNoClick();
      const  res  = await APIIPCProductScale().putApprove(reqData);
      if (res.status === 200) {
        GetIpcNeedApprove()
        MySwal.fire({
          position: "center",
          icon: "success",
          title: "Your transaction is saved.",
          showConfirmButton: false,
          timer: 2500,
        });
        //setIpcHeaderData([]);
        setIsModalOpen(false);
        

        //setIsRequest(true);
      }
      return;
    }
    if (error) {
      MySwal.fire({
        icon: "error",
        title: "Validation FAILED !",
        text: error,
      });
      return;
    }

    // const validateUser = await UserLoginApi(validationApproval);
    // if (!validateUser) {
    //   MySwal.fire({
    //     icon: "error",
    //     title: "Invalid Password !",
    //   });
    //   return;
    // } else {
    //   loadingAnimationNoClick();
    //   const { Data } = await PostApprovalApi(reqData);
    //   if (Data) {
    //     MySwal.fire({
    //       position: "center",
    //       icon: "success",
    //       title: "Your transaction is saved.",
    //       showConfirmButton: false,
    //       timer: 2500,
    //     });
    //     setIpcHeaderData([]);
    //     setIsModalOpen(false);

    //     setIsRequest(true);
    //   }
    // }
  };

  const dropdownChangeHandler = (value) => {
    if (value) {
      if (dropdownValue.length === 0) {
        setDropdownValue([value]);
        return;
      } else {
        let newData = dropdownValue.filter(
          (item) => item.Id_Setup !== value.Id_Setup,
        );
        const result = [...newData, value];
        setDropdownValue(result);
        return;
      }
    }
  };

  useEffect(() => {
    if (dropdownValue.length > 0) {
      // Update titleValue
      dropdownValue.forEach((item) => {
        setTitleValue((prevTitleValue) => ({
          ...prevTitleValue,
          [item.id_transaction_d]: item.reason_desc,
        }));
      });
  
      // Update detailReason by replacing existing items with the same id_transaction_d
      setDetailReason((prevDetailReason) => {
        // Filter out existing entries with the same id_transaction_d
        const filteredDetailReason = prevDetailReason.filter(
          (prevItem) => !dropdownValue.some((newItem) => newItem.id_transaction_d === prevItem.id_transaction_d)
        );
  
        // Append new dropdownValue
        return [...filteredDetailReason, ...dropdownValue];
      });
    }
  }, [dropdownValue]);
  

  return (
    <>
      <div>
        <Head>
          <title>Approval Reason Setup</title>
        </Head>
      </div>

      <MainContent>
        {/* <div className="mb-3">
          <h4>PIMS - Menu</h4>
          <p>In Process Control &gt; Approval Measurement</p>
        </div> */}
        <ModuleContentHeader
          breadcrumbs={breadcrumbsData}
          module_name={moduleName}
        />

        <div className="p-2">
          <form>
            <Form.Group controlId="select_date">
              <Stack spacing={6}>
                <Form.ControlLabel>Select Date : </Form.ControlLabel>
                <DatePicker
                  value={selectedDate}
                  onChange={(value) => setSelectedDate(value)}
                />
                <Form.ControlLabel>
                  <Button appearance="primary" onClick={refreshHandler}>
                    Refresh
                  </Button>
                </Form.ControlLabel>
              </Stack>
            </Form.Group>
          </form>
        </div>

        {/* <Table
          ipcHeader={ipcHeaderData}
          setPage={setPage}
          setInterData={setInterData}
          interComponentData={interComponentData}
          showMeasurementDetail={showMeasurementDetail}
        /> */}

        {ipcHeaderData && (
        <table className="table" style={{ textAlign: 'center' }}>
          <thead>
            <tr key={1}>
              <th scope="col">No</th>
              <th scope="col">Date</th>
              <th scope="col">Operator Name</th>
              <th scope="col">Product Code</th>
              <th scope="col">Batch Code</th>
              <th scope="col">Amount TMS</th>
              <th scope="col">Amount MS</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            {ipcHeaderData.map((item,index) => {
              return (
                <tr key={item.id_transaction}>
                  <td>{index+1}</td>
                  <td>{item.Date}</td>
                  <td>{item.name}</td>
                  <td>{item.product_code}</td>
                  <td>{item?.batch_code}</td>
                  <td>{item.amount_tms}</td>
                  <td>{item.amount_ms}</td>                  
                  <td>
                    <button
                      title="View"
                      className="btn btn-primary rounded border p-2"
                      onClick={() => {
                        const data = {
                          id_transaction_h: item.id_transaction,
                          operator_name: item.name,
                          product_code: item.product_code,
                          date_checked: item.Date,
                        };
                        setInterData(data);
                        showMeasurementDetail(data);
                        setDetailReason([])
                      }}
                    >
                      <FontAwesomeIcon
                        icon={faMagnifyingGlass}
                        style={{ fontSize: 15 }}
                      />
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      )}

        <Modal size="full" open={isModalOpen} onClose={handleClose}>
          <Modal.Header>
            <Modal.Title>
              <h5>Set Approval Reason</h5>
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <table className="table" style={{ maxWidth: 760, textAlign: 'center' }}>
                <thead>
                  <tr key={1}>
                    <th scope="col">No</th>
                    <th scope="col">Scale Weight</th>
                    <th scope="col">Created time</th>
                    <th scope="col">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {ipcDetailData.map((item,index) => {
                    return (
                      <tr key={item.id_transaction_d}>
                        <td>{index+1}</td>
                        <td
                          style={
                            item.status === 'TMS'
                              ? { color: '#de0a02', fontWeight: 'bold' }
                              : { color: '#000' }
                          }
                        >
                          {item.scale_weight} gram
                        </td>
                        <td>
                        {new Date(item.createdt).toLocaleDateString('en-GB')}
                        </td>
                        <td>
                          {item.status === 'TMS' && (
                            <Dropdown
                              title={
                                titleValue[item.id_transaction_d]
                                  ? titleValue[item.id_transaction_d]
                                  : '-- Select Reason--'
                              }
                              onSelect={dropdownChangeHandler}
                            >
                              <Dropdown.Item eventKey="">
                                -- Select Reason --
                              </Dropdown.Item>
                              {reasonData.map((itemReason) => (
                                <Dropdown.Item
                                  eventKey={{
                                    id_transaction_d: item.id_transaction_d,
                                    ...itemReason,
                                  }}
                                >
                                  {itemReason.reason_desc}
                                </Dropdown.Item>
                              ))}
                            </Dropdown>
                          )}
                        </td>                 
                      </tr>
                    );
                  })}
                </tbody>
              </table>
          </Modal.Body>
          <Modal.Footer>
            <Stack direction="row" justifyContent="space-between">
              <Stack>
                <Form.Group controlId="password-for-validation">
                  <Stack spacing={6}>
                    <Form.ControlLabel>
                      Enter your password :{" "}
                    </Form.ControlLabel>
                    <Input
                      type="password"
                      name="password-for-validation"
                      id="password-for-validation"
                      placeholder="Password"
                      onChange={(value) => setPassword(value)}
                    />
                  </Stack>
                </Form.Group>
              </Stack>

              <Stack>
                <Button onClick={handleClose} appearance="subtle">
                  Cancel
                </Button>
                <Button onClick={submitApprovalHandler} appearance="primary">
                  Ok
                </Button>
              </Stack>
            </Stack>
          </Modal.Footer>
        </Modal>
      </MainContent>
    </>
  );
}
