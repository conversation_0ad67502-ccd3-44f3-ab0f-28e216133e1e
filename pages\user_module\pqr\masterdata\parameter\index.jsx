import { useEffect, useState } from "react";
import Head from "next/head";
import {
  <PERSON>readcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  useToaster,
  ButtonGroup,
  Loader,
  DatePicker,
  RadioGroup,
  Radio,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiPQR from "@/pages/api/pqr/parameter/api_PQR";
import { useRouter } from "next/router";

export default function MasterdataParameter() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_parameter");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [PQRsDataState, setPQRsDataState] = useState([]);

  const emptyAddPQRForm = {
    parameter_name: null,
    flag_controllable: null,
    is_automate: null,
    uom: null,
    start_date: null,
    end_date: null,
  };

  const emptyEditPQRForm = {
    parameter_name: null,
    flag_controllable: null,
    is_automate: null,
    uom: null,
    start_date: null,
    end_date: null,
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [addPQRForm, setAddPQRForm] = useState(emptyAddPQRForm);
  const [editPQRForm, setEditPQRForm] = useState(emptyEditPQRForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = PQRsDataState.filter((rowData, i) => {
    const searchFields = ["id_parameter", "parameter_name", "flag_controllable", "uom", "is_automate", "start_date", "end_date", "create_date", "create_by", "update_date", "update_by", "delete_date", "delete_by", "is_active"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : PQRsDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/masterdata")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandleGetAllPQRApi();
    }
  }, []);

  const HandleGetAllPQRApi = async () => {
    try {
      const res = await ApiPQR().getAllPQR();

      console.log("res", res);
      if (res.status === 200) {
        setPQRsDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };

  const HandleAddPQRApi = async () => {
    const errors = {};

    if (!addPQRForm.parameter_name) {
      errors.parameter_name = "Nama Parameter wajib diisi";
    }

    if (!addPQRForm.uom) {
      errors.uom = "Nama UOM wajib diisi";
    }

    if (!addPQRForm.start_date) {
      errors.start_date = "Tanggal Mulai wajib diisi";
    }

    if (!addPQRForm.end_date) {
      errors.end_date = "Tanggal Selesai name wajib diisi";
    } else if (new Date(addPQRForm.end_date) < new Date(addPQRForm.start_date)) {
      errors.end_date = "Tanggal Selesai harus lebih besar dari Tanggal Mulai";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }
    setLoading(true);
    try {
      const res = await ApiPQR().createPQR({
        ...addPQRForm,
        create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        setAddPQRForm(emptyAddPQRForm);
        setShowAddModal(false);
        HandleGetAllPQRApi();
      } else {
        console.log("error on AddPQRApi ", res.message);
      }
    } catch (error) {
      console.log("error on AddPQRApi ", error);
    } finally {
      setLoading(false);
    }
  };

  const HandleEditPQRApi = async () => {
    const errors = {};

    if (!editPQRForm.parameter_name) {
      errors.parameter_name = "Nama Parameter wajib diisi";
    }
    if (!editPQRForm.uom) {
      errors.uom = "Nama UOM wajib diisi";
    }

    if (!editPQRForm.start_date) {
      errors.start_date = "Tanggal Mulai wajib diisi";
    }

    if (!editPQRForm.end_date) {
      errors.end_date = "End date name wajib diisi";
    } else if (new Date(editPQRForm.end_date) < new Date(editPQRForm.start_date)) {
      errors.end_date = "Tanggal Selesai harus lebih besar dari Tanggal Mulai";
    }


    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);
      return;
    }
    try {
      const res = await ApiPQR().editPQR({
        ...editPQRForm,
        update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        HandleGetAllPQRApi();
        setShowEditModal(false);
      } else {
        console.log("error on editPQRApi ", res.message);
      }
    } catch (error) {
      console.log("error on editPQRApi ", res.message);
    }
  };

  const handleEditStatusPQRApi = async (id_parameter, is_active) => {
    try {
      const res = await ApiPQR().editStatusPQR({
        id_parameter,
        is_active,
        delete_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        console.log("Status update success:", res.message);
        HandleGetAllPQRApi();
      } else {
        console.log("Error on update status: ", res.message);
      }
    } catch (error) {
      console.log("Error on update status: ", error.message);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Parameter E Release</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>E Release</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>Halaman Parameter E Release</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Halaman Parameter E Release</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >

              <Table
                bordered
                cellBordered
                height={400}
                data={getPaginatedData(getFilteredData(), limit, page)}
                sortColumn={sortColumn}
                sortType={sortType}
                onSortColumn={handleSortColumn}
                autoHeight
              >
                <Column width={120} align="center" sortable fullText resizable>
                  <HeaderCell>ID Parameter</HeaderCell>
                  <Cell dataKey="id_parameter" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Nama Parameter</HeaderCell>
                  <Cell dataKey="parameter_name" />
                </Column>
                {/* <Column width={150} sortable align="center" fullText>
                  <HeaderCell>Flag Controllable</HeaderCell>
                  <Cell dataKey="flag_controllable">{(rowData) => (rowData.flag_controllable === "Y" ? "Yes" : "No")}</Cell>
                </Column> */}

                <Column width={100} sortable fullText resizable>
                  <HeaderCell align="center">UOM</HeaderCell>
                  <Cell dataKey="uom" />
                </Column>

                {/* <Column width={120} sortable align="center" fullText>
                  <HeaderCell>Is Automate</HeaderCell>
                  <Cell dataKey="is_automate">{(rowData) => (rowData.is_automate === "Y" ? "Yes" : "No")}</Cell>
                </Column> */}

                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Mulai</HeaderCell>
                  <Cell>{(rowData) => (rowData.start_date ? new Date(rowData.start_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>

                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Selesai</HeaderCell>
                  <Cell>{(rowData) => (rowData.end_date ? new Date(rowData.end_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Pembuatan</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dibuat oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.delete_by}</>}</Cell>
                </Column>
                <Column width={100} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModal(true);
                            setEditPQRForm({
                              ...editPQRForm,
                              parameter_name: rowData.parameter_name,
                              flag_controllable: rowData.flag_controllable,
                              uom: rowData.uom,
                              is_automate: rowData.flag_controllable === "N" ? "N" : rowData.is_automate,
                              start_date: rowData.start_date,
                              end_date: rowData.end_date,
                              id_parameter: rowData.id_parameter,
                              update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                            });
                          }}
                        >
                          <EditIcon />
                        </Button>
                        <Button appearance="subtle" onClick={() => handleEditStatusPQRApi(rowData.id_parameter, rowData.is_active)}>
                          {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>

            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                setShowAddModal(false);
                setAddPQRForm(emptyAddPQRForm);
                setErrorsAddForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Tambah Parameter</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama Parameter</Form.ControlLabel>
                    <Form.Control
                      name="parameter_name"
                      value={addPQRForm.parameter_name}
                      onChange={(value) => {
                        setAddPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          parameter_name: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          parameter_name: undefined,
                        }));
                      }}
                    />
                    {errorsAddForm.parameter_name && <p style={{ color: "red" }}>{errorsAddForm.parameter_name}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>UOM</Form.ControlLabel>
                    <Form.Control
                      name="uom"
                      value={addPQRForm.uom}
                      onChange={(value) => {
                        setAddPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          uom: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          uom: undefined,
                        }));
                      }}
                    />
                    {errorsAddForm.uom && <p style={{ color: "red" }}>{errorsAddForm.uom}</p>}
                  </Form.Group>
                  {/* <Form.Group>
                    <Form.ControlLabel>Flag Controllable</Form.ControlLabel>
                    <RadioGroup
                      name="flag_controllable"
                      inline
                      value={addPQRForm.flag_controllable}
                      onChange={(value) => {
                        setAddPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          flag_controllable: value,
                          // Jika flag controllable diubah menjadi "N", set is_automate menjadi "N"
                          is_automate: value === "N" ? "N" : prevFormValue.is_automate,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          flag_controllable: undefined,
                        }));
                      }}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsAddForm.flag_controllable && <p style={{ color: "red" }}>{errorsAddForm.flag_controllable}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Is Automate</Form.ControlLabel>
                    <RadioGroup
                      name="is_automate"
                      inline
                      value={addPQRForm.is_automate}
                      onChange={(value) => {
                        setAddPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          is_automate: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          is_automate: undefined,
                        }));
                      }}
                      disabled={addPQRForm.flag_controllable === "N"}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsAddForm.is_automate && <p style={{ color: "red" }}>{errorsAddForm.is_automate}</p>}
                  </Form.Group> */}
                  <Form.Group>
                    <Form.ControlLabel>Tanggal Mulai</Form.ControlLabel>
                    <DatePicker
                      name="start_date"
                      placeholder="Pilih Tanggal Mulai"
                      value={addPQRForm.start_date}
                      onChange={(value) => {
                        setAddPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          start_date: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          start_date: undefined,
                        }));
                      }}
                      block
                    />
                    {errorsAddForm.start_date && <p style={{ color: "red" }}>{errorsAddForm.start_date}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Tanggal Selesai</Form.ControlLabel>
                    <DatePicker
                      name="end_date"
                      placeholder="Pilih Tanggal Selesai"
                      value={addPQRForm.end_date}
                      onChange={(value) => {
                        setAddPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          end_date: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          end_date: undefined,
                        }));
                      }}
                      block
                    />
                    {errorsAddForm.end_date && <p style={{ color: "red" }}>{errorsAddForm.end_date}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddPQRForm(emptyAddPQRForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleAddPQRApi();
                  }}
                  appearance="primary"
                  type="submit"
                  loading={loading} // Menampilkan loading state pada tombol
                  disabled={loading} // Menonaktifkan tombol saat loading
                >
                  Tambah
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Adding Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditPQRForm(emptyEditPQRForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Ubah Parameter</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama Parameter</Form.ControlLabel>
                    <Form.Control
                      name="parameter_name"
                      value={editPQRForm.parameter_name}
                      onChange={(value) => {
                        setEditPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          parameter_name: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          parameter_name: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.parameter_name && <p style={{ color: "red" }}>{errorsEditForm.parameter_name}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>UOM</Form.ControlLabel>
                    <Form.Control
                      name="uom"
                      value={editPQRForm.uom}
                      onChange={(value) => {
                        setEditPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          uom: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          uom: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.uom && <p style={{ color: "red" }}>{errorsEditForm.uom}</p>}
                  </Form.Group>
                  {/* <Form.Group>
                    <Form.ControlLabel>Flag Controllable</Form.ControlLabel>
                    <RadioGroup
                      name="flag_controllable"
                      inline
                      value={editPQRForm.flag_controllable}
                      onChange={(value) => {
                        setEditPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          flag_controllable: value,
                          // Jika flag controllable diubah menjadi "N", set is_automate menjadi "N"
                          is_automate: value === "N" ? "N" : prevFormValue.is_automate,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          flag_controllable: undefined,
                        }));
                      }}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsEditForm.flag_controllable && <p style={{ color: "red" }}>{errorsEditForm.flag_controllable}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Is Automate</Form.ControlLabel>
                    <RadioGroup
                      name="is_automate"
                      inline
                      value={editPQRForm.is_automate}
                      onChange={(value) => {
                        setEditPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          is_automate: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          is_automate: undefined,
                        }));
                      }}
                      disabled={editPQRForm.flag_controllable === "N"}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsEditForm.is_automate && <p style={{ color: "red" }}>{errorsEditForm.is_automate}</p>}
                  </Form.Group> */}
                  <Form.Group>
                    <Form.ControlLabel>Tanggal Mulai</Form.ControlLabel>
                    <DatePicker
                      format="dd/MM/yyyy"
                      value={editPQRForm.start_date ? new Date(editPQRForm.start_date) : null}
                      onChange={(value) => {
                        setEditPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          start_date: value ? value.toISOString().split("T")[0] : null,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          start_date: undefined,
                        }));
                      }}
                      block
                    />
                    {errorsEditForm.start_date && <p style={{ color: "red" }}>{errorsEditForm.start_date}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Tanggal Selesai</Form.ControlLabel>
                    <DatePicker
                      format="dd/MM/yyyy"
                      value={editPQRForm.end_date ? new Date(editPQRForm.end_date) : null}
                      onChange={(value) => {
                        setEditPQRForm((prevFormValue) => ({
                          ...prevFormValue,
                          end_date: value ? value.toISOString().split("T")[0] : null,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          end_date: undefined,
                        }));
                      }}
                      block
                    />
                    {errorsEditForm.end_date && <p style={{ color: "red" }}>{errorsEditForm.end_date}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditPQRForm(emptyEditPQRForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleEditPQRApi();
                  }}
                  appearance="primary"
                  type="submit"
                >
                  Simpan
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Editing Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
