import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import ipcApi from "@/pages/api/ipcApi";
import MainContent from "@/components/layout/MainContent";
import {
  Dropdown,
  Button,
  Stack,
  Form,
  ButtonToolbar,
  Checkbox,
  InputNumber,
  Input,
} from "rsuite";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import ContainerLayout from "@/components/layout/ContainerLayout";
import ModuleContentHeader from "@/components/ModuleContentHeader";

export default function AddProductCode() {
  const { InsertProductCode } = ipcApi();
  const MySwal = withReactContent(Swal);
  const [employeeId, setEmployeeId] = useState("");
  const [productCode, setProductCode] = useState("");
  const [hardnessMin, setHardnessMin] = useState(0);
  const [hardnessMax, setHardnessMax] = useState(0);
  const [hardnessUom, setHardnessUom] = useState("");
  const [diameterMin, setDiameterMin] = useState(0);
  const [diameterMax, setDiameterMax] = useState(0);
  const [diameterUom, setDiameterUom] = useState("");
  const [thicknessMin, setThicknessMin] = useState(0);
  const [thicknessMax, setThicknessMax] = useState(0);
  const [maMinWeight, setMaMinWeight] = useState(0);
  const [maMaxWeight, setMaMaxWeight] = useState(0);
  const [thicknessUom, setThicknessUom] = useState("");
  const [minDate, setMinDate] = useState(null);
  const [startDate, setStartdate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [endDateDisabled, setEndDateDisabled] = useState(false);
  const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
  const [moduleName, setModuleName] = useState("");
  const [remark, setRemark] = useState("")
  const router = useRouter();
  let path = "masterdata/ProductCode";
  let { data } = router.query;
  data ? (data = JSON.parse(data)) : data;
  var [breadcrumbsData, setBreadcrumbsData] = useState([]);
  // const breadcrumbsData = [
  //   moduleName,
  //   "Setup Master Data",
  //   "Add New Product Code",
  // ];

  // handler getCurentDate for present date time choice
  function dateHandler(date) {
    if (date) {
      // date sesuai input
      const today = new Date(date);
      const year = `${today.getFullYear()}`;
      const month = String(today.getMonth() + 1).padStart(2, "0");
      const day = String(today.getDate()).padStart(2, "0");
      const result = `${year}-${month}-${day}`;
      return result;
    } else {
      // present date
      const today = new Date();
      const year = `${today.getFullYear()}`;
      const month = String(today.getMonth() + 1).padStart(2, "0");
      const day = String(today.getDate()).padStart(2, "0");
      const result = `${year}-${month}-${day}`;
      return result;
    }
  }

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");
    if (dataLogin === null || dataLogin === undefined || dataLogin === "") {
      router.push("/");
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push("/dashboard");
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path)
    );
    if (validateUserAccess.length === 0) {
      router.push("/dashboard");
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ""
    ) {
      router.push("/dashboard");
      return;
    }

    const asPathWithoutQuery = router.asPath.split("?")[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split("/")
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      `${asPathNestedRoutes[1]}`,
      `Add ${asPathNestedRoutes[2]}`,
    ];
    setBreadcrumbsData(routerBreadCrumbsData);

    setModuleName(moduleNameValue);
    setEmployeeId(dataLogin.employee_id);
  }, []);

  // start date change useeffect to set end date minDate
  useEffect(() => {
    if (startDate) {
      setMinDate(startDate);
    }
  }, [startDate]);

  //   handler present checkbox
  const presentHandler = () => {
    setEndDateDisabled((value) => !value); // disable Date Picker

    // set End Date
    // Tanggal dalam bentuk string
    const dateString = "9998-12-31";

    // Memisahkan tahun, bulan, dan tanggal
    const [year, month, day] = dateString.split("-");

    // Menggunakan konstruktor Date untuk membuat objek Date baru
    const dateObject = new Date(Number(year), Number(month) - 1, Number(day));
    setEndDate(dateObject);
  };

  //   submit handler
  const submitHandler = async () => {
    // validasi jika ada field yang kosong
    if (hardnessUom === "" || thicknessUom === "" || diameterUom === "") {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please fill all required data!",
      });
      return;
    }

    // validasi jika tidak memilih present checkbox dan tidak memilih date
    if (!startDate) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "You HAVE to specify the Start Date !",
      });
      return;
    }

    // Validasi end date
    if (!endDate && !endDateDisabled) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "You HAVE to specify the End Date !",
      });
      return;
    }

    // parse number
    const h_min = parseFloat(hardnessMin);
    const h_max = parseFloat(hardnessMax);
    const t_min = parseFloat(thicknessMin);
    const t_max = parseFloat(thicknessMax);
    const d_min = parseFloat(diameterMin);
    const d_max = parseFloat(diameterMax);
    const ma_min = parseFloat(maMinWeight);
    const ma_max = parseFloat(maMaxWeight);

    // parse Date
    const formattedStartDate = dateHandler(startDate);
    const formattedEndDate = dateHandler(endDate);

    // validasi jika nilai max lebih kecil dari nilai min
    if (d_max <= d_min) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Diameter MAX SHOULD NOT be Lower Than Diameter Min",
      });
      return;
    }
    if (h_max <= h_min) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Hardness MAX SHOULD NOT be Lower Than Hardness Min",
      });
      return;
    }
    if (t_max <= t_min) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Thickness MAX SHOULD NOT be Lower Than Thickness Min",
      });
      return;
    }
    if (formattedEndDate < formattedStartDate) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Start Date SHOULD be earlier than the End Date",
      });
    }

    MySwal.showLoading();
    const insertData = {
      product_code: productCode,
      hardness_min: h_min,
      hardness_max: h_max,
      hardness_uom: hardnessUom,
      thickness_min: t_min,
      thickness_max: t_max,
      thickness_uom: thicknessUom,
      diameter_min: d_min,
      diameter_max: d_max,
      diameter_uom: diameterUom,
      start_date: formattedStartDate,
      end_date: formattedEndDate,
      created_by: employeeId,
      remark: remark,
      // created_by: session.data.user.emp_id,
      ma_min_weight: ma_min,
      ma_max_weight: ma_max,
    };

    // send data
    const { Data: insertingData } = await InsertProductCode(insertData);

    if (insertingData) {
      setIsSubmitButtonDisabled(true);
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "Product Code inserted successfully.",
        showConfirmButton: false,
        allowOutsideClick: false,
        timer: 2500,
      });
      router.push("/user_module/masterdata/ProductCode");
    } else {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Product Code insert FAILED.",
      });
      setIsSubmitButtonDisabled(false);
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Add Product Code</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <MainContent>
          {/* <div className="mb-3">
            <h4>PIMS - Menu</h4>
            <p>Setup Master Data &gt; Add New Product Code</p>
          </div> */}
          <ModuleContentHeader
            module_name={moduleName}
            breadcrumbs={breadcrumbsData}
          />

          <Form layout="horizontal" onSubmit={submitHandler}>
            <Form.Group controlId="name-1">
              <Form.ControlLabel>Product Code</Form.ControlLabel>
              <Form.Control
                name="product_code"
                value={productCode}
                onChange={(value) => setProductCode(value)}
                required={true}
              />
            </Form.Group>
            <Form.Group controlId="hardness">
              <Stack spacing={6}>
                <Form.ControlLabel>Hardness Range</Form.ControlLabel>
                <InputNumber
                  name="hardness_min"
                  style={{ width: 70 }}
                  value={hardnessMin}
                  onChange={(value) => setHardnessMin(value)}
                  required={true}
                />
                <Form.ControlLabel style={{ width: 10 }}>-</Form.ControlLabel>
                <InputNumber
                  name="hardness_max"
                  style={{ width: 70 }}
                  value={hardnessMax}
                  onChange={(value) => setHardnessMax(value)}
                  required={true}
                />
                <Dropdown
                  title={hardnessUom !== "" ? hardnessUom : "-- Select UoM --"}
                  onSelect={(value) => setHardnessUom(value)}
                >
                  <Dropdown.Item eventKey="">-- Select UoM --</Dropdown.Item>
                  <Dropdown.Item eventKey="kP">kP</Dropdown.Item>
                </Dropdown>
              </Stack>
            </Form.Group>
            <Form.Group controlId="diameter">
              <Stack spacing={6}>
                <Form.ControlLabel>Diameter Range</Form.ControlLabel>
                <InputNumber
                  name="diameter_min"
                  style={{ width: 70 }}
                  value={diameterMin}
                  onChange={(value) => setDiameterMin(value)}
                  required={true}
                />
                <Form.ControlLabel style={{ width: 10 }}>-</Form.ControlLabel>
                <InputNumber
                  name="diameter_max"
                  style={{ width: 70 }}
                  value={diameterMax}
                  onChange={(value) => setDiameterMax(value)}
                  required={true}
                />
                <Dropdown
                  title={diameterUom !== "" ? diameterUom : "-- Select UoM --"}
                  onSelect={(value) => setDiameterUom(value)}
                >
                  <Dropdown.Item eventKey="">-- Select UoM --</Dropdown.Item>
                  <Dropdown.Item eventKey="mm">mm</Dropdown.Item>
                </Dropdown>
              </Stack>
            </Form.Group>
            <Form.Group controlId="thickness">
              <Stack spacing={6}>
                <Form.ControlLabel>Thickness Range</Form.ControlLabel>
                <InputNumber
                  name="thickness_min"
                  style={{ width: 70 }}
                  value={thicknessMin}
                  onChange={(value) => setThicknessMin(value)}
                  required={true}
                />
                <Form.ControlLabel style={{ width: 10 }}>-</Form.ControlLabel>
                <InputNumber
                  name="thickness_max"
                  style={{ width: 70 }}
                  value={thicknessMax}
                  onChange={(value) => setThicknessMax(value)}
                  required={true}
                />
                <Dropdown
                  title={
                    thicknessUom !== "" ? thicknessUom : "-- Select UoM --"
                  }
                  onSelect={(value) => setThicknessUom(value)}
                >
                  <Dropdown.Item eventKey="">-- Select UoM --</Dropdown.Item>
                  <Dropdown.Item eventKey="mm">mm</Dropdown.Item>
                </Dropdown>
              </Stack>
            </Form.Group>
            <Form.Group controlId="ma">
              <Stack spacing={6}>
                <Form.ControlLabel>Ma Range</Form.ControlLabel>
                <InputNumber
                  name="ma_min"
                  style={{ width: 70 }}
                  value={maMinWeight}
                  onChange={(value) => setMaMinWeight(value)}
                  required={true}
                />
                <Form.ControlLabel style={{ width: 10 }}>-</Form.ControlLabel>
                <InputNumber
                  name="ma_max"
                  style={{ width: 70 }}
                  value={maMaxWeight}
                  onChange={(value) => setMaMaxWeight(value)}
                  required={true}
                />
              <span>%</span>
              </Stack>
            </Form.Group>
            <Form.Group controlId="start_date">
              <Stack spacing={6}>
                <Form.ControlLabel>Start Date</Form.ControlLabel>
                <Input
                  type="date"
                  value={startDate}
                  onChange={(value) => setStartdate(value)}
                />
              </Stack>
            </Form.Group>
            <Form.Group controlId="start_date">
              <Stack spacing={6}>
                <Form.ControlLabel>End Date</Form.ControlLabel>
                <Input
                  type="date"
                  disabled={endDateDisabled}
                  value={endDate}
                  min={minDate}
                  onChange={(value) => setEndDate(value)}
                />

                <Checkbox onChange={presentHandler}>Present</Checkbox>
              </Stack>
            </Form.Group>

            <Form.Group controlId="textarea">
            <Form.ControlLabel>Remark</Form.ControlLabel>
            <Form.Control name="textArea" onChange={(value)=> setRemark(value)}/>
          </Form.Group>

            <Form.Group>
              <ButtonToolbar>
                <Button
                  appearance="primary"
                  type="submit"
                  disabled={isSubmitButtonDisabled}
                >
                  Submit
                </Button>
                <Button appearance="default" onClick={() => router.back()}>
                  Cancel
                </Button>
              </ButtonToolbar>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}
