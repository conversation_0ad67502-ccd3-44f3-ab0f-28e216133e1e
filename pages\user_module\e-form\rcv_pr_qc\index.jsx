import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Button,
  IconButton,
  Stack,
  Breadcrumb,
  Tag,
  Table,
  Panel,
  Input,
  InputGroup,
  InputNumber,
  Pagination,
  Modal,
  Form,
  useToaster,
  DatePicker,
  Divider,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";
import dateFormatterLong from "@/lib/function/date-formatter-long";
import dateTimeFormatter from "@/lib/function/date-time-formatter";
import dateFormatterDash from "@/lib/function/date-formatter-dash";

import API_EformRcvPrQc from "@/pages/api/e_form/api_eform_rcv_pr_qc";

export default function EFormLogsheetMaterialPage() {
  const router = useRouter();
  const toaster = useToaster();
  const [props, setProps] = useState([]);
  const { HeaderCell, Cell, Column } = Table;

  const [moduleName, setModuleName] = useState("");
  const [rcvQcData, setRcvQcData] = useState([]);

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [selectedRcvQc, setSelectedRcvQc] = useState(null);

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [totalReceipt, setTotalReceipt] = useState(0);

  useEffect(() => {
    const fetchData = async () => {
      const res_rcv_pr_qc = await API_EformRcvPrQc().getAll();
      setRcvQcData(res_rcv_pr_qc.data || []);
    };

    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    console.log("dataLogin", dataLogin);
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("e-form/rcv_pr_qc")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      fetchData();
    }
  }, []);

  const emptyFormValue = {
    date_required: null,
    no_pr: null,
    pr_created_date: null,
    approved_date: null,
    items_detail: [
      {
        no_item: null,
        description: null,
        no_cat: null,
        supplier: null,
        brand: null,
        coa: null,
        price: 0,
        qty: 0,
        total: 0,
        no_po: null,
        arrival_date: null,
      },
    ],
    created_by_id: null,
    updated_by_id: null,
  };

  const handleNoItemChange = (index, value) => {
    const updatedItemsDetail = [...formValue.items_detail];
    updatedItemsDetail[index].no_item = value;
    setFormValue({
      ...formValue,
      items_detail: updatedItemsDetail,
    });
  };

  const handleDescriptionChange = (index, value) => {
    const updatedItemsDetail = [...formValue.items_detail];
    updatedItemsDetail[index].description = value;
    setFormValue({
      ...formValue,
      items_detail: updatedItemsDetail,
    });
  };

  const handleNoCatChange = (index, value) => {
    const updatedItemsDetail = [...formValue.items_detail];
    updatedItemsDetail[index].no_cat = value;
    setFormValue({
      ...formValue,
      items_detail: updatedItemsDetail,
    });
  };

  const handleSupplierChange = (index, value) => {
    const updatedItemsDetail = [...formValue.items_detail];
    updatedItemsDetail[index].supplier = value;
    setFormValue({
      ...formValue,
      items_detail: updatedItemsDetail,
    });
  };

  const handleBrandChange = (index, value) => {
    const updatedItemsDetail = [...formValue.items_detail];
    updatedItemsDetail[index].brand = value;
    setFormValue({
      ...formValue,
      items_detail: updatedItemsDetail,
    });
  };

  const handleCoaChange = (index, value) => {
    const updatedItemsDetail = [...formValue.items_detail];
    updatedItemsDetail[index].coa = value;
    setFormValue({
      ...formValue,
      items_detail: updatedItemsDetail,
    });
  };

  const handlePriceChange = (index, value) => {
    const updatedItemsDetail = [...formValue.items_detail];
    updatedItemsDetail[index].price = value;
    updatedItemsDetail[index].total = value * updatedItemsDetail[index].qty; // Update total based on price and qty
    setFormValue({
      ...formValue,
      items_detail: updatedItemsDetail,
    });
  };

  const handleQtyChange = (index, value) => {
    const updatedItemsDetail = [...formValue.items_detail];
    updatedItemsDetail[index].qty = value;
    updatedItemsDetail[index].total = value * updatedItemsDetail[index].price; // Update total based on price and qty
    setFormValue({
      ...formValue,
      items_detail: updatedItemsDetail,
    });
  };

  const handleNoPoChange = (index, value) => {
    const updatedItemsDetail = [...formValue.items_detail];
    updatedItemsDetail[index].no_po = value;
    setFormValue({
      ...formValue,
      items_detail: updatedItemsDetail,
    });
  };

  const handleArrivalDateChange = (index, value) => {
    const updatedItemsDetail = [...formValue.items_detail];
    updatedItemsDetail[index].arrival_date = value;
    setFormValue({
      ...formValue,
      items_detail: updatedItemsDetail,
    });
  };

  const addItemsDetail = () => {
    setFormValue({
      ...formValue,
      items_detail: [
        ...formValue.items_detail,
        { item_name: null, remarks: null, total_receipt: null },
      ],
    });
  };

  const removeItemsDetail = (index) => {
    const updatedItemsDetail = [...formValue.items_detail];
    updatedItemsDetail.splice(index, 1);
    setFormValue({
      ...formValue,
      items_detail: updatedItemsDetail,
    });
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id",
    "no_item",
    "description",
    "no_cat",
    "supplier",
    "brand",
    "coa",
    "price",
    "qty",
    "total",
    "date_required",
    "no_pr",
    "pr_created_date",
    "approved_date",
    "no_po",
    "arrival_date",
    "created_at",
    "created_by_id",
    "updated_at",
    "updated_by_id",
  ];

  const datas =
    rcvQcData && rcvQcData.length > 0
      ? rcvQcData
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "")
              .concat(
                item.date_required
                  ? dateFormatterLong(item.date_required).toLowerCase()
                  : ""
              )
              .concat(
                item.pr_created_date
                  ? dateFormatterLong(item.pr_created_date).toLowerCase()
                  : ""
              )
              .concat(
                item.approved_date
                  ? dateFormatterLong(item.approved_date).toLowerCase()
                  : ""
              )
              .concat(
                item.arrival_date
                  ? dateFormatterLong(item.arrival_date).toLowerCase()
                  : ""
              )
              .concat(
                item.created_at
                  ? dateTimeFormatter(
                      item.created_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .concat(
                item.updated_at
                  ? dateTimeFormatter(
                      item.updated_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .some((val) => val?.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const searchData = rcvQcData
    ? rcvQcData.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];

  const handleRequired = () => {
    let required = ["date_required", "pr_created_date", "approved_date"];

    // Required fields in items_detail
    let itemsDetailRequired = ["arrival_date"];

    let error = false;

    // Check if any of the main required fields are missing
    required.forEach((item) => {
      if (!formValue[item]) {
        error = true;
      }
    });

    // Check if any required field in items_detail is missing
    formValue.items_detail.forEach((item) => {
      itemsDetailRequired.forEach((field) => {
        if (!item[field]) {
          error = true;
        }
      });
    });

    return error;
  };

  const handleAdd = async () => {
    if (handleRequired()) {
      toaster.push(Messages("error", "Please fill all required fields!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }
    try {
      let result = await API_EformRcvPrQc().add({
        ...formValue,
        items_detail: formValue.items_detail?.map((item) => ({
          ...item,
          price: parseFloat(item.price),
          qty: parseInt(item.qty),
          total: parseFloat(item.total),
        })),
        created_by_id: props.employee_id,
      });
      if (result?.status !== 200) {
        throw "Failed adding E-Form RCV PR QC";
      }

      console.log("result", result);

      const res = await API_EformRcvPrQc().getAll();
      setRcvQcData(res.data);

      setShowAddModal(false);

      // Show the toast message
      toaster.push(Messages("success", "Success adding E-Form RCV PR QC!"), {
        placement: "topEnd",
        duration: 5000,
      });

      // Reset the form value
      setFormValue(emptyFormValue);
    } catch (error) {
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );
    }
  };

  const handleRequiredEdit = () => {
    let required = [
      "date_required",
      "pr_created_date",
      "approved_date",
      "arrival_date",
    ];
    let error = false;
    required.forEach((item) => {
      if (!formValue[item]) {
        error = true;
      }
    });
    return error;
  };

  const handleEdit = async () => {
    console.log("formValue", formValue);

    if (handleRequiredEdit()) {
      toaster.push(Messages("error", "Please fill all required fields!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }
    try {
      let result = await API_EformRcvPrQc().edit({
        ...formValue,
        id: selectedRcvQc,
        price: parseFloat(formValue.price),
        qty: parseInt(formValue.qty),
        total: parseFloat(formValue.price) * parseInt(formValue.qty),
        updated_by_id: props.employee_id,
      });
      if (result?.status !== 200) {
        throw "Failed editing E-Form RCV PR QC";
      }

      console.log("result", result);

      const res = await API_EformRcvPrQc().getAll();
      setRcvQcData(res.data);
      setShowEditModal(false);

      // Show the toast message
      toaster.push(Messages("success", "Success editing E-Form RCV PR QC!"), {
        placement: "topEnd",
        duration: 5000,
      });

      // Reset the form value
      setFormValue(emptyFormValue);
    } catch (error) {
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );
    }
  };

  const handleExportExcel = () => {
    if (rcvQcData.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }

    const data = rcvQcData.map((item) => ({
      ID: item.id,
      "No Item": item.no_item,
      Description: item.description,
      "No Cat": item.no_cat,
      Supplier: item.supplier,
      Brand: item.brand,
      COA: item.coa,
      Price: item.price,
      Qty: item.qty,
      Total: item.total,
      "Date Required": item.date_required
        ? dateFormatterLong(item.date_required)
        : "-",
      "No PR": item.no_pr,
      "PR Created Date": item.pr_created_date
        ? dateFormatterLong(item.pr_create_date)
        : "-",
      "Approved Date": item.approved_date
        ? dateFormatterLong(item.approved_date)
        : "-",
      "No PO": item.no_po,
      "Arrival Date": item.arrival_date
        ? dateFormatterLong(item.arrival_date)
        : "-",
      "Created At": item.created_at
        ? dateTimeFormatter(item.created_at, "id-ID", "seconds")
        : "-",
      "Created By ID": item.created_by_id,
      "Updated At": item.updated_at
        ? dateTimeFormatter(item.updated_at, "id-ID", "seconds")
        : "-",
      "Updated By ID": item.updated_by_id,
    }));

    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const fileName = `E-Form RCV PR QC ${date}.xlsx`;

    XLSX.writeFile(wb, fileName);
  };
  return (
    <>
      <div>
        <Head>
          <title>E-Form RCV PR QC</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>E-Form</Breadcrumb.Item>
                  <Breadcrumb.Item active>RCV PR QC</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusIcon />}
                    appearance="primary"
                    onClick={() => {
                      setShowAddModal(true);
                    }}
                  >
                    Add
                  </IconButton>
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              data={datas}
              bordered
              cellBordered
              height={400}
              onRowClick={(rowData) => {
                console.log(rowData);
              }}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>No. Item</HeaderCell>
                <Cell dataKey="no_item" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Description</HeaderCell>
                <Cell dataKey="description" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>No. Cat</HeaderCell>
                <Cell dataKey="no_cat" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Supplier</HeaderCell>
                <Cell dataKey="supplier" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Brand</HeaderCell>
                <Cell dataKey="brand" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>COA</HeaderCell>
                <Cell dataKey="coa" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Price</HeaderCell>
                <Cell dataKey="price" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Qty</HeaderCell>
                <Cell dataKey="qty" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Total</HeaderCell>
                <Cell dataKey="total" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Date Required</HeaderCell>
                <Cell dataKey="date_required">
                  {(rowData) => {
                    return rowData?.date_required
                      ? dateFormatterLong(rowData?.date_required)
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>No PR</HeaderCell>
                <Cell dataKey="no_pr" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>PR Created Date</HeaderCell>
                <Cell dataKey="pr_created_date">
                  {(rowData) => {
                    return rowData?.pr_created_date
                      ? dateFormatterLong(rowData?.pr_created_date)
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Approved Date</HeaderCell>
                <Cell dataKey="approved_date">
                  {(rowData) => {
                    return rowData?.approved_date
                      ? dateFormatterLong(rowData?.approved_date)
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>No PO</HeaderCell>
                <Cell dataKey="no_po" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Arrival Date</HeaderCell>
                <Cell dataKey="arrival_date">
                  {(rowData) => {
                    return rowData?.arrival_date
                      ? dateFormatterLong(rowData?.arrival_date)
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created At</HeaderCell>
                <Cell dataKey="created_at">
                  {(rowData) => {
                    return rowData?.created_at
                      ? dateTimeFormatter(
                          rowData?.created_at,
                          "id-ID",
                          "seconds"
                        )
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created By ID</HeaderCell>
                <Cell dataKey="created_by_id" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated At</HeaderCell>
                <Cell dataKey="updated_at">
                  {(rowData) => {
                    return rowData?.updated_at
                      ? dateTimeFormatter(
                          rowData?.updated_at,
                          "id-ID",
                          "seconds"
                        )
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated By ID</HeaderCell>
                <Cell dataKey="updated_by_id" />
              </Column>

              <Column width={80} fixed="right" align="center">
                <HeaderCell>...</HeaderCell>
                <Cell>
                  {(rowData) => {
                    function handleEditAction() {
                      setSelectedRcvQc(rowData.id);
                      setFormValue({
                        ...rowData,
                        date_required: rowData.date_required
                          ? new Date(rowData.date_required)
                          : null,
                        pr_created_date: rowData.pr_created_date
                          ? new Date(rowData.pr_created_date)
                          : null,
                        approved_date: rowData.approved_date
                          ? new Date(rowData.approved_date)
                          : null,
                        arrival_date: rowData.arrival_date
                          ? new Date(rowData.arrival_date)
                          : null,
                        arrival_date: rowData.arrival_date
                          ? new Date(rowData.arrival_date)
                          : null,
                      });
                      setShowEditModal(true);
                    }
                    return (
                      <span>
                        <a
                          onClick={handleEditAction}
                          className="cursor-pointer"
                        >
                          Edit
                        </a>
                      </span>
                    );
                  }}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                total={searchKeyword ? searchData.length : rcvQcData.length}
                limitOptions={[10, 30, 50]}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Add Modal */}
        <Modal
          backdrop="static"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Add E-Form RCV PR QC</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>
                  Date Required<span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  oneTap
                  format="dd-MM-yyyy"
                  value={formValue.date_required}
                  onChange={(value) => {
                    setFormValue({ ...formValue, date_required: value });
                  }}
                  block
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No PR</Form.ControlLabel>
                <Form.Control
                  placeholder="No PR"
                  value={formValue.no_pr}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_pr: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>
                  PR Created Date<span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  oneTap
                  format="dd-MM-yyyy"
                  value={formValue.pr_created_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, pr_created_date: value });
                  }}
                  block
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>
                  Approved Date<span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  oneTap
                  format="dd-MM-yyyy"
                  value={formValue.approved_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, approved_date: value });
                  }}
                  block
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Items Detail</Form.ControlLabel>
                <Panel bordered>
                  {formValue.items_detail?.map((item, index) => (
                    <div key={index} className="flex-col">
                      <Form.Group>
                        <Form.ControlLabel>No. Item</Form.ControlLabel>
                        <Form.Control
                          placeholder="No Item"
                          value={item.no_item}
                          onChange={(value) => handleNoItemChange(index, value)}
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>Description</Form.ControlLabel>
                        <Input
                          placeholder="Description"
                          value={item.description}
                          onChange={(value) =>
                            handleDescriptionChange(index, value)
                          }
                          as="textarea"
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>No. Cat</Form.ControlLabel>
                        <Form.Control
                          placeholder="No. Cat"
                          value={item.no_cat}
                          onChange={(value) => handleNoCatChange(index, value)}
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>Supplier</Form.ControlLabel>
                        <Form.Control
                          placeholder="Supplier"
                          value={item.supplier}
                          onChange={(value) =>
                            handleSupplierChange(index, value)
                          }
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>Brand</Form.ControlLabel>
                        <Form.Control
                          placeholder="Brand"
                          value={item.brand}
                          onChange={(value) => handleBrandChange(index, value)}
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>COA</Form.ControlLabel>
                        <Form.Control
                          placeholder="COA"
                          value={item.coa}
                          onChange={(value) => handleCoaChange(index, value)}
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>Price</Form.ControlLabel>
                        <InputNumber
                          value={item.price}
                          onChange={(value) => handlePriceChange(index, value)}
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>Qty</Form.ControlLabel>
                        <InputNumber
                          value={item.qty}
                          onChange={(value) => handleQtyChange(index, value)}
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>
                          Total (Calc of Price * Qty)
                        </Form.ControlLabel>
                        <InputNumber value={item.total} readOnly />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>No. PO</Form.ControlLabel>
                        <Form.Control
                          placeholder="No. PO"
                          value={item.no_po}
                          onChange={(value) => handleNoPoChange(index, value)}
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>
                          Arrival Date<span className="text-red-500">*</span>
                        </Form.ControlLabel>
                        <DatePicker
                          oneTap
                          format="dd-MM-yyyy"
                          value={item.arrival_date}
                          onChange={(value) =>
                            handleArrivalDateChange(index, value)
                          }
                          block
                        />
                      </Form.Group>

                      <Button
                        color="red"
                        appearance="primary"
                        onClick={() => removeItemsDetail(index)}
                        hidden={formValue.items_detail.length <= 1}
                      >
                        Remove
                      </Button>
                      <Divider />
                    </div>
                  ))}
                  <Button appearance="primary" onClick={addItemsDetail} block>
                    Add Items Detail
                  </Button>
                </Panel>
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowAddModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleAdd();
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Edit Modal */}
        <Modal
          backdrop="static"
          open={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Edit E-Form RCV PR QC</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>
                  Date Required<span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  oneTap
                  format="dd-MM-yyyy"
                  value={formValue.date_required}
                  onChange={(value) => {
                    setFormValue({ ...formValue, date_required: value });
                  }}
                  block
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No PR</Form.ControlLabel>
                <Form.Control
                  placeholder="No PR"
                  value={formValue.no_pr}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_pr: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>
                  PR Created Date<span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  oneTap
                  format="dd-MM-yyyy"
                  value={formValue.pr_created_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, pr_created_date: value });
                  }}
                  block
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>
                  Approved Date<span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  oneTap
                  format="dd-MM-yyyy"
                  value={formValue.approved_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, approved_date: value });
                  }}
                  block
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No. Item</Form.ControlLabel>
                <Form.Control
                  placeholder="No Item"
                  value={formValue.no_item}
                  onChange={(value) => handleNoItemChange(index, value)}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Description</Form.ControlLabel>
                <Input
                  placeholder="Description"
                  value={formValue.description}
                  onChange={(value) => handleDescriptionChange(index, value)}
                  as="textarea"
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No. Cat</Form.ControlLabel>
                <Form.Control
                  placeholder="No. Cat"
                  value={formValue.no_cat}
                  onChange={(value) => handleNoCatChange(index, value)}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Supplier</Form.ControlLabel>
                <Form.Control
                  placeholder="Supplier"
                  value={formValue.supplier}
                  onChange={(value) => handleSupplierChange(index, value)}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Brand</Form.ControlLabel>
                <Form.Control
                  placeholder="Brand"
                  value={formValue.brand}
                  onChange={(value) => handleBrandChange(index, value)}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>COA</Form.ControlLabel>
                <Form.Control
                  placeholder="COA"
                  value={formValue.coa}
                  onChange={(value) => handleCoaChange(index, value)}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Price</Form.ControlLabel>
                <Input
                  value={formValue.price}
                  onChange={(value) => {
                    setFormValue({ ...formValue, price: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Qty</Form.ControlLabel>
                <InputNumber
                  value={formValue.qty}
                  onChange={(value) => {
                    setFormValue({ ...formValue, qty: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>
                  Total (Calc of Price * Qty)
                </Form.ControlLabel>
                <InputNumber value={formValue.price * formValue.qty} readOnly />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No. PO</Form.ControlLabel>
                <Form.Control
                  placeholder="No. PO"
                  value={formValue.no_po}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_po: value });
                  }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>
                  Arrival Date<span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  oneTap
                  format="dd-MM-yyyy"
                  value={formValue.arrival_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, arrival_date: value });
                  }}
                  block
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowEditModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleEdit();
              }}
              appearance="primary"
              type="submit"
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
