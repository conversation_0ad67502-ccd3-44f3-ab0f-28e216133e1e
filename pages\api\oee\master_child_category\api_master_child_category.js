import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiMasterChildCategory() {
  return {
    GetAllMasterChildCategory: createApiFunction("get", "oee/master_child_category/list"),
    GetAllActiveMasterChildCategory: createApiFunction("get", "oee/master_child_category/list-active"),
    CreateMasterChildCategory: createApiFunction("post", "oee/master_child_category/create"),
    EditMasterChildCategory: createApiFunction("put", "oee/master_child_category/edit"),
    EditStatusMasterChildCategory: createApiFunction("put", "oee/master_child_category/edit-status"),
  };
}
