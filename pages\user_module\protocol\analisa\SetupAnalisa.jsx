import { useState, useEffect } from 'react';
import ProtocolApi from '@/pages/api/protocolApi';
import Head from 'next/head';
import ContainerLayout from '@/components/layout/ContainerLayout';
import MainContent from '@/components/layout/MainContent';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import { useRouter } from 'next/router';
import Swal from 'sweetalert2';
import {
    Button,
    Stack,
    Breadcrumb,
    Tag,
    Table,
    Panel,
    Input,
    InputGroup,
    Pagination,
    Divider,
    Modal,
    Form,
    Schema,
    useToaster,
    DatePicker,
    SelectPicker,
    IconButton,
} from "rsuite";
import withReactContent from 'sweetalert2-react-content';
import { paginate } from '@/utils/paginate';
import SearchIcon from '@rsuite/icons/Search';
import FileDownloadIcon from '@rsuite/icons/FileDownload';
import CheckIcon from '@rsuite/icons/Check';
import Messages from '@/components/Messages';
import CloseOutlineIcon from '@rsuite/icons/CloseOutline';

export default function SetupAnalisa() {
    const router = useRouter();
    const MySwal = withReactContent(Swal);
    const [selectedNoProtocol, setSelectedNoProtocol] = useState('');
    const [allProtocolHeaderData, setAllProtocolHeaderData] = useState([]);
    const { GetProtocolDataByNoProtocol, GetProtocolDataForSetupAnalysis, PostApproveSetupAnalisa } = ProtocolApi();
    const [userDept, setUserDept] = useState('');
    const [moduleName, setModuleName] = useState('');
    const [breadcrumbsData, setBreadcrumbsData] = useState([]);
    const { HeaderCell, Cell, Column } = Table;
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const toaster = useToaster();
    const [searchKeyword, setSearchKeyword] = useState("");
    let path = 'protocol/analisa/SetupAnalisa';

    // data to be displayed in the table
    var filteredData = allProtocolHeaderData.filter(item => item.No_Protocol.toLowerCase().includes(searchKeyword.toLowerCase()) || item.Created_By.toLowerCase().includes(searchKeyword.toLowerCase()));
    var datas = paginate(filteredData, page, limit);

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setSortColumn(sortColumn);
        setSortType(sortType);
    };

    const GetProtocolData = async (idDept) => {
        const reqData = {
            id_dept: parseInt(idDept)
        };

        const { data: data } = await GetProtocolDataForSetupAnalysis(reqData);

        if (data !== null && data !== undefined && data?.length > 0) {
            // setAllProtocolHeaderData(data);

            const newData = data.map(item => {
                item.Is_Available = true;
                return item;
            });

            setAllProtocolHeaderData(newData);
            return;
        } else {
            setAllProtocolHeaderData([]);
        }
    };

    useEffect(() => {
        const moduleNameValue = localStorage.getItem('module_name');
        const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
        if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
            router.push('/');
            return;
        }

        // validate access for this page
        if (!dataLogin.menu_link_code) {
            router.push('/dashboard');
            return;
        }

        const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
            item.includes(path),
        );
        if (validateUserAccess.length === 0) {
            router.push('/dashboard');
            return;
        }

        if (
            moduleNameValue === null ||
            moduleNameValue === undefined ||
            moduleNameValue === ''
        ) {
            router.push('/dashboard');
            return;
        }

        const asPathWithoutQuery = router.asPath.split('?')[0];
        const asPathNestedRoutes = asPathWithoutQuery
            .split('/')
            .filter((v) => v.length);
        let routerBreadCrumbsData = asPathNestedRoutes.filter((item, index) => index != 0);
        routerBreadCrumbsData.unshift(moduleNameValue);
        const capitalizeFirstLetter = (str) => {
            return str.charAt(0).toUpperCase() + str.slice(1);
        };
        const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
            const words = item.split(/(?=[A-Z])/);
            return words.map((word) => capitalizeFirstLetter(word)).join(' ');
        });
        setBreadcrumbsData(breadCrumbsResult);
        setUserDept(dataLogin.department);
        setModuleName(moduleNameValue);

        GetProtocolData(dataLogin.department);

    }, []);

    const isDisabledHandler = (effectiveDate) => {
        const newEffectiveDate = effectiveDate.slice(0, 10);

        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are zero-based
        const day = String(now.getDate()).padStart(2, '0');
        const nowFormattedDate = `${year}-${month}-${day}`;

        console.log("Nilai newEffectiveDate : ", newEffectiveDate);
        console.log("Nilai nowFormattedDate : ", nowFormattedDate);

        if (nowFormattedDate < newEffectiveDate) {
            return true;
        } else {
            return false;
        }
    };

    const confirmButtonHandler = async (idSetupCycle, effectiveDate) => {
        // Disable button
        const newData = allProtocolHeaderData.map(item => {
            if (item.Id_Setup_Cycle == parseInt(idSetupCycle)) {
                item.Is_Available = false;
            }
            return item;
        });
        setAllProtocolHeaderData(newData);

        // Validasi if effective date <= today
        const formattedEffectiveDate = effectiveDate.slice(0, 10);
        // Get now Date
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are zero-based
        const day = String(now.getDate()).padStart(2, '0');
        const nowFormattedDate = `${year}-${month}-${day}`;

        if (nowFormattedDate < formattedEffectiveDate) {
            toaster.push(
                Messages("error", "Not yet reached Effective Date !"),
                {
                    placement: "topEnd",
                    duration: 4000,
                }
            );
            // Enable button
            const newData = allProtocolHeaderData.map(item => {
                if (item.Id_Setup_Cycle == parseInt(idSetupCycle)) {
                    item.Is_Available = true;
                }
                return item;
            });
            setAllProtocolHeaderData(newData);
            return;
        }

        const dataReq = {
            id_setup_cycle: parseInt(idSetupCycle)
        };

        const { status, message } = await PostApproveSetupAnalisa(dataReq);
        if (status == 200) {
            toaster.push(
                Messages("success", message ? message : "Insertion success !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            GetProtocolData(parseInt(userDept));
            return;
        } else {
            // Enabling button
            buttonAvailability = true;
            const newData = allProtocolHeaderData.map(item => {
                if (item.Id_Setup_Cycle == parseInt(idSetupCycle)) {
                    item.Is_Available = buttonDisabled;
                }
                return item;
            });
            setAllProtocolHeaderData(newData);
            toaster.push(
                Messages("error", message ? message : "Insertion failed !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            return;
        }
    };

    return (
        <>
            <div>
                <Head>
                    <title>Setup Analisa</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <MainContent>
                    <ModuleContentHeader
                        breadcrumbs={breadcrumbsData}
                        module_name={moduleName}
                    />
                    {allProtocolHeaderData?.length > 0 ?
                        <>
                            <Stack direction='row' justifyContent='flex-end'>
                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="Search"
                                        value={searchKeyword}
                                        onChange={value => {
                                            setSearchKeyword(value);
                                            setPage(1);
                                        }}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}
                                    >
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                            <Panel>
                                <Table
                                    bordered
                                    cellBordered
                                    height={400}
                                    data={datas}
                                    sortColumn={sortColumn}
                                    sortType={sortType}
                                    onSortColumn={handleSortColumn}
                                >
                                    <Column width={60} align="center" fixed>
                                        <HeaderCell>No</HeaderCell>
                                        <Cell>
                                            {(rowData, rowIndex) => {
                                                return rowIndex + 1 + limit * (page - 1);
                                            }}
                                        </Cell>
                                    </Column>

                                    <Column width={200}>
                                        <HeaderCell>No Protocol</HeaderCell>
                                        <Cell dataKey="No_Protocol" />
                                    </Column>

                                    <Column width={250}>
                                        <HeaderCell>Kondisi Penyimpanan</HeaderCell>
                                        <Cell>
                                            {rowData => {
                                                const name = rowData.Timeframe_Name.slice(0, 3);
                                                if (name == 'T30') {
                                                    return <Tag color="red">{rowData.Timeframe_Name}</Tag>;
                                                } else {
                                                    return <Tag color="violet">{rowData.Timeframe_Name}</Tag>;
                                                }
                                            }}
                                        </Cell>
                                    </Column>

                                    <Column width={150}>
                                        <HeaderCell>Effective Date</HeaderCell>
                                        {/* <Cell>{rowData => rowData.Effective_Date.slice(0, 10)}</Cell> */}
                                        <Cell>{rowData => {
                                            const newEffectiveDate = rowData.Effective_Date.slice(0, 10);

                                            const now = new Date();
                                            const year = now.getFullYear();
                                            const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are zero-based
                                            const day = String(now.getDate()).padStart(2, '0');
                                            const nowFormattedDate = `${year}-${month}-${day}`;

                                            if (nowFormattedDate < newEffectiveDate) {
                                                return <p className='text-red-500'>{newEffectiveDate}</p>;
                                            } else {
                                                return <p className='text-green-500'>{newEffectiveDate}</p>;
                                            }
                                        }}</Cell>
                                    </Column>

                                    <Column width={150}>
                                        <HeaderCell>Display Date</HeaderCell>
                                        <Cell>{rowData => rowData.Display_Date.slice(0, 10)}</Cell>
                                    </Column>

                                    <Column width={250}>
                                        <HeaderCell>Dibuat Oleh</HeaderCell>
                                        <Cell dataKey="Created_By" />
                                    </Column>

                                    <Column width={50}>
                                        <HeaderCell>Versi</HeaderCell>
                                        <Cell dataKey="Protocol_Version" />
                                    </Column>

                                    <Column width={150} fixed="right">
                                        <HeaderCell>...</HeaderCell>

                                        <Cell style={{ padding: "6px" }}>
                                            {(rowData) => {
                                                let disabled = true;

                                                const newEffectiveDate = rowData.Effective_Date.slice(0, 10);

                                                const now = new Date();
                                                const year = now.getFullYear();
                                                const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are zero-based
                                                const day = String(now.getDate()).padStart(2, '0');
                                                const nowFormattedDate = `${year}-${month}-${day}`;

                                                if (nowFormattedDate < newEffectiveDate) {
                                                    disabled = true;
                                                } else {
                                                    disabled = false;
                                                }

                                                return <Stack direction='row' justifyContent='center' spacing={2}>
                                                    {<IconButton disabled={!rowData.Is_Available} size='sm' icon={<SearchIcon />} title='Tinjau dokumen' appearance='ghost' color='orange' onClick={() => {
                                                        router.push({
                                                            pathname:
                                                                '/user_module/protocol/management/ViewProtocol',
                                                            query: { noProtocol: rowData.No_Protocol },
                                                        });
                                                    }} />}
                                                    <Divider vertical />
                                                    {<IconButton disabled={disabled} size='sm' icon={<CheckIcon />} title='Konfirmasi' appearance='ghost' color='green' onClick={() => confirmButtonHandler(rowData.Id_Setup_Cycle, rowData.Effective_Date)} />}
                                                </Stack>;
                                            }
                                            }
                                        </Cell>
                                    </Column>
                                </Table>
                                <div style={{ padding: 20 }}>
                                    <Pagination
                                        prev
                                        next
                                        first
                                        last
                                        ellipsis
                                        boundaryLinks
                                        maxButtons={5}
                                        size="xs"
                                        layout={["total", "-", "limit", "|", "pager"]}
                                        // layout={["total", "-", "limit", "|", "pager", "skip"]}
                                        total={
                                            allProtocolHeaderData?.length
                                        }
                                        limitOptions={[10, 30, 50]}
                                        limit={limit}
                                        activePage={page}
                                        onChangePage={setPage}
                                        onChangeLimit={handleChangeLimit}
                                    />
                                </div>
                            </Panel>
                            <Stack direction='row' justifyContent='flex-end'>
                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="Search"
                                        value={searchKeyword}
                                        onChange={value => setSearchKeyword(value)}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}
                                    >
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                        </>
                        :
                        <Stack direction='row' justifyContent='center'>
                            <p className='font-semibold'>No data found.</p>
                        </Stack>
                    }
                </MainContent>
            </ContainerLayout>
        </>
    );
}
