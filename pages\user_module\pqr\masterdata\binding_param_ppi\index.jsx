import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, Notification, SelectPicker, RadioGroup, Radio } from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";

//import api section
import ApiPQR from "@/pages/api/pqr/parameter/api_PQR";
import ApiBindingParamPpi from "@/pages/api/pqr/binding_param_ppi/api_binding_param_ppi";
import ApiMasterdata_ppi from "@/pages/api/pqr/ppi/api_masterdata_ppi";

export default function BindingParamPpiPage() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const toaster = useToaster();
  const router = useRouter();

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [bindingParamPpiDataState, setBindingParamPpiDataState] = useState([]);
  const [ppiDataState, setPpiDataState] = useState([]);
  const [prmDataState, setprmDataState] = useState([]);

  const emptyAddBindingParamPpiForm = {
    id_ppi: null,
    id_parameter: null,
    order_no: null,
    min_value: null,
    max_value: null,
    wetmill: "N",
    created_by: null,
  };

  const emptyEditBindingParamPpiForm = {
    id_ppi: null,
    id_parameter: null,
    order_no: null,
    min_value: null,
    max_value: null,
    wetmill: "N",
    created_by: null,
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [addLoading, setAddLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(null);

  const [addBindingParamPpiForm, setAddBindingParamPpiForm] = useState(emptyAddBindingParamPpiForm);
  const [editBindingParamPpiForm, setEditBindingParamPpiForm] = useState(emptyEditBindingParamPpiForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = bindingParamPpiDataState.filter((rowData, i) => {
    const searchFields = ["id_binding", "ppi_name", "parameter_name", "order_no", "min_value", "max_value", "wetmill", "created_date", "created_by", "updated_date", "updated_by", "deleted_date", "deleted_by", "is_active"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : bindingParamPpiDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/masterdata")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandleGetAllBindingParamPpiApi();
      HandleGetAllPPIApi();
      HandleGetAllPQRApi();
    }
  }, []);

  const HandleGetAllPPIApi = async () => {
    try {
      const res = await ApiMasterdata_ppi().getAllActiveMasterPPI();
      if (res.status === 200) {
        const options = res.data.map((ppi) => ({
          label: ppi.ppi_name,
          value: ppi.id_ppi,
        }));
        setPpiDataState(options);
      } else {
        console.log("Error fetching PPI options", res.message);
      }
    } catch (error) {
      console.log("Error fetching PPI options", error);
    }
  };

  const HandleGetAllPQRApi = async () => {
    try {
      const res = await ApiPQR().getAllActivePQR();
      if (res.status === 200) {
        const options = res.data.map((prm) => ({
          label: prm.parameter_name,
          value: prm.id_parameter,
        }));
        setprmDataState(options);
      } else {
        console.log("Error fetching Parameter options", res.message);
      }
    } catch (error) {
      console.log("Error fetching Parameter options", error);
    }
  };

  const HandleGetAllBindingParamPpiApi = async () => {
    try {
      const res = await ApiBindingParamPpi().getAllBindingParamPpi();

      console.log("res", res);
      if (res.status === 200) {
        setBindingParamPpiDataState(res.data);
      } else {
        console.log("error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Api", error);
    }
  };

  const HandleGetDetailIdBinding = async (id_ppi) => {
    try {
      const res = await ApiBindingParamPpi().getIdPpiActiveBindingParamPpi({
        id_ppi,
      });
      console.log("res", res);
      if (res.status === 200) {
        setBindingParamPpiDataState(res.data);
      } else {
        console.log("error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Api", error);
    }
  };

  const HandleAddBindingParamPpiApi = async () => {
    const errors = {};

    // Check each field and only set the error if it's empty
    if (!addBindingParamPpiForm.id_ppi) {
      errors.id_ppi = "PPI Name is required";
    }
    if (!addBindingParamPpiForm.id_parameter) {
      errors.id_parameter = "Parameter Name is required";
    }
    if (!addBindingParamPpiForm.order_no) {
      errors.order_no = "Order No is required";
    }
    if (!addBindingParamPpiForm.min_value) {
      errors.min_value = "Min Value is required";
    }
    if (!addBindingParamPpiForm.max_value) {
      errors.max_value = "Max Value is required";
    }
    if (!addBindingParamPpiForm.wetmill) {
      errors.wetmill = "wetmill is required";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }
    try {
      setAddLoading(true);
      const res = await ApiBindingParamPpi().createBindingParamPpi({
        ...addBindingParamPpiForm,
        created_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        setAddBindingParamPpiForm(emptyAddBindingParamPpiForm);
        setShowAddModal(false);
        await HandleGetAllBindingParamPpiApi();
        showNotification("success", "Added successfully");
      } else {
        showNotification("error", "Failed to add data");
      }
    } catch (error) {
      showNotification("error", "Error adding data");
    } finally {
      setAddLoading(false);
    }
  };

  const HandleEditBindingParamPpiApi = async () => {
    const errors = {};

    // Check each field and only set the error if it's empty
    if (!editBindingParamPpiForm.id_ppi) {
      errors.id_ppi = "PPI Name is required";
    }
    if (!editBindingParamPpiForm.id_parameter) {
      errors.id_parameter = "Parameter Name is required";
    }
    if (!editBindingParamPpiForm.order_no) {
      errors.order_no = "Order No is required";
    }
    if (!editBindingParamPpiForm.min_value) {
      errors.min_value = "Min Value is required";
    }
    if (!editBindingParamPpiForm.max_value) {
      errors.max_value = "Max Value is required";
    }
    if (!editBindingParamPpiForm.wetmill) {
      errors.wetmill = "wetmill is required";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);

      return;
    }
    try {
      setEditLoading(true);
      const res = await ApiBindingParamPpi().editBindingParamPpi({
        ...editBindingParamPpiForm,
        updated_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        await HandleGetAllBindingParamPpiApi();
        setShowEditModal(false);
        showNotification("success", "Data updated successfully");
      } else {
        showNotification("error", "Failed to update data");
      }
    } catch (error) {
      toaster.push({ message: "Error updating data", type: "error" });
    } finally {
      setEditLoading(false);
    }
  };

  const handleEditStatusBindingParamPpiApi = async (id_binding, is_active) => {
    try {
      setStatusLoading(id_binding);
      const res = await ApiBindingParamPpi().editStatusBindingParamPpi({
        id_binding,
        is_active,
        deleted_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        await HandleGetAllBindingParamPpiApi();
        toaster.push({
          message: `Status ${is_active === 1 ? "deactivated" : "activated"} successfully`,
          type: "success",
        });
      } else {
        toaster.push({ message: "Failed to update status", type: "error" });
      }
    } catch (error) {
      toaster.push({ message: "Error updating status", type: "error" });
    } finally {
      setStatusLoading(null);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Binding Param Ppi</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>Binding Param Ppi</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Binding Param ppi Page</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}
                    >
                      Add
                    </IconButton>
                  </div>

                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                <Column width={180} align="center" sortable fullText>
                  <HeaderCell>ID Binding</HeaderCell>
                  <Cell dataKey="id_binding" />
                </Column>
                <Column width={250} sortable fullText>
                  <HeaderCell align="center">PPI Name</HeaderCell>
                  <Cell dataKey="ppi_name" />
                </Column>
                <Column width={250} sortable fullText>
                  <HeaderCell align="center">Parameter Name</HeaderCell>
                  <Cell dataKey="parameter_name" />
                </Column>
                <Column width={180} sortable fullText>
                  <HeaderCell align="center">Order No</HeaderCell>
                  <Cell dataKey="order_no" />
                </Column>
                <Column width={250} sortable fullText>
                  <HeaderCell align="center">Min Value</HeaderCell>
                  <Cell>{(rowData) => <span>{rowData.min_value.toLocaleString("id-ID")}</span>}</Cell>
                </Column>
                <Column width={250} sortable fullText>
                  <HeaderCell align="center">Max Value</HeaderCell>
                  <Cell>{(rowData) => <span>{rowData.max_value.toLocaleString("id-ID")}</span>}</Cell>
                </Column>
                <Column width={120} sortable fullText>
                  <HeaderCell align="center">wetmill</HeaderCell>
                  <Cell dataKey="wetmill">{(rowData) => (rowData.wetmill === "N" ? "No" : "Yes")}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Created Date</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.created_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Created By</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.created_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Updated Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.updated_date ? new Date(rowData.updated_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Updated By</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.updated_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Deleted Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.deleted_date ? new Date(rowData.deleted_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Deleted By</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.deleted_by}</>}</Cell>
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Active" : "Inactive"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Action</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div className="flex gap-2">
                        <Button
                          appearance="link"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModal(true);
                            setEditBindingParamPpiForm({
                              ...editBindingParamPpiForm,
                              id_binding: rowData.id_binding,
                              id_ppi: rowData.id_ppi,
                              id_parameter: rowData.id_parameter,
                              order_no: rowData.order_no,
                              min_value: rowData.min_value,
                              max_value: rowData.max_value,
                              wetmill: rowData.wetmill,
                              updated_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                            });
                          }}
                        >
                          Edit
                        </Button>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            const id_binding = rowData.id_binding;
                            router.push(`/user_module/pqr/masterdata/binding_param_ppi/detail_binding?Id=${id_binding}`);
                          }}
                        >
                          <SearchIcon style={{ fontSize: "16px" }} />
                        </Button>
                        <Button appearance="subtle" onClick={() => handleEditStatusBindingParamPpiApi(rowData.id_binding, rowData.is_active)}>
                          {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            {/* modal pop up untuk add binding */}
            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                if (!addLoading) {
                  setShowAddModal(false);
                  setAddBindingParamPpiForm(emptyAddBindingParamPpiForm);
                  setErrorsAddForm({});
                }
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Add Binding</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>PPI Name</Form.ControlLabel>
                    <SelectPicker
                      data={ppiDataState}
                      value={addBindingParamPpiForm.id_ppi}
                      onChange={(value) => {
                        setAddBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_ppi: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          id_ppi: undefined,
                        }));
                      }}
                      block
                      placeholder="Select PPI"
                      style={{ width: "100%" }}
                    />
                    {errorsAddForm.id_ppi && <p style={{ color: "red" }}>{errorsAddForm.id_ppi}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Parameter Name</Form.ControlLabel>
                    <SelectPicker
                      data={prmDataState}
                      value={addBindingParamPpiForm.id_parameter}
                      onChange={(value) => {
                        setAddBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_parameter: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          id_parameter: undefined,
                        }));
                      }}
                      block
                      placeholder="Select Parameter"
                      style={{ width: "100%" }}
                    />
                    {errorsAddForm.id_parameter && <p style={{ color: "red" }}>{errorsAddForm.id_parameter}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Order no</Form.ControlLabel>
                    <Form.Control
                      name="order_no"
                      value={addBindingParamPpiForm.order_no}
                      onChange={(value) => {
                        if (parseInt(value) >= 0 || value === "") {
                          setAddBindingParamPpiForm((prevFormValue) => ({
                            ...prevFormValue,
                            order_no: value === "" ? "" : parseInt(value),
                          }));
                          setErrorsAddForm((prevErrors) => ({
                            ...prevErrors,
                            order_no: undefined,
                          }));
                        }
                      }}
                      type="number"
                      min={0}
                    />
                    {errorsAddForm.order_no && <p style={{ color: "red" }}>{errorsAddForm.order_no}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Min Value</Form.ControlLabel>
                    <Form.Control
                      name="min_value"
                      value={addBindingParamPpiForm.min_value}
                      onChange={(value) => {
                        setAddBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          min_value: parseFloat(value) || "",
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          min_value: undefined,
                        }));
                      }}
                      type="number"
                    />
                    {errorsAddForm.min_value && <p style={{ color: "red" }}>{errorsAddForm.min_value}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Max Value</Form.ControlLabel>
                    <Form.Control
                      name="max_value"
                      value={addBindingParamPpiForm.max_value}
                      onChange={(value) => {
                        setAddBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          max_value: parseFloat(value) || "",
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          max_value: undefined,
                        }));
                      }}
                      type="number"
                    />
                    {errorsAddForm.max_value && <p style={{ color: "red" }}>{errorsAddForm.max_value}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>wetmill</Form.ControlLabel>
                    <RadioGroup
                      name="wetmill"
                      inline
                      value={addBindingParamPpiForm.wetmill}
                      onChange={(value) => {
                        setAddBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          wetmill: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          wetmill: undefined,
                        }));
                      }}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsAddForm.wetmill && <p style={{ color: "red" }}>{errorsAddForm.wetmill}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddBindingParamPpiForm(emptyAddBindingParamPpiForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle"
                  disabled={addLoading}
                >
                  Cancel
                </Button>
                <Button onClick={HandleAddBindingParamPpiApi} appearance="primary" loading={addLoading} disabled={addLoading}>
                  Add
                </Button>
              </Modal.Footer>
            </Modal>

            {/* modal pop up untuk edit ppi */}
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditBindingParamPpiForm(emptyEditBindingParamPpiForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Edit Binding</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>PPI Name</Form.ControlLabel>
                    <SelectPicker
                      data={ppiDataState}
                      value={editBindingParamPpiForm.id_ppi}
                      onChange={(value) => {
                        setEditBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_ppi: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          id_ppi: undefined,
                        }));
                      }}
                      block
                      placeholder="Select PPI"
                      style={{ width: "100%" }}
                    />
                    {errorsEditForm.id_ppi && <p style={{ color: "red" }}>{errorsEditForm.id_ppi}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Parameter Name</Form.ControlLabel>
                    <SelectPicker
                      data={prmDataState}
                      value={editBindingParamPpiForm.id_parameter}
                      onChange={(value) => {
                        setEditBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_parameter: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          id_parameter: undefined,
                        }));
                      }}
                      block
                      placeholder="Select Parameter"
                      style={{ width: "100%" }}
                    />
                    {errorsEditForm.id_parameter && <p style={{ color: "red" }}>{errorsEditForm.id_parameter}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Order no</Form.ControlLabel>
                    <Form.Control
                      name="order_no"
                      value={editBindingParamPpiForm.order_no}
                      onChange={(value) => {
                        if (parseInt(value) >= 0 || value === "") {
                          setEditBindingParamPpiForm((prevFormValue) => ({
                            ...prevFormValue,
                            order_no: value === "" ? "" : parseInt(value),
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            order_no: undefined,
                          }));
                        }
                      }}
                      type="number"
                      min={0}
                    />
                    {errorsEditForm.order_no && <p style={{ color: "red" }}>{errorsEditForm.order_no}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Min Value</Form.ControlLabel>
                    <Form.Control
                      name="min_value"
                      value={editBindingParamPpiForm.min_value}
                      onChange={(value) => {
                        setEditBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          min_value: parseFloat(value) || "",
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          min_value: undefined,
                        }));
                      }}
                      type="number"
                    />
                    {errorsEditForm.min_value && <p style={{ color: "red" }}>{errorsEditForm.min_value}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Max Value</Form.ControlLabel>
                    <Form.Control
                      name="max_value"
                      value={editBindingParamPpiForm.max_value}
                      onChange={(value) => {
                        setEditBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          max_value: parseFloat(value) || "",
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          max_value: undefined,
                        }));
                      }}
                      type="number"
                    />
                    {errorsEditForm.max_value && <p style={{ color: "red" }}>{errorsEditForm.max_value}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>wetmill</Form.ControlLabel>
                    <RadioGroup
                      name="wetmill"
                      inline
                      value={editBindingParamPpiForm.wetmill}
                      onChange={(value) => {
                        setEditBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          wetmill: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          wetmill: undefined,
                        }));
                      }}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsEditForm.wetmill && <p style={{ color: "red" }}>{errorsEditForm.wetmill}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditBindingParamPpiForm(emptyEditBindingParamPpiForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                  disabled={editLoading}
                >
                  Cancel
                </Button>
                <Button onClick={HandleEditBindingParamPpiApi} appearance="primary" loading={editLoading} disabled={editLoading}>
                  Edit
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
