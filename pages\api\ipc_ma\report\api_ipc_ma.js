import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiMAHeader() {
  return {
    getAllMAHeader: createApiFunction("get", "v2/ipc/ipc_ma/list"),
    getMADetailWithStatistics: createApiFunction("post", "v2/ipc/ipc_ma/list-detail"),
  };
}
