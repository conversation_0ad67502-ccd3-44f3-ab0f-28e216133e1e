// pages/pdf.js
import React from 'react';
import htmlToPdfMake from 'html-to-pdfmake';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';

pdfMake.vfs = pdfFonts.pdfMake.vfs;

const headerData = [
  {
    Title: "Sample Title",
    Desc_Purpose: `<ol><li><strong>Protocol 1</strong></li><li><em>Protocol 1</em></li><li><u>Protocol 1</u><span class="ql-cursor"></span></li></ol>`,
    Created_by: "<PERSON>",
    Job_title: "Engineer",
    Approve_By: "<PERSON>",
  }
];

const generatePdf = () => {
  const descPurposeContent = htmlToPdfMake(headerData[0].Desc_Purpose);

  const dd = {
    content: [
      { text: `\n${headerData[0].Title}\n\n`, style: 'subheader' },
      ...descPurposeContent,
      {
        style: 'tableExample',
        table: {
          widths: ['*', '*', '*'],
          body: [
            ['Name', 'Position', 'Date'],
            ['Prepared By', '', ''],
            [`${headerData[0].Created_by}`, `${headerData[0].Job_title}`, `${headerData[0].Job_title}`],
            ['Reviewed By', '', ''],
            [`${headerData[0].Approve_By}`, `${headerData[0].Job_title}`, 'by status'],
          ]
        }
      }
    ],
    styles: {
      header: {
        fontSize: 22,
        bold: true,
        alignment: 'center'
      },
      subheader: {
        fontSize: 18,
        bold: true,
        alignment: 'center'
      },
      tableExample: {
        margin: [0, 5, 0, 15]
      }
    }
  };

  pdfMake.createPdf(dd).open();
};

const PdfPage = () => {
  return (
    <div>
      <h1>Generate PDF</h1>
      <button onClick={generatePdf}>Download PDF</button>
    </div>
  );
};

export default PdfPage;
