import ContainerLayout from "@/components/layout/ContainerLayout";
import Head from "next/head";
import React, { useEffect, useState } from "react";
import {
  Breadcrumb,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  SelectPicker,
  IconButton,
  DateRangePicker,
  useToaster,
} from "rsuite";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import API_LogActivity from "@/pages/api/log_activity/api_log_activity_dashboard";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash"
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import Messages from "@/components/Messages";

export default function LogActivityDashboard() {
  const [moduleName, setModuleName] = useState("");
  const [props, setProps] = useState([]);
  const { HeaderCell, Cell, Column } = Table;

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const [logActivityDashboard, setLogActivityDashboard] = useState([]);
  const [modules, setModules] = useState([]);
  const [selectedModule, setSelectedModule] = useState(null);
  const [enabledDates, setEnabledDates] = useState([]);
  const [selectedRange, setSelectedRange] = useState(null);

  const toaster = useToaster();

  const handleGetAllApi = async () => {
    const response = await API_LogActivity().getAll();
    if (response.status === 200) {
      setLogActivityDashboard(response.data ? response.data : []);
    }
  };

  const handleGetModuleApi = async () => {
    const response = await API_LogActivity().getModule();
    if (response.status === 200) {
      setModules(
        response.data.map((module) => ({
          label: module.module_pim,
          value: module.module_pim,
        }))
      );
    }
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = logActivityDashboard.filter((rowData) => {
    const searchFields = [
      "id", 
      "employee_id", 
      "module_pim", 
      "activity", 
      "createdt"
    ];
  
    const matchesSearch = searchFields.some((field) =>
      rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase())
    );
  
    const matchesModule = !selectedModule || rowData.module_pim === selectedModule;
  
    const matchesDateRange =
      !selectedRange ||
      (new Date(rowData.createdt) >= selectedRange[0] &&
        new Date(rowData.createdt) <= selectedRange[1]);
  
    return matchesSearch && matchesModule && matchesDateRange;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = filteredData.length;

  const formatTableDate = (date) => {
    const dateObj = new Date(date);
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, "0");
    const day = String(dateObj.getDate()).padStart(2, "0");
    return `${day}/${month}/${year}`;
  };

  const handleRangeChange = (value) => {
    setSelectedRange(value);
    setPage(1);
    handleGetAllApi();
  };

  const handleExportExcel = () => {
    if (filteredData.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }
  
    const headerMapping = {
      id: "ID",
      employee_id: "Employee",
      module_pim: "Module",
      activity: "Activity",
      createdt: "Created Date",
    };
  
    const formattedData = filteredData.map((item) => {
      const formattedItem = {};
      for (const key in item) {
        if (headerMapping[key]) {
          if (key === "createdt") {
            formattedItem[headerMapping[key]] = formatTableDate(item[key]);
          } else {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
      }
      return formattedItem;
    });
  
    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");
  
    const date = dateFormatterDash(new Date());
    const filename = `Log Activity ${date}.xlsx`;
  
    XLSX.writeFile(wb, filename);
  };

  useEffect(() => {
    const uniqueDates = [
      ...new Set(
        logActivityDashboard.map((row) => new Date(row.createdt).toDateString())
      ),
    ].map((dateString) => new Date(dateString));
    setEnabledDates(uniqueDates);
  }, [logActivityDashboard]);

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      handleGetAllApi();
      handleGetModuleApi();
    }
  }, []);

  return (
    <>
      <div>
        <Head>
          <title>Log Activity Dashboard</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <Breadcrumb>
                <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                <Breadcrumb.Item>Quality</Breadcrumb.Item>
                <Breadcrumb.Item>Log Activity</Breadcrumb.Item>
                <Breadcrumb.Item active>Log Activity Dashboard</Breadcrumb.Item>
              </Breadcrumb>
            </Stack.Item>
            <Stack.Item>
              <Tag color="green">Module: {moduleName}</Tag>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack
                justifyContent="space-between"
                spacing={10}
                style={{ width: "100%" }}
              >
                <Stack.Item>
                  <SelectPicker
                    data={modules}
                    value={selectedModule}
                    onChange={(value) => {
                      setSelectedModule(value);
                      setPage(1);
                    }}
                    placeholder="Select Module"
                    searchable={false}
                    style={{ width: 150 }}
                  />
                </Stack.Item>

                <Stack.Item>
                  <DateRangePicker
                    placeholder="Select Date Range"
                    value={selectedRange}
                    onChange={handleRangeChange}
                    style={{ width: 210 }}
                    format="dd/MM/yyyy"
                    disabledDate={(date) => date > new Date()}
                  />
                </Stack.Item>

                <IconButton
                  icon={<FileDownloadIcon />}
                  appearance="primary"
                  onClick={handleExportExcel}
                >
                  Download (.xlsx)
                </IconButton>

                <Stack.Item style={{ marginLeft: "auto" }}>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input
                      placeholder="Search"
                      value={searchKeyword}
                      onChange={handleSearch}
                    />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack.Item>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, index) => index + 1 + limit * (page - 1)}
                </Cell>
              </Column>
              <Column width={70} align="center">
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id" />
              </Column>
              <Column width={350} sortable resizable>
                <HeaderCell align="center">Employee ID</HeaderCell>
                <Cell dataKey="employee_id" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell align="center">Module</HeaderCell>
                <Cell align="center" dataKey="module_pim" />
              </Column>
              <Column width={400} sortable resizable>
                <HeaderCell align="center">Activity</HeaderCell>
                <Cell dataKey="activity" />
              </Column>
              <Column width={132} align="center" sortable resizable>
                <HeaderCell>Created Date</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span>{formatTableDate(rowData.createdt)}</span>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>
    </>
  );
}
