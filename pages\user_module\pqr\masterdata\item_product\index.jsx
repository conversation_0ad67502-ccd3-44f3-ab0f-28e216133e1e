import { useEffect, useState } from "react";
import Head from "next/head";
import {
    Breadcrumb,
    IconButton,
    Input,
    InputGroup,
    Pagination,
    Panel,
    Stack,
    Table,
    Tag,
    Button,
    Modal,
    Form,
    useToaster,
    Notification,
    ButtonGroup,
    Loader,
    DatePicker,
    RadioGroup,
    Radio,
    InputNumber
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiItemProduct from "@/pages/api/pqr/item_product/api_item_product";
import { useRouter } from "next/router";

export default function MasterdataItemProduct() {
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState("id_item");
    const [sortType, setSortType] = useState("asc");
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const toaster = useToaster();
    const router = useRouter();
    const [loading, setLoading] = useState(false);

    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [itemProductDataState, setItemProductDataState] = useState([])

    const emptyAddItemProductForm = {
        item_code: null,
        nilai_konversi: null,
        is_active: 1,
    };

    const emptyEditItemProductForm = {
        item_code: null,
        nilai_konversi: null,
        is_active: 1,
    };

    const [showAddModal, setShowAddModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [addItemProductForm, setAddItemProductForm] = useState(emptyAddItemProductForm);
    const [editItemProductForm, setEditItemProductForm] = useState(emptyEditItemProductForm);
    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [errorsEditForm, setErrorsEditForm] = useState({});

    const showNotification = (type, message, duration = 2000) => {
        toaster.push(
            <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
                <p>{message}</p>
            </Notification>,
            { placement: "topEnd", duration }
        );
    };

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = itemProductDataState.filter((rowData) => {
        const searchFields = ["id_item", "item_code", "nilai_konversi", "is_active"];
        const matchesSearch = searchFields.some((field) =>
            rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase())
        );
        return matchesSearch;
    });

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    };

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : itemProductDataState.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setSessionAuth(dataLogin);

        if (!dataLogin) {
            router.push("/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("pqr/")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }

            HandleGetAllItemProductApi();
        }
    }, []);

    const HandleGetAllItemProductApi = async () => {
        try {
            const res = await ApiItemProduct().getAllItemProduct();
            if (res.status === 200) {
                setItemProductDataState(res.data);
            } else {
                console.log("Error on GetAllItemProductApi: ", res.message);
            }
        } catch (error) {
            console.log("Error on catch GetAllItemProductApi: ", error.message);
        }
    };

    const HandleAddItemProductApi = async () => {
        const errors = {};
        if (!addItemProductForm.item_code) {
            errors.item_code = "Kode Item Wajib Diisi!";
        }
        if (!addItemProductForm.nilai_konversi) {
            errors.nilai_konversi = "Nilai Konversi Wajib Diisi!";
        }
        if (!addItemProductForm.item_description) {
            errors.item_description = "Item Deskripsi Wajib Diisi!";
        }


        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }

        setLoading(true);
        try {
            const res = await ApiItemProduct().createItemProduct({
                ...addItemProductForm,
                create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
            });

            if (res.status === 200) {
                setAddItemProductForm(emptyAddItemProductForm);
                setShowAddModal(false);
                HandleGetAllItemProductApi();
                showNotification("success", "Item Produk Berhasil Ditambahkan");
            } else {
                console.log("Error on AddItemProductApi: ", res.message);
                showNotification("error", "Item Produk Gagal Ditambahkan");
            }
        } catch (error) {
            console.log("Error on AddItemProductApi: ", error.message);
            showNotification("error", "gagal menambah data");
        } finally {
            setLoading(false);

        }
    };

    const HandleEditItemProductApi = async () => {
        const errors = {};
        if (!editItemProductForm.item_code) {
            errors.item_code = "Item Produk Wajib Diisi!";
        }
        if (!editItemProductForm.nilai_konversi) {
            errors.nilai_konversi = "Nilai Konversi Wajib Diisi!";
        }
        if (!editItemProductForm.item_description) {
            errors.item_description = "Item Deskripsi Wajib Diisi!";
        }

        if (Object.keys(errors).length > 0) {
            setErrorsEditForm(errors);
            return;
        }
        setLoading(true);

        try {
            const res = await ApiItemProduct().editItemProduct({
                ...editItemProductForm,
                update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
            });

            if (res.status === 200) {
                HandleGetAllItemProductApi();
                showNotification("success", "Item Produk Berhasil Diubah");
                setShowEditModal(false);
            } else {
                console.log("Error on EditItemProductApi: ", res.message);
                showNotification("error", "gagal mengubah data");
            }
        } catch (error) {
            console.log("Error on EditItemProductApi: ", error.message);
            showNotification("error", "gagal mengubah data");
        }
        finally {
            setLoading(false);
        }
    };

    const handleEditStatusItemProductApi = async (id_item, is_active) => {
        try {
            const res = await ApiItemProduct().editStatusItemProduct({
                id_item,
                is_active,
                delete_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
            });

            if (res.status === 200) {
                HandleGetAllItemProductApi();
                showNotification("success", "Status Berhasil Diubah");
            } else {
                console.log("Error on update status: ", res.message);
                showNotification("error", "gagal mengubah status");
            }
        } catch (error) {
            console.log("Error on update status: ", error.message);
            showNotification("error", "gagal mengubah status");
        }
    };

    return (
        <div>
            <div>
                <Head>
                    <title>Master Data Produk</title>
                </Head>
            </div>
            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>E Release</Breadcrumb.Item>
                                    <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Halaman Item Produk</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Halaman Item Produk</h5>
                            </Stack>
                        }
                    ></Panel>
                    <div>
                        <Panel
                            bordered
                            bodyFill
                            header={
                                <Stack justifyContent="space-between">
                                    <div className="flex gap-2">
                                        <IconButton
                                            icon={<PlusRoundIcon />}
                                            appearance="primary"
                                            onClick={() => {
                                                setShowAddModal(true);
                                            }}
                                        >
                                            Tambah
                                        </IconButton>
                                    </div>
                                    <InputGroup inside>
                                        <InputGroup.Addon>
                                            <SearchIcon />
                                        </InputGroup.Addon>
                                        <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                                        <InputGroup.Addon
                                            onClick={() => {
                                                setSearchKeyword("");
                                                setPage(1);
                                            }}
                                            style={{
                                                display: searchKeyword ? "block" : "none",
                                                color: "red",
                                                cursor: "pointer",
                                            }}
                                        >
                                            <CloseOutlineIcon />
                                        </InputGroup.Addon>
                                    </InputGroup>
                                </Stack>
                            }
                        >

                            <Table
                                bordered
                                cellBordered
                                height={400}
                                data={getPaginatedData(getFilteredData(), limit, page)}
                                sortColumn={sortColumn}
                                sortType={sortType}
                                onSortColumn={handleSortColumn}
                            >
                                <Column width={120} align="center" sortable fullText resizable>
                                    <HeaderCell>ID Item Product</HeaderCell>
                                    <Cell dataKey="id_item" />
                                </Column>
                                <Column width={150} sortable fullText resizable>
                                    <HeaderCell align="center">Kode Item</HeaderCell>
                                    <Cell dataKey="item_code" />
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Nilai Konversi</HeaderCell>
                                    <Cell dataKey="nilai_konversi" />
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Item Deskripsi</HeaderCell>
                                    <Cell dataKey="item_description" />
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Pembuatan</HeaderCell>
                                    <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dibuat oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Diperbarui</HeaderCell>
                                    <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Dihapus</HeaderCell>
                                    <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.delete_by}</>}</Cell>
                                </Column>
                                <Column width={100} sortable resizable align="center" fullText>
                                    <HeaderCell>Status</HeaderCell>
                                    <Cell>
                                        {(rowData) => (
                                            <span
                                                style={{
                                                    color: rowData.is_active === 1 ? "green" : "red",
                                                }}
                                            >
                                                {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                                            </span>
                                        )}
                                    </Cell>
                                </Column>
                                <Column width={120} fixed="right" align="center">
                                    <HeaderCell>Aksi</HeaderCell>
                                    <Cell style={{ padding: "8px" }}>
                                        {(rowData) => (
                                            <div>
                                                <Button
                                                    appearance="subtle"
                                                    disabled={rowData.is_active === 0}
                                                    onClick={() => {
                                                        setShowEditModal(true);
                                                        setEditItemProductForm({
                                                            ...editItemProductForm,
                                                            item_code: rowData.item_code,
                                                            nilai_konversi: rowData.nilai_konversi,
                                                            id_item: rowData.id_item,
                                                            item_description: rowData.item_description,
                                                            update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                                                        });
                                                    }}
                                                >
                                                    <EditIcon />
                                                </Button>
                                                <Button appearance="subtle" onClick={() => handleEditStatusItemProductApi(rowData.id_item, rowData.is_active)}>
                                                    {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                                                </Button>
                                            </div>
                                        )}
                                    </Cell>
                                </Column>
                            </Table>

                            <div style={{ padding: 20 }}>
                                <Pagination
                                    prev
                                    next
                                    first
                                    last
                                    ellipsis
                                    boundaryLinks
                                    maxButtons={5}
                                    size="xs"
                                    layout={["total", "-", "limit", "|", "pager", "skip"]}
                                    limitOptions={[10, 30, 50]}
                                    total={totalRowCount}
                                    limit={limit}
                                    activePage={page}
                                    onChangePage={setPage}
                                    onChangeLimit={handleChangeLimit}
                                />
                            </div>
                        </Panel>

                        <Modal
                            backdrop="static"
                            open={showAddModal}
                            onClose={() => {
                                setShowAddModal(false);
                                setAddItemProductForm(emptyAddItemProductForm);
                                setErrorsAddForm({});
                            }}
                            overflow={false}
                        >
                            <Modal.Header>
                                <Modal.Title>Tambah Item Produk</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <Form fluid>
                                    <Form.Group>
                                        <Form.ControlLabel>Item Kode</Form.ControlLabel>
                                        <Form.Control
                                            name="item_code"
                                            value={addItemProductForm.item_code}
                                            onChange={(value) => {
                                                setAddItemProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    item_code: value,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    item_code: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsAddForm.item_code && <p style={{ color: "red" }}>{errorsAddForm.item_code}</p>}
                                    </Form.Group>

                                    <Form.Group>
                                        <Form.ControlLabel>Nilai Konversi</Form.ControlLabel>
                                        <Form.Control
                                            name="nilai_konversi"
                                            value={addItemProductForm.nilai_konversi}
                                            onChange={(value) => {
                                                setAddItemProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    nilai_konversi: parseFloat(value) || 0,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    nilai_konversi: undefined,
                                                }));
                                            }}
                                            accepter={InputNumber}
                                            step={0.01}
                                            precision={2}
                                        />
                                        {errorsAddForm.nilai_konversi && <p style={{ color: "red" }}>{errorsAddForm.nilai_konversi}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Item Deskripsi</Form.ControlLabel>
                                        <Form.Control
                                            name="item_description"
                                            value={addItemProductForm.item_description}
                                            onChange={(value) => {
                                                setAddItemProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    item_description: value,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    item_description: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsAddForm.item_description && <p style={{ color: "red" }}>{errorsAddForm.item_description}</p>}
                                    </Form.Group>
                                </Form>
                            </Modal.Body>
                            <Modal.Footer>
                                <Button
                                    onClick={() => {
                                        setShowAddModal(false);
                                        setAddItemProductForm(emptyAddItemProductForm);
                                        setErrorsAddForm({});
                                    }}
                                    appearance="subtle"
                                >
                                    Batal
                                </Button>
                                <Button
                                    onClick={() => {
                                        HandleAddItemProductApi();
                                    }}
                                    appearance="primary"
                                    type="submit"
                                    loading={loading}
                                    disabled={loading}
                                >
                                    Tambah
                                </Button>
                                {loading && <Loader backdrop size="md" vertical content="Adding Data..." active={loading} />}
                            </Modal.Footer>
                        </Modal>
                        <Modal
                            backdrop="static"
                            open={showEditModal}
                            onClose={() => {
                                setShowEditModal(false);
                                setEditItemProductForm(emptyEditItemProductForm);
                                setErrorsEditForm({});
                            }}
                            overflow={false}
                        >
                            <Modal.Header>
                                <Modal.Title>Ubah Item Produk</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <Form fluid>
                                    <Form.Group>
                                        <Form.ControlLabel>Item Produk</Form.ControlLabel>
                                        <Form.Control
                                            name="item_code"
                                            value={editItemProductForm.item_code}
                                            onChange={(value) => {
                                                setEditItemProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    item_code: value,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    item_code: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsEditForm.item_code && <p style={{ color: "red" }}>{errorsEditForm.item_code}</p>}
                                    </Form.Group>

                                    <Form.Group>
                                        <Form.ControlLabel>Nilai Konversi</Form.ControlLabel>
                                        <Form.Control
                                            name="nilai_konversi"
                                            value={editItemProductForm.nilai_konversi}
                                            onChange={(value) => {
                                                setEditItemProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    nilai_konversi: parseFloat(value) || 0,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    nilai_konversi: undefined,
                                                }));
                                            }}
                                            accepter={InputNumber}
                                            step={0.01}
                                            precision={2}
                                        />
                                        {errorsEditForm.nilai_konversi && <p style={{ color: "red" }}>{errorsEditForm.nilai_konversi}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Item Deskripsi</Form.ControlLabel>
                                        <Form.Control
                                            name="item_description"
                                            value={editItemProductForm.item_description}
                                            onChange={(value) => {
                                                setEditItemProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    item_description: value,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    item_description: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsEditForm.item_description && <p style={{ color: "red" }}>{errorsEditForm.item_description}</p>}
                                    </Form.Group>
                                </Form>
                            </Modal.Body>
                            <Modal.Footer>
                                <Button
                                    onClick={() => {
                                        setShowEditModal(false);
                                        setEditItemProductForm(emptyEditItemProductForm);
                                        setErrorsEditForm({});
                                    }}
                                    appearance="subtle"
                                >
                                    Batal
                                </Button>
                                <Button
                                    onClick={() => {
                                        HandleEditItemProductApi();
                                    }}
                                    appearance="primary"
                                    type="submit"
                                    loading={loading}
                                    disabled={loading}
                                >
                                    Simpan
                                </Button>
                                {loading && <Loader backdrop size="md" vertical content="Editing Data..." active={loading} />}
                            </Modal.Footer>
                        </Modal>
                    </div>
                </div>
            </ContainerLayout>
        </div>
    );

}
