import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/category${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function LkvMasterCategory(){
    return{
        getAllMasterCategory: createApiFunction("get", "/get/all"),
        getAllMasterCategoryActive : createApiFunction("get", "/get/all/active"),
        createMasterCategory: createApiFunction("post", "/create"),
        updateMasterCategory: createApiFunction("put", "/edit"),
        updateStatusMasterCategory: createApiFunction("put", "/active")
    }
}