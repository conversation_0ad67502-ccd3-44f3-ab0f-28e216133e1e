import { useEffect, useState } from "react";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Icon<PERSON>utton,
  Stack,
  Breadcrumb,
  Tag,
  Panel,
  Table,
  InputGroup,
  Input,
  Pagination,
  Button,
  Form,
  useToaster,
  Modal,
  SelectPicker,
} from "rsuite";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import API_MasterDataMasterDataMappingReagen from "@/pages/api/master_data/api_masterdata_master_data_mapping_reagen";
import API_MasterDataMasterDataReagenCriteria from "@/pages/api/master_data/api_masterdata_master_data_reagen_criteria";
import API_MasterDataMasterDataReagenDimension from "@/pages/api/master_data/api_masterdata_master_data_reagen_dimension";
import API_MasterDataMasterDataReagenCategory from "@/pages/api/master_data/api_masterdata_master_data_reagen_category";
import API_MasterDataMasterDataReagenType from "@/pages/api/master_data/api_masterdata_master_data_reagen_type";
import API_MasterDataReagenManufacture from "@/pages/api/master_data/api_reagen_manufacture";

export default function MasterDataMasterDataMappingReagenPage() {
  const [moduleName, setModuleName] = useState("");
  const [reagenDataMapping, setReagenDataMapping] = useState([]);
  const [props, setProps] = useState([]);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [sortColumn, setSortColumn] = useState();
  const [sortMapping, setSortMapping] = useState();

  const { HeaderCell, Cell, Column } = Table;

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const [formErrors, setFormErrors] = useState({});

  const toaster = useToaster();

  const [mapCriteria, setMapCriteria] = useState([]);
  const [mapDimension, setMapDimension] = useState([]);
  const [mapCategory, setMapCategory] = useState([]);
  const [mapType, setMapType] = useState([]);
  const [mapManufacture, setMapManufacture] = useState([]);

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortMapping) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortMapping(sortMapping);
    }, 500);
  };

  const errorMessageStyle = {
    color: "red",
    fontSize: "12px",
    marginTop: "5px",
  };

  const handleSubmit = (apiFunction) => {
    const requiredFields = [
      "id_type",
      "reagen_name",
      "cas_no",
    ];

    const errors = {};

    // Check other required fields
    requiredFields.forEach((field) => {
      if (!formValue[field]) {
        errors[field] = `${field === "msds"
            ? "MSDS"
            : field
              .split("_")
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(" ")
          } is required.`;
      }
    });

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setFormErrors({});
    apiFunction();
  };

  const handleGetAllApi = async () => {
    try {
      const reagenDataMapping =
        await API_MasterDataMasterDataMappingReagen().getAll();
      console.log(reagenDataMapping);

      setReagenDataMapping(reagenDataMapping.data || []);
    } catch (error) {
      setReagenDataMapping([]);
    }
  };

  const emptyFormValue = {
    id_criteria: null,
    id_dimension: null,
    id_category: null,
    id_type: null,
    id_manufacture: 0,
    item_list_no: null,
    reagen_name: null,
    catalogue: null,
    msds: null,
    cas_no: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const handleEditMapping = async (selectedIdMappingReagen) => {
    try {
      const result = await API_MasterDataMasterDataMappingReagen().edit({
        ...formValue,
        id_criteria_reagen: selectedIdMappingReagen,
        updated_by: props.employee_id,
        updated_name: props.employee_id + "-" + props.employee_name,
      });

      if (result.status === 200) {
        handleGetAllApi();
        setShowEditModal(false);

        toaster.push(
          Messages("success", "Success editing Mapping Reagen Data!"),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
      } else if (result.status === 400) {
        toaster.push(
          Messages(
            "error",
            `Error: "${result.message}". Please try again later!`
          ),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
      }
    } catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const filteredData = reagenDataMapping
    .filter((rowData, i) => {
      const searchFields = [
        "id_criteria_reagen",
        "id_criteria",
        "id_dimension",
        "id_category",
        "id_type",
        "id_manufacture",
        "criteria_desc",
        "dimension_desc",
        "category_desc",
        "type_desc",
        "manufacture_desc",
        "item_list_no",
        "reagen_name",
        "catalogue",
        "msds",
        "cas_no",
        "created_dt",
        "created_by",
        "updated_dt",
        "updated_by",
        "deleted_dt",
        "deleted_by",
        "is_active",
      ];

      const matchesSearch = searchFields.some((field) =>
        rowData[field]
          ?.toString()
          .toLowerCase()
          .includes(searchKeyword.toLowerCase())
      );

      return matchesSearch;
    })

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  }

  const totalRowCount = searchKeyword
    ? filteredData.length
    : reagenDataMapping.length;

  const handleExportExcel = () => {
    try {
      const headerMapping = {
        id_criteria_reagen: "Id Reagen",
        id_criteria: "Id Criteria",
        id_dimension: "Id Dimension",
        id_category: "Id Category",
        id_type: "Type",
        id_manufacture: "Id Manufacture",
        criteria_desc: "Criteria Description",
        dimension_desc: "Dimension Description",
        category_desc: "Category Description",
        type_desc: "Type Description",
        manufacture_desc: "Manufacture Description",
        item_list_no: "Item List No",
        reagen_name: "Reagen Name",
        catalogue: "Catalogue",
        msds: "MSDS",
        cas_no: "CAS No",
        created_dt: "Created Date",
        created_by: "Created By",
        updated_dt: "Updated Date",
        updated_by: "Updated By",
        deleted_dt: "Deleted Date",
        deleted_by: "Deleted By",
        is_active: "Is Active",
      };

      const formattedData = reagenDataMapping.map((item) => {
        const formattedItem = {};
        for (const key in item) {
          if (headerMapping[key]) {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
        return formattedItem;
      });

      const ws = XLSX.utils.json_to_sheet(formattedData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Reagen Data");

      const date = dateFormatterDash(new Date());
      XLSX.writeFile(wb, `MappingReagenData ${date}.xlsx`);

      toaster.push(
        Messages(
          "success",
          "Mapping Reagen data file (.xlsx) downloaded successfully"
        ),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    } catch (error) {
      console.error("Error exporting Excel:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const getData = () => {
    if (sortColumn && sortMapping) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortMapping === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const handleAddReagen = async () => {
    try {
      const result = await API_MasterDataMasterDataMappingReagen().add({
        ...formValue,
        created_by: props.employee_id,
        created_name: props.employee_id + "-" + props.employee_name,
      });

      if (result.status === 200) {
        handleGetAllApi();
        setShowAddModal(false);

        toaster.push(
          Messages("success", "Success adding Master Data Mapping Reagen!"),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
        setFormErrors({});
      } else if (result.status === 400) {
        toaster.push(
          Messages(
            "error",
            `Error: "${result.message}". Please try again later!`
          ),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
        setFormErrors({});
      }
    } catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const [deleteFormValue, setDeleteFormValue] = useState({
    id_criteria_reagen: null,
    deleted_by: null,
    reason: "",
    reagen_name:null,
  });

  const handleEditMappingReagen = async (selectedIdMappingReagen) => {
    console.log("selected id", selectedIdMappingReagen);
    await API_MasterDataMasterDataMappingReagen().editStatus({
      id_criteria_reagen: selectedIdMappingReagen,
      deleted_by: props.employee_id,
      reason: deleteFormValue.reason
    });
    handleGetAllApi();
    setShowDeleteModal(false);
    setDeleteFormValue({});
  };

  useEffect(() => {
    const fetchData = async () => {
      const res_map_criteria =
        await API_MasterDataMasterDataReagenCriteria().getAllActive();
      setMapCriteria(res_map_criteria.data || []);
      console.log("res_map_criteria", res_map_criteria);

      const res_map_dimension =
        await API_MasterDataMasterDataReagenDimension().getAllActive();
      setMapDimension(res_map_dimension.data || []);
      console.log("res_map_dimension", res_map_dimension);

      const res_map_category =
        await API_MasterDataMasterDataReagenCategory().getAllActive();
      setMapCategory(res_map_category.data || []);
      console.log("res_map_category", res_map_category);

      const res_map_type = await API_MasterDataMasterDataReagenType().getAllActive();
      setMapType(res_map_type.data || []);
      console.log("res_map_type", res_map_type);

      const res_map_manufacture =
        await API_MasterDataReagenManufacture().getAllActive();
      setMapManufacture(res_map_manufacture.data || []);
      console.log("res_map_manufacture", res_map_manufacture);
    };

    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("master_data/master_data_mapping_reagen")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetAllApi();
      fetchData();
    }
  }, []);

  return (
    <>
      <div>
        <Head>
          <title>Master Data Mapping Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>Mapping Reagen</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusRoundIcon />}
                    appearance="primary"
                    onClick={() => {
                      setShowAddModal(true);
                      setFormErrors({});
                    }}
                  >
                    Add
                  </IconButton>

                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getData(), limit, page)}
              sortColumn={sortColumn}
              sortMapping={sortMapping}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={150} align="center" sortable resizable>
                <HeaderCell>ID Criteria Reagen</HeaderCell>
                <Cell dataKey="id_criteria_reagen" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Reagen Name</HeaderCell>
                <Cell dataKey="reagen_name" />
              </Column>
              {/* <Column width={70} align="center" sortable>
                <HeaderCell>ID Criteria</HeaderCell>
                <Cell dataKey="id_criteria" />
              </Column> */}
              <Column width={150} sortable resizable>
                <HeaderCell>Criteria Description</HeaderCell>
                <Cell dataKey="criteria_desc" />
              </Column>
              {/* <Column width={70} align="center" sortable>
                <HeaderCell>ID Dimension</HeaderCell>
                <Cell dataKey="id_dimension" />
              </Column> */}
              <Column width={170} sortable resizable>
                <HeaderCell>Dimension Description</HeaderCell>
                <Cell dataKey="dimension_desc" />
              </Column>
              {/* <Column width={70} align="center" sortable>
                <HeaderCell>ID Category</HeaderCell>
                <Cell dataKey="id_category" />
              </Column> */}
              <Column width={160} sortable resizable>
                <HeaderCell>Category Description</HeaderCell>
                <Cell dataKey="category_desc" />
              </Column>
              {/* <Column width={70} align="center" sortable>
                <HeaderCell>ID Type</HeaderCell>
                <Cell dataKey="id_type" />
              </Column> */}
              <Column width={150} sortable resizable>
                <HeaderCell>Type Description</HeaderCell>
                <Cell dataKey="type_desc" />
              </Column>
              {/* <Column width={70} align="center" sortable>
                <HeaderCell>ID Manufacture</HeaderCell>
                <Cell dataKey="id_manufacture" />
              </Column> */}
              <Column width={180} sortable resizable>
                <HeaderCell>Manufacture Description</HeaderCell>
                <Cell dataKey="manufacture_desc" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Item List No</HeaderCell>
                <Cell dataKey="item_list_no" />
              </Column>             
              <Column width={150} sortable resizable>
                <HeaderCell>Catalogue</HeaderCell>
                <Cell dataKey="catalogue" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>MSDS</HeaderCell>
                <Cell dataKey="msds" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>CAS No</HeaderCell>
                <Cell dataKey="cas_no" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Created Date</HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Created By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.created_by} - {rowData.created_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Updated Date</HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Updated By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.updated_by} - {rowData.updated_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Deleted Date</HeaderCell>
                <Cell dataKey="deleted_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Deleted By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.deleted_by} - {rowData.deleted_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={100} sortable resizable>
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={100} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell style={{ padding: "8px" }}>
                  {(rowData) => (
                    <div>
                      <Button
                        appearance="link"
                        disabled={rowData.is_active === 0}
                        onClick={() => {
                          setFormErrors({});
                          setShowEditModal(true);
                          setFormValue(rowData);
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        appearance="subtle"
                        onClick={() =>
                          {
                            if (rowData.is_active === 1) {
                              setShowDeleteModal(true)
                              setDeleteFormValue({
                                  ...deleteFormValue,
                                  id_criteria_reagen: rowData.id_criteria_reagen,
                                  deleted_by: props.employee_id,
                                  reason:"",
                                  reagen_name: rowData.reagen_name
                              })
                            }else{
                              handleEditMappingReagen(rowData.id_criteria_reagen)
                            }                            
                          }
                        }
                      >
                        {rowData.is_active === 1 ? (
                          <TrashIcon style={{ fontSize: "16px" }} />
                        ) : (
                          <ReloadIcon style={{ fontSize: "16px" }} />
                        )}
                      </Button>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Add Modal Add Mapping Reagen Data */}
        <Modal
          backdrop="static"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Add Master Mapping Reagen Data</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Criteria Description</Form.ControlLabel>
                <SelectPicker
                  name="id_criteria"
                  value={formValue.id_criteria}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_criteria: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      id_criteria: null,
                    }));
                  }}
                  block
                  data={mapCriteria}
                  labelKey="criteria_desc"
                  valueKey="id_criteria"
                />
                {formErrors.id_criteria && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.id_criteria}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Dimension Description</Form.ControlLabel>
                <SelectPicker
                  name="id_dimension"
                  value={formValue.id_dimension}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_dimension: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      id_dimension: undefined,
                    }));
                  }}
                  block
                  data={mapDimension}
                  labelKey="dimension_desc"
                  valueKey="id_dimension"
                />
                {formErrors.id_dimension && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.id_dimension}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Category Description</Form.ControlLabel>
                <SelectPicker
                  name="id_category"
                  value={formValue.id_category}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_category: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      id_category: undefined,
                    }));
                  }}
                  block
                  data={mapCategory}
                  labelKey="category_desc"
                  valueKey="id_category"
                />
                {formErrors.id_category && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.id_category}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Type Description</Form.ControlLabel>
                <SelectPicker
                  name="id_type"
                  value={formValue.id_type}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_type: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      id_type: undefined,
                    }));
                  }}
                  block
                  data={mapType}
                  labelKey="type_desc"
                  valueKey="id_type"
                />
                {formErrors.id_type && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.id_type}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Manufacture Description</Form.ControlLabel>
                <SelectPicker
                  name="id_manufacture"
                  value={formValue.id_manufacture}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_manufacture: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      id_manufacture: null,
                    }));
                  }}
                  block
                  data={mapManufacture}
                  labelKey="manufacture_desc"
                  valueKey="id_manufacture"
                />
                {formErrors.id_manufacture && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.id_manufacture}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Item List No</Form.ControlLabel>
                <Form.Control
                  name="item_list_no"
                  value={formValue.item_list_no}
                  onChange={(value) => {
                    setFormValue({ ...formValue, item_list_no: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      item_list_no: undefined,
                    }));
                  }}
                />
                {formErrors.item_list_no && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.item_list_no}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Reagen Name</Form.ControlLabel>
                <Form.Control
                  name="reagen_name"
                  value={formValue.reagen_name}
                  onChange={(value) => {
                    setFormValue({ ...formValue, reagen_name: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      reagen_name: undefined,
                    }));
                  }}
                />
                {formErrors.reagen_name && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.reagen_name}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Catalogue</Form.ControlLabel>
                <Form.Control
                  name="catalogue"
                  value={formValue.catalogue}
                  onChange={(value) => {
                    setFormValue({ ...formValue, catalogue: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      catalogue: undefined,
                    }));
                  }}
                />
                {formErrors.catalogue && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.catalogue}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>MSDS</Form.ControlLabel>
                <Form.Control
                  name="msds"
                  value={formValue.msds}
                  onChange={(value) => {
                    setFormValue({ ...formValue, msds: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      msds: undefined,
                    }));
                  }}
                />
                {formErrors.msds && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.msds}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>CAS No</Form.ControlLabel>
                <Form.Control
                  name="cas_no"
                  value={formValue.cas_no}
                  onChange={(value) => {
                    setFormValue({ ...formValue, cas_no: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      cas_no: undefined,
                    }));
                  }}
                />
                {formErrors.cas_no && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.cas_no}
                  </div>
                )}
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                // Clear form errors immediately
                setFormErrors({});

                setShowAddModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>

            <Button
              onClick={() => {
                handleSubmit(handleAddReagen);
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        {/* EDIT REAGEN TYPE */}
        <Modal
          backdrop="static"
          open={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Edit Mapping Reagen Type</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>

              <Form.Group>
                <Form.ControlLabel>Category Description</Form.ControlLabel>
                <SelectPicker
                  name="id_category"
                  value={formValue.id_category}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_category: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      id_category: undefined,
                    }));
                  }}
                  block
                  data={mapCategory}
                  labelKey="category_desc"
                  valueKey="id_category"
                />
                {formErrors.id_category && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.id_category}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Type Description</Form.ControlLabel>
                <SelectPicker
                  name="id_type"
                  value={formValue.id_type}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_type: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      id_type: undefined,
                    }));
                  }}
                  block
                  data={mapType}
                  labelKey="type_desc"
                  valueKey="id_type"
                />
                {formErrors.id_type && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.id_type}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Manufacture Description</Form.ControlLabel>
                <SelectPicker
                  name="id_manufacture"
                  value={formValue.id_manufacture}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_manufacture: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      id_manufacture: null,
                    }));
                  }}
                  block
                  data={mapManufacture}
                  labelKey="manufacture_desc"
                  valueKey="id_manufacture"
                />
                {formErrors.id_manufacture && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.id_manufacture}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Item List No.</Form.ControlLabel>
                <Form.Control
                  name="item_list_no"
                  value={formValue.item_list_no}
                  onChange={(value) => {
                    setFormValue({ ...formValue, item_list_no: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      item_list_no: undefined,
                    }));
                  }}
                />
                {formErrors.item_list_no && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.item_list_no}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Reagen Name</Form.ControlLabel>
                <Form.Control
                  name="reagen_name"
                  value={formValue.reagen_name}
                  onChange={(value) => {
                    setFormValue({ ...formValue, reagen_name: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      reagen_name: undefined,
                    }));
                  }}
                />
                {formErrors.reagen_name && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.reagen_name}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Catalogue</Form.ControlLabel>
                <Form.Control
                  name="catalogue"
                  value={formValue.catalogue}
                  onChange={(value) => {
                    setFormValue({ ...formValue, catalogue: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      catalogue: undefined,
                    }));
                  }}
                />
                {formErrors.catalogue && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.catalogue}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>MSDS</Form.ControlLabel>
                <Form.Control
                  name="msds"
                  value={formValue.msds}
                  onChange={(value) => {
                    setFormValue({ ...formValue, msds: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      msds: undefined,
                    }));
                  }}
                />
                {formErrors.msds && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.msds}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>CAS No</Form.ControlLabel>
                <Form.Control
                  name="cas_no"
                  value={formValue.cas_no}
                  onChange={(value) => {
                    setFormValue({ ...formValue, cas_no: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      cas_no: undefined,
                    }));
                  }}
                />
                {formErrors.cas_no && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.cas_no}
                  </div>
                )}
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setFormErrors({});
                setShowEditModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setFormErrors({});
                handleSubmit(() =>
                  handleEditMapping(formValue.id_criteria_reagen)
                );
              }}
              appearance="primary"
              type="submit"
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>

        <Modal
          backdrop="static"
          open={showDeleteModal}
          onClose={() => {
              setShowDeleteModal(false);
              setDeleteFormValue({});
          }}
          overflow={false}
          >
          <Modal.Header>
              <Modal.Title>Delete Master Reagen {deleteFormValue.reagen_name}</Modal.Title>
          </Modal.Header>
          <Modal.Body>
              <Form fluid>
              <Form.Group>
                  <Form.ControlLabel>Reason</Form.ControlLabel>
                  <Form.Control
                  name="reason"
                  value={deleteFormValue.reason}
                  onChange={(value) => {
                      setDeleteFormValue((prevFormValue) => ({
                      ...prevFormValue,
                      reason: value,
                      }));
                  }}
                  />
              </Form.Group>
              </Form>
          </Modal.Body>
          <Modal.Footer>
              <Button
              onClick={() => {
                  setShowDeleteModal(false);
                  setDeleteFormValue({});
              }}
              appearance="subtle"
              >
              Cancel
              </Button>

              <Button
              onClick={() => {
                  handleEditMappingReagen(deleteFormValue.id_criteria_reagen);
              }}
              appearance="primary"
              type="submit"
              >
              Add
              </Button>
          </Modal.Footer>
      </Modal>
      </ContainerLayout>
    </>
  );
}
