import ContainerLayout from '@/components/layout/ContainerLayout'
import Head from 'next/head'
import React, { useEffect, useState } from 'react'
import { <PERSON>readcrumb, Button, Form, IconButton, Input, InputGroup, Modal, Pagination, Panel, Stack, Table, Tag, toaster } from 'rsuite'
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import EditIcon from '@rsuite/icons/Edit';
import API_ScmDashboard from "@/pages/api/scm/api_scm_dashboard";
import Messages from '@/components/Messages';

export default function ScmDashboardPage() {

    const [moduleName, setModuleName] = useState("");
    const [props, setProps] = useState([]);

    const { HeaderCell, Cell, Column } = Table;
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();

    const [masterScmDashboard, setMasterScmDashboard] = useState([]);

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showAddModal, setShowAddModal] = useState(false);
    const [showUpdateModal, setShowUpdateModal] = useState(false);

    const emptyForm = {
        table_name: null,
        module_name: null,
    }
    const [formValue, setFormValue] = useState(emptyForm);
    const emptyError = {
        table_name: null,
        module_name: null,
    }
    const [errors, setErrors] = useState(emptyError);

    const handleGetAllApi = async () => {
        const response = await API_ScmDashboard().getAll();

        if (response.status === 200) {
            setMasterScmDashboard(response.data ? response.data : []);
        }
    }

    const handleUpdateStatusApi = async (id_dashboard, is_active) => {
        await API_ScmDashboard().updateStatus({
            id_dashboard: id_dashboard,
            is_active: is_active,
            deleted_by: props.employee_id
        })
        handleGetAllApi();
    }

    const handleCreateApi = async () => {
        setIsSubmitting(true);
        try {
            const res = await API_ScmDashboard().create({
                ...formValue,
                created_by: props.employee_id,
            })

            if (res.status === 200) {
                toaster.push(
                    Messages("success", "Success: Data has been created!"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                )
                setShowAddModal(false);
                handleGetAllApi();
            }
        } catch (err) {
            console.log("Axios Error: ", err);
            toaster.push(
                Messages("error", "Error: Please try again later!"),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            )
        } finally {
            setIsSubmitting(false);
            setFormValue(emptyForm);
            setErrors(emptyError);
        }
    }

    const handleUpdateApi = async () => {
        setIsSubmitting(true);
        try {
            const res = await API_ScmDashboard().update({
                ...formValue,
                id_dashboard: formValue.id_dashboard,
                updated_by: props.employee_id,
            })

            if (res.status === 200) {
                toaster.push(
                    Messages("success", "Success: Data has been updated!"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                )
                setShowUpdateModal(false);
                handleGetAllApi();
            }
        } catch (err) {
            console.log("Axios Error: ", err);
            toaster.push(
                Messages("error", "Error: Please try again later!"),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            )
        } finally {
            setIsSubmitting(false);
            setFormValue(emptyForm);
            setErrors(emptyError);
        }
    }

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("scm/scm_dashboard")
            );
            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            handleGetAllApi();
        }
    }, []);

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = masterScmDashboard
        .filter((rowData, i) => {
            const searchFields = [
                "id_dashboard",
                "table_name",
                "module_name",
                "created_by",
                "created_dt",
                "updated_by",
                "updated_dt",
                "deleted_by",
                "deleted_dt",
                "is_active",
            ];

            const matchesSearch = searchFields.some((field) =>
                rowData[field]
                    ?.toString()
                    .toLowerCase()
                    .includes(searchKeyword.toLowerCase())
            );

            return matchesSearch;
        })

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    }

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : masterScmDashboard.length;

    const formatTableDate = (date) => {
        const dateObj = new Date(date);
        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, "0");
        const day = String(dateObj.getDate()).padStart(2, "0");
        return `${day}/${month}/${year}`;
    };

    return (
        <>
            <div>
                <Head>
                    <title>SCM Dashboard</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className='m-4 pt-2'>
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Quality</Breadcrumb.Item>
                                    <Breadcrumb.Item>SCM</Breadcrumb.Item>
                                    <Breadcrumb.Item active>SCM Dashboard</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                        header={
                            <Stack justifyContent='space-between'>
                                <IconButton
                                    icon={<PlusRoundIcon />}
                                    appearance="primary"
                                    onClick={() => {
                                        setShowAddModal(true);
                                    }}>
                                    Add
                                </IconButton>

                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="search"
                                        value={searchKeyword}
                                        onChange={handleSearch}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}
                                    >
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                        }
                    >
                        <Table
                            bordered
                            cellBordered
                            height={400}
                            data={getPaginatedData(getFilteredData(), limit, page)}
                            sortColumn={sortColumn}
                            sortType={sortType}
                            onSortColumn={handleSortColumn}
                        >
                            <Column width={70} align='center' fixed>
                                <HeaderCell>No</HeaderCell>
                                <Cell>
                                    {(rowData, index) => {
                                        return index + 1 + limit * (page - 1);
                                    }}
                                </Cell>
                            </Column>
                            <Column width={70} align='center'>
                                <HeaderCell>ID</HeaderCell>
                                <Cell dataKey='id_dashboard' />
                            </Column>
                            <Column width={200} sortable resizable>
                                <HeaderCell align='center'>Table Name</HeaderCell>
                                <Cell dataKey='table_name' />
                            </Column>
                            <Column width={200} sortable resizable>
                                <HeaderCell align='center'>Module Name</HeaderCell>
                                <Cell dataKey='module_name' />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell align='center'>Created By</HeaderCell>
                                <Cell dataKey='created_by' />
                            </Column>
                            <Column width={150} align='center' sortable resizable>
                                <HeaderCell>Created Date</HeaderCell>
                                <Cell>
                                    {(rowData) => (
                                        <span>{formatTableDate(rowData.created_dt)}</span>
                                    )}
                                </Cell>
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell align='center'>Updated By</HeaderCell>
                                <Cell dataKey='updated_by' />
                            </Column>
                            <Column width={150} align='center' sortable resizable>
                                <HeaderCell>Updated Date</HeaderCell>
                                <Cell dataKey='updated_dt' />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell align='center'>Deleted By</HeaderCell>
                                <Cell dataKey='deleted_by' />
                            </Column>
                            <Column width={150} align='center' sortable resizable>
                                <HeaderCell>Deleted Date</HeaderCell>
                                <Cell dataKey='deleted_dt' />
                            </Column>
                            <Column width={100} align='center' sortable resizable>
                                <HeaderCell>Status</HeaderCell>
                                <Cell>
                                    {(rowData) => (
                                        <span
                                            style={{
                                                color: rowData.is_active === 1 ? "green" : "red",
                                            }}
                                        >
                                            {rowData.is_active === 1 ? "Active" : "Inactive"}
                                        </span>
                                    )}
                                </Cell>
                            </Column>
                            <Column width={100} fixed="right" align="center">
                                <HeaderCell>Action</HeaderCell>
                                <Cell style={{ padding: "8px" }}>
                                    {(rowData) => (
                                        <div>
                                            <Button
                                                appearance="subtle"
                                                disabled={rowData.is_active === 0}
                                                onClick={() => {
                                                    setShowUpdateModal(true);
                                                    setFormValue(rowData);
                                                }}
                                            >
                                                <EditIcon />
                                            </Button>
                                            <Button
                                                appearance="subtle"
                                                onClick={() => { handleUpdateStatusApi(rowData.id_dashboard, rowData.is_active === 1 ? 0 : 1); }}
                                            >
                                                {rowData.is_active === 1 ? (
                                                    <TrashIcon style={{ fontSize: "16px" }} />
                                                ) : (
                                                    <ReloadIcon style={{ fontSize: "16px" }} />
                                                )}
                                            </Button>
                                        </div>
                                    )}
                                </Cell>
                            </Column>
                        </Table>
                        <div style={{ padding: 20 }}>
                            <Pagination
                                prev
                                next
                                first
                                last
                                ellipsis
                                boundaryLinks
                                maxButtons={5}
                                size="xs"
                                layout={["total", "-", "limit", "|", "pager", "skip"]}
                                limitOptions={[10, 30, 50]}
                                total={totalRowCount}
                                limit={limit}
                                activePage={page}
                                onChangePage={setPage}
                                onChangeLimit={handleChangeLimit}
                            />
                        </div>
                    </Panel>
                </div>

                {/* Add Modal */}
                <Modal
                    backdrop="static"
                    open={showAddModal}
                    overflow={false}
                    onClose={() => {
                        setShowAddModal(false);
                        setFormValue(emptyForm);
                        setErrors(emptyError);
                    }}
                >
                    <Modal.Header>
                        <Modal.Title>Add SCM Dashboard</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Table Name</Form.ControlLabel>
                                <Form.Control
                                    name='table_name'
                                    value={formValue.table_name}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, table_name: value });
                                        setErrors((prevErrors) => ({ ...prevErrors, table_name: null }));
                                    }}
                                    errorMessage={errors.table_name}
                                />
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Module Name</Form.ControlLabel>
                                <Form.Control
                                    name='module_name'
                                    value={formValue.module_name}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, module_name: value });
                                        setErrors((prevErrors) => ({ ...prevErrors, module_name: null }));
                                    }}
                                    errorMessage={errors.module_name}
                                />
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowAddModal(false);
                                setFormValue(emptyForm);
                                setErrors(emptyError);
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleCreateApi();
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Add
                        </Button>
                    </Modal.Footer>
                </Modal>

                {/* Update Modal */}
                <Modal
                    backdrop="static"
                    open={showUpdateModal}
                    overflow={false}
                    onClose={() => {
                        setShowUpdateModal(false);
                        setFormValue(emptyForm);
                        setErrors(emptyError);
                    }}
                >
                    <Modal.Header>
                        <Modal.Title>Update SCM Dashboard</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Table Name</Form.ControlLabel>
                                <Form.Control
                                    name='table_name'
                                    value={formValue.table_name}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, table_name: value });
                                        setErrors((prevErrors) => ({ ...prevErrors, table_name: null }));
                                    }}
                                    errorMessage={errors.table_name}
                                />
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Module Name</Form.ControlLabel>
                                <Form.Control
                                    name='module_name'
                                    value={formValue.module_name}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, module_name: value });
                                        setErrors((prevErrors) => ({ ...prevErrors, module_name: null }));
                                    }}
                                    errorMessage={errors.module_name}
                                />
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowUpdateModal(false);
                                setFormValue(emptyForm);
                                setErrors(emptyError);
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleUpdateApi(formValue.id_dashboard);
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Update
                        </Button>
                    </Modal.Footer>
                </Modal>
            </ContainerLayout>
        </>
    )
}
