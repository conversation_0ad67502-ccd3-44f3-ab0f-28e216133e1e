.notification-page {
  overflow: hidden;
  height: 100vh;
  &__nav {
    display: flex;
    align-items: center;
    background-color: black;
    padding: 10px;
    height: 8%;
    &-back {
      margin-right: 10px;
      cursor: pointer;
      font-size: 16px;
      align-items: center;
      border: 1px solid white;
      padding: 5px 10px;
      color: white;
      &:hover {
        background-color: white;
        color: black;
      }
    }
  }
}

.notifications {
  display: flex;
  font-family: "Arial", sans-serif;
  background: white;
  height: 92%;

  &__sidebar {
    width: 30%;
    padding: 20px;
    border-right: 1px solid #e8e8e8;
    position: relative;

    &__header {
      position: sticky;
      top: 0;
      background: white;
      z-index: 10;
      padding-bottom: 10px;
      border-bottom: 1px solid black;
    }

    &__list {
      overflow-y: auto;
      scrollbar-width: none;
      height: calc(100vh - 100px);
      padding-bottom: 100px;
    }

    &__title {
      margin-bottom: 10px;
      font-size: 18px;
      font-weight: bold;
      color: black;
    }

    // Style for the tabs in sidebar
    &__tabs {
      list-style: none;
      padding: 0;
      margin-top: 20px;
      display: flex;
      overflow-x: auto;

      &-item {
        padding: 8px 16px;
        border: 1px solid black;
        cursor: pointer;
        margin-right: 5px;
        &:hover {
          background-color: #e8e8e8;
        }

        &--active {
          background-color: black;
          color: white;
          font-weight: bold;
          &:hover {
            background-color: black;
          }
        }
      }
    }

    &__search {
      &-input {
        width: 100%;
        padding: 8px;
        border: 1px solid black;
        background-color: white;
      }
    }

    &__no-result {
      font-size: 16px;
      color: black;
      text-align: center;
      margin-top: 20px;
    }

    &__item {
      display: flex;
      align-items: center;
      padding: 10px;
      border-bottom: 1px solid black;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &-title {
        font-size: 16px;
        margin-bottom: 5px;
        font-weight: bold;
        color: black;
        text-transform: capitalize;
        &--read {
          font-weight: normal;
        }
      }

      &-date {
        font-size: 14px;
        color: black;
        font-weight: bold;
        &--read {
          font-weight: normal;
        }
      }

      &-preview {
        font-size: 14px;
        color: #999;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &--active {
        background-color: #e8e8e8;
      }

      &:hover {
        background-color: #e8e8e8;
      }
    }
  }

  &__main {
    width: 70%;
    padding: 50px;
    overflow-y: auto;
    font-size: 16px;

    &__header {
      display: flex;
      justify-content: space-between;
      align-items: first baseline;
      margin-bottom: 50px;
      padding-bottom: 15px;
      border-bottom: 1px solid black;
      &-title {
        width: 65%;
        font-size: 28px;
        font-weight: bold;
        color: black;
        text-transform: capitalize;
      }
      &-date {
        width: 30%;
        font-size: 16px;
        display: flex;
        justify-content: flex-end;
      }
    }
    &__sub-header {
      display: flex;
      justify-content: space-between;
      align-items: first baseline;
      margin-bottom: 50px;
      padding-bottom: 15px;
      &-left {
        width: 65%;
        &-module {
          margin-right: 10px;
          padding: 5px 10px;
          border: 1px solid #ccc;
          background-color: #e8e8e8;
          color: black;
        }
      }
      &-right {
        width: 30%;
        font-size: 16px;
        display: flex;
        justify-content: flex-end;
        &-goto {
          margin-left: 10px;
          padding: 5px 10px;
          color: white;
          background: black;
          cursor: pointer;
          &:hover {
            background-color: white;
            border: 1px solid black;
            color: black;
          }
        }
      }
    }
  }
}
