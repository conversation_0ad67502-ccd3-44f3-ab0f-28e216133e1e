import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";

import { <PERSON><PERSON>, Stack, Panel, ButtonGroup, Breadcrumb } from "rsuite";

import ContainerLayout from "@/components/layout/ContainerLayout";

import ApiTsApproval from "@/pages/api/ts/api_approval";

// Import Table
import TsReportAutomateLine from "./tsReportAutomateLine";
import TsReportManualLine from "./tsReportManualLine";

export default function TsReport() {
  const router = useRouter();

  const [moduleName, setModuleName] = useState("");

  const [props, setProps] = useState([]);

  const [canFetch, setCanFetch] = useState(false)

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    // const fetchData = async () => {
    //   const body = {
    //     employee_id: dataLogin.employee_id
    //   }
    //   const resultAutoLine = await ApiTsApproval().GetAllAutoSpv(body)
    //   const resultManualLine = await ApiTsApproval().GetAllManualSpv(body)

    //   console.log("[result auto parent]",resultAutoLine)
    //   //console.log("[result manual parent]",resultManualLine)
    //   setDataAutoLine(resultAutoLine.data || []);
    //   SetDataManualLine(resultManualLine.data || [])
    // };
    // fetchData();

    //nyalakan setelah coding
    
    // if (!dataLogin) {
    //   router.push(dataLogin ? "/dashboard" : "/");
    // } else {
    //   const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
    //     item.includes("masterdata/TsMainCategories")
    //   );

    //   if (validateUserAccess.length === 0) {
    //     router.push("/dashboard");
    //     return;
    //   }
    // }
    setCanFetch(true)
  }, []);

  const [step, setStep] = useState(0);

  return (
    <>
      <div>
        <Head>
          <title>TS Report</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item active>
              {moduleName ? moduleName : "User Module"}
            </Breadcrumb.Item>
          </Breadcrumb>
          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h3>TS Report</h3>
              <ButtonGroup>
                <Button
                  onClick={() => setStep(0)}
                  active={step === 0}
                  appearance="primary"
                >
                  TS Report Automate Line
                </Button>
                <Button
                  onClick={() => setStep(1)}
                  active={step === 1}
                  appearance="primary"
                >
                  TS Report Manual Line
                </Button>
              </ButtonGroup>
            </Stack>
          </Panel>
          <div>
            <Panel bordered>
              {step == 0 && canFetch ? (
                <TsReportAutomateLine employee_id={props.employee_id} />
              ) : step == 1 && canFetch  ? (
                <TsReportManualLine employee_id={props.employee_id}  />
              ) : null}
            </Panel>
          </div>
        </div>
      </ContainerLayout>
    </>
  );
}
