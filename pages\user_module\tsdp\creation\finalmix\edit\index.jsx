import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  InputGroup,
  Panel,
  Stack,
  Button,
  Form,
  useToaster,
  DatePicker,
  Uploader,
  Row,
  Col,
  RadioGroup,
  Radio,
  SelectPicker,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";

import { useRouter } from "next/router";
import Messages from "@/components/Messages";
import TrashIcon from "@rsuite/icons/Trash";
import ApiTsdpTH from "@/pages/api/tsdp/transaction_h/api_tsdp_transaction_h";
import ApiTsdpTDAttachments from "@/pages/api/tsdp/api_tsdp_td_attachments";
import ApiTsDetail from "@/pages/api/ts/api_ts_detail";
import ApiTsdpTDFinalMix from "@/pages/api/tsdp/api_tsdp_td_final_mix";

export default function EditFinalMix() {
  const router = useRouter();
  const toaster = useToaster();
  const [headerData, setHeaderData] = useState([]);
  const [formAttachments, setFormAttachments] = useState([]);
  const [idDetailTrans, setIdDetailTrans] = useState(0);
  const [sessionAuth, setSessionAuth] = useState(null);
  const [moduleName, setModuleName] = useState("");
  const [errorsFinalmixForm, setErrorsFinalmixForm] = useState({});
  const [finalMix, setFinalMix] = useState({});
  const [dataSpv, setDataSpv] = useState([]);
  const [dataDetail, setDataDetail] = useState([]);

  const fetchDropdownData = async () => {
    try {
      const resSpv = await ApiTsDetail().getAllActiveSpv();
      setDataSpv(resSpv.data || []);
      const resAllDetail = await ApiTsDetail().getAllDetail(); // Fetch data for the modal dropdown
      setDataDetail(resAllDetail.data ? resAllDetail.data : []);
    } catch (error) {
      console.error("Error fetching dropdown data:", error);
    }
  };

  const getDetailApiData = async (type) => {
    const res = await ApiTsDetail().getApiData({
      type_api: type,
    });

    if (res.status === 200) {
      setFormAddDetail((prevFormDetail) => {
        const existingDetail = prevFormDetail || [];
        const currentState = "Finalmix";
        const typeExists = existingDetail.some(
          (detail) => detail.type === currentState
        );

        return typeExists
          ? existingDetail
          : [
              ...existingDetail,
              {
                id_header_trans: finalMix.id_header_trans,
                type: currentState,
                data: res.data,
              },
            ];
      });
      setShowDetailModal(false);
    }
  };

  useEffect(() => {
    console.log("data spv = ", dataSpv);
  }, [dataSpv]);

  const handleUpdateFinalmixData = async () => {
    try {
      const errors = {};
      const requiredFields = [
        "final_mix_date",
        "final_mix_time_mix_1",
        "final_mix_time_mix_2",
        "ts_conclusion",
        "ts_followup",
        "bobot_granul",
        "bobot_teoritis",
        "rendemen",
        "spv_employee_id",
      ];
      for (const field of requiredFields) {
        if (
          !finalMix[field] ||
          (typeof finalMix[field] === "string" && finalMix[field].trim() === "")
        ) {
          errors[field] = `Kolom ${field.replace(/_/g, " ")} wajib diisi!`;
        }
      }

      if (Object.keys(errors).length > 0) {
        setErrorsFinalmixForm(errors);
        toaster.push(
          Messages("error", "Mohon isi semua kolom yang wajib diisi!"),
          { placement: "topCenter", duration: 3000 }
        );
        return;
      }

      const mainUpdatePayload = {
        final_mix_date: finalMix.final_mix_date,
        final_mix_time_mix_1: parseFloat(finalMix.final_mix_time_mix_1),
        final_mix_time_mix_2: parseFloat(finalMix.final_mix_time_mix_2),
        ts_conclusion: finalMix.ts_conclusion,
        ts_followup: finalMix.ts_followup,
        bobot_granul: parseFloat(finalMix.bobot_granul),
        bobot_teoritis: parseFloat(finalMix.bobot_teoritis),
        rendemen: parseFloat(finalMix.rendemen),
        spv_employee_id: finalMix.spv_employee_id,
        updated_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
        revised_remarks: finalMix.revised_remarks,
        id_detail_trans: parseInt(idDetailTrans),
      };

      const mainUpdateRes =
        await ApiTsdpTDFinalMix().UpdateTsTransactionFinalMix(
          mainUpdatePayload
        );

      if (mainUpdateRes.status !== 200) {
        toaster.push(
          Messages(
            "error",
            `Error saving finalmix data: ${mainUpdateRes.message}`
          ),
          { placement: "topCenter", duration: 3000 }
        );
        return;
      }
      let failedUploads = 0;
      let failedStatusUpdates = 0;

      // --- UPDATE STATUS OF EXISTING ATTACHMENTS ---
      const originalAttachments = await ApiTsdpTDAttachments()
        .getTsTransactionById({
          id_detail_trans: parseInt(finalMix.id_detail_trans),
        })
        .then((res) => res.data || []);

      for (const updatedAttachment of formAttachments) {
        // Find the original state of this attachment
        const originalAttachment = originalAttachments.find(
          (orig) => orig.id_attachments === updatedAttachment.id_attachments
        );

        console.log(
          "originalAttachment status = ",
          originalAttachment?.is_active
        );
        console.log(
          "updatedAttachment status = ",
          updatedAttachment?.is_active
        );

        // if the updatedAttachment's status changed
        if (updatedAttachment.is_active === 0) {
          const payload = {
            deleted_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
            id_attachments: updatedAttachment.id_attachments,
          };

          // deactivated
          console.log(
            `Deactivating attachment ID: ${updatedAttachment.id_attachments}`
          );
          const statusRes = await ApiTsdpTDAttachments().softDeleteAttachment(
            payload
          );

          if (statusRes.status !== 200) {
            failedStatusUpdates++;
          }
        }
      }
      // --- UPLOAD NEW ATTACHMENTS ---
      const newAttachments = formAttachments.filter(
        (file) => file.data !== undefined
      );
      console.log(`Found ${newAttachments.length} new attachments to upload.`);
      for (const fileItem of newAttachments) {
        console.log("masuk sini");
        const formData = new FormData();
        formData.append("Files", fileItem.data.blobFile, fileItem.data.name);
        formData.append("id_header_trans", headerData.id_header_trans);
        formData.append("type", "Finalmix");
        formData.append("path", "tsdp");
        formData.append("created_by", sessionAuth.employee_id);

        const postRes = await ApiTsdpTDAttachments().postTsTransactionUpload(
          formData
        );

        if (postRes.status !== 200) {
          failedUploads++;
        }
      }

      if (failedUploads > 0) {
        toaster.push(
          Messages("error", `${failedUploads} attachments failed to upload.`),
          { duration: 5000 }
        );
      } else {
        toaster.push(
          Messages(
            "success",
            "Finalmix data and attachments saved successfully!"
          ),
          { duration: 3000 }
        );
        router.push(`/user_module/tsdp/creation/finalmix/list`);
      }
    } catch (error) {
      console.error("Error updating finalmix data: ", error);
      toaster.push(Messages("error", "An unexpected error occurred."), {
        placement: "topCenter",
        duration: 3000,
      });
    }
  };

  const fetchAttachmentDataByID = async (finalmixId) => {
    try {
      const res = await ApiTsdpTDAttachments().getTsTransactionById({
        id_header_trans: parseInt(finalmixId),
        type: "Finalmix",
      });

      if (res.status === 200) {
        setFormAttachments(res.data);
      } else {
        console.error("Error fetching binder data: ", res.message);
      }
    } catch (error) {
      console.log("Error fetching binder data : ", error);
    }
  };

  const fetchAllPageData = async (finalmixId) => {
    try {
      const siftingRes =
        await ApiTsdpTDFinalMix().getAllTsTransactionFinalMixById({
          id_detail_trans: parseInt(finalmixId),
        });

      console.log("siftingRes = ", siftingRes.status);

      if (siftingRes.status !== 200) {
        toaster.push(Messages("error", "Failed to fetch finalmix record."));
        return;
      }

      const fetchedSiftingData = siftingRes.data;
      console.log("fetchedSiftingData = ", fetchedSiftingData);

      console.log("spv = ", fetchedSiftingData.spv_employee_id);
      setFinalMix(fetchedSiftingData);
      fetchAttachmentDataByID(siftingRes.data.id_header_trans);

      const headerRes = await ApiTsdpTH().getTsTransHeaderById({
        id_header_trans: parseInt(fetchedSiftingData.id_header_trans),
      });

      if (headerRes.status !== 200) {
        toaster.push(Messages("error", "Failed to fetch header record."));
        return;
      }
      const fetchedHeaderData = headerRes.data;
      console.log("dryingdetail = ", fetchedSiftingData.sifting_detail);
      setHeaderData(fetchedHeaderData);
    } catch (error) {
      console.error("Error during initial data fetch:", error);
      toaster.push(
        Messages("error", "An error occurred while fetching page data.")
      );
    }
  };

  useEffect(() => {
    console.log("formattachments =", formAttachments);
  }, [formAttachments]);

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push("/");
    }
  }, []);

  useEffect(() => {
    console.log("header data = ", headerData);
  }, [headerData]);

  useEffect(() => {
    if (router.isReady) {
      console.log("router is ready", router.query);

      const finalmixDataId = router.query.data;
      console.log("finalMix = ", finalmixDataId);

      if (finalmixDataId) {
        fetchAllPageData(finalmixDataId);
        setIdDetailTrans(finalmixDataId);
      }
      fetchDropdownData();
    }
  }, [router.isReady, router.query]);

  const handleUploadAttachments = (file) => {
    setFormAttachments((prevAttachments) => [
      ...prevAttachments,
      {
        type: "Finalmix",
        data: file,
      },
    ]);
  };

  const handleRemoveAttachments = (fileKey) => {
    setFormAttachments((prevAttachments) =>
      prevAttachments.filter(
        (attachment) => attachment?.data?.fileKey !== fileKey
      )
    );
  };

  const handleActivateDeactivate = (idAttachment, isActive) => {
    setFormAttachments((prevAttachments) =>
      prevAttachments.map((attachment) => {
        if (attachment.id_attachments === idAttachment) {
          return { ...attachment, is_active: isActive ? 0 : 1 };
        }
        return attachment;
      })
    );
  };

  const getAttachmentsForDisplay = () => {
    const attachments = formAttachments || [];

    const formatFileSize = (bytes) => {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    return attachments.map((attachment) => {
      if (attachment.id_attachments) {
        const fileName = attachment.path.substring(
          attachment.path.lastIndexOf("/") + 1
        );
        return {
          name: fileName,
          url: `${process.env.NEXT_PUBLIC_STORAGE}/${attachment.path}`,
          id_attachments: attachment.id_attachments,
          is_active: attachment.is_active,
          size: null,
        };
      } else {
        return {
          name: attachment.data.name,
          url: URL.createObjectURL(attachment.data.blobFile),
          flag: "new",
          fileKey: attachment.data.fileKey,
          size: formatFileSize(attachment.data.blobFile.size),
        };
      }
    });
  };

  const attachmentsToDisplay = getAttachmentsForDisplay();

  const AttachmentDisplay = ({ attachment }) => (
    <Panel
      bordered
      className="mt-3 flex flex-col"
      style={{ width: 230, height: 350, position: "relative" }}
      shaded>
      <span style={{ flexGrow: 1 }}>
        <img
          src={attachment.url}
          alt={attachment.name}
          style={{
            maxWidth: 200,
            maxHeight: 250,
            objectFit: "contain",
            objectPosition: "center",
            display: "block",
            margin: "0 auto",
          }}
        />
        <p
          className="word-break mt-1"
          style={{ maxHeight: 20, overflow: "hidden" }}>
          File Name : {attachment.name}
        </p>
        {attachment.size && (
          <p style={{ color: "grey", fontSize: "12px", marginTop: "4px" }}>
            File Size : {attachment.size}
          </p>
        )}
      </span>
      {attachment.flag === "new" ? (
        <div style={{ position: "absolute", bottom: 10, right: 10 }}>
          <Button
            appearance="ghost"
            color="red"
            onClick={() => handleRemoveAttachments(attachment.fileKey)}>
            <TrashIcon />
          </Button>
        </div>
      ) : (
        <div style={{ position: "absolute", bottom: 10, left: 10, right: 10 }}>
          <Button
            appearance="ghost"
            size="sm"
            color={attachment.is_active === 0 ? "green" : "red"}
            onClick={() =>
              handleActivateDeactivate(
                attachment.id_attachments,
                attachment.is_active
              )
            }
            block>
            {attachment.is_active === 0 ? "Activate" : "Deactivate"}
          </Button>
        </div>
      )}
    </Panel>
  );

  return (
    <div>
      <Head>
        <title>TSDP Creation Finalmix Edit</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item>TSDP</Breadcrumb.Item>
            <Breadcrumb.Item>Creation</Breadcrumb.Item>
            <Breadcrumb.Item>Finalmix</Breadcrumb.Item>
            <Breadcrumb.Item active>Edit</Breadcrumb.Item>
          </Breadcrumb>

          <Panel bordered className="mb-2 mt-4">
            <Stack justifyContent="flex-start">
              <h3>TS Edit Finalmix</h3>
            </Stack>
            <hr className="my-4" />
            <hr className="my-4" />

            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Kode Batch</Form.ControlLabel>
                <Form.Control
                  name="batch_no"
                  value={headerData.batch_no || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Line Type</Form.ControlLabel>
                <RadioGroup
                  name="line_type"
                  value={headerData.line_type ?? "-"}
                  readOnly
                  disabled>
                  <Radio value={1}>Automate</Radio>
                  <Radio value={0}>Manual</Radio>
                </RadioGroup>
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Line</Form.ControlLabel>
                <Form.Control
                  name="id_line"
                  value={headerData.id_line || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Jenis Sediaan</Form.ControlLabel>
                <Form.Control
                  name="sediaan_type"
                  value={headerData.sediaan_type || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                <Form.Control
                  name="product_code"
                  value={headerData.product_code || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Nama Produk</Form.ControlLabel>
                <Form.Control
                  name="product_name"
                  value={headerData.product_name || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Skala Produksi</Form.ControlLabel>
                <Form.Control
                  name="production_scale"
                  value={headerData.production_scale || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Fokus Trial</Form.ControlLabel>
                <Form.Control
                  name="trial_focus"
                  value={headerData.trial_focus || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>No PPI</Form.ControlLabel>
                <Form.Control
                  name="ppi_no"
                  value={headerData.ppi_no || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Tujuan Proses</Form.ControlLabel>
                <Form.Control
                  name="process_purpose"
                  value={headerData.process_purpose || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Background</Form.ControlLabel>
                <Form.Control
                  name="background"
                  value={headerData.background || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Tanggal Proses</Form.ControlLabel>
                <Form.Control
                  name="process_date"
                  value={headerData.process_date?.split("T")[0] || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Tanggal Final Mix <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  block
                  oneTap
                  value={
                    finalMix.final_mix_date
                      ? new Date(finalMix.final_mix_date)
                      : null
                  }
                  format="dd-MM-yyyy"
                  onChange={(value) => {
                    setFinalMix((prevFormValue) => ({
                      ...prevFormValue,
                      final_mix_date: value,
                    }));
                    setErrorsFinalmixForm((prevErrors) => ({
                      ...prevErrors,
                      final_mix_date: undefined,
                    }));
                  }}
                  name="final_mix_date"
                />
                {errorsFinalmixForm.final_mix_date && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsFinalmixForm.final_mix_date}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Waktu Aduk 2 <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="final_mix_time_mix_1"
                    placeholder="final_mix_time_mix_1"
                    value={finalMix.final_mix_time_mix_1 || ""}
                    onChange={(value) => {
                      setFinalMix((prevFormValue) => ({
                        ...prevFormValue,
                        final_mix_time_mix_1: value,
                      }));
                      setErrorsFinalmixForm((prevErrors) => ({
                        ...prevErrors,
                        final_mix_time_mix_1: undefined,
                      }));
                    }}
                    type="number"
                  />
                  <InputGroup.Addon>Menit</InputGroup.Addon>
                </InputGroup>
                {errorsFinalmixForm.final_mix_time_mix_1 && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsFinalmixForm.final_mix_time_mix_1}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Waktu Aduk 2 <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="final_mix_time_mix_2"
                    placeholder="final_mix_time_mix_2"
                    value={finalMix.final_mix_time_mix_2 || ""}
                    onChange={(value) => {
                      setFinalMix((prevFormValue) => ({
                        ...prevFormValue,
                        final_mix_time_mix_2: value,
                      }));
                      setErrorsFinalmixForm((prevErrors) => ({
                        ...prevErrors,
                        final_mix_time_mix_2: undefined,
                      }));
                    }}
                    type="number"
                  />
                  <InputGroup.Addon>Menit</InputGroup.Addon>
                </InputGroup>
                {errorsFinalmixForm.final_mix_time_mix_2 && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsFinalmixForm.final_mix_time_mix_2}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Kesimpulan<span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="ts_conclusion"
                    placeholder="ts_conclusion"
                    value={finalMix.ts_conclusion || ""}
                    onChange={(value) => {
                      setFinalMix((prevFormValue) => ({
                        ...prevFormValue,
                        ts_conclusion: value,
                      }));
                      setErrorsFinalmixForm((prevErrors) => ({
                        ...prevErrors,
                        ts_conclusion: undefined,
                      }));
                    }}
                  />
                </InputGroup>
                {errorsFinalmixForm.ts_conclusion && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsFinalmixForm.ts_conclusion}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Tindak Lanjut<span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="ts_followup"
                    placeholder="ts_followup"
                    value={finalMix.ts_followup || ""}
                    onChange={(value) => {
                      setFinalMix((prevFormValue) => ({
                        ...prevFormValue,
                        ts_followup: value,
                      }));
                      setErrorsFinalmixForm((prevErrors) => ({
                        ...prevErrors,
                        ts_followup: undefined,
                      }));
                    }}
                  />
                </InputGroup>
                {errorsFinalmixForm.ts_followup && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsFinalmixForm.ts_followup}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Bobot Granul <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="bobot_granul"
                    placeholder="bobot_granul"
                    value={finalMix.bobot_granul || ""}
                    onChange={(value) => {
                      setFinalMix((prevFormValue) => ({
                        ...prevFormValue,
                        bobot_granul: value,
                      }));
                      setErrorsFinalmixForm((prevErrors) => ({
                        ...prevErrors,
                        bobot_granul: undefined,
                      }));
                    }}
                    type="number"
                  />
                  <InputGroup.Addon>Kg</InputGroup.Addon>
                </InputGroup>
                {errorsFinalmixForm.bobot_granul && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsFinalmixForm.bobot_granul}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Bobot Teoritis <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="bobot_teoritis"
                    placeholder="bobot_teoritis"
                    value={finalMix.bobot_teoritis || ""}
                    onChange={(value) => {
                      setFinalMix((prevFormValue) => ({
                        ...prevFormValue,
                        bobot_teoritis: value,
                      }));
                      setErrorsFinalmixForm((prevErrors) => ({
                        ...prevErrors,
                        bobot_teoritis: undefined,
                      }));
                    }}
                    type="number"
                  />
                  <InputGroup.Addon>Kg</InputGroup.Addon>
                </InputGroup>
                {errorsFinalmixForm.bobot_teoritis && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsFinalmixForm.bobot_teoritis}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Rendemen <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="rendemen"
                    placeholder="rendemen"
                    value={finalMix.rendemen || ""}
                    onChange={(value) => {
                      setFinalMix((prevFormValue) => ({
                        ...prevFormValue,
                        rendemen: value,
                      }));
                      setErrorsFinalmixForm((prevErrors) => ({
                        ...prevErrors,
                        rendemen: undefined,
                      }));
                    }}
                    type="number"
                  />
                  <InputGroup.Addon>Kg</InputGroup.Addon>
                </InputGroup>
                {errorsFinalmixForm.rendemen && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsFinalmixForm.rendemen}
                  </div>
                )}
              </Form.Group>
              {finalMix.status_approval === 0 && (
                <Form.Group>
                  <Form.ControlLabel>Revised Remarks</Form.ControlLabel>
                  <InputGroup
                    style={{
                      borderColor: "red",
                    }}>
                    <Form.Control
                      readOnly
                      name="revised_remarks"
                      placeholder="revised_remarks"
                      value={finalMix.revised_remarks || "-"}
                      onChange={(value) => {
                        setFinalMix((prevFormValue) => ({
                          ...prevFormValue,
                          revised_remarks: value,
                        }));
                        setErrorsFinalmixForm((prevErrors) => ({
                          ...prevErrors,
                          revised_remarks: undefined,
                        }));
                      }}
                      type="text"
                    />
                  </InputGroup>
                </Form.Group>
              )}
              <Form.Group>
                <Form.ControlLabel>
                  Spv <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  name="spv_employee_id"
                  accepter={SelectPicker}
                  value={finalMix.spv_employee_id || ""}
                  data={dataSpv}
                  valueKey="employee_id"
                  labelKey="employee_name"
                  block
                  onChange={(value) => {
                    setFinalMix({ ...finalMix, spv_employee_id: value });
                    setErrorsFinalmixForm((prevErrors) => ({
                      ...prevErrors,
                      spv_employee_id: undefined,
                    }));
                  }}
                />
                {errorsFinalmixForm.spv_employee_id && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsFinalmixForm.spv_employee_id}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Lampiran</Form.ControlLabel>
                <Uploader
                  listType="picture-text"
                  action=""
                  onUpload={handleUploadAttachments}
                  removable={false}
                  fileListVisible={false}>
                  <Button appearance="ghost">Ambil Gambar</Button>
                </Uploader>
                <Row>
                  {attachmentsToDisplay.map((attachment, index) => (
                    <Col md={5} sm={12} key={index} className="mr-5">
                      <AttachmentDisplay attachment={attachment} />
                    </Col>
                  ))}
                </Row>
              </Form.Group>
            </Form>

            <Button
              appearance="primary"
              style={{ backgroundColor: "#1fd306", marginTop: "20px" }}
              onClick={handleUpdateFinalmixData}>
              Save
            </Button>
          </Panel>
        </div>
      </ContainerLayout>
    </div>
  );
}
