import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  try {
    const response = await axios[method](
      `${process.env.NEXT_PUBLIC_BASE_URL}/v2/eform-logsheet-material-criteria/${url}`,
      data
    );
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default function EformLogsheetMaterialApi() {
  return {
    getAll: createApiFunction("get", "get-all"),
  };
}
