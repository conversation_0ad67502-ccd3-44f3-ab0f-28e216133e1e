import axios from "axios";

export default function ipcApi() {
  // Mendapatkan semua nilai Product Code
  const GetAllProductCode = async () => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/all/productcode`)
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Get Product Code by ID
  const GetProductCodeById = async (id) => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/id/productcode`, id)
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };

  // Get Product Code for Entry
  const GetProductCodeForEntry = async () => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/productCodeEntry`)
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Mendapatkan semua nilai instrument
  const GetAllInstrument = async () => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/all/instrument`)
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  const GetInstrumentById = async (reqData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/getById/instrument`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Mendapatkan instrument yang aktif
  const GetActiveInstrument = async () => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/activeInstrument`)
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Get Entry Measurement Data
  const GetEntryMeasurementData = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/entrymeasurementdata`,
        inputData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Get Total MS dan TMS
  const GetTotalMSAndTMS = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/totalmsandtms`,
        inputData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Get Total MS dan TMS And EntryMeasurement
  const GetEntryMeasurementTotalMSAndTMS = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/entryTotalData`,
        inputData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  const GetEntryMeasurementTotalMSAndTMSMysql = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/mysql/entryTotalData`,
        inputData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Get IPC Header By Date
  const GetIpcHeaderByDate = async (inputData) => {
    let reqData = {
      date: inputData,
    };

    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/ipcHeaderData`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Get IPC All data need approve
  const GetIpcHeaderAllDataNeedApprove = async () => {
  

    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/ipcHeaderDataAllNeedApprove`
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Get IPC Detail By Id_Transaction
  const GetIpcDetailData = async (reqData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/ipcDetailData`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Get Ms Reason
  const GetMsReason = async () => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/ipcMsReason`)
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Get Reporting Measurement
  const GetReportingMeasurement = async (reqData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/reportingMeasurement`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Get All Reporting Measurement
  const GetAllReportingMeasurement = async (reqData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/reportingMeasurementAll`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Get Reporting Measurement Details
  const GetReportingMeasurementDetails = async (reqData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/reportingMeasurementDetails`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  const GetReportingMeasurementById = async (reqData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/get/reportingById`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Tambah data baru instrument
  const InsertNewInstrument = async (dataInput) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/new/instrument`,
        dataInput
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };

  // Insert New Product Code
  const InsertProductCode = async (dataInput) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/new/productcode`,
        dataInput
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Insert IPC Header
  const InsertIpcHeader = async (dataInput) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/post/ipcHeader`,
        dataInput
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };

  // Insert IPC Detail
  const InsertIpcDetail = async (dataInput) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/post/ipcDetail`,
        dataInput
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };


  const InsertIpcDetailPostgre = async (dataInput) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/post/ipcDetailPostgre`,
        dataInput
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Update data Instrument
  const UpdateDataInstrument = async (reqData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/update/instrument`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Update Product Code
  const UpdateProductCode = async (updateData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/update/productcode`,
        updateData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Update Staging Status
  const UpdateStagingStatus = async (updateData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/put/stagingStatus`,
        updateData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };
  const UpdateStagingStatusMysql = async (updateData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/put/mysql/stagingStatus`,
        updateData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  const InsertIpcStaging = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/insertEntryMeasurement`,
        inputData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  const ValidateApproval = async (userData) => {
    let data = null;

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/approval/validateApprover`,
        userData
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        console.log(err);
        data = err.response.data;
      });
    return data;
  };

  // Post Approval API
  const PostApprovalApi = async (postData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/post/measurementApprove`,
        postData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

   // Update Staging Status
   const PutClearStaging = async (updateData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/put/clearstagingstatus`,
        updateData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  const PutClearStagingMysql = async (updateData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/ipc/put/mysql/clearstagingstatus`,
        updateData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  return {
    InsertNewInstrument,
    GetAllInstrument,
    GetAllProductCode,
    InsertProductCode,
    UpdateDataInstrument,
    GetProductCodeById,
    UpdateProductCode,
    GetActiveInstrument,
    GetProductCodeForEntry,
    GetEntryMeasurementData,
    GetTotalMSAndTMS,
    InsertIpcHeader,
    InsertIpcDetail,
    InsertIpcDetailPostgre,
    UpdateStagingStatus,
    GetIpcHeaderByDate,
    GetIpcDetailData,
    GetMsReason,
    PostApprovalApi,
    GetReportingMeasurement,
    GetReportingMeasurementDetails,
    GetReportingMeasurementById,
    GetInstrumentById,
    ValidateApproval,
    InsertIpcStaging,
    GetEntryMeasurementTotalMSAndTMS,
    PutClearStaging,
    GetIpcHeaderAllDataNeedApprove,
    GetAllReportingMeasurement,
    PutClearStagingMysql,
    UpdateStagingStatusMysql,
    GetEntryMeasurementTotalMSAndTMSMysql,
  };
}
