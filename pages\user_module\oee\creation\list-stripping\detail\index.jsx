import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, InputGroup, Form, Panel, Stack, Tag, Button, SelectPicker, Table, Col, FlexboxGrid, InputNumber, Notification, Pagination, useToaster, Modal, Loader } from "rsuite";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { Trash as TrashIcon } from "@rsuite/icons";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import ContainerLayout from "@/components/layout/ContainerLayout";

import ApiMachineOeeHeaderStripping from "@/pages/api/oee/machine_oee_stripping/api_machine_oee_stripping";
import ApiOeeCategory from "@/pages/api/oee/oee_category/api_oee_category";
import ApiMachineOeeDetail from "@/pages/api/oee/machine_oee_detail/api_machine_oee_detail"; // Import API Detail
import { useRouter } from "next/router";

export default function index() {
  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [idRouter, setIdRouter] = useState(null);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [loading, setLoading] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const [machineStrippingHeaderDataState, setMachineStrippingHeaderDataState] = useState([]);
  const [dataDetailByIdHeader, setDataDetailByIdHeader] = useState([]);

  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("");
  const [sortType, setSortType] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const toaster = useToaster();

  const router = useRouter();
  const { Id } = router.query;

  // State untuk data OEE Category
  const [plannedCategoryData, setPlannedCategoryData] = useState([]);
  const [unplannedCategoryData, setUnplannedCategoryData] = useState([]);

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = dataDetailByIdHeader.filter((rowData, i) => {
    const searchFields = ["category_name", "child_category_name", "category_type", "duration_detail", "remarks", "created_date", "created_by", "updated_date", "updated_by", "deleted_date", "deleted_by", "is_active"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const formatDateTimeCustom = (isoDateString) => {
    if (!isoDateString) return "-";
    const date = new Date(isoDateString);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  const formatDateTimeCustomNoIso = (isoDateString) => {
    if (!isoDateString) return "-";
    const [datePart, timePart] = isoDateString.split("T");
    const [year, month, day] = datePart.split("-");
    const [hours, minutes] = timePart.split(":");

    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  const formatTimeRange = (startTime, endTime, duration) => {
    const [startDate, startTimeOnly] = startTime.split(" ");
    const [endDate, endTimeOnly] = endTime.split(" ");

    if (startDate === endDate) {
      // Same day: Show only the time range
      return `${startDate} ${startTimeOnly} - ${endTimeOnly} (${duration} menit)`;
    }

    // Different days: Include the date for both
    return `${startDate} ${startTimeOnly} - ${endDate} ${endTimeOnly} (${duration} menit)`;
  };

  const totalRowCount = searchKeyword ? filteredData.length : dataDetailByIdHeader.length;

  // Data Type (Planned/Unplanned)
  const typeData = [
    { label: "Planned", value: "P" },
    { label: "Unplanned", value: "U" },
  ];

  const [addFields, setaddFields] = useState([
    {
      order_number: 1,
      category_type: null,
      category_name: null,
      child_category_name: null,
      duration: null,
      remarks: "",
    },
  ]);

  const addAddField = () => {
    const newField = {
      order_number: addFields.length + 1,
      category_type: null,
      category_name: null,
      child_category_name: null,
      duration: null,
      remarks: "",
    };
    setaddFields([...addFields, newField]);
  };

  const removeAddField = () => {
    if (addFields.length > 1) {
      const newData = [...addFields];
      newData.pop();
      setaddFields(newData);
    }
  };

  const deleteAddField = (index) => {
    if (addFields.length > 1) {
      const dataInputField = [...addFields];
      dataInputField.splice(index, 1);
      setaddFields(dataInputField);
    }
  };

  const handleChange = (index, field, value) => {
    let data = [...addFields];
    data[index][field] = field === "duration" ? Number(value) : value;

    // Reset category_name & child_category_name jika category_type diubah
    if (field === "category_type") {
      data[index].category_name = null;
      data[index].child_category_name = null;
    }

    // Reset child_category_name jika category_name diubah
    if (field === "category_name") {
      data[index].child_category_name = null;
    }

    setaddFields(data);
  };

  const HandleGetTransactionHeaderMachine = async (id_oee_header) => {
    try {
      const apiTransactionHeader = ApiMachineOeeHeaderStripping();
      const response = await apiTransactionHeader.GetMachineOeeListStrippingById({ id_oee_header: parseInt(id_oee_header) });
      if (response.status === 200) {
        const data = response.data;
        setMachineStrippingHeaderDataState({
          id_oee_header: data.id_oee_header,
          id_machine_oee: data.id_machine_oee,
          machine_name: data.machine_name,
          start_time: data.start_time,
          end_time: data.end_time,
          duration: data.duration,
          created_date: data.created_date || "-",
          created_by: data.created_by || "-",
          updated_date: data.update_date || "-",
          updated_by: data.update_by || "-",
          deleted_date: data.delete_date || "-",
          deleted_by: data.delete_by || "-",
          is_active: data.is_active,
        });
        return data;
      } else {
        console.error("Failed to fetch detail data");
        return null;
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
      return null;
    }
  };

  const HandleGetAllOeeCategory = async () => {
    try {
      const res = await ApiOeeCategory().GetAllOeeCategory();
      if (res.status === 200) {
        const { planned_category, unplanned_category } = res.data;

        // Proses planned_category
        const plannedCategoryDataProcessed = planned_category.map((item) => ({
          ...item,
          label: item.category_name,
          value: item.id_main_category,
          childData: item.child_category.map((child) => ({
            ...child,
            label: child.child_category_name,
            value: child.id_child_category,
          })),
        }));

        // Proses unplanned_category
        const unplannedCategoryDataProcessed = unplanned_category.map((item) => ({
          ...item,
          label: item.category_name,
          value: item.id_main_category,
          childData: item.child_category.map((child) => ({
            ...child,
            label: child.child_category_name,
            value: child.id_child_category,
          })),
        }));

        setPlannedCategoryData(plannedCategoryDataProcessed);
        setUnplannedCategoryData(unplannedCategoryDataProcessed);
      } else {
        console.log("Error fetching main categories:", res.message);
      }
    } catch (error) {
      console.log("Error on catch Get All Api", error);
    }
  };

  const HandleGetOeeDetail = async (id_oee_header) => {
    try {
      console.log("Fetching OEE Detail for ID:", id_oee_header);
      const api = ApiMachineOeeDetail();
      const response = await api.GetMachineOeeDetailByIdHeader({ id_oee_header: parseInt(id_oee_header) });
      console.log("API Response:", response);

      if (response.status === 200) {
        console.log("Response Data:", response.data);
        // Set the dataDetailByIdHeader state with the fetched data
        setDataDetailByIdHeader(response.data);
      } else {
        console.error("Failed to fetch detail data");
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
    }
  };

  // Fungsi untuk Submit Data sebagai Array
  const HandleAddTransactionDetailMachine = async () => {
    const errors = {};
    let hasError = false;
    const errorMessages = [];

    // Validasi setiap baris input
    addFields.forEach((input, index) => {
      // Validasi Tipe Aktivitas
      if (!input.category_type) {
        hasError = true;
        errors[`category_type_${index}`] = "Tipe aktivitas harus dipilih.";
        errorMessages.push(`Tipe aktivitas harus dipilih baris ${index + 1}.`);
      }

      // Validasi Nama Aktivitas
      if (!input.category_name) {
        hasError = true;
        errors[`category_name_${index}`] = "Nama aktivitas harus dipilih.";
        errorMessages.push(`Nama aktivitas harus dipilih baris ${index + 1}.`);
      }

      // Validasi Detail Aktivitas
      if (!input.child_category_name) {
        hasError = true;
        errors[`child_category_name_${index}`] = "Detail aktivitas harus dipilih.";
        errorMessages.push(`Detail aktivitas harus dipilih baris ${index + 1}.`);
      }

      // Validasi Durasi
      if (Number(input.duration) <= 0 || isNaN(Number(input.duration))) {
        hasError = true;
        errors[`duration_${index}`] = "Durasi harus lebih dari nol.";
        errorMessages.push(`Durasi harus lebih dari nol baris ${index + 1}.`);
      }
    });

    // Validasi total durasi
    const totalDetailDuration = addFields.reduce((sum, item) => sum + (parseFloat(item.duration) || 0), 0);
    const headerDuration = Number(machineStrippingHeaderDataState.duration) || 0;

    if (totalDetailDuration > headerDuration) {
      hasError = true;
      errors["totalDuration"] = "Durasi detail tidak melebihi total durasi stop";
      errorMessages.push("Durasi detail tidak melebihi total durasi stop");
    }

    // Set semua error ke state
    setErrorsAddForm(errors);

    // Tampilkan notifikasi jika ada kesalahan
    if (hasError) {
      toaster.push(
        <Notification type="error" header="Error">
          {errorMessages.map((msg, idx) => (
            <li key={idx}>{msg}</li>
          ))}
        </Notification>,
        { placement: "topEnd" }
      );
      return; // Hentikan proses jika ada kesalahan
    }

    // Jika tidak ada kesalahan, lanjutkan proses pengiriman data
    try {
      setLoading(true);

      const id_oee_header = machineStrippingHeaderDataState.id_oee_header;
      const created_by = `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`;

      // Membuat payload array
      const payloadArray = addFields.map((input) => {
        const mainCategory = input.category_type === "P" ? plannedCategoryData.find((item) => item.value === input.category_name) : unplannedCategoryData.find((item) => item.value === input.category_name);

        const childCategory = mainCategory?.childData.find((child) => child.value === input.child_category_name);

        if (!mainCategory || !childCategory) {
          throw new Error("Data kategori utama atau kategori anak tidak ditemukan.");
        }

        return {
          id_oee_header: id_oee_header,
          id_binding_category: childCategory.id_binding_category,
          category_name: mainCategory.label,
          child_category_name: childCategory.label,
          category_type: mainCategory.category_type,
          duration_detail: Number(input.duration),
          remarks: input.remarks,
          created_by: created_by,
        };
      });

      // Mengirim data ke API
      const res = await ApiMachineOeeDetail().CreateBatchMachineOeeDetail({ data: payloadArray });

      if (res.status === 200) {
        // Reset input fields setelah berhasil
        setaddFields([
          {
            order_number: 1,
            category_type: null,
            category_name: null,
            child_category_name: null,
            duration: 0,
            remarks: "",
          },
        ]);
        // Reset errors jika ada
        setErrorsAddForm({});
        // Tampilkan notifikasi sukses
        toaster.push(
          <Notification type="success" header="Success">
            Data berhasil disimpan.
          </Notification>,
          { placement: "topEnd" }
        );
        router.replace(`/user_module/oee/creation/list-stripping/detail/edit?Id=${id_oee_header}`);
      } else if (res.status === 400) {
        toaster.push(
          <Notification type="error" header="Error">
            Gagal menyimpan data.
          </Notification>,
          { placement: "topEnd" }
        );
      } else {
        toaster.push(
          <Notification type="error" header="Error">
            Terjadi kesalahan saat menyimpan data.
          </Notification>,
          { placement: "topEnd" }
        );
      }
    } catch (error) {
      console.error("Error saat menambahkan detail transaksi:", error);
      toaster.push(
        <Notification type="error" header="Error">
          Terjadi kesalahan ketika menyimpan data.
        </Notification>,
        { placement: "topEnd" }
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else if (Id) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("oee/creation/list-stripping"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      const parsedId = parseInt(Id, 10);
      setIdRouter(parsedId);
      HandleGetOeeDetail(parsedId);
      HandleGetTransactionHeaderMachine(parsedId);
      HandleGetAllOeeCategory();
    }
  }, [router, Id]);

  return (
    <div>
      <Head>
        <title>Detail HM1 Stripping</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <Breadcrumb>
                <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                <Breadcrumb.Item>Production</Breadcrumb.Item>
                <Breadcrumb.Item>OEE</Breadcrumb.Item>
                <Breadcrumb.Item>Creation</Breadcrumb.Item>
                <Breadcrumb.Item>List</Breadcrumb.Item>
                <Breadcrumb.Item>HM1 Stripping</Breadcrumb.Item>
                <Breadcrumb.Item active>Add Detail</Breadcrumb.Item>
              </Breadcrumb>
            </Stack.Item>
            <Stack.Item>
              <Tag color="green">Module: {moduleName}</Tag>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-4"
            header={
              <Form fluid>
                <FlexboxGrid className="mb-4">
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Nama Mesin</Form.ControlLabel>
                      <Form.Control name="machine_name" value={"HM1 Stripping"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Waktu Stop</Form.ControlLabel>
                        <Form.Control
                          name="time_range"
                          value={
                            machineStrippingHeaderDataState.start_time && machineStrippingHeaderDataState.end_time && machineStrippingHeaderDataState.duration
                              ? formatTimeRange(formatDateTimeCustomNoIso(machineStrippingHeaderDataState.start_time), formatDateTimeCustomNoIso(machineStrippingHeaderDataState.end_time), machineStrippingHeaderDataState.duration)
                              : "-"
                          }
                          readOnly
                          className="mb-3"
                        />
                      </Form.Group>
                    </Form.Group>
                  </FlexboxGrid.Item>
                </FlexboxGrid>

                {/* <FlexboxGrid className="mb-4">
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Waktu Mulai</Form.ControlLabel>
                      <Form.Control name="start_time" value={machineStrippingHeaderDataState.start_time ? formatDateTimeCustomNoIso(machineStrippingHeaderDataState.start_time) : "-"} readOnly className="mb-3" />
                      <Form.ControlLabel>Durasi (Menit)</Form.ControlLabel>
                      <Form.Control name="duration" value={machineStrippingHeaderDataState.duration || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Waktu Selesai</Form.ControlLabel>
                      <Form.Control name="end_time" value={machineStrippingHeaderDataState.end_time ? formatDateTimeCustomNoIso(machineStrippingHeaderDataState.end_time) : "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                </FlexboxGrid> */}

                {/* <FlexboxGrid>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Nama Mesin</Form.ControlLabel>
                      <Form.Control name="machine_name" value={machineStrippingHeaderDataState.machine_name || "-"} readOnly className="mb-3" />
                      <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                      <Form.Control name="updated_date" value={machineStrippingHeaderDataState.updated_date || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  {/* <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                      <Form.Control name="created_by" value={machineStrippingHeaderDataState.created_by || "-"} readOnly className="mb-3" />
                      <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                      <Form.Control name="updated_by" value={machineStrippingHeaderDataState.updated_by || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                      <Form.Control name="deleted_date" value={machineStrippingHeaderDataState.deleted_date || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                      <Form.Control name="deleted_by" value={machineStrippingHeaderDataState.deleted_by || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                </FlexboxGrid> */}
              </Form>
            }
          ></Panel>

          {/* Panel Add Transaction Detail Machine */}
          <Panel bordered>
            <Form.Group controlId="AddTransactionDetailMachine">
              <FlexboxGrid justify="space-between" align="middle" className="mb-2">
                <FlexboxGrid.Item>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Tambahkan Detail Transaksi Mesin:</Form.ControlLabel>
                </FlexboxGrid.Item>
                <FlexboxGrid className="mb-3">
                  <FlexboxGrid.Item>
                    <Button color="green" appearance="primary" onClick={HandleAddTransactionDetailMachine} loading={loading}>
                      Submit
                    </Button>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item style={{ paddingLeft: "10px" }}>
                    {" "}
                    <Button appearance="primary" onClick={() => setShowDetailModal(true)}>
                      Lihat Rincian
                    </Button>
                  </FlexboxGrid.Item>
                </FlexboxGrid>
              </FlexboxGrid>
              {addFields.map((input, index) => {
                return (
                  <FlexboxGrid
                    key={index}
                    style={{
                      padding: "0.5em",
                      border: "0.1em solid #adadad",
                      borderRadius: "5px",
                      marginBottom: "0.5em",
                    }}
                    align="middle"
                    justify="start"
                    gutter={10}
                  >
                    {/* Tipe Aktivitas */}
                    <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={8} xs={8}>
                      <Form.Group>
                        <Form.ControlLabel>Tipe Aktivitas</Form.ControlLabel>
                        <SelectPicker
                          placement="auto"
                          data={typeData}
                          value={input.category_type}
                          onChange={(value) => handleChange(index, "category_type", value)}
                          style={{ width: "100%" }}
                          placeholder="Pilih Tipe"
                          // searchable={false}
                          errorMessage={errorsAddForm[`category_type_${index}`]}
                        />
                      </Form.Group>
                    </FlexboxGrid.Item>

                    {/* Nama Aktivitas */}
                    <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={8} xs={8}>
                      <Form.Group>
                        <Form.ControlLabel>Nama Aktivitas</Form.ControlLabel>
                        <SelectPicker
                          placement="auto"
                          data={input.category_type === "P" ? plannedCategoryData : unplannedCategoryData}
                          value={input.category_name}
                          onChange={(value) => handleChange(index, "category_name", value)}
                          style={{ width: "100%" }}
                          placeholder="Pilih Aktivitas"
                          // searchable={false}
                          disabled={!input.category_type}
                          errorMessage={errorsAddForm[`category_name_${index}`]}
                        />
                      </Form.Group>
                    </FlexboxGrid.Item>

                    {/* Detail Aktivitas */}
                    <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={8} xs={8}>
                      <Form.Group>
                        <Form.ControlLabel>Detail Aktivitas</Form.ControlLabel>
                        <SelectPicker
                          placement="auto"
                          data={
                            input.category_type === "P" ? plannedCategoryData.find((item) => item.value === input.category_name)?.childData || [] : unplannedCategoryData.find((item) => item.value === input.category_name)?.childData || []
                          }
                          value={input.child_category_name}
                          onChange={(value) => handleChange(index, "child_category_name", value)}
                          style={{ width: "100%" }}
                          placeholder="Pilih Detail"
                          // searchable={false}
                          disabled={!input.category_name}
                          errorMessage={errorsAddForm[`child_category_name_${index}`]}
                        />
                      </Form.Group>
                    </FlexboxGrid.Item>

                    {/* Durasi */}
                    <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={8} xs={8}>
                      <Form.Group>
                        <Form.ControlLabel>Durasi</Form.ControlLabel>
                        <InputNumber value={input.duration} onChange={(value) => handleChange(index, "duration", value)} style={{ width: "100%" }} placeholder="Durasi" min={0} errorMessage={errorsAddForm[`duration_${index}`]} />
                      </Form.Group>
                    </FlexboxGrid.Item>

                    {/* Catatan */}
                    <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={8} xs={8}>
                      <Form.Group>
                        <Form.ControlLabel>Catatan</Form.ControlLabel>
                        <Input value={input.remarks} onChange={(value) => handleChange(index, "remarks", value)} style={{ width: "100%" }} placeholder="Remarks" />
                      </Form.Group>
                    </FlexboxGrid.Item>

                    {/* Tombol Hapus */}
                    <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={8} xs={8} style={{ paddingLeft: "1em", display: "flex", alignItems: "flex-end" }}>
                      <IconButton icon={<TrashIcon />} color="red" appearance="primary" circle onClick={() => deleteAddField(index)} title="Hapus baris ini" />
                    </FlexboxGrid.Item>
                  </FlexboxGrid>
                );
              })}

              {/* Tombol Tambah & Kurang Baris */}
              <FlexboxGrid style={{ marginTop: "0.5em" }} gutter={10}>
                <FlexboxGrid.Item style={{ paddingRight: "0.5em" }}>
                  <IconButton icon={<FontAwesomeIcon icon={faPlus} />} color="green" appearance="primary" circle onClick={addAddField} title="Tambah Baris" />
                </FlexboxGrid.Item>
                <FlexboxGrid.Item>
                  <IconButton icon={<FontAwesomeIcon icon={faMinus} />} color="red" appearance="primary" circle onClick={removeAddField} title="Kurangi Baris" />
                </FlexboxGrid.Item>
              </FlexboxGrid>

              {/* Tombol Submit */}
              {/* <FlexboxGrid style={{ marginTop: "1em" }}>
                <FlexboxGrid.Item>
                  <Button appearance="primary" onClick={HandleAddTransactionDetailMachine} loading={loading}>
                    Submit
                  </Button>
                </FlexboxGrid.Item>
              </FlexboxGrid> */}
            </Form.Group>
          </Panel>
          <Modal open={showDetailModal} onClose={() => setShowDetailModal(false)} size="lg">
            <Modal.Header>
              <Stack justifyContent="space-between" alignItems="center">
                <Modal.Title>Detail Transaksi Mesin</Modal.Title>
                {/* Pencarian */}
                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  ></InputGroup.Addon>
                </InputGroup>
              </Stack>
            </Modal.Header>
            <Modal.Body>
              {loading ? (
                <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                  <Loader size="sm" content="Loading..." />
                </div>
              ) : (
                <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn} autoHeight>
                  <Column width={70} align="center" fixed>
                    <HeaderCell>No</HeaderCell>
                    <Cell>{(rowData, rowIndex) => rowIndex + 1 + limit * (page - 1)}</Cell>
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Kategori</HeaderCell>
                    <Cell dataKey="category_name" />
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Sub Kategori</HeaderCell>
                    <Cell dataKey="child_category_name" />
                  </Column>
                  <Column width={120} sortable fullText>
                    <HeaderCell align="center">Tipe Kategori</HeaderCell>
                    <Cell>
                      {(rowData) => {
                        switch (rowData.category_type) {
                          case "P":
                            return "Planned";
                          case "U":
                            return "Unplanned";
                          default:
                            return rowData.category_type; // Menampilkan nilai asli jika tidak 'P' atau 'U'
                        }
                      }}
                    </Cell>
                  </Column>
                  <Column width={120} sortable fullText>
                    <HeaderCell align="center">Durasi (Menit)</HeaderCell>
                    <Cell dataKey="duration_detail" align="center" />
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Info Tambahan</HeaderCell>
                    <Cell dataKey="remarks" />
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Dibuat Tanggal</HeaderCell>
                    <Cell>{(rowData) => formatDateTimeCustom(rowData.created_date)}</Cell>
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                    <Cell dataKey="created_by" />
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Diperbarui Tanggal</HeaderCell>
                    <Cell>{(rowData) => formatDateTimeCustom(rowData.updated_date)}</Cell>
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                    <Cell dataKey="updated_by" />
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Dihapus Tanggal</HeaderCell>
                    <Cell>{(rowData) => formatDateTimeCustom(rowData.deleted_date)}</Cell>
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                    <Cell dataKey="deleted_by" />
                  </Column>
                </Table>
              )}

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Modal.Body>
            <Modal.Footer>
              <Button appearance="primary" onClick={() => setShowDetailModal(false)}>
                Tutup
              </Button>
            </Modal.Footer>
          </Modal>
        </div>
      </ContainerLayout>
    </div>
  );
}
