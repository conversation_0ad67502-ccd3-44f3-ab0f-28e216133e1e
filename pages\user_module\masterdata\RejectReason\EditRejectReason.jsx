import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import MainContent from '@/components/layout/MainContent';
import { Dropdown, Button, Form, ButtonToolbar, Toggle } from 'rsuite';
import Head from 'next/head';
import withReactContent from 'sweetalert2-react-content';
import Swal from 'sweetalert2';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import RejectReasonApi from '@/pages/api/rejectReasonApi';

export default function EditRejectReason({ idRejectReason }) {
  const MySwal = withReactContent(Swal);
  const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
  const [reasonDesc, setReasonDesc] = useState('');
  const [moduleName, setModuleName] = useState('');
  const [isActive, setIsActive] = useState(false);
  const [employeeId, setEmployeeId] = useState('');
  const [userDepartment, setUserDepartment] = useState('');
  const router = useRouter();
  let path = 'masterdata/RejectReason';
  let { data } = router.query;
  data ? (data = JSON.parse(data)) : data;
  const [breadcrumbsData, setBreadcrumbsData] = useState([]);
  const { GetRejectReasonById, UpdateRejectReason } = RejectReasonApi();

  const GetExistingRejectReason = async (id) => {
    const inputData = {
      id_reason: parseInt(id),
    };
    const { data: existingData } = await GetRejectReasonById(inputData);

    if (existingData !== null && existingData !== undefined) {
      const isActiveValue = existingData[0].Is_Active === 1 ? true : false;
      setReasonDesc(existingData[0].Reason_Desc);
      setIsActive(isActiveValue);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem('module_name');
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
      router.push('/');
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push('/dashboard');
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path),
    );
    if (validateUserAccess.length === 0) {
      router.push('/dashboard');
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ''
    ) {
      router.push('/dashboard');
      return;
    }

    const asPathWithoutQuery = router.asPath.split('?')[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split('/')
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      `Setup ${asPathNestedRoutes[1]}`,
      `Edit ${asPathNestedRoutes[2]}`,
    ];

    setBreadcrumbsData(routerBreadCrumbsData);
    setModuleName(moduleNameValue);
    setEmployeeId(dataLogin.employee_id);
    setUserDepartment(dataLogin.department);
    GetExistingRejectReason(idRejectReason);
  }, []);

  //   submitHandler
  const submitHandler = async () => {
    if (reasonDesc === '') {
      MySwal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Please fill all required data!',
      });
      return;
    } else {
      MySwal.showLoading();
      const isDataActive = isActive ? 1 : 0;

      const dataInput = {
        id_reason: parseInt(idRejectReason),
        reason_desc: reasonDesc,
        is_active: isDataActive,
        updated_by: employeeId,
      };

      const { data, message, status } = await UpdateRejectReason(dataInput);

      if (status === 200) {
        setIsSubmitButtonDisabled(true);
        MySwal.fire({
          position: 'center',
          icon: 'success',
          title: message,
          showConfirmButton: false,
          timer: 2500,
        });
        router.push('/user_module/masterdata/RejectReason');
      } else {
        MySwal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'Reject Reason update FAILED.',
        });
      }
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Edit Reject Reason</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <MainContent>
          <ModuleContentHeader
            breadcrumbs={breadcrumbsData}
            module_name={moduleName}
          />

          <Form layout="horizontal" onSubmit={submitHandler}>
            <Form.Group controlId="reason_description">
              <Form.ControlLabel>Reason description :</Form.ControlLabel>
              <Form.Control
                name="reason_description"
                type="text"
                value={reasonDesc}
                onChange={(value) => setReasonDesc(value)}
                required
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>Active</Form.ControlLabel>
              <Toggle
                checked={isActive}
                onChange={() => setIsActive((value) => !value)}
              />
            </Form.Group>

            <Form.Group>
              <ButtonToolbar>
                <Button
                  appearance="primary"
                  type="submit"
                  disabled={isSubmitButtonDisabled}
                >
                  Update
                </Button>
                <Button appearance="default" onClick={() => router.back()}>
                  Cancel
                </Button>
              </ButtonToolbar>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  const { idRejectReason } = query;

  return {
    props: {
      idRejectReason,
    },
  };
}
