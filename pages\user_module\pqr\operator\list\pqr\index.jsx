import { useEffect, useState, useCallback } from "react";
import Head from "next/head";
import { Breadcrumb, Stack, Panel, Tag, Form, Grid, Row, Col, Table, Button, Pagination, Modal, Loader, IconButton, InputGroup, Input, InputNumber, Checkbox, SelectPicker, useToaster, Notification } from "rsuite";
import SearchIcon from "@rsuite/icons/Search";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import ContainerLayout from "@/components/layout/ContainerLayout";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiTransactionDetail from "@/pages/api/pqr/transaction_d/api_transaction_d";
import Api_param_ppi from "@/pages/api/pqr/param_ppi/api_param_ppi";
import ApiStep from "@/pages/api/pqr/step/api_masterdata_step";

export default function Index() {
  const { HeaderCell, Cell, Column } = Table;
  const toaster = useToaster();
  const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [sortColumn, setSortColumn] = useState("id_trans_detail");
  const [sortType, setSortType] = useState("desc");
  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [idRouter, setIdRouter] = useState(null);
  const router = useRouter();
  const { IdHeader } = router.query;

  const emptyAddTransactionDetailForm = {
    id_trans_header: "",
    id_recipe_param: "",
    actual_value: 0,
    result: "",
    create_by: sessionAuth ? sessionAuth.employee_name : "",
    parameters: [],
  };

  const [addTransactionDetailForm, setAddTransactionDetailForm] = useState(emptyAddTransactionDetailForm);
  const [stepForm, SetStepForm] = useState({
    label: "",
    value: "",
  })
  const [transactionDetailsDataState, setTransactionDetailDataState] = useState([]);

  const [parameterDataState, setParameterDataState] = useState([]);
  const [stepWetmillDataState, setStepWetmillDataState] = useState([]);

  const [stepsDataState, setStepsDataState] = useState([]);
  const [machinePPIdataState, setMachinePPIdataState] = useState([]);

  const [bindingParamPpiDataState, setBindingParamPpiDataState] = useState([]);
  const [formDataHeader, setformDataHeader] = useState({});

  const [showAddModal, setShowAddModal] = useState(false);
  const [errorsAddForm, setErrorsAddForm] = useState({});

  const [isGetDataClicked, setIsGetDataClicked] = useState(false);
  const [timerActive, setTimerActive] = useState(false);
  const [timer, setTimer] = useState(50);
  const [waitingData, setWaitingData] = useState(false);

  const filteredData = transactionDetailsDataState.filter((rowData, i) => {
    const searchFields = ["create_date", "parameter_name", "min_value", "max_value", "description_value", "set_point_value", "actual_value", "result", , "create_by"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleGetDataClick = () => {
    setWaitingData(true);
    HandleGetAllMachinePPIData()
  };

  const resetTimer = useCallback(() => {
    setTimer(50);
    setTimerActive(false);
    setIsGetDataClicked(false);
  }, []);

  const resetForm =async  () => {
    HandleGetDetailTransactionHeader(idRouter)
    setAddTransactionDetailForm(emptyAddTransactionDetailForm);
    setStepWetmillDataState([]);
    setErrorsAddForm({});
    resetTimer();
  };

  const totalRowCount = searchKeyword ? filteredData.length : transactionDetailsDataState.length;

  const HandleAddTransactionDetailApi = async () => {
    setLoading(true);
    
    try {
      const parameters = addTransactionDetailForm.parameters.map((param) => ({
        id_recipe_param: param.id_recipe_param,
        parameter_name: param.parameter_name,
        step_name: stepForm.label,
        binding_type: param.binding_type,
        min_value: param.min_value,
        max_value: param.max_value,
        absolute_value: param.absolute_value,
        description_value: param.description_value,
        set_point_flag: param.set_point_flag,
        set_point_value: param.set_point_value,
        actual_value:
          param.binding_type === 1 || param.binding_type === 2
            ? param.actual_value.toString()
            : param.actual_value,
        result: param.binding_type === 0 ? "MS" : param.result,
        id_step: addTransactionDetailForm.id_step
      }));
      //balik kesini

      console.log("HandleAddTransactionDetailApi parameters", parameters)

      const filteredSteps = stepWetmillDataState.filter((step) => {
        return step.set_point_value !== null && step.set_point_value !== "" ||
          step.actual_value !== null && step.actual_value !== "";
      });

      console.log("HandleAddTransactionDetailApi filteredSteps", filteredSteps)

      const steps = filteredSteps.map((step) => ({
        id_recipe_param: step.id_recipe_param,
        step_name: step.step_name,
        parameter_name: step.parameter_name,
        binding_type: step.binding_type,
        min_value: step.min_value,
        max_value: step.max_value,
        absolute_value: step.absolute_value,
        description_value: step.description_value,
        set_point_flag: step.set_point_flag,
        set_point_value: step.set_point_value,
        actual_value:
          step.binding_type === 1 || step.binding_type === 2
            ? step.actual_value.toString()
            : step.actual_value,
        result: step.binding_type === 3
          ? (step.result === "MS" ? "MS" : "TMS")
          : step.binding_type === 2
            ? (step.result === "MS" ? "MS" : "TMS")
            : step.binding_type === 1
              ? (step.result === "MS" ? "MS" : "TMS")
              : (step.set_point_value !== undefined && step.set_point_value !== null
                ? "MS"
                : step.result),
        id_step: addTransactionDetailForm.id_step
      }));

      console.log("HandleAddTransactionDetailApi steps", steps)

      if (stepWetmillDataState.length !== steps.length) {
        showNotification("error", "Pastikan semua isian sudah diisi");
        return
      }

      await handleGetDataClick()

      const payload = {
        id_trans_header: parseInt(IdHeader, 10),
        // parameters,
        steps,
        create_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
      };

      const res = await ApiTransactionDetail().createTransactionDetail(payload);

      if (res.status === 200) {
        setShowAddModal(false);
        await HandleGetDetailTransactionDetail(IdHeader);
        setAddTransactionDetailForm((prev) => ({
          ...prev,
          parameters: prev.parameters.map((param) => ({
            ...param,
            actual_value: null,
            result: null,
          })),
        }));
        resetForm()
        showNotification("success", "Detail Parameter dan Steps berhasil ditambahkan");
      } else {
        showNotification("error", "Gagal Menambahkan Detail Transaksi");
        console.log("gagal menambah data", res.message);
      }
    } catch (error) {
      console.log("error gagal menambah data", error);
      showNotification("error", "Terjadi kesalahan saat menambahkan detail transaksi");
    } finally {
      setLoading(false);
    }
  };

  const HandleGetDetailTransactionHeader = async (id_trans_header) => {
    try {
      const apiTransactionHeader = ApiTransactionHeader();
      const response = await apiTransactionHeader.getPPITransactionHeaderById({ id_trans_header: parseInt(id_trans_header) });
      if (response.status === 200) {
        const data = response.data;
        setformDataHeader({
          id_trans_header: data.id_trans_header,
          id_ppi: data.id_ppi,
          ppi_name: data.ppi_name,
          batch_code: data.batch_code,
          iot_desc: data.iot_desc,
          line_desc: data.line_desc,
          remarks: data.remarks,
          wetmill: data.wetmill,
          status_transaction: data.status_transaction,
          create_date: data.create_date ? new Date(data.create_date).toLocaleDateString("en-GB") : "-",
          create_by: data.create_by || "-",
          update_date: data.update_date ? new Date(data.update_date).toLocaleDateString("en-GB") : "-",
          update_by: data.update_by || "-",
          delete_date: data.delete_date ? new Date(data.delete_date).toLocaleDateString("en-GB") : "-",
          delete_by: data.delete_by || "-",
        });
        setAddTransactionDetailForm({...addTransactionDetailForm, id_step: data.id_step})               
        HandleGetAllActiveStepApi(data.id_ppi);
        return data;
      } else {
        console.log("gagal mengambil data");
        return null;
      }
    } catch (error) {
      console.log("Error gagal mengambil data:", error);
      return null;
    }
  };

  const HandleGetDetailTransactionDetail = async (id_trans_header) => {
    try {
      const res = await ApiTransactionDetail().getTransactionParamBindingDetailById({ id_trans_header: parseInt(id_trans_header) });
      if (res.status === 200) {
        setTransactionDetailDataState(res.data);
      } else {
        console.log("gagal mengambil data ", res.message);
      }
    } catch (error) {
      console.log("Error gagal mengambil data", error);
    }
  };

  const HandleGetAllMachinePPIData = async (id_ppi, wetmill) => {
    try {
      let res = null;
      if (formDataHeader.wetmill === "N") {
        console.log("object", formDataHeader)
        res = await Api_param_ppi().getParamWetmillN({ id_ppi: parseInt(formDataHeader.id_ppi),id_step: addTransactionDetailForm.id_step, id_trans_header: parseInt(idRouter),created_by: sessionAuth ? sessionAuth.employee_name : "" });
      } else if (formDataHeader.wetmill === "Y") {
        res = await Api_param_ppi().getParamWetmillY({ id_ppi: parseInt(formDataHeader.id_ppi),id_step: addTransactionDetailForm.id_step,id_trans_header: parseInt(idRouter),created_by: sessionAuth ? sessionAuth.employee_name : "" });
      } else {
        console.log("Invalid wetmill value");
        return;
      }
      if (res?.status === 200) {
        showNotification("success", "Berhasil mengambil data mesin");
        // if (!res?.data?.data_automate || res?.data?.data_automate.length === 0) {
        //   showNotification("warning", "Tidak ada data mesin pada step ini");
        //   setWaitingData(false);          
        //   if (stepWetmillDataState.length > 0) {
        //     setIsGetDataClicked(true);
        //   }              
        //   return;
        // }
        // setMachinePPIdataState(res?.data?.data_automate);
        
        // const newParameters = res?.data?.data_automate.map((item) => {
        //   let statusComply = "MS";
        //   if (item.binding_type === 1) {
        //     statusComply =
        //       item.actual_value >= item.min_value && item.actual_value <= item.max_value
        //         ? "MS"
        //         : "TMS";
        //   } else if (item.binding_type === 2) {
        //     statusComply =
        //       parseFloat(item.actual_value) === parseFloat(item.actual_value)
        //         ? "MS"
        //         : "TMS";
        //   }
        //   return ({
        //     id_recipe_param: item.id_recipe_param,
        //     parameter_name: item.parameter_name,
        //     min_value: item.min_value,
        //     max_value: item.max_value,
        //     absolute_value: item.absolute_value,
        //     description_value: item.description_value,
        //     binding_type: item.binding_type,
        //     actual_value: item.actual_value,
        //     result: statusComply
        //   })
        // });
        // setAddTransactionDetailForm((prev) => ({
        //   ...prev,
        //   parameters: [
        //     ...prev.parameters,
        //     ...newParameters,
        //   ],
        // }));

        // setWaitingData(false);
        // setTimer(50);
        // setTimerActive(true);
        // setIsGetDataClicked(true);
        // await HandleGetDetailTransactionDetail(IdHeader);
      } else {
        console.log("Error on Get All Api ", res);
        showNotification("warning", "Tidak ada parameter mengambil dari mesin");        
          setWaitingData(false);
          if (stepWetmillDataState.length > 0) {
          setIsGetDataClicked(true);
          }
      }
    } catch (error) {
      console.log("gagal mengambil data", error);
      showNotification("error", "Gagal Mengambil data Mesin");
      if (stepWetmillDataState.length > 0) {
        setIsGetDataClicked(true);
      }
    }
  };

  const HandleGetAllActiveStepApi = async (id_ppi) => {
    try {
      const res = await ApiStep().getAllActiveStepPPI({
        id_ppi: parseInt(id_ppi)
      });
      if (res.status === 200) {
        const options = res.data.map((steps) => ({
          label: steps.step_name,
          value: steps.id_step,
        }));
        setStepsDataState(options);
      } else {
        console.log("gagal mengambil data step: ", res.message);
      }
    } catch (error) {
      console.log("Error gagal mengambil data ", error);
    }
  };

  const HandleGetAllStepWetmillData = async (id_step, id_ppi) => {
    try {
      let res = null;
      if (formDataHeader.wetmill === "Y") {
        res = await Api_param_ppi().getStepWetmillY({ id_step, id_ppi: parseInt(formDataHeader.id_ppi), id_trans_header: parseInt(idRouter), create_by: sessionAuth ? sessionAuth.employee_name : "", });
      } else if (formDataHeader.wetmill === "N") {
        res = await Api_param_ppi().getStepWetmillN({ id_step, id_ppi: parseInt(formDataHeader.id_ppi), id_trans_header: parseInt(idRouter), create_by: sessionAuth ? sessionAuth.employee_name : "", });
      } else {
        console.log("Invalid wetmill value");
        return;
      }
      if (res?.status === 200) {
        setStepWetmillDataState(res.data.data_automate || []);
        HandleGetDetailTransactionDetail(IdHeader);
        return res.data.data_automate;
      } else {
        console.log("gagal mengambil data", res?.message);
        setStepWetmillDataState([]);
        return [];
      }
    } catch (error) {
      console.log("Error gagal mengambil data:", error);
      setStepWetmillDataState([]);
      return [];
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else if (IdHeader) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/operator")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      console.log("Router data:", IdHeader);
      setIdRouter(IdHeader);      
      HandleGetDetailTransactionHeader(IdHeader)
      HandleGetDetailTransactionDetail(IdHeader);
    }
  }, [router]);

  useEffect(() => {
    let timerInterval;
    if (timerActive) {
      timerInterval = setInterval(() => {
        setTimer((prevTimer) => {
          if (prevTimer <= 1) {
            clearInterval(timerInterval);
            setTimerActive(false);
            return 0;
          }
          return prevTimer - 1;
        });
      }, 1000);
    }
    return () => clearInterval(timerInterval);
  }, [timerActive]);

  return (
    <div>
      <div>
        <Head>
          <title>Transaction Header Detail</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>E Release</Breadcrumb.Item>
                  <Breadcrumb.Item>List</Breadcrumb.Item>
                  <Breadcrumb.Item active>Transaction Header</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <Form layout="vertical">
                  <Grid fluid>
                    <Row style={{ marginBottom: "16px" }}>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>ID Transaksi Header</Form.ControlLabel>
                          <Form.Control name="id_trans_header" value={formDataHeader.id_trans_header} style={{ width: "100%" }} />
                        </Form.Group>
                      </Col>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                          <Form.Control name="ppi_name" value={formDataHeader.ppi_name} style={{ width: "100%" }} />
                        </Form.Group>
                      </Col>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Batch Code</Form.ControlLabel>
                          <Form.Control name="batch_code" value={formDataHeader.batch_code} style={{ width: "100%" }} />
                        </Form.Group>
                      </Col>
                      {/* <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Deskripsi IOT</Form.ControlLabel>
                          <Form.Control name="iot_desc" value={formDataHeader.iot_desc} style={{ width: "100%" }} />
                        </Form.Group>
                      </Col> */}
                      <Row style={{ marginBottom: "16px" }}></Row>
                      <Row style={{ marginBottom: "16px" }}>
                        {/* <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Deskripsi Line</Form.ControlLabel>
                            <Form.Control name="line_desc" value={formDataHeader.line_desc} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col> */}
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Remarks</Form.ControlLabel>
                            <Form.Control name="remarks" value={formDataHeader.remarks} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Status Transaksi</Form.ControlLabel>
                            <Form.Control name="status_transaction" value={formDataHeader.status_transaction === 2 ? "Draft" : formDataHeader.status_transaction === 1 ? "Done" : "Dropped"} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Wetmill</Form.ControlLabel>
                            <Form.Control name="wetmill" value={formDataHeader.wetmill === "Y" ? "Yes" : "No"} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                      </Row>
                      <Row style={{ marginBottom: "24px" }}></Row>
                      <Row style={{ marginBottom: "24px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                            <Form.Control name="create_date" value={formDataHeader.create_date} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                            <Form.Control name="created_by" value={formDataHeader.create_by} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                            <Form.Control name="update_date" value={formDataHeader.update_date} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                            <Form.Control name="update_by" value={formDataHeader.update_by} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                      </Row>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                          <Form.Control name="delete_date" value={formDataHeader.delete_date} style={{ width: "100%" }} />
                        </Form.Group>
                      </Col>
                      <Row style={{ marginBottom: "16px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                            <Form.Control name="delete_by" value={formDataHeader.delete_by} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                      </Row>
                    </Row>
                  </Grid>
                </Form>
              </Stack>
            }
          />
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                <IconButton
                    icon={<PlusRoundIcon />}
                    appearance="primary"
                    onClick={async () => { // Marked as async
                      if (bindingParamPpiDataState.length > 0) {
                        const { id_recipe_param, parameter_name, min_value, max_value } = bindingParamPpiDataState[0];
                        setAddTransactionDetailForm((prevForm) => ({
                          ...prevForm,
                          id_recipe_param,
                          step_name,
                          parameter_name,
                          min_value,
                          max_value,
                          actual_value: 0,
                        }));
                      }

                      setShowAddModal(true);

                      if (addTransactionDetailForm.id_step) {
                        try {
                          const response = await HandleGetAllStepWetmillData(addTransactionDetailForm.id_step);
                          if (response && response.length > 0) {
                            showNotification("success", "Data untuk step ini berhasil dimuat");
                          } else {
                            showNotification("info", "Data untuk step ini tidak ada");
                          }
                        } catch (error) {
                          showNotification("error", "Gagal memuat data step");
                        }
                      }
                    }}
                  >
                    Tambah
                  </IconButton>

                </div>
                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input placeholder="cari" value={searchKeyword} onChange={handleSearch} />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={(column, type) => {
                setSortColumn(column);
                setSortType(type);
              }}
              autoHeight
            >
              <Column width={175} sortable align="center" fullText resizable>
                <HeaderCell>Tanggal Dibuat</HeaderCell>
                <Cell>
                  {(rowData) => {
                    if (!rowData.create_date) return "-";
                    
                    // Example input: "2025-05-16T08:25:57.958859Z"
                    const match = rowData.create_date.match(/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2})/);

                    if (!match) return "-";

                    const [, year, month, day, hours, minutes] = match;

                    return `${day}-${month}-${year} ${hours}:${minutes}`;
                  }
                  }
                </Cell>

              </Column>
              <Column width={250} align="center" sortable fullText resizable>
                <HeaderCell>Nama Parameter</HeaderCell>
                <Cell dataKey="parameter_name" />
              </Column>
              <Column width={250} align="center" sortable fullText resizable>
                <HeaderCell>Standard Parameter</HeaderCell>
                <Cell>
                  {(rowData) => {
                    let valueToDisplay;
                    if (rowData.binding_type === 1) {
                      valueToDisplay = rowData.min_value !== undefined && rowData.max_value !== undefined ? `${Number(rowData.min_value).toLocaleString("id-ID")} - ${Number(rowData.max_value).toLocaleString("id-ID")}` : "-";
                    } else if (rowData.binding_type === 2) {
                      valueToDisplay = rowData.absolute_value !== undefined && rowData.absolute_value !== null ? Number(rowData.absolute_value).toLocaleString("id-ID") : "-";
                    } else if (rowData.binding_type === 3) {
                      valueToDisplay = rowData.description_value || "-";
                    } else if (rowData.binding_type === 0) {
                      valueToDisplay = rowData.set_point_value !== undefined && rowData.set_point_value !== null ? Number(rowData.set_point_value).toLocaleString("id-ID") : "-";
                    }
                    return <span>{valueToDisplay}</span>;
                  }}
                </Cell>
              </Column>
              <Column width={180} align="center" sortable fullText resizable>
                <HeaderCell>Nilai Aktual</HeaderCell>
                <Cell>
                  {(rowData) => {
                    let valueToDisplay;
                    if (rowData.set_point_value != 0) {
                      valueToDisplay = rowData.set_point_value !== undefined && rowData.set_point_value !== null ? Number(rowData.set_point_value).toLocaleString("id-ID") : "-";
                    }
                    else {
                      valueToDisplay = rowData.actual_value;
                    }
                    return <span>{valueToDisplay}</span>;
                  }}
                </Cell>
              </Column>
              <Column width={180} align="center" sortable fullText resizable>
                <HeaderCell>Hasil</HeaderCell>
                <Cell dataKey="result" />
              </Column>

              <Column width={250} sortable fullText>
                <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
            <Modal
              backdrop="static"
              size="md"
              open={showAddModal}
              onClose={() => {
                resetForm()
                setShowAddModal(false);
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Menambahkan Detail</Modal.Title>
                <div style={{ marginTop: "16px" }}>
                  <SelectPicker
                    data={stepsDataState}
                    value={addTransactionDetailForm.id_step}
                    onChange={async (value) => {
                      setAddTransactionDetailForm((prevFormValue) => ({
                        ...prevFormValue,
                        id_step: value,
                      }));
                      setErrorsAddForm((prevErrors) => ({
                        ...prevErrors,
                        id_step: undefined,
                      }));

                      const selectedStep = stepsDataState.find((step) => step.value === value);

                      SetStepForm({ label: selectedStep.label, value: selectedStep.value });

                      const response = await HandleGetAllStepWetmillData(value);
                      if (response && response.length > 0) {
                        showNotification("success", "Data untuk step ini berhasil dimuat");
                      } else {
                        showNotification("success", "Data untuk step ini tidak ada");
                      }

                    }}
                    block
                    placeholder="Select Step"
                    style={{ width: "300px" }}
                    disabled={!!addTransactionDetailForm.id_step || addTransactionDetailForm.id_step === 0}
                  />                    
                </div>
                {/* <div style={{ marginTop: "8px" }}>
                  <Button appearance="primary"
                    onClick={() => {
                      handleGetDataClick()
                    }}
                    disabled={timerActive || waitingData || !addTransactionDetailForm.id_step}>
                    Mengambil Data Mesin
                  </Button>
                  {timerActive && <span style={{ marginLeft: "16px" }}>Timer: {timer}s</span>}
                  {!timerActive && waitingData && <span style={{ marginLeft: "16px" }}>Memproses data dari mesin</span>}
                </div> */}
              </Modal.Header>
              <Modal.Body>
                {addTransactionDetailForm.id_step === 0 ? (
                   <div className="mt-2 mb-2" style={{ color: 'red' }}>
                   <strong>Step Sudah Terisi Semua</strong> 
                 </div>
                ):( 
                <div className="mt-2 mb-2" style={{ color: 'red' }}>
                <strong>Perhatian:</strong> Untuk Parameter Set Point dan Data Conectivity akan disimpan setelah klik tombol simpan,
                Pastikan semua isian pada form sudah terisi dengan benar.
              </div>)}
             
                <Form fluid>
                  <Grid fluid>
                    {stepWetmillDataState.map((stepItem, idx) => (
                      <Row key={idx} style={{ marginBottom: "16px" }}>
                        <Col xs={6} sm={6}>
                          <Form.Group controlId={`parameter_name_${idx}`}>
                            {idx === 0 && <Form.ControlLabel>Parameter</Form.ControlLabel>}
                            <Input value={stepItem.parameter_name} readOnly placeholder="Nama Parameter" />
                          </Form.Group>
                        </Col>
                        <Col xs={10} sm={6}>
                          <Form.Group controlId={`criteriaStandard_${idx}`}>
                            {idx === 0 && <Form.ControlLabel>Kriteria Standard</Form.ControlLabel>}
                            <InputGroup>
                              {stepItem.binding_type === 0 && <Input value={stepItem.set_point_value} readOnly style={{ width: 40 }} />}
                              {stepItem.binding_type === 1 && (
                                <>
                                  <Input value={stepItem.min_value} readOnly style={{ width: 30 }} />
                                  <InputGroup.Addon style={{ width: 20 }}>-</InputGroup.Addon>
                                  <Input value={stepItem.max_value} readOnly style={{ width: 30 }} />
                                </>
                              )}
                              {stepItem.binding_type === 2 && <Input value={stepItem.absolute_value} readOnly style={{ width: 40 }} />}
                              {stepItem.binding_type === 3 && <Input value={stepItem.description_value} readOnly style={{ width: 40 }} />}
                            </InputGroup>
                          </Form.Group>
                        </Col>
                        <Col xs={6} sm={6}>
                          <Form.Group controlId={`actual_value_${idx}`}>
                            {idx === 0 && <Form.ControlLabel>Hasil</Form.ControlLabel>}
                            {stepItem.binding_type === 0 || stepItem.binding_type === 1 || stepItem.binding_type === 2 ? (
                              stepItem.set_point_value ? (
                                <Input
                                  value={stepItem.set_point_value}
                                  readOnly
                                  placeholder="Set Point Value"
                                />
                              ) : (
                                <InputNumber
                                  value={stepItem.actual_value}
                                  onChange={(value) => {
                                    setStepWetmillDataState((prevData) => {
                                      const newData = [...prevData];
                                      let resultVal = "TMS";
                                      if (stepItem.binding_type === 1) {
                                        if (value >= stepItem.min_value && value <= stepItem.max_value) {
                                          resultVal = "MS";
                                        }
                                      } else if (stepItem.binding_type === 2) {
                                        if (parseFloat(value) === parseFloat(stepItem.absolute_value)) {
                                          resultVal = "MS";
                                        }
                                      }

                                      newData[idx] = {
                                        ...newData[idx],
                                        actual_value: value,
                                        result: resultVal,
                                      };
                                      return newData;
                                    });
                                  }}
                                  style={{ backgroundColor: "#FFFDD0", width: "100%" }}
                                />
                              )
                            ) : stepItem.binding_type === 3 ? (
                              <Input
                                value={stepItem.actual_value}
                                onChange={(value) => {
                                  setStepWetmillDataState((prevData) => {
                                    const newData = [...prevData];
                                    newData[idx] = {
                                      ...newData[idx],
                                      actual_value: value,
                                    };
                                    return newData;
                                  });
                                }}
                              />
                            ) : null}
                          </Form.Group>
                        </Col>
                        <Col xs={2} sm={2} style={{ display: "flex", alignItems: "center" }}>
                          <Form.Group controlId={`comply_${idx}`}>
                            {idx === 0 && <Form.ControlLabel>Comply</Form.ControlLabel>}
                            {stepItem.binding_type === 3 ? (
                            <Checkbox
                              onChange={(value, checked, event) => {
                                setStepWetmillDataState((prevData) => {
                                  const newData = [...prevData];
                                  newData[idx] = {
                                    ...newData[idx],
                                    result: checked ? "MS" : "TMS",
                                  };
                                  return newData;
                                });
                              }}
                            />                          
                            ) : stepItem.set_point_value ? (
                              <Checkbox
                                checked
                                disabled
                              />
                            ) : (
                              <Checkbox
                                checked={stepItem.result === "MS"}
                                disabled
                              />
                            )}
                          </Form.Group>
                        </Col>
                        <Col xs={12}>
                          {errorsAddForm.steps && <p style={{ color: "red" }}>{errorsAddForm.steps}</p>}
                        </Col>
                      </Row>
                    ))}
                    {addTransactionDetailForm.parameters &&
                      addTransactionDetailForm.parameters.map((param, index) => (
                        <Row key={index} style={{ marginBottom: "16px" }}>
                          <Col xs={6} sm={6}>
                            <Form.Group controlId={`parameter_name_${index}`}>
                              {index === 0 && <Form.ControlLabel></Form.ControlLabel>}
                              <Input value={param.parameter_name} readOnly placeholder="Nama Parameter" />
                            </Form.Group>
                          </Col>
                          <Col xs={10} sm={6}>
                            <Form.Group controlId={`criteriaStandard_${index}`}>
                              {index === 0 && <Form.ControlLabel></Form.ControlLabel>}
                              <InputGroup>
                                {param.binding_type === 1 && (
                                  <>
                                    <Input value={param.min_value} readOnly style={{ width: 30 }} />
                                    <InputGroup.Addon style={{ width: 20 }}>-</InputGroup.Addon>
                                    <Input value={param.max_value} readOnly style={{ width: 30 }} />
                                  </>
                                )}
                                {param.binding_type === 2 && <Input value={param.absolute_value} readOnly style={{ width: 40 }} />}
                                {param.binding_type === 3 && <Input value={param.description_value} readOnly style={{ width: 40 }} />}
                              </InputGroup>
                            </Form.Group>
                          </Col>
                          <Col xs={6} sm={6}>
                            <Form.Group controlId={`actual_value_${index}`}>
                              {index === 0 && <Form.ControlLabel></Form.ControlLabel>}
                              {param.binding_type === 3 ? (
                                <Input
                                  value={param.actual_value}
                                  onChange={(value) => {
                                    setAddTransactionDetailForm((prev) => {
                                      const updatedParameters = [...prev.parameters];
                                      updatedParameters[index].actual_value = value || "";
                                      updatedParameters[index].result = value ? "MS" : null;
                                      return { ...prev, parameters: updatedParameters };
                                    });
                                  }}
                                />
                              ) : (
                                <Input
                                  value={param.actual_value}
                                  onChange={(value) => {
                                    setAddTransactionDetailForm((prev) => {
                                      const updatedParameters = [...prev.parameters];
                                      if (updatedParameters[index]) {
                                        updatedParameters[index].actual_value = value || null;
                                        if (param.binding_type === 1) {
                                          updatedParameters[index].result =
                                            value >= param.min_value && value <= param.max_value
                                              ? "MS"
                                              : "TMS";
                                        } else if (param.binding_type === 2) {
                                          updatedParameters[index].result =
                                            parseFloat(value) === parseFloat(param.absolute_value)
                                              ? "MS"
                                              : "TMS";
                                        }
                                      }
                                      return { ...prev, parameters: updatedParameters };
                                    });
                                  }}
                                  style={{width: '100%' }}
                                  disabled
                                />
                              )}
                            </Form.Group>
                          </Col>
                          <Col xs={2} sm={2} style={{ display: "flex", alignItems: "center" }}>
                            <Form.Group controlId={`comply_${index}`}>
                              {index === 0 && param.binding_type !== 3 && <Form.ControlLabel></Form.ControlLabel>}
                              {param.binding_type !== 3 && <Checkbox checked={param.result === "MS"} disabled />}
                            </Form.Group>
                          </Col>
                          <Col xs={12}>{errorsAddForm.parameters && <p style={{ color: "red" }}>{errorsAddForm.parameters}</p>}</Col>
                        </Row>
                      ))}
                  </Grid>
                </Form>
              </Modal.Body>

              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    resetForm()
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={async () => {                    
                    HandleAddTransactionDetailApi();
                  }}
                  appearance="primary"
                  type="submit"
                  loading={loading}
                  disabled={loading 
                    // || !isGetDataClicked
                  }
                >
                  Simpan
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Adding Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
          </Panel>
        </div>
      </ContainerLayout>
    </div>
  );
}