import { useEffect, useRef, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  Loader,
  RadioGroup,
  Radio,
  useToaster,
  Notification,
  Whisper,
  Tooltip,
} from "rsuite";
import EditIcon from "@rsuite/icons/Edit";
import TrashIcon from "@rsuite/icons/Trash";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import ApiLineClearanceGranulasi from "@/pages/api/pqr/line_clearance/granule/api_line_clearance_granulasi";

export default function Clearance_Granulasi() {
  const toaster = useToaster();
  const { HeaderCell, Cell, ColumnGroup, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_trans_header");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [ppiDataState, setPPIDataState] = useState([]);
  const [lineClearanceData, setLineClearanceData] = useState([]);
  const [selectedLineClearanceData, setselectedLineClearanceData] = useState(
    []
  );
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // pdf?idTransactionH=11

  const emptyAddLineClearanceForm = {
    id_clearance: null,
    batch_code: "",
    product_code: "",
    clean_routine: null,
    calibration_routine: null,
    area_routine: null,
    label_routine: null,
    clean_periodic: null,
    calibration_periodic: null,
    area_periodic: null,
    label_preiodic: null,
    dust_periodic: null,
    discharge: null,
    bowl_machine: null,
    lid_container: null,
    floor: null,
    filter_clem: null,
    machine_panel: null,
    spray_gun: null,
    impeller_chopper: null,
    granulator_wetmill: null,
    connector_sifting: null,
    impeller_sifting: null,
    mesh: null,
    seal_sifting: null,
    fbd_sifting: null,
    label_process: null,
    product_container: null,
    create_by: sessionAuth
      ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`
      : "",
  };

  const emptyEditLineClearanceForm = {
    batch_code: "",
    product_code: "",
    clean_routine: null,
    calibration_routine: null,
    area_routine: null,
    label_routine: null,
    clean_periodic: null,
    calibration_periodic: null,
    area_periodic: null,
    label_preiodic: null,
    dust_periodic: null,
    discharge: null,
    bowl_machine: null,
    lid_container: null,
    floor: null,
    filter_clem: null,
    machine_panel: null,
    spray_gun: null,
    impeller_chopper: null,
    granulator_wetmill: null,
    connector_sifting: null,
    impeller_sifting: null,
    mesh: null,
    seal_sifting: null,
    fbd_sifting: null,
    status_approval: null,
    label_process: null,
    product_container: null,
    update_by: sessionAuth
      ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`
      : "",
    id_clearance: null,
  };

  const emptyDeleteLineClearanceForm = {
    id_clearance: null,
    delete_by: sessionAuth
      ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`
      : "",
  };

  const [addLineClearanceForm, setAddLineClearanceForm] = useState(
    emptyAddLineClearanceForm
  );
  const [editLineClearanceForm, setEditLineClearanceForm] = useState(
    emptyEditLineClearanceForm
  );
  const [deleteLineClearanceForm, setDeleteLineClearanceForm] = useState(
    emptyDeleteLineClearanceForm
  );
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const viewHandler = async (id_clearance) => {
    const url = `${
      process.env.NEXT_PUBLIC_PIMS_FE
    }/user_module/pqr/line_clearance/granule/pdf?id_clearance=${parseInt(
      id_clearance
    )}`;
    window.open(url, "_blank");
  };

  const filteredData = lineClearanceData.filter((rowData, i) => {
    const searchFields = [
      "id_clearance",
      "batch_code",
      "product_code",
      "create_date",
      "create_by",
      "update_date",
      "update_by",
      "delete_date",
      "delete_by",
      "is_active",
    ];

    const matchesSearch = searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );

    return matchesSearch;
  });

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };
  const totalRowCount = searchKeyword
    ? filteredData.length
    : lineClearanceData.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    console.log("credential = ", sessionAuth);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/operator")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetAllLineClearData();
    }
  }, []);

  const formatDate = (rowData, dateKey) => {
    const formattedDate = rowData[dateKey];

    if (!formattedDate) return "-";

    const datePart = formattedDate.substring(0, 10);
    const timePart = formattedDate.substring(11, 16);

    const dateParts = datePart.split("-");
    const year = dateParts[0];
    const month = dateParts[1];
    const day = dateParts[2];

    return `${day}-${month}-${year} ${timePart}`;
  };

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification
        type={type}
        header={type === "success" ? "Success" : "Error"}
        closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const resetForm = () => {
    setAddLineClearanceForm(emptyAddLineClearanceForm);
    setEditLineClearanceForm(emptyEditLineClearanceForm);
    setDeleteLineClearanceForm(emptyDeleteLineClearanceForm);
    setErrorsAddForm({});
  };

  const HandleCreateLineClearanceApi = async () => {
    setLoading(true);
    try {
      const errors = {};
      const requiredFields = [
        "batch_code",
        "product_code",
        "clean_routine",
        "calibration_routine",
        "area_routine",
        "label_routine",
        "clean_periodic",
        "calibration_periodic",
        "area_periodic",
        "label_preiodic",
        "dust_periodic",
        "discharge",
        "bowl_machine",
        "lid_container",
        "floor",
        "filter_clem",
        "machine_panel",
        "spray_gun",
        "impeller_chopper",
        "granulator_wetmill",
        "connector_sifting",
        "impeller_sifting",
        "mesh",
        "seal_sifting",
        "fbd_sifting",
        "label_process",
        "product_container",
      ];

      for (const field of requiredFields) {
        if (
          addLineClearanceForm[field] === null ||
          (typeof addLineClearanceForm[field] === "string" &&
            addLineClearanceForm[field].trim() === "")
        ) {
          errors[field] = `kolom ${field.replace(/_/g, " ")} wajib diisi`;
        }
      }

      if (Object.keys(errors).length > 0) {
        setErrorsAddForm(errors);
        showNotification("error", "Mohon isi seluruh kolom yang wajib diisi.");
        setLoading(false);
        return;
      }

      // Line Clearance Data Payload
      const lineClearancePayload = {
        batch_code: addLineClearanceForm.batch_code,
        product_code: addLineClearanceForm.product_code,
        clean_routine: addLineClearanceForm.clean_routine,
        calibration_routine: addLineClearanceForm.calibration_routine,
        area_routine: addLineClearanceForm.area_routine,
        label_routine: addLineClearanceForm.label_routine,
        clean_periodic: addLineClearanceForm.clean_periodic,
        calibration_periodic: addLineClearanceForm.calibration_periodic,
        area_periodic: addLineClearanceForm.area_periodic,
        label_preiodic: addLineClearanceForm.label_preiodic,
        dust_periodic: addLineClearanceForm.dust_periodic,
        discharge: addLineClearanceForm.discharge,
        bowl_machine: addLineClearanceForm.bowl_machine,
        lid_container: addLineClearanceForm.lid_container,
        floor: addLineClearanceForm.floor,
        filter_clem: addLineClearanceForm.filter_clem,
        machine_panel: addLineClearanceForm.machine_panel,
        spray_gun: addLineClearanceForm.spray_gun,
        impeller_chopper: addLineClearanceForm.impeller_chopper,
        granulator_wetmill: addLineClearanceForm.granulator_wetmill,
        connector_sifting: addLineClearanceForm.connector_sifting,
        impeller_sifting: addLineClearanceForm.impeller_sifting,
        mesh: addLineClearanceForm.mesh,
        seal_sifting: addLineClearanceForm.seal_sifting,
        fbd_sifting: addLineClearanceForm.fbd_sifting,
        create_by: emptyAddLineClearanceForm.create_by,
        label_process: addLineClearanceForm.label_process,
        product_container: addLineClearanceForm.product_container,
      };

      const res = await ApiLineClearanceGranulasi().postCreateLineClear(
        lineClearancePayload
      );

      console.log("status", res.status);

      if (res.status === 200) {
        setShowAddModal(false);
        resetForm();
        showNotification("success", "Line Clearance berhasil ditambahkan");
        handleGetAllLineClearData();
      } else {
        showNotification("error", "Gagal Menambahkan Line Clearance");
      }
    } catch (error) {
      console.log("error on AddTransactionDetailApi ", error);
      showNotification("error", "Terjadi kesalahan saat menambahkan transaksi");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateLineClearanceApi = async (status_approval) => {
    setLoading(true);
    try {
      const errors = {};
      const requiredFields = ["batch_code", "product_code"];

      for (const field of requiredFields) {
        if (
          typeof editLineClearanceForm[field] === "string" &&
          editLineClearanceForm[field].trim() === ""
        ) {
          errors[field] = `${field.replace(/_/g, " ")} is required`;
        }
      }

      if (Object.keys(errors).length > 0) {
        setErrorsEditForm(errors);
        showNotification("error", "Please fill in all required fields.");
        setLoading(false);
        return;
      }

      // Line Clearance Data Payload for Update
      const lineClearancePayload = {
        batch_code: editLineClearanceForm.batch_code,
        product_code: editLineClearanceForm.product_code,
        clean_routine: editLineClearanceForm.clean_routine,
        calibration_routine: editLineClearanceForm.calibration_routine,
        area_routine: editLineClearanceForm.area_routine,
        label_routine: editLineClearanceForm.label_routine,
        clean_periodic: editLineClearanceForm.clean_periodic,
        calibration_periodic: editLineClearanceForm.calibration_periodic,
        area_periodic: editLineClearanceForm.area_periodic,
        label_preiodic: editLineClearanceForm.label_preiodic,
        dust_periodic: editLineClearanceForm.dust_periodic,
        discharge: editLineClearanceForm.discharge,
        bowl_machine: editLineClearanceForm.bowl_machine,
        lid_container: editLineClearanceForm.lid_container,
        floor: editLineClearanceForm.floor,
        filter_clem: editLineClearanceForm.filter_clem,
        machine_panel: editLineClearanceForm.machine_panel,
        spray_gun: editLineClearanceForm.spray_gun,
        impeller_chopper: editLineClearanceForm.impeller_chopper,
        granulator_wetmill: editLineClearanceForm.granulator_wetmill,
        connector_sifting: editLineClearanceForm.connector_sifting,
        impeller_sifting: editLineClearanceForm.impeller_sifting,
        mesh: editLineClearanceForm.mesh,
        seal_sifting: editLineClearanceForm.seal_sifting,
        fbd_sifting: editLineClearanceForm.fbd_sifting,
        status_approval: status_approval,
        label_process: editLineClearanceForm.label_process,
        product_container: editLineClearanceForm.product_container,
        update_by: emptyEditLineClearanceForm.update_by,
        id_clearance: editLineClearanceForm.id_clearance,
      };

      const res = await ApiLineClearanceGranulasi().putUpdateLineClear(
        lineClearancePayload
      );

      console.log("status", res.status);

      if (res.status === 200) {
        setShowEditModal(false);
        showNotification("success", "Line Clearance berhasil diperbarui");
        handleGetAllLineClearData();
      } else {
        showNotification("error", "Gagal memperbarui Line Clearance");
      }
    } catch (error) {
      console.log("error on UpdateLineClearanceApi ", error);
      showNotification(
        "error",
        "Terjadi kesalahan saat memperbarui Line Clearance"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteLineClearanceApi = async () => {
    setLoading(true);
    try {
      const lineClearancePayload = {
        id_clearance: deleteLineClearanceForm.id_clearance,
        delete_by: emptyDeleteLineClearanceForm.delete_by,
      };

      const res = await ApiLineClearanceGranulasi().putDeleteStatusLineClear(
        lineClearancePayload
      );

      console.log("status", res.status);

      if (res.status === 200) {
        setShowDeleteModal(false);
        showNotification("success", "Line Clearance berhasil dihapus");
        handleGetAllLineClearData();
      } else {
        showNotification("error", "Gagal menghapus Line Clearance");
      }
    } catch (error) {
      console.log("error on putDeleteStatusLineClear ", error);
      showNotification(
        "error",
        "Terjadi kesalahan saat menghapus Line Clearance"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleGetLineClearByID = async (payload) => {
    try {
      const requestPayload = {
        id_clearance: payload,
      };

      const res = await ApiLineClearanceGranulasi().getLineClearByID(
        requestPayload
      );
      console.log("res = ", res);

      if (res.status === 200) {
        console.log("res", res.data);
        setselectedLineClearanceData(res.data);
        setEditLineClearanceForm(res.data);
        setDeleteLineClearanceForm({ id_clearance: res.data.id_clearance });
      } else {
        console.log("error on handleGetLineClearByID API", res.message);
      }
    } catch (error) {
      console.log("error on catch handleGetLineClearByID", error);
    }
  };

  const handleGetAllLineClearData = async () => {
    try {
      const res = await ApiLineClearanceGranulasi().getAllLineClear();

      console.log("res", res);
      if (res.status === 200) {
        console.log("res data = ", res.data);
        setLineClearanceData(res.data);
      } else {
        console.log("error on handleGetAllLineClearData API", res.message);
      }
    } catch (error) {
      console.log("error on catch handleGetAllLineClearData", error);
    }
  };

  // useEffect(() => {
  //   console.log("Line Clearance Data : ", lineClearanceData);
  // }, [lineClearanceData]);

  // useEffect(() => {
  //   console.log("Line Clearance Data by id = ", selectedLineClearanceData);
  // }, [selectedLineClearanceData]);

  return (
    <>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>Line Clearance</Breadcrumb.Item>
                  <Breadcrumb.Item active>Granulasi</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Line Clearance Granulasi</h5>
              </Stack>
            }></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <Stack spacing={10}>
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}>
                      Tambah
                    </IconButton>
                    <InputGroup inside>
                      <InputGroup.Addon>
                        <SearchIcon />
                      </InputGroup.Addon>
                      <Input
                        placeholder="cari"
                        value={searchKeyword}
                        onChange={handleSearch}
                      />
                      <InputGroup.Addon
                        onClick={() => {
                          setSearchKeyword("");
                          setPage(1);
                        }}
                        style={{
                          display: searchKeyword ? "block" : "none",
                          color: "red",
                          cursor: "pointer",
                        }}>
                        <CloseOutlineIcon />
                      </InputGroup.Addon>
                    </InputGroup>
                  </Stack>
                </Stack>
              }>
              <Table
                bordered
                cellBordered
                height={400}
                data={getPaginatedData(getFilteredData(), limit, page)}
                autoHeight
                className="text-center">
                <Column width={180} align="center" fullText>
                  <HeaderCell>ID Clearance</HeaderCell>
                  <Cell dataKey="id_clearance" />
                </Column>
                <Column width={180} fullText>
                  <HeaderCell align="center">Kode Batch</HeaderCell>
                  <Cell dataKey="batch_code" />
                </Column>
                <Column width={180} fullText>
                  <HeaderCell align="center">Kode Produk</HeaderCell>
                  <Cell dataKey="product_code" />
                </Column>
                <Column width={180} fullText dataKey="clean_routine">
                  <HeaderCell align="center">Clean Routine</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.clean_routine == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="clean_routine">
                  <HeaderCell align="center">Calibration Routine</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.calibration_routine == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="area_routine">
                  <HeaderCell align="center">Area Routine</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.area_routine == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="label_routine">
                  <HeaderCell align="center">Label Routine</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.label_routine == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="clean_periodic">
                  <HeaderCell align="center">Clean Periodic</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.clean_periodic == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="calibration_periodic">
                  <HeaderCell align="center">Calibration Periodic</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.calibration_periodic == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="area_periodic">
                  <HeaderCell align="center">Area Periodic</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.area_periodic == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="label_preiodic">
                  <HeaderCell align="center">Label Periodic</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.label_preiodic == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="dust_periodic">
                  <HeaderCell align="center">Dust Periodic</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.dust_periodic == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="discharge">
                  <HeaderCell align="center">Discharge</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.discharge == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="bowl_machine">
                  <HeaderCell align="center">Bowl Machine</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.bowl_machine == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="lid_container">
                  <HeaderCell align="center">Lid Container</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.lid_container == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="floor">
                  <HeaderCell align="center">Floor</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.floor == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="filter_clem">
                  <HeaderCell align="center">Filter Clem</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.filter_clem == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="machine_panel">
                  <HeaderCell align="center">Machine Panel</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.machine_panel == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="spray_gun">
                  <HeaderCell align="center">Spray Gun</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.spray_gun == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="impeller_chopper">
                  <HeaderCell align="center">Impeller Chopper</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.impeller_chopper == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="granulator_wetmill">
                  <HeaderCell align="center">granulator wetmill</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.granulator_wetmill == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="connector_sifting">
                  <HeaderCell align="center">Connector Sifting</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.connector_sifting == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="impeller_sifting">
                  <HeaderCell align="center">Impeller Sifting</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.impeller_sifting == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="mesh">
                  <HeaderCell align="center">Mesh</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.mesh == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="seal_sifting">
                  <HeaderCell align="center">Seal Sifting</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.seal_sifting == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="fbd_sifting">
                  <HeaderCell align="center">Fbd Sifting</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.fbd_sifting == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="approval_date">
                  <HeaderCell align="center">Approval Date</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.approval_date)
                        return formatDate(rowData, "approval_date");
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="revise_remarks">
                  <HeaderCell align="center">Revise Remarks</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.revise_remarks) return rowData.revise_remarks;
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText>
                  <HeaderCell align="center">Create Date</HeaderCell>
                  <Cell dataKey="create_date">
                    {(rowData) => formatDate(rowData, "create_date")}
                  </Cell>
                </Column>
                <Column width={180} fullText>
                  <HeaderCell align="center">Create By</HeaderCell>
                  <Cell dataKey="create_by" />
                </Column>
                <Column width={180} fullText dataKey="update_date">
                  <HeaderCell align="center">Update Date</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.update_date)
                        return formatDate(rowData, "update_date");
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="update_by">
                  <HeaderCell align="center">Update By</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.update_by) return rowData.update_by;
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="delete_date">
                  <HeaderCell align="center">Delete Date</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.delete_date)
                        return formatDate(rowData, "delete_date");
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="delete_by">
                  <HeaderCell align="center">Delete By</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.delete_by) return rowData.delete_by;
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="is_active">
                  <HeaderCell align="center">Is Active</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}>
                        {rowData.is_active === 1 ? "Active" : "Inactive"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column
                  width={170}
                  fixed="right"
                  fullText
                  dataKey="fbd_sifting">
                  <HeaderCell align="center">Status Approval</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      const statusMap = {
                        2: { label: "Waiting For Approval", color: "violet" },
                        1: { label: "Approved", color: "green" },
                        0: { label: "Revised", color: "yellow" },
                      };

                      const status =
                        rowData.is_active == 0
                          ? { label: "Deleted", color: "red" }
                          : statusMap[rowData?.status_approval] || {
                              label: "Unknown",
                              color: "black",
                            };
                      let tooltipContent = null;
                      if (rowData.is_active === 0) {
                        tooltipContent = "";
                      } else if (rowData?.status_approval === 0) {
                        tooltipContent = rowData?.revise_remarks;
                      }

                      return tooltipContent ? (
                        <Whisper
                          placement="top"
                          trigger="hover"
                          speaker={<Tooltip>{tooltipContent}</Tooltip>}>
                          <Tag color={status.color}>{status.label}</Tag>
                        </Whisper>
                      ) : (
                        <Tag color={status.color}>{status.label}</Tag>
                      );
                    }}
                  </Cell>
                </Column>
                <Column width={180} fixed="right" align="center" colSpan={3}>
                  <HeaderCell>Actions</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="link"
                          disabled={
                            rowData.status_approval === 1 ||
                            rowData.is_active === 0
                          }
                          onClick={() => {
                            viewHandler(rowData.id_clearance);
                          }}>
                          Detail
                        </Button>
                        <Button
                          appearance="link"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModal(true);
                            emptyEditLineClearanceForm.status_approval =
                              rowData.status_approval;
                            handleGetLineClearByID(rowData.id_clearance);
                          }}>
                          <EditIcon style={{ fontSize: "16px" }} />
                        </Button>
                        <Button
                          appearance="link"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModal(true);
                            emptyEditLineClearanceForm.status_approval =
                              rowData.status_approval;
                            handleGetLineClearByID(rowData.id_clearance);
                          }}>
                          <EditIcon style={{ fontSize: "16px" }} />
                        </Button>
                        <Button
                          appearance="link"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            viewHandler(rowData.id_clearance);
                          }}>
                          <SearchIcon style={{ fontSize: "16px" }} />
                        </Button>
                        <Button
                          appearance="link"
                          disabled={
                            rowData.status_approval === 1 ||
                            rowData.is_active === 0
                          }
                          onClick={() => {
                            setShowDeleteModal(true);
                            handleGetLineClearByID(rowData.id_clearance);
                          }}>
                          <TrashIcon style={{ fontSize: "16px" }} />
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
                {/* <Column width={80} fixed="right" align="center">
                    <HeaderCell> dasd</HeaderCell>

                    <Cell style={{ padding: "8px" }}>
                      {(rowData) => (
                       
                      )}
                    </Cell>
                  </Column>
                  <Column width={80} fixed="right" align="center">
                    <HeaderCell> asd</HeaderCell>
                    <Cell style={{ padding: "8px" }}>
                      {(rowData) => (
                      
                      )}
                    </Cell>
                  </Column> */}
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            {/* Add modal */}
            <Modal
              size={"lg"}
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                setShowAddModal(false);
                setAddLineClearanceForm(emptyAddLineClearanceForm);
                setErrorsEditForm({});
                setErrorsAddForm({});
              }}
              overflow={false}>
              <Modal.Header>
                <Modal.Title>Tambah Line Clearance</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Batch Code</Form.ControlLabel>
                    <Form.Control
                      name="batch_code"
                      value={addLineClearanceForm.batch_code}
                      onChange={(value) => {
                        setAddLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          batch_code: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          batch_code: undefined,
                        }));
                      }}
                    />
                    {errorsAddForm.batch_code && (
                      <p style={{ color: "red" }}>{errorsAddForm.batch_code}</p>
                    )}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Product Code</Form.ControlLabel>
                    <Form.Control
                      name="product_code"
                      value={addLineClearanceForm.product_code}
                      onChange={(value) => {
                        setAddLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          product_code: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          product_code: undefined,
                        }));
                      }}
                    />
                    {errorsAddForm.product_code && (
                      <p style={{ color: "red" }}>
                        {errorsAddForm.product_code}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      <p>Pembersihan Rutin, boleh ada sisa produk jika:</p>
                      <ul>
                        <li>a. Ganti batch prodak</li>
                        <li>b. Formula sama tetapi kode produk berbeda.</li>
                      </ul>
                      {errorsAddForm.clean_routine && (
                        <p className="text-red-500">
                          {errorsAddForm.clean_routine}
                        </p>
                      )}
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="floor"
                        inline
                        value={addLineClearanceForm.clean_routine}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            clean_routine: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            clean_routine: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan Kabel kalitrasi dan kualifikasi pada mesin masih
                      berlaku.
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="calibration_routine"
                        inline
                        value={addLineClearanceForm.calibration_routine}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            calibration_routine: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            calibration_routine: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.calibration_routine && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.calibration_routine}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan kebersihan lantai dan area mesin High Shear Mixer
                      Granulator GEA (PMA 800) (M01GEAS0001) serta lemari/ meja.
                      Pastikan tidak ada dokumen, prodak antara dan bahan /
                      material, dari produk sebelumnya.
                      {errorsAddForm.area_routine && (
                        <p className="text-red-500">
                          {errorsAddForm.area_routine}
                        </p>
                      )}
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="area_routine"
                        inline
                        value={addLineClearanceForm.area_routine}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            area_routine: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            area_routine: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan tidak ada label identitas mesin "SEDANG PROSES"
                      dari produk sebelumnya yang tertinggal di ruangan.
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="label_routine"
                        inline
                        value={addLineClearanceForm.label_routine}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            label_routine: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            label_routine: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.label_routine && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.label_routine}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      <p>Pembersihan Berkala, Jila:</p>
                      <ul>
                        <li>a. Ganti produk</li>
                        <li>
                          b. Ganti batch tetapi sudah mencapai batas campaign
                          batch (sesuai Supporting Document).
                        </li>
                        <li>
                          c. Mesin tidak digunakan lebih dari hari yang sudah
                          ditetapkan (sesuai Supporting Document pembersihan
                          tiap mesin/ alat).
                        </li>
                      </ul>
                      {errorsAddForm.clean_periodic && (
                        <p className="text-red-500">
                          {errorsAddForm.clean_periodic}
                        </p>
                      )}
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="clean_periodic"
                        inline
                        value={addLineClearanceForm.clean_periodic}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            clean_periodic: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            clean_periodic: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan label kalitrasi dan kualifikasi pada mesin masih
                      berlaku.
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="calibration_periodic"
                        inline
                        value={addLineClearanceForm.calibration_periodic}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            calibration_periodic: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            calibration_periodic: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.calibration_periodic && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.calibration_periodic}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan kebersihan lantai dan area mesin High Shear Mixer
                      Granulator GEA PMA 800 (M0IGEA8000I) serta lemari / meja.
                      Pastikan tidak ada dokumen, produk antara dan bahan /
                      material, dari produk sebelumnya.
                      {errorsAddForm.area_periodic && (
                        <p className="text-red-500">
                          {errorsAddForm.area_periodic}
                        </p>
                      )}
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="area_periodic"
                        inline
                        value={addLineClearanceForm.area_periodic}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            area_periodic: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            area_periodic: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan label "SIAP PAKAI" tersedia dan masih berlaku.
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="label_preiodic"
                        inline
                        value={addLineClearanceForm.label_preiodic}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            label_preiodic: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            label_preiodic: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.label_preiodic && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.label_preiodic}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan dust collector dan filter low return bersih dan
                      terbebas dari debu serta dalam kondisi yang baik
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="dust_periodic"
                        inline
                        value={addLineClearanceForm.dust_periodic}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            dust_periodic: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            dust_periodic: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.dust_periodic && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.dust_periodic}
                      </p>
                    )}
                  </Form.Group>
                  <p>
                    Pastikan kebersihan area mesin High Shear Mixer Granulator
                    GEA PMA 800 (M0IGEA8000I) sesuai area part dibawah ini:
                  </p>
                  <ul>
                    <li>-Tidak terdapat sisa produk dari produk sebelumnya.</li>
                    <li>
                      -Tidak terdapat debu dan kotoran serta baik untuk
                      digunakan.
                    </li>
                  </ul>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>Discharge</Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="discharge"
                        inline
                        value={addLineClearanceForm.discharge}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            discharge: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            discharge: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.discharge && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.discharge}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Container / bowl mesin
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="bowl_machine"
                        inline
                        value={addLineClearanceForm.bowl_machine}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            bowl_machine: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            bowl_machine: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.bowl_machine && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.bowl_machine}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>Tutup container</Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="lid_container"
                        inline
                        value={addLineClearanceForm.lid_container}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            lid_container: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            lid_container: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.lid_container && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.lid_container}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>Product container</Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="product_container"
                        inline
                        value={addLineClearanceForm.product_container}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            product_container: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            product_container: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.product_container && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.product_container}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>lantai panggung</Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="floor"
                        inline
                        value={addLineClearanceForm.floor}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            floor: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            floor: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.floor && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.floor}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Filter dan rangka filter pastikan Juga kekuatan dan
                      kekencangan pemasangan klem
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="filter_clem"
                        inline
                        value={addLineClearanceForm.filter_clem}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            filter_clem: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            filter_clem: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.filter_clem && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.filter_clem}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>Panel mesin</Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="machine_panel"
                        inline
                        value={addLineClearanceForm.machine_panel}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            machine_panel: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            machine_panel: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.machine_panel && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.machine_panel}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>Spray Gun Binder</Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="spray_gun"
                        inline
                        value={addLineClearanceForm.spray_gun}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            spray_gun: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            spray_gun: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.spray_gun && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.spray_gun}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>Impeller dan chopper</Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="impeller_chopper"
                        inline
                        value={addLineClearanceForm.impeller_chopper}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            impeller_chopper: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            impeller_chopper: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.impeller_chopper && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.impeller_chopper}
                      </p>
                    )}
                  </Form.Group>
                  <p>
                    Pastian kebersihan arca mesin High Shear Mixer Granulator
                    GEA PMA 800 (M01GEA80001) sesuai arca / part di bawah ini:
                  </p>
                  <ul>
                    <li>Tidak terdapat sisa produk dari produk sebelumnya.</li>
                    <li>
                      Tidak terdapat debu dan kotoran serta baik untuk
                      digunakan.
                    </li>
                  </ul>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Konektor Granulator ke Wet mill
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="granulator_wetmill"
                        inline
                        value={addLineClearanceForm.granulator_wetmill}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            granulator_wetmill: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            granulator_wetmill: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.granulator_wetmill && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.granulator_wetmill}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Konektor antara selang transfer dan ayakan (jika
                      digunakan)
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="connector_sifting"
                        inline
                        value={addLineClearanceForm.connector_sifting}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            connector_sifting: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            connector_sifting: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.connector_sifting && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.connector_sifting}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>Pisau ayak/impeller</Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="impeller_sifting"
                        inline
                        value={addLineClearanceForm.impeller_sifting}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            impeller_sifting: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            impeller_sifting: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.impeller_sifting && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.impeller_sifting}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>Mesh</Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="mesh"
                        inline
                        value={addLineClearanceForm.mesh}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            mesh: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            mesh: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.mesh && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.mesh}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>Seal pada mesin ayak</Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="seal_sifting"
                        inline
                        value={addLineClearanceForm.seal_sifting}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            seal_sifting: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            seal_sifting: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.seal_sifting && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.seal_sifting}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Konektor antara ayakan dan FBD
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="fbd_sifting"
                        inline
                        value={addLineClearanceForm.fbd_sifting}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            fbd_sifting: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            fbd_sifting: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.fbd_sifting && (
                      <p className="absolute top-6 mt-1 text-red-500">
                        {errorsAddForm.fbd_sifting}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="relative flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan tidak ada label identitas mesin "SEDANG PROSES"
                      dari produk sebelumnya yang tertinggal di Arca High Shear
                      Mixer Granalator GEA PMA 800 (MOIGEA80001).
                    </Form.ControlLabel>
                    <div>
                      <RadioGroup
                        name="label_process"
                        inline
                        value={addLineClearanceForm.label_process}
                        onChange={(value) => {
                          setAddLineClearanceForm((prevFormValue) => ({
                            ...prevFormValue,
                            label_process: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            label_process: undefined,
                          }));
                        }}>
                        <Radio value={1}>Y</Radio>
                        <Radio value={0}>N/A</Radio>
                      </RadioGroup>
                    </div>
                    {errorsAddForm.label_process && (
                      <p className="absolute top-6 mt-3 text-red-500 ">
                        {errorsAddForm.label_process}
                      </p>
                    )}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddLineClearanceForm(emptyAddLineClearanceForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle">
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleCreateLineClearanceApi();
                  }}
                  appearance="primary"
                  type="submit">
                  Tambah
                </Button>
                {loading && (
                  <Loader
                    backdrop
                    size="md"
                    vertical
                    content="Inserting Line Clearance Data..."
                    active={loading}
                  />
                )}
              </Modal.Footer>
            </Modal>

            {/* edit modal */}
            <Modal
              backdrop="static"
              size={"lg"}
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditLineClearanceForm(emptyEditLineClearanceForm);
                setErrorsEditForm({});
              }}
              overflow={false}>
              <Modal.Header>
                <Modal.Title>Edit Line Clearance</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Batch Code</Form.ControlLabel>
                    <Form.Control
                      name="batch_code"
                      value={editLineClearanceForm.batch_code}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          batch_code: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          batch_code: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.batch_code && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.batch_code}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Product Code</Form.ControlLabel>
                    <Form.Control
                      name="product_code"
                      value={editLineClearanceForm.product_code}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          product_code: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          product_code: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.product_code && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.product_code}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      <p>Pembersihan Rutin, boleh ada sisa produk jika:</p>
                      <ul>
                        <li>a. Ganti batch prodak</li>
                        <li>b. Forula sama tetapi kode produk berbeda.</li>
                      </ul>
                    </Form.ControlLabel>
                    <RadioGroup
                      name="clean_routine"
                      inline
                      value={editLineClearanceForm.clean_routine}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          clean_routine: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          clean_routine: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.clean_routine && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.clean_routine}
                      </p>
                    )}
                  </Form.Group>{" "}
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan Kabel kalitrasi dan kualifikasi pada mesin masih
                      berlaku.
                    </Form.ControlLabel>
                    <RadioGroup
                      name="calibration_routine"
                      inline
                      value={editLineClearanceForm.calibration_routine}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          calibration_routine: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          calibration_routine: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.calibration_routine && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.calibration_routine}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan kebersihan lantai dan area mesin High Shear Mixer
                      Granulator GEA (PMA 800) (M01GEAS0001) serta lemari/ meja.
                      Pastikan tidak ada dokumen, prodak antara dan bahan /
                      material, dari produk sebelumnya.
                    </Form.ControlLabel>
                    <RadioGroup
                      name="area_routine"
                      inline
                      value={editLineClearanceForm.area_routine}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          area_routine: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          area_routine: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.area_routine && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.area_routine}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan tidak ada label identitas mesin "SEDANG PROSES"
                      dari produk sebelumnya yang tertinggal di ruangan.
                    </Form.ControlLabel>
                    <RadioGroup
                      name="label_routine"
                      inline
                      value={editLineClearanceForm.label_routine}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          label_routine: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          label_routine: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.label_routine && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.label_routine}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      <p>Pembersihan Berkala, Jila:</p>
                      <ul>
                        <li>a. Ganti produk</li>
                        <li>
                          b. Ganti batch tetapi sudah mencapai batas campaign
                          batch (sesuai Supporting Document).
                        </li>
                        <li>
                          c. Mesin tidak digunakan lebih dari hari yang sudah
                          ditetapkan (sesuai Supporting Document pembersihan
                          tiap mesin/ alat).
                        </li>
                      </ul>
                    </Form.ControlLabel>
                    <RadioGroup
                      name="clean_periodic"
                      inline
                      value={editLineClearanceForm.clean_periodic}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          clean_periodic: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          clean_periodic: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.clean_periodic && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.clean_periodic}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan label kalibrasi dan kualifikasi pada mesin masih
                      berlaku.
                    </Form.ControlLabel>
                    <RadioGroup
                      name="calibration_periodic"
                      inline
                      value={editLineClearanceForm.calibration_periodic}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          calibration_periodic: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          calibration_periodic: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.calibration_periodic && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.calibration_periodic}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan kebersihan lantai dan area mesin High Shear Mixer
                      Granulator GEA PMA 800 (M0IGEA8000I) serta lemari / meja.
                      Pastikan tidak ada dokumen, produk antara dan bahan /
                      material, dari produk sebelumnya.
                    </Form.ControlLabel>
                    <RadioGroup
                      name="area_periodic"
                      inline
                      value={editLineClearanceForm.area_periodic}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          area_periodic: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          area_periodic: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan label "SIAP PAKAI" tersedia dan masih berlaku.
                    </Form.ControlLabel>
                    <RadioGroup
                      name="label_preiodic"
                      inline
                      value={editLineClearanceForm.label_preiodic}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          label_preiodic: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          label_preiodic: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.label_preiodic && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.label_preiodic}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan dust collector dan filter low return bersih dan
                      terbebas dari debu serta dalam kondisi yang baik
                    </Form.ControlLabel>
                    <RadioGroup
                      name="dust_periodic"
                      inline
                      value={editLineClearanceForm.dust_periodic}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          dust_periodic: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          dust_periodic: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.dust_periodic && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.dust_periodic}
                      </p>
                    )}
                  </Form.Group>
                  <p>
                    Pastikan kebersihan area mesin High Shear Mixer Granulator
                    GEA PMA 800 (M0IGEA8000I) sesuai area part dibawah ini:
                  </p>
                  <ul>
                    <li>-Tidak terdapat sisa produk dari produk sebelumnya.</li>
                    <li>
                      -Tidak terdapat debu dan kotoran serta baik untuk
                      digunakan.
                    </li>
                  </ul>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>Discharge</Form.ControlLabel>
                    <RadioGroup
                      name="discharge"
                      inline
                      value={editLineClearanceForm.discharge}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          discharge: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          discharge: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.discharge && (
                      <p style={{ color: "red" }}>{errorsEditForm.discharge}</p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Container / bowl mesin
                    </Form.ControlLabel>
                    <RadioGroup
                      name="bowl_machine"
                      inline
                      value={editLineClearanceForm.bowl_machine}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          bowl_machine: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          bowl_machine: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.bowl_machine && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.bowl_machine}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>Tutup container</Form.ControlLabel>
                    <RadioGroup
                      name="lid_container"
                      inline
                      value={editLineClearanceForm.lid_container}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          lid_container: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          lid_container: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.lid_container && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.lid_container}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>Product container</Form.ControlLabel>
                    <RadioGroup
                      name="product_container"
                      inline
                      value={editLineClearanceForm.product_container}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          product_container: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          product_container: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.product_container && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.product_container}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>lantai panggung</Form.ControlLabel>
                    <RadioGroup
                      name="floor"
                      inline
                      value={editLineClearanceForm.floor}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          floor: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          floor: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.floor && (
                      <p style={{ color: "red" }}>{errorsEditForm.floor}</p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Filter dan rangka filter pastikan Juga kekuatan dan
                      kekencangan pemasangan klem
                    </Form.ControlLabel>
                    <RadioGroup
                      name="filter_clem"
                      inline
                      value={editLineClearanceForm.filter_clem}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          filter_clem: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          filter_clem: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.filter_clem && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.filter_clem}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>Panel mesin</Form.ControlLabel>
                    <RadioGroup
                      name="machine_panel"
                      inline
                      value={editLineClearanceForm.machine_panel}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          machine_panel: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          machine_panel: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.machine_panel && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.machine_panel}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>Spray Gun Binder</Form.ControlLabel>
                    <RadioGroup
                      name="spray_gun"
                      inline
                      value={editLineClearanceForm.spray_gun}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          spray_gun: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          spray_gun: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.spray_gun && (
                      <p style={{ color: "red" }}>{errorsEditForm.spray_gun}</p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>Impeller dan chopper</Form.ControlLabel>
                    <RadioGroup
                      name="impeller_chopper"
                      inline
                      value={editLineClearanceForm.impeller_chopper}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          impeller_chopper: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          impeller_chopper: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.impeller_chopper && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.impeller_chopper}
                      </p>
                    )}
                  </Form.Group>
                  <p>
                    Pastian kebersihan arca mesin High Shear Mixer Granulator
                    GEA PMA 800 (M01GEA80001) scsuai arca / part di bawah ini:
                  </p>
                  <ul>
                    <li>Tidak terdapat sisa produk dari produk sebelumnya.</li>
                    <li>
                      Tidak terdapat debu dan kotoran serta baik untuk
                      digunakan.
                    </li>
                  </ul>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Konektor Granulator ke Wet mill
                    </Form.ControlLabel>
                    <RadioGroup
                      name="granulator_wetmill"
                      inline
                      value={editLineClearanceForm.granulator_wetmill}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          granulator_wetmill: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          granulator_wetmill: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.granulator_wetmill && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.granulator_wetmill}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Konektor antara selang transfer dan ayakan (jika
                      digunakan)
                    </Form.ControlLabel>
                    <RadioGroup
                      name="connector_sifting"
                      inline
                      value={editLineClearanceForm.connector_sifting}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          connector_sifting: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          connector_sifting: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.connector_sifting && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.connector_sifting}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>Pisau ayak/impeller</Form.ControlLabel>
                    <RadioGroup
                      name="impeller_sifting"
                      inline
                      value={editLineClearanceForm.impeller_sifting}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          impeller_sifting: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          impeller_sifting: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.impeller_sifting && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.impeller_sifting}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>Mesh</Form.ControlLabel>
                    <RadioGroup
                      name="mesh"
                      inline
                      value={editLineClearanceForm.mesh}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          mesh: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          mesh: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.mesh && (
                      <p style={{ color: "red" }}>{errorsEditForm.mesh}</p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>Seal pada mesin ayak</Form.ControlLabel>
                    <RadioGroup
                      name="seal_sifting"
                      inline
                      value={editLineClearanceForm.seal_sifting}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          seal_sifting: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          seal_sifting: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.seal_sifting && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.seal_sifting}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Konektor antara ayakan dan FBD
                    </Form.ControlLabel>
                    <RadioGroup
                      name="fbd_sifting"
                      inline
                      value={editLineClearanceForm.fbd_sifting}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          fbd_sifting: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          fbd_sifting: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.fbd_sifting && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.fbd_sifting}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group className="flex justify-between items-center">
                    <Form.ControlLabel>
                      Pastikan tidak ada label identitas mesin "SEDANG PROSES"
                      dari produk sebelumnya yang tertinggal di Arca High Shear
                      Mixer Granalator GEA PMA 800 (MOIGEA80001).
                    </Form.ControlLabel>
                    <RadioGroup
                      name="label_process"
                      inline
                      value={editLineClearanceForm.label_process}
                      onChange={(value) => {
                        setEditLineClearanceForm((prevFormValue) => ({
                          ...prevFormValue,
                          label_process: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          label_process: undefined,
                        }));
                      }}>
                      <Radio value={1}>Y</Radio>
                      <Radio value={0}>N/A</Radio>
                    </RadioGroup>
                    {errorsEditForm.label_process && (
                      <p style={{ color: "red", marginTop: "20px" }}>
                        {errorsEditForm.label_process}
                      </p>
                    )}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditLineClearanceForm(editLineClearanceForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle">
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    if (editLineClearanceForm.status_approval !== 2) {
                      handleUpdateLineClearanceApi(2);
                    } else {
                      handleUpdateLineClearanceApi(
                        editLineClearanceForm.status_approval
                      );
                    }
                  }}
                  appearance="primary"
                  color={
                    editLineClearanceForm.status_approval === 2
                      ? "blue"
                      : "orange"
                  }
                  type="submit">
                  {editLineClearanceForm.status_approval === 2
                    ? "Edit"
                    : "Submit To Approval"}
                </Button>
                {loading && (
                  <Loader
                    backdrop
                    size="md"
                    vertical
                    content="Editing Data..."
                    active={loading}
                  />
                )}
              </Modal.Footer>
            </Modal>

            {/* delete modal */}
            <Modal
              backdrop="static"
              size={"md"}
              open={showDeleteModal}
              onClose={() => {
                setShowDeleteModal(false);
                setEditLineClearanceForm(emptyEditLineClearanceForm);
                setErrorsEditForm({});
              }}
              overflow={false}>
              <Modal.Header>
                <Modal.Title className="text-center">
                  Apakah Anda Yakin Ingin Menghapus Line Clearance Ini?
                </Modal.Title>
              </Modal.Header>
              <Modal.Body className=""></Modal.Body>
              <div className="flex justify-center gap-3">
                <Button
                  onClick={() => {
                    setShowDeleteModal(false);
                    setErrorsEditForm({});
                  }}
                  appearance="default"
                  size="lg">
                  Tidak
                </Button>
                <Button
                  onClick={() => {
                    handleDeleteLineClearanceApi();
                  }}
                  appearance="primary"
                  type="submit"
                  color="red"
                  size="lg">
                  Iya
                </Button>
              </div>

              {loading && (
                <Loader
                  backdrop
                  size="md"
                  vertical
                  content="Delete Data..."
                  active={loading}
                />
              )}
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </>
  );
}
