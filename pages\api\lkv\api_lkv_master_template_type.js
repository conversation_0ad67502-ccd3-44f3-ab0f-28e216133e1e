import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/template_type${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function LkvMasterTemplateType(){
    return{
        getAllMasterTemplateType: createApiFunction("get", "/get/all"),
        getAllMasterTemplateTypeActive : createApiFunction("get", "/get/all/active"),
        createMasterTemplateType: createApiFunction("post", "/create"),
        updateMasterTemplateType: createApiFunction("put", "/edit"),
        updateStatusMasterTemplateType: createApiFunction("put", "/active")
    }
}