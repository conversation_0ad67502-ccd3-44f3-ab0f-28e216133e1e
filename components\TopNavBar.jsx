import React from "react";
import { useRouter } from "next/router";
import { IconButton } from "rsuite";

import ArrowLeftLineIcon from "@rsuite/icons/ArrowLeftLine";
import CloseIcon from "@rsuite/icons/Close";

export default function TopNav({ title, icon, action }) {
  const router = useRouter();

  let iconComponent;
  if (icon === "back") {
    iconComponent = <ArrowLeftLineIcon />;
  } else if (icon === "close") {
    iconComponent = <CloseIcon />;
  } else {
    iconComponent = icon;
  }

  return (
    <div className="w-full bg-white flex items-center justify-self-start fixed top-0 z-[1000] shadow-sm pl-8 py-3">
      <IconButton
        icon={iconComponent}
        appearance="primary"
        size="sm"
        onClick={action ? action : () => router.back()}
      ></IconButton>
      <h5 className="font-semibold" style={{ marginLeft: "22px" }}>
        {title}
      </h5>
    </div>
  );
}
