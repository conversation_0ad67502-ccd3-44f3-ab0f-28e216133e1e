import { useEffect, useState } from "react";
import Head from "next/head"
import { <PERSON><PERSON><PERSON>rumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, SelectPicker, ButtonGroup, InputNumber } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import API_MasterDataReagenCapRack from "@/pages/api/master_data/api_master_data_rc_rack";
import ApiStockSummaryOra from "@/pages/api/stock_summary/api_stock_summary";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash"
import { useRouter } from "next/router";

export default function MasterDataStockSummaryPage() {

    const [moduleName, setModuleName] = useState("");
    const { HeaderCell, Cell, Column } = Table;
    const [capRack, setCapRack] = useState([]);
    const [stockSummary, setStockSummary] = useState([])
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [props, setProps] = useState([]);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showAddModal, setShowAddModal] = useState(false);
    const [dataCriteria, setDataCriteria] = useState([]);
    const [oracleStock, setStockOracleStock] = useState([])
    const emptyFormValue = {
        created_by :"",
        item_code  :"",
        item_desc  :"",
        uom        :"",
        min_qty    :0.0,
        max_qty    :0.0,
    }
    const [formValue, setFormValue] = useState(emptyFormValue);
    const toaster = useToaster();
    const emptyErrorValue = {
        created_by :null,
        item_code  :null,
        item_desc  :null,
        uom        :null,
        min_qty    :null,
        max_qty    :null,
    }
    const [errors, setErrors] = useState(emptyErrorValue);
    const router = useRouter();

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const handleGetAllApi = async () => {
        try {
            const resOra = await ApiStockSummaryOra().getOcList();
            if (resOra.status === 200) {
                const transformedData = resOra.data 
                ? resOra.data.map(item => ({
                    item_code: item.item_code,
                    item_desc: item.item_desc,
                    uom: item.uom,
                    quantity: item.quantity,
                    label: `${item.item_code} - ${item.item_desc}`,
                    status_kanban: item.status_kanban
                })).sort((a, b) => (a.status_kanban === "Y" ? -1 : 1)) // "Y" will come before "N"
                : [];
                setStockOracleStock(transformedData ? transformedData : [])
            }else if (resOra.status ===400){
                console.log("error on response", resOra.message)
            }else {
                console.log("error on response", resOra.message)
            }
        } catch (error) {
            console.log("error data ", error)
        }


        // const res = await API_MasterDataReagenCapRack().getAll();
        // console.log(res);
        // setCapRack(res.data ? res.data : []);
        // const resultCriteria = await API_MasterDataMasterDataReagenCriteria().getAllActive()
        // console.log("result ", resultCriteria)
        // setDataCriteria(resultCriteria.data ? resultCriteria.data : []);
    };

    const filteredData = oracleStock
        .filter((rowData, i) => {
            const searchFields = [
                "item_code",
                "item_desc",
                "uom",
                "quantity",
                "status_kanban"
            ];

            const matchesSearch = searchFields.some((field) =>
                rowData[field]
                    ?.toString()
                    .toLowerCase()
                    .includes(searchKeyword.toLowerCase())
            );

            return matchesSearch;
        })

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    }

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : oracleStock.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("stock_summary/master")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            handleGetAllApi();
        }
    }, []);

    const handleExportExcel = () => {

        if (oracleStock.length === 0) {
            toaster.push(Messages("error", "No data to export!"), {
                placement: "topCenter",
                duration: 5000,
            });
            return;
        }

        const headerMapping = {
            item_code: "item_code",
            item_desc: "item_desc",
            uom: "uom",
            quantity: "quantity",
            status_kanban: "status kanban"
        };

        const formattedData = oracleStock.map((item) => {
            const formattedItem = {};
            for (const key in item) {
                if (headerMapping[key]) {
                    if (key === 'is_active') {
                        formattedItem[headerMapping[key]] = item[key] === 1 ? 'Active' : 'Inactive';
                    } else {
                        formattedItem[headerMapping[key]] = item[key];
                    }
                }
            }
            return formattedItem;
        });

        const ws = XLSX.utils.json_to_sheet(formattedData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "data");

        const date = dateFormatterDash(new Date());
        const filename = `Master Data Reagen Stock Oracle Summary ${date}.xlsx`;

        XLSX.writeFile(wb, filename);
    }

    return (
        <>
            <div>
                <Head>
                    <title>Stock Oracle Summary</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>Stock Oracle Summary</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Mater Data</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Stock Oracle Summary </h5>
                                {/* <Stack className="flex gap-2">
                                    <ButtonGroup>
                                        <Button
                                            onClick={handleRackClick}
                                            appearance="primary"
                                            active>
                                            Master Rack
                                        </Button>
                                        <Button
                                            onClick={handleFloorClick}
                                            appearance="primary">
                                            Master Floor
                                        </Button>
                                        <Button
                                            appearance="primary"
                                            onClick={handleRowClick}>
                                            Master Row
                                        </Button>
                                    </ButtonGroup>
                                </Stack> */}
                            </Stack>}>
                    </Panel>

                    <Panel
                        bordered
                        bodyFill
                        header={
                            <Stack justifyContent="space-between">
                                <div className="flex gap-2">
                                    <IconButton
                                        icon={<FileDownloadIcon />}
                                        appearance="primary"
                                        onClick={handleExportExcel}
                                    >
                                        Download (.xlsx)
                                    </IconButton>
                                </div>

                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="search"
                                        value={searchKeyword}
                                        onChange={handleSearch}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}>
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                        }
                    >
                        <Table
                            bordered
                            cellBordered
                            height={400}
                            data={getPaginatedData(getFilteredData(), limit, page)}
                            sortColumn={sortColumn}
                            sortType={sortType}
                            onSortColumn={handleSortColumn}
                        >
                            <Column width={70} align="center" fixed>
                                <HeaderCell>No</HeaderCell>
                                <Cell>
                                    {(rowData, rowIndex) => {
                                        return rowIndex + 1 + limit * (page - 1);
                                    }}
                                </Cell>
                            </Column>
                            <Column width={150} align="center" sortable resizable>
                                <HeaderCell>Item Code</HeaderCell>
                                <Cell dataKey="item_code" />
                            </Column>
                            <Column width={300} sortable resizable fullText>
                                <HeaderCell>Description</HeaderCell>
                                <Cell dataKey="item_desc" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>UOM</HeaderCell>
                                <Cell dataKey="uom" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Quantity</HeaderCell>
                                <Cell dataKey="quantity" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Status Kanban</HeaderCell>
                                <Cell>
                                    {(rowData) => (
                                        <span
                                            style={{
                                                color: rowData.status_kanban === "Y" ? "green" : "red",
                                            }}
                                        >
                                            {rowData.status_kanban}
                                        </span>
                                    )}
                                </Cell>
                            </Column>
                        </Table>
                        <div style={{ padding: 20 }}>
                            <Pagination
                                prev
                                next
                                first
                                last
                                ellipsis
                                boundaryLinks
                                maxButtons={5}
                                size="xs"
                                layout={["total", "-", "limit", "|", "pager", "skip"]}
                                limitOptions={[10, 30, 50]}
                                total={totalRowCount}
                                limit={limit}
                                activePage={page}
                                onChangePage={setPage}
                                onChangeLimit={handleChangeLimit}
                            />
                        </div>
                    </Panel>
                </div>
            </ContainerLayout>
        </>
    )
}
