import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  useToaster,
  ButtonGroup,
  Loader,
  DatePicker,
  RadioGroup,
  Radio,
  Placeholder, Row, Col, FlexboxGrid, Progress, Header, SelectPicker, Badge, Whisper, Tooltip,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiStep from "@/pages/api/pqr/step/api_masterdata_step";
import PageEndIcon from '@rsuite/icons/PageEnd';
import PageTopIcon from '@rsuite/icons/PageTop';
import { useRouter } from "next/router";
import APIMaintenanceImpeller from "@/pages/api/maintenance/impeller/api_binding";
import { ArrowUp, ArrowDown } from '@rsuite/icons';
import { faL } from "@fortawesome/free-solid-svg-icons";
import ChartDataLabels from "chartjs-plugin-datalabels";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
  ChartDataLabels
);

import { Line } from "react-chartjs-2";
import ScheduleCarouselComponent from "@/components/maintenance/schedulerLayout";


export default function MasterdataStep() {
    const router = useRouter();
    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [averageImpellerData, setSetAverageImpellerData] = useState([]);

    const [averageImpellerAllData, setSetAverageImpellerAllData] = useState([]);

    const [rampTimeDashboardData, setRampTimeDashboardData] = useState({
      "date": null,
      "avg_duration": null,
      "total_avg_duration":null,
      "percentage": null,
      "flag_red": false,
  });

  const [dateImpeller, setDateImpeller] = useState([])
  const [selectedDateImpeller, setSelectedDateImpeller] = useState(null);

  const [highestOverspeed, setHighestOverspeed] = useState(null)

  const [latestBatchData, setLatestBatchData] = useState({
    "key_batch":null,
    "time_start":null,
    "time_end":null,
    "info_batch":null,
    "duration": null,
    "info_step":null,
})



  useEffect(() => {
    // Step 1: Sort and get the latest date from averageImpellerAllData
    const sortedDates = [...averageImpellerAllData].sort((a, b) => {
      const dateA = new Date(a.date.split('-').reverse().join('-'));
      const dateB = new Date(b.date.split('-').reverse().join('-'));
      return dateB - dateA;
    });

    
    // console.log(sortedDates[0])
    const latestDate = sortedDates[0]?.date; // e.g., "18-02-2025"
  
    // Step 2: Filter averageImpellerData to exclude records with the latest date
    const filteredData = averageImpellerData.filter((entry) => {
      const entryDate = entry.time_start.split(' ')[0]; // Extract "dd-mm-yyyy"
      return entryDate !== latestDate;
    });

    const latestData = averageImpellerData.filter((entry) => {
      const entryDate = entry.time_start.split(' ')[0];
      return entryDate === latestDate;
    });

    const lastItem = latestData[latestData.length - 1];

    setLatestBatchData(lastItem)
  
    // Step 3: Calculate average duration of the filtered data
    const totalDuration = filteredData.reduce((sum, entry) => sum + entry.duration, 0);
    const averageDuration = filteredData.length > 0
      ? (totalDuration / filteredData.length).toFixed(2)
      : '0.00';
  
    console.log('Average Duration (excluding latest date):', averageDuration);

    const avgDuration = Math.round(sortedDates[0]?.avg_duration || 0);
    const totalAvgDuration = Math.round(averageDuration || 1); // Hindari pembagian dengan nol

    const percentage = ((avgDuration - totalAvgDuration) / totalAvgDuration) * 100;
    const flagRed = avgDuration > totalAvgDuration;

    console.log({
      date: sortedDates[0]?.date,
      avg_duration: avgDuration,
      total_avg_duration: totalAvgDuration,
      percentage: percentage.toFixed(2),
      flag_red: flagRed
    });

    setRampTimeDashboardData({
      ...rampTimeDashboardData,
      date: sortedDates[0]?.date,
      avg_duration: avgDuration,
      total_avg_duration: totalAvgDuration,
      percentage: percentage.toFixed(2),
      flag_red: flagRed
    });


  }, [averageImpellerData, averageImpellerAllData]);
  



    const [overSpeedData, setOverspeedData] = useState([
      {
          "month": "2025-02",
          "week_in_month": "2",
          "total_records": 3584,
          "over_speed_count": 280,
          "overspeed_percentage": 7.81
      }
  ]);
  


    const [impellerData, setImpellerData] = useState([]);
    const [nextScheduleImpellerData, setNextScheduleImpellerData] = useState([]);
    const [impellerAssetData, setImpellerAssetData] = useState([]);


    const HandleImpellerData = async (flag, date) => {
      try {
        let res;
    
        if (flag) {
          res = await APIMaintenanceImpeller().getListImpellerData({
            flag: true,
          });
        } else {
          res = await APIMaintenanceImpeller().getListImpellerData({
            flag: false,
            date: date,
          });
        }
    
        if (res.status === 200) {
          
          const highestSpeed = Math.max(...res?.data?.map(item => item?.impeller_speed));
          setHighestOverspeed(highestSpeed);
          setImpellerData(res?.data);
          if (res?.data?.length > 0) {
            HandleGetAverageImpellerData();
          }
        } else {
          console.log("Error on HandleGetAverageImpellerData: ", res.message);
        }
      } catch (error) {
        console.log("Error on catch HandleGetAverageImpellerData: ", error.message);
      }
    };

    const HandleDateImpellerData = async() =>{      
      try {
        const res = await APIMaintenanceImpeller().getListDate();
        if (res.status === 200) {
          setDateImpeller(res?.data);
          setSelectedDateImpeller(res?.data[0])
        } else {
          console.log("Error on HandleGetAverageImpellerData: ", res.message);
        }
      } catch (error) {
        console.log("Error on catch HandleGetAverageImpellerData: ", error.message);
      }
    }
    

  const HandleGetAverageImpellerData = async () => {
    try {
      const res = await APIMaintenanceImpeller().getAverageImpeller();
      if (res.status === 200) {
        setSetAverageImpellerData(res?.data?.average_all);
        setSetAverageImpellerAllData(res?.data?.average_ramp_all);
      } else {
        console.log("Error on HandleGetAverageImpellerData: ", res.message);
      }
    } catch (error) {
      console.log("Error on catch HandleGetAverageImpellerData: ", error.message);
    }
  };

  const HandleGetMaxImpeller = async () => {
    try {
      const res = await APIMaintenanceImpeller().getOverspeed();
      if (res.status === 200) {
        const sortedData = res.data.sort((a, b) => {
          if (a.month !== b.month) {
            return b.month.localeCompare(a.month); // sort month descending
          }
          return b.week_in_month - a.week_in_month; // sort week descending
        });
  
        setOverspeedData(res.data);
      } else {
        console.log("Error on HandleGetMaxImpeller: ", res.message);
      }
    } catch (error) {
      console.log("Error on catch HandleGetMaxImpeller: ", error.message);
    }
  };

  const HandleGetNextScheduler = async () => {
    try {
      const res = await APIMaintenanceImpeller().getImpellerNextSchedule();
      if (res.status === 200) {
        setNextScheduleImpellerData(res.data);
      } else {
        console.log("Error on HandleGetNextScheduler: ", res.message);
      }
    } catch (error) {
      console.log("Error on catch HandleGetNextScheduler: ", error.message);
    }
  };

  const HandleGetImpellerAsset = async () => {
    try {
      const res = await APIMaintenanceImpeller().getImpellerAsset();
      if (res.status === 200) {
        setImpellerAssetData(res.data);
      } else {
        console.log("Error on HandleGetImpellerAsset: ", res.message);
      }
    } catch (error) {
      console.log("Error on catch HandleGetImpellerAsset: ", error.message);
    }
  };
  
  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push("/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      const fetchData = () => {
        // HandleGetAverageImpellerData(); // uncomment if needed
        HandleGetMaxImpeller();
        HandleImpellerData(true);
        HandleDateImpellerData();
        HandleGetNextScheduler();
        HandleGetImpellerAsset();
      };

      // Call once immediately
      fetchData();

      // Then every 10 minutes
      const interval = setInterval(fetchData, 10 * 60 * 1000); // 10 minutes

      // Clear interval on unmount
      return () => clearInterval(interval);
    }
  }, []);


  const prepareImpellerDashboardChartData = (data) => {
    // const labels = data.map((item) => item.info + " " + item.status_ipc_ma);

    const labels = data.map((item) => {
      // Split by space and take everything after the date
      const parts = item.time.split(" ");
      return parts.slice(1).join(" "); // join remaining parts to get "00:51 - 01:20"
    });    
    const avg = data.map((item) => item.impeller_speed);
    const min = data.map((item) => item.target_speed);
    const max = data.map((item) => 170);

    return {
      labels,
      datasets: [
        {
          label: "Impeller Speed",
          data: avg,
          backgroundColor: "#FF0000",
          borderColor: "#FF0000",
        },
        {
          label: "Max Machine RPM",
          data: max,
          backgroundColor: "#0000FF",
          borderColor: "#0000FF",
        },
        {
          label: "Max Recipe RPM",
          data: min,
          backgroundColor: "#FFA507",
          borderColor: "#FFA500",
        },
      ],
    };
  };

  const LineWeightChart = ({ chartData, title }) => {
    const options = {
      responsive: true,
      maintainAspectRatio: false, // <-- important
      plugins: {
        legend: {
          position: "bottom",
        },
        title: {
          display: true,
          h5: title,
          padding: {
            bottom: 50,
            top: 20,
          },
        },
        datalabels: false, // disable to test

      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };
    

    return (
      <div style={{ height: "400px", width: "100%" }}>
        <Line data={chartData} options={options} />
      </div>
    );
    
  };

  const ScheduleCarousel = ({ schedules }) => {
    const [activeIndex, setActiveIndex] = useState(0);

    useEffect(()=>{})
  
    // Sort schedules by date then priority
    const sortedSchedules = [...schedules].sort((a, b) => {
      const dateA = new Date(a?.scheduled_start_date || 0);
      const dateB = new Date(b?.scheduled_start_date || 0);
      if (dateA - dateB !== 0) return dateA - dateB;
      
      const priorityOrder = { high: 1, medium: 2, low: 3 };
      const priorityA = priorityOrder[a?.priority_disp?.toLowerCase()] || 4;
      const priorityB = priorityOrder[b?.priority_disp?.toLowerCase()] || 4;
      return priorityA - priorityB;
    });
    useEffect(() => {
      const interval = setInterval(() => {
        setActiveIndex((prev) =>
          prev === sortedSchedules.length - 1 ? 0 : prev + 1
        );
      }, 5000);
    
      return () => clearInterval(interval);
    }, [sortedSchedules.length]);
  
    const nextSlide = () => {
      setActiveIndex((prev) => (prev === sortedSchedules.length - 1 ? 0 : prev + 1));
    };
  
    const prevSlide = () => {
      setActiveIndex((prev) => (prev === 0 ? sortedSchedules.length - 1 : prev - 1));
    };
  
    if (!sortedSchedules || sortedSchedules.length === 0) {
      return (
        <Panel bordered style={{ h5Align: 'center', padding: '20px', color: '#666' }}>
          No upcoming schedules available
        </Panel>
      );
    }
  
    const currentItem = sortedSchedules[activeIndex];
  
    return (
      <div style={{ position: 'relative' }}>
        {/* Navigation Arrows */}
        {sortedSchedules.length > 1 && (
          <>
            <IconButton 
              icon={<PageTopIcon />} 
              onClick={prevSlide}
              circle
              size="sm"
              style={{ 
                position: 'absolute',
                left: -10,
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 2,
                backgroundColor: '#fff',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
              }} 
            />
            <IconButton 
              icon={<PageEndIcon/>} 
              onClick={nextSlide}
              circle
              size="sm"
              style={{ 
                position: 'absolute',
                right: -10,
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 2,
                backgroundColor: '#fff',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
              }} 
            />
          </>
        )}
  
        {/* Carousel Panel */}
        <Panel
          key={activeIndex}
          bordered
          style={{
            marginBottom: '12px',
            borderRadius: '6px',
            borderLeft: `4px solid ${getPriorityColor(currentItem?.priority_disp)}`,
            minHeight: '180px'
          }}
          header={
            <div style={{ 
              display: 'flex', 
              alignItems: 'center',
              padding: '8px 12px',
              backgroundColor: '#f8f9fa'
            }}>
              <span style={{ fontWeight: '600', fontSize: '0.9rem' }}>
                Upcoming: {currentItem?.scheduled_start_date ? formatDate(currentItem.scheduled_start_date) : 'No date'}
              </span>
            </div>
          }
        >
          <div style={{ padding: '12px' }}>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              marginBottom: '8px'
            }}>
              <span style={{ fontSize: '0.85rem' }}>
                Priority: <strong style={{ 
                  color: getPriorityColor(currentItem?.priority_disp),
                  padding: '2px 8px',
                  backgroundColor: `${getPriorityColor(currentItem?.priority_disp)}20`,
                  borderRadius: '4px'
                }}>
                  {currentItem?.priority_disp}
                </strong>
              </span>
            </div>
            
            <div style={{ 
              display: 'flex',
              alignItems: 'flex-start',
              marginBottom: '4px'
            }}>
              <span style={{ fontSize: '0.85rem', color: '#666' }}>
                {currentItem?.asset_description || 'No description available'}
              </span>
            </div>
  
            {/* Indicator dots */}
            {sortedSchedules.length > 1 && (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                marginTop: '16px',
                gap: '6px'
              }}>
                {sortedSchedules.map((_, index) => (
                  <div
                    key={index}
                    onClick={() => setActiveIndex(index)}
                    style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: index === activeIndex ? 
                        getPriorityColor(currentItem?.priority_disp) : '#ddd',
                      cursor: 'pointer',
                      transition: 'background-color 0.3s'
                    }}
                  />
                ))}
              </div>
            )}
          </div>
        </Panel>
      </div>
    );
  };

  
const formatDate = (dateString) => {
  if (!dateString) return '';
  const d = new Date(dateString);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  return `${day}-${month}-${year}`;
};

const getPriorityColor = (priority) => {
  switch(priority?.toLowerCase()) {
    case 'high': return '#e74c3c';
    case 'medium': return '#f39c12';
    case 'low': return '#2ecc71';
    default: return '#3498db';
  }
}

  return (
    <div>
      <div>
        <Head>
          <title>Dashboard PMA 800</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>PMA 800</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Dashboard PMA 800</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill             
            >
             <Row style={{ marginBottom: 16 }}>
  <Col md={24}>
    <Panel bordered>
      <FlexboxGrid align="middle">
        {/* Current Batch Code */}
        <FlexboxGrid.Item colspan={8} style={{ padding: '12px 16px' }}>
          <FlexboxGrid align="middle">
            <FlexboxGrid.Item>
              <h5 bold>Current Batch Code:</h5>
            </FlexboxGrid.Item>
            <FlexboxGrid.Item style={{ marginLeft: 8 }}>
              <Tag color="red">{latestBatchData?.info_batch || "-"}</Tag>
            </FlexboxGrid.Item>
          </FlexboxGrid>
        </FlexboxGrid.Item>

        {/* Current Step */}
        <FlexboxGrid.Item colspan={8} style={{ padding: '12px 16px', borderLeft: '1px solid #f0f0f0' }}>
          <FlexboxGrid align="middle">
            <FlexboxGrid.Item>
              <h5 bold>Current Step:</h5>
            </FlexboxGrid.Item>
            <FlexboxGrid.Item style={{ marginLeft: 8 }}>
              <Tag color="blue">{latestBatchData?.info_step || "-"}</Tag>
            </FlexboxGrid.Item>
          </FlexboxGrid>
        </FlexboxGrid.Item>

        {/* Time Until Next Refresh */}
        {/* <FlexboxGrid.Item colspan={8} style={{ padding: '12px 16px', borderLeft: '1px solid #f0f0f0' }}>
          <FlexboxGrid align="middle">
            <FlexboxGrid.Item>
              <h5 bold>Time Until Next Refresh:</h5>
            </FlexboxGrid.Item>
            <FlexboxGrid.Item style={{ marginLeft: 8 }}>
              <Tag color="orange">
              </Tag>
            </FlexboxGrid.Item>
          </FlexboxGrid>
        </FlexboxGrid.Item> */}
      </FlexboxGrid>
    </Panel>
  </Col>
</Row>

              <Row style={{ display: 'flex', flexWrap: 'wrap' }}>
                <Col md={6} sm={6} style={{ display: 'flex', flexDirection: 'column' }}>
                <Panel style={{ flexGrow: 1 }} bordered header={<span style={{ fontWeight: 'bold' }}>Ramp Time</span>}>
<FlexboxGrid align="middle">
      <FlexboxGrid.Item colspan={14} style={{ display: 'flex', flexDirection: 'column' }}>
        <p style={{ 
          margin: '0 0 12px 0', 
          fontSize: '0.875rem', 
          color: '#666',
          h5Align: 'center',
          fontWeight: 'bold'
        }}>
          PERCENTAGE RATE
        </p>

        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          gap: '24px'
        }}>
          {/* Manual Circle with Percentage and Arrow Side by Side */}
          <div style={{
              position: 'relative',
              width: '120px',
              height: '120px',
              borderRadius: '50%',
              border: `8px solid ${rampTimeDashboardData.flag_red ? '#F44336'  :'#4CAF50' }`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '6px',
                color: rampTimeDashboardData.flag_red ? '#F44336'  :'#4CAF50' 
              }}>
                <span style={{
                  fontSize: '1.25rem',
                  fontWeight: 700
                }}>
                  {rampTimeDashboardData.percentage}%
                </span>
                <div style={{
                  fontSize: '1.5rem'
                }}>
                  { rampTimeDashboardData.flag_red ? <ArrowUp />  :  <ArrowDown />}
                </div>
              </div>
            </div>
        </div>
      </FlexboxGrid.Item>
      
      <FlexboxGrid.Item colspan={10}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <div>
            <p style={{ margin: 0, fontSize: '0.875rem', color: '#666' }}>Current Ramp Time</p>
            <p style={{ margin: 0, fontSize: '1.5rem' }}><b>{rampTimeDashboardData.avg_duration} </b>min</p>
          </div>
          <div>
            <p style={{ margin: 0, fontSize: '0.875rem', color: '#666' }}>Average Ramp Time</p>
            <p style={{ margin: 0, fontSize: '1.5rem' }}><b>{rampTimeDashboardData.total_avg_duration} </b>min</p>
          </div>
        </div>
      </FlexboxGrid.Item>
    </FlexboxGrid>
  </Panel>
                </Col>

                <Col md={6} sm={6} style={{ display: 'flex', flexDirection: 'column' }}>
                <Panel style={{ flexGrow: 1 }} bordered header={<span style={{ fontWeight: 'bold' }}>Max Speed</span>}>
                      <FlexboxGrid align="middle">
                        <FlexboxGrid.Item colspan={14} style={{ display: 'flex', flexDirection: 'column' }}>
                          <p style={{ 
                            margin: '0 0 12px 0', 
                            fontSize: '0.875rem', 
                            color: '#666',
                            h5Align: 'center',
                            fontWeight: 'bold'
                          }}>
                            FREQUENCY RATE
                          </p>

                          <div style={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            justifyContent: 'center',
                            gap: '24px'
                          }}>
                            {/* Manual Circle with Percentage and Arrow Side by Side */}
                            <div style={{
                                position: 'relative',
                                width: '120px',
                                height: '120px',
                                borderRadius: '50%',
                                border: `8px solid ${overSpeedData?.[0]?.over_speed_count <= 170 ? '#4CAF50' : '#F44336'}`,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}>
                                <div style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  gap: '6px',
                                  color: overSpeedData?.[0]?.over_speed_count <= 170 ? '#4CAF50' : '#F44336'
                                }}>
                                  <span style={{
                                    fontSize: '1.25rem',
                                    fontWeight: 700
                                  }}>
                                    {overSpeedData?.[0]?.overspeed_percentage}%
                                  </span>
                                  <div style={{
                                    fontSize: '1.5rem'
                                  }}>
                                    {overSpeedData?.[0]?.over_speed_count <= 170 ? <ArrowDown /> :  <ArrowUp /> }
                                  </div>
                                </div>
                              </div>
                          </div>
                        </FlexboxGrid.Item>
                        
                        <FlexboxGrid.Item colspan={10}>
                          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                            <div>
                              <p style={{ margin: 0, fontSize: '0.875rem', color: '#666' }}>Current Max Speed Captured</p>
                              <p style={{ margin: 0, fontSize: '1.5rem' }}><b>{highestOverspeed} </b>rpm</p>
                            </div>
                            <div>
                              <p style={{ margin: 0, fontSize: '0.875rem', color: '#666' }}>Max Speed Allowed</p>
                              <p style={{ margin: 0, fontSize: '1.5rem' }}><b>170 </b>rpm</p>
                            </div>
                          </div>
                        </FlexboxGrid.Item>
                      </FlexboxGrid>
                    </Panel>
                </Col>
                
                <Col md={6} sm={6} style={{ display: 'flex', flexDirection: 'column' }}>
                  <ScheduleCarouselComponent schedules={nextScheduleImpellerData} />
                </Col>
                
                <Col md={6} sm={6} style={{ display: 'flex', flexDirection: 'column' }}>
                  <Panel
                    bordered
                    style={{
                      flex: 1, // Make Panel fill the height of the Col
                      borderRadius: '6px',
                      borderLeft: '4px solid #2ecc71',
                      display: 'flex',
                      flexDirection: 'column',
                      height: '100%', // Ensure the panel fills the column height
                    }}
                    header={
                      <div style={{ 
                        display: 'flex', 
                        alignItems: 'center',
                        padding: '8px 12px',
                        backgroundColor: '#f8f9fa',
                      }}>
                        <span style={{ fontWeight: '600', fontSize: '0.9rem' }}>
                          Last Maintenance {impellerAssetData?.[0]?.formatted_date
                            ? formatDate(impellerAssetData[0].formatted_date)
                            : 'No date available'}
                        </span>
                      </div>
                    }
                  >
                    <div style={{ padding: '12px' }}>
                      <div style={{ marginBottom: '8px' }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          marginBottom: '6px',
                        }}>
                          <span style={{ fontSize: '0.85rem', color: '#666' }}>Maintained Assets</span>
                        </div>
                        <div style={{
                          backgroundColor: '#f8f9fa',
                          padding: '8px',
                          borderRadius: '4px',
                        }}>
                          {impellerAssetData?.[0]?.asset_desc?.length > 0 ? (
                            impellerAssetData[0].asset_desc.map((item, index) => (
                              <div key={index} style={{
                                display: 'flex',
                                alignItems: 'center',
                                padding: '4px 0',
                                borderBottom: index < impellerAssetData[0].asset_desc.length - 1 ? '1px solid #eee' : 'none',
                              }}>
                                <span style={{
                                  display: 'inline-block',
                                  width: '20px',
                                  color: '#7f8c8d',
                                  fontSize: '0.8rem',
                                }}>
                                  {index + 1}.
                                </span>
                                <span style={{ fontSize: '0.85rem' }}>{item}</span>
                              </div>
                            ))
                          ) : (
                            <div style={{ color: '#95a5a6', fontSize: '0.85rem' }}>No assets maintained</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </Panel>
                </Col>
              </Row>


               <Row>
                  <Col md={24} sm={24}>
                  <Panel
                      bordered
                      header={
                        <div style={{ width: '100%' }}>
                          <FlexboxGrid justify="space-between" align="middle">
                            <FlexboxGrid.Item>
                              <span style={{ fontWeight: 'bold' }}>Dashboard Ramp</span>
                            </FlexboxGrid.Item>
                            <FlexboxGrid.Item>
                            <SelectPicker
                                data={dateImpeller.map(date => ({ label: date, value: date }))}
                                value={selectedDateImpeller}
                                style={{ minWidth: 200 }}
                                onChange={(value) => {
                                  if (value) {
                                    HandleImpellerData(false, value); 
                                    setSelectedDateImpeller(value) 
                                  }                                  
                                }}
                              />
                            </FlexboxGrid.Item>
                          </FlexboxGrid>
                        </div>
                      }
                      style={{ height: '100%' }}
                    >

                    <LineWeightChart
                      chartData={prepareImpellerDashboardChartData(impellerData)}
                      plugins={[ChartDataLabels]}
                    />
                  </Panel>
                  </Col>
                  </Row>              
            </Panel>            
          </div>
        </div>
      </ContainerLayout>
    </div>
  );

}