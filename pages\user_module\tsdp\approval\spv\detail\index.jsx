import React, { useState, useEffect } from "react";
import Head from "next/head";
import { 
  Breadcrumb, 
  Panel, 
  Stack, 
  Table, 
  Tag, 
  Divider, 
  Form, 
  useToaster, 
  Loader,
  Modal,
  Button,
  ButtonToolbar,
  IconButton,
  Input,
  Checkbox,
  Pagination
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import ApiTsdpTH from "@/pages/api/tsdp/transaction_h/api_tsdp_transaction_h";
import ApiTsdpTDAttachments from "@/pages/api/tsdp/api_tsdp_td_attachments";
import ApiTsdpAuto from "@/pages/api/tsdp/api_tsdp_td_auto";
import ApiTsdpManual from "@/pages/api/tsdp/api_tsdp_td_manual";
import { useRouter } from "next/router";
import Messages from '@/components/Messages';
import EditIcon from '@rsuite/icons/Edit';
import PagePreviousIcon from '@rsuite/icons/PagePrevious';
import CheckIcon from '@rsuite/icons/Check';
import CloseIcon from '@rsuite/icons/Close';

import ApiApproval from "@/pages/api/tsdp/approval/api_tsdp_approval";

export default function TransactionApprovalDetail() {
  const { HeaderCell, Cell, Column } = Table;
  const [transaction, setTransaction] = useState(null);
  const [loading, setLoading] = useState(true);
  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [activeModal, setActiveModal] = useState(null);
  const [activeDetail, setActiveDetail] = useState(null);
  const [selectedDetails, setSelectedDetails] = useState({
    binder: [],
    drying: [],
    granulasi: [],
    finalMix: [],
    sifting: []
  });
  const [approving, setApproving] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [transactionAutoDetails, setTransactionAutoDetails] = useState([]);
  const [transactionManualDetails, setTransactionManualDetails] = useState([]);
  const router = useRouter();
  const toaster = useToaster();
  const [autoPage, setAutoPage] = useState(1);
  const [manualPage, setManualPage] = useState(1);
  const limit = 10;
  const [autoSortColumn, setAutoSortColumn] = useState();
  const [autoSortType, setAutoSortType] = useState();
  const [manualSortColumn, setManualSortColumn] = useState();
  const [manualSortType, setManualSortType] = useState();
  const [isSortingAuto, setIsSortingAuto] = useState(false);
  const [isSortingManual, setIsSortingManual] = useState(false);
  const [passwordModalOpen, setPasswordModalOpen] = useState(false);
  const [password, setPassword] = useState("");
  const [pendingApproval, setPendingApproval] = useState(null); // null, 'approve', or 'reject'
  const [revisedRemarks, setRevisedRemarks] = useState("");

  const formatDate = (dateString) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("en-GB");
  };

  const getStatusLabel = (statusCode) => {
    switch (statusCode) {
      case 1: return { label: "Active", color: "green" };
      case 0: return { label: "Inactive", color: "red" };
      default: return { label: "Unknown", color: "gray" };
    }
  };

  const getFilteredAutoDetails = (headerId, type) => {
    return transactionAutoDetails.filter(detail => 
      detail.id_header_trans === headerId && 
      detail.type.toLowerCase() === type.toLowerCase()
    );
  };

  const getFilteredManualDetails = (headerId, type) => {
    return transactionManualDetails.filter(detail => 
      detail.id_header_trans === headerId && 
      detail.type.toLowerCase() === type.toLowerCase()
    );
  };

  const handleAutoSortColumn = (sortColumn, sortType) => {
    setIsSortingAuto(true);
    setAutoSortColumn(sortColumn);
    setAutoSortType(sortType);
    setTimeout(() => setIsSortingAuto(false), 400);
  };
  const handleManualSortColumn = (sortColumn, sortType) => {
    setIsSortingManual(true);
    setManualSortColumn(sortColumn);
    setManualSortType(sortType);
    setTimeout(() => setIsSortingManual(false), 400);
  };

  const renderTransactionDetailTables = (headerId, type) => {
    let autoDetailsAll = getFilteredAutoDetails(headerId, type);
    let manualDetailsAll = getFilteredManualDetails(headerId, type);

    if (autoSortColumn && autoSortType) {
      autoDetailsAll = [...autoDetailsAll].sort((a, b) => {
        if (autoSortType === 'asc') return a[autoSortColumn] - b[autoSortColumn];
        return b[autoSortColumn] - a[autoSortColumn];
      });
    }
    if (manualSortColumn && manualSortType) {
      manualDetailsAll = [...manualDetailsAll].sort((a, b) => {
        if (manualSortType === 'asc') return a[manualSortColumn] - b[manualSortColumn];
        return b[manualSortColumn] - a[manualSortColumn];
      });
    }

    const autoDetails = autoDetailsAll.slice((autoPage - 1) * limit, autoPage * limit);
    const manualDetails = manualDetailsAll.slice((manualPage - 1) * limit, manualPage * limit);

    return (
      <>
        {autoDetailsAll.length > 0 && (
          <>
            <Divider>Inserted Details Automate</Divider>
            <Panel bordered>
              <Table
                data={autoDetails}
                autoHeight
                loading={isSortingAuto}
                sortColumn={autoSortColumn}
                sortType={autoSortType}
                onSortColumn={handleAutoSortColumn}
              >
                <Column width={170} resizable sortable dataKey="id_detail_trans_a">
                  <HeaderCell>ID Detail Trans Auto</HeaderCell>
                  <Cell dataKey="id_detail_trans_a" />
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Parameter</HeaderCell>
                  <Cell dataKey="parameter" />
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Value</HeaderCell>
                  <Cell dataKey="value" />
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Description</HeaderCell>
                  <Cell dataKey="description" />
                </Column>
                {/* <Column width={200} resizable>
                  <HeaderCell>Created At</HeaderCell>
                  <Cell>{(rowData) => formatDate(rowData.created_at)}</Cell>
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Updated At</HeaderCell>
                  <Cell>{(rowData) => formatDate(rowData.updated_at)}</Cell>
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Deleted At</HeaderCell>
                  <Cell>{(rowData) => formatDate(rowData.deleted_at)}</Cell>
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Created By</HeaderCell>
                  <Cell dataKey="created_by" />
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Updated By</HeaderCell>
                  <Cell dataKey="updated_by" />
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Deleted By</HeaderCell>
                  <Cell dataKey="deleted_by" />
                </Column> */}
              </Table>
              <div className="flex justify-end my-2">
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="sm"
                  total={autoDetailsAll.length}
                  limit={limit}
                  activePage={autoPage}
                  onChangePage={setAutoPage}
                />
              </div>
            </Panel>
          </>
        )}

        {manualDetailsAll.length > 0 && (
          <>
            <Divider>Inserted Details Manual</Divider>
            <Panel bordered>
              <Table
                data={manualDetails}
                autoHeight
                loading={isSortingManual}
                sortColumn={manualSortColumn}
                sortType={manualSortType}
                onSortColumn={handleManualSortColumn}
              >
                <Column width={170} resizable sortable dataKey="id_detail_trans_m">
                  <HeaderCell>ID Detail Trans Manual</HeaderCell>
                  <Cell dataKey="id_detail_trans_m" />
                </Column>
                <Column width={180} resizable>
                  <HeaderCell>Parameter</HeaderCell>
                  <Cell dataKey="parameter_name" />
                </Column>
                <Column width={90} resizable>
                  <HeaderCell>Value</HeaderCell>
                  <Cell dataKey="value" />
                </Column>
                <Column width={80} resizable>
                  <HeaderCell>UOM</HeaderCell>
                  <Cell dataKey="parameter_uom" />
                </Column>
                <Column width={120} resizable>
                  <HeaderCell>Category</HeaderCell>
                  <Cell dataKey="main_category_name" />
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Created At</HeaderCell>
                  <Cell>{(rowData) => formatDate(rowData.created_at)}</Cell>
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Updated At</HeaderCell>
                  <Cell>{(rowData) => formatDate(rowData.updated_at)}</Cell>
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Deleted At</HeaderCell>
                  <Cell>{(rowData) => formatDate(rowData.deleted_at)}</Cell>
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Created By</HeaderCell>
                  <Cell dataKey="created_by" />
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Updated By</HeaderCell>
                  <Cell dataKey="updated_by" />
                </Column>
                <Column width={200} resizable>
                  <HeaderCell>Deleted By</HeaderCell>
                  <Cell dataKey="deleted_by" />
                </Column>
              </Table>
              <div className="flex justify-end my-2">
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="sm"
                  total={manualDetailsAll.length}
                  limit={limit}
                  activePage={manualPage}
                  onChangePage={setManualPage}
                />
              </div>
            </Panel>
          </>
        )}
      </>
    );
  };

  const openModal = (modalType, detailData) => {
    setActiveModal(modalType);
    setActiveDetail(detailData);
  };

  const closeModal = () => {
    setActiveModal(null);
    setActiveDetail(null);
  };

  const fetchAttachmentsData = async () => {
    try {
      const api = ApiTsdpTDAttachments();
      const response = await api.getAllTransactionAttachment();
      if (response.status === 200) {
        setAttachments(response.data || []);
      } else {
        toaster.push(Messages("error", response.message || "Failed to fetch attachments"), { duration: 3000 });
      }
    } catch (error) {
      console.error("Error fetching attachments:", error);
      toaster.push(Messages("error", "Failed to fetch attachments"), { duration: 3000 });
    }
  };

  const fetchTransactionAutoDetails = async () => {
    try {
      const api = ApiTsdpAuto();
      const response = await api.getAllTransactionAutoDetail();
      if (response.status === 200) {
        setTransactionAutoDetails(response.data || []);
      } else {
        toaster.push(Messages("error", response.message || "Failed to fetch auto details"), { duration: 3000 });
      }
    } catch (error) {
      console.error("Error fetching auto details:", error);
      toaster.push(Messages("error", "Failed to fetch auto details"), { duration: 3000 });
    }
  };

  const fetchTransactionManualDetails = async () => {
    try {
      const api = ApiTsdpManual();
      const response = await api.getAllTransactionManualDetail();
      if (response.status === 200) {
        setTransactionManualDetails(response.data || []);
      } else {
        toaster.push(Messages("error", response.message || "Failed to fetch manual details"), { duration: 3000 });
      }
    } catch (error) {
      console.error("Error fetching manual details:", error);
      toaster.push(Messages("error", "Failed to fetch manual details"), { duration: 3000 });
    }
  };

  const toggleDetailSelection = (type, id) => {
    setSelectedDetails(prev => {
      const currentSelection = [...prev[type]];
      const index = currentSelection.indexOf(id);
      
      if (index === -1) {
        currentSelection.push(id);
      } else {
        currentSelection.splice(index, 1);
      }
      
      return {
        ...prev,
        [type]: currentSelection
      };
    });
  };

  const toggleAllDetails = (type, details) => {
    setSelectedDetails(prev => {
      // If all are already selected, deselect all
      if (details.every(detail => prev[type].includes(detail.id_detail_trans))) {
        return {
          ...prev,
          [type]: []
        };
      }
      
      // Otherwise select all
      return {
        ...prev,
        [type]: details.map(detail => detail.id_detail_trans)
      };
    });
  };

const handleApprove = (approve) => {
  // First check if any details are selected
  const allSelectedDetails = [
    ...selectedDetails.binder,
    ...selectedDetails.drying,
    ...selectedDetails.granulasi,
    ...selectedDetails.finalMix,
    ...selectedDetails.sifting
  ];

  if (allSelectedDetails.length === 0) {
    toaster.push(Messages("warning", "Please select at least one detail to approve/reject"), { duration: 3000 });
    return;
  }

  // Store the pending approval action and open password modal
  setPendingApproval(approve ? 'approve' : 'reject');
  setPassword("");
  setPasswordModalOpen(true);
};

const confirmApproval = async () => {
    try {
      setApproving(true);

      const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
      const employeeID = dataLogin?.employee_id;

      if (!employeeID) {
        toaster.push(Messages("warning", "Employee ID not found. Please log in again."), { duration: 3000 });
        return;
      }
      if (!password) {
        toaster.push(Messages("warning", "Please enter your password"), { duration: 3000 });
        return;
      }
      if (pendingApproval === 'reject' && !revisedRemarks.trim()) {
        toaster.push(Messages("warning", "Please enter rejection remarks"), { duration: 3000 });
        return;
      }

      // 1. Buat objek data dasar yang sama untuk approve & reject
      let requestData = {
        id_header_trans: parseInt(router.query.id_header_trans),
        binder_details: selectedDetails.binder,
        drying_details: selectedDetails.drying,
        granulasi_details: selectedDetails.granulasi,
        final_mix_details: selectedDetails.finalMix,
        sifting_details: selectedDetails.sifting,
        password: password,
      };

      let apiEndpoint;

      // 2. Tambahkan field spesifik berdasarkan aksi (Approve atau Reject)
      if (pendingApproval === 'approve') {
        // Jika APPROVE, backend mengharapkan 'spv_employee_id'
        requestData.spv_employee_id = employeeID;
        requestData.status_approval = 2;
        // Tidak perlu remarks untuk approve
        apiEndpoint = ApiApproval().putStatusApprovalSupervisor;
      } else {
        // Jika REJECT, backend mengharapkan 'revise_employee_id'
        requestData.revise_employee_id = employeeID;
        requestData.status_approval = 0;
        requestData.revised_remarks = revisedRemarks.trim();
        requestData.revised_dt = new Date().toISOString();
        apiEndpoint = ApiApproval().putStatusRejectedSupervisor;
      }
      
      // 3. Panggil endpoint yang sesuai dengan data yang sudah disesuaikan
      const res = await apiEndpoint(requestData);

      if (res.status === 200) {
        toaster.push(Messages("success", `Successfully ${pendingApproval === 'approve' ? 'approved' : 'rejected'} selected details`), { duration: 3000 });
        
        // Reset states
        setSelectedDetails({
          binder: [], drying: [], granulasi: [], finalMix: [], sifting: []
        });
        setPassword("");
        setRevisedRemarks("");
        setPasswordModalOpen(false);
        setPendingApproval(null);
        
        // Refresh data
        setTimeout(fetchTransactionData, 100);
      } else {
        const errorMessage = res.status === 401 
          ? "Invalid password. Please try again."
          : res.message || `Failed to ${pendingApproval === 'approve' ? 'approve' : 'reject'} details`;
        
        toaster.push(Messages("error", errorMessage), { duration: 3000 });
      }
    } catch (error) {
      console.error(`Error ${pendingApproval === 'approve' ? 'approving' : 'rejecting'} details:`, error);
      
      const errorMessage = error.response 
        ? error.response.status === 401 
          ? "Invalid password. Please try again."
          : error.response.data.message || "An error occurred"
        : "Network error or server unavailable";
      
      toaster.push(Messages("error", errorMessage), { duration: 3000 });
    } finally {
      setApproving(false);
    }
  };


const fetchTransactionData = async () => {
  try {
    setLoading(true);
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const spvEmployeeID = dataLogin?.employee_id;
    
    if (!router.query.id_header_trans) return;
    
    const requestData = {
      spv_employee_id: spvEmployeeID,
      id_header_trans: parseInt(router.query.id_header_trans)
    };

    const res = await ApiTsdpTH().getAllActiveTransactionWithDetail(requestData);

    if (res.status === 200) {
      const data = Array.isArray(res.data) ? res.data[0] : res.data;
      setTransaction({
        ...data,
        binderDetails: data.binder_details || [],
        dryingDetails: data.drying_details || [],
        granulasiDetails: data.granulasi_details || [],
        finalMixDetails: data.final_mix_details || [],
        siftingDetails: data.sifting_details || []
      });
    } else {
      toaster.push(Messages("error", res.message || "Failed to fetch transaction data"), { duration: 3000 });
    }
  } catch (error) {
    console.error("Error fetching transaction data:", error);
    toaster.push(Messages("error", error.message || "Failed to fetch transaction data"), { duration: 3000 });
  } finally {
    setLoading(false);
  }
};

  useEffect(() => {
    if (router.isReady && router.query.id_header_trans) {
      fetchTransactionData();
      fetchAttachmentsData();
      fetchTransactionAutoDetails();
      fetchTransactionManualDetails();
    }
  }, [router.isReady, router.query.id_header_trans]);

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push("/");
      return;
    }

    const parentMenu = localStorage.getItem("parentMenu");
    const childMenu = localStorage.getItem("childMenu");
    if (!parentMenu || !childMenu) {
      router.push("/");
      return;
    }
  }, []);

  useEffect(() => {
    setAutoPage(1);
    setManualPage(1);
  }, [activeModal, transaction?.id_header_trans]);

  if (loading && !transaction) {
    return (
      <div className="text-center p-4">
        <Loader size="md" content="Loading transaction details..." />
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className="text-center p-4">
        <p>No transaction data found</p>
      </div>
    );
  }

const renderPasswordModal = () => (
  <Modal 
    open={passwordModalOpen} 
    onClose={() => {
      setPasswordModalOpen(false);
      setPendingApproval(null);
      setPassword("");
      setRevisedRemarks(""); // Reset remarks ketika modal ditutup
    }} 
    size="xs"
    backdrop="static"
  >
    <Modal.Header>
      <Modal.Title>{pendingApproval === 'approve' ? 'Approve' : 'Reject'} Confirmation</Modal.Title>
    </Modal.Header>
    <Modal.Body>
      <Form fluid>
        
        {pendingApproval === 'reject' && (
  <Form.Group>
    <Form.ControlLabel>Rejection Remarks*</Form.ControlLabel>
    {/* Gunakan komponen Input secara langsung */}
    <Input 
      as="textarea" 
      rows={3}
      placeholder="Please provide specific reasons for rejection"
      value={revisedRemarks}
      onChange={(value) => setRevisedRemarks(value)}
    />
  </Form.Group>
)}
        <Form.Group>
          <Form.ControlLabel>Enter your password to confirm</Form.ControlLabel>
          <Form.Control 
            name="password" 
            type="password" 
            value={password}
            onChange={setPassword}
            autoComplete="current-password"
            placeholder="Enter your password"
          />
        </Form.Group>
      </Form>
    </Modal.Body>
    <Modal.Footer>
      <Button 
        onClick={() => {
          setPasswordModalOpen(false);
          setPendingApproval(null);
          setPassword("");
          setRevisedRemarks("");
        }} 
        appearance="subtle"
      >
        Cancel
      </Button>
      <Button 
        onClick={confirmApproval} 
        appearance="primary"
        color={pendingApproval === 'reject' ? 'red' : 'blue'}
        loading={approving}
        disabled={
          !password || 
          approving || 
          (pendingApproval === 'reject' && !revisedRemarks.trim()) // Wajib isi remarks untuk reject
        }
      >
        {pendingApproval === 'approve' ? 'Confirm Approve' : 'Confirm Reject'}
      </Button>
    </Modal.Footer>
  </Modal>
);


// Binder Modal with all fields in 2-column grid layout
const renderBinderModal = () => {
  const relevantAttachments = attachments.filter(
    (att) =>
      att.id_header_trans === transaction.id_header_trans &&
      att.type.toLowerCase() === 'binder'
  );

  return (
  <Modal open={activeModal === 'binder'} onClose={closeModal} size="md">
    <Modal.Header>
      <Modal.Title>Binder Detail</Modal.Title>
    </Modal.Header>
    <Modal.Body>
      <Form fluid style={{ marginBottom: 24 }}>
        <div className="grid grid-cols-2 gap-2">
          <Form.Group>
            <Form.ControlLabel>Detail ID</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.id_detail_trans || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Header ID</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.id_header_trans || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Binder Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.binder_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Mix Amount</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.binder_mix_amount || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Mix Time</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.binder_mix_time || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Remarks</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.binder_remarks || '-'} />
          </Form.Group>
          
          <Divider className="col-span-2" />
          
          <Form.Group>
            <Form.ControlLabel>Created Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.created_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Created By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.created_by || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Updated Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.updated_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Updated By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.updated_by || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Deleted Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.deleted_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Deleted By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.deleted_by || '-'} />
          </Form.Group>
          
          <Form.Group>
            <Form.ControlLabel>Status</Form.ControlLabel>
            <Form.Control readOnly value={getStatusLabel(activeDetail?.is_active).label || '-'} />
          </Form.Group>
        </div>
      </Form>
      {renderTransactionDetailTables(transaction.id_header_trans, 'Binder')}
      {relevantAttachments.length > 0 && (
        <>
          <Divider>Attachments</Divider>
          <div>
            {relevantAttachments.map((attach, index) => (
              <div key={index} className="border-1 p-2 my-1 rounded-md">
                <p><strong>ID:</strong> {attach.id_attachments}</p>
                <p><strong>Type:</strong> {attach.type}</p>
                <p><strong>Created By:</strong> {attach.created_by}</p>
                <p><strong>Created At:</strong> {formatDate(attach.created_at)}</p>
                <p><strong>Status:</strong> {attach.is_active ? 'Active' : 'Inactive'}</p>
                {attach.path && (
                  <div className="mt-2">
                    <img 
                      className="object-contain max-w-full h-auto max-h-64" 
                      src={`${process.env.NEXT_PUBLIC_STORAGE}/${attach.path}`} 
                      alt={attach.type} 
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </>
      )}
    </Modal.Body>
    <Modal.Footer>
      <Button onClick={closeModal} appearance="primary">
        Close
      </Button>
    </Modal.Footer>
  </Modal>
  );
};

const renderDryingModal = () => {
  const relevantAttachments = attachments.filter(
    (att) =>
      att.id_header_trans === transaction.id_header_trans &&
      att.type.toLowerCase() === 'drying'
  );
  return (
  <Modal open={activeModal === 'drying'} onClose={closeModal} size="md">
    <Modal.Header>
      <Modal.Title>Drying Detail</Modal.Title>
    </Modal.Header>
    <Modal.Body>
      <Form fluid style={{ marginBottom: 24 }}>
        <div className="grid grid-cols-2 gap-2">
          <Form.Group>
            <Form.ControlLabel>Detail ID</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.id_detail_trans || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Header ID</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.id_header_trans || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Drying Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.drying_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>LOD</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.drying_lod || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Product Temp</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.drying_product_temp || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Exhaust Temp</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.drying_exhaust_temp || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Remarks</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.drying_remarks || '-'} />
          </Form.Group>
          
          <Divider className="col-span-2" />
          
          <Form.Group>
            <Form.ControlLabel>Created Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.created_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Created By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.created_by || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Updated Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.updated_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Updated By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.updated_by || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Deleted Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.deleted_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Deleted By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.deleted_by || '-'} />
          </Form.Group>
          
          <Form.Group>
            <Form.ControlLabel>Status</Form.ControlLabel>
            <Form.Control readOnly value={getStatusLabel(activeDetail?.is_active).label || '-'} />
          </Form.Group>
        </div>
      </Form>
      {renderTransactionDetailTables(transaction.id_header_trans, 'Drying')}
      {relevantAttachments.length > 0 && (
        <>
          <Divider>Attachments</Divider>
          <div>
            {relevantAttachments.map((attach, index) => (
              <div key={index} className="border-1 p-2 my-1 rounded-md">
                <p><strong>ID:</strong> {attach.id_attachments}</p>
                <p><strong>Type:</strong> {attach.type}</p>
                <p><strong>Created By:</strong> {attach.created_by}</p>
                <p><strong>Created At:</strong> {formatDate(attach.created_at)}</p>
                <p><strong>Status:</strong> {attach.is_active ? 'Active' : 'Inactive'}</p>
                {attach.path && (
                  <div className="mt-2">
                    <img 
                      className="object-contain max-w-full h-auto max-h-64" 
                      src={`${process.env.NEXT_PUBLIC_STORAGE}/${attach.path}`} 
                      alt={attach.type} 
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </>
      )}
    </Modal.Body>
    <Modal.Footer>
      <Button onClick={closeModal} appearance="primary">
        Close
      </Button>
    </Modal.Footer>
  </Modal>
  );
};

const renderGranulasiModal = () => {
  const relevantAttachments = attachments.filter(
    (att) =>
      att.id_header_trans === transaction.id_header_trans &&
      att.type.toLowerCase() === 'granulasi'
  );
  return (
  <Modal open={activeModal === 'granulasi'} onClose={closeModal} size="md">
    <Modal.Header>
      <Modal.Title>Granulasi Detail</Modal.Title>
    </Modal.Header>
    <Modal.Body>
      <Form fluid style={{ marginBottom: 24 }}>
        <div className="grid grid-cols-2 gap-2">
          <Form.Group>
            <Form.ControlLabel>Detail ID</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.id_detail_trans || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Header ID</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.id_header_trans || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Granule Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.granule_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Ampere</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.granule_ampere || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Power</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.granule_power || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Remarks</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.granule_remarks || '-'} />
          </Form.Group>
          
          <Divider className="col-span-2" />
          
          <Form.Group>
            <Form.ControlLabel>Created Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.created_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Created By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.created_by || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Updated Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.updated_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Updated By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.updated_by || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Deleted Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.deleted_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Deleted By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.deleted_by || '-'} />
          </Form.Group>
          
          <Form.Group>
            <Form.ControlLabel>Status</Form.ControlLabel>
            <Form.Control readOnly value={getStatusLabel(activeDetail?.is_active).label || '-'} />
          </Form.Group>
        </div>
      </Form>
      {renderTransactionDetailTables(transaction.id_header_trans, 'Granulasi')}
      {relevantAttachments.length > 0 && (
        <>
          <Divider>Attachments</Divider>
          <div>
            {relevantAttachments.map((attach, index) => (
              <div key={index} className="border-1 p-2 my-1 rounded-md">
                <p><strong>ID:</strong> {attach.id_attachments}</p>
                <p><strong>Type:</strong> {attach.type}</p>
                <p><strong>Created By:</strong> {attach.created_by}</p>
                <p><strong>Created At:</strong> {formatDate(attach.created_at)}</p>
                <p><strong>Status:</strong> {attach.is_active ? 'Active' : 'Inactive'}</p>
                {attach.path && (
                  <div className="mt-2">
                    <img 
                      className="object-contain max-w-full h-auto max-h-64" 
                      src={`${process.env.NEXT_PUBLIC_STORAGE}/${attach.path}`} 
                      alt={attach.type} 
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </>
      )}
    </Modal.Body>
    <Modal.Footer>
      <Button onClick={closeModal} appearance="primary">
        Close
      </Button>
    </Modal.Footer>
  </Modal>
  );
};

const renderFinalMixModal = () => {
  const relevantAttachments = attachments.filter(
    (att) =>
      att.id_header_trans === transaction.id_header_trans &&
      att.type.toLowerCase() === 'finalmix'
  );
  return (
  <Modal open={activeModal === 'finalMix'} onClose={closeModal} size="md">
    <Modal.Header>
      <Modal.Title>Final Mix Detail</Modal.Title>
    </Modal.Header>
    <Modal.Body>
      <Form fluid style={{ marginBottom: 24 }}>
        <div className="grid grid-cols-2 gap-2">
          <Form.Group>
            <Form.ControlLabel>Detail ID</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.id_detail_trans || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Header ID</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.id_header_trans || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Final Mix Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.final_mix_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Time Mix 1</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.final_mix_time_mix_1 || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Time Mix 2</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.final_mix_time_mix_2 || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Conclusion</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.ts_conclusion || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Follow Up</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.ts_followup || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Bobot Granul</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.bobot_granul || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Bobot Teoritis</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.bobot_teoritis || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Rendemen</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.rendemen || '-'} />
          </Form.Group>

          <Divider className="col-span-2" />

          <Form.Group>
            <Form.ControlLabel>Created Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.created_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Created By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.created_by || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Updated Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.updated_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Updated By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.updated_by || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Deleted Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.deleted_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Deleted By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.deleted_by || '-'} />
          </Form.Group>

          <Form.Group>
            <Form.ControlLabel>Status</Form.ControlLabel>
            <Form.Control readOnly value={getStatusLabel(activeDetail?.is_active).label || '-'} />
          </Form.Group>
        </div>
      </Form>
      {renderTransactionDetailTables(transaction.id_header_trans, 'FinalMix')}
      {relevantAttachments.length > 0 && (
        <>
          <Divider>Attachments</Divider>
          <div>
            {relevantAttachments.map((attach, index) => (
              <div key={index} className="border-1 p-2 my-1 rounded-md">
                <p><strong>ID:</strong> {attach.id_attachments}</p>
                <p><strong>Type:</strong> {attach.type}</p>
                <p><strong>Created By:</strong> {attach.created_by}</p>
                <p><strong>Created At:</strong> {formatDate(attach.created_at)}</p>
                <p><strong>Status:</strong> {attach.is_active ? 'Active' : 'Inactive'}</p>
                {attach.path && (
                  <div className="mt-2">
                    <img 
                      className="object-contain max-w-full h-auto max-h-64" 
                      src={`${process.env.NEXT_PUBLIC_STORAGE}/${attach.path}`} 
                      alt={attach.type} 
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </>
      )}
    </Modal.Body>
    <Modal.Footer>
      <Button onClick={closeModal} appearance="primary">
        Close
      </Button>
    </Modal.Footer>
  </Modal>
  );
};

// Sifting Modal with all fields
const renderSiftingModal = () => {
  const relevantAttachments = attachments.filter(
    (att) =>
      att.id_header_trans === transaction.id_header_trans &&
      att.type.toLowerCase() === 'sifting'
  );
  return (
  <Modal open={activeModal === 'sifting'} onClose={closeModal} size="md">
    <Modal.Header>
      <Modal.Title>Sifting Detail</Modal.Title>
    </Modal.Header>
    <Modal.Body>
      <Form fluid style={{ marginBottom: 24 }}>
        <div className="grid grid-cols-2 gap-2">
          <Form.Group>
            <Form.ControlLabel>Detail ID</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.id_detail_trans || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Header ID</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.id_header_trans || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Sifting Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.sifting_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Screen Quadro</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.sifting_screen_quadro || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Bin Tumbler</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.sifting_bin_tumbler || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Impeller Speed 1</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.sifting_impeller_speed_1 || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Impeller Speed 2</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.sifting_impeller_speed_2 || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Remarks</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.sifting_remarks || '-'} />
          </Form.Group>
          
          <Divider className="col-span-2" />
          
          <Form.Group>
            <Form.ControlLabel>Created Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.created_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Created By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.created_by || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Updated Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.updated_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Updated By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.updated_by || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Deleted Date</Form.ControlLabel>
            <Form.Control readOnly value={formatDate(activeDetail?.deleted_date) || '-'} />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Deleted By</Form.ControlLabel>
            <Form.Control readOnly value={activeDetail?.deleted_by || '-'} />
          </Form.Group>
          
          <Form.Group>
            <Form.ControlLabel>Status</Form.ControlLabel>
            <Form.Control readOnly value={getStatusLabel(activeDetail?.is_active).label || '-'} />
          </Form.Group>
        </div>
      </Form>
      {renderTransactionDetailTables(transaction.id_header_trans, 'Sifting')}
      {relevantAttachments.length > 0 && (
        <>
          <Divider>Attachments</Divider>
          <div>
            {relevantAttachments.map((attach, index) => (
              <div key={index} className="border-1 p-2 my-1 rounded-md">
                <p><strong>ID:</strong> {attach.id_attachments}</p>
                <p><strong>Type:</strong> {attach.type}</p>
                <p><strong>Created By:</strong> {attach.created_by}</p>
                <p><strong>Created At:</strong> {formatDate(attach.created_at)}</p>
                <p><strong>Status:</strong> {attach.is_active ? 'Active' : 'Inactive'}</p>
                {attach.path && (
                  <div className="mt-2">
                    <img 
                      className="object-contain max-w-full h-auto max-h-64" 
                      src={`${process.env.NEXT_PUBLIC_STORAGE}/${attach.path}`} 
                      alt={attach.type} 
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </>
      )}
    </Modal.Body>
    <Modal.Footer>
      <Button onClick={closeModal} appearance="primary">
        Close
      </Button>
    </Modal.Footer>
  </Modal>
  );
};

  return (
    <div>
      <Head>
        <title>Transaction Approval Detail</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>TSDP</Breadcrumb.Item>
                  <Breadcrumb.Item>Approval</Breadcrumb.Item>
                  <Breadcrumb.Item active>Transaction Detail</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel bordered bodyFill className="mb-3" header={<h5>Transaction Header Detail - ID: {transaction.id_header_trans}</h5>}></Panel>
          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between" alignItems="center">
                <h5>Header Information</h5>
                <ButtonToolbar>
                  <IconButton 
                    appearance="primary" 
                    color="blue" 
                    icon={<PagePreviousIcon />}
                    onClick={() => router.push('/user_module/tsdp/approval/spv')}
                  >
                    Back to Approval Supervisor List
                  </IconButton>
                </ButtonToolbar>
              </Stack>
            }
          >
            <div className="p-4">
              <Divider />
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Form fluid>
                    <Form.Group>
                      <Form.ControlLabel>Header ID</Form.ControlLabel>
                      <Form.Control value={transaction.id_header_trans} readOnly />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Product Code</Form.ControlLabel>
                      <Form.Control value={transaction.product_code} readOnly />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Product Name</Form.ControlLabel>
                      <Form.Control value={transaction.product_name} readOnly />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Batch No</Form.ControlLabel>
                      <Form.Control value={transaction.batch_no} readOnly />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Sediaan Type</Form.ControlLabel>
                      <Form.Control value={transaction.sediaan_type} readOnly />
                    </Form.Group>
                  </Form>
                </div>
                <div>
                  <Form fluid>
                    <Form.Group>
                      <Form.ControlLabel>Process Purpose</Form.ControlLabel>
                      <Form.Control value={transaction.process_purpose} readOnly />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Process Date</Form.ControlLabel>
                      <Form.Control value={formatDate(transaction.process_date)} readOnly />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Created By</Form.ControlLabel>
                      <Form.Control value={transaction.created_by} readOnly />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Created Date</Form.ControlLabel>
                      <Form.Control value={formatDate(transaction.created_date)} readOnly />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Status</Form.ControlLabel>
                      <Form.Control value={getStatusLabel(transaction.is_active).label} readOnly />
                    </Form.Group>
                  </Form>
                </div>
              </div>

              {/* Approval buttons */}
               <div className="mt-4 mb-4">
                <ButtonToolbar>
                  <Button 
                    appearance="primary" 
                    color="green" 
                    startIcon={<CheckIcon />}
                    onClick={() => handleApprove(true)}
                    loading={approving}
                  >
                    Approve Selected
                  </Button>
                  <Button 
                    appearance="primary" 
                    color="red" 
                    startIcon={<CloseIcon />}
                    onClick={() => handleApprove(false)}
                    loading={approving}
                  >
                    Reject Selected
                  </Button>
                </ButtonToolbar>
              </div> 

              {/* <Button 
  appearance="primary" 
  color="green" 
  startIcon={<CheckIcon />}
  onClick={() => handleApprove(true)}
>
  Approve Selected
</Button>
<Button 
  appearance="primary" 
  color="red" 
  startIcon={<CloseIcon />}
  onClick={() => handleApprove(false)}
>
  Reject Selected
</Button> */}

              {/* Binder Details */}
              {transaction.binderDetails && transaction.binderDetails.length > 0 && (
                <div className="mt-4">
                  <Stack justifyContent="space-between" className="mb-2">
                    <h6>Binder Details</h6>
                    <Checkbox 
                      indeterminate={
                        selectedDetails.binder.length > 0 && 
                        selectedDetails.binder.length < transaction.binderDetails.length
                      }
                      checked={
                        selectedDetails.binder.length > 0 && 
                        selectedDetails.binder.length === transaction.binderDetails.length
                      }
                      onChange={() => toggleAllDetails('binder', transaction.binderDetails)}
                    >
                      Select All
                    </Checkbox>
                  </Stack>
                  <Divider />
                  <Table
                    bordered
                    cellBordered
                    height={200}
                    data={transaction.binderDetails}
                    autoHeight
                  >
                    <Column width={50} align="center" fixed>
                      <HeaderCell>#</HeaderCell>
                      <Cell>
                        {rowData => (
                          <Checkbox
                            checked={selectedDetails.binder.includes(rowData.id_detail_trans)}
                            onChange={() => toggleDetailSelection('binder', rowData.id_detail_trans)}
                          />
                        )}
                      </Cell>
                    </Column>
                    <Column width={120} align="center">
                      <HeaderCell>ID</HeaderCell>
                      <Cell dataKey="id_detail_trans" />
                    </Column>
                    <Column width={200}>
                      <HeaderCell>Binder Date</HeaderCell>
                      <Cell>{rowData => formatDate(rowData.binder_date)}</Cell>
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Mix Amount</HeaderCell>
                      <Cell dataKey="binder_mix_amount" />
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Mix Time</HeaderCell>
                      <Cell dataKey="binder_mix_time" />
                    </Column>
                    <Column width={300}>
                      <HeaderCell>Remarks</HeaderCell>
                      <Cell dataKey="binder_remarks" />
                    </Column>
                    <Column width={100} align="center" fixed="right">
                      <HeaderCell>Action</HeaderCell>
                      <Cell>
                        {rowData => (
                          <IconButton
                            icon={<EditIcon />}
                            circle
                            size="xs"
                            appearance="subtle"
                            onClick={() => openModal('binder', rowData)}
                          />
                        )}
                      </Cell>
                    </Column>
                  </Table>
                </div>
              )}

              {/* Drying Details */}
              {transaction.dryingDetails && transaction.dryingDetails.length > 0 && (
                <div className="mt-4">
                  <Stack justifyContent="space-between" className="mb-2">
                    <h6>Drying Details</h6>
                    <Checkbox 
                      indeterminate={
                        selectedDetails.drying.length > 0 && 
                        selectedDetails.drying.length < transaction.dryingDetails.length
                      }
                      checked={
                        selectedDetails.drying.length > 0 && 
                        selectedDetails.drying.length === transaction.dryingDetails.length
                      }
                      onChange={() => toggleAllDetails('drying', transaction.dryingDetails)}
                    >
                      Select All
                    </Checkbox>
                  </Stack>
                  <Divider />
                  <Table
                    bordered
                    cellBordered
                    height={200}
                    data={transaction.dryingDetails}
                    autoHeight
                  >
                    <Column width={50} align="center" fixed>
                      <HeaderCell>#</HeaderCell>
                      <Cell>
                        {rowData => (
                          <Checkbox
                            checked={selectedDetails.drying.includes(rowData.id_detail_trans)}
                            onChange={() => toggleDetailSelection('drying', rowData.id_detail_trans)}
                          />
                        )}
                      </Cell>
                    </Column>
                    <Column width={120} align="center">
                      <HeaderCell>ID</HeaderCell>
                      <Cell dataKey="id_detail_trans" />
                    </Column>
                    <Column width={200}>
                      <HeaderCell>Drying Date</HeaderCell>
                      <Cell>{rowData => formatDate(rowData.drying_date)}</Cell>
                    </Column>
                    <Column width={180}>
                      <HeaderCell>LOD</HeaderCell>
                      <Cell dataKey="drying_lod" />
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Product Temp</HeaderCell>
                      <Cell dataKey="drying_product_temp" />
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Exhaust Temp</HeaderCell>
                      <Cell dataKey="drying_exhaust_temp" />
                    </Column>
                    <Column width={300}>
                      <HeaderCell>Remarks</HeaderCell>
                      <Cell dataKey="drying_remarks" />
                    </Column>
                    <Column width={100} align="center" fixed="right">
                      <HeaderCell>Action</HeaderCell>
                      <Cell>
                        {rowData => (
                          <IconButton
                            icon={<EditIcon />}
                            circle
                            size="xs"
                            appearance="subtle"
                            onClick={() => openModal('drying', rowData)}
                          />
                        )}
                      </Cell>
                    </Column>
                  </Table>
                </div>
              )}

              {/* Granulasi Details */}
              {transaction.granulasiDetails && transaction.granulasiDetails.length > 0 && (
                <div className="mt-4">
                  <Stack justifyContent="space-between" className="mb-2">
                    <h6>Granulasi Details</h6>
                    <Checkbox 
                      indeterminate={
                        selectedDetails.granulasi.length > 0 && 
                        selectedDetails.granulasi.length < transaction.granulasiDetails.length
                      }
                      checked={
                        selectedDetails.granulasi.length > 0 && 
                        selectedDetails.granulasi.length === transaction.granulasiDetails.length
                      }
                      onChange={() => toggleAllDetails('granulasi', transaction.granulasiDetails)}
                    >
                      Select All
                    </Checkbox>
                  </Stack>
                  <Divider />
                  <Table
                    bordered
                    cellBordered
                    height={200}
                    data={transaction.granulasiDetails}
                    autoHeight
                  >
                    <Column width={50} align="center" fixed>
                      <HeaderCell>#</HeaderCell>
                      <Cell>
                        {rowData => (
                          <Checkbox
                            checked={selectedDetails.granulasi.includes(rowData.id_detail_trans)}
                            onChange={() => toggleDetailSelection('granulasi', rowData.id_detail_trans)}
                          />
                        )}
                      </Cell>
                    </Column>
                    <Column width={120} align="center">
                      <HeaderCell>ID</HeaderCell>
                      <Cell dataKey="id_detail_trans" />
                    </Column>
                    <Column width={200}>
                      <HeaderCell>Granule Date</HeaderCell>
                      <Cell>{rowData => formatDate(rowData.granule_date)}</Cell>
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Ampere</HeaderCell>
                      <Cell dataKey="granule_ampere" />
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Power</HeaderCell>
                      <Cell dataKey="granule_power" />
                    </Column>
                    <Column width={300}>
                      <HeaderCell>Remarks</HeaderCell>
                      <Cell dataKey="granule_remarks" />
                    </Column>
                    <Column width={100} align="center" fixed="right">
                      <HeaderCell>Action</HeaderCell>
                      <Cell>
                        {rowData => (
                          <IconButton
                            icon={<EditIcon />}
                            circle
                            size="xs"
                            appearance="subtle"
                            onClick={() => openModal('granulasi', rowData)}
                          />
                        )}
                      </Cell>
                    </Column>
                  </Table>
                </div>
              )}

              {/* Final Mix Details */}
              {transaction.finalMixDetails && transaction.finalMixDetails.length > 0 && (
                <div className="mt-4">
                  <Stack justifyContent="space-between" className="mb-2">
                    <h6>Final Mix Details</h6>
                    <Checkbox 
                      indeterminate={
                        selectedDetails.finalMix.length > 0 && 
                        selectedDetails.finalMix.length < transaction.finalMixDetails.length
                      }
                      checked={
                        selectedDetails.finalMix.length > 0 && 
                        selectedDetails.finalMix.length === transaction.finalMixDetails.length
                      }
                      onChange={() => toggleAllDetails('finalMix', transaction.finalMixDetails)}
                    >
                      Select All
                    </Checkbox>
                  </Stack>
                  <Divider />
                  <Table
                    bordered
                    cellBordered
                    height={200}
                    data={transaction.finalMixDetails}
                    autoHeight
                  >
                    <Column width={50} align="center" fixed>
                      <HeaderCell>#</HeaderCell>
                      <Cell>
                        {rowData => (
                          <Checkbox
                            checked={selectedDetails.finalMix.includes(rowData.id_detail_trans)}
                            onChange={() => toggleDetailSelection('finalMix', rowData.id_detail_trans)}
                          />
                        )}
                      </Cell>
                    </Column>
                    <Column width={120} align="center">
                      <HeaderCell>ID</HeaderCell>
                      <Cell dataKey="id_detail_trans" />
                    </Column>
                    <Column width={200}>
                      <HeaderCell>Final Mix Date</HeaderCell>
                      <Cell>{rowData => formatDate(rowData.final_mix_date)}</Cell>
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Time Mix 1</HeaderCell>
                      <Cell dataKey="final_mix_time_mix_1" />
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Time Mix 2</HeaderCell>
                      <Cell dataKey="final_mix_time_mix_2" />
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Bobot Granul</HeaderCell>
                      <Cell dataKey="bobot_granul" />
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Bobot Teoritis</HeaderCell>
                      <Cell dataKey="bobot_teoritis" />
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Rendemen</HeaderCell>
                      <Cell dataKey="rendemen" />
                    </Column>
                    <Column width={300}>
                      <HeaderCell>Conclusion</HeaderCell>
                      <Cell dataKey="ts_conclusion" />
                    </Column>
                    <Column width={300}>
                      <HeaderCell>Follow Up</HeaderCell>
                      <Cell dataKey="ts_followup" />
                    </Column>
                    <Column width={100} align="center" fixed="right">
                      <HeaderCell>Action</HeaderCell>
                      <Cell>
                        {rowData => (
                          <IconButton
                            icon={<EditIcon />}
                            circle
                            size="xs"
                            appearance="subtle"
                            onClick={() => openModal('finalMix', rowData)}
                          />
                        )}
                      </Cell>
                    </Column>
                  </Table>
                </div>
              )}

              {/* Sifting Details */}
              {transaction.siftingDetails && transaction.siftingDetails.length > 0 && (
                <div className="mt-4">
                  <Stack justifyContent="space-between" className="mb-2">
                    <h6>Sifting Details</h6>
                    <Checkbox 
                      indeterminate={
                        selectedDetails.sifting.length > 0 && 
                        selectedDetails.sifting.length < transaction.siftingDetails.length
                      }
                      checked={
                        selectedDetails.sifting.length > 0 && 
                        selectedDetails.sifting.length === transaction.siftingDetails.length
                      }
                      onChange={() => toggleAllDetails('sifting', transaction.siftingDetails)}
                    >
                      Select All
                    </Checkbox>
                  </Stack>
                  <Divider />
                  <Table
                    bordered
                    cellBordered
                    height={200}
                    data={transaction.siftingDetails}
                    autoHeight
                  >
                    <Column width={50} align="center" fixed>
                      <HeaderCell>#</HeaderCell>
                      <Cell>
                        {rowData => (
                          <Checkbox
                            checked={selectedDetails.sifting.includes(rowData.id_detail_trans)}
                            onChange={() => toggleDetailSelection('sifting', rowData.id_detail_trans)}
                          />
                        )}
                      </Cell>
                    </Column>
                    <Column width={120} align="center">
                      <HeaderCell>ID</HeaderCell>
                      <Cell dataKey="id_detail_trans" />
                    </Column>
                    <Column width={200}>
                      <HeaderCell>Sifting Date</HeaderCell>
                      <Cell>{rowData => formatDate(rowData.sifting_date)}</Cell>
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Screen Quadro</HeaderCell>
                      <Cell dataKey="sifting_screen_quadro" />
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Bin Tumbler</HeaderCell>
                      <Cell dataKey="sifting_bin_tumbler" />
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Impeller Speed 1</HeaderCell>
                      <Cell dataKey="sifting_impeller_speed_1" />
                    </Column>
                    <Column width={180}>
                      <HeaderCell>Impeller Speed 2</HeaderCell>
                      <Cell dataKey="sifting_impeller_speed_2" />
                    </Column>
                    <Column width={300}>
                      <HeaderCell>Remarks</HeaderCell>
                      <Cell dataKey="sifting_remarks" />
                    </Column>
                    <Column width={100} align="center" fixed="right">
                      <HeaderCell>Action</HeaderCell>
                      <Cell>
                        {rowData => (
                          <IconButton
                            icon={<EditIcon />}
                            circle
                            size="xs"
                            appearance="subtle"
                            onClick={() => openModal('sifting', rowData)}
                          />
                        )}
                      </Cell>
                    </Column>
                  </Table>
                </div>
              )}
            </div>
          </Panel>
        </div>
      </ContainerLayout>

      {/* Render modals */}
{renderBinderModal()}
{renderDryingModal()}
{renderGranulasiModal()}
{renderFinalMixModal()}
{renderSiftingModal()}
{renderPasswordModal()}
    </div>
  );
}