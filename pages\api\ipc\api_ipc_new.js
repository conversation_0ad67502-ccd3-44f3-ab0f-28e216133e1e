import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};


export default function ApiIPCNew() {
  return {
    UpdateStagingStatusMysql: createApiFunction("put", "ipc/put/mysql/stagingStatus"),
    PutClearStagingMysql: createApiFunction("put", "ipc/put/mysql/clearstagingstatus"),
    GetEntryMeasurementTotalMSAndTMSMysql: createApiFunction("post", "ipc/get/mysql/entryTotalData"),
    UpdateStagingStatus: createApiFunction("put", "ipc/put/stagingStatus"),
    PutClearStaging: createApiFunction("put", "ipc/put/clearstagingstatus"),
    GetEntryMeasurementTotalMSAndTMS: createApiFunction("post", "ipc/get/entryTotalData"),
  };
}
