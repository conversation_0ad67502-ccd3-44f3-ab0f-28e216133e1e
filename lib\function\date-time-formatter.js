export default function DateTimeFormatter(utcDate, reg, seconds) {
  const options = {
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
  };

  if (seconds) {
    options.second = "numeric";
  }

  // Parse the UTC date string to a Date object and format it in Indonesian time
  const formattedDate = new Date(utcDate).toLocaleDateString(reg, options);
  return formattedDate;
}
