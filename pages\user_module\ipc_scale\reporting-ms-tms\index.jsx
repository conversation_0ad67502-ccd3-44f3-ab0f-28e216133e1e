import ContainerLayout from "@/components/layout/ContainerLayout";
import Head from "next/head";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import {
  Breadcrumb,
  Button,
  DatePicker,
  Panel,
  SelectPicker,
  Stack,
  Table,
} from "rsuite";

import XLSX from "xlsx";
import ChartDataLabels from "chartjs-plugin-datalabels";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
  ChartDataLabels
);

import { Line } from "react-chartjs-2";
import Api_IPC_Scale_DASHBOARD from "@/pages/api/ipc_scale/api_ipc_scale_dashboard";
import ApiIpcStep from "@/pages/api/ipc/api_ipc_step";

export default function ReportingScaleMsTms() {
  const router = useRouter();
  const [moduleName, setModuleName] = useState("");
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const { HeaderCell, Cell, Column } = Table;
  // Calculate the date for 30 days ago
  const thirtyDaysAgo = new Date(new Date().setDate(new Date().getDate() - 30));

  // Set the start date to 30 days ago and end date to the current date
  const [startDate, setStartDate] = useState(thirtyDaysAgo);
  const [endDate, setEndDate] = useState(new Date());
  const [stateDashboardData, setStateDashboardData] = useState([]);
  const [selectedBatchCode, setSelectedBatchCode] = useState("");
  const [stepData, setStepData] = useState([]);
  const [selectedStep, setSelectedStep] = useState("");
  const [stateFilteredDashboardData, setStateFilteredDashboardData] = useState(
    []
  );
  const [
    stateFilteredDashboardReasonData,
    setStateFilteredDashboardReasonData,
  ] = useState([]);

  const [dateForm, setDateForm] = useState(null);

  const [stateReasonData, setStateReasonData] = useState([]);

  const handleExportToPDF = () => {
    const encryptedBatchCode = btoa(selectedBatchCode);
    const encryptedStep = btoa(selectedStep);

    router.push(
      `/user_module/ipc_scale/reporting-ms-tms/PdfPreview?ssr_batch_code=${encryptedBatchCode}&ssr_selected_step=${encryptedStep}`
    );
  };

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("ipc_scale/reporting-ms-tms")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
    }
    setStartDate(thirtyDaysAgo);
    setEndDate(new Date());

    handleFetchData();
  }, []);

  const handleFetchData = async () => {
    setLoading(true);
    const data = await Api_IPC_Scale_DASHBOARD().getIpcScaleReportExcel({
      start_date: startDate,
      end_date: endDate,
    });
    console.log("data", data || []);
    setData(data.data || []);
    setLoading(false);

    const ipcDashboard = await Api_IPC_Scale_DASHBOARD().getIpcScaleDashboard();
    console.log("ipc data", ipcDashboard);

    setStateDashboardData(ipcDashboard.data || []);

    const resReason = await Api_IPC_Scale_DASHBOARD().getIpcScaleReason();

    setStateReasonData(resReason.data || []);

    const ipcStep = await ApiIpcStep().getAllActiveIpcStep();
    setStepData(ipcStep.data || []);
  };

  const formatDateToShort = (date) => {
    if (!(date instanceof Date)) {
      date = new Date(date);
    }
    let month = "" + (date.getMonth() + 1);
    let day = "" + date.getDate();
    const year = date.getFullYear();

    if (month.length < 2) month = "0" + month;
    if (day.length < 2) day = "0" + day;

    return [year, month, day].join("-");
  };

  const exportFile = (data) => {
    // Map data to a new array
    const formattedData = data.map((item) => ({
      Date: item.formatted_date,
      "TMS Count": item.tms_count,
      "MS Count": item.ms_count,
    }));

    // Generate worksheet from the new array
    const ws = XLSX.utils.json_to_sheet(formattedData);

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const formattedStartDate = formatDateToShort(startDate);
    const formattedEndDate = formatDateToShort(endDate);
    const fileName =
      "reporting-ms-tms" +
      " " +
      formattedStartDate +
      " to " +
      formattedEndDate +
      ".xlsx";

    XLSX.writeFile(wb, fileName);
  };

  const prepareChartData = (data) => {
    const labels = data.map((item) => item.formatted_date);
    const msCounts = data.map((item) => item.ms_count);
    const tmsCounts = data.map((item) => item.tms_count);

    return {
      labels,
      datasets: [
        {
          label: "MS Count",
          data: msCounts,
          backgroundColor: "#0f7141",
        },
        {
          label: "TMS Count",
          data: tmsCounts,
          backgroundColor: "#d32f2f",
        },
      ],
    };
  };

  const LineChart = ({ chartData }) => {
    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: "right",
        },
        title: {
          display: true,
          text: "MS and TMS Counts Over Time",
        },
        datalabels: {
          anchor: "end",
          align: "end",
          color: function (context) {
            return context.dataset.backgroundColor;
          },
          font: {
            weight: "bold",
          },
          formatter: (value, context) => {
            const formattedValue = value.toLocaleString();
            return formattedValue;
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return <Line data={chartData} options={options} />;
  };

  useEffect(() => {
    console.log("drop down set", stateDashboardData);
  }, [stateDashboardData]);

  useEffect(() => {
    console.log("step data = ", stepData);
  }, [stepData]);

  useEffect(() => {
    const selectedProductCode = selectedBatchCode;

    //always set selectedStep to empty if null
    if (selectedStep == null) {
      setSelectedStep("");
    }

    const filteredTransactions = stateDashboardData.filter((transaction) => {
      // console.log("selected step=", selectedStep);
      if (selectedStep === "") {
        return transaction.product_code === selectedProductCode;
      } else {
        return (
          transaction.product_code === selectedProductCode &&
          transaction.info.slice(0, 2) === selectedStep
        );
      }
    });

    const filteredReason = stateReasonData.filter((transaction) => {
      if (selectedStep === "") {
        return transaction.product_code === selectedProductCode;
      } else {
        return (
          transaction.product_code === selectedProductCode &&
          transaction.info.slice(0, 2) === selectedStep
        );
      }
    });

    setStateFilteredDashboardData(filteredTransactions);
    setStateFilteredDashboardReasonData(filteredReason);
  }, [selectedBatchCode, selectedStep]);

  const droppedDownData = (data) => {
    console.log("datax ", data);

    if (dateForm) {
      const filteredData = data.filter((item) => {
        const itemDate = new Date(item.created_date);
        return itemDate.toDateString() === dateForm.toDateString();
      });
      // console.log("filtered data", filteredData);
      const uniqueProductCodes = [
        ...new Set(filteredData.map((item) => item.product_code)),
      ];
      // const uniqueProductCodes = [...new Set(data.map(item => item.product_code))];

      const result = uniqueProductCodes.map((code) => ({
        label: code,
        value: code,
      }));

      return result;
    } else {
      return [];
    }
  };

  const prepareWeightChartData = (data) => {
    const labels = data.map((item) => item.info + " " + item.status_ipc_scale);
    const avg = data.map((item) => item.scale_avg_weight);
    const min = data.map((item) => item.scale_min_weight);
    const max = data.map((item) => item.scale_max_weight);

    return {
      labels,
      datasets: [
        {
          label: "Avg Weight",
          data: avg,
          backgroundColor: "#FF0000",
          borderColor: "#FF0000",
        },
        {
          label: "Max Weight",
          data: max,
          backgroundColor: "#0000FF",
          borderColor: "#0000FF",
        },
        {
          label: "Min Weight",
          data: min,
          backgroundColor: "#FFA507",
          borderColor: "#FFA500",
        },
      ],
    };
  };

  const LineWeightChart = ({ chartData, title }) => {
    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: "right",
        },
        title: {
          display: true,
          text: title,
          padding: {
            bottom: 50,
            top: 20,
          },
        },
        datalabels: {
          anchor: "end",
          align: "end",
          color: function (context) {
            return context.dataset.backgroundColor;
          },
          font: {
            weight: "bold",
          },
          formatter: (value, context) => {
            const formattedValue = value.toLocaleString();
            return formattedValue;
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return <Line data={chartData} options={options} />;
  };

  return (
    <>
      <div>
        <Head>
          <title>Reporting Scale MS and TMS</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item active>
              {moduleName ? moduleName : "User Module"} / Reporting Scale MS and
              TMS
            </Breadcrumb.Item>
          </Breadcrumb>
          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h4>Reporting Scale MS and TMS</h4>
            </Stack>
          </Panel>
          <div>
            <Panel bordered>
              <Stack className="gap-2">
                <Stack.Item>
                  <DatePicker
                    value={dateForm}
                    onChange={(value) => {
                      setDateForm(value);
                      setSelectedBatchCode(null);
                      console.log(value);
                    }}
                  />
                </Stack.Item>
                <Stack.Item>
                  <SelectPicker
                    name="batch code"
                    label="batch code"
                    value={selectedBatchCode}
                    block
                    data={droppedDownData(stateDashboardData)}
                    onChange={(value) => setSelectedBatchCode(value)}
                  />
                </Stack.Item>
                <Stack.Item>
                  <SelectPicker
                    name="step code"
                    label="step code"
                    value={selectedStep}
                    block
                    // show all of the step_name data from master step
                    data={stepData.map((item) => ({
                      label: item.step_name,
                      value: item.step_name,
                    }))}
                    onChange={(value) => setSelectedStep(value)}
                  />
                </Stack.Item>
                <Stack.Item>
                  <Button appearance="primary" onClick={handleExportToPDF}>
                    Export to PDF
                  </Button>
                </Stack.Item>
              </Stack>
              <LineWeightChart
                chartData={prepareWeightChartData(stateFilteredDashboardData)}
                plugins={[ChartDataLabels]}
                loading={loading}
                title={
                  selectedBatchCode
                    ? `${selectedBatchCode} IPC : SCALE ${
                        selectedStep ? selectedStep : ""
                      }`
                    : "IPC : SCALE"
                }
              />
              <Table
                bordered
                cellBordered
                autoHeight
                data={stateFilteredDashboardReasonData}>
                <Column width={70} align="center" fixed>
                  <HeaderCell>No</HeaderCell>
                  <Cell>
                    {(rowData, rowIndex) => {
                      return rowIndex + 1;
                    }}
                  </Cell>
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Product Code</HeaderCell>
                  <Cell dataKey="product_code" />
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Info</HeaderCell>
                  <Cell dataKey="info" />
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Reason</HeaderCell>
                  <Cell dataKey="reason" />
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Operator</HeaderCell>
                  <Cell dataKey="operator_created" />
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Approval by</HeaderCell>
                  <Cell dataKey="approve_details" />
                </Column>
              </Table>
            </Panel>
          </div>
        </div>
      </ContainerLayout>
      ;
    </>
  );
}
