import ipcApi from "@/pages/api/ipcApi";
import { faDownload, faFileDownload } from "@fortawesome/free-solid-svg-icons";
import Head from "next/head";
import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button, Stack, Table, Panel } from "rsuite";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import ProtocolApi from "@/pages/api/protocolApi";
import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";
// import Table from "@/components/Table";

export default function PrintProtocolDoc({
    noProtocol,
    headerData,
    detailData,
    t30Data,
    t40Data,
    report,
    maxCycleMonth
}) {
    const [stateShowDownload, setStateShowDownload] = useState(true);
    const [clickDownload, setClickDownload] = useState(false);
    const { HeaderCell, Cell, Column } = Table;
    const [sessionAuth, setSessionAuth] = useState(null)
    const [today, setToday] = useState(null)
    let rowNumber = 1;

    const MySwal = withReactContent(Swal);

    const generatePDF = async () => {
        // const input = document.getElementById("printedDocument");
        // const pdf = new jsPDF("p", "mm", "a4");

        // const canvas = await html2canvas(input);
        // const imageData = canvas.toDataURL("image/png");

        // //get image width and height
        // const imgProps= pdf.getImageProperties(imageData);
        // const pdfWidth = pdf.internal.pageSize.getWidth();
        // const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
        // //set image height to proposize with a4
        // pdf.addImage(imageData, "PNG", 0, 0, pdfWidth, pdfHeight); // A4 size
        // pdf.save(
        //     `Print Out Reporting Measurement - In Process Control ID [${noProtocol}]`
        // );
        setClickDownload(true);
        setStateShowDownload(false);
    };

    useEffect(() => {

        const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
        if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
            router.push('/');
            return;
        }


        setSessionAuth(dataLogin)
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0];

        setToday(formattedDate)
        // if (t40Data === null || t40Data === undefined || t40Data.length === 0) {
            if (maxCycleMonth === 99) {
                MySwal.fire({
                    icon: "error",
                    title: "Protocol Data NOT Found !",
                });
            }else{
                if (report === null || report === undefined || report.length === 0) {
                    MySwal.fire({
                        icon: "error",
                        title: "Protocol Data NOT Found !",
                    });
                }
            }
            
        // }
        console.log(report)
    }, [report, maxCycleMonth]);


    useEffect(() => {
        // window.print()
        // if (stateClicked) {
        //   const status = !stateClicked
        //   setStateClicked(status)
        //   //document.getElementById("headdownload").append()
        // }
        // console.log("first")

        if (clickDownload) {
            setClickDownload(false);
            var style = document.createElement('style');
            style.innerHTML = '@page { size: landscape; }';
            document.head.appendChild(style);
            window.print();
            style.remove();
        }

        setStateShowDownload(true);
    }, [stateShowDownload]);

    return (
        <>
            <Head>
                <title>
                    Print Out - Protocol Document :                    {"R" + noProtocol.slice(1)}
                </title>
            </Head>
            <div>
                {stateShowDownload && (
                    <div
                        style={{
                            width: "100%",
                            padding: "1em",
                            backgroundColor: "#2c2c30",
                            boxShadow: "2px 2px 10px #6c6b75",
                            position: "-webkit-sticky",
                            position: "sticky",
                            top: 0,
                        }}
                    >
                        <Stack justifyContent="space-between">
                            <Stack>
                                <p style={{ color: "white", fontSize: "1em" }}>
                                    Print Out Protocol Document :{" "}
                                    {"R" + noProtocol.slice(1)}
                                </p>
                            </Stack>
                            <Stack>
                                <Button title="Download" onClick={generatePDF}>
                                    <FontAwesomeIcon
                                        icon={faFileDownload}
                                        style={{ fontSize: 15 }}
                                    />
                                </Button>
                            </Stack>
                        </Stack>
                    </div>
                )}
                <div style={{ width: "100%", backgroundColor: "#65656b" }}>
                    <div
                        id="printedDocument"
                        style={{
                            width: "100%",
                            backgroundColor: "white",
                            margin: "auto",
                            padding: "4em 2em 4em 6em",
                        }}
                    >
                        {maxCycleMonth != undefined && maxCycleMonth != null && maxCycleMonth != 99 &&
                        <>
                        <p><strong>Hasil pengujian {"R" + noProtocol.slice(1)}</strong></p>
                        <p><strong>Provided by {sessionAuth?.employee_name} at {today} </strong></p>
                        {report.map((item, index)=>
                        <div>
                            <p><strong>Batch Number: {item.Batch_number}</strong></p>
                            <div style={{ marginTop: '1rem' }}>
                                <div style={{ paddingLeft: '2rem', paddingLeft: '2rem' }}>
                                    <div style={{ width: '100%', overflowX: 'auto', margin: '2em 0' }}>
                                        <table style={{ width: '100%', borderCollapse: 'collapse', border: '1px solid #ddd' }}>
                                            <thead>
                                                <tr>
                                                    <th rowSpan={'2'} style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>Parameter Name</th>
                                                    <th rowSpan={'2'} style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>Standard</th>
                                                    {item.MaxCycleMonth >= 0 &&
                                                    <th colspan="2" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>
                                                        timeframe 0
                                                        </th>
                                                    }
                                                    {item.MaxCycleMonth >= 3 &&
                                                    <th colspan="2" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>
                                                        timeframe 3
                                                        </th>
                                                    }              
                                                    {item.MaxCycleMonth >= 6 &&
                                                    <th colspan="2" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>
                                                        timeframe 6
                                                        </th>
                                                    }
                                                    {item.MaxCycleMonth >= 9 &&
                                                    <th colspan="2" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>
                                                        timeframe 9
                                                        </th>
                                                    }                                                
                                                    {item.MaxCycleMonth >= 12 &&
                                                    <th colspan="2" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>
                                                        timeframe 12
                                                    </th>
                                                    }                                                   
                                                </tr>
                                                <tr>
                                                    {item.MaxCycleMonth >=0 &&
                                                    <>
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T30</th>
                                                    
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T40</th>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=3 &&
                                                    <>
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T30</th>
                                                    
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T40</th>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=6 &&
                                                    <>
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T30</th>
                                                    
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T40</th>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=9 &&
                                                    <>
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T30</th>
                                                    
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T40</th>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=12 &&
                                                    <>
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T30</th>
                                                    
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T40</th>
                                                    </>
                                                    }
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {item.Cycle_value_batch.map(itemBatch => <tr>
                                                    <td scope= "row" style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.Parameter_name}</td>
                                                    <td scope= "row" style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.Standard}</td>
                                                    {item.MaxCycleMonth >=0 &&
                                                    <>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T30_0}</td>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T40_0}</td>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=3 &&
                                                    <>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T30_3}</td>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T40_3}</td>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=6 &&
                                                    <>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T30_6}</td>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T40_6}</td>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=9 &&
                                                    <>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T30_9}</td>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T40_9}</td>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=12 &&
                                                    <>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T30_12}</td>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T40_12}</td>
                                                    </>
                                                    }
                                                </tr>)}
                                            </tbody>
                                        </table>                                    
                                    </div>
                                </div>
                            </div>
                            
                            {item.MaxCycleMonth >=18 && item.MaxCycleMonth != undefined && item.MaxCycleMonth != null && item.MaxCycleMonth != 99 &&
                            <div style={{ marginTop: '1rem' }}>
                                <div style={{ paddingLeft: '2rem', paddingLeft: '2rem' }}>
                                    <div style={{ width: '100%', overflowX: 'auto', margin: '2em 0' }}>                                    
                                        <table style={{ width: '100%', borderCollapse: 'collapse', border: '1px solid #ddd' }}>
                                            <thead>
                                                <tr>
                                                    <th rowSpan={'2'} style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>Parameter Name</th>
                                                    <th rowSpan={'2'} style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>Standard</th>
                                                    {item.MaxCycleMonth >= 18 &&
                                                    <th colspan="2" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>
                                                        timeframe 18
                                                        </th>
                                                    }
                                                    {item.MaxCycleMonth >= 24 &&
                                                    <th colspan="2" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>
                                                        timeframe 24
                                                        </th>
                                                    }              
                                                    {item.MaxCycleMonth >= 36 &&
                                                    <th colspan="2" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>
                                                        timeframe 36
                                                        </th>
                                                    }                                             
                                                </tr>
                                                <tr>
                                                    {item.MaxCycleMonth >=18 &&
                                                    <>
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T30</th>
                                                    
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T40</th>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=24 &&
                                                    <>
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T30</th>
                                                    
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T40</th>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=36 &&
                                                    <>
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T30</th>
                                                    
                                                    <th scope="col" style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>T40</th>
                                                    </>
                                                    }
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {item.Cycle_value_batch.map(itemBatch => <tr>
                                                    <td scope= "row" style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.Parameter_name}</td>
                                                    <td scope= "row" style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.Standard}</td>
                                                    {item.MaxCycleMonth >=18 &&
                                                    <>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T30_18}</td>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T40_18}</td>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=24 &&
                                                    <>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T30_24}</td>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T40_24}</td>
                                                    </>
                                                    }
                                                    {item.MaxCycleMonth >=36 &&
                                                    <>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T30_36}</td>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{itemBatch.T40_36}</td>
                                                    </>
                                                    }
                                                </tr>)}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            }
                        </div>
                        )} 
                        <p><strong>This document is printed by refer to SOP Document no : SOP-QC-O060</strong></p>                       
                        </>
                        }
                        {maxCycleMonth === 99 && <div style={{ textAlign: 'center', padding: '2em 0', textDecoration: 'underline' }}>Tidak ada data pengujian.</div>}
                    </div>
                </div>
            </div>
        </>
    );
}

export async function getServerSideProps(context) {
    const { query } = context;
    const { noProtocol, userDept, userData } = query;
    const { GetReport } = ProtocolApi();



    let report = [];
    let maxCycleMonth = 99;
    const { data: reportData,  maxCycleMonth: maxCycleMonthData} = await GetReport({ no_protocol: noProtocol });
    if (maxCycleMonthData != undefined && maxCycleMonthData != null) {
        maxCycleMonth = maxCycleMonthData
        if (reportData != undefined && reportData != null) {
            report = reportData;
        }
    }
    

    return {
        props: {
            noProtocol,
            report,
            maxCycleMonth
        },
    };
}
