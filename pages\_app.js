import '@/styles/globals.css';
import './app.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import './Dashboard.css';
import '@fortawesome/fontawesome-svg-core/styles.css';
import { UserProvider } from '@/contexts/UserContext';
import Head from 'next/head';
import { config } from '@fortawesome/fontawesome-svg-core';
import LoadingUI from '@/components/LoadingUI';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
config.autoAddCss = false;
import 'react-quill/dist/quill.snow.css';
import { iacrypt } from '@/lib/aicrypt/aiCrypt';


export default function App({
  Component,
  pageProps: { session, ...pageProps },
}) {
  const [loading, setLoading] = useState(false);
  const router = useRouter()

  useEffect(() => {
    const startLoading = () => {
      setLoading(true);
    };

    const stopLoading = () => {
      setLoading(false);
    };

    router.events.on('routeChangeStart', startLoading);
    router.events.on('routeChangeComplete', stopLoading);
    router.events.on('routeChangeError', stopLoading);

    return () => {
      router.events.off('routeChangeStart', startLoading);
      router.events.off('routeChangeComplete', stopLoading);
      router.events.off('routeChangeError', stopLoading);
    };
  }, []);

  useEffect(()=>{
    if (router.pathname === "/") {
            return;
    }

    if(router.pathname.includes("user_profile/changePassword")){
        return
    }


    if(router.pathname.includes("ForgotPassword")){
      return
    }

    if(router.pathname.includes("ResetPassword")){
      return
    }


    const encrypted = localStorage.getItem("session")
    if (encrypted) {
      const decrypted = iacrypt(encrypted)
      const dataLogin = JSON.parse(decrypted);      
      if (!dataLogin) {      
        localStorage.clear();
        router.push("/");
      } else if (new Date(dataLogin.expire) < new Date()){
        localStorage.clear();
        router.push("/");
      }
    } else{
        localStorage.clear();
        router.push("/")
    }
  },[router])

  return (
    <>
      <Head>
        <link rel="icon" href="/Logo_kalbe_header.png" />
      </Head>
      {loading && <LoadingUI />}
      <Component {...pageProps} />
    </>
  );
}
