import {
  faEdit,
  faMagnifyingGlass,
  faPrint,
  faSearch,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import {
  BsPencilFill,
  BsFillPeopleFill,
  BsUniversalAccessCircle,
} from 'react-icons/bs';

import { Button, Dropdown, Stack } from 'rsuite';
//import AES  from 'crypto-js/aes';

export default function Table(props) {
  const router = useRouter();
  const [dropdownValue, setDropdownValue] = useState([]);
  const [titleValue, setTitleValue] = useState({});

  let rowNumber = 1;

  const dropdownChangeHandler = (value) => {
    if (value) {
      if (dropdownValue.length === 0) {
        setDropdownValue([value]);
        return;
      } else {
        let newData = dropdownValue.filter(
          (item) => item.Id_Setup !== value.Id_Setup,
        );
        const result = [...newData, value];
        setDropdownValue(result);
        return;
      }
    }
  };

  useEffect(() => {
    if (dropdownValue.length > 0) {
      dropdownValue.map((item) => {
        let resultDataValue = { ...titleValue };
        resultDataValue[item.Id_Setup] = item.Reason_Desc;
        setTitleValue(resultDataValue);
      });

      props.detailReasonHandler(dropdownValue);
    }
  }, [dropdownValue]);

  const moveTo = (dataUser, path) => {
    router.push({
      pathname: path,
      query: dataUser,
    });
  };
  return (
    <div>
      {props.userData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col" style={{ minWidth: 150 }}>
                Employee ID
              </th>
              <th scope="col" style={{ minWidth: 150 }}>
                Employee Name
              </th>
              <th scope="col">Email</th>
              <th scope="col">Division</th>
              <th scope="col">Department</th>
              <th scope="col">is_Active</th>
              <th scope="col" style={{ minWidth: 150 }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {props.userData.map((item) => (
              <tr key={item.Employee_Id}>
                <td>{item.Employee_Id}</td>
                <td>{item.Name}</td>
                <td>{item.Email}</td>
                <td>{item.Division}</td>
                <td>{item.Department}</td>
                <td>{item.Is_active > 0 ? 'Yes' : 'No'}</td>
                <td>
                  <button
                    title="Edit User"
                    className="rounded border p-2 btn btn-warning"
                    onClick={() =>
                      router.push({
                        pathname: '/user_management/editUser/',
                        query: { userId: `${item.Employee_Id}` },
                      })
                    }
                  >
                    <BsPencilFill />
                  </button>
                  <button
                    title="User Mapping"
                    className="rounded border p-2 btn btn-success"
                    onClick={moveTo.bind(
                      null,
                      { emp_id: item.Employee_Id, emp_name: item.Name },
                      '/user_management/userMapping',
                    )}
                  >
                    <BsFillPeopleFill />
                  </button>
                  <button
                    title="User Access"
                    className="rounded border p-2 btn btn-danger"
                    onClick={moveTo.bind(
                      null,
                      { emp_id: item.Employee_Id, emp_name: item.Name },
                      '/user_management/userAccess',
                    )}
                  >
                    <BsUniversalAccessCircle />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {props.moduleData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col">Module Code</th>
              <th scope="col">Module Name</th>
              <th scope="col">Icon</th>
              <th scope="col">Actions</th>
            </tr>
          </thead>
          <tbody>
            {props.moduleData.map((item) => (
              <tr key={item.Module_Code}>
                <td>{item.Module_Code}</td>
                <td>{item.Module_Name}</td>
                <td>{item.Module_Icon}</td>
                <td>
                  <button
                    title="Edit Module"
                    className="rounded border p-2 btn btn-warning"
                    // onClick={() =>
                    //   router.push(
                    //     `/module_management/editModule/${item.Module_Code}`
                    //   )
                    // }
                    onClick={() =>
                      router.push({
                        pathname: '/module_management/editModule',
                        query: { moduleCode: `${item.Module_Code}` },
                      })
                    }
                  >
                    <BsPencilFill />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {props.menuData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col">ID</th>
              <th scope="col">Menu Name</th>
              <th scope="col">Menu Link</th>
              <th scope="col">Module Name</th>
              <th scope="col">Created At</th>
              <th scope="col">Is Parent</th>
              <th scope="col">Is Active</th>
              <th scope="col">Actions</th>
            </tr>
          </thead>
          <tbody>
            {props.menuData.map((item) => (
              <tr key={item.Id_Menu}>
                <td>{item.Id_Menu}</td>
                <td>{item.Menu_Name}</td>
                <td>{item.Menu_Link}</td>
                <td>{item.Module_Name}</td>
                <td>{item.Created_At}</td>
                <td>{item.Is_Parent > 0 ? 'Yes' : 'No'}</td>
                <td>{item.Is_Active > 0 ? 'Yes' : 'No'}</td>
                <td>
                  <button
                    title="Edit Menu"
                    className="rounded border p-2 btn btn-warning"
                    // onClick={() =>
                    //   router.push(`/menu_management/editMenu/${item.Id_Menu}`)
                    // }
                    onClick={() =>
                      router.push({
                        pathname: '/menu_management/editMenu',
                        query: { dataIdMenu: `${item.Id_Menu}` },
                      })
                    }
                  >
                    <BsPencilFill />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {props.measureData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Nilai Kekerasan</th>
              <th scope="col">Nilai Ketebalan</th>
              <th scope="col">Nilai Diameter</th>
            </tr>
          </thead>
          <tbody>
            {props.measureData.map((item) => {
              return (
                <tr>
                  <td>{rowNumber++}</td>
                  <td
                    style={
                      item.Hardness_Status === 'TMS'
                        ? { color: '#de0a02', fontWeight: 'bold' }
                        : { color: '#000' }
                    }
                  >
                    {item.Hardness_Value}
                  </td>
                  <td
                    style={
                      item.Thickness_Status === 'TMS'
                        ? { color: '#de0a02', fontWeight: 'bold' }
                        : { color: '#000' }
                    }
                  >
                    {item.Thickness_Value}
                  </td>
                  <td
                    style={
                      item.Diameter_Status === 'TMS'
                        ? { color: '#de0a02', fontWeight: 'bold' }
                        : { color: '#000' }
                    }
                  >
                    {item.Diameter_Value}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      )}

      {props.ipcHeader && (
        <table className="table" style={{ textAlign: 'center' }}>
          <thead>
            <tr key={rowNumber}>
              <th scope="col">No</th>
              <th scope="col">Date</th>
              <th scope="col">Operator Name</th>
              <th scope="col">Product Code</th>
              <th scope="col">Batch Code</th>
              <th scope="col">Amount TMS</th>
              <th scope="col">Amount MS</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            {props.ipcHeader.map((item) => {
              return (
                <tr key={item.Id_Transaction}>
                  <td>{rowNumber++}</td>
                  <td>{item.Date}</td>
                  <td>{item.Operator_Name}</td>
                  <td>{item.Product_Code}</td>
                  <td>{item?.Batch_code}</td>
                  <td>{item.Amount_TMS}</td>
                  <td>{item.Amount_MS}</td>
                  <td>
                    <button
                      title="View"
                      className="btn btn-primary rounded border p-2"
                      onClick={() => {
                        const data = {
                          id_transaction: item.Id_Transaction,
                          operator_name: item.Operator_Name,
                          product_code: item.Product_Code,
                          date_checked: item.Date,
                        };
                        props.setInterData(data);
                        props.showMeasurementDetail(data);
                      }}
                    >
                      <FontAwesomeIcon
                        icon={faMagnifyingGlass}
                        style={{ fontSize: 15 }}
                      />
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      )}

      {props.ipcDetailData && props.reasonData && (
        <table className="table" style={{ maxWidth: 760, textAlign: 'center' }}>
          <thead>
            <tr key={rowNumber}>
              <th scope="col">No</th>
              <th scope="col">Hardness</th>
              <th scope="col">Thickness</th>
              <th scope="col">Diameter</th>
              <th scope="col">Time HH:MM</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            {props.ipcDetailData.map((item) => {
              return (
                <tr key={item.Id_Setup}>
                  <td>{rowNumber++}</td>
                  <td
                    style={
                      item.Hardness_Status === 'TMS'
                        ? { color: '#de0a02', fontWeight: 'bold' }
                        : { color: '#000' }
                    }
                  >
                    {item.Hardness_Value}
                  </td>
                  <td
                    style={
                      item.Thickness_Status === 'TMS'
                        ? { color: '#de0a02', fontWeight: 'bold' }
                        : { color: '#000' }
                    }
                  >
                    {item.Thickness_Value}
                  </td>
                  <td
                    style={
                      item.Diameter_Status === 'TMS'
                        ? { color: '#de0a02', fontWeight: 'bold' }
                        : { color: '#000' }
                    }
                  >
                    {item.Diameter_Value}
                  </td>
                  <td>
                    {item.Time_insert}
                  </td>
                  <td>
                    {item.Overall_Status === 'TMS' && (
                      <Dropdown
                        title={
                          titleValue[item.Id_Setup]
                            ? titleValue[item.Id_Setup]
                            : '-- Select Reason--'
                        }
                        onSelect={dropdownChangeHandler}
                      >
                        <Dropdown.Item eventKey="">
                          -- Select Reason --
                        </Dropdown.Item>
                        {props.reasonData.map((itemReason) => (
                          <Dropdown.Item
                            eventKey={{
                              Id_Setup: item.Id_Setup,
                              ...itemReason,
                            }}
                          >
                            {itemReason.Reason_Desc}
                          </Dropdown.Item>
                        ))}
                      </Dropdown>
                    )}
                  </td>                 
                </tr>
              );
            })}
          </tbody>
        </table>
      )}

      {props?.allProductCodeData && (
        <table className="table" style={{ textAlign: 'center' }}>
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Product Code</th>
              <th scope="col">Hardness Value</th>
              <th scope="col">Diameter Value</th>
              <th scope="col">Thickness Value</th>
              <th scope="col">Moisture Value</th>
              <th scope="col">Start Date</th>
              <th scope="col">End Date</th>
              <th scope="col">Action</th>
              <th scope="col">Detail Step</th>
            </tr>
          </thead>
          <tbody>
            {props?.allProductCodeData.map((item) => {
              return (
                <tr key={`${item.id_setup}`}>
                  <td>{rowNumber++}</td>
                  <td>{item.product_code}</td>
                  <td>{item.hardness_range}</td>
                  <td>{item.diameter_range}</td>
                  <td>{item.thickness_range}</td>
                  <td>{item.ma_range}</td>
                  <td>{item.start_date}</td>
                  <td>{item.end_date}</td>
                  <td>
                    <Stack spacing={6}>
                      <Button
                        appearance="primary"
                        color="yellow"
                        onClick={() => {
                          // router.push(
                          //   `/user_module/masterdata/ProductCode/EditProduct/${item.id_setup}`
                          // );

                          router.push({
                            pathname:
                              '/user_module/masterdata/ProductCode/EditProductCode',
                            query: { idProductCode: `${item.id_setup}` },
                          });
                        }}
                      >
                        <FontAwesomeIcon
                          icon={faEdit}
                          style={{ fontSize: 15 }}
                        />
                      </Button>
                    </Stack>
                  </td>
                  <td>
                    <Stack spacing={6}>
                      <Button
                        appearance="primary"
                        color="blue"
                        onClick={() => {
                          // router.push(
                          //   `/user_module/masterdata/ProductCode/EditProduct/${item.id_setup}`
                          // );

                          router.push({
                            pathname:
                              '/user_module/masterdata/ProductCode/detail',
                            query: { id: `${item.id_setup}` },
                          });
                        }}
                      >
                        <FontAwesomeIcon
                          icon={faSearch}
                          style={{ fontSize: 15 }}
                        />
                      </Button>
                    </Stack>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      )}

      {props?.allInstrumentData && (
        <table className="table" style={{ textAlign: 'center' }}>
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Name</th>
              <th scope="col">Location</th>
              <th scope="col">Code Instrument</th>
              <th scope="col">Is Active </th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            {props?.allInstrumentData.map((item) => {
              return (
                <tr key={`${item.Instrument_Id}`}>
                  <td>{rowNumber++}</td>
                  <td>{item.Instrument_Name}</td>
                  <td>{item.Location}</td>
                  <td>{item.Code_Instrument}</td>
                  <td>{item.Is_Active}</td>
                  <td>
                    <Stack spacing={6}>
                      <Button
                        appearance="primary"
                        color="yellow"
                        onClick={() => {
                          // router.push(
                          //   `/user_module/masterdata/Instrument/EditInstrument/${item.Instrument_Id}`
                          // );
                          //encrypt
                          // const encryptedData ={
                          //   idInstrument: `${item.Instrument_Id}`
                          // };
                          // var ciphertext = AES.encrypt(`${item.Instrument_Id}`, `${process.env.NEXT_PUBLIC_PIMS_SERVICE}`).toString();
                          router.push({
                            pathname:
                              '/user_module/masterdata/Instrument/EditInstrument',
                            query: { idInstrument: `${item.Instrument_Id}` },
                          });
                        }}
                      >
                        <FontAwesomeIcon
                          icon={faEdit}
                          style={{ fontSize: 15 }}
                        />
                      </Button>
                    </Stack>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      )}

      {props.reportData && (
        <table className="table" style={{ textAlign: 'center' }}>
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Date</th>
              <th scope="col">Operator Name</th>
              <th scope="col">Product Code</th>
              <th scope="col">Amount MS</th>
              <th scope="col">Amount TMS</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            {props.reportData.map((item) => {
              return (
                <tr key={item.Id_Transaction_H}>
                  <td>{rowNumber++}</td>
                  <td>{item.Approve_Date}</td>
                  <td>{item.Employee_Name}</td>
                  <td>{item.Product_Code}</td>
                  <td>{item.Amount_MS}</td>
                  <td>{item.Amount_TMS}</td>
                  <td>
                    <Stack spacing={6}>
                      <Button
                        appearance="primary"
                        color="violet"
                        onClick={() => {
                          props.viewHandler(item.Id_Transaction_H);
                        }}
                      >
                        <FontAwesomeIcon
                          icon={faMagnifyingGlass}
                          style={{ fontSize: 15 }}
                        />
                      </Button>
                    </Stack>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      )}

      {props.reportMeasurementDetailData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Hardness</th>
              <th scope="col">Thickness</th>
              <th scope="col">Diameter</th>
              <th scope="col">Status</th>
              <th scope="col">Reason</th>
            </tr>
          </thead>
          <tbody>
            {props.reportMeasurementDetailData.map((item) => {
              return (
                <tr key={item.Id_Transaction_D}>
                  <td>{rowNumber++}</td>
                  <td
                    style={
                      item.Hardness_Status === 'TMS'
                        ? { color: '#de0a02', fontWeight: 'bold' }
                        : { color: '#000' }
                    }
                  >
                    {item.Hardness_Value}
                  </td>
                  <td
                    style={
                      item.Thickness_Status === 'TMS'
                        ? { color: '#de0a02', fontWeight: 'bold' }
                        : { color: '#000' }
                    }
                  >
                    {item.Thickness_Value}
                  </td>
                  <td
                    style={
                      item.Diameter_Status === 'TMS'
                        ? { color: '#de0a02', fontWeight: 'bold' }
                        : { color: '#000' }
                    }
                  >
                    {item.Diameter_Value}
                  </td>
                  <td>{item.Overall_Status}</td>
                  <td>{item.Reason === 'N/A' ? '-' : item.Reason}</td>
                </tr>
              );
            })}
          </tbody>
        </table>
      )}

      {props.departmentData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Department</th>
              <th scope="col">Status</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            {props.departmentData.map((item) => (
              <tr key={item.Id_Setup}>
                <td>{rowNumber++}</td>
                <td>{item.Department_Name}</td>
                <td>{item.Is_Active === 1 ? 'Active' : 'Inactive'}</td>
                <td>
                  <button
                    title="Edit User"
                    className="rounded border p-2 btn btn-warning"
                    onClick={() =>
                      router.push({
                        pathname: '/department_management/editDepartment/',
                        query: { idDepartment: `${item.Id_Setup}` },
                      })
                    }
                  >
                    <BsPencilFill />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {props.parameterData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Parameter</th>
              <th scope="col">Cara Input</th>
              <th scope="col">Is Active</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            {props.parameterData.map((item) => (
              <tr key={item.Id_Parameter}>
                <td>{rowNumber++}</td>
                <td>{item.Parameter_Name}</td>
                <td>{item.Method_Desc}</td>
                <td>{item.Is_Active === 1 ? 'Active' : 'Inactive'}</td>
                <td>
                  <button
                    title="Edit User"
                    className="rounded border p-2 btn btn-warning"
                    onClick={() =>
                      router.push({
                        pathname:
                          '/user_module/masterdata/Parameter/EditParameter',
                        query: { idParameter: item.Id_Parameter },
                      })
                    }
                  >
                    <BsPencilFill />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {props.reviewerApproverData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Name</th>
              <th scope="col">Department</th>
              <th scope="col">Role</th>
              <th scope="col">Is Active</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            {props.reviewerApproverData.map((item) => (
              <tr key={item.Id_Setup}>
                <td>{rowNumber++}</td>
                <td>{item.Employee_Name}</td>
                <td>{item.Department_Name}</td>
                <td>{item.Role_Desc}</td>
                <td>{item.Is_Active === 1 ? 'Active' : 'Inactive'}</td>
                <td>
                  <button
                    title="Edit User"
                    className="rounded border p-2 btn btn-warning"
                    onClick={() =>
                      router.push({
                        pathname:
                          '/user_module/masterdata/ReviewerApprover/EditReviewerApprover',
                        query: { idSetup: item.Id_Setup },
                      })
                    }
                  >
                    <BsPencilFill />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {props.protocolData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">No Protocol</th>
              <th scope="col">Dibuat Oleh</th>
              <th scope="col">Versi</th>
              <th scope="col">Tindakan</th>
            </tr>
          </thead>
          <tbody>
            {props.protocolData.map((item) => (
              <tr key={item.Id_Setup}>
                <td>{rowNumber++}</td>
                <td>{item.No_Protocol}</td>
                <td>{item.Created_By}</td>
                <td>{item.Version}</td>
                <td>{item.Is_Active === 1 ? 'Active' : 'Inactive'}</td>
                <td>
                  <button
                    title="Edit User"
                    className="rounded border p-2 btn btn-warning"
                    onClick={() =>
                      router.push({
                        pathname:
                          '/user_module/masterdata/ReviewerApprover/EditReviewerApprover',
                        query: { idSetup: item.Id_Setup },
                      })
                    }
                  >
                    <BsPencilFill />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {props.rejectReasonData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Reject Reason</th>
              <th scope="col">Active</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            {props.rejectReasonData.map((item) => (
              <tr key={item.Id_Reason}>
                <td>{rowNumber++}</td>
                <td>{item.Reason_Desc}</td>
                <td>{parseInt(item.Is_Active) === 1 ? 'Yes' : 'No'}</td>
                <td>
                  <button
                    title="Edit Reason"
                    className="rounded border p-2 btn btn-warning"
                    onClick={() =>
                      router.push({
                        pathname:
                          '/user_module/masterdata/RejectReason/EditRejectReason',
                        query: { idRejectReason: item.Id_Reason },
                      })
                    }
                  >
                    <BsPencilFill />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {props.approvalProtocolHeaderData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">No Protocol</th>
              <th scope="col">Diajukan oleh </th>
              <th scope="col">Versi</th>
              <th scope="col">Tipe Protokol</th>
              <th scope="col">Status</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            {props.approvalProtocolHeaderData.map((item) => (
              <tr key={item.Id_Protocol}>
                <td>{rowNumber++}</td>
                <td>{item.No_Protocol}</td>
                <td>{item.Created_by}</td>
                <td>{item.Protocol_Version}</td>
                <td>{item.Department_Name}</td>
                <td>
                  <p style={{ fontSize: '0.8rem', color: 'gray' }}>
                    {item.Approval_Status == '3' ? 'Waiting' : ''}
                    {item.Approval_Status == '2' ? 'Ready for Approval' : ''}
                    {item.Approval_Status == '1' ? 'Approved' : ''}
                    {item.Approval_Status == '0' ? 'Rejected' : ''}
                  </p>
                </td>
                <td>
                  <Stack spacing={6}>

                    <button
                      title="Tinjau Protokol"
                      className="rounded border p-2 btn btn-warning"
                      onClick={() =>
                        router.push({
                          pathname:
                            '/user_module/protocol/management/ProtocolApprovalDetail',
                          query: { noProtocol: item.No_Protocol },
                        })
                      }
                    >
                      <FontAwesomeIcon icon={faSearch} style={{ fontSize: 15 }} />
                    </button>
                    <button
                      title="Print Document"
                      className="rounded border p-2 btn btn-success"
                      onClick={() => router.push({ pathname: '/user_module/protocol/PrintProtocolDoc', query: { noProtocol: item.No_Protocol, userDept: parseInt(props.userDeptId) } })}
                    >
                      <FontAwesomeIcon icon={faPrint} style={{ fontSize: 15 }} />
                    </button>
                  </Stack>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {props.browseProtocolData && (
        <table className="table">
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">No Protocol</th>
              <th scope="col">Diajukan oleh </th>
              <th scope="col">Versi</th>
              <th scope="col">Tipe Protokol</th>
              <th scope="col">Status</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            {props.browseProtocolData.map((item) => (
              <tr key={item.Id_Protocol}>
                <td>{item.indexNo}</td>
                <td>{item.No_Protocol}</td>
                <td>{item.Created_by}</td>
                <td>{item.Protocol_Version}</td>
                <td>{item.Department_Name}</td>
                <td>
                  <p style={{ fontSize: '0.8rem', color: 'gray' }}>
                    {item.Document_Status === '3' ? 'Waiting' : ''}
                    {item.Document_Status === '1' ? 'Approved' : ''}
                    {item.Document_Status === '0' ? 'Rejected' : ''}
                  </p>
                </td>
                <td>
                  <Stack spacing={6}>

                    <button
                      title="Tinjau Protokol"
                      className="rounded border p-2 btn btn-warning"
                      onClick={() =>
                        router.push({
                          pathname:
                            '/user_module/protocol/ViewProtocol',
                          query: { noProtocol: item.No_Protocol },
                        })
                      }
                    >
                      <FontAwesomeIcon icon={faSearch} style={{ fontSize: 15 }} />
                    </button>
                    <button
                      title="Print Document"
                      className="rounded border p-2 btn btn-success"
                      onClick={() => router.push({ pathname: '/user_module/protocol/PrintProtocolDoc', query: { noProtocol: item.No_Protocol, userDept: parseInt(props.userDeptId) } })}
                    >
                      <FontAwesomeIcon icon={faPrint} style={{ fontSize: 15 }} />
                    </button>
                  </Stack>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {props.reviewerHistoryData && (
        <table className="table" style={{ minWidth: '30rem' }}>
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Reviewer</th>
              <th scope="col">Status</th>
              <th scope="col">Date </th>
            </tr>
          </thead>
          <tbody>
            {props.reviewerHistoryData.map((item) => {
              if (item.Approval_Status == 0) {
                return (
                  <tr key={item.Id_Protocol}>
                    <td style={{ color: 'red' }}>{rowNumber++}</td>
                    <td style={{ color: 'red' }}>{item.Approve_By}</td>
                    <td style={{ color: 'red' }}>{item.Status}</td>
                    <td style={{ color: 'red' }}>{item.Date}</td>
                  </tr>
                );
              } else if (item.Approval_Status == 1 || item.Approval_Status == 5) {
                return (
                  <tr key={item.Id_Protocol}>
                    <td style={{ color: 'green' }}>{rowNumber++}</td>
                    <td style={{ color: 'green' }}>{item.Approve_By}</td>
                    <td style={{ color: 'green' }}>{item.Status}</td>
                    <td style={{ color: 'green' }}>{item.Date}</td>
                  </tr>
                );
              } else {
                return (
                  <tr key={item.Id_Protocol}>
                    <td>{rowNumber++}</td>
                    <td>{item.Approve_By}</td>
                    <td>{item.Status}</td>
                    <td>{item.Date}</td>
                  </tr>
                );
              }
            })}
          </tbody>
        </table>
      )}

      {props.approvalHistoryData && (
        <table className="table" style={{ minWidth: '30rem' }}>
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Name</th>
              <th scope="col">Role</th>
              <th scope="col">Status</th>
              <th scope="col">Reason</th>
              <th scope="col">Date </th>
            </tr>
          </thead>
          <tbody>
            {props.approvalHistoryData.map((item) => {
              if (item.Approval_Status == 0) {
                return (
                  <tr key={item.Id_Protocol}>
                    <td style={{ color: 'red' }}>{rowNumber++}</td>
                    <td style={{ color: 'red' }}>{item.Approve_By}</td>
                    <td style={{ color: 'red' }}>{item.Role}</td>
                    <td style={{ color: 'red' }}>{item.Status}</td>
                    <td style={{ color: 'red' }}>{item.Reject_reason}</td>
                    <td style={{ color: 'red' }}>{item.Date}</td>
                  </tr>
                );
              } else if (item.Approval_Status == 1 || item.Approval_Status == 5) {
                return (
                  <tr key={item.Id_Protocol}>
                    <td style={{ color: 'green' }}>{rowNumber++}</td>
                    <td style={{ color: 'green' }}>{item.Approve_By}</td>
                    <td style={{ color: 'green' }}>{item.Role}</td>
                    <td style={{ color: 'green' }}>{item.Status}</td>
                    <td style={{ color: 'green' }}>{item.Reject_reason}</td>
                    <td style={{ color: 'green' }}>{item.Date}</td>
                  </tr>
                );
              } else {
                return (
                  <tr key={item.Id_Protocol}>
                    <td>{rowNumber++}</td>
                    <td>{item.Approve_By}</td>
                    <td>{item.Role}</td>
                    <td>{item.Status}</td>
                    <td>{item.Reject_reason}</td>
                    <td>{item.Date}</td>
                  </tr>
                );
              }
            })}
          </tbody>
        </table>
      )}

      {/* Approval History for ViewProtocol page */}
      {props.approverHistoryData && (
        <table className="table" style={{ minWidth: '30rem' }}>
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Approver</th>
              <th scope="col">Status</th>
              <th scope="col">Date </th>
            </tr>
          </thead>
          <tbody>
            {props.approverHistoryData.map((item) => {
              if (item.Approval_Status == 0) {
                return (
                  <tr key={item.Id_Protocol}>
                    <td style={{ color: 'red' }}>{rowNumber++}</td>
                    <td style={{ color: 'red' }}>{item.Approve_By}</td>
                    <td style={{ color: 'red' }}>{item.Status}</td>
                    <td style={{ color: 'red' }}>{item.Date}</td>
                  </tr>
                );
              } else if (item.Approval_Status == 1 || item.Approval_Status == 5) {
                return (
                  <tr key={item.Id_Protocol}>
                    <td style={{ color: 'green' }}>{rowNumber++}</td>
                    <td style={{ color: 'green' }}>{item.Approve_By}</td>
                    <td style={{ color: 'green' }}>{item.Status}</td>
                    <td style={{ color: 'green' }}>{item.Date}</td>
                  </tr>
                );
              } else {
                return (
                  <tr key={item.Id_Protocol}>
                    <td>{rowNumber++}</td>
                    <td>{item.Approve_By}</td>
                    <td>{item.Status}</td>
                    <td>{item.Date}</td>
                  </tr>
                );
              }
            })}
          </tbody>
        </table>
      )}

      {/* Approval History for ProtocolApprovalDetail page */}
      {props.approvalHistoryForApproval && (
        <table className="table" style={{ minWidth: '30rem' }}>
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">Approver</th>
              <th scope="col">Status</th>
              <th scope="col">Date </th>
            </tr>
          </thead>
          <tbody>
            {props.approvalHistoryForApproval.map((item) => {
              if (item.Approval_Status == 0) {
                return (
                  <tr key={item.Id_Protocol}>
                    <td style={{ color: 'red' }}>{rowNumber++}</td>
                    <td style={{ color: 'red' }}>{item.Approve_By}</td>
                    <td style={{ color: 'red' }}>{item.Status}</td>
                    <td style={{ color: 'red' }}>{item.Date}</td>
                  </tr>
                );
              } else if (item.Approval_Status == 1 || item.Approval_Status == 5) {
                return (
                  <tr key={item.Id_Protocol}>
                    <td style={{ color: 'green' }}>{rowNumber++}</td>
                    <td style={{ color: 'green' }}>{item.Approve_By}</td>
                    <td style={{ color: 'green' }}>{item.Status}</td>
                    <td style={{ color: 'green' }}>{item.Date}</td>
                  </tr>
                );
              } else if (item.Approval_Status == 2) {
                return (
                  <tr key={item.Id_Protocol}>
                    <td style={{ fontWeight: 'bolder' }}>{rowNumber++}</td>
                    <td style={{ fontWeight: 'bolder' }}>{item.Approve_By}</td>
                    <td style={{ fontWeight: 'bolder' }}>{item.Status}</td>
                    <td style={{ fontWeight: 'bolder' }}>{item.Date}</td>
                  </tr>
                );
              } else {
                return (
                  <tr key={item.Id_Protocol}>
                    <td>{rowNumber++}</td>
                    <td>{item.Approve_By}</td>
                    <td>{item.Status}</td>
                    <td>{item.Date}</td>
                  </tr>
                );
              }
            })}
          </tbody>
        </table>
      )}

      {props.historyChanges && (
        <table className='table table-bordered' style={{ fontSize: '0.75rem' }}>
          <thead>
            <tr>
              <th scope="col">No</th>
              <th scope="col">No Dokumen</th>
              <th scope="col">Tanggal perubahan</th>
              <th scope="col">Detail perubahan </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>1</td>
              <td>Bondan</td>
              <td>2023-10-10</td>
              <td>Test 1</td>
            </tr>
          </tbody>
        </table>
      )
      }

    </div >
  );
}
