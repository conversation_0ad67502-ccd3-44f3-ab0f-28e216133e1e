import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, useToaster, Notification, Loader } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import ApiMachineOeeHeaderStripping from "@/pages/api/oee/machine_oee_stripping/api_machine_oee_stripping";
import { useRouter } from "next/router";

export default function ReportingListStrippingPage() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const toaster = useToaster();
  const router = useRouter();

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [reportingListStripping, setReportingListStripping] = useState([]);

  const [loading, setLoading] = useState(false);

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = reportingListStripping.filter((rowData, i) => {
    const searchFields = ["id_oee_header", "id_machine_oee", "category_type", "machine_name", "start_time", "end_time", "duration", "created_date", "created_by", "updated_date", "updated_by", "deleted_date", "deleted_by", "is_active"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const formatDateTime = (isoDateString) => {
    if (!isoDateString) return "";

    const date = new Date(isoDateString);
    const options = {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
      timeZone: "UTC",
    };

    return date.toLocaleString("id-ID", options).replace(",", "");
  };

  const formatDateTimeCustomNoIso = (isoDateString) => {
    if (!isoDateString) return "-";
    const [datePart, timePart] = isoDateString.split("T");
    const [year, month, day] = datePart.split("-");
    const [hours, minutes] = timePart.split(":");

    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  const totalRowCount = searchKeyword ? filteredData.length : reportingListStripping.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("oee/reporting/list-stripping"));
      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      HandleGetAllActiveHeaderStrippingApi();
    }
  }, []);

  const HandleGetAllActiveHeaderStrippingApi = async () => {
    setLoading(true);
    try {
      const res = await ApiMachineOeeHeaderStripping().GetAllActiveMachineOeeListStripping();

      if (res.status === 200) {
        const activeStripping = res.data.data || [];

        setReportingListStripping(activeStripping);
      } else {
        console.log("Error on Get All Api:", res.message);
        setReportingListStripping([]);
      }
    } catch (error) {
      console.error("Error on catch Get All Api:", error);
      setReportingListStripping([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Halaman Laporan Stripping</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>OEE</Breadcrumb.Item>
                  <Breadcrumb.Item>Reporting</Breadcrumb.Item>
                  <Breadcrumb.Item>List</Breadcrumb.Item>
                  <Breadcrumb.Item active>Reporting Stripping Page</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Halaman Laporan Stripping</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              {loading ? (
                <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                  <Loader size="sm" content="Loading..." />
                </div>
              ) : (
                <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                  <Column width={70} align="center" fixed>
                    <HeaderCell>No</HeaderCell>
                    <Cell>
                      {(rowData, rowIndex) => {
                        return rowIndex + 1 + limit * (page - 1);
                      }}
                    </Cell>
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Waktu Mulai</HeaderCell>
                    <Cell>{(rowData) => formatDateTimeCustomNoIso(rowData.start_time)}</Cell>
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Waktu Selesai</HeaderCell>
                    <Cell>{(rowData) => formatDateTimeCustomNoIso(rowData.end_time)}</Cell>
                  </Column>

                  <Column width={120} sortable fullText>
                    <HeaderCell align="center">Durasi (Menit)</HeaderCell>
                    <Cell dataKey="duration" align="center" />
                  </Column>

                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Dibuat Tanggal</HeaderCell>
                    <Cell>{(rowData) => new Date(rowData.created_date).toLocaleDateString("en-GB")}</Cell>
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                    <Cell dataKey="created_by" />
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Diperbarui Tanggal</HeaderCell>
                    <Cell>{(rowData) => (rowData.updated_date ? new Date(rowData.updated_date).toLocaleDateString("en-GB") : "")}</Cell>
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                    <Cell dataKey="updated_by" />
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Dihapus Tanggal</HeaderCell>
                    <Cell>{(rowData) => formatDateTimeCustomNoIso(rowData.deleted_date)}</Cell>
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                    <Cell dataKey="deleted_by" />
                  </Column>
                  <Column width={80} fixed="right" align="center">
                    <HeaderCell>Action</HeaderCell>
                    <Cell style={{ padding: "8px" }}>
                      {(rowData) => (
                        <div>
                          <Button
                            appearance="subtle"
                            onClick={() => {
                              const id = rowData.id_oee_header;
                              router.push(`/user_module/oee/reporting/list-stripping/detail?Id=${id}`);
                            }}
                          >
                            <SearchIcon style={{ fontSize: "16px" }} />
                          </Button>
                        </div>
                      )}
                    </Cell>
                  </Column>
                </Table>
              )}

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
