import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiTransactionHeader() {
  return {
    getAllTransactionHeader: createApiFunction("get", "pqr/transaction_h/list"),
    getAllNeedApproveTransactionHeader: createApiFunction("get", "pqr/transaction_h/need-approve"),
    getAllFullyApproveTransactionHeader: createApiFunction("get", "pqr/transaction_h/fully-approve"),
    createTransactionHeader: createApiFunction("post", "pqr/transaction_h/create"),
    editTransactionHeader: createApiFunction("put", "pqr/transaction_h/edit"),
    editStatusTransactionHeader: createApiFunction("put", "pqr/transaction_h/edit-status"),
    getPPITransactionHeaderById: createApiFunction("post", "pqr/transaction_h/ppi_byIdHeader"),
  };
}
