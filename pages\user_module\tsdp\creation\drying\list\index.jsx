import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Divider,
  Form,
  useToaster,
  Loader,
  Tooltip,
  Whisper,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import PlusIcon from "@rsuite/icons/Plus";
import EditIcon from "@rsuite/icons/Edit";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import ApiTsdpTDDrying from "@/pages/api/tsdp/api_tsdp_td_drying";
import ApiTsdpTH from "@/pages/api/tsdp/transaction_h/api_tsdp_transaction_h";
import { useRouter } from "next/router";
import Messages from "@/components/Messages";

export default function ListDrying() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_detail_trans");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [dryingData, setDryingData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const router = useRouter();
  const toaster = useToaster();

  // Modal states
  const [showSpvModal, setShowSpvModal] = useState(false);
  const [showMgrModal, setShowMgrModal] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedDryingId, setSelectedDryingId] = useState(null);
  const [selectedDryingStatus, setSelectedDryingStatus] = useState(null);
  const [headerTransactionData, setHeaderTransactionData] = useState([]);
  const [showHeaderModal, setShowHeaderModal] = useState(false);
  const [selectedHeaderData, setSelectedHeaderData] = useState(null);
  const [mergedDryingData, setMergedDryingData] = useState([]);

  const mergeDryingWithHeaderData = () => {
    if (dryingData.length === 0 || headerTransactionData.length === 0) return;

    const merged = dryingData.map((drying) => {
      const matchingHeader = headerTransactionData.find(
        (header) => header.id_header_trans === drying.id_header_trans
      );

      return {
        ...drying,
        headerData: matchingHeader || null,
      };
    });

    setMergedDryingData(merged);
  };

  useEffect(() => {
    mergeDryingWithHeaderData();
  }, [dryingData, headerTransactionData]);

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const handleAddClick = () => {
    router.push("/user_module/tsdp/creation/drying/add");
  };

  const formatDate = (dateString) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("en-GB");
  };

  const handleSpvClick = (rowData) => {
    setSelectedRow(rowData);
    setShowSpvModal(true);
  };

  const handleMgrClick = (rowData) => {
    setSelectedRow(rowData);
    setShowMgrModal(true);
  };

  const handleStatusClick = (id, isActive) => {
    setSelectedDryingId(id);
    setSelectedDryingStatus(isActive);
    setShowDeleteModal(true);
  };

  const handleUpdateStatus = async () => {
    try {
      setDeleteLoading(true);
      const res = await ApiTsdpTDDrying().UpdateStatusTsTransactionDrying({
        id_detail_trans: selectedDryingId,
        is_active: selectedDryingStatus,
        deleted_by:
          sessionAuth?.employee_id + " - " + sessionAuth?.employee_name,
      });

      if (res.status === 200) {
        if (selectedDryingStatus === 1) {
          toaster.push(Messages("success", "Drying berhasil dihapus"), {
            duration: 3000,
          });
        } else {
          toaster.push(Messages("success", "Drying berhasil diaktifkan"), {
            duration: 3000,
          });
        }
        fetchDryingData();
      } else {
        toaster.push(
          Messages(
            "error",
            `Error: "${res.message}". Silakan coba lagi nanti!`
          ),
          { duration: 3000 }
        );
      }
    } catch (error) {
      console.error("Error updating drying status:", error);
      toaster.push(Messages("error", "Gagal mengubah status drying"), {
        duration: 3000,
      });
    } finally {
      setDeleteLoading(false);
      setShowDeleteModal(false);
    }
  };

  const filteredData = mergedDryingData.filter((rowData) => {
    const searchFields = [
      "id_detail_trans",
      "id_header_trans",
      "drying_remarks",
      "drying_lod",
      "drying_product_temp",
      "drying_exhaust_temp",
      "drying_remarks",
      "created_date",
      "updated_date",
      "deleted_date",
      "created_by",
      "updated_by",
      "deleted_by",
    ];

    if (rowData.headerData) {
      searchFields.push(
        "headerData.product_code",
        "headerData.product_name",
        "headerData.batch_no",
        "headerData.sediaan_type"
      );
    }

    return searchFields.some((field) => {
      const fieldParts = field.split(".");
      let value;

      if (fieldParts.length === 1) {
        value = rowData[field];
      } else if (fieldParts.length === 2 && rowData[fieldParts[0]]) {
        value = rowData[fieldParts[0]][fieldParts[1]];
      }

      return value
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase());
    });
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword
    ? filteredData.length
    : mergedDryingData.length;

  const fetchDryingData = async () => {
    try {
      setLoading(true);
      const res = await ApiTsdpTDDrying().getAllTsTransactionDrying();
      if (res.status === 200) {
        setDryingData(res.data);
      } else {
        console.log("Error fetching drying data:", res.message);
      }
    } catch (error) {
      console.log("Error in catch block:", error);
    } finally {
      setLoading(false);
    }
  };

  const GetAllHeaderTransaction = async () => {
    try {
      const res = await ApiTsdpTH().getAllTsTransactionHeader();
      if (res.status === 200) {
        setHeaderTransactionData(res.data);
      } else {
        console.log("Error fetching header transaction data:", res.message);
      }
    } catch (error) {
      console.log("Error in catch block:", error);
    }
  };

  const showHeaderDetails = (id_header_trans) => {
    const headerData = headerTransactionData.find(
      (header) => header.id_header_trans === id_header_trans
    );

    const dryingDataForHeader = dryingData.find(
      (drying) => drying.id_header_trans === id_header_trans
    );

    if (headerData) {
      setSelectedHeaderData({
        ...headerData,
        dryingData: dryingDataForHeader,
      });
      setShowHeaderModal(true);
    } else {
      toaster.push(
        Messages("warning", "Header data not found. Please refresh the page."),
        { duration: 3000 }
      );
    }
  };

  const getStatusLabel = (statusCode) => {
    switch (statusCode) {
      case 3:
        return { label: "Draft", color: "blue" };
      case 2:
        return { label: "Waiting Approval", color: "violet" };
      case 1:
        return { label: "Approved", color: "green" };
      case 0:
        return { label: "Revised", color: "yellow" };
      default:
        return { label: "Unknown", color: "black" };
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push("/");
    } else {
      fetchDryingData();
      GetAllHeaderTransaction();
    }
  }, []);

  return (
    <div>
      <Head>
        <title>Drying List</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>TSDP</Breadcrumb.Item>
                  <Breadcrumb.Item>Creation</Breadcrumb.Item>
                  <Breadcrumb.Item active>Drying List</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Drying List</h5>
              </Stack>
            }></Panel>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusIcon />}
                    appearance="primary"
                    onClick={handleAddClick}>
                    Add
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}>
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }>
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
              loading={loading}>
              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id_detail_trans" />
              </Column>
              <Column width={70} align="center" sortable>
                <HeaderCell>Header ID</HeaderCell>
                <Cell dataKey="id_header_trans" />
              </Column>
              <Column width={120} sortable resizable>
                <HeaderCell align="center">Kode Batch</HeaderCell>
                <Cell>{(rowData) => rowData.headerData?.batch_no || "-"}</Cell>
              </Column>
              <Column width={120} align="center" sortable resizable>
                <HeaderCell>Line Type</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <Tag
                      color={
                        rowData.headerData?.line_type === 1 ? "green" : "blue"
                      }>
                      {rowData.headerData?.line_type === 1
                        ? "Automate"
                        : "Manual"}
                    </Tag>
                  )}
                </Cell>
              </Column>
              <Column width={120} sortable resizable>
                <HeaderCell align="center">Drying Date</HeaderCell>
                <Cell>{(rowData) => formatDate(rowData.drying_date)}</Cell>
              </Column>
              <Column width={120} sortable resizable>
                <HeaderCell align="center">lod</HeaderCell>
                <Cell dataKey="drying_lod" />
              </Column>
              <Column width={120} sortable resizable>
                <HeaderCell align="center">Product Temp</HeaderCell>
                <Cell dataKey="drying_product_temp" />
              </Column>
              <Column width={120} sortable resizable>
                <HeaderCell align="center">Exhaust Temp</HeaderCell>
                <Cell dataKey="drying_exhaust_temp" />
              </Column>
              <Column width={200} sortable fullText resizable>
                <HeaderCell align="center">Remarks</HeaderCell>
                <Cell dataKey="drying_remarks" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell align="center">Created Date</HeaderCell>
                <Cell>{(rowData) => formatDate(rowData.created_date)}</Cell>
              </Column>
              <Column width={120} sortable resizable>
                <HeaderCell align="center">Created By</HeaderCell>
                <Cell dataKey="created_by" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell align="center">Updated Date</HeaderCell>
                <Cell>{(rowData) => formatDate(rowData.updated_date)}</Cell>
              </Column>
              <Column width={120} sortable resizable>
                <HeaderCell align="center">Updated By</HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell align="center">Deleted Date</HeaderCell>
                <Cell>{(rowData) => formatDate(rowData.deleted_date)}</Cell>
              </Column>
              <Column width={120} sortable resizable>
                <HeaderCell align="center">Deleted By</HeaderCell>
                <Cell dataKey="deleted_by" />
              </Column>
              <Column width={120} sortable resizable align="center">
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}>
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={120} fixed="right" align="center">
                <HeaderCell>Approval Status</HeaderCell>
                <Cell>
                  {(rowData) => {
                    const status = getStatusLabel(rowData.status_approval);

                    const tooltip = (
                      <Tooltip>
                        {rowData.revised_remarks || "No remarks available"}
                      </Tooltip>
                    );

                    return rowData.status_approval === 0 ? (
                      <Whisper
                        placement="top"
                        controlId="control-id-focus"
                        trigger="hover"
                        speaker={tooltip}>
                        <Tag color={status.color}>{status.label}</Tag>
                      </Whisper>
                    ) : (
                      <Tag color={status.color}>{status.label}</Tag>
                    );
                  }}
                </Cell>
              </Column>
              <Column width={120} fixed="right" align="center">
                <HeaderCell>Trans H</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <Button
                      appearance="ghost"
                      size="xs"
                      onClick={() =>
                        showHeaderDetails(rowData.id_header_trans)
                      }>
                      Show Header
                    </Button>
                  )}
                </Cell>
              </Column>
              <Column width={180} fixed="right" align="center">
                <HeaderCell>Approval Information</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        gap: "8px",
                      }}>
                      <Button
                        appearance="ghost"
                        size="xs"
                        onClick={() => handleSpvClick(rowData)}>
                        Supervisor
                      </Button>
                      <Button
                        appearance="ghost"
                        color="orange"
                        size="xs"
                        onClick={() => handleMgrClick(rowData)}>
                        Manager
                      </Button>
                    </div>
                  )}
                </Cell>
              </Column>
              <Column width={100} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        gap: "8px",
                      }}>
                      <Button
                        appearance="subtle"
                        onClick={() =>
                          router.push({
                            pathname: "/user_module/tsdp/creation/drying/edit",
                            query: { data: rowData.id_detail_trans },
                          })
                        }>
                        <EditIcon style={{ fontSize: "16px" }} />
                      </Button>

                      {rowData.is_active === 1 ? (
                        <Button
                          appearance="subtle"
                          onClick={() =>
                            handleStatusClick(
                              rowData.id_detail_trans,
                              rowData.is_active
                            )
                          }>
                          <TrashIcon
                            style={{ fontSize: "16px", color: "red" }}
                          />
                        </Button>
                      ) : (
                        <Button
                          appearance="subtle"
                          onClick={() =>
                            handleStatusClick(
                              rowData.id_detail_trans,
                              rowData.is_active
                            )
                          }>
                          <ReloadIcon
                            style={{ fontSize: "16px", color: "green" }}
                          />
                        </Button>
                      )}
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>

            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>

      {/* Header Details Modal */}
      <Modal
        open={showHeaderModal}
        onClose={() => setShowHeaderModal(false)}
        size="md">
        <Modal.Header>
          <Modal.Title>Transaction Header Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedHeaderData ? (
            <div>
              <h6 className="mt-4 mb">Transaction Header</h6>
              <Divider />
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Form fluid>
                    <Form.Group>
                      <Form.ControlLabel>Header ID</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.id_header_trans}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Line Type</Form.ControlLabel>
                      <Form.Control
                        value={
                          selectedHeaderData.line_type === 1
                            ? "Automate"
                            : "Manual"
                        }
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Line ID</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.id_line}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Sediaan Type</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.sediaan_type}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Product Name</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.product_name}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Process Purpose</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.process_purpose}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Background</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.background}
                        readOnly
                      />
                    </Form.Group>
                  </Form>
                </div>

                <div>
                  <Form fluid>
                    <Form.Group>
                      <Form.ControlLabel>Product Code</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.product_code}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Batch Number</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.batch_no}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Production Scale</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.production_scale}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Trial Focus</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.trial_focus}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>PPI Number</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.ppi_no}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Process Date</Form.ControlLabel>
                      <Form.Control
                        value={formatDate(selectedHeaderData.process_date)}
                        readOnly
                      />
                    </Form.Group>
                  </Form>
                </div>
              </div>
              <div>
                <h6 className="mt-4 mb">Document Paths</h6>
                <Divider />
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Form fluid>
                      <Form.Group>
                        <Form.ControlLabel>CQA Path</Form.ControlLabel>
                        <Form.Control
                          value={selectedHeaderData.cqa_path || "-"}
                          readOnly
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>QBD Path</Form.ControlLabel>
                        <Form.Control
                          value={selectedHeaderData.qbd_path || "-"}
                          readOnly
                        />
                      </Form.Group>
                    </Form>
                  </div>

                  <div>
                    <Form fluid>
                      <Form.Group>
                        <Form.ControlLabel>QTTP Path</Form.ControlLabel>
                        <Form.Control
                          value={selectedHeaderData.qttp_path || "-"}
                          readOnly
                        />
                      </Form.Group>
                    </Form>
                  </div>
                </div>
              </div>
              <div className="mt-4">
                <h6 className="mt-4 mb">Information</h6>
                <Divider />
                <div className="grid grid-cols-2 gap-4">
                  <Form fluid>
                    <Form.Group>
                      <Form.ControlLabel>Created Date</Form.ControlLabel>
                      <Form.Control
                        value={formatDate(selectedHeaderData.created_date)}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Updated Date</Form.ControlLabel>
                      <Form.Control
                        value={formatDate(selectedHeaderData.updated_date)}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Deleted Date</Form.ControlLabel>
                      <Form.Control
                        value={formatDate(selectedHeaderData.deleted_date)}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Status</Form.ControlLabel>
                      <Form.Control
                        value={
                          selectedHeaderData.is_active === 1
                            ? "Active"
                            : "Inactive"
                        }
                        readOnly
                      />
                    </Form.Group>
                  </Form>

                  <Form fluid>
                    <Form.Group>
                      <Form.ControlLabel>Created By</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.created_by || "-"}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Updated By</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.updated_by}
                        readOnly
                      />
                    </Form.Group>
                    <Form.Group>
                      <Form.ControlLabel>Deleted By</Form.ControlLabel>
                      <Form.Control
                        value={selectedHeaderData.deleted_by || "-"}
                        readOnly
                      />
                    </Form.Group>
                  </Form>
                </div>
              </div>
              <div>
                <h6 className="mt-4 mb">Detail Drying</h6>
                <Divider />
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Form fluid>
                      <Form.Group>
                        <Form.ControlLabel>ID</Form.ControlLabel>
                        <Form.Control
                          value={
                            selectedHeaderData.dryingData?.id_detail_trans ||
                            "-"
                          }
                          readOnly
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>Drying Date</Form.ControlLabel>
                        <Form.Control
                          value={formatDate(
                            selectedHeaderData.dryingData?.drying_date
                          )}
                          readOnly
                        />
                      </Form.Group>

                      <Form.Group>
                        <Form.ControlLabel>
                          Drying Exhaust Temperature
                        </Form.ControlLabel>
                        <Form.Control
                          value={
                            selectedHeaderData.dryingData
                              ?.drying_exhaust_temp || "-"
                          }
                          readOnly
                        />
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Created Date</Form.ControlLabel>
                        <Form.Control
                          value={formatDate(
                            selectedHeaderData.dryingData?.created_date
                          )}
                          readOnly
                        />
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Updated Date</Form.ControlLabel>
                        <Form.Control
                          value={formatDate(
                            selectedHeaderData.dryingData?.updated_date
                          )}
                          readOnly
                        />
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Deleted Date</Form.ControlLabel>
                        <Form.Control
                          value={formatDate(
                            selectedHeaderData.dryingData?.deleted_date
                          )}
                          readOnly
                        />
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Status</Form.ControlLabel>
                        <Form.Control
                          value={
                            selectedHeaderData.dryingData?.is_active === 1
                              ? "Active"
                              : selectedHeaderData.dryingData?.is_active === 0
                              ? "Inactive"
                              : "-"
                          }
                          readOnly
                        />
                      </Form.Group>
                    </Form>
                  </div>
                  <div>
                    <Form fluid>
                      <Form.Group>
                        <Form.ControlLabel>ID Header Trans</Form.ControlLabel>
                        <Form.Control
                          value={
                            selectedHeaderData.dryingData?.id_header_trans ||
                            "-"
                          }
                          readOnly
                        />
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Drying lod</Form.ControlLabel>
                        <Form.Control
                          value={
                            selectedHeaderData.dryingData?.drying_lod || "-"
                          }
                          readOnly
                        />
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>
                          Drying Product Temp
                        </Form.ControlLabel>
                        <Form.Control
                          value={
                            selectedHeaderData.dryingData
                              ?.drying_product_temp || "-"
                          }
                          readOnly
                        />
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Drying Remarks</Form.ControlLabel>
                        <Form.Control
                          value={
                            selectedHeaderData.dryingData?.drying_remarks || "-"
                          }
                          readOnly
                        />
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Created By</Form.ControlLabel>
                        <Form.Control
                          value={
                            selectedHeaderData.dryingData?.created_by || "-"
                          }
                          readOnly
                        />
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Updated By</Form.ControlLabel>
                        <Form.Control
                          value={
                            selectedHeaderData.dryingData?.updated_by || "-"
                          }
                          readOnly
                        />
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Deleted By</Form.ControlLabel>
                        <Form.Control
                          value={
                            selectedHeaderData.dryingData?.deleted_by || "-"
                          }
                          readOnly
                        />
                      </Form.Group>
                    </Form>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center p-4">
              <Loader size="md" content="Loading header data..." />
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            onClick={() => setShowHeaderModal(false)}
            appearance="primary">
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* SPV Modal */}
      <Modal open={showSpvModal} onClose={() => setShowSpvModal(false)}>
        <Modal.Header>
          <Modal.Title>Supervisor Approval Information</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedRow && (
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Id Drying</Form.ControlLabel>
                <Form.Control
                  name="id_detail_trans"
                  value={selectedRow.id_detail_trans}
                  readOnly
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Supervisor Employee ID</Form.ControlLabel>
                <Form.Control
                  name="spv_employee_id"
                  value={selectedRow.spv_employee_id || "-"}
                  readOnly
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Supervisor Approval Date</Form.ControlLabel>
                <Form.Control
                  name="spv_approved_dt"
                  value={formatDate(selectedRow.spv_approved_dt)}
                  readOnly
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Approval Status</Form.ControlLabel>
                <div style={{ paddingTop: "7px" }}>
                  <Tag
                    color={getStatusLabel(selectedRow.status_approval).color}>
                    {getStatusLabel(selectedRow.status_approval).label}
                  </Tag>
                </div>
              </Form.Group>
            </Form>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowSpvModal(false)} appearance="primary">
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Manager Modal */}
      <Modal open={showMgrModal} onClose={() => setShowMgrModal(false)}>
        <Modal.Header>
          <Modal.Title>Manager Approval Information</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedRow && (
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Id Drying</Form.ControlLabel>
                <Form.Control
                  name="id_detail_trans"
                  value={selectedRow.id_detail_trans}
                  readOnly
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Manager Employee ID</Form.ControlLabel>
                <Form.Control
                  name="mgr_employee_id"
                  value={selectedRow.mgr_employee_id || "-"}
                  readOnly
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Manager Approval Date</Form.ControlLabel>
                <Form.Control
                  name="mgr_approved_dt"
                  value={formatDate(selectedRow.mgr_approved_dt)}
                  readOnly
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Approval Status</Form.ControlLabel>
                <div style={{ paddingTop: "7px" }}>
                  <Tag
                    color={getStatusLabel(selectedRow.status_approval).color}>
                    {getStatusLabel(selectedRow.status_approval).label}
                  </Tag>
                </div>
              </Form.Group>
            </Form>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowMgrModal(false)} appearance="primary">
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Status Update Confirmation Modal */}
      <Modal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        backdrop="static">
        <Modal.Header>
          <Modal.Title>
            {selectedDryingStatus === 1
              ? "Konfirmasi Hapus"
              : "Konfirmasi Aktifkan"}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedDryingStatus === 1 ? (
            <p>
              Apakah Anda yakin ingin menghapus drying dengan ID:{" "}
              {selectedDryingId}?
            </p>
          ) : (
            <p>
              Apakah Anda yakin ingin mengaktifkan kembali drying dengan ID:{" "}
              {selectedDryingId}?
            </p>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowDeleteModal(false)} appearance="subtle">
            Batal
          </Button>
          <Button
            onClick={handleUpdateStatus}
            appearance="primary"
            color={selectedDryingStatus === 1 ? "red" : "green"}
            disabled={deleteLoading}>
            {deleteLoading ? (
              <Loader size="xs" content="Processing..." />
            ) : selectedDryingStatus === 1 ? (
              "Ya, Hapus"
            ) : (
              "Ya, Aktifkan"
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}
