import MainContent from "@/components/layout/MainContent";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import ModuleApi from "../api/moduleApi";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";

function AddModule() {
  const MySwal = withReactContent(Swal);
  const { PostModuleApi } = ModuleApi();
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  },[]);

  const [moduleName, setModuleName] = useState("");
  const [moduleIcon, setModuleIcon] = useState("");

  // Handler
  function moduleNameHandler(event) {
    setModuleName(event.target.value);
  }
  function moduleIconHandler(event) {
    setModuleIcon(event.target.value);
  }

  const addModuleHandler = async (event) => {
    setIsFormDisabled(true);
    event.preventDefault();

    let dataModule = {
      module_name: moduleName,
      module_icon: moduleIcon,
    };

    // Send ke backend
    const { Data } = await PostModuleApi(dataModule);

    if (Data) {
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "Module inserted successfully.",
        allowOutsideClick: false,
        timer: 2500,
      });
      router.push("/module_management");
    } else {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Module insert FAILED.",
      });
      setIsFormDisabled(false);
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Add Module</title>
        </Head>
      </div>
      <ContainerLayout
        title="Module Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <h4>Form Edit Module</h4>
          <div className="p-5">
            <form onSubmit={addModuleHandler}>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Module Name
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  id="employeeId"
                  value={moduleName}
                  autoComplete="off"
                  onChange={moduleNameHandler}
                  disabled={isFormDisabled}
                  aria-describedby="validationServer03Feedback"
                  required
                />
              </div>
              <div className="mb-3">
                <label htmlFor="employee_name" className="form-label">
                  Module Icon
                </label>
                <input
                  type="text"
                  className="form-control mb-5"
                  id="employee_name"
                  aria-describedby="emailHelp"
                  value={moduleIcon}
                  disabled={isFormDisabled}
                  onChange={moduleIconHandler}
                  placeholder="https://"
                  autoComplete="off"
                />
              </div>

              <button type="submit" className="btn btn-primary p-2">
                Add Module
              </button>
              <button
                type="button"
                disabled={isFormDisabled}
                className="btn btn-dark mx-2 p-2"
                onClick={() => router.back()}
              >
                Cancel
              </button>
            </form>
          </div>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export default AddModule;
