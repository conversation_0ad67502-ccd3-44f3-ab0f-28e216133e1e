import {useRouter} from 'next/router';
import {useEffect, useState} from 'react';
import MainContent from '@/components/layout/MainContent';
import ipcApi from '@/pages/api/ipcApi';
import {<PERSON><PERSON>, Stack, Loader} from 'rsuite';
import Head from 'next/head';
import Table from '@/components/Table';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';

export default function InstrumentData({
  allInstrument,
  parentMenuName,
  childMenuName,
}) {
  const [allInstrumentData, setAllInstrumentData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [moduleName, setModuleName] = useState('');
  const router = useRouter();
  let path = 'masterdata/Instrument';
  let {data} = router.query;
  data ? (data = JSON.parse(data)) : data;
  var [breadcrumbsData, setBreadcrumbsData] = useState([]);
  // const breadcrumbsData = [moduleName, parentMenuName, childMenuName];

  useEffect(() => {
    console.log('LocalStorage', localStorage);
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    const moduleNameValue = localStorage.getItem('module_name');
    if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
      router.push('/');
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push('/dashboard');
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path)
    );
    if (validateUserAccess.length === 0) {
      router.push('/dashboard');
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ''
    ) {
      router.push('/dashboard');
      return;
    }
    const asPathWithoutQuery = router.asPath.split('?')[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split('/')
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      `Setup ${asPathNestedRoutes[1]}`,
      asPathNestedRoutes[2],
    ];
    setBreadcrumbsData(routerBreadCrumbsData);

    setModuleName(moduleNameValue);
  }, []);

  useEffect(() => {
    if (allInstrument.length > 0) {
      setAllInstrumentData(allInstrument);
    }
  }, [allInstrument]);

  return (
    <>
      <div>
        <Head>
          <title>Master Data Instrument</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <MainContent>
          {/* <div className="mb-3">
            <div className="flex justify-end mb-4">
              <p className="text-lg">
                Module : <strong>Production</strong>
              </p>
            </div>
            <p>Setup Master Data &gt; Master Instrument</p>
          </div> */}
          <ModuleContentHeader
            module_name={moduleName}
            breadcrumbs={breadcrumbsData}
          />

          <Stack spacing={6} direction="column" alignItems="flex-start">
            <Button
              appearance="primary"
              onClick={() =>
                router.push('/user_module/masterdata/Instrument/AddInstrument')
              }
            >
              New Instrument
            </Button>

            {/* {allInstrumentData.length > 0 ? (
              <Table allInstrumentData={allInstrumentData} />
            ) : (
              <h4>No Instrument Data FOUND.</h4>
            )} */}
            {allInstrumentData.length === 0 && isLoading === true && <Loader />}
            {allInstrumentData.length === 0 && isLoading === false && (
              <h4>No Instrument Data FOUND.</h4>
            )}
            {allInstrumentData.length > 0 && (
              <Table allInstrumentData={allInstrumentData} />
            )}
          </Stack>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({query}) {
  const {GetAllInstrument} = ipcApi();
  const {Data: instruments} = await GetAllInstrument();

  // const { parentMenuName, childMenuName } = query;
  let allInstrument = [];
  if (instruments != undefined) {
    allInstrument = instruments;
  }

  return {
    props: {
      allInstrument,
      // parentMenuName,
      // childMenuName,
    },
  };
}
