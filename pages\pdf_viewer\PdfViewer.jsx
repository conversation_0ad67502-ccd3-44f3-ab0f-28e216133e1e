export default function Pdf_Viewer({ dataFromQuery }) {
  return (
    <>
      <iframe
        src={dataFromQuery}
        frameborder="0"
        style={{
          overflow: "hidden",
          overflowX: "hidden",
          overflowY: "hidden",
          height: "100%",
          width: "100%",
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}
        height="100%"
        width="100%"
      ></iframe>
    </>
  );
}

export async function getServerSideProps(context) {
  const { query } = context;
  const dataFromQuery = query.pdfFile;

  return {
    props: {
      dataFromQuery,
    },
  };
}
