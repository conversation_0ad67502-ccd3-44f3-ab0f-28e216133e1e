import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_REAGEN}/ts/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiTsdpTH() {
  return {
    getAllTsTransactionHeader: createApiFunction("get", "transaction-h/list"),
    getAllActiveTsTransactionHeader: createApiFunction("get", "transaction-h/list-active"),
    getTsTransHeaderById: createApiFunction("post", "transaction-h/id"),
    postTsTransactionHeader: createApiFunction("post", "transaction-h/create"),
    putTsTransactionHeader: createApiFunction("put", "transaction-h/edit"),
    putStatusTsTransactionHeader: createApiFunction("put", "transaction-h/edit-status"),
    getAllNeedApproveHeader: createApiFunction("post", "transaction-h/list-approve"),
    getAllMasterUser: createApiFunction("get", "transaction-h/list-user"),
    getAllActiveTransactionWithDetail: createApiFunction("post", "transaction-h/list-with-details"),
    getAllNeedApproveHeaderManager: createApiFunction("get", "transaction-h/list-approve-manager"),
    getAllActiveTransactionWithDetailManager: createApiFunction("post", "transaction-h/list-with-details-manager"),
  };
}
