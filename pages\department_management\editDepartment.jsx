import MainContent from "@/components/layout/MainContent";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import { Form, Toggle } from "rsuite";
import departmentApi from "../api/departmentApi";

function EditDepartment({ departmentData, idDepartment }) {
  const MySwal = withReactContent(Swal);
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const router = useRouter();
  const [departmentName, setDepartmentName] = useState("");
  const [isActive, setIsActive] = useState("");
  const { PutDepartment } = departmentApi();

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }

    if (departmentData.length > 0) {
      setDepartmentName(departmentData[0].Department_Name);
      const isActiveValue = departmentData[0].Is_Active === 1 ? true : false;
      setIsActive(isActiveValue);
    }
  }, [departmentData]);

  // Handler
  function departmentNameHandler(event) {
    setDepartmentName(event.target.value);
  }

  const editDepartmentHandler = async (event) => {
    setIsFormDisabled(true);
    event.preventDefault();

    let dataDepartment = {
      department_name: departmentName,
      is_active: isActive ? 1 : 0,
      id_setup: parseInt(idDepartment),
    };

    // // Send ke backend
    const { data: putResult } = await PutDepartment(dataDepartment);
    if (putResult) {
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "Data saved !",
        allowOutsideClick: false,
        timer: 2500,
      });
      router.push("/department_management");
    } else {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Data insert FAILED.",
      });
      setIsFormDisabled(false);
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Edit Department</title>
        </Head>
      </div>
      <ContainerLayout
        title="Department Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <h4>Form Edit Department</h4>
          <div className="p-5">
            <form onSubmit={editDepartmentHandler}>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Department Name
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  id="employeeId"
                  autoComplete="off"
                  onChange={departmentNameHandler}
                  disabled={isFormDisabled}
                  value={departmentName}
                  aria-describedby="validationServer03Feedback"
                  required
                />
              </div>
              <div className="mb-5">
                <Form.Group>
                  <Form.ControlLabel>Active</Form.ControlLabel>
                  <Toggle
                    checked={isActive}
                    onChange={() => setIsActive((value) => !value)}
                  />
                </Form.Group>
              </div>

              <button type="submit" className="btn btn-primary p-2">
                Save
              </button>
              <button
                type="button"
                disabled={isFormDisabled}
                className="btn btn-dark mx-2 p-2"
                onClick={() => router.back()}
              >
                Cancel
              </button>
            </form>
          </div>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export default EditDepartment;

export async function getServerSideProps({ query }) {
  const { idDepartment } = query;
  const { GetDepartmentById } = departmentApi();

  const idDeptData = {
    id_setup: parseInt(idDepartment),
  };

  const { data: department } = await GetDepartmentById(idDeptData);

  let departmentData = [];
  if (department !== null && department !== undefined) {
    departmentData = department;
  }

  return {
    props: {
      departmentData,
      idDepartment,
    },
  };
}
