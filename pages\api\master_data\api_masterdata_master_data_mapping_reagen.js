import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/mapping_reagen/master_mapping_reagen/${url}`,
        data
    )
        .then((res) => {return res.data})
        .catch((err) => {return err.response.data});
    return response;
};

export default function MasterDataMasterDataReagenApi(){
    return{
        add: createApiFunction("post", "create"),
        getAll: createApiFunction("get", "get/all"),
        getAllActive: createApiFunction("get", "get/all/active"),
        getById: createApiFunction("get", "get/id"),
        editStatus: createApiFunction("put", "edit/status"),
        edit: createApiFunction("put", "edit"),
        getAllActive: createApiFunction("get", "get/all/active"),
        getAllActiveInbound: createApiFunction("get", "get/all/active/inbound"),
    }
}