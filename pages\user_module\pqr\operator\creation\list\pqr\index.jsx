import { useEffect, useState, useCallback } from "react";
import Head from "next/head";
import { Breadcrumb, Stack, Panel, Tag, Form, Grid, Row, Col, Table, Button, Pagination, Modal, Loader, IconButton, InputGroup, Input, InputNumber, Checkbox, SelectPicker, useToaster, Notification } from "rsuite";
import SearchIcon from "@rsuite/icons/Search";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import ContainerLayout from "@/components/layout/ContainerLayout";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiTransactionDetail from "@/pages/api/pqr/transaction_d/api_transaction_d";
import ApiBindingParamPpi from "@/pages/api/pqr/binding_param_ppi/api_binding_param_ppi";
import Api_param_ppi from "@/pages/api/pqr/param_ppi/api_param_ppi";

export default function Index() {
  const { HeaderCell, Cell, Column } = Table;
  const toaster = useToaster();
  const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [sortColumn, setSortColumn] = useState("id_trans_detail"); // Default sort column
  const [sortType, setSortType] = useState("desc"); // Default sort type (descending)

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [idRouter, setIdRouter] = useState(null);
  const router = useRouter();
  const { IdHeader } = router.query;

  const emptyAddTransactionDetailForm = {
    id_trans_header: "",
    id_binding: "",
    actual_value: 0,
    result: "",
    create_by: sessionAuth ? sessionAuth.employee_name : "",
  };

  const [addTransactionDetailForm, setAddTransactionDetailForm] = useState(emptyAddTransactionDetailForm);
  const [transactionDetailsDataState, setTransactionDetailDataState] = useState([]);
  const [bindingParamPpiDataState, setBindingParamPpiDataState] = useState([]);
  const [machinePPIdataState, setMachinePPIdataState] = useState([]);

  const [formDataHeader, setformDataHeader] = useState({});
  const [showAddModal, setShowAddModal] = useState(false);
  const [errorsAddForm, setErrorsAddForm] = useState({});

  const [isGetDataClicked, setIsGetDataClicked] = useState(false);
  const [timerActive, setTimerActive] = useState(false);
  const [timer, setTimer] = useState(50);
  const [waitingData, setWaitingData] = useState(false);

  const filteredData = transactionDetailsDataState.filter((rowData, i) => {
    const searchFields = ["id_trans_detail", "id_trans_header", "parameter_name", "min_value", "max_value", "actual_value", "result", "create_date", "create_by"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const HandleAddTransactionDetailApi = async () => {
    const errors = {};

    // Validasi khusus untuk parameter dengan flag_controllable "N" dan is_automate "N"
    const missingRequiredParameters = addTransactionDetailForm.parameters.filter((param) => {
      return param.flag_controllable === "N" && param.is_automate === "N" && (param.actual_value === null || param.actual_value === "" || (param.binding_type !== 3 && isNaN(param.actual_value)));
    });

    if (missingRequiredParameters.length > 0) {
      errors.parameters = "Isi semua parameter yang controllable dan automatable 'N'.";
    }

    // Mengecek apakah parameter dengan flag_controllable "N" sudah ada dalam tabel
    const controllableNExists = transactionDetailsDataState.some((param) => param.flag_controllable === "N");

    // Filter hanya parameter dengan flag_controllable "Y" jika parameter dengan flag_controllable "N" sudah ada
    const parametersToProcess = controllableNExists ? addTransactionDetailForm.parameters.filter((param) => param.flag_controllable === "Y") : addTransactionDetailForm.parameters;

    // Filter hanya parameter yang diisi dengan nilai valid
    const filledParameters = parametersToProcess.filter((param) => {
      if (param.binding_type === 3) {
        // Jika binding_type adalah 3 (Description), validasi harus berupa teks yang tidak kosong
        return param.actual_value !== null && param.actual_value.trim() !== "";
      } else {
        // Jika binding_type bukan 3, pastikan actual_value adalah angka valid
        return param.actual_value !== null && param.actual_value !== "" && !isNaN(param.actual_value);
      }
    });

    // Jika ada error, hentikan proses dan tampilkan pesan error
    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }

    setLoading(true);

    try {
      // Iterasi setiap parameter yang sudah difilter dan kirim sebagai transaksi individual
      for (const param of filledParameters) {
        // Ubah actual_value menjadi string jika binding_type adalah 1 atau 2
        const actualValueString = param.binding_type === 1 || param.binding_type === 2 ? param.actual_value.toString() : param.actual_value;

        const res = await ApiTransactionDetail().createTransactionDetail({
          id_trans_header: parseInt(IdHeader, 10),
          id_binding: param.id_binding,
          parameter_name: param.parameter_name,
          binding_type: param.binding_type,
          min_value: param.min_value,
          max_value: param.max_value,
          absolute_value: param.absolute_value,
          description_value: param.description_value,
          actual_value: actualValueString, // Ubah ke string jika binding_type 1 atau 2
          result: param.result,
          create_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
        });

        if (res.status !== 200) {
          console.log("gagal menambah data transaksi header", res.message);
        showNotification("error", "gagal menambah data transaksi header");
        }
      }

      // Reset form setelah berhasil
      setShowAddModal(false);
      await HandleGetDetailTransactionDetail(IdHeader);
      setAddTransactionDetailForm((prev) => ({
        ...prev,
        parameters: prev.parameters.map((param) => ({
          ...param,
          actual_value: null,
          result: null,
        })),
      }));
    } catch (error) {
      console.log("error gagal menambah data transaksi header", error);
      showNotification("error", "gagal menambah data transaksi header");    } finally {
      setLoading(false);
    }
  };

  const HandleGetAllBindingParamPpiApi = async (id_ppi, wetmill) => {
    try {
      let res;
      if (wetmill === "N") {
        res = await ApiBindingParamPpi().getIdPpiActiveBindingParamPpiWetmillN({ id_ppi: parseInt(id_ppi) });
      } else if (wetmill === "Y") {
        res = await ApiBindingParamPpi().getIdPpiActiveBindingParamPpiWetmillY({ id_ppi: parseInt(id_ppi) });
      } else {
        console.log("Invalid wetmill value");
        return;
      }

      if (res.status === 200) {
        setBindingParamPpiDataState(res.data);
      } else {
        console.log("gagal dapat data binding ", res.message);
      }
    } catch (error) {
      console.log("gagal dapat data binding", error);
    }
  };

  const HandleGetDetailTransactionHeader = async (id_trans_header) => {
    try {
      const apiTransactionHeader = ApiTransactionHeader();
      const response = await apiTransactionHeader.getPPITransactionHeaderById({ id_trans_header: parseInt(id_trans_header) });
      if (response.status === 200) {
        const data = response.data;
        setformDataHeader({
          id_trans_header: data.id_trans_header,
          id_ppi: data.id_ppi,
          ppi_name: data.ppi_name,
          batch_code: data.batch_code,
          iot_desc: data.iot_desc,
          line_desc: data.line_desc,
          remarks: data.remarks,
          wetmill: data.wetmill,
          status_transaction: data.status_transaction,
          create_date: data.create_date ? new Date(data.create_date).toLocaleDateString("en-GB") : "-",
          create_by: data.create_by || "-",
          update_date: data.update_date ? new Date(data.update_date).toLocaleDateString("en-GB") : "-",
          update_by: data.update_by || "-",
          delete_date: data.delete_date ? new Date(data.delete_date).toLocaleDateString("en-GB") : "-",
          delete_by: data.delete_by || "-",
        });

        // Panggil API binding parameter yang sesuai berdasarkan nilai wetmill
        if (data.id_ppi) {
          await HandleGetAllBindingParamPpiApi(data.id_ppi, data.wetmill);
        }

        return data; // Mengembalikan seluruh data header, termasuk id_ppi
      } else {
        console.log("gagal mendapatkan data detail", res.message);
        return null;
      }
    } catch (error) {
      console.log("gagal mendapatkan data detail", error);
      return null;
    }
  };

  const HandleGetDetailTransactionDetail = async (id_trans_header) => {
    try {
      const res = await ApiTransactionDetail().getTransactionParamBindingDetailById({ id_trans_header: parseInt(id_trans_header) });
      if (res.status === 200) {
        setTransactionDetailDataState(res.data);
      } else {
        console.log("gagal mendapatkan data detail ", res.message);
      }
    } catch (error) {
      console.log("gagal mendapatkan data detail", error);
    }
  };

  const handleGetDataClick = () => {
    // setTimer(50); // Reset timer to 50 seconds
    // setTimerActive(true); // Activate timer
    // setIsGetDataClicked(true); // Set Get Data clicked to true
    setWaitingData(true);
    HandleGetAllMachinePPIData()
  };


  const HandleGetAllMachinePPIData = async (id_ppi, wetmill) => {
    try {
      let res = null;
      if (formDataHeader.wetmill === "N") {
        console.log("object", formDataHeader)
        res = await Api_param_ppi().getParamWetmillN({ id_ppi: parseInt(formDataHeader.id_ppi) });
      } else if (formDataHeader.wetmill === "Y") {
        res = await Api_param_ppi().getParamWetmillY({ id_ppi: parseInt(formDataHeader.id_ppi) });
      } else {
        console.log("Invalid wetmill value");
        return;
      }

      if (res?.status === 200) {
        setMachinePPIdataState(res?.data?.data_automate);

        const newParameters = res?.data?.data_automate.map((item) => {

          let statusComply = "MS";

          // Tentukan hasil berdasarkan tipe binding
          if (item.binding_type === 1) {
            statusComply =
              item.actual_value >= item.min_value && item.actual_value <= item.max_value
                ? "MS"
                : "TMS";
          } else if (item.binding_type === 2) {
            statusComply =
              parseFloat(item.actual_value) === parseFloat(item.actual_value)
                ? "MS"
                : "TMS";
          }


          return ({
            id_binding: item.id_binding,
            parameter_name: item.parameter_name,
            min_value: item.min_value,
            max_value: item.max_value,
            absolute_value: item.absolute_value,
            description_value: item.description_value,
            binding_type: item.binding_type, // Menentukan jenis binding
            actual_value: item.actual_value,
            flag_controllable: 'Y',
            is_automate: 'Y',
            result: statusComply
          })
        });

        setAddTransactionDetailForm((prev) => ({
          ...prev,
          parameters: [
            ...prev.parameters, // Retain existing parameters
            ...newParameters,   // Add new parameters
          ],
        }));



        setWaitingData(false);
        setTimer(50); // Reset timer to 50 seconds
        setTimerActive(true); // Activate timer
        setIsGetDataClicked(true); // Set Get Data clicked to true
      } else {
        console.log("gagal mendapatkan data mesin ", res.message);
        showNotification("Error", "Gagal Mengambil data Mesin");
        if (addTransactionDetailForm.parameters.length > 0) {
          setIsGetDataClicked(true); // Set Get Data clicked to true
        }
      }
    } catch (error) {
      console.log("Gagal Mengambil data Mesin", error);
      showNotification("Error", "Gagal Mengambil data Mesin");
      if (addTransactionDetailForm.parameters.length > 0) {
        setIsGetDataClicked(true); // Set Get Data clicked to true
      }
    }


  };

  const resetTimer = useCallback(() => {
    setTimer(50);
    setTimerActive(false);
    setIsGetDataClicked(false);
  }, []);

  const totalRowCount = searchKeyword ? filteredData.length : transactionDetailsDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else if (IdHeader) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/operator")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      console.log("Router data:", IdHeader);
      setIdRouter(IdHeader);

      // Mendapatkan detail dari header dan mengambil id_ppi dari header tersebut
      HandleGetDetailTransactionHeader(IdHeader).then((headerData) => {
        if (headerData && headerData.id_ppi) {
          // Panggil API binding parameter yang tepat berdasarkan nilai wetmill
          HandleGetAllBindingParamPpiApi(headerData.id_ppi, headerData.wetmill);
        }
      });

      HandleGetDetailTransactionDetail(IdHeader);
    }
  }, [router]);

  useEffect(() => {
    if (bindingParamPpiDataState.length > 0) {
      let filteredParameters = [];

      // Mengecek apakah sudah ada parameter dengan flag_controllable = "N" pada tabel trans_d
      const controllableNExistsInTransD = transactionDetailsDataState.some((param) => param.flag_controllable === "N");

      // Mengecek apakah ada parameter dengan flag_controllable = "N" di wetmill
      const controllableNExistsInBindingParamPpi = bindingParamPpiDataState.some((item) => item.flag_controllable === "N");

      if (controllableNExistsInTransD) {
        // Jika sudah ada flag_controllable "N" di trans_d, tampilkan parameter dengan flag_controllable "Y"
        filteredParameters = bindingParamPpiDataState.filter((item) => item.flag_controllable === "Y");
      } else if (controllableNExistsInBindingParamPpi) {
        // Jika ada parameter dengan flag_controllable "N" di wetmill dan belum ada di trans_d, tampilkan flag_controllable "N"
        filteredParameters = bindingParamPpiDataState.filter((item) => item.flag_controllable === "N");
      } else {
        // Jika tidak ada flag_controllable "N" di wetmill, tampilkan parameter dengan flag_controllable "Y"
        filteredParameters = bindingParamPpiDataState.filter((item) => item.flag_controllable === "Y");
      }

      // Mengurutkan parameter dengan is_automate "N" ke bagian paling atas
      const sortedParameters = filteredParameters.sort((a, b) => {
        if (a.is_automate === "N" && b.is_automate === "Y") {
          return -1; // "N" ditempatkan lebih atas dari "Y"
        } else if (a.is_automate === "Y" && b.is_automate === "N") {
          return 1; // "Y" ditempatkan lebih bawah dari "N"
        }
        return 0; // Tidak ada perubahan jika keduanya memiliki nilai sama
      });

      //hanya tampilkan yang N saja
      const fixedParameter = sortedParameters.filter(
        (item) => item.is_automate === "N"
      );

      // Mapping hasil sortedParameters untuk setAddTransactionDetailForm
      const parameters = fixedParameter.map((item) => ({
        id_binding: item.id_binding,
        parameter_name: item.parameter_name,
        min_value: item.min_value,
        max_value: item.max_value,
        absolute_value: item.absolute_value,
        description_value: item.description_value,
        binding_type: item.binding_type, // Menentukan jenis binding
        actual_value: null,
        flag_controllable: item.flag_controllable,
        is_automate: item.is_automate,
      }));

      setAddTransactionDetailForm((prev) => ({
        ...prev,
        parameters,
      }));
    }
  }, [bindingParamPpiDataState, transactionDetailsDataState]);

  useEffect(() => {
    let timerInterval;

    if (timerActive) {
      timerInterval = setInterval(() => {
        setTimer((prevTimer) => {
          if (prevTimer <= 1) {
            clearInterval(timerInterval);
            setTimerActive(false); // Deactivate timer when it reaches 0
            return 0;
          }
          return prevTimer - 1;
        });
      }, 1000);
    }

    return () => clearInterval(timerInterval); // Cleanup interval on unmount
  }, [timerActive]);

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  return (
    <div>
      <div>
        <Head>
          <title>Transaction Header Detail</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>List</Breadcrumb.Item>
                  <Breadcrumb.Item active>Transaction Header</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <Form layout="vertical">
                  <Grid fluid>
                    <Row style={{ marginBottom: "16px" }}>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>ID Transaksi Header</Form.ControlLabel>
                          <Form.Control name="id_trans_header" value={formDataHeader.id_trans_header} style={{ width: "100%" }} />
                        </Form.Group>
                      </Col>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                          <Form.Control name="ppi_name" value={formDataHeader.ppi_name} style={{ width: "100%" }} />
                        </Form.Group>
                      </Col>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Batch Code</Form.ControlLabel>
                          <Form.Control name="batch_code" value={formDataHeader.batch_code} style={{ width: "100%" }} />
                        </Form.Group>
                      </Col>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Wetmill</Form.ControlLabel>
                          <Form.Control name="wetmill" value={formDataHeader.wetmill === "Y" ? "Yes" : "No"} style={{ width: "100%" }} />
                        </Form.Group>
                      </Col>
                      <Row style={{ marginBottom: "16px" }}></Row>
                      <Row style={{ marginBottom: "16px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Remarks</Form.ControlLabel>
                            <Form.Control name="remarks" value={formDataHeader.remarks} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Status Transaksi</Form.ControlLabel>
                            <Form.Control name="status_transaction" value={formDataHeader.status_transaction === 2 ? "Draft" : formDataHeader.status_transaction === 1 ? "Done" : "Dropped"} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                      </Row>
                      <Row style={{ marginBottom: "24px" }}></Row>
                      <Row style={{ marginBottom: "24px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                            <Form.Control name="create_date" value={formDataHeader.create_date} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                            <Form.Control name="created_by" value={formDataHeader.create_by} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                            <Form.Control name="update_date" value={formDataHeader.update_date} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                            <Form.Control name="update_by" value={formDataHeader.update_by} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                      </Row>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                          <Form.Control name="delete_date" value={formDataHeader.delete_date} style={{ width: "100%" }} />
                        </Form.Group>
                      </Col>
                      <Row style={{ marginBottom: "16px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                            <Form.Control name="delete_by" value={formDataHeader.delete_by} style={{ width: "100%" }} />
                          </Form.Group>
                        </Col>
                      </Row>
                    </Row>
                  </Grid>
                </Form>
              </Stack>
            }
          />
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusRoundIcon />}
                    appearance="primary"
                    onClick={() => {
                      if (bindingParamPpiDataState.length > 0) {
                        const { id_binding, parameter_name, min_value, max_value } = bindingParamPpiDataState[0];
                        setAddTransactionDetailForm((prevForm) => ({
                          ...prevForm,
                          id_binding,
                          parameter_name,
                          min_value,
                          max_value,
                          actual_value: 0, // atau nilai default lain untuk actual_value
                        }));
                      }
                      setShowAddModal(true);
                    }}
                  >
                    Tambah
                  </IconButton>
                </div>
                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input placeholder="cari" value={searchKeyword} onChange={handleSearch} />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={(column, type) => {
                setSortColumn(column);
                setSortType(type);
              }}
              autoHeight
            >
              {/* <Column width={180} align="center" sortable fullText>
                <HeaderCell>ID Transaksi Detail</HeaderCell>
                <Cell dataKey="id_trans_detail" />
              </Column> */}
              <Column width={175} sortable align="center" fullText>
                <HeaderCell>Tanggal Dibuat</HeaderCell>
                <Cell>
                  {(rowData) => {
                    if (!rowData.create_date) return "-";
                    const date = new Date(rowData.create_date);
                    const day = String(date.getDate()).padStart(2, "0");
                    const month = String(date.getMonth() + 1).padStart(2, "0");
                    const year = date.getFullYear();
                    const hours = String(date.getHours()).padStart(2, "0");
                    const minutes = String(date.getMinutes()).padStart(2, "0");

                    return `${day}-${month}-${year} ${hours}:${minutes}`;
                  }}
                </Cell>

              </Column>
              <Column width={250} align="center" sortable fullText resizable>
                <HeaderCell>Nama Parameter</HeaderCell>
                <Cell dataKey="parameter_name" />
              </Column>
              <Column width={250} align="center" sortable fullText>
                <HeaderCell>Standard Parameter</HeaderCell>
                <Cell>
                  {(rowData) => {
                    let valueToDisplay;
                    if (rowData.binding_type === 1) {
                      valueToDisplay = rowData.min_value !== undefined && rowData.max_value !== undefined ? `${Number(rowData.min_value).toLocaleString("id-ID")} - ${Number(rowData.max_value).toLocaleString("id-ID")}` : "-";
                    } else if (rowData.binding_type === 2) {
                      valueToDisplay = rowData.absolute_value !== undefined && rowData.absolute_value !== null ? Number(rowData.absolute_value).toLocaleString("id-ID") : "-";
                    } else if (rowData.binding_type === 3) {
                      valueToDisplay = rowData.description_value || "-";
                    }
                    return <span>{valueToDisplay}</span>;
                  }}
                </Cell>
              </Column>
              <Column width={180} align="center" sortable fullText>
                <HeaderCell>Nilai Aktual</HeaderCell>
                <Cell dataKey="actual_value" />
              </Column>
              <Column width={180} align="center" sortable fullText>
                <HeaderCell>Hasil</HeaderCell>
                <Cell dataKey="result" />
              </Column>

              <Column width={250} sortable fullText>
                <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
            <Modal
              backdrop="static"
              size="md"
              open={showAddModal}
              onClose={() => {
                setShowAddModal(false);
                resetTimer(); // Reset timer ketika modal ditutup
                setErrorsAddForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Menambahkan Detail</Modal.Title>
                <div style={{ marginTop: "16px" }}>
                  {/* {addTransactionDetailForm.parameters && addTransactionDetailForm.parameters.some((param) => param.flag_controllable === "Y") && ( */}
                  <Button appearance="primary"
                    onClick={() => {
                      handleGetDataClick()
                    }}
                    disabled={timerActive}>
                    Mengambil Data Mesin
                  </Button>
                  {/* )} */}
                  {timerActive && <span style={{ marginLeft: "16px" }}>Timer: {timer}s</span>}
                  {!timerActive && waitingData && <span style={{ marginLeft: "16px" }}>Memproses data dari mesin</span>}
                </div>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Grid fluid>
                    {addTransactionDetailForm.parameters &&
                      addTransactionDetailForm.parameters.map((param, index) => (
                        <Row key={index} style={{ marginBottom: "16px" }}>
                          {/* Parameter Name */}
                          <Col xs={6} sm={6}>
                            <Form.Group controlId={`parameter_name_${index}`}>
                              {index === 0 && <Form.ControlLabel>Parameter</Form.ControlLabel>}
                              <Input value={param.parameter_name} readOnly placeholder="Nama Parameter" />
                            </Form.Group>
                          </Col>

                          {/* Kriteria Standard */}
                          <Col xs={10} sm={6}>
                            <Form.Group controlId={`criteriaStandard_${index}`}>
                              {index === 0 && <Form.ControlLabel>Kriteria Standard</Form.ControlLabel>}
                              <InputGroup>
                                {param.binding_type === 1 && (
                                  <>
                                    <Input value={param.min_value} readOnly style={{ width: 30 }} />
                                    <InputGroup.Addon style={{ width: 20 }}>-</InputGroup.Addon>
                                    <Input value={param.max_value} readOnly style={{ width: 30 }} />
                                  </>
                                )}
                                {param.binding_type === 2 && <Input value={param.absolute_value} readOnly style={{ width: 40 }} />}
                                {param.binding_type === 3 && <Input value={param.description_value} readOnly style={{ width: 40 }} />}
                              </InputGroup>
                            </Form.Group>
                          </Col>

                          {/* Actual Value (Hasil) */}
                          <Col xs={6} sm={6}>
                            <Form.Group controlId={`actual_value_${index}`}>
                              {index === 0 && <Form.ControlLabel>Hasil</Form.ControlLabel>}
                              {param.binding_type === 3 ? (
                                // Jika binding_type adalah 3 (Description), gunakan Input biasa untuk teks
                                <Input
                                  value={param.actual_value}
                                  onChange={(value) => {
                                    setAddTransactionDetailForm((prev) => {
                                      const updatedParameters = [...prev.parameters];
                                      updatedParameters[index].actual_value = value || "";
                                      updatedParameters[index].result = value ? "MS" : null; // Tetapkan result sesuai dengan input
                                      return { ...prev, parameters: updatedParameters };
                                    });
                                  }}
                                  disabled={param.is_automate === "Y"} // Hanya nonaktifkan jika is_automate = "Y"
                                />
                              ) : (
                                // Jika binding_type adalah 1 (Range) atau 2 (Absolute), gunakan InputNumber
                                <InputNumber
                                  value={param.actual_value}
                                  onChange={(value) => {
                                    setAddTransactionDetailForm((prev) => {
                                      const updatedParameters = [...prev.parameters];
                                      if (updatedParameters[index]) {
                                        updatedParameters[index].actual_value = value || null;

                                        // Tentukan hasil berdasarkan tipe binding
                                        if (param.binding_type === 1) {
                                          updatedParameters[index].result = value >= param.min_value && value <= param.max_value ? "MS" : "TMS";
                                        } else if (param.binding_type === 2) {
                                          updatedParameters[index].result = parseFloat(value) === parseFloat(param.absolute_value) ? "MS" : "TMS";
                                        }
                                      }
                                      return { ...prev, parameters: updatedParameters };
                                    });
                                  }}
                                  style={{ backgroundColor: "#FFFDD0", width: "100%" }}
                                  disabled={param.is_automate === "Y"} // Hanya nonaktifkan jika is_automate = "Y"
                                />
                              )}
                            </Form.Group>
                          </Col>

                          {/* Comply */}
                          <Col xs={2} sm={2} style={{ display: "flex", alignItems: "center" }}>
                            <Form.Group controlId={`comply_${index}`}>
                              {index === 0 && param.binding_type !== 3 && <Form.ControlLabel>Comply</Form.ControlLabel>}
                              {param.binding_type !== 3 && <Checkbox checked={param.result === "MS"} disabled />}
                            </Form.Group>
                          </Col>

                          {/* Error Handling */}
                          <Col xs={12}>{errorsAddForm.parameters && param.flag_controllable === "N" && <p style={{ color: "red" }}>{errorsAddForm.parameters}</p>}</Col>
                        </Row>
                      ))}
                  </Grid>
                </Form>
              </Modal.Body>

              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    resetTimer(); // Reset timer ketika tombol Cancel ditekan
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={async () => {
                    HandleAddTransactionDetailApi();
                    resetTimer(); // Reset timer ketika tombol Add ditekan dan berhasil menambahkan data
                  }}
                  appearance="primary"
                  type="submit"
                  loading={loading}
                  disabled={loading || !isGetDataClicked}
                >
                  Simpan
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Adding Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
          </Panel>
        </div>
      </ContainerLayout>
    </div>
  );
}
