import ContainerLayout from '@/components/layout/ContainerLayout';
import Head from 'next/head';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react'
import { <PERSON>readcrumb, Button, Form, InputGroup, Panel, Stack, Uploader, Input, SelectPicker, DatePicker, Table, Modal, Pagination, toaster, Loader, PanelGroup, Row, Col } from 'rsuite';
import ApiTsDetail from "@/pages/api/ts/api_ts_detail";
import ApiMasterLine from "@/pages/api/ts/api_ts_master_line";
import Messages from '@/components/Messages';
import ApiTsHeader from '@/pages/api/ts/api_ts_header';
import ApiAttachments from '@/pages/api/ts/api_attachments';
import Image from 'next/image';
import TrashIcon from '@rsuite/icons/Trash';

export default function TsEditAutomate({ data }) {

  const router = useRouter();
  const [moduleName, setModuleName] = useState("");
  const [props, setProps] = useState([]);
  const { HeaderCell, Cell, Column } = Table;
  const [addLimit, setAddLimit] = useState(10);
  const [limit, setLimit] = useState(10);
  const [addPage, setAddPage] = useState(1);
  const [page, setPage] = useState(1);
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const [currentState, setCurrentState] = useState("Header");

  // const emptyformDetail = {
  //   id_line: null,
  //   sediaan_type: null,
  //   product_code: null,
  //   product_name: null,
  //   batch_no: null,
  //   production_scale: null,
  //   focus_trial: null,
  //   ppi_no: null,
  //   process_purpose: null,
  //   background: null,
  //   process_date: null,
  //   binder_date: null,
  //   binder_mix_amount: null,
  //   binder_mix_time: null,
  //   binder_remarks: null,
  //   granule_date: null,
  //   granule_ampere: null,
  //   granule_power: null,
  //   granule_remarks: null,
  //   drying_date: null,
  //   drying_lod: null,
  //   drying_product_temp: null,
  //   drying_exhaust_temp: null,
  //   drying_remarks: null,
  //   sifting_date: null,
  //   sifting_screen_quadro: null,
  //   sifting_bin_tumbler: null,
  //   sifting_impeller_speed_1: null,
  //   sifting_impeller_speed_2: null,
  //   sifting_remarks: null,
  //   final_mix_date: null,
  //   final_mix_time_mix_1: null,
  //   final_mix_time_mix_2: null,
  //   ts_conclusion: null,
  //   ts_followup: null,
  //   bobot_granul: null,
  //   bobot_teoritis: null,
  //   rendemen: null,
  //   discussion: null,
  //   analyzed_data: null,
  //   spv_employee_id: null,
  //   ts_detail_trans: [],
  // }
  // const [formDetail, setFormDetail] = useState(emptyformDetail);
  const [formDetail, setFormDetail] = useState({});
  const [formAddDetail, setFormAddDetail] = useState([]);
  const [formAttachments, setFormAttachments] = useState([]);

  const [dataProduct, setDataProduct] = useState([]);
  const [dataSpv, setDataSpv] = useState([]);
  const [dataDetail, setDataDetail] = useState([]);
  const [dataLine, setDataLine] = useState([]);
  const [selectedApiType, setSelectedApiType] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedDetail, setSelectedDetail] = useState(null);
  const [showSelectedDetailModal, setShowSelectedDetailModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("masterdata/TsMainCategories")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      fetchData();
    }
  }, []);

  const fetchData = async () => {
    const resDataDetail = await ApiTsDetail().getDetailAuto({
      id_reference: parseInt(data),
    })
    const resProduct = await ApiTsDetail().getAllOfProduct()
    const resSpv = await ApiTsDetail().getAllActiveSpv()
    const resAllDetail = await ApiTsDetail().getAllDetail()
    const resLine = await ApiMasterLine().getAutomateMasterLine()

    const dataDetail = resDataDetail.data ? resDataDetail.data : {}
    dataDetail.ts_attachments = dataDetail.attachments ? dataDetail.attachments : []
    delete dataDetail.attachments

    setFormDetail(resDataDetail.data ? resDataDetail.data : {})
    setDataProduct(resProduct.data ? resProduct.data : [])
    setDataSpv(resSpv.data ? resSpv.data : [])
    setDataDetail(resAllDetail.data ? resAllDetail.data : [])
    setDataLine(resLine.data ? resLine.data : [])
  }

  const getDetailApiData = async (type) => {
    const res = await ApiTsDetail().getApiData({
      type_api: type
    })

    if (res.status === 200) {
      setFormAddDetail((prevformDetail) => {
        const existingDetail = prevformDetail || [];
        const typeExists = existingDetail.some((detail) => detail.type === currentState);

        return typeExists
          ? existingDetail
          : [
            ...existingDetail,
            {
              id_header_trans: formDetail.id_header_trans,
              type: currentState,
              data: res.data,
            }
          ]
      })
      setShowDetailModal(false)
    }
  }

  const dataJenisSediaan = ['Kapsul', 'Tablet', 'Sirup'].map(
    item => ({ label: item, value: item })
  );

  const dataSkalaProduksi = ['Pilot', 'Commercial'].map(
    item => ({ label: item, value: item })
  );

  const dataFokusTrial = ['Carry Over', 'Diversifikasi', 'Others'].map(
    item => ({ label: item, value: item })
  );

  const dataScreenQuadro = ['10', '20', '30'].map(
    item => ({ label: item, value: item })
  );

  const dataBinTumbler = ['10', '20', '30'].map(
    item => ({ label: item, value: item })
  );

  const handleChangeAddLimit = (dataKey) => {
    setAddPage(1);
    setAddLimit(dataKey);
  }

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const getAddPaginatedData = (currentDataDetail, limit, page) => {
    const start = addLimit * (addPage - 1);
    const end = start + addLimit;
    return currentDataDetail?.slice(start, end);
  }

  const getPaginatedData = (currentDataDetail, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return currentDataDetail?.slice(start, end);
  }

  const getFilteredData = (detail) => {
    if (sortColumn && sortType) {
      return detail.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return detail
  };

  const totalAddRowCount = formAddDetail?.length;
  const totalRowCount = formDetail.detail_trans?.length;

  const handleUploadAttachments = (file, type) => {
    setFormDetail((prevFormDetail) => ({
      ...prevFormDetail,
      ts_attachments: [
        ...prevFormDetail.ts_attachments,
        {
          type,
          data: file
        },
      ],
    }));
  };

  const handleRemoveAttachments = (fileKey) => {
    setFormDetail((prevFormDetail) => {
      const newAttachments = prevFormDetail.ts_attachments.filter((attachment) => attachment?.data?.fileKey !== fileKey);
      return { ...prevFormDetail, ts_attachments: newAttachments };
    });
  };

  const handleRemoveDetail = (index) => {
    setFormAddDetail((prevFormDetail) => [...prevFormDetail.slice(0, index), ...prevFormDetail.slice(index + 1)]);
  };

  const NewDetailTable = () => {
    return (
      <Panel bordered bodyFill className='mt-3'>
        <h6 className='m-3'>Add New Details</h6>
        <Table
          bordered
          cellBordered
          height={250}
          data={getAddPaginatedData(getFilteredData(formAddDetail), addLimit, addPage)}
          sortColumn={sortColumn}
          sortType={sortType}
          onSortColumn={handleSortColumn}
        >
          <Column width={70} align="center">
            <HeaderCell>No</HeaderCell>
            <Cell>
              {(rowData, rowIndex) => (addPage - 1) * addLimit + rowIndex + 1}
            </Cell>
          </Column>
          <Column flexGrow={2} align='center'>
            <HeaderCell>Type</HeaderCell>
            <Cell dataKey="type" />
          </Column>
          <Column flexGrow={1}>
            <HeaderCell align='center'>Action</HeaderCell>
            <Cell style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              {(rowData, rowIndex) => (
                <div className='space-x-2'>
                  <Button
                    appearance="ghost"
                    size="xs"
                    onClick={() => {
                      setSelectedDetail(rowData);
                      setShowSelectedDetailModal(true);
                    }}
                  >
                    View Detail
                  </Button>
                  <Button
                    appearance="ghost"
                    color='red'
                    size="xs"
                    onClick={() => handleRemoveDetail(rowIndex)}
                  >
                    Remove
                  </Button>
                </div>
              )}
            </Cell>
          </Column>
        </Table>
        <div style={{ padding: 20 }}>
          <Pagination
            prev
            next
            first
            last
            ellipsis
            boundaryLinks
            maxButtons={5}
            size="xs"
            layout={["total", "-", "limit", "|", "pager", "skip"]}
            limitOptions={[10, 30, 50]}
            total={totalAddRowCount}
            limit={addLimit}
            activePage={addPage}
            onChangePage={setAddPage}
            onChangeLimit={handleChangeAddLimit}
          />
        </div>
      </Panel>
    )
  }

  const DetailTable = () => {
    return (
      <Panel bordered bodyFill className='mt-3'>
        <h6 className='m-3'>Inserted Details</h6>
        <Table
          bordered
          cellBordered
          height={400}
          data={getPaginatedData(getFilteredData(formDetail.detail_trans), limit, page)}
          sortColumn={sortColumn}
          sortType={sortType}
          onSortColumn={handleSortColumn}
        >
          <Column width={70} align="center">
            <HeaderCell>No</HeaderCell>
            <Cell>
              {(rowData, rowIndex) => (page - 1) * limit + rowIndex + 1}
            </Cell>
          </Column>
          <Column flexGrow={1} align='center'>
            <HeaderCell>Type</HeaderCell>
            <Cell dataKey="type" />
          </Column>
          <Column flexGrow={2} align='center'>
            <HeaderCell>Parameter</HeaderCell>
            <Cell dataKey="parameter" />
          </Column>
          <Column flexGrow={1} align='center'>
            <HeaderCell>Value</HeaderCell>
            <Cell dataKey="value" />
          </Column>
        </Table>
        <div style={{ padding: 20 }}>
          <Pagination
            prev
            next
            first
            last
            ellipsis
            boundaryLinks
            maxButtons={5}
            size="xs"
            layout={["total", "-", "limit", "|", "pager", "skip"]}
            limitOptions={[10, 30, 50]}
            total={totalRowCount}
            limit={limit}
            activePage={page}
            onChangePage={setPage}
            onChangeLimit={handleChangeLimit}
          />
        </div>
      </Panel>
    )
  }

  const handleGetAttachmentsByType = (type) => {
    const attachments = formDetail?.ts_attachments?.filter((attachment) => attachment.type === type);
    return attachments?.map((attachment, index) => {
      if (attachment.id_attachments) {
        const fileName = attachment.path.substring(attachment.path.lastIndexOf('/') + 1);
        return {
          name: fileName,
          url: `${process.env.NEXT_PUBLIC_STORAGE}/${attachment.path}`,
          id_attachments: attachment.id_attachments,
          is_active: attachment.is_active,
        };
      } else {
        return {
          name: attachment.data.name,
          url: attachment?.data ? URL.createObjectURL(attachment?.data.blobFile) : '',
          size: attachment?.data?.blobFile?.size > 1024 * 1024
            ? `${(attachment?.data?.blobFile?.size / (1024 * 1024)).toFixed(2)}MB`
            : `${(attachment?.data?.blobFile?.size / 1024).toFixed(2)}KB`,
          flag: attachment.data ? 'new' : null,
          fileKey: attachment.data ? attachment.data.fileKey : null,
        }
      }
    });
  };

  const UpdateTsHeaderTransApi = async () => {
    setIsLoading(true);
    try {
      const res = await ApiTsDetail().putDetailAuto(formDetail)
      if (res.status === 200) {

        for (const detail of formAddDetail) {
          const postDetail = await ApiTsDetail().postDetailAuto(detail)

          if (postDetail.status !== 200) {
            toaster.push(
              Messages("error", `Error: something error. ${postDetail.message}!`),
              {
                placement: "topCenter",
                duration: 5000
              }
            );
          }
        }

        for (const fileItem of formDetail.ts_attachments) {
          if (fileItem.data !== undefined) {
            const formData = new FormData();
            formData.append("Files", fileItem.data.blobFile, fileItem.data.name);
            formData.append("id_header_trans", formDetail.id_header_trans);
            formData.append("type", fileItem.type);
            formData.append("path", "ts");
            formData.append("created_by", props.employee_id);

            const postRes = await ApiTsDetail().postAttachmentAuto(formData)

            if (postRes.status !== 200) {
              toaster.push(
                Messages("error", `Error: something error. ${postRes.message}!`),
                {
                  placement: "topCenter",
                  duration: 5000
                }
              );
            }
          }
        }

        toaster.push(
          Messages("success", `Success Save Data!`),
          {
            placement: "topCenter",
            duration: 5000
          }
        );

        setFormDetail({});
        setFormAddDetail([]);
        setCurrentState('Header');
        fetchData();
      } else {
        toaster.push(
          Messages("error", `Error: something error. ${res.message}!`),
          {
            placement: "topCenter",
            duration: 5000
          }
        );
      }
    } catch (error) {
      console.log("Axios Error", error)
      toaster.push(
        Messages("error", `Error: Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000
        }
      )
    } finally {
      setIsLoading(false);
    }
  }

  const SaveButton = () => (
    <Button
      appearance="primary"
      style={{ backgroundColor: "#1fd306" }}
      onClick={UpdateTsHeaderTransApi}
    >
      Save
    </Button>
  );

  const validateAddDetail = () => {
    const isValid = (Array.isArray(formDetail.detail_trans) ? [...formDetail.detail_trans] : []).concat(formAddDetail).some((detail) => detail.type === currentState);
    return isValid;
  }

  const attachmentsToDisplay = handleGetAttachmentsByType(currentState);

  const handleActivateDeactivate = (idAttachment, isActive) => {
    setFormDetail((prevFormDetail) => {
      const updatedAttachments = prevFormDetail.ts_attachments.map((attachment) => {
        if (attachment.id_attachments === idAttachment) {
          return { ...attachment, is_active: isActive ? 0 : 1 };
        }
        return attachment;
      });
      return { ...prevFormDetail, ts_attachments: updatedAttachments };
    });
  };

  const AttachmentDisplay = ({ attachment }) => {
    return (
      <Panel
        bordered
        className="mt-3 flex flex-col"
        style={{ width: 230, height: 330, position: 'relative' }}
        shaded
      >
        <div style={{ flexGrow: 1 }}>
          <img
            src={attachment?.url}
            alt={attachment?.name}
            style={{
              width: 200,
              height: 200,
              objectFit: 'cover',
              objectPosition: 'center',
              display: 'block',
              margin: '0 auto',
            }}
          />
          <p
            className="word-break mt-1 text-ellipsis"
            style={{ maxHeight: 40, overflow: 'hidden' }}
          >
            {attachment?.name}
          </p>
        </div>
        {
          attachment.flag === 'new' ? (
            <div style={{ position: 'absolute', bottom: 10, right: 10 }}>
              <Button appearance="ghost" color="red" onClick={() => handleRemoveAttachments(attachment.fileKey)}>
                <TrashIcon />
              </Button>
            </div>
          ) : (
            <div style={{ position: 'absolute', bottom: 10, left: 10, right: 10 }}>
              <Button
                appearance="ghost"
                size="sm"
                color={attachment.is_active === 0 ? 'green' : 'red'}
                onClick={() => handleActivateDeactivate(attachment.id_attachments, attachment.is_active)}
                block
              >
                {attachment.is_active === 0 ? 'Activate' : 'Deactivate'}
              </Button>
            </div>
          )
        }
      </Panel>
    );
  };

  return (
    <>
      <div>
        <Head>
          <title>TS Edit Automate Line</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className='m-4'>
          {
            isLoading ? (
              <Loader backdrop size="md" content="Saving data..." />
            ) : (
              <>
                <Breadcrumb className='mt-2'>
                  <Breadcrumb.Item href='/dashboard'>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>
                    {moduleName ? moduleName : "User Module"}
                  </Breadcrumb.Item>
                  <Breadcrumb.Item>TS Report</Breadcrumb.Item>
                  <Breadcrumb.Item active>TS Edit Automate Line</Breadcrumb.Item>
                </Breadcrumb>
                <Panel bordered className='mb-2'>
                  <Stack justifyContent='flex-start'>
                    <h3>TS Edit Automate Line</h3>
                  </Stack>
                </Panel>
                <Panel bordered className='mb-2'>
                  <div>
                    <h5>{currentState}</h5>

                    <div className='my-4'>
                      <Form fluid>
                        {currentState === "Header" && (
                          <>
                            <Form.Group>
                              <Form.ControlLabel>Line <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                name="id_line"
                                accepter={SelectPicker}
                                value={formDetail.id_line}
                                data={dataLine}
                                valueKey="id_line"
                                labelKey="line_description"
                                block
                                onChange={(value) => setFormDetail({ ...formDetail, id_line: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Jenis Sediaan <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                name="sediaan_type"
                                accepter={SelectPicker}
                                value={formDetail.sediaan_type}
                                data={dataJenisSediaan}
                                valueKey="value"
                                labelKey="label"
                                block
                                onChange={(value) => setFormDetail({ ...formDetail, sediaan_type: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Kode Produk <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                name="product_code"
                                accepter={SelectPicker}
                                value={formDetail.product_code}
                                data={dataProduct}
                                valueKey="product_code"
                                labelKey="product_code"
                                block
                                onChange={(value) => setFormDetail({ ...formDetail, product_code: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Nama Produk <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="product_name"
                                value={formDetail.product_name}
                                onChange={(value) => setFormDetail({ ...formDetail, product_name: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Kode Batch <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="batch_no"
                                value={formDetail.batch_no}
                                onChange={(value) => setFormDetail({ ...formDetail, batch_no: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Skala Produksi <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                name="production_scale"
                                accepter={SelectPicker}
                                value={formDetail.production_scale}
                                data={dataSkalaProduksi}
                                valueKey="value"
                                labelKey="value"
                                block
                                onChange={(value) => setFormDetail({ ...formDetail, production_scale: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Fokus Trial <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                name="trial_focus"
                                accepter={SelectPicker}
                                value={formDetail.trial_focus}
                                data={dataFokusTrial}
                                valueKey="value"
                                labelKey="value"
                                block
                                onChange={(value) => setFormDetail({ ...formDetail, trial_focus: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>No PPI <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="ppi_no"
                                value={formDetail.ppi_no}
                                onChange={(value) => setFormDetail({ ...formDetail, ppi_no: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Tujuan Proses <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="process_purpose"
                                value={formDetail.process_purpose}
                                onChange={(value) => setFormDetail({ ...formDetail, process_purpose: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Background <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="background"
                                value={formDetail.background}
                                onChange={(value) => setFormDetail({ ...formDetail, background: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Tanggal Proses <span className='text-red-500'>*</span></Form.ControlLabel>
                              <DatePicker
                                oneTap
                                value={formDetail.process_date ? new Date(formDetail.process_date) : null}
                                format="dd-MM-yyyy"
                                onChange={(value) => setFormDetail({ ...formDetail, process_date: value })}
                                block
                              />
                            </Form.Group>
                            <Stack justifyContent="end" className="mt-4">
                              <Button
                                appearance="primary"
                                onClick={() => setCurrentState("Binder")}
                              >
                                Lanjut Tahap Binder
                              </Button>
                            </Stack>
                            <Stack justifyContent="end" className="mt-2">
                              <SaveButton />
                            </Stack>
                          </>
                        )}
                        {currentState === "Binder" && (
                          <>
                            <Form.Group>
                              <Form.ControlLabel>Tanggal Binder <span className='text-red-500'>*</span></Form.ControlLabel>
                              <DatePicker
                                oneTap
                                value={formDetail.binder_date ? new Date(formDetail.binder_date) : null}
                                format="dd-MM-yyyy"
                                onChange={(value) => setFormDetail({ ...formDetail, binder_date: value })}
                                block
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Jumlah Pelarut <span className='text-red-500'>*</span></Form.ControlLabel>
                              <InputGroup>
                                <Form.Control
                                  placeholder="binder_mix_amount"
                                  value={formDetail.binder_mix_amount}
                                  onChange={(value) => setFormDetail({ ...formDetail, binder_mix_amount: parseFloat(value) })}
                                  type="number"
                                />
                                <InputGroup.Addon>L</InputGroup.Addon>
                              </InputGroup>
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Waktu Aduk <span className='text-red-500'>*</span></Form.ControlLabel>
                              <InputGroup>
                                <Form.Control
                                  placeholder="binder_mix_time"
                                  value={formDetail.binder_mix_time}
                                  onChange={(value) => setFormDetail({ ...formDetail, binder_mix_time: parseFloat(value) })}
                                  type="number"
                                />
                                <InputGroup.Addon>Menit</InputGroup.Addon>
                              </InputGroup>
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Binder Remarks <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="binder_remarks"
                                value={formDetail.binder_remarks}
                                onChange={(value) => setFormDetail({ ...formDetail, binder_remarks: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Lampiran</Form.ControlLabel>
                              <Uploader
                                key={currentState}
                                listType="picture-text"
                                action=''
                                defaultFileList={handleGetAttachmentsByType(currentState)}
                                onUpload={(file) => handleUploadAttachments(file, currentState)}
                                removable={false}
                                fileListVisible={false}
                              >
                                <Button appearance='ghost'>Ambil Gambar</Button>
                              </Uploader>
                              <Row>
                                {attachmentsToDisplay?.map((attachment, index) => (
                                  <Col md={5} sm={12} key={index}>
                                    <AttachmentDisplay
                                      attachment={attachment}
                                    />
                                  </Col>
                                ))}
                              </Row>
                            </Form.Group>
                            <Stack justifyContent="space-between" className="mt-4">
                              <Button
                                appearance="default"
                                onClick={() => setCurrentState("Header")}
                              >
                                Kembali ke Tahap Header
                              </Button>
                              <Button
                                appearance="primary"
                                onClick={() => setCurrentState("Granulasi")}
                              >
                                Lanjut Tahap Granulasi
                              </Button>
                            </Stack>
                            <Stack justifyContent="end" className="mt-2">
                              <SaveButton />
                            </Stack>
                          </>
                        )}
                        {currentState === "Granulasi" && (
                          <>
                            <Form.Group>
                              <Form.ControlLabel>Tanggal Granulasi <span className='text-red-500'>*</span></Form.ControlLabel>
                              <DatePicker
                                oneTap
                                value={formDetail.granule_date ? new Date(formDetail.granule_date) : null}
                                format="dd-MM-yyyy"
                                onChange={(value) => setFormDetail({ ...formDetail, granule_date: value })}
                                block
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Ampere <span className='text-red-500'>*</span></Form.ControlLabel>
                              <InputGroup>
                                <Form.Control
                                  placeholder="granule_ampere"
                                  value={formDetail.granule_ampere}
                                  onChange={(value) => setFormDetail({ ...formDetail, granule_ampere: parseFloat(value) })}
                                  type="number"
                                />
                                <InputGroup.Addon>A</InputGroup.Addon>
                              </InputGroup>
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Power <span className='text-red-500'>*</span></Form.ControlLabel>
                              <InputGroup>
                                <Form.Control
                                  placeholder="granule_power"
                                  value={formDetail.granule_power}
                                  onChange={(value) => setFormDetail({ ...formDetail, granule_power: parseFloat(value) })}
                                  type="number"
                                />
                                <InputGroup.Addon>Kwh</InputGroup.Addon>
                              </InputGroup>
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Granulasi Remarks <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="granule_remarks"
                                value={formDetail.granule_remarks}
                                onChange={(value) => setFormDetail({ ...formDetail, granule_remarks: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Lampiran</Form.ControlLabel>
                              <Uploader
                                key={currentState}
                                listType="picture-text"
                                action=''
                                defaultFileList={handleGetAttachmentsByType(currentState)}
                                onUpload={(file) => handleUploadAttachments(file, currentState)}
                                removable={false}
                                fileListVisible={false}
                              >
                                <Button appearance='ghost'>Ambil Gambar</Button>
                              </Uploader>
                              <Row>
                                {attachmentsToDisplay?.map((attachment, index) => (
                                  <Col md={5} sm={12} key={index}>
                                    <AttachmentDisplay
                                      attachment={attachment}
                                    />
                                  </Col>
                                ))}
                              </Row>
                            </Form.Group>
                            <Button
                              appearance='primary'
                              onClick={() => setShowDetailModal(true)}
                              disabled={validateAddDetail()}
                            >+ Data Granulasi</Button>
                            <NewDetailTable />
                            <DetailTable />
                            <Stack justifyContent="space-between" className="mt-4">
                              <Button
                                appearance="default"
                                onClick={() => setCurrentState("Binder")}
                              >
                                Kembali ke Tahap Binder
                              </Button>
                              <Button
                                appearance="primary"
                                onClick={() => setCurrentState("Pengeringan")}
                              >
                                Lanjut Tahap Pengeringan
                              </Button>
                            </Stack>
                            <Stack justifyContent="end" className="mt-2">
                              <SaveButton />
                            </Stack>
                          </>
                        )}
                        {currentState === "Pengeringan" && (
                          <>
                            <Form.Group>
                              <Form.ControlLabel>Tanggal Pengeringan <span className='text-red-500'>*</span></Form.ControlLabel>
                              <DatePicker
                                oneTap
                                value={formDetail.drying_date ? new Date(formDetail.drying_date) : null}
                                format="dd-MM-yyyy"
                                onChange={(value) => setFormDetail({ ...formDetail, drying_date: value })}
                                block
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>LOD <span className='text-red-500'>*</span></Form.ControlLabel>
                              <InputGroup>
                                <Form.Control
                                  placeholder="drying_lod"
                                  value={formDetail.drying_lod}
                                  onChange={(value) => setFormDetail({ ...formDetail, drying_lod: parseFloat(value) })}
                                  type="number"
                                />
                                <InputGroup.Addon>%</InputGroup.Addon>
                              </InputGroup>
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Product Temp <span className='text-red-500'>*</span></Form.ControlLabel>
                              <InputGroup>
                                <Form.Control
                                  placeholder="drying_product_temp"
                                  value={formDetail.drying_product_temp}
                                  onChange={(value) => setFormDetail({ ...formDetail, drying_product_temp: parseFloat(value) })}
                                  type="number"
                                />
                                <InputGroup.Addon>C</InputGroup.Addon>
                              </InputGroup>
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Exhaust Temp <span className='text-red-500'>*</span></Form.ControlLabel>
                              <InputGroup>
                                <Form.Control
                                  placeholder="drying_exhaust_temp"
                                  value={formDetail.drying_exhaust_temp}
                                  onChange={(value) => setFormDetail({ ...formDetail, drying_exhaust_temp: parseFloat(value) })}
                                  type="number"
                                />
                                <InputGroup.Addon>C</InputGroup.Addon>
                              </InputGroup>
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Pengeringan Remarks <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="drying_remarks"
                                value={formDetail.drying_remarks}
                                onChange={(value) => setFormDetail({ ...formDetail, drying_remarks: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Lampiran</Form.ControlLabel>
                              <Uploader
                                key={currentState}
                                listType="picture-text"
                                action=''
                                defaultFileList={handleGetAttachmentsByType(currentState)}
                                onUpload={(file) => handleUploadAttachments(file, currentState)}
                                removable={false}
                                fileListVisible={false}
                              >
                                <Button appearance='ghost'>Ambil Gambar</Button>
                              </Uploader>
                              <Row>
                                {attachmentsToDisplay?.map((attachment, index) => (
                                  <Col md={5} sm={12} key={index}>
                                    <AttachmentDisplay
                                      attachment={attachment}
                                    />
                                  </Col>
                                ))}
                              </Row>
                            </Form.Group>
                            <Button
                              appearance='primary'
                              onClick={() => setShowDetailModal(true)}
                              disabled={validateAddDetail()}
                            >+ Data Pengeringan</Button>
                            <NewDetailTable />
                            <DetailTable />
                            <Stack justifyContent="space-between" className="mt-4">
                              <Button
                                appearance="default"
                                onClick={() => setCurrentState("Granulasi")}
                              >
                                Kembali ke Tahap Granulasi
                              </Button>
                              <Button
                                appearance="primary"
                                onClick={() => setCurrentState("Pengayakan")}
                              >
                                Lanjut Tahap Pengayakan
                              </Button>
                            </Stack>
                            <Stack justifyContent="end" className="mt-2">
                              <SaveButton />
                            </Stack>
                          </>
                        )}
                        {currentState === "Pengayakan" && (
                          <>
                            <Form.Group>
                              <Form.ControlLabel>Tanggal Pengayakan <span className='text-red-500'>*</span></Form.ControlLabel>
                              <DatePicker
                                oneTap
                                value={formDetail.sifting_date ? new Date(formDetail.sifting_date) : null}
                                format="dd-MM-yyyy"
                                onChange={(value) => setFormDetail({ ...formDetail, sifting_date: value })}
                                block
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Screen Quadro <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                name="sifting_screen_quadro"
                                accepter={SelectPicker}
                                value={formDetail.sifting_screen_quadro}
                                data={dataScreenQuadro}
                                valueKey="value"
                                labelKey="value"
                                block
                                onChange={(value) => setFormDetail({ ...formDetail, sifting_screen_quadro: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Bin Tumbler <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                name="sifting_bin_tumbler"
                                accepter={SelectPicker}
                                value={formDetail.sifting_bin_tumbler}
                                data={dataBinTumbler}
                                valueKey="value"
                                labelKey="value"
                                block
                                onChange={(value) => setFormDetail({ ...formDetail, sifting_bin_tumbler: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Impeller Speed Quadro 1 <span className='text-red-500'>*</span></Form.ControlLabel>
                              <InputGroup>
                                <Form.Control
                                  placeholder="sifting_impeller_speed_1"
                                  value={formDetail.sifting_impeller_speed_1}
                                  onChange={(value) => setFormDetail({ ...formDetail, sifting_impeller_speed_1: parseFloat(value) })}
                                  type="number"
                                />
                                <InputGroup.Addon>RPM</InputGroup.Addon>
                              </InputGroup>
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Impeller Speed Quadro 2 <span className='text-red-500'>*</span></Form.ControlLabel>
                              <InputGroup>
                                <Form.Control
                                  placeholder="sifting_impeller_speed_2"
                                  value={formDetail.sifting_impeller_speed_2}
                                  onChange={(value) => setFormDetail({ ...formDetail, sifting_impeller_speed_2: parseFloat(value) })}
                                  type="number"
                                />
                                <InputGroup.Addon>RPM</InputGroup.Addon>
                              </InputGroup>
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Pengayakan Remarks <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="sifting_remarks"
                                value={formDetail.sifting_remarks}
                                onChange={(value) => setFormDetail({ ...formDetail, sifting_remarks: value })}
                              />
                            </Form.Group>
                            <Button
                              appearance='primary'
                              onClick={() => setShowDetailModal(true)}
                              disabled={validateAddDetail()}
                            >+ Data Pengayakan</Button>
                            <NewDetailTable />
                            <DetailTable />
                            <Stack justifyContent="space-between" className="mt-4">
                              <Button
                                appearance="default"
                                onClick={() => setCurrentState("Pengeringan")}
                              >
                                Kembali ke Tahap Pengeringan
                              </Button>
                              <Button
                                appearance="primary"
                                onClick={() => setCurrentState("FinalMix")}
                              >
                                Lanjut Tahap Final Mix
                              </Button>
                            </Stack>
                            <Stack justifyContent="end" className="mt-2">
                              <SaveButton />
                            </Stack>
                          </>
                        )}
                        {currentState === "FinalMix" && (
                          <>
                            <Form.Group>
                              <Form.ControlLabel>Tanggal Final Mix <span className='text-red-500'>*</span></Form.ControlLabel>
                              <DatePicker
                                oneTap
                                value={formDetail.final_mix_date ? new Date(formDetail.final_mix_date) : null}
                                format="dd-MM-yyyy"
                                onChange={(value) => setFormDetail({ ...formDetail, final_mix_date: value })}
                                block
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Waktu Aduk 1 <span className='text-red-500'>*</span></Form.ControlLabel>
                              <InputGroup>
                                <Form.Control
                                  placeholder="final_mix_time_mix_1"
                                  value={formDetail.final_mix_time_mix_1}
                                  onChange={(value) => setFormDetail({ ...formDetail, final_mix_time_mix_1: parseFloat(value) })}
                                  type="number"
                                />
                                <InputGroup.Addon>Menit</InputGroup.Addon>
                              </InputGroup>
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Waktu Aduk 2 <span className='text-red-500'>*</span></Form.ControlLabel>
                              <InputGroup>
                                <Form.Control
                                  placeholder="final_mix_time_mix_2"
                                  value={formDetail.final_mix_time_mix_2}
                                  onChange={(value) => setFormDetail({ ...formDetail, final_mix_time_mix_2: parseFloat(value) })}
                                  type="number"
                                />
                                <InputGroup.Addon>Menit</InputGroup.Addon>
                              </InputGroup>
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Kesimpulan <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="ts_conclusion"
                                value={formDetail.ts_conclusion}
                                onChange={(value) => setFormDetail({ ...formDetail, ts_conclusion: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Tindak Lanjut <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="ts_followup"
                                value={formDetail.ts_followup}
                                onChange={(value) => setFormDetail({ ...formDetail, ts_followup: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Bobot Granul <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="bobot_granul"
                                value={formDetail.bobot_granul}
                                onChange={(value) => setFormDetail({ ...formDetail, bobot_granul: parseFloat(value),
                                  rendemen:
                                  (parseFloat(value) / formDetail.bobot_teoritis) *
                                  100,
                                })}
                                type="number"
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Bobot Teoritis <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="bobot_teoritis"
                                value={formDetail.bobot_teoritis}
                                onChange={(value) => setFormDetail({ ...formDetail, bobot_teoritis: parseFloat(value),
                                  rendemen:
                                  (formDetail.bobot_granul / parseFloat(value)) * 100,
                                })}
                                type="number"
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Rendemen <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="rendemen"
                                value={formDetail.rendemen}
                                onChange={(value) => setFormDetail({ ...formDetail, rendemen: parseFloat(value) })}
                                type="number"
                                disabled
                              />
                            </Form.Group>
                            <Stack justifyContent="space-between" className="mt-4">
                              <Button
                                appearance="default"
                                onClick={() => setCurrentState("Pengayakan")}
                              >
                                Kembali ke Tahap Pengayakan
                              </Button>
                              <Button
                                appearance="primary"
                                onClick={() => setCurrentState("AnalisisData")}
                              >
                                Lanjut Tahap Analisis Data
                              </Button>
                            </Stack>
                            <Stack justifyContent="end" className="mt-2">
                              <SaveButton />
                            </Stack>
                          </>
                        )}
                        {currentState === "AnalisisData" && (
                          <>
                            <Form.Group>
                              <Form.ControlLabel>Diskusi <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="discussion"
                                value={formDetail.discussion}
                                onChange={(value) => setFormDetail({ ...formDetail, discussion: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Analisis Data <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                placeholder="analyzed_data"
                                value={formDetail.analyzed_data}
                                onChange={(value) => setFormDetail({ ...formDetail, analyzed_data: value })}
                              />
                            </Form.Group>
                            <Form.Group>
                              <Form.ControlLabel>Spv <span className='text-red-500'>*</span></Form.ControlLabel>
                              <Form.Control
                                name="spv_employee_id"
                                accepter={SelectPicker}
                                value={formDetail.spv_employee_id}
                                data={dataSpv}
                                valueKey="employee_id"
                                labelKey="employee_name"
                                block
                                onChange={(value) => setFormDetail({ ...formDetail, spv_employee_id: value })}
                              />
                            </Form.Group>
                            <Stack justifyContent="space-between" className="mt-4">
                              <Button
                                appearance="default"
                                onClick={() => setCurrentState("FinalMix")}
                              >
                                Kembali ke Tahap Final Mix
                              </Button>
                              <SaveButton />
                            </Stack>
                          </>
                        )}
                      </Form>
                    </div>
                  </div>
                </Panel>
              </>
            )
          }
        </div >

        <Modal
          backdrop="static"
          open={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedApiType(null);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Detail {currentState}</Modal.Title>
            <Modal.Body>
              <SelectPicker
                name="selected_api_type"
                value={selectedApiType}
                data={dataDetail}
                valueKey="type_api"
                labelKey="type_api"
                block
                onChange={(value) => setSelectedApiType(value)}
              />
            </Modal.Body>
            <Modal.Footer>
              <Button
                onClick={() => {
                  setShowDetailModal(false);
                  setSelectedApiType(null);
                }}
                appearance='subtle'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  getDetailApiData(selectedApiType);
                  setSelectedApiType(null);
                }}
                appearance='primary'
              >
                Add
              </Button>
            </Modal.Footer>
          </Modal.Header>
        </Modal>

        <Modal
          backdrop="static"
          open={showSelectedDetailModal}
          onClose={() => {
            setShowSelectedDetailModal(false);
            setSelectedDetail(null);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>{selectedDetail?.type} Data in JSON Format</Modal.Title>
            <Modal.Body>
              <Modal.Body>
                <pre>
                  {selectedDetail?.data && JSON.stringify(JSON.parse(selectedDetail?.data), null, 2)}
                </pre>
              </Modal.Body>
            </Modal.Body>
          </Modal.Header>
        </Modal>
      </ContainerLayout >
    </>
  )
}

export async function getServerSideProps(context) {
  const { query } = context;
  const data = query.data || ''; // Access the data parameter from the query

  // Return the data as props
  return {
    props: {
      data,
    },
  };
}