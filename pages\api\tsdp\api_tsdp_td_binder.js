import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](
    `${process.env.NEXT_PUBLIC_REAGEN}/ts/${url}`,
    data
  )
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiTsdpTDBinder() {
  return {
    getAllTsTransactionBinder: createApiFunction(
      "get",
      "transaction-d-binder/list"
    ),
    getTsTransactionBinderById: createApiFunction(
      "post",
      "transaction-d-binder/id"
    ),
    updateTsTransactionBinder: createApiFunction(
      "put",
      "transaction-d-binder/edit"
    ),
    postTsTransactionBinder: createApiFunction(
      "post",
      "transaction-d-binder/create"
    ),
    UpdateStatusTsTransactionBinder: createApiFunction(
      "put",
      "transaction-d-binder/edit-status"
    ),
  };
}
