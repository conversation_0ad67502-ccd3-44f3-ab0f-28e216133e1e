import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  try {
    const response = await axios[method](
      `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-master-line/${url}`,
      data
    );
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default function API_TsMatrixLine() {
  return {
    getAutomateMasterLine: createApiFunction("get", "get-automate-line"),
    getManualMasterLine: createApiFunction("post", "get/manual"),
  };
}