import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiStep() {
  return {
    getAllStep: createApiFunction("get", "pqr/masterdata_step/list"),
    getAllActiveStep: createApiFunction("get", "pqr/masterdata_step/list-active"),
    createStep: createApiFunction("post", "pqr/masterdata_step/create"),
    editStep: createApiFunction("put", "pqr/masterdata_step/edit"),
    editStatusStep: createApiFunction("put", "pqr/masterdata_step/edit-status"),
    getAllActiveStepPPI: createApiFunction("post", "pqr/masterdata_step/list-ppi-active"),
  };
}
