import {useRouter} from 'next/router';
import MenuManagement from '../menu_management';
import ModuleManagement from '../module_management';
import UserManagement from '../user_management';
import {useEffect, useState} from 'react';
import Head from 'next/head';
import GASidebar from './GASidebar';
import DepartmentManagement from '../department_management';

function GeneralAdministration() {
  const [content, setContent] = useState('');
  const router = useRouter();
  const {module_name} = router.query;

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    if (!dataLogin) {
      router.push('/');
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push('/dashboard');
      return;
    }
  }, []);

  return (
    <>
      <div>
        <Head>
          <title>General Administration</title>
        </Head>
      </div>
      <GASidebar />
      <div>
        {content === 'menu' && <MenuManagement />}
        {content === 'module' && <ModuleManagement />}
        {content === 'user' && <UserManagement />}
        {content === 'department' && <DepartmentManagement />}
      </div>
    </>
  );
}

export default GeneralAdministration;
