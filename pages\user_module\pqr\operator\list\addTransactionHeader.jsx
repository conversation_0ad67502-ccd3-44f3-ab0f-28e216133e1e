import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, Panel, Stack, Tag, Button, useToaster, RadioGroup, Radio, SelectPicker, Notification, Form, Input, Checkbox, Loader, FlexboxGrid, Col } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";


import { useRouter } from "next/router";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiBindingParamPpi from "@/pages/api/pqr/binding_param_ppi/api_binding_param_ppi";
import ApiPPI from "@/pages/api/pqr/ppi/api_masterdata_ppi";


export default function AddTransactionHeader() {
  const toaster = useToaster();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [addLoading, setAddLoading] = useState(false);


  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [transactionHeadersDataState, setTransactionHeadersDataState] = useState([]);
  const [ppiDataState, setPPIDataState] = useState([]);


  const emptyAddTransactionHeaderForm = {
    id_trans_header: null,
    id_ppi: null,
    batch_code: "",
    iot_desc: "",
    line_desc: "",
    remarks: "",
    wetmill: "",
    status_transaction: 2,
    create_date: null,
    create_by: "",
    update_date: null,
    update_by: null,
    delete_date: null,
    delete_by: null,
    details: [],
  };


  const [addTransactionHeaderForm, setAddTransactionHeaderForm] = useState(emptyAddTransactionHeaderForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [bindingParamPpiDataState, setBindingParamPpiDataState] = useState([]);
  const [isLoadingBindingParam, setIsLoadingBindingParam] = useState(false);


  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };


  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/operator")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandleGetAllPPIApi();
      // Reset binding param data when either id_ppi or wetmill changes
      if (addTransactionHeaderForm.id_ppi && addTransactionHeaderForm.wetmill) {
        resetAllInputs();
        setBindingParamPpiDataState([]); // Clear current binding params


        // Clear any existing details in the form
        setAddTransactionHeaderForm((prev) => ({
          ...prev,
          details: [],
        }));
        HandleGetAllBindingParamPpiApi(addTransactionHeaderForm.id_ppi, addTransactionHeaderForm.wetmill);
      }
    }
  }, [addTransactionHeaderForm.id_ppi, addTransactionHeaderForm.wetmill]);


  const HandleGetAllPPIApi = async () => {
    try {
      const res = await ApiPPI().getAllFullyApproveMasterPPI();
      if (res.status === 200) {
        const options = res.data.map((ppi) => ({
          label: ppi.ppi_name,
          value: ppi.id_ppi,
        }));
        setPPIDataState(options);
      } else {
        console.log("gagal mengambil data", res.message);
      }
    } catch (error) {
      console.log("gagal mengambil data", error);
    }
  };


  const HandleGetAllBindingParamPpiApi = async (id_ppi, wetmill) => {
    try {
      setIsLoadingBindingParam(true);
      let res;
      if (wetmill === "N") {
        res = await ApiBindingParamPpi().getIdPpiActiveBindingParamPpiWetmillN({ id_ppi: parseInt(id_ppi) });
      } else if (wetmill === "Y") {
        res = await ApiBindingParamPpi().getIdPpiActiveBindingParamPpiWetmillY({ id_ppi: parseInt(id_ppi) });
      } else {
        console.log("Invalid wetmill value");
        return;
      }


      if (res.status === 200) {
        const initializedData = res.data.map((param) => ({
          ...param,
          actual_value: null,
          result: null,
        }));
        setBindingParamPpiDataState(initializedData);
      } else {
        console.log("gagal mengambil data binding ", res.message);
        setBindingParamPpiDataState([]);
      }
    } catch (error) {
      console.log("Error gagal mengambil data binding", error);
      setBindingParamPpiDataState([]);
    } finally {
      setIsLoadingBindingParam(false);
    }
  };


  const HandleAddTransactionHeaderApi = async () => {
    const errors = {};


    // Cek apakah batch_code dan id_ppi sudah ada
    const isDuplicate = transactionHeadersDataState.some((transaction) => transaction.batch_code === addTransactionHeaderForm.batch_code && transaction.id_ppi === addTransactionHeaderForm.id_ppi);


    if (isDuplicate) {
      errors.batch_code = "Kode batch dan kombinasi PPI sudah ada";
    }


    if (!addTransactionHeaderForm.id_ppi) {
      errors.id_ppi = "Nama PPI wajib diisi";
    }


    if (!addTransactionHeaderForm.batch_code) {
      errors.batch_code = "Kode batch wajib diisi";
    }


    if (!addTransactionHeaderForm.iot_desc) {
      errors.iot_desc = "Deskripsi IoT wajib diisi";
    }


    if (!addTransactionHeaderForm.line_desc) {
      errors.line_desc = "Deskripsi baris wajib diisi";
    }


    if (!addTransactionHeaderForm.remarks) {
      errors.remarks = "Catatan wajib diisi";
    }


    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }
    setLoading(true);


    try {
      const res = await ApiTransactionHeader().createTransactionHeader({
        ...addTransactionHeaderForm,
        create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });


      if (res.status === 200) {
        setAddTransactionHeaderForm(emptyAddTransactionHeaderForm);
        showNotification("success", "Data Berhasil Ditambahkan");
        const newIdTransactionH = res.id;
        router.push(`/user_module/pqr/creation/list/pqr?IdHeader=${newIdTransactionH}`);
      } else {
        console.log("gagal menambah data", res.message);
        showNotification("error", "Gagal Menambah data");
      }
    } catch (error) {
      console.log("error gagal menambah data ", error);
      showNotification("error", "Terjadi Kesalahan Saat Menambahkan Data");
    } finally {
      setLoading(false);
    }
  };


  const resetAllInputs = () => {
    // Reset all form inputs to empty
    setBindingParamPpiDataState((prevState) =>
      prevState.map((param) => ({
        ...param,
        actual_value: null,
        result: null,
      }))
    );
  };


  const handleResultChange = (value, index) => {
    setBindingParamPpiDataState((prevState) => {
      const updatedParameters = [...prevState];


      // Set actual_value
      updatedParameters[index].actual_value = value;


      // Validasi hanya jika actual_value tidak null
      if (updatedParameters[index].actual_value !== null) {
        // Handle binding_type 1 (min-max range)
        if (updatedParameters[index].binding_type === 1) {
          const actualValue = parseFloat(updatedParameters[index].actual_value);
          const minValue = parseFloat(updatedParameters[index].min_value);
          const maxValue = parseFloat(updatedParameters[index].max_value);


          updatedParameters[index].result = actualValue >= minValue && actualValue <= maxValue ? "MS" : "TMS";
        } else if (updatedParameters[index].binding_type === 2) {
          updatedParameters[index].result = parseFloat(updatedParameters[index].actual_value) === parseFloat(updatedParameters[index].absolute_value) ? "MS" : "TMS";
        } else if (updatedParameters[index].binding_type === 3) {
          updatedParameters[index].result = updatedParameters[index].actual_value.trim() !== "" ? "MS" : null;
        }
      }


      // Update details state dengan validasi
      setAddTransactionHeaderForm((prevFormValue) => {
        const validDetails = updatedParameters
          .filter((param) => param.actual_value !== null && param.actual_value !== undefined && param.actual_value !== "")
          .map((param) => ({
            id_binding: param.id_binding,
            parameter_name: param.parameter_name,
            binding_type: param.binding_type,
            min_value: param.min_value,
            max_value: param.max_value,
            absolute_value: param.absolute_value,
            description_value: param.description_value,
            actual_value: param.actual_value,
            result: param.result,
            create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
          }));


        return {
          ...prevFormValue,
          details: validDetails,
        };
      });


      return updatedParameters;
    });
  };


  return (
    <div>
      <div>
        <Head>
          <title>Transaction Header</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>List</Breadcrumb.Item>
                  <Breadcrumb.Item>Transaction Header</Breadcrumb.Item>
                  <Breadcrumb.Item active>Tambah Transaction Header</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Halaman Tambah Transaction Header</h5>
              </Stack>
            }
          ></Panel>

          <div className="p-6 border border-slate-200 rounded-lg shadow-sm w-full">
            <Form className="space-y-6 w-full">
              {/* Nama PPI */}
              <Form.Group className="flex items-center w-full">
                <div className="w-1/3 text-sm font-bold flex items-center justify-between">
                  <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                  <span className="mx-2">:</span>
                </div>
                <SelectPicker
                  data={ppiDataState}
                  value={addTransactionHeaderForm.id_ppi}
                  onChange={(value) => {
                    setAddTransactionHeaderForm((prevFormValue) => ({
                      ...prevFormValue,
                      id_ppi: value,
                    }));
                    setErrorsAddForm((prevErrors) => ({
                      ...prevErrors,
                      id_ppi: undefined,
                    }));
                  }}
                  block
                  placeholder="Pilih PPI"
                />
                {errorsAddForm.id_ppi && <p style={{ color: "red" }}>{errorsAddForm.id_ppi}</p>}
              </Form.Group>

              {/* Kode Batch */}
              <Form.Group className="flex items-center">
                <div className="w-1/3 text-sm font-bold flex items-center justify-between">
                  <Form.ControlLabel>Kode Batch</Form.ControlLabel>
                  <span className="mx-2">:</span>
                </div>
                <Form.Control
                  name="batch_code"
                  value={addTransactionHeaderForm.batch_code}
                  onChange={(value) => {
                    setAddTransactionHeaderForm((prevFormValue) => ({
                      ...prevFormValue,
                      batch_code: value,
                    }));
                    setErrorsAddForm((prevErrors) => ({
                      ...prevErrors,
                      batch_code: undefined,
                    }));
                  }}
                  className="w-full"
                />
                {errorsAddForm.batch_code && <p style={{ color: "red" }}>{errorsAddForm.batch_code}</p>}
              </Form.Group>

              {/* Deskripsi IOT */}
              <Form.Group className="flex items-center">
                <div className="w-1/3 text-sm font-bold flex items-center justify-between">
                  <Form.ControlLabel>Deskripsi IOT</Form.ControlLabel>
                  <span className="mx-2">:</span>
                </div>
                <Form.Control
                  name="iot_desc"
                  value={addTransactionHeaderForm.iot_desc}
                  onChange={(value) => {
                    setAddTransactionHeaderForm((prevFormValue) => ({
                      ...prevFormValue,
                      iot_desc: value,
                    }));
                    setErrorsAddForm((prevErrors) => ({
                      ...prevErrors,
                      iot_desc: undefined,
                    }));
                  }}
                  className="w-full"
                />
                {errorsAddForm.iot_desc && <p style={{ color: "red" }}>{errorsAddForm.iot_desc}</p>}
              </Form.Group>

              {/* Deskripsi Baris */}
              <Form.Group className="flex items-center">
                <div className="w-1/3 text-sm font-bold flex items-center justify-between">
                  <Form.ControlLabel>Deskripsi Baris</Form.ControlLabel>
                  <span className="mx-2">:</span>
                </div>
                <Form.Control
                  name="line_desc"
                  value={addTransactionHeaderForm.line_desc}
                  onChange={(value) => {
                    setAddTransactionHeaderForm((prevFormValue) => ({
                      ...prevFormValue,
                      line_desc: value,
                    }));
                    setErrorsAddForm((prevErrors) => ({
                      ...prevErrors,
                      line_desc: undefined,
                    }));
                  }}
                  className="w-full"
                />
                {errorsAddForm.line_desc && <p style={{ color: "red" }}>{errorsAddForm.line_desc}</p>}
              </Form.Group>

              {/* Catatan */}
              <Form.Group className="flex items-center">
                <div className="w-1/3 text-sm font-bold flex items-center justify-between">
                  <Form.ControlLabel>Catatan</Form.ControlLabel>
                  <span className="mx-2">:</span>
                </div>
                <Form.Control
                  name="remarks"
                  value={addTransactionHeaderForm.remarks}
                  onChange={(value) => {
                    setAddTransactionHeaderForm((prevFormValue) => ({
                      ...prevFormValue,
                      remarks: value,
                    }));
                    setErrorsAddForm((prevErrors) => ({
                      ...prevErrors,
                      remarks: undefined,
                    }));
                  }}
                  className="w-full"
                />
                {errorsAddForm.remarks && <p style={{ color: "red" }}>{errorsAddForm.remarks}</p>}
              </Form.Group>

              {/* Wetmill */}
              <Form.Group className="flex items-center">
                <div className="w-1/3 text-sm font-bold flex items-center justify-between">
                  <Form.ControlLabel>Wetmill</Form.ControlLabel>
                  <span className="mx-2">:</span>
                </div>
                <RadioGroup
                  name="wetmill"
                  inline
                  value={addTransactionHeaderForm.wetmill}
                  onChange={(value) => {
                    setAddTransactionHeaderForm((prevFormValue) => ({
                      ...prevFormValue,
                      wetmill: value,
                    }));
                    setErrorsAddForm((prevErrors) => ({
                      ...prevErrors,
                      wetmill: undefined,
                    }));
                  }}
                >
                  <Radio value="Y">Ya</Radio>
                  <Radio value="N">Tidak</Radio>
                </RadioGroup>
                {errorsAddForm.wetmill && <p style={{ color: "red" }}>{errorsAddForm.wetmill}</p>}
              </Form.Group>

              {/* PARAMETER SECTION */}
              {addTransactionHeaderForm.id_ppi && addTransactionHeaderForm.wetmill && (
                <div className="bg-white p-6 border border-slate-200 rounded-lg shadow-md">
                  {isLoadingBindingParam ? (
                    <div className="text-center">
                      <Loader />
                    </div>
                  ) : bindingParamPpiDataState.length > 0 ? (
                    <>
                      {/* Tambahkan wrapper untuk scroll horizontal */}
                      <div className="overflow-x-auto">
                        <div className="grid grid-cols-4 mb-4 pb-2 border-b border-slate-200 font-semibold text-slate-600">
                          <div className="text-left">Nama Parameter</div>
                          <div className="text-left">Kriteria Standar</div>
                          <div className="text-left mx-2">Hasil</div>
                          <div className="text-left">Comply</div>
                        </div>

                        {bindingParamPpiDataState
                          .sort((a, b) => {
                            if (a.flag_controllable === "N" && a.is_automate === "N") return -1;
                            if (b.flag_controllable === "N" && b.is_automate === "N") return 1;

                            if (a.flag_controllable === "Y" && a.is_automate === "N") return -1;
                            if (b.flag_controllable === "Y" && b.is_automate === "N") return 1;

                            if (a.flag_controllable === "Y" && a.is_automate === "Y") return -1;
                            if (b.flag_controllable === "Y" && b.is_automate === "Y") return 1;

                            return 0; // Jika tidak memenuhi kondisi di atas, kembalikan 0
                          })
                          .map((param, index) => (
                            <div key={param.id_binding} className="grid grid-cols-4 items-center py-2">
                              {/* Parameter Name - First Column */}
                              <div className="text-left w-full">
                                <label className="text-sm font-medium text-slate-600 block w-full">{param.parameter_name}</label>
                              </div>

                              {/* Kriteria Standar - Second Column */}
                              <div className="flex flex-col md:flex-row items-center w-full space-y-2 md:space-y-0 md:space-x-2">
                                {param.binding_type === 1 && (
                                  <>
                                    <Input value={param.min_value} readOnly placeholder="Min" className="w-full md:flex-1 text-center" />
                                    <span className="hidden md:inline text-slate-500">-</span>
                                    <Input value={param.max_value} readOnly placeholder="Max" className="w-full md:flex-1 text-center" />
                                  </>
                                )}
                                {param.binding_type === 2 && <Input value={param.absolute_value} readOnly placeholder="Absolute" className="w-full" />}
                                {param.binding_type === 3 && <Input value={param.description_value} readOnly placeholder="Description" className="w-full" />}
                              </div>

                              <div className="flex items-center mx-2">
                                <Form.Control
                                  name={`param_${param.id_binding}`}
                                  value={param.actual_value || ""}
                                  placeholder={param.is_automate === "Y" ? "-" : "Masukan Nilai"}
                                  disabled={param.is_automate === "Y"}
                                  onChange={(value) => {
                                    if ((param.binding_type === 1 || param.binding_type === 2) && !/^\d*\.?\d*$/.test(value)) {
                                      return;
                                    }
                                    handleResultChange(value, index);
                                  }}
                                  style={{ width: "100%" }}
                                />
                              </div>

                              {/* Comply - Fourth Column */}
                              <div className="flex items-center w-full">{param.binding_type !== 3 && <Checkbox checked={param.result === "MS"} disabled style={{ cursor: "default" }} />}</div>
                            </div>
                          ))}
                      </div>
                    </>
                  ) : (
                    <div className="text-center font-semibold py-4">Tidak terdapat rincian data.</div>
                  )}
                </div>
              )}

              {/* Buttons */}
              <Form.Group className="flex justify-start md:justify-end">
                <Stack spacing={10}>
                  <Button onClick={() => router.back()} appearance="subtle" disabled={addLoading}>
                    Batal
                  </Button>
                  <Button
                    onClick={HandleAddTransactionHeaderApi}
                    appearance="primary"
                    loading={addLoading}
                    disabled={addLoading || !addTransactionHeaderForm.id_ppi || !addTransactionHeaderForm.wetmill || isLoadingBindingParam || bindingParamPpiDataState.length === 0}
                  >
                    Tambah
                  </Button>
                </Stack>
              </Form.Group>
            </Form>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}