import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import MainContent from '@/components/layout/MainContent';
import { Button, Stack, Loader, Pagination, Table, IconButton, Input, InputGroup, Panel } from 'rsuite';
import Head from 'next/head';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import ParameterApi from '@/pages/api/parameterApi';
import { paginate } from '@/utils/paginate';
import EditIcon from '@rsuite/icons/Edit';
import SearchIcon from '@rsuite/icons/Search';
import CloseOutlineIcon from '@rsuite/icons/CloseOutline';

export default function Parameter() {
  const [isLoading, setIsLoading] = useState(false);
  const [allParameterData, setAllParameterData] = useState([]);
  const [moduleName, setModuleName] = useState('');
  const router = useRouter();
  let path = 'masterdata/Parameter';
  let { data } = router.query;
  const { HeaderCell, Cell, Column } = Table;
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const [searchKeyword, setSearchKeyword] = useState("");
  data ? (data = JSON.parse(data)) : data;
  var [breadcrumbsData, setBreadcrumbsData] = useState([]);
  const { GetAllParameter } = ParameterApi();

  const GetDataParameter = async () => {
    setIsLoading(true);
    const { data: parameters } = await GetAllParameter();

    if (parameters !== null && parameters.length > 0) {
      setAllParameterData(parameters);
      return;
    }

    setIsLoading(false);
  };

  // data to be displayed in the table
  var filteredData = allParameterData.filter(item => item.Parameter_Name.toLowerCase().includes(searchKeyword.toLowerCase()) || item.Method_Desc.toLowerCase().includes(searchKeyword.toLowerCase()));
  var datas = paginate(filteredData, page, limit);

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    const moduleNameValue = localStorage.getItem('module_name');
    if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
      router.push('/');
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push('/dashboard');
      return;
    }

    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path)
    );
    if (validateUserAccess.length === 0) {
      router.push('/dashboard');
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ''
    ) {
      router.push('/dashboard');
      return;
    }
    const asPathWithoutQuery = router.asPath.split('?')[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split('/')
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      asPathNestedRoutes[1],
      asPathNestedRoutes[2],
    ];
    const capitalizeFirstLetter = (str) => {
      return str.charAt(0).toUpperCase() + str.slice(1);
    };
    const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
      const words = item.split(/(?=[A-Z])/);
      return words.map((word) => capitalizeFirstLetter(word)).join(' ');
    });
    setBreadcrumbsData(breadCrumbsResult);

    setModuleName(moduleNameValue);

    GetDataParameter();
  }, []);

  return (
    <>
      <div>
        <Head>
          <title>Master Data Parameter</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <MainContent>
          <ModuleContentHeader
            module_name={moduleName}
            breadcrumbs={breadcrumbsData}
          />

          <Stack spacing={6} direction="column" alignItems="flex-start">
            <Button
              appearance="primary"
              onClick={() =>
                router.push('/user_module/masterdata/Parameter/AddParameter')
              }
            >
              New Parameter
            </Button>
            {allParameterData.length === 0 && isLoading === true && <Loader />}
            {allParameterData.length === 0 && isLoading === false && (
              <h4>No Parameter FOUND.</h4>
            )}
          </Stack>
          {allParameterData.length > 0 && <>
            <Stack direction='row' justifyContent='flex-end'>
              <InputGroup inside>
                <InputGroup.Addon>
                  <SearchIcon />
                </InputGroup.Addon>
                <Input
                  placeholder="Search"
                  value={searchKeyword}
                  onChange={value => {
                    setSearchKeyword(value);
                    setPage(1);
                  }}
                />
                <InputGroup.Addon
                  onClick={() => {
                    setSearchKeyword("");
                    setPage(1);
                  }}
                  style={{
                    display: searchKeyword ? "block" : "none",
                    color: "red",
                    cursor: "pointer",
                  }}
                >
                  <CloseOutlineIcon />
                </InputGroup.Addon>
              </InputGroup>
            </Stack>
            <Panel>
              <Table bordered
                cellBordered
                height={400}
                data={datas}
                sortColumn={sortColumn}
                sortType={sortType}
                onSortColumn={handleSortColumn}
              >
                <Column width={60} align="center" fixed>
                  <HeaderCell>No</HeaderCell>
                  <Cell>
                    {(rowData, rowIndex) => {
                      return rowIndex + 1 + limit * (page - 1);
                    }}
                  </Cell>
                </Column>
                <Column width={250} fixed>
                  <HeaderCell>Parameter</HeaderCell>
                  <Cell dataKey='Parameter_Name' />
                </Column>
                <Column width={150} fixed>
                  <HeaderCell>Input Method</HeaderCell>
                  <Cell dataKey='Method_Desc' />
                </Column>
                <Column width={100} fixed>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (parseInt(rowData.Is_Active) == 1) {
                        return "Active";
                      } else {
                        return "Non-Active";
                      }
                    }}
                  </Cell>
                </Column>
                <Column width={60} align="center" fixed>
                  <HeaderCell>...</HeaderCell>
                  <Cell style={{ padding: "6px" }}>
                    {(rowData) => <IconButton size='sm' icon={<EditIcon />} title='Tinjau dokumen' appearance='ghost' color='orange' onClick={() => {
                      router.push({
                        pathname:
                          '/user_module/masterdata/Parameter/EditParameter',
                        query: { idParameter: rowData.Id_Parameter },
                      });
                    }} />}

                  </Cell>
                </Column>
              </Table>
              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager"]}
                  // layout={["total", "-", "limit", "|", "pager", "skip"]}
                  total={
                    allParameterData.length
                  }
                  limitOptions={[10, 30, 50]}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
          </>}
        </MainContent>
      </ContainerLayout>
    </>
  );
}
