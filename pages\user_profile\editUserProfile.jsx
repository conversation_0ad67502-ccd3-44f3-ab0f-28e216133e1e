import Head from "next/head";
import MainContent from "@/components/layout/MainContent";
import { Button, Form, ButtonToolbar, Dropdown } from "rsuite";
import { useEffect, useState } from "react";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import UseTestApi from "../api/userApi";
import { useRouter } from "next/router";
import ContainerLayout from "@/components/layout/ContainerLayout";

export default function EditUserProfile({ userProfileData }) {
  const router = useRouter();
  const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
  const MySwal = withReactContent(Swal);
  const [employeeId, setEmployeeId] = useState("");
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  // const [department, setDepartment] = useState("");
  // const [division, setDivision] = useState("");
  const { UpdateProfileApi } = UseTestApi();

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }
    setEmployeeId(dataLogin.employee_id);
  }, []);

  useEffect(() => {
    if (userProfileData) {
      setName(userProfileData[0].Name);
      setEmail(userProfileData[0].Email);
      // setDepartment(userProfileData[0].Department);
      // setDivision(userProfileData[0].Division);
      return;
    } else {
      router.back();
      return;
    }
  }, [userProfileData]);

  const submitHandler = async () => {
    setIsSubmitButtonDisabled(true);
    if (name === "" || email === "") {
      setIsSubmitButtonDisabled(false);
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please fill all required data!",
      });
      return;
    }

    const updateData = {
      employee_id: employeeId,
      name,
      email,
      // department,
      // division,
    };

    const { Data: updateProfileResult } = await UpdateProfileApi(updateData);

    if (updateProfileResult) {
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "Profile updated successfully.",
        showConfirmButton: false,
        timer: 2500,
      });
      router.push("/user_profile");
    } else {
      setIsSubmitButtonDisabled(false);
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Update user profile FAILED.",
      });
      return;
    }
  };

  return (
    <>
      <Head>
        <title>Edit Profile</title>
      </Head>
      <ContainerLayout title="Profile">
        <MainContent>
          <div className="mb-5 p-2" style={{ borderLeft: "0.5em solid teal" }}>
            <h4>Edit Profile</h4>
          </div>
          <Form layout="horizontal" onSubmit={submitHandler}>
            <Form.Group controlId="employee_id">
              <Form.ControlLabel>Employee ID</Form.ControlLabel>
              <Form.Control
                name="employee_id"
                value={employeeId}
                disabled={true}
              />
            </Form.Group>
            <Form.Group controlId="name">
              <Form.ControlLabel>Name</Form.ControlLabel>
              <Form.Control
                name="name"
                value={name}
                autoComplete="off"
                onChange={(value) => setName(value)}
              />
            </Form.Group>
            <Form.Group controlId="email">
              <Form.ControlLabel>Email</Form.ControlLabel>
              <Form.Control
                name="email"
                value={email}
                autoComplete="off"
                onChange={(value) => setEmail(value)}
              />
            </Form.Group>
            {/* <Form.Group controlId="department">
              <Form.ControlLabel>Department</Form.ControlLabel>
              <Dropdown
                title={department ? department : "-- Select Department --"}
                onSelect={(value) => setDepartment(value)}
              >
                <Dropdown.Item eventKey="">
                  -- Select Department --
                </Dropdown.Item>
                <Dropdown.Item eventKey="IT">IT</Dropdown.Item>
                <Dropdown.Item eventKey="Finance">Finance</Dropdown.Item>
              </Dropdown>
            </Form.Group>
            <Form.Group controlId="division">
              <Form.ControlLabel>Division</Form.ControlLabel>
              <Form.Control
                name="division"
                value={division}
                autoComplete="off"
                onChange={(value) => setDivision(value)}
              />
            </Form.Group> */}

            <Form.Group>
              <ButtonToolbar>
                <Button
                  appearance="primary"
                  type="submit"
                  disabled={isSubmitButtonDisabled}
                >
                  Submit
                </Button>
                <Button
                  appearance="default"
                  onClick={() => {
                    router.back();
                  }}
                >
                  Cancel
                </Button>
              </ButtonToolbar>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  // const emp_id = query.EditUserProfile[1];
  const { emp_id } = query;
  const { GetByIdTestApi } = UseTestApi();

  const { Data: userProfile } = await GetByIdTestApi(emp_id);

  let userProfileData = [];

  if (userProfile != undefined) {
    userProfileData = userProfile;
  }

  return {
    props: {
      userProfileData,
    },
  };
}
