import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import MainContent from "@/components/layout/MainContent";
import { Dropdown, Button, Form, ButtonToolbar, Toggle } from "rsuite";
import ipcApi from "@/pages/api/ipcApi";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import ContainerLayout from "@/components/layout/ContainerLayout";
import ModuleContentHeader from "@/components/ModuleContentHeader";

export default function EditInstrument({
  interComponentData,
  setupPageIndex,
  setupInterCompData,
  instrumentData,
  dataId,
}) {
  const MySwal = withReactContent(Swal);
  const [instrumentName, setInstrumentName] = useState("");
  const [instrumentCode, setInstrumentCode] = useState("");
  const [isActive, setIsActive] = useState(false);
  const [location, setLocation] = useState("");
  const router = useRouter();
  const { UpdateDataInstrument } = ipcApi();
  const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
  const [moduleName, setModuleName] = useState("");
  let path = "masterdata/Instrument";
  var [breadcrumbsData, setBreadcrumbsData] = useState([]);
  const [sessionAuth, setSessionAuth] = useState(null);
  //const breadcrumbsData = [moduleName, "Setup Master Data", "Edit Instrument"];

  useEffect(() => {
    // if (setupInterCompData) {
    //   setInstrumentName(interComponentData.Instrument_Name);
    //   setInstrumentCode(interComponentData.Code_Instrument);
    //   setLocation(interComponentData.Location);
    //   if (interComponentData.Is_Active === "Y") {
    //     setIsActive(true);
    //   } else {
    //     setIsActive(false);
    //   }
    // }
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (dataLogin === null || dataLogin === undefined || dataLogin === "") {
      router.push("/");
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push("/dashboard");
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path)
    );
    if (validateUserAccess.length === 0) {
      router.push("/dashboard");
      return;
    }
    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ""
    ) {
      router.push("/dashboard");
      return;
    }

    const asPathWithoutQuery = router.asPath.split("?")[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split("/")
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      `Setup ${asPathNestedRoutes[1]}`,
      `Edit ${asPathNestedRoutes[2]}`,
    ];
    setBreadcrumbsData(routerBreadCrumbsData);

    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (instrumentData.length > 0) {
      setInstrumentName(instrumentData[0].Instrument_Name);
      setInstrumentCode(instrumentData[0].Code_Instrument);
      setLocation(instrumentData[0].Location);
      if (instrumentData[0].Is_Active === "Y") {
        setIsActive(true);
      } else {
        setIsActive(false);
      }
    }
  }, [instrumentData]);

  //   submitHandler
  const submitHandler = async () => {
    let dataInput;
    if (instrumentName === "" || instrumentCode === "" || location === "") {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please fill all required data!",
      });
      return;
    } else {
      const isInstrumentActive = isActive ? "Y" : "N";
      dataInput = {
        id_instrument: dataId,
        instrument_name: instrumentName,
        location: location,
        code_instrument: instrumentCode,
        is_active: isInstrumentActive,
        update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      };
    }
    // send data
    const { Data: updatingInstrument } = await UpdateDataInstrument(dataInput);

    if (updatingInstrument) {
      setIsSubmitButtonDisabled(true);
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "Instrument updated successfully.",
        allowOutsideClick: false,
        showConfirmButton: false,
        timer: 2500,
      });

      router.push("/user_module/masterdata/Instrument");
    } else {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Instrument update FAILED.",
      });
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Edit Instrument</title>
        </Head>
      </div>
      {/* Modal */}

      <ContainerLayout title="User Module">
        <MainContent>
          {/* <div className="mb-3">
            <h4>PIMS - Menu</h4>
            <p>Setup Master Data &gt; Edit Instrument</p>
          </div> */}
          <ModuleContentHeader
            module_name={moduleName}
            breadcrumbs={breadcrumbsData}
          />

          <Form layout="horizontal" onSubmit={submitHandler}>
            <Form.Group controlId="instrument_name">
              <Form.ControlLabel>Instrument Name</Form.ControlLabel>
              <Form.Control
                name="instrument_name"
                type="text"
                value={instrumentName}
                onChange={(value) => setInstrumentName(value)}
              />
            </Form.Group>
            <Form.Group controlId="location">
              <Form.ControlLabel>Location</Form.ControlLabel>
              <Dropdown
                title={location !== "" ? location : "-- Select Item --"}
                onSelect={(value) => setLocation(value)}
              >
                <Dropdown.Item eventKey="">-- Select Item --</Dropdown.Item>
                <Dropdown.Item eventKey="Production">Production</Dropdown.Item>
              </Dropdown>
            </Form.Group>
            <Form.Group controlId="instrument_code">
              <Form.ControlLabel>Instrument Code</Form.ControlLabel>
              <Form.Control
                name="instrument_code"
                type="text"
                value={instrumentCode}
                onChange={(value) => setInstrumentCode(value)}
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>Active</Form.ControlLabel>
              <Toggle
                checked={isActive}
                onChange={() => setIsActive((value) => !value)}
              />
            </Form.Group>

            <Form.Group>
              <ButtonToolbar>
                <Button appearance="primary" type="submit">
                  Submit
                </Button>
                <Button appearance="default" onClick={() => router.back()}>
                  Cancel
                </Button>
              </ButtonToolbar>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  // const dataId = query.EditInstrument[1];
  const { idInstrument } = query;
  const { GetInstrumentById } = ipcApi();

  const reqData = {
    id_instrument: parseInt(idInstrument),
  };
  const { Data: instrument } = await GetInstrumentById(reqData);

  let instrumentData = [];
  if (instrument != undefined) {
    instrumentData = instrument;
  }

  return {
    props: {
      instrumentData,
      dataId: parseInt(idInstrument),
    },
  };
}
