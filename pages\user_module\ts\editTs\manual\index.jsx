import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";

import {
  Button,
  Stack,
  Panel,
  Breadcrumb,
  Form,
  Uploader,
  Divider,
  InputGroup,
  Tag,
  SelectPicker,
  DatePicker,
  useToaster,
  Modal,
  Row,
  Col,
} from "rsuite";

import ContainerLayout from "@/components/layout/ContainerLayout";
import ExpandOutlineIcon from "@rsuite/icons/ExpandOutline";

import ApiTsDetail from "@/pages/api/ts/api_ts_detail";
import API_TsMatrixCategoriesParameter from "@/pages/api/ts/api_ts-matrix-categories-parameter";
import Messages from "@/components/Messages";
import TrashIcon from "@rsuite/icons/Trash";

export default function TsEditManual({ data }) {
  const router = useRouter();
  const toaster = useToaster();

  const [moduleName, setModuleName] = useState("");
  const [props, setProps] = useState([]);

  const [canFetch, setCanFetch] = useState(false);
  const [dataDetail, setDataDetail] = useState({
    detail_trans1: [],
    ts_details1_old: [],
  });
  const [dataSpv, setDataSpv] = useState([]);
  const [dataProduct, setDataProduct] = useState([]);
  const [tsMatrixCategories, setTsMatrixCategories] = useState([]);

  const [currentPanel, setCurrentPanel] = useState("Header");
  const [uploadedImages, setUploadedImages] = useState([
    { panel: "Binder", files: [] },
    { panel: "Granulasi", files: [] },
    { panel: "Pengeringan", files: [] },
  ]);

  const [modalGranulasi, setModalGranulasi] = useState(false);
  const [modalPengeringan, setModalPengeringan] = useState(false);
  const [modalPengayakan, setModalPengayakan] = useState(false);

  const [granulasiDetail, setGranulasiDetail] = useState([]);
  const [pengeringanDetail, setPengeringanDetail] = useState([]);
  const [pengayakanDetail, setPengayakanDetail] = useState([]);

  const [formAttachments, setFormAttachments] = useState([]);

  const [editIndex, setEditIndex] = useState(null);
  const [isEditingNewDetail, setIsEditingNewDetail] = useState(false);

  const emptyDetailForm = {
    id_matrix1: null,
    id_categories1: null,
    value1: null,

    id_matrix2: null,
    id_categories2: null,
    value2: null,

    id_matrix3: null,
    id_categories3: null,
    value3: null,
  };

  const [formDetailValue, setFormDetailValue] = useState(emptyDetailForm);

  const dataSkalaProduksi = ["Pilot", "Commercial"]?.map((item) => ({
    label: item,
    value: item,
  }));

  const dataJenisSediaan = ["Kapsul", "Tablet", "Sirup"]?.map((item) => ({
    label: item,
    value: item,
  }));

  const dataFokusTrial = ["Carry Over", "Diversifikasi", "Others"]?.map(
    (item) => ({ label: item, value: item })
  );

  const dataScreenQuadro = ["10", "20", "30"]?.map((item) => ({
    label: item,
    value: item,
  }));

  const dataBinTumbler = ["10", "20", "30"]?.map((item) => ({
    label: item,
    value: item,
  }));

  const fetchData = async () => {
    const result = await ApiTsDetail().getDetailManual({
      id_reference: parseInt(data),
    });

    const resultSpv = await ApiTsDetail().getAllActiveSpv();

    const resultProd = await ApiTsDetail().getAllOfProduct();

    setDataDetail(result.data ? result.data : {});
    setDataSpv(resultSpv.data ? resultSpv.data : {});
    setDataProduct(resultProd.data ? resultProd.data : {});
  };

  const handleGetTsMatrixCategoriesApi = async () => {
    try {
      const response =
        await API_TsMatrixCategoriesParameter().getAllActiveTsMatrixCategoriesParameter();

      if (response.status === 200 && response.data) {
        const formattedData = response.data?.map((item) => ({
          label: item.parameter_name,
          value: item.id_matrix,
          id_categories: item.id_categories,
          main_category_name: item.main_category_name,
        }));

        setTsMatrixCategories(formattedData);
      }
    } catch (error) {
      console.error("Error fetching matrix categories:", error);
    }
  };

  const handleDetailGranulasi = () => {
    const selectedMatrix = tsMatrixCategories.find(
      (item) => item.value === formDetailValue.id_matrix1
    );

    const newDetail = {
      id_matrix: formDetailValue.id_matrix1,
      matrix_label: selectedMatrix?.label,
      id_categories: selectedMatrix?.id_categories,
      value: formDetailValue.value1,
      isNew: true,
    };

    if (editIndex !== null) {
      setGranulasiDetail((prevDetails) => {
        const updatedDetails = [...prevDetails];
        updatedDetails[editIndex] = newDetail;
        return updatedDetails;
      });
    } else {
      setGranulasiDetail((prevDetails) => [...prevDetails, newDetail]);
    }

    setEditIndex(null);
  };

  const handleDetailPengeringan = () => {
    const selectedMatrix = tsMatrixCategories.find(
      (item) => item.value === formDetailValue.id_matrix2
    );

    const newDetail = {
      id_matrix: formDetailValue.id_matrix2,
      matrix_label: selectedMatrix?.label,
      id_categories: selectedMatrix?.id_categories,
      value: formDetailValue.value2,
    };

    setPengeringanDetail((prevDetails) => [...prevDetails, newDetail]);
  };

  const handleDetailPengayakan = () => {
    const selectedMatrix = tsMatrixCategories.find(
      (item) => item.value === formDetailValue.id_matrix3
    );

    const newDetail = {
      id_matrix: formDetailValue.id_matrix3,
      matrix_label: selectedMatrix?.label,
      id_categories: selectedMatrix?.id_categories,
      value: formDetailValue.value3,
    };

    setPengayakanDetail((prevDetails) => [...prevDetails, newDetail]);
  };

  const handleGetAttachmentsByType = (type) => {
    const attachments = dataDetail.attachments1?.filter(
      (attachment) => attachment.type === type
    );
    return attachments?.map((attachment) => {
      const fileName = attachment.path.substring(
        attachment.path.lastIndexOf("/") + 1
      );
      return {
        id_attachments1: attachment.id_attachments1,
        name: fileName,
        url: `${process.env.NEXT_PUBLIC_STORAGE}/${attachment.path}`,
        is_active: attachment.is_active,
      };
    });
  };

  const updatePanelImages = (panel, files) => {
    setUploadedImages((prevImages) => {
      const panelIndex = prevImages.findIndex((item) => item.panel === panel);

      if (panelIndex !== -1) {
        const updatedImages = [...prevImages];
        updatedImages[panelIndex] = { ...updatedImages[panelIndex], files };
        return updatedImages;
      } else {
        return [...prevImages, { panel, files }];
      }
    });
  };

  const getPanelImages = (panel) => {
    return uploadedImages.find((item) => item.panel === panel)?.files || [];
  };

  const handleDeactiveExistingDetail = (detail, type) => {
    if (type === "existing") {
      const existingIndex = dataDetail.detail_trans1.findIndex(
        (d) => d.id_matrix === detail.id_matrix
      );

      if (existingIndex !== -1 && currentPanel === detail.main_category_name) {
        const updatedDetails = [...dataDetail.detail_trans1];
        updatedDetails[existingIndex] = {
          ...updatedDetails[existingIndex],
          is_active: 0,
        };

        const updatedOldDetails = Array.isArray(dataDetail.ts_details1_old)
          ? [...dataDetail.ts_details1_old]
          : [];
        const oldDetailIndex = updatedOldDetails.findIndex(
          (d) => d.id_matrix === detail.id_matrix
        );

        if (oldDetailIndex !== -1) {
          updatedOldDetails[oldDetailIndex] = {
            ...updatedOldDetails[oldDetailIndex],
            is_active: 0,
          };
        } else {
          updatedOldDetails.push({
            ...detail,
            is_active: 0,
          });
        }
        setDataDetail((prev) => ({
          ...prev,
          detail_trans1: updatedDetails,
          ts_details1_old: updatedOldDetails,
        }));
      }
    }
  };

  const handleReactivateExistingDetail = (detail, type) => {
    if (type === "existing") {
      const existingIndex = dataDetail.detail_trans1.findIndex(
        (d) => d.id_matrix === detail.id_matrix
      );

      if (existingIndex !== -1 && currentPanel === detail.main_category_name) {
        const updatedDetails = [...dataDetail.detail_trans1];
        updatedDetails[existingIndex] = {
          ...updatedDetails[existingIndex],
          is_active: 1,
        };

        const updatedOldDetails = [...dataDetail.ts_details1_old];
        const oldDetailIndex = updatedOldDetails.findIndex(
          (d) => d.id_matrix === detail.id_matrix
        );
        if (oldDetailIndex !== -1) {
          updatedOldDetails[oldDetailIndex] = {
            ...updatedOldDetails[oldDetailIndex],
            is_active: 1,
          };
        }

        setDataDetail((prev) => ({
          ...prev,
          detail_trans1: updatedDetails,
          ts_details1_old: updatedOldDetails,
        }));
      }
    }
  };

  const handleEditExistingDetail = (detail, type) => {
    const existingIndex = dataDetail.detail_trans1.findIndex(
      (d) => d.id_matrix === detail.id_matrix
    );

    setEditIndex(existingIndex);
    setIsEditingNewDetail(false);

    if (type === "existing") {
      if (currentPanel === detail.main_category_name) {
        setFormDetailValue({
          id_matrix1:
            detail.main_category_name === "Granulasi" ? detail.id_matrix : null,
          value1:
            detail.main_category_name === "Granulasi" ? detail.value : null,
          id_matrix2:
            detail.main_category_name === "Pengeringan"
              ? detail.id_matrix
              : null,
          value2:
            detail.main_category_name === "Pengeringan" ? detail.value : null,
          id_matrix3:
            detail.main_category_name === "Pengayakan"
              ? detail.id_matrix
              : null,
          value3:
            detail.main_category_name === "Pengayakan" ? detail.value : null,
        });

        if (detail.main_category_name === "Granulasi") {
          setModalGranulasi(true);
        } else if (detail.main_category_name === "Pengeringan") {
          setModalPengeringan(true);
        } else if (detail.main_category_name === "Pengayakan") {
          setModalPengayakan(true);
        }
      }
    }
  };

  const handleEditNewDetail = (index, type) => {
    const detail =
      type === "Granulasi"
        ? granulasiDetail[index]
        : type === "Pengeringan"
        ? pengeringanDetail[index]
        : pengayakanDetail[index];

    setEditIndex(index);
    setIsEditingNewDetail(true);

    setFormDetailValue({
      id_matrix1: type === "Granulasi" ? detail.id_matrix : null,
      value1: type === "Granulasi" ? detail.value : null,
      id_matrix2: type === "Pengeringan" ? detail.id_matrix : null,
      value2: type === "Pengeringan" ? detail.value : null,
      id_matrix3: type === "Pengayakan" ? detail.id_matrix : null,
      value3: type === "Pengayakan" ? detail.value : null,
    });

    if (type === "Granulasi") {
      setModalGranulasi(true);
    } else if (type === "Pengeringan") {
      setModalPengeringan(true);
    } else if (type === "Pengayakan") {
      setModalPengayakan(true);
    }
  };

  const handleRemoveNewDetail = (indexToRemove) => {
    setGranulasiDetail((prevDetails) =>
      prevDetails.filter((_, index) => index !== indexToRemove)
    );

    setPengeringanDetail((prevDetails) =>
      prevDetails.filter((_, index) => index !== indexToRemove)
    );

    setPengayakanDetail((prevDetails) =>
      prevDetails.filter((_, index) => index !== indexToRemove)
    );
  };

  const AttachmentDisplay = ({ attachment, onRemove, onUpdateStatus }) => {
    const [isActive, setIsActive] = useState(attachment?.is_active ?? 1);

    const handleDeactivateReactivate = () => {
      const newStatus = isActive === 1 ? 0 : 1;
      setIsActive(newStatus);

      onUpdateStatus({ ...attachment, is_active: newStatus });
    };

    return (
      <Panel
        bordered
        className="mt-3 flex flex-col"
        style={{
          width: "100%",
          maxWidth: 230,
          height: 330,
          position: "relative",
        }}
        shaded
      >
        <div style={{ flexGrow: 1 }}>
          <img
            src={attachment?.url}
            alt={attachment?.name}
            style={{
              width: "100%",
              height: 200,
              objectFit: "cover",
              objectPosition: "center",
              display: "block",
              margin: "0 auto",
            }}
          />
          <p
            className="word-break mt-1 text-ellipsis"
            style={{
              maxHeight: 40,
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {attachment?.name}
          </p>
        </div>

        <div
          style={{
            position: "absolute",
            bottom: 10,
            left: 10,
            right: 10,
            display: "flex",
            justifyContent: "flex-end",
          }}
        >
          {attachment?.blobFile ? (
            <Button
              appearance="ghost"
              size="xs"
              color="red"
              onClick={() => onRemove(attachment)}
              style={{
                width: 32,
                height: 32,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                padding: 0,
              }}
            >
              <TrashIcon />
            </Button>
          ) : (
            <Button
              appearance="ghost"
              size="sm"
              color={isActive === 1 ? "red" : "green"}
              onClick={handleDeactivateReactivate}
              block
            >
              {isActive === 1 ? "Deactivate" : "Reactivate"}
            </Button>
          )}
        </div>
      </Panel>
    );
  };

  const isDisabled = Object.entries(dataDetail).some(([key, value]) => {
    if (key === "spv_employee_id") return false;
    if (key === "ts_keyword") return false;
    if (key === "mgr_employee_id") return false;
    if (key === "mgr_approved_dt") return false;
    if (key === "spv_approved_dt") return false;
    if (key === "attachments1") return false;
    if (key === "spv_name") return false;
    if (key === "mgr_name") return false;
    if (key === "link_remarks") return false;
  
    const isValueEmpty =
      value === null || value === undefined || value === "";
  
    // const anyDetailEmpty = ["granulasiDetail", "pengeringanDetail", "pengayakanDetail"].some(
    //   (detailKey) => {
    //     const detailValue = dataDetail[detailKey];
    //     const isEmpty =
    //       detailValue === null || detailValue === undefined || detailValue === "";
    //     if (isEmpty) {
    //       console.log(`[EMPTY DETAIL] detailKey: ${detailKey}, value:`, detailValue);
    //     }
    //     return isEmpty;
    //   }
    // );
  
    // const shouldDisable = isValueEmpty && anyDetailEmpty;
    const shouldDisable = isValueEmpty 
  
    if (shouldDisable) {
      console.log(`[DISABLED TRIGGERED] key: ${key}, value:`, value);
    }
  
    return shouldDisable;
  });
  
  console.log("SPV field disabled:", isDisabled);
  

  const handleUpdateAttachmentStatus = (updatedAttachment) => {
    const updatedAttachments = dataDetail.attachments1?.map((attachment) => {
      if (attachment.id_attachments1 === updatedAttachment.id_attachments1) {
        return { ...attachment, is_active: updatedAttachment.is_active };
      }
      return attachment;
    });

    setDataDetail((prevDetail) => ({
      ...prevDetail,
      attachments1: updatedAttachments,
    }));
  };

  const handleRemoveAttachments = (file) => {
    setFormAttachments((prevAttachments) =>
      prevAttachments.filter(
        (attachment) => attachment.data.fileKey !== file.fileKey
      )
    );

    setUploadedImages((prevImages) =>
      prevImages?.map((item) => ({
        ...item,
        files: item.files.filter(
          (attachment) => attachment.fileKey !== file.fileKey
        ),
      }))
    );
  };

  const handleEditManualApi = async () => {
    try {
      const ts_detail_trans1 = [
        ...granulasiDetail,
        ...pengeringanDetail,
        ...pengayakanDetail,
      ].filter((detail) => detail.id_matrix && detail.value);

      const ts_details1_old = Array.isArray(dataDetail.ts_details1_old)
        ? dataDetail.ts_details1_old?.map((detail) => ({
            ...detail,
            is_active: detail.is_active || 0,
          }))
        : [];

      const ts_attachments1 = dataDetail.attachments1?.map((attachment) => ({
        ...attachment,
        is_active: attachment.is_active || 0,
      }));

      const body = {
        ...dataDetail,
        update_by: props.employee_id,
        ts_details1_old,
        ts_detail_trans1,
        ts_attachments1,
      };

      const result = await ApiTsDetail().putDetailManual(body);

      if (result.status === 200) {
        const id_header_trans1 = dataDetail.id_header_trans1;

        const uploadImages = async (images) => {
          if (!Array.isArray(images) || images.length === 0) return [];

          return await Promise.all(
            images?.map(async (file) => {
              if (!file) return null;

              const formData = new FormData();
              formData.append("id_header_trans1", id_header_trans1);
              formData.append("type", file.currentPanel);
              formData.append("path", "ts");
              formData.append("created_by", props.employee_id);
              formData.set("Files", file.blobFile, file.name);

              try {
                const response = await ApiTsDetail().postAttachmentManual(
                  formData
                );

                if (response.status === 200) {
                  return {
                    type: file.currentPanel,
                    path: `storage/ts/${id_header_trans1}/${response.data.fileName}`,
                  };
                } else {
                  console.error("Error uploading file:", response.data);
                }
              } catch (error) {
                console.error("Error uploading file:", error);
              }
              return null;
            })
          );
        };

        await Promise.all(
          uploadedImages?.map((panel) => uploadImages(panel.files))
        ).then((paths) => paths.flat());

        fetchData();
        setGranulasiDetail([]);
        setPengeringanDetail([]);
        setPengayakanDetail([]);
        setUploadedImages([]);

        toaster.push(Messages("success", "Successfully updated data!"), {
          placement: "topCenter",
          duration: 3000,
        });
      } else {
        toaster.push(Messages("warning", "Error updating data"), {
          placement: "topCenter",
          duration: 3000,
        });
      }
    } catch (error) {
      console.error("Error:", error);
      toaster.push(
        Messages("error", `Error: "${error.message}". Please try again later!`),
        { placement: "topCenter", duration: 5000 }
      );
    }
  };

  const SaveButton = () => (
    <Button
      appearance="primary"
      style={{ backgroundColor: "#1fd306" }}
      onClick={handleEditManualApi}
    >
      Save
    </Button>
  );

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    setCanFetch(true);
    fetchData();
    handleGetTsMatrixCategoriesApi();
  }, []);

  return (
    <>
      <div>
        <Head>
          <title>Ts Edit Manual</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item active>
              {moduleName ? moduleName : "User Module"}
            </Breadcrumb.Item>
          </Breadcrumb>
          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h3>TS Edit Manual Line</h3>
            </Stack>
            <Divider />
            <Stack justifyContent="flex-start">
              <Tag color="blue">{dataDetail.ts_code}</Tag>
              <Tag color="green" style={{ marginLeft: "5px" }}>
                {dataDetail.line_description}
              </Tag>
            </Stack>
          </Panel>

          <Form fluid>
            {currentPanel === "Header" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between" className="mb-4">
                  <h4 className="mb-4">Header</h4>
                </Stack>

                <Form.Group controlId="jenis_sediaan">
                  <Form.ControlLabel>
                    Jenis Sediaan<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="jenis_sediaan"
                    accepter={SelectPicker}
                    value={dataDetail.sediaan_type}
                    data={dataJenisSediaan}
                    valueKey="label"
                    labelKey="value"
                    block
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, sediaan_type: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="product_code">
                  <Form.ControlLabel>
                    Kode Produk<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="product_code"
                    accepter={SelectPicker}
                    value={dataDetail.product_code}
                    data={dataProduct}
                    valueKey="product_code"
                    labelKey="product_code"
                    block
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, product_code: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="product_name">
                  <Form.ControlLabel>
                    Nama Produk<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    placeholder="product_name"
                    value={dataDetail.product_name}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, product_name: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="batch_no">
                  <Form.ControlLabel>
                    Kode Batch<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    placeholder="batch_no"
                    value={dataDetail.batch_no}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, batch_no: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="production_scale">
                  <Form.ControlLabel>
                    Skala Produk<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="production_scale"
                    accepter={SelectPicker}
                    value={dataDetail.production_scale}
                    data={dataSkalaProduksi}
                    valueKey="value"
                    labelKey="value"
                    block
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, production_scale: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="trial_focus">
                  <Form.ControlLabel>
                    Fokus Trial<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="trial_focus"
                    accepter={SelectPicker}
                    value={dataDetail.trial_focus}
                    data={dataFokusTrial}
                    valueKey="value"
                    labelKey="value"
                    block
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, trial_focus: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="ppi_no">
                  <Form.ControlLabel>
                    No PPI<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    placeholder="ppi_no"
                    value={dataDetail.ppi_no}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, ppi_no: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="process_purpose">
                  <Form.ControlLabel>
                    Tujuan Proses<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    placeholder="process_purpose"
                    value={dataDetail.process_purpose}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, process_purpose: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="background">
                  <Form.ControlLabel>
                    Background<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    placeholder="background"
                    value={dataDetail.background}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, background: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="process_date">
                  <Form.ControlLabel>
                    Tanggal Proses<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <DatePicker
                    oneTap
                    value={
                      dataDetail.process_date
                        ? new Date(dataDetail.process_date)
                        : null
                    }
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, process_date: value });
                    }}
                    block
                  />
                </Form.Group>
                <Stack justifyContent="end" className="mt-4">
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("Binder")}
                  >
                    Lanjut Tahap Binder
                  </Button>
                </Stack>
                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}

            {currentPanel === "Binder" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between">
                  <h4 className="mb-4">Binder</h4>
                </Stack>
                <Form.Group controlId="binder_date">
                  <Form.ControlLabel>Tanggal Binder</Form.ControlLabel>
                  <DatePicker
                    oneTap
                    value={
                      dataDetail.binder_date
                        ? new Date(dataDetail.binder_date)
                        : null
                    }
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, binder_date: value });
                    }}
                    block
                  />
                </Form.Group>

                <Form.Group controlId="binder_mix_amount">
                  <Form.ControlLabel>Jumlah Pelarut</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="binder_mix_amount"
                      value={dataDetail.binder_mix_amount}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          binder_mix_amount: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> L</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="binder_mix_time">
                  <Form.ControlLabel>Waktu Aduk</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="binder_mix_time"
                      value={dataDetail.binder_mix_time}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          binder_mix_time: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> Menit</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="binder_remarks">
                  <Form.ControlLabel>Binder Remarks</Form.ControlLabel>
                  <Form.Control
                    placeholder="binder_remarks"
                    value={dataDetail.binder_remarks}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, binder_remarks: value });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Lampiran Binder<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Uploader
                    action=""
                    listType="picture-text"
                    fileList={[
                      ...(handleGetAttachmentsByType("Binder") || []),
                      ...getPanelImages("Binder"),
                    ]}
                    fileListVisible={false}
                    appearance="ghost"
                    onChange={(fileList) => {
                      const existingFiles = [
                        ...(handleGetAttachmentsByType("Binder") || []),
                        ...getPanelImages("Binder"),
                      ];

                      const newFiles = fileList
                        .filter(
                          (file) =>
                            !existingFiles.some(
                              (existingFile) => existingFile.name === file.name
                            )
                        )
                        ?.map((file) => ({
                          ...file,
                          currentPanel: "Binder",
                          url: file.blobFile
                            ? URL.createObjectURL(file.blobFile)
                            : file.url,
                        }));

                      if (newFiles.length > 0) {
                        updatePanelImages("Binder", [
                          ...getPanelImages("Binder"),
                          ...newFiles,
                        ]);
                      }
                    }}
                  >
                    <Button>Ambil Gambar</Button>
                  </Uploader>

                  <Row>
                    {[
                      ...(handleGetAttachmentsByType("Binder") || []),
                      ...getPanelImages("Binder"),
                    ]?.map((attachment, index) => (
                      <Col md={5} sm={12} key={index}>
                        <AttachmentDisplay
                          attachment={attachment}
                          onRemove={() => handleRemoveAttachments(attachment)}
                          onUpdateStatus={handleUpdateAttachmentStatus}
                        />
                      </Col>
                    ))}
                  </Row>
                </Form.Group>

                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("Header")}
                  >
                    Kembali ke Tahap Header
                  </Button>
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("Granulasi")}
                  >
                    Lanjut Tahap Granulasi
                  </Button>
                </Stack>
                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}

            {currentPanel === "Granulasi" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between">
                  <h4 className="mb-4">Granulasi</h4>
                </Stack>
                <Form.Group controlId="granule_date">
                  <Form.ControlLabel>Tanggal Granulasi</Form.ControlLabel>
                  <DatePicker
                    oneTap
                    value={
                      dataDetail.granule_date
                        ? new Date(dataDetail.granule_date)
                        : null
                    }
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, granule_date: value });
                    }}
                    block
                  />
                </Form.Group>

                <Form.Group controlId="granule_ampere">
                  <Form.ControlLabel>Ampere</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="granule_ampere"
                      value={dataDetail.granule_ampere}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          granule_ampere: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> A</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="granule_power">
                  <Form.ControlLabel>Power</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="granule_power"
                      value={dataDetail.granule_power}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          granule_power: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> Kwh</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="granule_remarks">
                  <Form.ControlLabel>Granulasi Remarks</Form.ControlLabel>
                  <Form.Control
                    placeholder="granule_remarks"
                    value={dataDetail.granule_remarks}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, granule_remarks: value });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Lampiran Granulasi<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Uploader
                    action=""
                    listType="picture-text"
                    fileList={[
                      ...(handleGetAttachmentsByType("Granulasi") || []),
                      ...getPanelImages("Granulasi"),
                    ]}
                    fileListVisible={false}
                    appearance="ghost"
                    onChange={(fileList) => {
                      const existingFiles = [
                        ...(handleGetAttachmentsByType("Granulasi") || []),
                        ...getPanelImages("Granulasi"),
                      ];

                      const newFiles = fileList
                        .filter(
                          (file) =>
                            !existingFiles.some(
                              (existingFile) => existingFile.name === file.name
                            )
                        )
                        ?.map((file) => ({
                          ...file,
                          currentPanel: "Granulasi",
                          url: file.blobFile
                            ? URL.createObjectURL(file.blobFile)
                            : file.url,
                        }));

                      if (newFiles.length > 0) {
                        updatePanelImages("Granulasi", [
                          ...getPanelImages("Granulasi"),
                          ...newFiles,
                        ]);
                      }
                    }}
                  >
                    <Button>Ambil Gambar</Button>
                  </Uploader>

                  <Row>
                    {[
                      ...(handleGetAttachmentsByType("Granulasi") || []),
                      ...getPanelImages("Granulasi"),
                    ]?.map((attachment, index) => (
                      <Col md={5} sm={12} key={index}>
                        <AttachmentDisplay
                          attachment={attachment}
                          onRemove={() => handleRemoveAttachments(attachment)}
                          onUpdateStatus={handleUpdateAttachmentStatus}
                        />
                      </Col>
                    ))}
                  </Row>
                </Form.Group>

                <Divider></Divider>

                <Form.Group>
                  <Form.ControlLabel>
                    <h4 className="mb-4">Detail Granulasi</h4>
                  </Form.ControlLabel>
                  <Button
                    appearance="primary"
                    startIcon={<ExpandOutlineIcon />}
                    onClick={() => setModalGranulasi(true)}
                  >
                    Tambah Detail Granulasi
                  </Button>
                </Form.Group>

                <Panel bordered className="mb-3">
                  {granulasiDetail.length > 0 ? (
                    <>
                      <h4 className="mb-4">New Details</h4>
                      {granulasiDetail?.map((detail, index) => (
                        <div key={`Granulasi-${index}`}>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div>
                              <p>Matrix: {detail.matrix_label}</p>
                              <p>Value: {detail.value}</p>
                            </div>
                            <div style={{ display: "flex", gap: "8px" }}>
                              <Button
                                appearance="ghost"
                                color="blue"
                                size="xs"
                                onClick={() =>
                                  handleEditNewDetail(index, "Granulasi")
                                }
                              >
                                Edit
                              </Button>
                              <Button
                                appearance="ghost"
                                color="red"
                                size="xs"
                                onClick={() => handleRemoveNewDetail(index)}
                              >
                                Remove
                              </Button>
                            </div>
                          </div>
                          {index < granulasiDetail.length - 1 && (
                            <Divider style={{ margin: "10px 0" }} />
                          )}
                        </div>
                      ))}
                    </>
                  ) : (
                    <p>No New Details Added</p>
                  )}
                </Panel>

                <Panel bordered>
                  {dataDetail?.detail_trans1 ? (
                    <>
                      <h4 className="mb-4">Inserted Details</h4>
                      {dataDetail.detail_trans1
                        .filter(
                          (detail) => detail.main_category_name === currentPanel
                        )
                        ?.map((detail, index) => (
                          <div key={`existing-${index}`}>
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                              }}
                            >
                              <div>
                                <p>Parameter: {detail.parameter_name}</p>
                                <p>Value: {detail.value}</p>
                              </div>
                              <div style={{ display: "flex", gap: "8px" }}>
                                {detail.is_active === 1 ? (
                                  <>
                                    <Button
                                      appearance="ghost"
                                      color="blue"
                                      size="xs"
                                      onClick={() =>
                                        handleEditExistingDetail(
                                          detail,
                                          "existing"
                                        )
                                      }
                                    >
                                      Edit
                                    </Button>
                                    <Button
                                      appearance="ghost"
                                      color="red"
                                      size="xs"
                                      onClick={() =>
                                        handleDeactiveExistingDetail(
                                          detail,
                                          "existing"
                                        )
                                      }
                                    >
                                      Deactivate
                                    </Button>
                                  </>
                                ) : (
                                  <Button
                                    appearance="ghost"
                                    color="green"
                                    size="xs"
                                    onClick={() =>
                                      handleReactivateExistingDetail(
                                        detail,
                                        "existing"
                                      )
                                    }
                                  >
                                    Reactivate
                                  </Button>
                                )}
                              </div>
                            </div>
                            {index <
                              dataDetail.detail_trans1.filter(
                                (detail) =>
                                  detail.main_category_name === currentPanel
                              ).length -
                                1 && <Divider style={{ margin: "10px 0" }} />}
                          </div>
                        ))}
                    </>
                  ) : (
                    <p>Loading Data...</p>
                  )}
                </Panel>

                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("Binder")}
                  >
                    Kembali ke Tahap Binder
                  </Button>
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("Pengeringan")}
                  >
                    Lanjut Tahap Pengeringan
                  </Button>
                </Stack>
                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}

            {currentPanel === "Pengeringan" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between">
                  <h4 className="mb-4">Pengeringan</h4>
                </Stack>
                <Form.Group controlId="drying_date">
                  <Form.ControlLabel>Tanggal Pengeringan</Form.ControlLabel>
                  <DatePicker
                    oneTap
                    value={
                      dataDetail.drying_date
                        ? new Date(dataDetail.drying_date)
                        : null
                    }
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, drying_date: value });
                    }}
                    block
                  />
                </Form.Group>

                <Form.Group controlId="drying_lod">
                  <Form.ControlLabel>LOD</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="drying_lod"
                      value={dataDetail.drying_lod}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          drying_lod: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> %</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="drying_product_temp">
                  <Form.ControlLabel>Product Temp</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="drying_product_temp"
                      value={dataDetail.drying_product_temp}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          drying_product_temp: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> C</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="drying_exhaust_temp">
                  <Form.ControlLabel>Exhaust Temp</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="drying_exhaust_temp"
                      value={dataDetail.drying_exhaust_temp}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          drying_exhaust_temp: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> C</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="drying_remarks">
                  <Form.ControlLabel>Pengeringan Remarks</Form.ControlLabel>
                  <Form.Control
                    placeholder="drying_remarks"
                    value={dataDetail.drying_remarks}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, drying_remarks: value });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Lampiran Pengeringan<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Uploader
                    action=""
                    listType="picture-text"
                    fileList={[
                      ...(handleGetAttachmentsByType("Pengeringan") || []),
                      ...getPanelImages("Pengeringan"),
                    ]}
                    fileListVisible={false}
                    appearance="ghost"
                    onChange={(fileList) => {
                      const existingFiles = [
                        ...(handleGetAttachmentsByType("Pengeringan") || []),
                        ...getPanelImages("Pengeringan"),
                      ];

                      const newFiles = fileList
                        .filter(
                          (file) =>
                            !existingFiles.some(
                              (existingFile) => existingFile.name === file.name
                            )
                        )
                        ?.map((file) => ({
                          ...file,
                          currentPanel: "Pengeringan",
                          url: file.blobFile
                            ? URL.createObjectURL(file.blobFile)
                            : file.url,
                        }));

                      if (newFiles.length > 0) {
                        updatePanelImages("Pengeringan", [
                          ...getPanelImages("Pengeringan"),
                          ...newFiles,
                        ]);
                      }
                    }}
                  >
                    <Button>Ambil Gambar</Button>
                  </Uploader>

                  <Row>
                    {[
                      ...(handleGetAttachmentsByType("Pengeringan") || []),
                      ...getPanelImages("Pengeringan"),
                    ]?.map((attachment, index) => (
                      <Col md={5} sm={12} key={index}>
                        <AttachmentDisplay
                          attachment={attachment}
                          onRemove={() => handleRemoveAttachments(attachment)}
                          onUpdateStatus={handleUpdateAttachmentStatus}
                        />
                      </Col>
                    ))}
                  </Row>
                </Form.Group>

                <Divider></Divider>

                <Form.Group>
                  <Form.ControlLabel>
                    <h4 className="mb-4">Detail Pengeringan</h4>
                  </Form.ControlLabel>
                  <Button
                    appearance="primary"
                    startIcon={<ExpandOutlineIcon />}
                    onClick={() => setModalPengeringan(true)}
                  >
                    Tambah Detail Pengeringan
                  </Button>
                </Form.Group>

                <Panel bordered className="mb-3">
                  {pengeringanDetail.length > 0 ? (
                    <>
                      <h4 className="mb-4">New Details</h4>
                      {pengeringanDetail?.map((detail, index) => (
                        <div key={`Pengeringan-${index}`}>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div>
                              <p>Matrix: {detail.matrix_label}</p>
                              <p>Value: {detail.value}</p>
                            </div>
                            <div style={{ display: "flex", gap: "8px" }}>
                              <Button
                                appearance="ghost"
                                color="blue"
                                size="xs"
                                onClick={() =>
                                  handleEditNewDetail(index, "Pengeringan")
                                }
                              >
                                Edit
                              </Button>
                              <Button
                                appearance="ghost"
                                color="red"
                                size="xs"
                                onClick={() => handleRemoveNewDetail(index)}
                              >
                                Remove
                              </Button>
                            </div>
                          </div>
                          {index < pengeringanDetail.length - 1 && (
                            <Divider style={{ margin: "10px 0" }} />
                          )}
                        </div>
                      ))}
                    </>
                  ) : (
                    <p>No New Details Added</p>
                  )}
                </Panel>

                <Panel bordered>
                  {dataDetail?.detail_trans1 ? (
                    <>
                      <h4 className="mb-4">Inserted Details</h4>
                      {dataDetail.detail_trans1
                        .filter(
                          (detail) => detail.main_category_name === currentPanel
                        )
                        ?.map((detail, index) => (
                          <div key={`existing-${index}`}>
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                              }}
                            >
                              <div>
                                <p>Parameter: {detail.parameter_name}</p>
                                <p>Value: {detail.value}</p>
                              </div>
                              <div style={{ display: "flex", gap: "8px" }}>
                                {detail.is_active === 1 ? (
                                  <>
                                    <Button
                                      appearance="ghost"
                                      color="blue"
                                      size="xs"
                                      onClick={() =>
                                        handleEditExistingDetail(
                                          detail,
                                          "existing"
                                        )
                                      }
                                    >
                                      Edit
                                    </Button>
                                    <Button
                                      appearance="ghost"
                                      color="red"
                                      size="xs"
                                      onClick={() =>
                                        handleDeactiveExistingDetail(
                                          detail,
                                          "existing"
                                        )
                                      }
                                    >
                                      Deactivate
                                    </Button>
                                  </>
                                ) : (
                                  <Button
                                    appearance="ghost"
                                    color="green"
                                    size="xs"
                                    onClick={() =>
                                      handleReactivateExistingDetail(
                                        detail,
                                        "existing"
                                      )
                                    }
                                  >
                                    Reactivate
                                  </Button>
                                )}
                              </div>
                            </div>
                            {index <
                              dataDetail.detail_trans1.filter(
                                (detail) =>
                                  detail.main_category_name === currentPanel
                              ).length -
                                1 && <Divider style={{ margin: "10px 0" }} />}
                          </div>
                        ))}
                    </>
                  ) : (
                    <p>Loading Data...</p>
                  )}
                </Panel>

                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("Granulasi")}
                  >
                    Kembali ke Tahap Granulasi
                  </Button>
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("Pengayakan")}
                  >
                    Lanjut Tahap Pengayakan
                  </Button>
                </Stack>
                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}

            {currentPanel === "Pengayakan" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between">
                  <h4 className="mb-4">Pengayakan</h4>
                </Stack>
                <Form.Group controlId="sifting_date">
                  <Form.ControlLabel>Tanggal Pengayakan</Form.ControlLabel>
                  <DatePicker
                    oneTap
                    value={
                      dataDetail.sifting_date
                        ? new Date(dataDetail.sifting_date)
                        : null
                    }
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, sifting_date: value });
                    }}
                    block
                  />
                </Form.Group>

                <Form.Group controlId="sifting_screen_quadro">
                  <Form.ControlLabel>Screen Quadro</Form.ControlLabel>
                  <Form.Control
                    name="sifting_screen_quadro"
                    accepter={SelectPicker}
                    value={dataDetail.sifting_screen_quadro}
                    data={dataScreenQuadro}
                    valueKey="value"
                    labelKey="value"
                    block
                    onChange={(value) => {
                      setDataDetail({
                        ...dataDetail,
                        sifting_screen_quadro: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="sifting_bin_tumbler">
                  <Form.ControlLabel>Bin Tumbler</Form.ControlLabel>
                  <Form.Control
                    name="sifting_bin_tumbler"
                    accepter={SelectPicker}
                    value={dataDetail.sifting_bin_tumbler}
                    data={dataBinTumbler}
                    valueKey="value"
                    labelKey="value"
                    block
                    onChange={(value) => {
                      setDataDetail({
                        ...dataDetail,
                        sifting_bin_tumbler: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="sifting_impeller_speed_1">
                  <Form.ControlLabel>Impeller Speed Quadro 1</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="sifting_impeller_speed_1"
                      value={dataDetail.sifting_impeller_speed_1}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          sifting_impeller_speed_1: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> RPM</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="sifting_impeller_speed_2">
                  <Form.ControlLabel>Impeller Speed Quadro 2</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="sifting_impeller_speed_2"
                      value={dataDetail.sifting_impeller_speed_2}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          sifting_impeller_speed_2: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> RPM</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="sifting_remarks">
                  <Form.ControlLabel>Pengayakan Remarks</Form.ControlLabel>
                  <Form.Control
                    placeholder="sifting_remarks"
                    value={dataDetail.sifting_remarks}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, sifting_remarks: value });
                    }}
                  />
                </Form.Group>

                <Divider></Divider>

                <Form.Group>
                  <Form.ControlLabel>
                    <h4 className="mb-4">Detail Pengayakan</h4>
                  </Form.ControlLabel>
                  <Button
                    appearance="primary"
                    startIcon={<ExpandOutlineIcon />}
                    onClick={() => setModalPengayakan(true)}
                  >
                    Tambah Detail Pengayakan
                  </Button>
                </Form.Group>

                <Panel bordered className="mb-3">
                  {pengayakanDetail.length > 0 ? (
                    <>
                      <h4 className="mb-4">New Details</h4>
                      {pengayakanDetail?.map((detail, index) => (
                        <div key={`Pengayakan-${index}`}>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div>
                              <p>Matrix: {detail.matrix_label}</p>
                              <p>Value: {detail.value}</p>
                            </div>
                            <div style={{ display: "flex", gap: "8px" }}>
                              <Button
                                appearance="ghost"
                                color="blue"
                                size="xs"
                                onClick={() =>
                                  handleEditNewDetail(index, "Pengayakan")
                                }
                              >
                                Edit
                              </Button>
                              <Button
                                appearance="ghost"
                                color="red"
                                size="xs"
                                onClick={() => handleRemoveNewDetail(index)}
                              >
                                Remove
                              </Button>
                            </div>
                          </div>
                          {index < pengayakanDetail.length - 1 && (
                            <Divider style={{ margin: "10px 0" }} />
                          )}
                        </div>
                      ))}
                    </>
                  ) : (
                    <p>No New Details Added</p>
                  )}
                </Panel>

                <Panel bordered>
                  {dataDetail?.detail_trans1 ? (
                    <>
                      <h4 className="mb-4">Inserted Details</h4>
                      {dataDetail.detail_trans1
                        .filter(
                          (detail) => detail.main_category_name === currentPanel
                        )
                        ?.map((detail, index) => (
                          <div key={`existing-${index}`}>
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                              }}
                            >
                              <div>
                                <p>Parameter: {detail.parameter_name}</p>
                                <p>Value: {detail.value}</p>
                              </div>
                              <div style={{ display: "flex", gap: "8px" }}>
                                {detail.is_active === 1 ? (
                                  <>
                                    <Button
                                      appearance="ghost"
                                      color="blue"
                                      size="xs"
                                      onClick={() =>
                                        handleEditExistingDetail(
                                          detail,
                                          "existing"
                                        )
                                      }
                                    >
                                      Edit
                                    </Button>
                                    <Button
                                      appearance="ghost"
                                      color="red"
                                      size="xs"
                                      onClick={() =>
                                        handleDeactiveExistingDetail(
                                          detail,
                                          "existing"
                                        )
                                      }
                                    >
                                      Deactivate
                                    </Button>
                                  </>
                                ) : (
                                  <Button
                                    appearance="ghost"
                                    color="green"
                                    size="xs"
                                    onClick={() =>
                                      handleReactivateExistingDetail(
                                        detail,
                                        "existing"
                                      )
                                    }
                                  >
                                    Reactivate
                                  </Button>
                                )}
                              </div>
                            </div>
                            {index <
                              dataDetail.detail_trans1.filter(
                                (detail) =>
                                  detail.main_category_name === currentPanel
                              ).length -
                                1 && <Divider style={{ margin: "10px 0" }} />}
                          </div>
                        ))}
                    </>
                  ) : (
                    <p>Loading Data...</p>
                  )}
                </Panel>

                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("Pengeringan")}
                  >
                    Kembali ke Tahap Pengeringan
                  </Button>
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("finalmix")}
                  >
                    Lanjut Tahap Final Mix
                  </Button>
                </Stack>
                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}

            {currentPanel === "finalmix" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between">
                  <h4 className="mb-4">Final Mix</h4>
                </Stack>
                <Form.Group controlId="final_mix_date">
                  <Form.ControlLabel>Tanggal Final Mix</Form.ControlLabel>
                  <DatePicker
                    oneTap
                    value={
                      dataDetail.final_mix_date
                        ? new Date(dataDetail.final_mix_date)
                        : null
                    }
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, final_mix_date: value });
                    }}
                    block
                  />
                </Form.Group>

                <Form.Group controlId="final_mix_time_mix_1">
                  <Form.ControlLabel>Waktu Aduk 1</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="final_mix_time_mix_1"
                      value={dataDetail.final_mix_time_mix_1}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          final_mix_time_mix_1: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> menit</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="final_mix_time_mix_2">
                  <Form.ControlLabel>Waktu Aduk 2</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="final_mix_time_mix_2"
                      value={dataDetail.final_mix_time_mix_2}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          final_mix_time_mix_2: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> menit</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="ts_conclusion">
                  <Form.ControlLabel>Kesimpulan</Form.ControlLabel>
                  <Form.Control
                    placeholder="ts_conclusion"
                    value={dataDetail.ts_conclusion}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, ts_conclusion: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="ts_followup">
                  <Form.ControlLabel>Tindak Lanjut</Form.ControlLabel>
                  <Form.Control
                    placeholder="ts_followup"
                    value={dataDetail.ts_followup}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, ts_followup: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="bobot_granul">
                  <Form.ControlLabel>Bobot Granul</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="bobot_granul"
                      value={dataDetail.bobot_granul}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          bobot_granul: parseFloat(value),
                          rendemen:
                            (parseFloat(value) / dataDetail.bobot_teoritis) *
                            100,
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> kg</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="bobot_teoritis">
                  <Form.ControlLabel>Bobot Teoritis</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="bobot_teoritis"
                      value={dataDetail.bobot_teoritis}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          bobot_teoritis: parseFloat(value),
                          rendemen:
                            (dataDetail.bobot_granul / parseFloat(value)) * 100,
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> kg</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group controlId="rendemen">
                  <Form.ControlLabel>Rendemen</Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      placeholder="rendemen"
                      value={dataDetail.rendemen}
                      onChange={(value) => {
                        setDataDetail({
                          ...dataDetail,
                          rendemen: parseFloat(value),
                        });
                      }}
                      readOnly
                      disabled
                      type="number"
                    />
                    <InputGroup.Addon> %</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>
                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("Pengayakan")}
                  >
                    Kembali ke Tahap Pengayakan
                  </Button>
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("analisadata")}
                  >
                    Lanjut Tahap Analisa Data
                  </Button>
                </Stack>
                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}

            {currentPanel === "analisadata" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between">
                  <h4 className="mb-4">Analisa Data</h4>
                </Stack>

                <Form.Group controlId="discussion">
                  <Form.ControlLabel>Diskusi</Form.ControlLabel>
                  <Form.Control
                    placeholder="discussion"
                    value={dataDetail.discussion}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, discussion: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="analyzed_data">
                  <Form.ControlLabel>Analisa Data</Form.ControlLabel>
                  <Form.Control
                    placeholder="analyzed_data"
                    value={dataDetail.analyzed_data}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, analyzed_data: value });
                    }}
                  />
                </Form.Group>

                <Form.Group controlId="spv_employee_id">
                  <Form.ControlLabel>
                    Spv<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="spv_employee_id"
                    accepter={SelectPicker}
                    value={dataDetail.spv_employee_id}
                    data={dataSpv}
                    valueKey="employee_id"
                    labelKey="employee_name"
                    block
                    disabled={Object.entries(dataDetail).some(
                      ([key, value]) =>
                        key !== "spv_employee_id" &&
                        key !== "ts_keyword" &&
                        key !== "mgr_employee_id" &&
                        key !== "mgr_approved_dt" &&
                        key !== "spv_approved_dt" &&
                        key !== "attachments1" &&
                        key !== "spv_name" &&
                        key !== "mgr_name" &&
                        key !== "link_remarks" &&
                        (value === null || value === undefined || value === "") 
                        // &&
                        // ["granulasiDetail", "pengeringanDetail", "pengayakanDetail"].some(
                        //   (detailKey) =>
                        //     dataDetail[detailKey] === null ||
                        //     dataDetail[detailKey] === undefined ||
                        //     dataDetail[detailKey] === ""
                        // )
                    )}                    
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, spv_employee_id: value });
                    }}
                  />
                </Form.Group>

                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("finalmix")}
                  >
                    Kembali ke Tahap Final Mix
                  </Button>
                  <Button
                    appearance="primary"
                    style={{ backgroundColor: "#1fd306" }}
                    onClick={handleEditManualApi}
                  >
                    Save
                  </Button>
                </Stack>
              </Panel>
            )}

            {/* Modal Granulasi */}
            <Modal
              open={modalGranulasi}
              onClose={() => {
                setModalGranulasi(false);
                setFormDetailValue(emptyDetailForm);
              }}
            >
              <Modal.Header>
                <Modal.Title>Detail Granulasi</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Matrix</Form.ControlLabel>
                    <Form.Control
                      name="id_matrix1"
                      accepter={SelectPicker}
                      data={tsMatrixCategories.filter(
                        (item) => item.main_category_name === "Granulasi"
                      )}
                      valueKey="value"
                      labelKey="label"
                      block
                      value={formDetailValue.id_matrix1 || ""}
                      onChange={(value) => {
                        const selectedMatrix = tsMatrixCategories.find(
                          (item) => item.value === value
                        );

                        setFormDetailValue({
                          ...formDetailValue,
                          id_matrix1: value,
                          id_categories1: selectedMatrix
                            ? selectedMatrix.id_categories1
                            : null,
                        });
                      }}
                    />
                  </Form.Group>

                  <Form.Group>
                    <Form.ControlLabel>Value</Form.ControlLabel>
                    <Form.Control
                      name="value1"
                      value={formDetailValue.value1 || ""}
                      onChange={(value) =>
                        setFormDetailValue({
                          ...formDetailValue,
                          value1: value,
                        })
                      }
                    />
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  appearance="primary"
                  disabled={!formDetailValue.id_matrix1}
                  onClick={() => {
                    if (editIndex !== null) {
                      if (isEditingNewDetail) {
                        const updatedDetails = [...granulasiDetail];
                        const selectedMatrix = tsMatrixCategories.find(
                          (item) => item.value === formDetailValue.id_matrix1
                        );

                        updatedDetails[editIndex] = {
                          ...updatedDetails[editIndex],
                          id_matrix: formDetailValue.id_matrix1,
                          id_categories: selectedMatrix?.id_categories,
                          parameter_name: selectedMatrix?.label,
                          value: formDetailValue.value1,
                        };
                        setGranulasiDetail(updatedDetails);
                      } else {
                        const updatedDetails = [...dataDetail.detail_trans1];
                        const selectedMatrix = tsMatrixCategories.find(
                          (item) => item.value === formDetailValue.id_matrix1
                        );

                        updatedDetails[editIndex] = {
                          ...updatedDetails[editIndex],
                          id_matrix: formDetailValue.id_matrix1,
                          id_categories: selectedMatrix?.id_categories,
                          parameter_name: selectedMatrix?.label,
                          value: formDetailValue.value1,
                        };

                        setDataDetail((prev) => ({
                          ...prev,
                          detail_trans1: updatedDetails,
                          ts_details1_old: updatedDetails,
                        }));
                      }

                      setEditIndex(null);
                    } else {
                      handleDetailGranulasi();
                    }

                    setModalGranulasi(false);
                    setFormDetailValue(emptyDetailForm);
                  }}
                >
                  Save
                </Button>
              </Modal.Footer>
            </Modal>

            {/* Modal Pengeringan */}
            <Modal
              open={modalPengeringan}
              onClose={() => {
                setModalPengeringan(false);
                setFormDetailValue(emptyDetailForm);
              }}
            >
              <Modal.Header>
                <Modal.Title>Detail Pengeringan</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Matrix</Form.ControlLabel>
                    <Form.Control
                      name="id_matrix2"
                      accepter={SelectPicker}
                      data={tsMatrixCategories.filter(
                        (item) => item.main_category_name === "Pengeringan"
                      )}
                      valueKey="value"
                      labelKey="label"
                      block
                      value={formDetailValue.id_matrix2 || ""}
                      onChange={(value) => {
                        const selectedMatrix = tsMatrixCategories.find(
                          (item) => item.value === value
                        );

                        setFormDetailValue({
                          ...formDetailValue,
                          id_matrix2: value,
                          id_categories2: selectedMatrix
                            ? selectedMatrix.id_categories2
                            : null,
                        });
                      }}
                    />
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Value</Form.ControlLabel>
                    <Form.Control
                      name="value2"
                      value={formDetailValue.value2 || ""}
                      onChange={(value) =>
                        setFormDetailValue({
                          ...formDetailValue,
                          value2: value,
                        })
                      }
                    />
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  appearance="primary"
                  disabled={!formDetailValue.id_matrix2}
                  onClick={() => {
                    if (editIndex !== null) {
                      if (isEditingNewDetail) {
                        const updatedDetails = [...pengeringanDetail];
                        const selectedMatrix = tsMatrixCategories.find(
                          (item) => item.value === formDetailValue.id_matrix2
                        );

                        updatedDetails[editIndex] = {
                          ...updatedDetails[editIndex],
                          id_matrix: formDetailValue.id_matrix2,
                          id_categories: selectedMatrix?.id_categories,
                          parameter_name: selectedMatrix?.label,
                          value: formDetailValue.value2,
                        };
                        setPengeringanDetail(updatedDetails);
                      } else {
                        const updatedDetails = [...dataDetail.detail_trans1];
                        const selectedMatrix = tsMatrixCategories.find(
                          (item) => item.value === formDetailValue.id_matrix2
                        );

                        updatedDetails[editIndex] = {
                          ...updatedDetails[editIndex],
                          id_matrix: formDetailValue.id_matrix2,
                          id_categories: selectedMatrix?.id_categories,
                          parameter_name: selectedMatrix?.label,
                          value: formDetailValue.value2,
                        };

                        setDataDetail((prev) => ({
                          ...prev,
                          detail_trans1: updatedDetails,
                          ts_details1_old: updatedDetails,
                        }));
                      }

                      setEditIndex(null);
                    } else {
                      handleDetailPengeringan();
                    }

                    setModalPengeringan(false);
                    setFormDetailValue(emptyDetailForm);
                  }}
                >
                  Save
                </Button>
              </Modal.Footer>
            </Modal>

            {/* Modal Pengayakan */}
            <Modal
              open={modalPengayakan}
              onClose={() => {
                setModalPengayakan(false);
                setFormDetailValue(emptyDetailForm);
              }}
            >
              <Modal.Header>
                <Modal.Title>Detail Pengayakan</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Matrix</Form.ControlLabel>
                    <Form.Control
                      name="id_matrix3"
                      accepter={SelectPicker}
                      data={tsMatrixCategories.filter(
                        (item) => item.main_category_name === "Pengayakan"
                      )}
                      valueKey="value"
                      labelKey="label"
                      block
                      value={formDetailValue.id_matrix3 || ""}
                      onChange={(value) => {
                        const selectedMatrix = tsMatrixCategories.find(
                          (item) => item.value === value
                        );

                        setFormDetailValue({
                          ...formDetailValue,
                          id_matrix3: value,
                          id_categories3: selectedMatrix
                            ? selectedMatrix.id_categories3
                            : null,
                        });
                      }}
                    />
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Value</Form.ControlLabel>
                    <Form.Control
                      name="value3"
                      value={formDetailValue.value3 || ""}
                      onChange={(value) =>
                        setFormDetailValue({
                          ...formDetailValue,
                          value3: value,
                        })
                      }
                    />
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  appearance="primary"
                  disabled={!formDetailValue.id_matrix3}
                  onClick={() => {
                    if (editIndex !== null) {
                      if (isEditingNewDetail) {
                        const updatedDetails = [...pengayakanDetail];
                        const selectedMatrix = tsMatrixCategories.find(
                          (item) => item.value === formDetailValue.id_matrix3
                        );

                        updatedDetails[editIndex] = {
                          ...updatedDetails[editIndex],
                          id_matrix: formDetailValue.id_matrix3,
                          id_categories: selectedMatrix?.id_categories,
                          parameter_name: selectedMatrix?.label,
                          value: formDetailValue.value3,
                        };
                        setPengayakanDetail(updatedDetails);
                      } else {
                        const updatedDetails = [...dataDetail.detail_trans1];
                        const selectedMatrix = tsMatrixCategories.find(
                          (item) => item.value === formDetailValue.id_matrix3
                        );

                        updatedDetails[editIndex] = {
                          ...updatedDetails[editIndex],
                          id_matrix: formDetailValue.id_matrix3,
                          id_categories: selectedMatrix?.id_categories,
                          parameter_name: selectedMatrix?.label,
                          value: formDetailValue.value3,
                        };

                        setDataDetail((prev) => ({
                          ...prev,
                          detail_trans1: updatedDetails,
                          ts_details1_old: updatedDetails,
                        }));
                      }

                      setEditIndex(null);
                    } else {
                      handleDetailPengayakan();
                    }

                    setModalPengayakan(false);
                    setFormDetailValue(emptyDetailForm);
                  }}
                >
                  Save
                </Button>
              </Modal.Footer>
            </Modal>
          </Form>
        </div>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps(context) {
  const { query } = context;
  const data = query.data || "";

  return {
    props: {
      data,
    },
  };
}
