import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_BASE_URL}/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};

export default function ApiTransactionDetail() {
    return {
        getAllTransactionDetail: createApiFunction("get", "pqr/transaction_d/list"),
        createTransactionDetail: createApiFunction("post", "pqr/transaction_d/create"),
        editTransactionDetail: createApiFunction("put", "pqr/transaction_d/edit"),
        editStatusTransactionDetail: createApiFunction("put", "pqr/transaction_d/edit-status"),
        getTransactionDetailById: createApiFunction("post", "pqr/transaction_d/ppi_byIdHeader"),
        getTransactionParamBindingDetailById: createApiFunction("post", "pqr/transaction_d/trans_d_byIdHeader"),
        getBindingParamValueFlagById: createApiFunction("post", "pqr/transaction_d/binding_byIdHeader"),
    }
}