import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](
    `${process.env.NEXT_PUBLIC_BASE_URL}/${url}`,
    data
  )
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiLineClearanceGranulasi() {
  return {
    getAllLineClear: createApiFunction(
      "get",
      "pqr/pqr_line_clearance_granulasi/list"
    ),
    getAllActiveLineClear: createApiFunction(
      "get",
      "pqr/pqr_line_clearance_granulasi/list-active"
    ),
    getAllNeedApproveLineClear: createApiFunction(
      "get",
      "pqr/pqr_line_clearance_granulasi/need-approve"
    ),
    getLineClearByID: createApiFunction(
      "post",
      "pqr/pqr_line_clearance_granulasi/id"
    ),
    postCreateLineClear: createApiFunction(
      "post",
      "pqr/pqr_line_clearance_granulasi/create"
    ),
    putDeleteStatusLineClear: createApiFunction(
      "put",
      "pqr/pqr_line_clearance_granulasi/delete"
    ),
    putUpdateLineClear: createApiFunction(
      "put",
      "pqr/pqr_line_clearance_granulasi/update"
    ),
  };
}
