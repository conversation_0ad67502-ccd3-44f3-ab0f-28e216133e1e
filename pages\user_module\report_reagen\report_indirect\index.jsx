import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  useToaster,
  SelectPicker,
  DatePicker,
  DateRangePicker,
  Loader,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import { useRouter } from "next/router";
import ApiOracleReagen from "@/pages/api/oracle/reagen/api_oracle_reagen";

export default function ReportIndirect() {
  const [moduleName, setModuleName] = useState("");
  const { HeaderCell, Cell, Column } = Table;
  const [indirectData, setIndirectData] = useState([]);
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const [props, setProps] = useState([]);
  const toaster = useToaster();
  const router = useRouter();
  const [dateRange, setDateRange] = useState([])
  
  const handleDateRange = async (dateFrom, dateTo) =>{
    if (dateFrom != '' && dateTo != '') {
      let yyyy = dateFrom.getFullYear();
      let mm = String(dateFrom.getMonth() + 1).padStart(2, '0'); // Months are zero-based, so add 1
      let dd = String(dateFrom.getDate()).padStart(2, '0');
      const dateFromFormated =  `${yyyy}-${mm}-${dd}`;
  
      yyyy = dateTo.getFullYear();
      mm = String(dateTo.getMonth() + 1).padStart(2, '0'); // Months are zero-based, so add 1
      dd = String(dateTo.getDate()).padStart(2, '0');
      const dateToFormated = `${yyyy}-${mm}-${dd}`;
  
      console.log("date from", dateFromFormated)
      console.log("date to", dateToFormated)
        const res = await ApiOracleReagen().getPoRecevie({
        "date_start": dateFromFormated,
        "date_end": dateToFormated
      });
      console.log(res);
      setIndirectData(res.data ? res.data : []);

      setDateRange([dateFromFormated, dateToFormated]);
    }else{
      hanldeInitialApi()
    }    
}

  
  const hanldeInitialApi = async () => {
    const currentYear = new Date().getFullYear();

    const startDate = `${currentYear}-01-01`;
    const endDate = `${currentYear}-12-31`; 
    const res = await ApiOracleReagen().getPoRecevie({
      "date_start": startDate,
      "date_end": endDate
    });
    console.log(res);
    setIndirectData(res.data ? res.data : []);
  };

  

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

 

  const filteredData = indirectData
    .filter((rowData, i) => {
    const searchFields = [
      "po_number",
      "item_code",
      "receipt_number",
      "supplier_name",
      "status",
    ];

    const matchesSearch = searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : indirectData.length;


  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("outbound_reagen/list_outbound_reagen")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      hanldeInitialApi();
    }
  }, []);

  const handleExportExcel = () => {
    if (indirectData.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }

   
    const headerMapping = {
      po_number:  "PO Number",
      item_code:  "Item Code",
      receipt_number:  "Receipt Number",
      supplier_name:  "Supplier Name",
      item_description:  "Item Description",
      po_creation_date:  "Po Creation Date",
      po_promised_date:  "Po Promised Date",
      aging: "Aging",
      receiving_date_text:  "Receiving Date_text",
      status_overdue: "Status Overdue",
      status:  "Status",
    };

    const formattedData = indirectData.map((item) => {
      const formattedItem = {};
      for (const key in item) {
        if (headerMapping[key]) {
          if (key === "is_active") {
            formattedItem[headerMapping[key]] =
              item[key] === 1 ? "Active" : "Inactive";
          } else if (key === "status") {
            formattedItem[headerMapping[key]] =
              item[key] === 0 ? "Not Realized" : "Realized";
          } else {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
      }
      return formattedItem;
    });

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const filename = `Indirect data${date}.xlsx`;

    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <div>
        <Head>
          <title>Report Indirect Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>{moduleName}</Breadcrumb.Item>
                  <Breadcrumb.Item>Report Reagen</Breadcrumb.Item>
                  <Breadcrumb.Item active>Report Indirect</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                <DateRangePicker                                    
                      onChange={(value) => {
                          // console.log('[daterange]',dateRange)
                          console.log('[0]',value?.[0])
                          console.log('[1]',value?.[1])
                          handleDateRange(value?.[0] || "",value?.[1] || "")
                      }}
                      block
                      />
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Po Number</HeaderCell>
                <Cell dataKey="po_number" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Item Code</HeaderCell>
                <Cell dataKey="item_code" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Receipt Number</HeaderCell>
                <Cell dataKey="receipt_number" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Supplier Name</HeaderCell>
                <Cell dataKey="supplier_name" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Item Description</HeaderCell>
                <Cell dataKey="item_description" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>PO Creation Date</HeaderCell>
                <Cell dataKey="po_creation_date" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>PO Promised Date</HeaderCell>
                <Cell dataKey="po_promised_date" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Receiving Date</HeaderCell>
                <Cell dataKey="receiving_date_text" />
              </Column>
              <Column width={130} fixed="right" align="center">
              <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.status_overdue === 2
                        ? "red"
                        : rowData.status_overdue === 1
                        ? "orange"
                        : "green",
                      }}
                    >
                      {rowData.status_overdue === 2 
                      ? `${(-1 * rowData.aging)} days overdue}` 
                      : rowData.status_overdue === 1
                      ? `Wait for Receive`
                      : `Fulfilled`}
                    </span>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>
    </>
  );
}
