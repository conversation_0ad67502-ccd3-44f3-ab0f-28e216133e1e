import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](
    `${process.env.NEXT_PUBLIC_REAGEN}/ts/${url}`,
    data
  )
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiTsdpAuto() {
  return {
    postCreateTransactionAutoDetail: createApiFunction(
      "post",
      "transaction-d-auto/create"
    ),
    getAllTransactionAutoDetail: createApiFunction(
      "get",
      "transaction-d-auto/list"
    ),
  };
}
