import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/ipc/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function APIIPCProductScale(){
    return{
        getAllDetailProductScale: createApiFunction("post", "ipc_scale/product/get-all"),
        getDetailProductScale: createApiFunction("post", "ipc_scale/product/get-detail"),
        createProductScale: createApiFunction("post", "ipc_scale/product/create"),
        updateProductScale: createApiFunction("post", "ipc_scale/product/update"),
        deleteProductScale: createApiFunction("post", "ipc_scale/product/delete"),
        getEntryData : createApiFunction("post", "ipc_scale/measurement/entry-scale"),
        getEntryDataMYSQL : createApiFunction("post", "ipc_scale/measurement/mysql/entry-scale"),
        EntryHeaderData : createApiFunction("post", "ipc_scale/measurement/entry-scale-header"),
        EntryDetailData : createApiFunction("post", "ipc_scale/measurement/entry-scale-detail"),
        clearStaging : createApiFunction("put", "ipc_scale/measurement/entry-scale-clear"),
        EntryDetailDataMYSQL : createApiFunction("post", "ipc_scale/measurement/mysql/entry-scale-detail"),
        clearStagingMYSQL : createApiFunction("put", "ipc_scale/measurement/mysql/entry-scale-clear"),
        getApproval : createApiFunction("get", "approval/need-approve-scale"),
        getApprovalDate : createApiFunction("post", "approval/need-approve-scale-date"),
        getApprovalDetail : createApiFunction("post", "approval/need-approve-detail-scale"),
        putApprove : createApiFunction("put", "approval/approve-scale"),
        getReasonScale : createApiFunction("get", "ipc_reason/scale/get-all-active"),
    }
}