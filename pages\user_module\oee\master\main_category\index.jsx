import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, Notification, SelectPicker } from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiMain from "@/pages/api/oee/main/api_main";
import { useRouter } from "next/router";

export default function MasterDataMainCategoryPage() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_main_category");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [masterMainCategoryData, setMasterMainCategoryData] = useState([]);

  const emptyAddMasterMainCategoryForm = {
    category_name: "",
    category_type: "",
  };

  const emptyEditMasterMainCategoryForm = {
    category_name: "",
    category_type: "",
  };

  // Updated Category Type Options
  const [categoryTypeOptions] = useState([
    { label: "Planned (P)", value: "P" },
    { label: "Unplanned (U)", value: "U" },
  ]);

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [addLoading, setAddLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(null);

  const [addCategoryForm, setAddCategoryForm] = useState(emptyAddMasterMainCategoryForm);
  const [editCategoryForm, setEditCategoryForm] = useState(emptyEditMasterMainCategoryForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = masterMainCategoryData.filter((rowData) => {
    const searchFields = ["id_main_category", "category_name", "category_type", "create_date", "create_by", "update_date", "update_by", "delete_date", "delete_by", "is_active"];

    const matchesSearch = searchFields.some((field) => {
      const fieldValue = rowData[field];
      return fieldValue && fieldValue.toString().toLowerCase().includes(searchKeyword.toLowerCase());
    });

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return [...filteredData].sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.toLowerCase();
          y = y.toLowerCase();
        }
        if (sortType === "asc") {
          return x > y ? 1 : x < y ? -1 : 0;
        } else {
          return x < y ? 1 : x > y ? -1 : 0;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : masterMainCategoryData.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("oee/master/main_category"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      //get data disini
      HandleGetAllMasterMainCategory();
    }
  }, []);

  const HandleGetAllMasterMainCategory = async () => {
    try {
      const res = await ApiMain().getAllMain();

      console.log("res", res);
      if (res.status === 200) {
        setMasterMainCategoryData(res.data);
      } else {
        console.log("Error fetching main categories:", res.message);
      }
    } catch (error) {
      console.log("Error on catch Get All Api", error);
    }
  };

  const HandleAddMasterMainCategory = async () => {
    const errors = {};

    // Validate fields
    if (!addCategoryForm.category_name || addCategoryForm.category_name.trim() === "") {
      errors.category_name = "Category name is required";
    }
    if (!addCategoryForm.category_type) {
      errors.category_type = "Category type is required";
    }

    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }

    try {
      setAddLoading(true);
      const res = await ApiMain().createMain({
        ...addCategoryForm,
        create_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
      });

      if (res.status === 201 || res.status === 200) {
        setAddCategoryForm(emptyAddMasterMainCategoryForm);
        setShowAddModal(false);
        await HandleGetAllMasterMainCategory();
        const newId = res.id_main_category || res.data.id_main_category;
        router.push(`/user_module/oee/masterdata/main_category/detail?Id=${newId}`);
      }
    } catch (error) {
      console.error("Error adding main category:", error);
    } finally {
      setAddLoading(false);
    }
  };

  const HandleEditMasterMainCategory = async () => {
    const errors = {};

    // Validate fields
    if (!editCategoryForm.category_name || editCategoryForm.category_name.trim() === "") {
      errors.category_name = "Category name is required";
    }
    if (!editCategoryForm.category_type) {
      errors.category_type = "Category type is required";
    }

    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);
      return;
    }

    try {
      setEditLoading(true);
      const res = await ApiMain().editMain({
        ...editCategoryForm,
        update_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
      });

      if (res.status === 200) {
        await HandleGetAllMasterMainCategory();
        setShowEditModal(false);
      }
    } catch (error) {
      console.error("Error updating main category:", error);
    } finally {
      setEditLoading(false);
    }
  };

  const HandleEditStatusMasterMainCategory = async (id_main_category, is_active) => {
    try {
      const res = await ApiMain().editStatusMain({
        id_main_category,
        is_active,
        delete_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
      });

      if (res.status === 200) {
        console.log("Status update success:", res.message);
        await HandleGetAllMasterMainCategory();
      } else {
        console.log("Error on update status: ", res.message);
      }
    } catch (error) {
      console.log("Error on update status: ", error.message);
    }
  };

  return (
    <div>
      <Head>
        <title>Master Main Category Page</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <Breadcrumb>
                <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                <Breadcrumb.Item>Production</Breadcrumb.Item>
                <Breadcrumb.Item>OEE</Breadcrumb.Item>
                <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                <Breadcrumb.Item active>Main Category</Breadcrumb.Item>
              </Breadcrumb>
            </Stack.Item>
            <Stack.Item>
              <Tag color="green">Module: {moduleName}</Tag>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Halaman Master Main Category</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>

                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="Search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn} autoHeight>
                <Column width={150} align="center" sortable>
                  <HeaderCell>ID Main Category</HeaderCell>
                  <Cell dataKey="id_main_category" />
                </Column>
                <Column width={250} sortable>
                  <HeaderCell align="center">Category Name</HeaderCell>
                  <Cell dataKey="category_name" />
                </Column>
                <Column width={175} sortable align="center">
                  <HeaderCell>Category Type</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      const typeMap = {
                        P: "Planned",
                        U: "Unplanned",
                      };
                      return typeMap[rowData.category_type] || rowData.category_type;
                    }}
                  </Cell>
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Creation Date</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Created By</HeaderCell>
                  <Cell dataKey="create_by" />
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Update Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Updated By</HeaderCell>
                  <Cell dataKey="update_by" />
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Deletion Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Deleted By</HeaderCell>
                  <Cell dataKey="delete_by" />
                </Column>
                <Column width={120} sortable resizable align="center">
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Active" : "Inactive"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Actions</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModal(true);
                            setEditCategoryForm({
                              ...editCategoryForm,
                              category_name: rowData.category_name,
                              category_type: rowData.category_type,
                              id_main_category: rowData.id_main_category,
                              updated_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
                            });
                          }}
                        >
                          <EditIcon />
                        </Button>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            const Id = rowData.id_main_category;
                            router.push(`/user_module/oee/master/main_category/detail?Id=${Id}`);
                          }}
                        >
                          <SearchIcon style={{ fontSize: "16px" }} />
                        </Button>
                        <Button appearance="subtle" onClick={() => HandleEditStatusMasterMainCategory(rowData.id_main_category, rowData.is_active)} loading={statusLoading === rowData.id_main_category}>
                          {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            {/* Modal for adding main category */}
            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                if (!addLoading) {
                  setShowAddModal(false);
                  setAddCategoryForm(emptyAddMasterMainCategoryForm);
                  setErrorsAddForm({});
                }
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Add Main Category</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Category Name</Form.ControlLabel>
                    <Form.Control
                      name="category_name"
                      value={addCategoryForm.category_name}
                      onChange={(value) => {
                        setAddCategoryForm((prevFormValue) => ({
                          ...prevFormValue,
                          category_name: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          category_name: undefined,
                        }));
                      }}
                      disabled={addLoading}
                    />
                    {errorsAddForm.category_name && <p style={{ color: "red" }}>{errorsAddForm.category_name}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Category Type</Form.ControlLabel>
                    <SelectPicker
                      data={categoryTypeOptions}
                      value={addCategoryForm.category_type}
                      onChange={(value) => {
                        setAddCategoryForm((prevFormValue) => ({
                          ...prevFormValue,
                          category_type: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          category_type: undefined,
                        }));
                      }}
                      style={{ width: "100%" }}
                      placeholder="Select Type"
                      disabled={addLoading}
                    />
                    {errorsAddForm.category_type && <p style={{ color: "red" }}>{errorsAddForm.category_type}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddCategoryForm(emptyAddMasterMainCategoryForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle"
                  disabled={addLoading}
                >
                  Cancel
                </Button>
                <Button onClick={HandleAddMasterMainCategory} appearance="primary" loading={addLoading} disabled={addLoading}>
                  Add
                </Button>
              </Modal.Footer>
            </Modal>
            {/* Modal for editing main category */}
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditCategoryForm(emptyEditMasterMainCategoryForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Edit Main Category</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Category Name</Form.ControlLabel>
                    <Form.Control
                      name="category_name"
                      value={editCategoryForm.category_name}
                      onChange={(value) => {
                        setEditCategoryForm((prevFormValue) => ({
                          ...prevFormValue,
                          category_name: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          category_name: undefined,
                        }));
                      }}
                      disabled={editLoading}
                    />
                    {errorsEditForm.category_name && <p style={{ color: "red" }}>{errorsEditForm.category_name}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Category Type</Form.ControlLabel>
                    <SelectPicker
                      data={categoryTypeOptions}
                      value={editCategoryForm.category_type}
                      onChange={(value) => {
                        setEditCategoryForm((prevFormValue) => ({
                          ...prevFormValue,
                          category_type: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          category_type: undefined,
                        }));
                      }}
                      style={{ width: "100%" }}
                      placeholder="Select Type"
                      disabled={editLoading}
                    />
                    {errorsEditForm.category_type && <p style={{ color: "red" }}>{errorsEditForm.category_type}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditCategoryForm(emptyEditMasterMainCategoryForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                  disabled={editLoading}
                >
                  Cancel
                </Button>
                <Button onClick={HandleEditMasterMainCategory} appearance="primary" loading={editLoading} disabled={editLoading}>
                  Edit
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
