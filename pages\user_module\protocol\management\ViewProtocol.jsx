import { useRouter } from 'next/router';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import Head from 'next/head';
import MainContent from '@/components/layout/MainContent';
import { useState, useEffect } from 'react';
import {
    Form,
    Stack,
    Input,
    Panel,
    InputNumber,
    Dropdown,
} from 'rsuite';
import Editor from '@/components/Editor';
import ProtocolApi from '@/pages/api/protocolApi';
import Table from '@/components/Table';

export default function ViewProtocol({ noProtocol }) {
    const router = useRouter();
    let path = 'protocol/management/BrowseProtocol';
    let { data } = router.query;
    data ? (data = JSON.parse(data)) : data;
    const [breadcrumbsData, setBreadcrumbsData] = useState([]);
    const [cycleMonthData, setCycleMonthData] = useState([
        0, 3, 6, 9, 12, 18, 24, 36,
    ]);
    const [t30selected, setT30Selected] = useState([0]);
    const [t40selected, setT40Selected] = useState([0]);
    const [moduleName, setModuleName] = useState('');
    const [tujuan, setTujuan] = useState('');
    const [ruangLingkup, setRuangLingkup] = useState('');
    const [dokumenAcuan, setDokumenAcuan] = useState('');
    const [tanggungJawab, setTanggungJawab] = useState('');
    const [namaProduk, setNamaProduk] = useState('');
    const [kodeProduk, setKodeProduk] = useState('');
    const [nomorBatch, setNomorBatch] = useState('');
    const [nomorPPI, setNomorPPI] = useState('');
    const [batchSize, setBatchSize] = useState('');
    const [dibuatOleh, setDibuatOleh] = useState('');
    const [komposisiFormula, setKomposisiFormula] = useState('');
    const [additionalNotes, setAdditionalNotes] = useState('');
    const [approvalHistory, setApprovalHistory] = useState([]);
    const [protocolDetailData, setProtocolDetailData] = useState([]);
    const [title, setTitle]= useState('');
    const [batchNumber, setBatchNumber] = useState([])
    const {
        GetProtocolDataByNoProtocol,
    } = ProtocolApi();    

    // Handler awal checked
    const isChecked = (group, code) => {
        let result;
        if (group === 'T30') {
            for (let item of t30selected) {
                if (code === item) {
                    result = true;
                    break;
                } else {
                    result = false;
                }
            }
        } else {
            for (let item of t40selected) {
                if (code === item) {
                    result = true;
                    break;
                } else {
                    result = false;
                }
            }
        }
        return result;
    };

    const GetProtocolData = async (noProtocol, userData, userDept) => {
        const inputData = {
            no_protocol: noProtocol,
            employee_id: userData,
            department: parseInt(userDept)
        };

        const { data: protocolData } = await GetProtocolDataByNoProtocol(inputData);

        if (protocolData !== null && protocolData !== undefined) {
            // Set Header Data
            if (protocolData.Header_Data) {
                setTitle(protocolData.Header_Data[0].Title)
                setTujuan(protocolData.Header_Data[0].Desc_Purpose);
                setRuangLingkup(protocolData.Header_Data[0].Desc_Scope);
                setDokumenAcuan(protocolData.Header_Data[0].Desc_Document);
                setTanggungJawab(protocolData.Header_Data[0].Desc_Responsibilities);
                setNamaProduk(protocolData.Header_Data[0].Product_Name);
                setKodeProduk(protocolData.Header_Data[0].Product_Code);
                setNomorBatch(protocolData.Header_Data[0].Batch_No);
                setNomorPPI(protocolData.Header_Data[0].Ppi_No);
                setBatchSize(protocolData.Header_Data[0].Batch_Size);
                setDibuatOleh(protocolData.Header_Data[0].Manufactured_By);
                setKomposisiFormula(protocolData.Header_Data[0].Formula);
                setAdditionalNotes(protocolData.Header_Data[0].Additional_Notes);
            }

            // Set T30 TimeFrame Data
            let t30DataList = [];
            protocolData.T30Data.map((item) => {
                t30DataList.push(item.Cycle_Month);
            });

            // Set T40 TimeFrame Data
            let t40DataList = [];
            protocolData.T40Data.map((item) => {
                t40DataList.push(item.Cycle_Month);
            });

            setT30Selected(t30DataList);
            setT40Selected(t40DataList);

            // Set Detail Data
            setProtocolDetailData(protocolData.Detail_Data);

            // set approvalHistory data
            let approvalHistoryCombined = [];
            if (protocolData.ReviewerOrder?.length > 0 && protocolData.ReviewerOrder !== null) {
                const revOrder = protocolData.ReviewerOrder.map(item => {
                    // Set date 
                    let newDate = "";
                    if (item.Approval_Date != "") {
                        newDate = item.Approval_Date.substring(0, 10);
                    }

                    // Set status
                    let Status = ``;
                    if (item.Approval_Status == 0) {
                        Status = `Rejected`;
                    } else if (item.Approval_Status == 1) {
                        Status = `Approved`;
                    } else if (item.Approval_Status == 2) {
                        Status = `Waiting for approval`;
                    } else if (item.Approval_Status == 3) {
                        Status = `Waiting for others approval`;
                    } else if (item.Approval_Status == 4) {
                        Status = `Rejected by others`;
                    } else {
                        Status = `Had approved but rejected by others`;
                    }
                    const newReviewerData = { Status, Date: newDate, ...item };
                    return newReviewerData;
                });
                approvalHistoryCombined.push(...revOrder);
            }

            if (protocolData.ApproverOrder?.length > 0 && protocolData.ApproverOrder !== null) {
                const aprOrder = protocolData.ApproverOrder.map(item => {
                    // Set date 
                    let newDate = "";
                    if (item.Approval_Date != "") {
                        newDate = item.Approval_Date.substring(0, 10);
                    }

                    // Set status
                    let Status = ``;
                    if (item.Approval_Status == 0) {
                        Status = `Rejected`;
                    } else if (item.Approval_Status == 1) {
                        Status = `Approved`;
                    } else if (item.Approval_Status == 2) {
                        Status = `Waiting for approval`;
                    } else if (item.Approval_Status == 3) {
                        Status = `Waiting for others approval`;
                    } else if (item.Approval_Status == 4) {
                        Status = `Rejected by others`;
                    } else {
                        Status = `Had approved but rejected by others`;
                    }
                    const newApproverData = { Status, Date: newDate, ...item };
                    return newApproverData;
                });
                approvalHistoryCombined.push(...aprOrder);
            }
            setApprovalHistory(approvalHistoryCombined);

            //set batch number
            setBatchNumber(protocolData.BatchNumber_Data)
        }
    };

    useEffect(() => {
        const moduleNameValue = localStorage.getItem('module_name');
        const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
        if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
            router.push('/');
            return;
        }

        // validate access for this page
        if (!dataLogin.menu_link_code) {
            router.push('/dashboard');
            return;
        }
        const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
            item.includes(path),
        );
        if (validateUserAccess.length === 0) {
            router.push('/dashboard');
            return;
        }

        if (
            moduleNameValue === null ||
            moduleNameValue === undefined ||
            moduleNameValue === ''
        ) {
            router.push('/dashboard');
            return;
        }

        const asPathWithoutQuery = router.asPath.split('?')[0];
        const asPathNestedRoutes = asPathWithoutQuery
            .split('/')
            .filter((v) => v.length);
        let routerBreadCrumbsData = [
            moduleNameValue,
            asPathNestedRoutes[1],
            asPathNestedRoutes[2],
        ];
        const capitalizeFirstLetter = (str) => {
            return str.charAt(0).toUpperCase() + str.slice(1);
        };
        const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
            const words = item.split(/(?=[A-Z])/);
            return words.map((word) => capitalizeFirstLetter(word)).join(' ');
        });
        setBreadcrumbsData(breadCrumbsResult);

        setModuleName(moduleNameValue);

        GetProtocolData(noProtocol, dataLogin.employee_id, dataLogin.department);
    }, []);

    const t30ChangeHandler = (event) => {
        const isChecked = event.target.checked;
        const value = parseInt(event.target.value);

        if (isChecked) {
            setT30Selected((prevValue) => [...prevValue, value]);
        } else {
            const newData = t30selected.filter((item) => item !== value);
            setT30Selected(newData);
        }
    };

    const t40ChangeHandler = (event) => {
        const isChecked = event.target.checked;
        const value = parseInt(event.target.value);

        if (isChecked) {
            setT40Selected((prevValue) => [...prevValue, value]);
        } else {
            const newData = t40selected.filter((item) => item !== value);
            setT40Selected(newData);
        }
    };

    return (
        <>
            <div>
                <Head>
                    <title>View Protocol</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <MainContent>
                    <ModuleContentHeader
                        breadcrumbs={breadcrumbsData}
                        module_name={moduleName}
                    />

                    <Form layout="horizontal">
                        <Stack alignItems="flex-start" direction="column" spacing={9}>
                            <div>
                                <p>Title Protocol:</p>
                                <Input
                                as="textarea"
                                value={title}
                                onChange={v=>setTitle(v)}
                                readOnly={true}
                                style={{ width: 224 }} 
                                />
                             </div>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Tujuan : </p>
                                <Editor contentValue={tujuan} isReadOnly={true} />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Ruang Lingkup : </p>
                                <Editor contentValue={ruangLingkup} isReadOnly={true} />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Dokumen Acuan : </p>
                                <Editor contentValue={dokumenAcuan} isReadOnly={true} />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Tanggung Jawab : </p>
                                <Editor contentValue={tanggungJawab} isReadOnly={true} />
                            </Stack>
                            <Stack
                                direction="column"
                                alignItems="flex-start"
                                style={{ marginTop: '1rem' }}
                            >
                                <p className="font-bold">Rancangan Studi : </p>
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p>Nama Produk : </p>
                                <Editor contentValue={namaProduk} isReadOnly={true} />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p>Kode Produk : </p>
                                <Editor contentValue={kodeProduk} isReadOnly={true} />
                            </Stack>
                            {/* <Stack direction="column" alignItems="flex-start">
                                <p>Nomor Batch : </p>
                                <Editor contentValue={nomorBatch} isReadOnly={true} />
                            </Stack> */}
                            <Stack direction="column" alignItems="flex-start">
                                <p>Nomor PPI : </p>
                                <Editor contentValue={nomorPPI} isReadOnly={true} />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p>Batch Size : </p>
                                <Editor contentValue={batchSize} isReadOnly={true} />
                            </Stack>
                            <Form.Group
                                controlId="dibuatOleh"
                                style={{ marginBottom: '1em' }}
                            >
                                <Form.ControlLabel>
                                    <p className="text-xs">Dibuat Oleh :</p>
                                </Form.ControlLabel>
                                <Form.Control
                                    name="dibuatOleh"
                                    type="text"
                                    value={dibuatOleh}
                                    readOnly={true}
                                    required
                                />
                            </Form.Group>
                        </Stack>

                        <Form.Group
                            controlId="komponenPenyimpanan"
                            style={{ marginTop: '2em' }}
                        >
                            <Form.Group>
                                <Form.ControlLabel>
                                    <strong>Kondisi Penyimpanan :</strong>
                                </Form.ControlLabel>
                            </Form.Group>
                            <table className="table" style={{ maxWidth: 546 }}>
                                <thead>
                                    <tr>
                                        <th scope="col">No</th>
                                        <th scope="col" style={{ minWidth: 250 }}>
                                            Kriteria
                                        </th>
                                        {cycleMonthData.map((value) => (
                                            <th scope="col" className="text-center">
                                                {value}
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td scope="col">1</td>
                                        <td scope="col">T30 (30 &deg;C &plusmn; 2&deg;C / 75% &plusmn; 5% RH)</td>
                                        {cycleMonthData.map((item) => (
                                            <td scope="col">
                                                <input
                                                    type="checkbox"
                                                    name="T30"
                                                    value={item}
                                                    onChange={t30ChangeHandler}
                                                    checked={isChecked('T30', parseInt(item))}
                                                    disabled={true}
                                                />
                                            </td>
                                        ))}
                                    </tr>
                                    <tr>
                                        <td scope="col">2</td>
                                        <td scope="col">T40 (40 &deg;C &plusmn; 2&deg;C / 75% &plusmn; 5% RH)</td>
                                        {cycleMonthData.map((item) => (
                                            <td scope="col">
                                                <input
                                                    type="checkbox"
                                                    name="T40"
                                                    value={item}
                                                    onChange={t40ChangeHandler}
                                                    checked={isChecked('T40', parseInt(item))}
                                                    disabled={true}
                                                />
                                            </td>
                                        ))}
                                    </tr>
                                </tbody>
                            </table>
                        </Form.Group>

                        <Form.Group controlId="komposisiFormula">
                            <Stack>
                                <label htmlFor="komposisiFormula">
                                    <strong>Komposisi Formula :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <Editor contentValue={komposisiFormula} isReadOnly={true} />
                            </Stack>
                        </Form.Group>

                        {batchNumber != null && batchNumber != undefined && batchNumber.map((input) => {
                            return (
                             <Form.Group controlId="batchNumberProtocol">
                                <Stack direction="column" alignItems="flex-start">
                                    <Stack
                                    style={{
                                        padding: '0.5em',
                                        border: '0.1em solid #adadad',
                                        borderRadius: '5px 5px 5px 5px',
                                        marginBottom: '0.5em',
                                    }}
                                    >
                                    <Stack
                                        direction="column"
                                        alignItems="flex-start"
                                        style={{ marginLeft: '0.5em' }}
                                    >
                                        <p>
                                        <strong>Batch Number</strong>
                                        </p>
                                        {(
                                            <Stack>
                                            <Input
                                                value={input.Batch_number}
                                                as="textarea"
                                                type="text"
                                                required
                                                readOnly
                                                />
                                            </Stack>
                                        )}       
                                    </Stack>
                                    </Stack>                               
                                </Stack>
                            </Form.Group>
                            );
                            })}

                        <Form.Group controlId="parameterPengujianSpesifikasi">
                            <p className="mb-2">
                                <strong>Parameter Pengujian & Spesifikasi :</strong>
                            </p>
                            <Stack direction="column" alignItems="flex-start">
                                {protocolDetailData?.length > 0 &&
                                    protocolDetailData.map((item) => {
                                        return (
                                            <Stack
                                                style={{
                                                    padding: '0.5em',
                                                    border: '0.1em solid #adadad',
                                                    borderRadius: '5px 5px 5px 5px',
                                                    marginBottom: '0.5em',
                                                }}
                                            >
                                                <Stack direction="column" alignItems="flex-start">
                                                    <p>
                                                        <strong>Parameter</strong>
                                                    </p>
                                                    <Dropdown
                                                        title={item.Parameter_Name}
                                                        readOnly={true}
                                                        style={{ minWidth: 246 }}
                                                    />
                                                </Stack>
                                                <Stack
                                                    direction="column"
                                                    alignItems="flex-start"
                                                    style={{ marginLeft: '0.5em' }}
                                                >
                                                    <p>
                                                        <strong>Spesifikasi</strong>
                                                    </p>
                                                    {item.Input_Method_Desc == undefined && (
                                                        <Input disabled={true} />
                                                    )}
                                                    {item.Input_Method_Desc !== undefined &&
                                                        item.Input_Method_Desc !== null &&
                                                        item.Input_Method_Desc.toLowerCase() ===
                                                        'absolute' && (
                                                            <InputNumber
                                                                value={item.Value_Reference}
                                                                readOnly={true}
                                                            />
                                                        )}
                                                    {item.Input_Method_Desc !== undefined &&
                                                        item.Input_Method_Desc !== null &&
                                                        item.Input_Method_Desc.toLowerCase() ===
                                                        'description' && (
                                                            <Input
                                                                as="textarea"
                                                                value={item.Desc}
                                                                type="text"
                                                                readOnly={true}
                                                            />
                                                        )}
                                                    {item.Input_Method_Desc !== undefined &&
                                                        item.Input_Method_Desc !== null &&
                                                        item.Input_Method_Desc.toLowerCase() ===
                                                        'range' && (
                                                            <Stack>
                                                                <InputNumber
                                                                    value={item.Min_Value}
                                                                    readOnly={true}
                                                                    style={{ maxWidth: '7em' }}
                                                                />{' '}
                                                                -{' '}
                                                                <InputNumber
                                                                    value={item.Max_Value}
                                                                    readOnly={true}
                                                                    style={{ maxWidth: '7em' }}
                                                                />
                                                            </Stack>
                                                        )}
                                                </Stack>
                                            </Stack>
                                        );
                                    })}
                            </Stack>
                        </Form.Group>

                        <Form.Group controlId="keteranganTambahan">
                            <Stack>
                                <label htmlFor="keteranganTambahan">
                                    <strong>Keterangan Tambahan :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <Editor contentValue={additionalNotes} isReadOnly={true} />
                            </Stack>
                        </Form.Group>

                    </Form>

                    <Stack style={{ marginTop: '2rem' }} spacing={6} alignItems='flex-start' direction='column'>
                        <Panel shaded>
                            <p style={{ fontWeight: "bold" }}>Approval history</p>
                            <hr />
                            <Table approvalHistoryData={approvalHistory} />
                        </Panel>
                    </Stack>
                </MainContent>
            </ContainerLayout>
        </>
    );
}

export async function getServerSideProps({ query }) {
    const { noProtocol } = query;

    return {
        props: {
            noProtocol,
        },
    };
}
