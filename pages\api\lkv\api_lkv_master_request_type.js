import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/request_type${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};



export default function LkvMasterRequestType(){
    return{
        getAllMasterRequestType: createApiFunction("get", "/get/all"),
        getAllMasterRequestTypeActive: createApiFunction("get", "/get/all/active"),
        createMasterRequestType: createApiFunction("post", "/create"),
        updateMasterRequestType: createApiFunction("put", "/edit"),
        updateStatusMasterRequestType: createApiFunction("put", "/active")
    }
}