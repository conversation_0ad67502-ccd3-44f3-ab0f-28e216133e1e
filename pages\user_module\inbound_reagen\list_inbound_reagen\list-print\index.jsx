import { useEffect, useState } from "react";
import Head from "next/head";
import {
  <PERSON>readcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  useToaster,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import { useRouter } from "next/router";
import API_InboundReagen from "@/pages/api/inbound_reagen/api_inbound_reagen";
import API_MasterDataMappingReagen from "@/pages/api/master_data/api_masterdata_master_data_mapping_reagen";


export default function ListInboundReagenPage() {
  const [moduleName, setModuleName] = useState("");
  const { HeaderCell, Cell, Column } = Table;
  const [inbound, setInbound] = useState([]);
  const [mapping, setMapping] = useState([]);
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const [props, setProps] = useState([]);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const emptyFormValue = {
    id_criteria_reagen: null,
    room_temperature: null,
    batch_no: null,
    expired_date: null,
    amount: null,
    id_criteria: null,
    id_dimension: null,
    id_type: null,
    id_manufacture: null,
  };
  const [formValue, setFormValue] = useState(emptyFormValue);
  const toaster = useToaster();
  const router = useRouter();
  const { id_inbound_reagen } = router.query

  const [overInput, setOverInput] = useState(0);
  const [rackValues, setRackValues] = useState({});

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const handleGetAllApi = async () => {
    try {
        const res = await API_InboundReagen().getListPrint({
            id_inbound_reagen: parseInt(id_inbound_reagen)
        });
        console.log(res);
        if (res.status == 200) {
            setInbound(res.data ? res.data : []);    
        }else{
            console.log("error ", res.message)
        }        
    } catch (error) {
        console.log("error", error)
    }
  };

  

  const filteredData = inbound.filter((rowData, i) => {
    const searchFields = [
      "id_criteria_reagen",
      "reagen_name",
      "room_temperature",
      "batch_no",
      "expired_date",
      "manufacture_desc",
    ];

    const matchesSearch = searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : inbound.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("inbound_reagen/list_inbound_reagen")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }      
    }

    if (id_inbound_reagen) {
        handleGetAllApi();
    }
  },  [id_inbound_reagen]) 

  

  const handleGetDetailApi = async (rowData) => {
    try {
      const result = await API_InboundReagen().getPrint({
        "id_inbound_reagen_locator" : rowData.id_inbound_reagen_locator,
        "reagen_name" : rowData.reagen_name,
        "manufacture_desc" : rowData.manufacture_desc,
        "room_temperature" : rowData.room_temperature.replace('°', '_B0 '),
        "expired_date" : rowData.expired_date,
        "rack": rowData.rack,
        "floor": rowData.floor,
        "row": rowData.row,
      });
      
      console.log(result)
    } catch (error) {
      console.error("Error fetching details:", error);
    }
  };

 

  const handleExportExcel = () => {
    if (inbound.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }

    const ws = XLSX.utils.json_to_sheet(inbound);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const filename = `Inbound Reagen ${date}.xlsx`;

    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <div>
        <Head>
          <title>Inbound Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Inbound Reagen</Breadcrumb.Item>
                  <Breadcrumb.Item active>List Inbound Reagen</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              {/* <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column> */}
              <Column width={150} align="center" sortable>
                <HeaderCell>ID Inbound Reagen Locator</HeaderCell>
                <Cell dataKey="id_inbound_reagen_locator" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Reagen Name</HeaderCell>
                <Cell dataKey="reagen_name" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Brand Name</HeaderCell>
                <Cell dataKey="manufacture_desc" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Expired Date</HeaderCell>
                <Cell dataKey="expired_date" />
              </Column>
              <Column width={150} align="center" sortable>
                <HeaderCell>Storage Temperature</HeaderCell>
                <Cell dataKey="room_temperature" />
              </Column>
              <Column width={200} align="center">
                <HeaderCell>Rack - Floor - Row</HeaderCell>
                <Cell style={{ padding: "8px" }}>
                  {(rowData) => (
                    <div>
                     {`${rowData.rack} - ${rowData.floor} - ${rowData.row}`}
                    </div>
                  )}
                </Cell>
              </Column>
              <Column width={130} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell style={{ padding: "8px" }}>
                  {(rowData) => (
                    <div>
                     
                      <Button
                        appearance="link"
                        onClick={() => {
                        //   setShowDetailModal(true);
                          handleGetDetailApi(rowData);
                        }}
                      >
                        Print
                      </Button>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[50, 100, 300]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>
    </>
  );
}
