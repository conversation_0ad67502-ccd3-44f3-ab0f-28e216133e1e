import { useState, useEffect } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  Panel,
  Stack,
  Table,
  Input,
  InputGroup,
  Tag,
  Button,
  Loader,
  Pagination,
  ButtonToolbar,
  ButtonGroup
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import ApiTsdpTH from "@/pages/api/tsdp/transaction_h/api_tsdp_transaction_h";
import { useRouter } from "next/router";

export default function ApprovalManager() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [approvalData, setApprovalData] = useState([]);
  const [moduleName, setModuleName] = useState("");
  const [lineType, setLineType] = useState("automate"); // automate or manual
  const [filteredApprovalData, setFilteredApprovalData] = useState([]);
  const [masterUserList, setMasterUserList] = useState([]);
  const router = useRouter();

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    setModuleName(moduleNameValue || "");
    fetchApprovalData();
    fetchMasterUserList();
  }, []);

  // Effect untuk memfilter data berdasarkan line type
  useEffect(() => {
    if (approvalData.length > 0) {
      const filtered = approvalData.filter((item) => {
        if (lineType === "automate") {
          return item.line_type === 1;
        } else {
          return item.line_type === 0;
        }
      });
      setFilteredApprovalData(filtered);
      setPage(1); // Reset pagination when switching line type
    }
  }, [lineType, approvalData]);

const fetchApprovalData = async () => {
    try {
        setLoading(true);
        const api = ApiTsdpTH();
        const response = await api.getAllNeedApproveHeaderManager();
        
        if (response.status === 200) {
            const filteredData = response.data.filter(item => item.count_need_approve > 0);
            setApprovalData(filteredData || []);
        } else {
            console.error("Failed to fetch approval data:", response.message);
            setApprovalData([]);
        }
    } catch (error) {
        console.error("Error fetching approval data:", error);
        setApprovalData([]);
    } finally {
        setLoading(false);
    }
};

  const fetchMasterUserList = async () => {
    try {
      const api = ApiTsdpTH();
      const response = await api.getAllMasterUser();
      if (response.status === 200) {
        setMasterUserList(response.data || []);
      }
    } catch (error) {
      console.error("Error fetching master user list:", error);
    }
  };

  const handleLineTypeChange = (type) => {
    setLineType(type);
  };

  // Handler search
  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  // Filtered data (now using filteredApprovalData instead of approvalData)
  const searchFilteredData = filteredApprovalData.filter((rowData) => {
    const searchFields = [
      "id_header_trans",
      "product_code",
      "product_name",
      "batch_no",
      "sediaan_type",
      "production_scale",
      "trial_focus",
      "ppi_no",
      "process_purpose",
      "background",
      "created_by",
      "count_need_approve",
      "is_active"
    ];
    return searchFields.some((field) =>
      rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase())
    );
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const totalRowCount = searchKeyword ? searchFilteredData.length : filteredApprovalData.length;

  // Format date function
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("en-GB");
  };

  const getStatusLabel = (statusCode) => {
    switch (statusCode) {
      case 1: return { label: "Automate", color: "green" };
      case 0: return { label: "Manual", color: "blue" };
      default: return { label: "Unknown", color: "red" };
    }
  };

  const getNikName = (nik) => {
    if (!nik) return "-";
    const user = masterUserList.find((u) => u.employee_id === nik);
    return user ? `${nik} - ${user.name}` : nik;
  };

  return (
    <div>
      <Head>
        <title>TSDP Approval Manager</title>
      </Head>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <Breadcrumb>
                <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                <Breadcrumb.Item>TSDP</Breadcrumb.Item>
                <Breadcrumb.Item>Approval</Breadcrumb.Item>
                <Breadcrumb.Item active>Manager</Breadcrumb.Item>
              </Breadcrumb>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>TSDP Approval Manager</h5>
                <ButtonToolbar>
                  <ButtonGroup>
                    <Button 
                      appearance={lineType === "automate" ? "primary" : "ghost"}
                      onClick={() => handleLineTypeChange("automate")}
                    >
                      Automate Line
                    </Button>
                    <Button 
                      appearance={lineType === "manual" ? "primary" : "ghost"}
                      onClick={() => handleLineTypeChange("manual")}
                    >
                      Manual Line
                    </Button>
                  </ButtonGroup>
                </ButtonToolbar>
              </Stack>
            }
          ></Panel>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div />
                <InputGroup inside style={{ width: 300 }}>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Cari Approval..."
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    x
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(searchFilteredData, limit, page)}
              loading={loading}
              autoHeight
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => rowIndex + 1 + (page - 1) * limit}
                </Cell>
              </Column>

              <Column width={100} resizable>
                <HeaderCell>ID Trans</HeaderCell>
                <Cell dataKey="id_header_trans" />
              </Column>

              <Column width={100} resizable>
                <HeaderCell>Line ID</HeaderCell>
                <Cell dataKey="id_line" />
              </Column>

              <Column width={190} resizable>
                <HeaderCell>Batch No</HeaderCell>
                <Cell dataKey="batch_no" />
              </Column>

              <Column width={120} resizable>
                <HeaderCell>Product Code</HeaderCell>
                <Cell dataKey="product_code" />
              </Column>

              <Column width={200} resizable>
                <HeaderCell>Product Name</HeaderCell>
                <Cell dataKey="product_name" />
              </Column>

              <Column width={120} resizable>
                <HeaderCell>Sediaan</HeaderCell>
                <Cell dataKey="sediaan_type" />
              </Column>

              <Column width={120} resizable>
                <HeaderCell>Production Scale</HeaderCell>
                <Cell dataKey="production_scale" />
              </Column>

              <Column width={120} resizable>
                <HeaderCell>Trial Focus</HeaderCell>
                <Cell dataKey="trial_focus" />
              </Column>

              <Column width={150} resizable>
                <HeaderCell>PPI No</HeaderCell>
                <Cell dataKey="ppi_no" />
              </Column>

              <Column width={150} resizable>
                <HeaderCell>Process Date</HeaderCell>
                <Cell>
                  {(rowData) => formatDate(rowData.process_date)}
                </Cell>
              </Column>

              <Column width={200} resizable>
                <HeaderCell>Process Purpose</HeaderCell>
                <Cell dataKey="process_purpose" />
              </Column>

              <Column width={200} resizable>
                <HeaderCell>Background</HeaderCell>
                <Cell dataKey="background" />
              </Column>

              <Column width={150} resizable>
                <HeaderCell>Created Date</HeaderCell>
                <Cell>{(rowData) => formatDate(rowData.created_date)}</Cell>
              </Column>

              <Column width={190} resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell>
                  {(rowData) => getNikName(rowData.created_by)}
                </Cell>
              </Column>

              <Column width={150} resizable>
                <HeaderCell>Updated Date</HeaderCell>
                <Cell>{(rowData) => formatDate(rowData.updated_date)}</Cell>
              </Column>

              <Column width={190} resizable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell>
                  {(rowData) => getNikName(rowData.updated_by)}
                </Cell>
              </Column>

              <Column width={120} resizable align="center" fixed="right">
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={100} resizable fixed="right">
                <HeaderCell>Line Type</HeaderCell>
                <Cell>
                  {(rowData) => {
                    const status = getStatusLabel(rowData.line_type);
                    return <Tag color={status.color}>{status.label}</Tag>;
                  }}
                </Cell>
              </Column>
              
              <Column width={120} align="center" fixed="right">
                <HeaderCell>Need Approve</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span style={{ 
                      fontWeight: 'bold',
                      color: '#FF0000'
                    }}>
                      {rowData.count_need_approve}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={120} align="center" fixed="right">
                <HeaderCell>Action</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <Button 
                      appearance="primary" 
                      size="xs"
                      disabled={rowData.count_need_approve === 0}
                      onClick={() => router.push(`/user_module/tsdp/approval/mgr/detail?id_header_trans=${rowData.id_header_trans}`)}
                    >
                      Approval Details
                    </Button>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20, display: "flex", justifyContent: "space-between" }}>
              <span>Total Rows: {totalRowCount}</span>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={setLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>
    </div>
  );
}