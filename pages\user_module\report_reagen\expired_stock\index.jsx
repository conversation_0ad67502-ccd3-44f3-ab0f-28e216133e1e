import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  useToaster,
  SelectPicker,
  DatePicker,
  DateRangePicker,
  Loader,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import { useRouter } from "next/router";
import ApiReportReagen from "@/pages/api/report_reagen/api_report_reagen";

export default function ReportIndirect() {
  const [moduleName, setModuleName] = useState("");
  const { HeaderCell, Cell, Column } = Table;  
  const [expiredStockData, setExpiredStockData] = useState([]);
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const [props, setProps] = useState([]);
  const toaster = useToaster();
  const router = useRouter();
  const [dateRange, setDateRange] = useState([])
  
  const handleDateRange = async (date) =>{
    if (date != '') {
      let yyyy = date.getFullYear();
      let mm = date.getMonth() + 1;


      const res = await ApiReportReagen().getListExpired({
        "month": mm,
        "year": yyyy
      });
      console.log(res);
      setExpiredStockData(res.data ? res.data : []);
    }else{
      hanldeInitialApi()
    }    
}

  
  const hanldeInitialApi = async () => {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // Months are zero-based, so add 1

    console.log(new Date())

    const res = await ApiReportReagen().getListExpired({
      "month": currentMonth,
      "year": currentYear
    });
    console.log(res);
    setExpiredStockData(res.data ? res.data : []);
  };

  

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

 

  const filteredData = expiredStockData
    .filter((rowData, i) => {
    const searchFields = [
      "room_temperature",
      "reagen_name",
      "expired_date",
      "total_stock",
    ];

    const matchesSearch = searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : expiredStockData.length;


  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("outbound_reagen/list_outbound_reagen")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      hanldeInitialApi();
    }
  }, []);

  const handleExportExcel = () => {
    if (expiredStockData.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }

   
    
    const headerMapping = {
      room_temperature: "room temperature",
      reagen_name: "reagen name",
      expired_date: "expired date",
      total_stock: "total stock",      
    };

    const formattedData = expiredStockData.map((item) => {
      const formattedItem = {};
      for (const key in item) {
        if (headerMapping[key]) {
          if (key === "is_active") {
            formattedItem[headerMapping[key]] =
              item[key] === 1 ? "Active" : "Inactive";
          } else if (key === "status") {
            formattedItem[headerMapping[key]] =
              item[key] === 0 ? "Not Realized" : "Realized";
          } else {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
      }
      return formattedItem;
    });

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const filename = `Indirect data${date}.xlsx`;

    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <div>
        <Head>
          <title>Report Stock Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>{moduleName}</Breadcrumb.Item>
                  <Breadcrumb.Item>Report Reagen</Breadcrumb.Item>
                  <Breadcrumb.Item active>Report Stock</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                <DatePicker oneTap format="yyyy-MM" style={{ width: 200 }} 
                  onChange={(value)=>{
                      console.log("value", value)
                      handleDateRange(value || "")
                  }}
                />
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Reagen name</HeaderCell>
                <Cell dataKey="reagen_name" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Room Temperature</HeaderCell>
                <Cell dataKey="room_temperature" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Expired Date</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.expired_date <= new Date()
                        ? "red"
                        : "green",
                      }}
                    >
                      {rowData.expired_date}
                    </span>
                  )}
                </Cell>
               

              </Column>             
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Stock</HeaderCell>
                <Cell dataKey="total_stock" />
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>
    </>
  );
}
