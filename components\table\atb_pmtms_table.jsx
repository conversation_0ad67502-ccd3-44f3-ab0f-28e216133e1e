import { useEffect, useState } from "react";
import XLSX from "xlsx";
import {
  Button,
  IconButton,
  Stack,
  Table,
  Panel,
  Input,
  InputGroup,
  Pagination,
  Modal,
  useToaster,
  DatePicker,
  InputPicker,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import Messages from "@/components/Messages";
import dateTimeFormatter from "@/lib/function/date-time-formatter";
import dateFormatterDash from "@/lib/function/date-formatter-dash";

import API_MasterlistAtbPms from "@/pages/api/e_form/api_eform_masterlist_atb_pmtms";

export default function AtbPsTmsTable() {
  const toaster = useToaster();
  const [props, setProps] = useState([]);
  const { HeaderCell, Cell, Column } = Table;
  const [data, setData] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [mode, setMode] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const typeData = ["PM", "RM"].map((item) => ({ label: item, value: item }));
  const scopeData = ["Internal", "External"].map((item) => ({
    label: item,
    value: item,
  }));

  useEffect(() => {
    const fetchData = async () => {
      const res_data = await API_MasterlistAtbPms().getAll();
      setData(res_data.data || []);
    };
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setProps(dataLogin);
    fetchData();
  }, []);

  const emptyFormValue = {
    manufacturing_date: null,
    type: null,
    reference_id: null,
    material_code: null,
    material_name: null,
    lot_qc: null,
    lot_supplier: null,
    supplier_name: null,
    scope: null,
    qtt_reject: null,
    defect: null,
    no_pptp: null,
    remarks: null,
    created_by_id: null,
    updated_by_id: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id",
    "manufacturing_date",
    "type",
    "reference_id",
    "material_code",
    "material_name",
    "lot_qc",
    "lot_supplier",
    "supplier_name",
    "scope",
    "qtt_reject",
    "defect",
    "no_pptp",
    "remarks",
    "created_at",
    "created_by_id",
    "updated_at",
    "updated_by_id",
  ];

  const datas =
    data && data.length > 0
      ? data
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .concat(
                item.manufacturing_date
                  ? dateFormatterDash(item.manufacturing_date)
                  : ""
              )
              .concat(
                item.created_at
                  ? dateTimeFormatter(
                      item.created_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .concat(
                item.updated_at
                  ? dateTimeFormatter(
                      item.updated_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .some((val) => val?.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const searchData = data
    ? data.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];

  const handleOpenModal = (mode) => {
    setMode(mode);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setFormValue(emptyFormValue);
    setMode(null);
  };

  const handleRequired = () => {
    let required = ["manufacturing_date"];
    let error = false;
    required.forEach((item) => {
      if (!formValue[item]) {
        error = true;
      }
    });
    return error;
  };

  const handleActions = async () => {
    if (handleRequired()) {
      toaster.push(Messages("error", "Fill in the required fields!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }

    try {
      let result;
      if (mode === "add") {
        result = await API_MasterlistAtbPms().add({
          ...formValue,
          created_by_id: props.employee_id,
        });
      } else if (mode === "edit") {
        result = await API_MasterlistAtbPms().edit({
          ...formValue,
          id: selectedRow,
          updated_by_id: props.employee_id,
        });
      }

      const res = await API_MasterlistAtbPms().getAll();
      setData(res.data);

      // Show the toast message
      toaster.push(
        Messages(
          "success",
          `Success ${
            mode === "add" ? "adding" : "editing"
          } E-Form Masterlist ATB, PMTMS!`
        ),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );

      handleCloseModal();
    } catch (error) {
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );
    }
  };

  const handleExportExcel = (data) => {
    if (!data || data.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }

    const formattedData = data.map((item) => ({
      ID: item.id,
      "Manufacturing Date": dateFormatterDash(item.manufacturing_date),
      Type: item.type,
      "Reference ID": item.reference_id,
      "Material Code": item.material_code,
      "Material Name": item.material_name,
      "Lot QC": item.lot_qc,
      "Lot Supplier": item.lot_supplier,
      "Supplier Name": item.supplier_name,
      Scope: item.scope,
      "QTT Reject": item.qtt_reject,
      Defect: item.defect,
      "No. PTPP": item.no_pptp,
      Remarks: item.remarks,
      "Created At": dateTimeFormatter(item.created_at, "id-ID", "seconds"),
      "Created By ID": item.created_by_id,
      "Updated At": dateTimeFormatter(item.updated_at, "id-ID", "seconds"),
      "Updated By ID": item.updated_by_id,
    }));

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");
    const date = dateFormatterDash(new Date());
    const filename = `E-Form Masterlist ATB, PMTMS ${date}.xlsx`;
    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <Panel
        bordered
        bodyFill
        header={
          <Stack justifyContent="space-between">
            <div className="flex gap-2">
              <IconButton
                icon={<PlusIcon />}
                appearance="primary"
                onClick={() => handleOpenModal("add")}
              >
                Add
              </IconButton>
              <IconButton
                icon={<FileDownloadIcon />}
                appearance="primary"
                onClick={() => handleExportExcel(data)}
              >
                Download (.xlsx)
              </IconButton>
            </div>

            <InputGroup inside>
              <InputGroup.Addon>
                <SearchIcon />
              </InputGroup.Addon>
              <Input
                placeholder="Search"
                value={searchKeyword}
                onChange={handleSearch}
              />
              <InputGroup.Addon
                onClick={() => {
                  setSearchKeyword("");
                  setPage(1);
                }}
                style={{
                  display: searchKeyword ? "block" : "none",
                  color: "red",
                  cursor: "pointer",
                }}
              >
                <CloseOutlineIcon />
              </InputGroup.Addon>
            </InputGroup>
          </Stack>
        }
      >
        <Table
          data={datas}
          bordered
          cellBordered
          autoHeight
          sortColumn={sortColumn}
          sortType={sortType}
          onSortColumn={handleSortColumn}
          onRowClick={(data) => {
            console.log(data);
          }}
        >
          <Column fullText width={60} sortable resizable>
            <HeaderCell>ID</HeaderCell>
            <Cell dataKey="id" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Manufacturing Date</HeaderCell>
            <Cell dataKey="manufacturing_date">
              {(rowData) => {
                return rowData?.manufacturing_date
                  ? dateFormatterDash(rowData?.manufacturing_date)
                  : "-";
              }}
            </Cell>
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Type</HeaderCell>
            <Cell dataKey="type" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Reference ID</HeaderCell>
            <Cell dataKey="reference_id" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Material Code</HeaderCell>
            <Cell dataKey="material_code" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Material Name</HeaderCell>
            <Cell dataKey="material_name" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Lot QC</HeaderCell>
            <Cell dataKey="lot_qc" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Lot Supplier</HeaderCell>
            <Cell dataKey="lot_supplier" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Supplier Name</HeaderCell>
            <Cell dataKey="supplier_name" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Scope</HeaderCell>
            <Cell dataKey="scope" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>QTT Reject</HeaderCell>
            <Cell dataKey="qtt_reject" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Defect</HeaderCell>
            <Cell dataKey="defect" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>No. PTPP</HeaderCell>
            <Cell dataKey="no_pptp" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Remarks</HeaderCell>
            <Cell dataKey="remarks" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Created At</HeaderCell>
            <Cell dataKey="created_at">
              {(rowData) => {
                return rowData?.created_at
                  ? dateTimeFormatter(rowData?.created_at, "id-ID", "seconds")
                  : "-";
              }}
            </Cell>
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Created By ID</HeaderCell>
            <Cell dataKey="created_by_id" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Updated At</HeaderCell>
            <Cell dataKey="updated_at">
              {(rowData) => {
                return rowData?.updated_at
                  ? dateTimeFormatter(rowData?.updated_at, "id-ID", "seconds")
                  : "-";
              }}
            </Cell>
          </Column>

          <Column fullText width={150} sortable>
            <HeaderCell>Updated By ID</HeaderCell>
            <Cell dataKey="updated_by_id" />
          </Column>

          <Column width={80} fixed="right" align="center">
            <HeaderCell>...</HeaderCell>
            <Cell>
              {(rowData) => {
                function handleEditAction() {
                  setSelectedRow(rowData.id);
                  setFormValue({
                    ...rowData,
                    manufacturing_date: rowData.manufacturing_date
                      ? new Date(rowData.manufacturing_date)
                      : null,
                  });
                  setShowModal(true);
                  setMode("edit");
                }
                return (
                  <Button
                    onClick={handleEditAction}
                    appearance="link"
                    className="p-0"
                  >
                    Edit
                  </Button>
                );
              }}
            </Cell>
          </Column>
        </Table>
        <div style={{ padding: 20 }}>
          <Pagination
            prev
            next
            first
            last
            ellipsis
            boundaryLinks
            maxButtons={5}
            size="xs"
            layout={["total", "-", "limit", "|", "pager", "skip"]}
            total={searchKeyword ? searchData.length : data.length}
            limitOptions={[10, 30, 50]}
            limit={limit}
            activePage={page}
            onChangePage={setPage}
            onChangeLimit={handleChangeLimit}
          />
        </div>
      </Panel>

      <Modal
        backdrop="static"
        open={showModal}
        overflow={false}
        onClose={() => handleCloseModal()}
        size="md"
      >
        <Modal.Header>
          <Modal.Title>
            {mode === "add" ? "Add" : "Edit"} E-Form Masterlist ATB, PMTMS
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="flex flex-col gap-3">
          <div className="flex justify-between gap-4 w-full">
            <div className="flex flex-col gap-3 w-full">
              <div>
                <label>
                  Manufacture Date <span className="text-red-500">*</span>
                </label>
                <DatePicker
                  value={formValue.manufacturing_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, manufacturing_date: value });
                  }}
                  block
                  oneTap
                />
              </div>

              <div>
                <label>Type</label>
                <InputPicker
                  data={typeData}
                  value={formValue.type}
                  onChange={(value) => {
                    setFormValue({ ...formValue, type: value });
                  }}
                  block
                  maxLength={10}
                />
              </div>

              <div>
                <label>Reference ID</label>
                <Input
                  value={formValue.reference_id}
                  onChange={(value) => {
                    setFormValue({ ...formValue, reference_id: value });
                  }}
                  block
                  maxLength={100}
                />
              </div>

              <div>
                <label>Material Code</label>
                <Input
                  value={formValue.material_code}
                  onChange={(value) => {
                    setFormValue({ ...formValue, material_code: value });
                  }}
                  block
                  maxLength={100}
                />
              </div>

              <div>
                <label>Material Name</label>
                <Input
                  value={formValue.material_name}
                  onChange={(value) => {
                    setFormValue({ ...formValue, material_name: value });
                  }}
                  block
                  maxLength={150}
                />
              </div>
            </div>

            <div className="flex flex-col gap-3 w-full">
              <div>
                <label>Lot QC</label>
                <Input
                  value={formValue.lot_qc}
                  onChange={(value) => {
                    setFormValue({ ...formValue, lot_qc: value });
                  }}
                  block
                  maxLength={100}
                />
              </div>

              <div>
                <label>Lot Supplier</label>
                <Input
                  value={formValue.lot_supplier}
                  onChange={(value) => {
                    setFormValue({ ...formValue, lot_supplier: value });
                  }}
                  block
                  maxLength={100}
                />
              </div>

              <div>
                <label>Supplier Name</label>
                <Input
                  value={formValue.supplier_name}
                  onChange={(value) => {
                    setFormValue({ ...formValue, supplier_name: value });
                  }}
                  block
                  maxLength={150}
                />
              </div>

              <div>
                <label>Scope</label>
                <InputPicker
                  data={scopeData}
                  value={formValue.scope}
                  onChange={(value) => {
                    setFormValue({ ...formValue, scope: value });
                  }}
                  block
                  maxLength={50}
                />
              </div>

              <div>
                <label>No. PTPP</label>
                <Input
                  value={formValue.no_pptp}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_pptp: value });
                  }}
                  block
                  maxLength={100}
                />
              </div>
            </div>
          </div>

          <div>
            <div>
              <label>QTT Reject</label>
              <Input
                value={formValue.qtt_reject}
                onChange={(value) => {
                  setFormValue({ ...formValue, qtt_reject: value });
                }}
                as="textarea"
                maxLength={250}
              />
              {/* char counter */}
              <div className="flex justify-end">
                <span className="text-xs text-gray-400">
                  {formValue.qtt_reject?.length || 0}/250
                </span>
              </div>
            </div>

            <div>
              <label>Defect</label>
              <Input
                value={formValue.defect}
                onChange={(value) => {
                  setFormValue({ ...formValue, defect: value });
                }}
                as="textarea"
                maxLength={250}
              />
              {/* char counter */}
              <div className="flex justify-end">
                <span className="text-xs text-gray-400">
                  {formValue.defect?.length || 0}/250
                </span>
              </div>
            </div>

            <div>
              <label>Remarks</label>
              <Input
                value={formValue.remarks}
                onChange={(value) => {
                  setFormValue({ ...formValue, remarks: value });
                }}
                as="textarea"
                maxLength={250}
              />
              {/* char counter */}
              <div className="flex justify-end">
                <span className="text-xs text-gray-400">
                  {formValue.remarks?.length || 0}/250
                </span>
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <span className="text-red-500">* Required</span>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            onClick={() => {
              handleCloseModal();
            }}
            appearance="subtle"
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              handleActions();
            }}
            appearance="primary"
          >
            {mode === "add" ? "Add" : "Edit"}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}
