import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/reagen/masterdata/${url}`,
        data
    )
        .then((res) => {return res})
        .catch((err) => {return err});
    return response;
};

export default function MasterDataMasterDataReagenApi(){
    return{
        getAll: createApiFunction("get", "get/all"),
        add: createApiFunction("post", "create"),
        edit: createApiFunction("put", "update"),
        getById: createApiFunction("get", "get/id"),
        editStatus: createApiFunction("put", "edit/status"),
    }
}