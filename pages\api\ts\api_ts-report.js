import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  try {
    // /mobile-api/ts-dashboard/get-dashboard1
    const response = await axios[method](
      `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/${url}`,
      data
    ) .then((res) => { return res.data })
      .catch((err) => { return err.response.data });
    return response;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default function ApiTsReport() {
  return {
    // Automate Line
    getReport1: createApiFunction("get", "ts-dashboard/get-dashboard"),
    getReportDetail1: createApiFunction("post", "ts-detail-trans/get-id"),
    increasePrintAmount: createApiFunction("post", "ts-dashboard/increase-print-amount"),
    getReportByKeywordAuto: createApiFunction("post","ts-dashboard/auto/keyword"),
    getDetailKeywordAuto: createApiFunction("post", "ts-dashboard/auto/detail/keyword"),
    postDetailKeywordAuto: createApiFunction("post", "ts-header-trans/keyword"),
    postDetailLinkAuto: createApiFunction("post", "ts-header-trans/link"),

    // Manual Line
    getReport2: createApiFunction("get", "ts-dashboard/get-dashboard1"),
    increasePrintAmount1: createApiFunction("post", "ts-dashboard/increase-print-amount1"),
    getReportByKeywordmanual: createApiFunction("post","ts-dashboard/manual/keyword"),
    getDetailKeywordmanual: createApiFunction("post", "ts-dashboard/manual/detail/keyword"),
    postDetailKeywordManual: createApiFunction("post", "ts-header-trans1/keyword"),
    postDetailLinkManual: createApiFunction("post", "ts-header-trans1/link"),
  };
}
