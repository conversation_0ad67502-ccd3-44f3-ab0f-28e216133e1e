import MainContent from "@/components/layout/MainContent";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import UseTestApi from "../api/userApi";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Head from "next/head";
import { Dropdown } from "rsuite";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import departmentApi from "../api/departmentApi";

function EditUser({ dataToEdit, userId, allDepartment }) {
  const router = useRouter();
  const { GetByIdTestApi, UpdateUserDataTestApi } = UseTestApi();
  const MySwal = withReactContent(Swal);
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [employeeId, setEmployeeId] = useState("");
  const [employeeName, setEmployeeName] = useState("");
  const [email, setEmail] = useState("");
  const [division, setDivision] = useState("");
  const [isActive, setIsActive] = useState("");
  const [department, setDepartment] = useState({});
  const [allDepartmentList, setAllDepartmentList] = useState([]);
  const [selectedDepartmentOld, setSelectedDepartmentOld] = useState({});
  const [jobtitle, setJobTitle] = useState('')

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  }, []);

  useEffect(() => {
    if (dataToEdit.length > 0) {
      setEmployeeId(dataToEdit[0].Employee_Id);
      setEmployeeName(dataToEdit[0].Name);
      setEmail(dataToEdit[0].Email);
      setDivision(dataToEdit[0].Division);
      setIsActive(`${dataToEdit[0].Is_active}`);
      setJobTitle(dataToEdit[0].Job_title)
    }

    console.log("[datatoedit]",dataToEdit[0].Job_title)

    if (
      allDepartment !== undefined &&
      allDepartment !== null &&
      allDepartment.length > 0
    ) {
      setAllDepartmentList(allDepartment);

      // Set initial department value
      const selected = allDepartment.filter(
        (item) => item.Department_Name === dataToEdit[0].Department
      );
      if (selected[0].Id_Setup !== null && selected[0].Id_Setup !== undefined) {
        setDepartment(selected[0]);
      }
    }
  }, [dataToEdit, allDepartment]);

  // Handler
  function employeeIdHandler(event) {
    setEmployeeId(event.target.value);
  }
  function employeeNameHandler(event) {
    setEmployeeName(event.target.value);
  }
  function emailHandler(event) {
    setEmail(event.target.value);
  }
  function divisionHandler(event) {
    setDivision(event.target.value);
  }
  function isActiveHandler(event) {
    setIsActive(event.target.value);
  }
  function jobTitleHandler(event) {
    setJobTitle(event.target.value);
  }

  async function updateUserHandler(e) {
    setIsFormDisabled(true);
    e.preventDefault();

    if (
      department.Id_Setup === "" ||
      department.Id_Setup === undefined ||
      department.Id_Setup === null
    ) {
      setIsFormDisabled(false);
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please fill all required data.",
      });
      return;
    }

    // Mengubah nilai is_active menjadi integer
    let is_active = parseInt(isActive);

    const dataUpdate = {
      name: employeeName,
      division: division,
      department: parseInt(department.Id_Setup),
      email: email,
      is_active: is_active,
      employee_id: employeeId,
      job_title: jobtitle
    };

    const { Data } = await UpdateUserDataTestApi(dataUpdate);

    if (Data) {
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "User updated successfully.",
        allowOutsideClick: false,
        timer: 2500,
      });
      router.push({
        pathname: "/user_management",
      });
    }
  }
  return (
    <>
      <div>
        <Head>
          <title>Edit User</title>
        </Head>
      </div>
      <ContainerLayout
        title="User Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <h4>Form Edit User</h4>
          <div className="p-5">
            <form onSubmit={updateUserHandler}>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Employee ID
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  id="employeeId"
                  value={employeeId}
                  onChange={employeeIdHandler}
                  aria-describedby="validationServer03Feedback"
                  required
                  disabled={true}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="employee_name" className="form-label">
                  Employee Name
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="employee_name"
                  aria-describedby="emailHelp"
                  value={employeeName}
                  onChange={employeeNameHandler}
                  required
                  disabled={isFormDisabled}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="email" className="form-label">
                  Email
                </label>
                <input
                  type="email"
                  className="form-control"
                  id="email"
                  aria-describedby="emailHelp"
                  value={email}
                  onChange={emailHandler}
                  required
                  disabled={isFormDisabled}
                />
              </div>

              <div className="mb-3">
                <label htmlFor="division" className="form-label">
                  Division
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="division"
                  aria-describedby="emailHelp"
                  value={division}
                  onChange={divisionHandler}
                  required
                  disabled={isFormDisabled}
                />
              </div>

              <div className="mb-3">
                <label htmlFor="jobTitle" className="form-label">
                  Job Title
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="jobTitle"
                  aria-describedby="emailHelp"
                  value={jobtitle}
                  onChange={jobTitleHandler}
                  required
                  disabled={isFormDisabled}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="department" className="form-label">
                  Department
                </label>
                <br />
                <Dropdown
                  disabled={isFormDisabled}
                  title={
                    department.Department_Name !== undefined
                      ? department.Department_Name
                      : "-- Select Department --"
                  }
                  onSelect={(value) => setDepartment(value)}
                >
                  <Dropdown.Item eventKey="">
                    -- Select Department --
                  </Dropdown.Item>
                  {allDepartmentList.length > 0 &&
                    allDepartmentList.map((department) => (
                      <Dropdown.Item
                        eventKey={department}
                        key={department.Id_Setup}
                      >
                        {department.Department_Name}
                      </Dropdown.Item>
                    ))}
                  {/* <Dropdown.Item eventKey="IT">IT</Dropdown.Item>
                  <Dropdown.Item eventKey="Finance">Finance</Dropdown.Item> */}
                </Dropdown>
              </div>
              <label className="mb-2">Is_Active</label>
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="radio"
                  name="is_active"
                  id="is_active"
                  value="1"
                  checked={isActive === "1"}
                  onChange={isActiveHandler}
                />
                <label className="form-check-label" htmlFor="is_active">
                  Active
                </label>
              </div>
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="radio"
                  name="is_active"
                  id="non_active"
                  value="0"
                  checked={isActive === "0"}
                  onChange={isActiveHandler}
                />
                <label className="form-check-label mb-5" htmlFor="non_active">
                  Non-active
                </label>
              </div>

              <button
                disabled={isFormDisabled}
                type="submit"
                className="btn btn-primary p-2"
              >
                Update User
              </button>
              <button
                disabled={isFormDisabled}
                type="button"
                className="btn btn-dark mx-2 p-2"
                onClick={() => router.back()}
              >
                Cancel
              </button>
            </form>
          </div>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  // const userId = query.editUser[1];

  const { userId } = query;
  const { GetByIdTestApi } = UseTestApi();
  const { GetAllDepartment } = departmentApi();

  const reqData = `${userId}`;

  const { Data: dataToEditResult } = await GetByIdTestApi(reqData);
  const { data: departmentList } = await GetAllDepartment();

  let dataToEdit = [];
  let allDepartment = [];

  if (dataToEditResult != undefined) {
    dataToEdit = dataToEditResult;
  }
  if (departmentList !== undefined) {
    allDepartment = departmentList;
  }

  return {
    props: {
      dataToEdit,
      userId,
      allDepartment,
    },
  };
}

export default EditUser;
