import { useState } from "react";

export default function Menu(props) {
  console.log(props.menuData);
  return (
    <>
      {props.menuData &&
        props.menuData.map((item) => {
          if (item.Is_Parent == 1) {
            return (
              <button className="container text-center border-0">
                <p>{item.Menu_Name}</p>
              </button>
            );
          } else {
            return (
              <button className="container bg-dark text-white text-center border-0">
                <p>{item.Menu_Name}</p>
              </button>
            );
          }
        })}
    </>
  );
}
