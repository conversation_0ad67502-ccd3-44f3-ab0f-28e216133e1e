import { useRouter } from "next/router";
import { useState } from "react";
import { loadingAnimationNoClick } from "./SweetAlertLoading";

export default function ModuleButton({
  children,
  target,
  id,
  icon,
  buttonDisabledHandler,
  isDisabled,
}) {
  const router = useRouter();

  function goTo(destination, id = null) {
    // Mengubah format penulisan, nantinya akan digunakan untuk route tujuan
    const convertToLowercaseString = destination.toLowerCase();
    const convertToUnderscoreSpacing = convertToLowercaseString.replace(
      / /g,
      "_"
    );
    let resultDestination = convertToUnderscoreSpacing;

    switch (resultDestination) {
      case "general_administration":
        router.push({
          pathname: "/general_administration",
          query: { module_name: target },
        });
        break;
      case "module_management":
        router.push("/module_management");
        break;
      case "user_management":
        router.push("/user_management");
        break;
      case "menu_management":
        router.push("/menu_management");
        break;
      default:
        router.push({
          pathname: "/user_module",
          // query: { module_code: id, module_icon: icon, module_name: target },
          query: { module_code: id, module_name: target },
        });
        // router.push(`/user_module/${target}/${id}`);
        break;
    }
  }

  function buttonHandler() {
    // loadingAnimationNoClick();
    // buttonDisabledHandler();
    goTo(target, id);
  }

  return (
    <button
      disabled={isDisabled}
      className="container btn btn-success pt-2 pb-2 text-center rounded mb-2"
      onClick={buttonHandler}
    >
      {children}
    </button>
  );
}
