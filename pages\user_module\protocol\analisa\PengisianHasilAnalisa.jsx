import { useState, useEffect } from 'react';
import ProtocolApi from '@/pages/api/protocolApi';
import Head from 'next/head';
import ContainerLayout from '@/components/layout/ContainerLayout';
import MainContent from '@/components/layout/MainContent';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import { useRouter } from 'next/router';
import {
    Stack,
    Table,
    Panel,
    Input,
    InputGroup,
    Pagination,
    Tag,
    IconButton,
} from "rsuite";
import { paginate } from '@/utils/paginate';
import SearchIcon from '@rsuite/icons/Search';
import CloseOutlineIcon from '@rsuite/icons/CloseOutline';

export default function PengisianHasilAnalisa() {
    const router = useRouter();
    const [allProtocolHeaderData, setAllProtocolHeaderData] = useState([]);
    const { GetProtocolDataForAnalysisReport } = ProtocolApi();
    const [userDept, setUserDept] = useState('');
    const [moduleName, setModuleName] = useState('');
    const [breadcrumbsData, setBreadcrumbsData] = useState([]);
    const { HeaderCell, Cell, Column } = Table;
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [searchKeyword, setSearchKeyword] = useState("");
    let path = 'protocol/analisa/PengisianHasilAnalisa';

    // data to be displayed in the table
    var filteredData = allProtocolHeaderData.filter(item => item.No_Protocol.toLowerCase().includes(searchKeyword.toLowerCase()) || item.Created_By.toLowerCase().includes(searchKeyword.toLowerCase()) || item.Timeframe_Name.toLowerCase().includes(searchKeyword.toLowerCase()) || item.Effective_Date.toLowerCase().includes(searchKeyword.toLowerCase()) || item.Display_Date.toLowerCase().includes(searchKeyword.toLowerCase()));
    var datas = paginate(filteredData, page, limit);

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setSortColumn(sortColumn);
        setSortType(sortType);
    };

    const GetProtocolData = async (idDept) => {
        const reqData = {
            id_dept: parseInt(idDept)
        };

        const { data: data } = await GetProtocolDataForAnalysisReport(reqData);

        if (data !== null && data?.length > 0) {
            const newData = data.map(item => {
                item.Effective_Date = item.Effective_Date.slice(0, 10);
                item.Display_Date = item.Display_Date.slice(0, 10);
                return item;
            });
            setAllProtocolHeaderData(newData);
        } else {
            setAllProtocolHeaderData([]);
        }
    };

    useEffect(() => {
        const moduleNameValue = localStorage.getItem('module_name');
        const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
        if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
            router.push('/');
            return;
        }

        // validate access for this page
        if (!dataLogin.menu_link_code) {
            router.push('/dashboard');
            return;
        }

        const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
            item.includes(path),
        );
        if (validateUserAccess.length === 0) {
            router.push('/dashboard');
            return;
        }

        if (
            moduleNameValue === null ||
            moduleNameValue === undefined ||
            moduleNameValue === ''
        ) {
            router.push('/dashboard');
            return;
        }

        const asPathWithoutQuery = router.asPath.split('?')[0];
        const asPathNestedRoutes = asPathWithoutQuery
            .split('/')
            .filter((v) => v.length);
        let routerBreadCrumbsData = asPathNestedRoutes.filter((item, index) => index != 0);
        routerBreadCrumbsData.unshift(moduleNameValue);
        const capitalizeFirstLetter = (str) => {
            return str.charAt(0).toUpperCase() + str.slice(1);
        };
        const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
            const words = item.split(/(?=[A-Z])/);
            return words.map((word) => capitalizeFirstLetter(word)).join(' ');
        });
        setBreadcrumbsData(breadCrumbsResult);
        setUserDept(dataLogin.department);
        setModuleName(moduleNameValue);

        GetProtocolData(dataLogin.department);

    }, []);

    return (
        <>
            <div>
                <Head>
                    <title>Pengisian Hasil Analisa</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <MainContent>
                    <ModuleContentHeader
                        breadcrumbs={breadcrumbsData}
                        module_name={moduleName}
                    />
                    {allProtocolHeaderData?.length > 0 ?
                        <>
                            <Stack direction='row' justifyContent='flex-end'>
                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="Search"
                                        value={searchKeyword}
                                        onChange={value => {
                                            setSearchKeyword(value);
                                            setPage(1);
                                        }}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}
                                    >
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                            <Panel>
                                <Table
                                    bordered
                                    cellBordered
                                    height={400}
                                    data={datas}
                                    sortColumn={sortColumn}
                                    sortType={sortType}
                                    onSortColumn={handleSortColumn}
                                >
                                    <Column width={60} align="center" fixed>
                                        <HeaderCell>No</HeaderCell>
                                        <Cell>
                                            {(rowData, rowIndex) => {
                                                return rowIndex + 1 + limit * (page - 1);
                                            }}
                                        </Cell>
                                    </Column>

                                    <Column width={200}>
                                        <HeaderCell>No Protocol</HeaderCell>
                                        <Cell dataKey="No_Protocol" />
                                    </Column>

                                    <Column width={250}>
                                        <HeaderCell>Kondisi Penyimpanan</HeaderCell>
                                        <Cell>
                                            {rowData => {
                                                const name = rowData.Timeframe_Name.slice(0, 3);
                                                if (name == 'T30') {
                                                    return <Tag color="cyan">{rowData.Timeframe_Name}</Tag>;
                                                } else {
                                                    return <Tag color="violet">{rowData.Timeframe_Name}</Tag>;
                                                }
                                            }}
                                        </Cell>
                                    </Column>

                                    <Column width={150} resizable>
                                        <HeaderCell>Batch Number</HeaderCell>
                                        <Cell dataKey='Batch_number'  />
                                    </Column>

                                    <Column width={100}>
                                        <HeaderCell>Cycle Month</HeaderCell>
                                        <Cell dataKey='Cycle_Month' />
                                    </Column>

                                    <Column width={150}>
                                        <HeaderCell>Effective Date</HeaderCell>
                                        <Cell>{rowData => rowData.Effective_Date.slice(0, 10)}</Cell>
                                    </Column>

                                    <Column width={150}>
                                        <HeaderCell>Display Date</HeaderCell>
                                        <Cell>{rowData => rowData.Display_Date.slice(0, 10)}</Cell>
                                    </Column>

                                    <Column width={250}>
                                        <HeaderCell>Dibuat Oleh</HeaderCell>
                                        <Cell dataKey="Created_By" />
                                    </Column>

                                    <Column width={50}>
                                        <HeaderCell>Versi</HeaderCell>
                                        <Cell dataKey="Protocol_Version" />
                                    </Column>

                                    <Column width={100} fixed="right">
                                        <HeaderCell>...</HeaderCell>

                                        <Cell style={{ padding: "6px" }}>
                                            {(rowData) => (
                                                <Stack direction='row' justifyContent='center' spacing={2}>
                                                    {<IconButton size='md' icon={<SearchIcon />} title='Tinjau dokumen' appearance='ghost' color='orange' onClick={() => {
                                                        router.push({
                                                            pathname:
                                                                '/user_module/protocol/analisa/PengisianHasilAnalisaDetail',
                                                            query: { noProtocol: rowData.No_Protocol, idSetupCycle: rowData.Id_Setup_Cycle, id_detail_batch: rowData.Id_detail_batch },
                                                        });
                                                    }} />}
                                                </Stack>
                                            )}
                                        </Cell>
                                    </Column>
                                </Table>
                                <div style={{ padding: 20 }}>
                                    <Pagination
                                        prev
                                        next
                                        first
                                        last
                                        ellipsis
                                        boundaryLinks
                                        maxButtons={5}
                                        size="xs"
                                        layout={["total", "-", "limit", "|", "pager"]}
                                        // layout={["total", "-", "limit", "|", "pager", "skip"]}
                                        total={
                                            allProtocolHeaderData?.length
                                        }
                                        limitOptions={[10, 30, 50]}
                                        limit={limit}
                                        activePage={page}
                                        onChangePage={setPage}
                                        onChangeLimit={handleChangeLimit}
                                    />
                                </div>
                            </Panel>
                        </>
                        : <Stack direction='row' justifyContent='center'>
                            <p className='font-semibold'>No data found.</p>
                        </Stack>
                    }
                </MainContent>
            </ContainerLayout>
        </>
    );
}
