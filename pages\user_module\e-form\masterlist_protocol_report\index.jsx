import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Button,
  IconButton,
  Stack,
  Breadcrumb,
  Tag,
  Table,
  Panel,
  Input,
  InputGroup,
  Pagination,
  Modal,
  useToaster,
  DatePicker,
  InputPicker,
  InputNumber,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";
import dateTimeFormatter from "@/lib/function/date-time-formatter";
import dateFormatterDash from "@/lib/function/date-formatter-dash";

import API_MasterListProtocolReport from "@/pages/api/e_form/api_eform_masterlist_protocol_report";

export default function EFormMasterlistProtocolReportPage() {
  const router = useRouter();
  const toaster = useToaster();
  const [props, setProps] = useState([]);
  const { HeaderCell, Cell, Column } = Table;

  const [moduleName, setModuleName] = useState("");
  const [data, setData] = useState([]);

  const [showModal, setShowModal] = useState(false);
  const [mode, setMode] = useState(null);

  const [selectedRow, setSelectedRow] = useState(null);

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const typeData = [
    "STUDI MASA BERSIH",
    "STUDI MASA SIMPAN",
    "MASA SIMPAN MEDIA",
    "VALIDASI",
    "STABILITAS",
  ].map((item) => ({ label: item, value: item }));

  useEffect(() => {
    const fetchData = async () => {
      const res_data = await API_MasterListProtocolReport().getAll();
      setData(res_data.data || []);
    };

    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("e-form/masterlist_protocol_report")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      fetchData();
    }
  }, []);

  const emptyFormValue = {
    type: null,
    no_cupboard: null,
    no: null,
    no_protocol: null,
    protocol_title: null,
    protocol_created_by: null,
    no_report: null,
    report_title: null,
    report_created_by: null,
    testing_time_period: null,
    testing_start_date: null,
    execution_time: null,
    test_analyst: null,
    status: null,
    doc_attachment: null,
    created_by: null,
    updated_by: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id",
    "type",
    "no_cupboard",
    "no",
    "no_protocol",
    "protocol_title",
    "protocol_created_by",
    "no_report",
    "report_title",
    "report_created_by",
    "testing_time_period",
    "testing_start_date",
    "execution_time",
    "test_analyst",
    "status",
    "doc_attachment",
    "created_at",
    "created_by",
    "updated_at",
    "updated_by",
  ];

  const datas =
    data && data.length > 0
      ? data
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .concat(
                item.created_at
                  ? dateTimeFormatter(
                      item.created_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .concat(
                item.updated_at
                  ? dateTimeFormatter(
                      item.updated_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .some((val) => val?.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const searchData = data
    ? data.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];

  const handleOpenModal = (mode) => {
    setMode(mode);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setFormValue(emptyFormValue);
    setMode(null);
  };

  const handleRequired = () => {
    let required = ["testing_start_date"];
    let error = false;
    required.forEach((item) => {
      if (!formValue[item]) {
        error = true;
      }
    });
    return error;
  };

  const handleActions = async () => {
    if (handleRequired()) {
      toaster.push(Messages("error", "Fill in the required fields!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }

    try {
      let result;
      if (mode === "add") {
        result = await API_MasterListProtocolReport().add({
          ...formValue,
          no: parseInt(formValue.no),
          created_by: props.employee_id,
        });
      } else if (mode === "edit") {
        result = await API_MasterListProtocolReport().edit({
          ...formValue,
          id: selectedRow,
          no: parseInt(formValue.no),
          updated_by: props.employee_id,
        });
      }

      const res = await API_MasterListProtocolReport().getAll();
      setData(res.data);

      // Show the toast message
      toaster.push(
        Messages(
          "success",
          `Success ${
            mode === "add" ? "adding" : "editing"
          } E-Form Masterlist Protocol Report!`
        ),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );

      handleCloseModal();
    } catch (error) {
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );
    }
  };

  const handleExportExcel = (data) => {
    if (!data || data.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }

    const formattedData = data.map((item) => ({
      ID: item.id,
      "No. Cupboard": item.no_cupboard,
      "No.": item.no,
      "No. Protocol": item.no_protocol,
      "Protocol Title": item.protocol_title,
      "Protocol Created By": item.protocol_created_by,
      "No. Report": item.no_report,
      "Report Title": item.report_title,
      "Report Created By": item.report_created_by,
      "Testing Time Period": item.testing_time_period,
      "Testing Start Date": dateTimeFormatter(
        item.testing_start_date,
        "id-ID",
        "seconds"
      ),
      "Execution Time": item.execution_time,
      "Test Analyst": item.test_analyst,
      Status: item.status,
      "Doc Attachment": item.doc_attachment,
      "Created At": dateTimeFormatter(item.created_at, "id-ID", "seconds"),
      "Created By": item.created_by,
      "Updated At": dateTimeFormatter(item.updated_at, "id-ID", "seconds"),
      "Updated By": item.updated_by,
    }));

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");
    const date = dateFormatterDash(new Date());
    const filename = `E-Form Masterlist Protocol Report ${date}.xlsx`;
    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <Head>
        <title>E-Form Masterlist Protocol Report</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>E-Form</Breadcrumb.Item>
                  <Breadcrumb.Item active>
                    Masterlist Protocol Report
                  </Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusIcon />}
                    appearance="primary"
                    onClick={() => handleOpenModal("add")}
                  >
                    Add
                  </IconButton>
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={() => handleExportExcel(data)}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              data={datas}
              bordered
              cellBordered
              autoHeight
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={60} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>

              <Column fullText width={60} sortable resizable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Type</HeaderCell>
                <Cell dataKey="type" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>No. Cupboard</HeaderCell>
                <Cell dataKey="no_cupboard" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>No.</HeaderCell>
                <Cell dataKey="no" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>No. Protocol</HeaderCell>
                <Cell dataKey="no_protocol" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Protocol Title</HeaderCell>
                <Cell dataKey="protocol_title" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Protocol Created By</HeaderCell>
                <Cell dataKey="protocol_created_by" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>No. Report</HeaderCell>
                <Cell dataKey="no_report" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Report Title</HeaderCell>
                <Cell dataKey="report_title" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Report Created By</HeaderCell>
                <Cell dataKey="report_created_by" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Testing Time Period</HeaderCell>
                <Cell dataKey="testing_time_period" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Testing Start Date</HeaderCell>
                <Cell dataKey="testing_start_date" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Execution Time</HeaderCell>
                <Cell dataKey="execution_time" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Test Analyst</HeaderCell>
                <Cell dataKey="test_analyst" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Status</HeaderCell>
                <Cell dataKey="status" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Doc Attachment</HeaderCell>
                <Cell dataKey="doc_attachment" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Created At</HeaderCell>
                <Cell dataKey="created_at">
                  {(rowData) => {
                    return rowData?.created_at
                      ? dateTimeFormatter(
                          rowData?.created_at,
                          "id-ID",
                          "seconds"
                        )
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell dataKey="created_by" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Updated At</HeaderCell>
                <Cell dataKey="updated_at">
                  {(rowData) => {
                    return rowData?.updated_at
                      ? dateTimeFormatter(
                          rowData?.updated_at,
                          "id-ID",
                          "seconds"
                        )
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column fullText width={150} sortable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>

              <Column width={80} fixed="right" align="center">
                <HeaderCell>...</HeaderCell>
                <Cell>
                  {(rowData) => {
                    function handleEditAction() {
                      setSelectedRow(rowData.id);
                      setFormValue({
                        ...rowData,
                        testing_start_date: rowData.testing_start_date
                          ? new Date(rowData.testing_start_date)
                          : null,
                      });
                      setShowModal(true);
                      setMode("edit");
                    }
                    return (
                      <Button
                        onClick={handleEditAction}
                        appearance="link"
                        className="p-0"
                      >
                        Edit
                      </Button>
                    );
                  }}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                total={searchKeyword ? searchData.length : data.length}
                limitOptions={[10, 30, 50]}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        <Modal
          backdrop="static"
          open={showModal}
          overflow={false}
          onClose={() => handleCloseModal()}
        >
          <Modal.Header>
            <Modal.Title>
              {mode === "add" ? "Add" : "Edit"} E-Form Masterlist Protocol
              Report
            </Modal.Title>
          </Modal.Header>
          <Modal.Body className="flex flex-col gap-3">
            <div className="flex justify-between gap-4 w-full">
              <div className="flex flex-col gap-3 w-full">
                <div>
                  <label>
                    Type <span className="text-red-500">*</span>
                  </label>
                  <InputPicker
                    data={typeData}
                    value={formValue.type}
                    onChange={(value) => {
                      setFormValue({ ...formValue, type: value });
                    }}
                    block
                  />
                </div>

                <div>
                  <label>No. Cupboard</label>
                  <Input
                    value={formValue.no_cupboard}
                    onChange={(value) => {
                      setFormValue({ ...formValue, no_cupboard: value });
                    }}
                    maxLength={50}
                  />
                </div>

                <div>
                  <label>No.</label>
                  <InputNumber
                    value={formValue.no}
                    onChange={(value) => {
                      setFormValue({ ...formValue, no: value });
                    }}
                  />
                </div>

                <div>
                  <label>No. Protocol</label>
                  <Input
                    value={formValue.no_protocol}
                    onChange={(value) => {
                      setFormValue({ ...formValue, no_protocol: value });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>Protocol Title</label>
                  <Input
                    value={formValue.protocol_title}
                    onChange={(value) => {
                      setFormValue({ ...formValue, protocol_title: value });
                    }}
                    as="textarea"
                    maxLength={100}
                  />
                  {/* char counter */}
                  <div className="flex justify-end">
                    <span className="text-xs text-gray-400">
                      {formValue.remarks?.protocol_title || 0}/100
                    </span>
                  </div>
                </div>

                <div>
                  <label>Protocol Created By</label>
                  <Input
                    value={formValue.protocol_created_by}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        protocol_created_by: value,
                      });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>No. Report</label>
                  <Input
                    value={formValue.no_report}
                    onChange={(value) => {
                      setFormValue({ ...formValue, no_report: value });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>Report Title</label>
                  <Input
                    value={formValue.report_title}
                    onChange={(value) => {
                      setFormValue({ ...formValue, report_title: value });
                    }}
                    as="textarea"
                    maxLength={100}
                  />
                  {/* char counter */}
                  <div className="flex justify-end">
                    <span className="text-xs text-gray-400">
                      {formValue.remarks?.report_title || 0}/100
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-3 w-full">
                <div>
                  <label>Report Created By</label>
                  <Input
                    value={formValue.report_created_by}
                    onChange={(value) => {
                      setFormValue({ ...formValue, report_created_by: value });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>Testing Time Period</label>
                  <Input
                    value={formValue.testing_time_period}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        testing_time_period: value,
                      });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>
                    Testing Start Date <span className="text-red-500">*</span>
                  </label>
                  <DatePicker
                    value={formValue.testing_start_date}
                    onChange={(value) => {
                      setFormValue({ ...formValue, testing_start_date: value });
                    }}
                    block
                    oneTap
                  />
                </div>

                <div>
                  <label>Execution Time</label>
                  <Input
                    value={formValue.execution_time}
                    onChange={(value) => {
                      setFormValue({ ...formValue, execution_time: value });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>Test Analyst</label>
                  <Input
                    value={formValue.test_analyst}
                    onChange={(value) => {
                      setFormValue({ ...formValue, test_analyst: value });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>Status</label>
                  <Input
                    value={formValue.status}
                    onChange={(value) => {
                      setFormValue({ ...formValue, status: value });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>Doc Attachment</label>
                  <Input
                    value={formValue.doc_attachment}
                    onChange={(value) => {
                      setFormValue({ ...formValue, doc_attachment: value });
                    }}
                    as="textarea"
                    maxLength={250}
                  />
                  {/* char counter */}
                  <div className="flex justify-end">
                    <span className="text-xs text-gray-400">
                      {formValue.doc_attachment?.length || 0}/250
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-2">
              <span className="text-red-500">* Required</span>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                handleCloseModal();
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleActions();
              }}
              appearance="primary"
            >
              {mode === "add" ? "Add" : "Edit"}
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
