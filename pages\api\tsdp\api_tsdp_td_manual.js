import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](
    `${process.env.NEXT_PUBLIC_REAGEN}/ts/${url}`,
    data
  )
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiTsdpManual() {
  return {
    postCreateTransactionManualDetail: createApiFunction(
      "post",
      "transaction-d-manual/create"
    ),
    getAllTransactionManualDetail: createApiFunction(
      "get",
      "transaction-d-manual/list"
    ),
  };
}
