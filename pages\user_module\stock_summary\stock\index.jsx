import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";

import { <PERSON><PERSON>, Stack, Panel, ButtonGroup, Breadcrumb } from "rsuite";

import ContainerLayout from "@/components/layout/ContainerLayout";

// Import Table
import OverstockTable from "./Overstock";
import UnderstockTable from "./UnderStock";
import ApiStockSummaryOra from "@/pages/api/stock_summary/api_stock_summary";

export default function StockReport() {
  const router = useRouter();

  const [moduleName, setModuleName] = useState("");

  const [props, setProps] = useState([]);

  const [stockData, SetStockData] = useState({
      overstock:[],
      understock:[]
  })

  const HandleGetAllData = async ()=>{
    try {
      const res = await ApiStockSummaryOra().getAllStock();
      if (res.status === 200) {
        console.log("res ", res.data)
        console.log("res overstock", res.data.overstock)
          SetStockData({
            ...stockData, 
            overstock: res.data.overstock ? res.data.overstock : [],
            understock: res.data.understock ? res.data.understock: []
          })
      }else if (res.status ===400){
          console.log("error on response", res.message)
      }else {
          console.log("error on response", res.message)
      }
  } catch (error) {
      console.log("error data ", error)
  }
  }

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("stock_summary/stock")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
    }
    HandleGetAllData()
  }, []);

  useEffect(()=>{
    console.log("stock data ", stockData)
  },[stockData])

  const [step, setStep] = useState(0);

  return (
    <>
      <div>
        <Head>
          <title>Stock Report</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item active>
              {moduleName ? moduleName : "User Module"}
            </Breadcrumb.Item>
          </Breadcrumb>
          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h3>Stock Data</h3>
              <ButtonGroup>
                <Button
                  onClick={() => setStep(0)}
                  active={step === 0}
                  appearance="primary"
                >
                  Overstock
                </Button>
                <Button
                  onClick={() => setStep(1)}
                  active={step === 1}
                  appearance="primary"
                >
                  Understock
                </Button>
              </ButtonGroup>
            </Stack>
          </Panel>
          <div>
            <Panel bordered>
              {step == 0 ? (
                <OverstockTable employee_id={props.employee_id} stockData={stockData.overstock} />
              ) : step == 1 ? (
                <UnderstockTable employee_id={props.employee_id} stockData={stockData.understock} />
              ) : null}
            </Panel>
          </div>
        </div>
      </ContainerLayout>
    </>
  );
}
