import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/manufacturer${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function LkvMasterManufacturer(){
    return{
        getAllMasterManufacturer: createApiFunction("get", "/get/all"),
        getAllMasterManufacturerActive : createApiFunction("get", "/get/all/active"),
        createMasterManufacturer: createApiFunction("post", "/create"),
        updateMasterManufacturer: createApiFunction("put", "/edit"),
        updateStatusMasterManufacturer: createApiFunction("put", "/active")
    }
}