import ContainerLayout from '@/components/layout/ContainerLayout';

export default function GASidebar() {
  return (
    <ContainerLayout
      title="General Administration"
      customNavMenu={[
        {
          name: 'Menu Management',
          route: 'menu_management',
          icon: 'faTableList',
        },
        {
          name: 'Module Management',
          route: 'module_management',
          icon: 'faBook',
        },
        {
          name: 'User Management',
          route: 'user_management',
          icon: 'faUserAlt',
        },
        {
          name: 'Department Management',
          route: 'department_management',
          icon: 'faBuilding',
        },
      ]}
    >
      <div className="col-9 p-5">
        <h1>GA Dashboard</h1>
      </div>
    </ContainerLayout>
  );
}
