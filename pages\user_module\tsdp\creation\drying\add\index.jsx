import ContainerLayout from "@/components/layout/ContainerLayout";
import ApiTsDetail from "@/pages/api/ts/api_ts_detail";
import Head from "next/head";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import {
  Breadcrumb,
  Button,
  Form,
  InputGroup,
  Panel,
  Stack,
  Uploader,
  Input,
  SelectPicker,
  DatePicker,
  Table,
  Modal,
  Pagination,
  toaster,
  Loader,
  AutoComplete,
  RadioGroup,
  Radio,
  Divider,
  Column,
} from "rsuite";
import ApiMasterLine from "@/pages/api/ts/api_ts_master_line";
import ApiTsdpTH from "@/pages/api/tsdp/transaction_h/api_tsdp_transaction_h";
import API_TsMasterLine from "@/pages/api/ts/api_ts_master_line";
import ApiTsdpTDDrying from "@/pages/api/tsdp/api_tsdp_td_drying";
import ApiTsdpTDAttachments from "@/pages/api/tsdp/api_tsdp_td_attachments";

import API_TsMatrixCategoriesParameter from "@/pages/api/ts/api_ts-matrix-categories-parameter";

import Messages from "@/components/Messages";

import ExpandOutlineIcon from "@rsuite/icons/ExpandOutline";

export default function AddDrying() {
  const router = useRouter();
  const [moduleName, setModuleName] = useState("");
  const [props, setProps] = useState([]);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const emptyFormValue = {
    id_line: null,
    id_header_trans: null,
    line_type: null,
    sediaan_type: null,
    product_code: null,
    product_name: null,
    batch_no: null,
    production_scale: null,
    trial_focus: null,
    ppi_no: null,
    process_purpose: null,
    background: null,
    process_date: null,
    drying_date: null,
    drying_lod: null,
    drying_product_temp: null,
    drying_exhaust_temp: null,
    drying_remarks: null,
    spv_employee_id: null,
    ts_detail_trans: [],
  };
  const emptyDetailForm = {
    id_matrix1: null,
    id_categories1: null,
    value1: null,

    id_matrix2: null,
    id_categories2: null,
    value2: null,

    id_matrix3: null,
    id_categories3: null,
    value3: null,
  };
  const [formValue, setFormValue] = useState(emptyFormValue);
  const [formDetailValue, setFormDetailValue] = useState(emptyDetailForm);

  //dropdown
  const [dataLineAutomate, setDataLineAutomate] = useState([]);
  const [dataLineManual, setDataLineManual] = useState([]);
  const [dataBatch, setDataBatch] = useState([]);
  const [dataBatchAutoComplete, setDataBatchAutoComplete] = useState([]);
  const [dataProduct, setDataProduct] = useState([]);
  const [dataSpv, setDataSpv] = useState([]);
  const [modalGranulasi, setModalGranulasi] = useState(false);
  const [pengeringanDetail, setPengeringanDetail] = useState([]);
  const [tsMatrixCategories, setTsMatrixCategories] = useState([]);
  const [editIndex, setEditIndex] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedApiType, setSelectedApiType] = useState(null);
  const [dataDetail, setDataDetail] = useState([]);
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const { HeaderCell, Cell, Column } = Table;
  const [showSelectedDetailModal, setShowSelectedDetailModal] = useState(false);

  const [selectedDetail, setSelectedDetail] = useState(null);
  const [isDetailValid, setIsDetailValid] = useState(false);

  const dataJenisSediaan = ["Kapsul", "Tablet", "Sirup"].map((item) => ({
    label: item,
    value: item,
  }));

  const dataSkalaProduksi = ["Pilot", "Commercial"].map((item) => ({
    label: item,
    value: item,
  }));

  const dataFokusTrial = ["Carry Over", "Diversifikasi", "Others"].map(
    (item) => ({ label: item, value: item })
  );

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("tsdp/creation/binder")
      );

      if (validateUserAccess.length === 0) {
        // router.push("/dashboard");
        return;
      }
    }
  }, []);

  useEffect(() => {
    if (formValue.line_type === 1) {
      const isValid = formValue.ts_detail_trans?.length > 0 || false;
      setIsDetailValid(isValid);
    } else if (formValue.line_type === 0) {
      setIsDetailValid(pengeringanDetail.length > 0);
    } else {
      setIsDetailValid(false);
    }
  }, [formValue.line_type, formValue.ts_detail_trans, pengeringanDetail]);

  const handleDetailGranulasi = () => {
    const selectedMatrix = tsMatrixCategories.find(
      (item) => item.value === formDetailValue.id_matrix1
    );

    const newDetail = {
      id_matrix: formDetailValue.id_matrix1,
      matrix_label: selectedMatrix?.label,
      id_categories: selectedMatrix?.id_categories || null,
      value: formDetailValue.value1,
    };

    setPengeringanDetail((prevDetails) => [...prevDetails, newDetail]);
  };
  console.log("ini detail pengeringan", pengeringanDetail);

  const handleRemoveDetail = (indexToRemove, type) => {
    if (type === "granulasi") {
      setPengeringanDetail((prevDetails) =>
        prevDetails.filter((_, index) => index !== indexToRemove)
      );
    }
  };

  const handleRemoveAPIDetail = (index) => {
    setFormValue((prevFormValue) => {
      const existingDetail = [...(prevFormValue.ts_detail_trans || [])];
      existingDetail.splice(index, 1);

      const newState = { ...prevFormValue, ts_detail_trans: existingDetail };

      if (newState.line_type === 1) {
        setIsDetailValid(
          newState.ts_detail_trans && newState.ts_detail_trans.length > 0
        );
      }

      return newState;
    });
  };

  const handleGetTsMatrixCategoriesApi = async () => {
    try {
      const response =
        await API_TsMatrixCategoriesParameter().getAllActiveTsMatrixCategoriesParameter();

      if (response.status === 200 && response.data) {
        const formattedData = response.data.map((item) => ({
          label: item.parameter_name,
          value: item.id_matrix,
          id_categories: item.id_categories,
          main_category_name: item.main_category_name,
        }));

        setTsMatrixCategories(formattedData);
      }
    } catch (error) {
      console.error("Error fetching matrix categories:", error);
    }
  };

  const handelGetManualMasterLineApi = async () => {
    try {
      const response = await API_TsMasterLine().getManualMasterLine();
      if (response.status === 200 && response.data) {
        const formatMasterLine = response.data.map((item) => ({
          label: item.line_description,
          value: item.id_line,
        }));
        setTsManualMasterLine(formatMasterLine);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const getDetailApiData = async (type) => {
    try {
      const res = await ApiTsDetail().getApiData({
        type_api: type,
      });

      if (res.status === 200) {
        console.log("API Response:", res.data);

        let parsedData;
        if (typeof res.data === "string") {
          try {
            parsedData = JSON.parse(res.data);
          } catch (e) {
            console.error("Error parsing JSON:", e);
            parsedData = res.data;
          }
        } else {
          parsedData = res.data;
        }

        const parameters = [];
        const values = [];

        if (typeof parsedData === "object" && parsedData !== null) {
          Object.entries(parsedData).forEach(([key, val]) => {
            parameters.push(key);
            values.push(val);
          });
        }

        setFormValue((prevFormValue) => {
          const existingDetail = prevFormValue.ts_detail_trans || [];

          const typeExists = existingDetail.some(
            (detail) => detail.type === type
          );

          if (typeExists) {
            return {
              ...prevFormValue,
              ts_detail_trans: existingDetail.map((detail) =>
                detail.type === type
                  ? {
                      ...detail,
                      data: res.data,
                      parsedData: parsedData,
                      parameters: parameters,
                      values: values,
                    }
                  : detail
              ),
            };
          } else {
            const newDetail = {
              data: res.data,
              parsedData: parsedData,
              type: type,
              parameters: parameters,
              values: values,
            };

            console.log("Adding new detail:", newDetail);

            return {
              ...prevFormValue,
              ts_detail_trans: [...existingDetail, newDetail],
            };
          }
        });

        setPage(1);
        setShowDetailModal(false);
        setSelectedApiType(null);

        toaster.push(Messages("success", "Data successfully added!"), {
          placement: "topCenter",
          duration: 3000,
        });
      } else {
        toaster.push(
          Messages("error", "Failed to fetch data. Please try again."),
          { placement: "topCenter", duration: 5000 }
        );
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
      toaster.push(
        Messages("error", "An error occurred while fetching data."),
        { placement: "topCenter", duration: 5000 }
      );
    }
  };

  const fetchData = async () => {
    try {
      const resProduct = await ApiTsDetail().getAllOfProduct();
      const resSpv = await ApiTsDetail().getAllActiveSpv();
      const resLine = await ApiMasterLine().getAutomateMasterLine();
      const reslineManual = await API_TsMasterLine().getManualMasterLine();
      const resBatchCode = await ApiTsdpTH().getAllTsTransactionHeader();

      const resAllDetail = await ApiTsDetail().getAllDetail();

      setDataProduct(resProduct.data ? resProduct.data : []);
      setDataSpv(resSpv.data ? resSpv.data : []);

      setDataLineAutomate(resLine.data ? resLine.data : []);

      setDataDetail(resAllDetail.data ? resAllDetail.data : []);

      if (reslineManual?.data) {
        const formatMasterLine = reslineManual.data.map((item) => ({
          id_line: item.id_line,
          line_description: item.line_description,
          label: item.line_description,
          value: item.id_line,
        }));
        setDataLineManual(formatMasterLine);
        console.log("Formatted Manual Line Data:", formatMasterLine);
      } else {
        setDataLineManual([]);
      }

      setDataBatch(resBatchCode.data ? resBatchCode.data : []);
      const batchCodeMapped = resBatchCode?.data?.map((item) => item.batch_no);
      setDataBatchAutoComplete(batchCodeMapped ? batchCodeMapped : []);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  useEffect(() => {
    fetchData();
    handleGetTsMatrixCategoriesApi();
  }, []);

  useEffect(() => {
    console.log("Line Type:", formValue.line_type);
    console.log("dataLineAutomate:", dataLineAutomate);
    console.log("dataLineManual:", dataLineManual);
  }, [formValue.line_type, dataLineAutomate, dataLineManual]);

  const [inputValue, setInputValue] = useState(formValue.batch_no || "");
  const [disabledNotFound, setDisabledNotFound] = useState(false);

  useEffect(() => {
    console.log("dataBatch", dataBatch);
    console.log("formValue", formValue);

    const matched = dataBatch.find(
      (batch) => batch.batch_no === formValue.batch_no
    );

    if (matched) {
      setFormValue((prev) => ({
        ...prev,
        id_header_trans: matched.id_header_trans,
        id_line: matched.id_line,
        sediaan_type: matched.sediaan_type,
        product_code: matched.product_code,
        product_name: matched.product_name,
        production_scale: matched.production_scale,
        trial_focus: matched.trial_focus,
        ppi_no: matched.ppi_no,
        process_purpose: matched.process_purpose,
        background: matched.background,
        process_date: matched.process_date,
        line_type: matched.line_type,
      }));
      setDisabledNotFound(true);
    } else {
      setDisabledNotFound(false);
      setFormValue((prev) => ({
        ...prev,
        id_line: null,
        sediaan_type: null,
        product_code: null,
        product_name: "",
        production_scale: null,
        trial_focus: null,
        ppi_no: "",
        process_purpose: "",
        background: "",
        process_date: null,
        line_type: null,
      }));
    }
  }, [inputValue]);

  const [formAttachments, setFormAttachments] = useState([]);

  const handleGetAttachmentsByType = (type) => {
    const attachments = formAttachments.filter(
      (attachment) => attachment.type === type
    );
    return attachments?.map((attachment) => ({
      name: attachment?.data?.name,
      fileKey: attachment?.data?.fileKey,
      url: attachment?.data
        ? URL.createObjectURL(attachment?.data.blobFile)
        : "",
      size:
        attachment?.data?.blobFile?.size > 1024 * 1024
          ? `${(attachment?.data?.blobFile?.size / (1024 * 1024)).toFixed(2)}MB`
          : `${(attachment?.data?.blobFile?.size / 1024).toFixed(2)}KB`,
    }));
  };

  const handleUploadAttachments = (file, type) => {
    setFormAttachments((prevAttachments) => [
      ...prevAttachments,
      {
        type,
        data: file,
      },
    ]);
  };

  const handleRemoveAttachments = (file) => {
    setFormAttachments((prevAttachments) => {
      const newAttachments = [...prevAttachments];
      const index = newAttachments.findIndex(
        (attachment) => attachment.data.fileKey === file.fileKey
      );
      if (index !== -1) {
        newAttachments.splice(index, 1);
      }
      return newAttachments;
    });
  };

  const handleAddManualApi = async () => {
    setIsSubmitting(true);
    let error = false;
    const newErrors = {};

    // Validasi form
    const requiredFields = [
      { key: "batch_no", label: "Kode Batch" },
      { key: "line_type", label: "Line Type" },
      { key: "id_line", label: "Line" },
      { key: "sediaan_type", label: "Jenis Sediaan" },
      { key: "product_code", label: "Kode Produk" },
      { key: "product_name", label: "Nama Produk" },
      { key: "production_scale", label: "Skala Produksi" },
      { key: "trial_focus", label: "Fokus Trial" },
      { key: "ppi_no", label: "No PPI" },
      { key: "process_purpose", label: "Tujuan Proses" },
      { key: "background", label: "Background" },
      { key: "process_date", label: "Tanggal Proses" },
      { key: "drying_date", label: "Tanggal Pengeringan" },
      { key: "drying_lod", label: "LOD" },
      { key: "drying_product_temp", label: "Pengeringan Produk" },
      { key: "drying_exhaust_temp", label: "Pengeringan Exhaust" },
      { key: "drying_remarks", label: "Pengeringan Remarks" },
      { key: "spv_employee_id", label: "Spv" },
    ];

    requiredFields.forEach((field) => {
      if (field.key === "line_type") {
        if (formValue[field.key] !== 0 && formValue[field.key] !== 1) {
          error = true;
          newErrors[field.key] = `${field.label} harus diisi`;
        }
      } else if (!formValue[field.key]) {
        error = true;
        newErrors[field.key] = `${field.label} harus diisi`;
      }
    });

    setErrors(newErrors);

    if (error) {
      toaster.push(
        Messages("error", "Harap isi semua field yang wajib diisi!"),
        { placement: "topCenter", duration: 5000 }
      );
      setIsSubmitting(false);
      return;
    }

    try {
      if (formValue.line_type === 1) {
        // Automate
        const autoDetailPayload = formValue.ts_detail_trans.map((detail) => {
          let parsedData;
          try {
            parsedData =
              typeof detail.data === "string"
                ? JSON.parse(detail.data)
                : detail.data;
          } catch (e) {
            console.error("Error parsing JSON:", e);
            parsedData = {};
          }

          return {
            Type: "Drying",
            Value: JSON.stringify(parsedData),
            Description: formValue.ppi_no,
            Parameter: "full_data",
            created_by: props.employee_id,
          };
        });

        const autoPayload = {
          id_header_trans: formValue.id_header_trans || 0,
          line_type: formValue.line_type,
          id_line: formValue.id_line,
          sediaan_type: formValue.sediaan_type,
          product_code: formValue.product_code,
          product_name: formValue.product_name,
          batch_no: formValue.batch_no,
          production_scale: formValue.production_scale,
          trial_focus: formValue.trial_focus,
          ppi_no: formValue.ppi_no,
          process_date: formValue.process_date,
          process_purpose: formValue.process_purpose,
          background: formValue.background,
          drying_date: formValue.drying_date,
          drying_lod: formValue.drying_lod,
          drying_product_temp: formValue.drying_product_temp,
          drying_exhaust_temp: formValue.drying_exhaust_temp,
          drying_remarks: formValue.drying_remarks,
          spv_employee_id: formValue.spv_employee_id,
          created_by: props.employee_id,
          drying_detail: autoDetailPayload,
        };

        const resHeader = await ApiTsdpTDDrying().postTsTransactionDryingAuto(
          autoPayload
        );
        console.log("resHeader", resHeader);
        if (resHeader.status === 200) {
          const id_header_trans = resHeader.data;
          let failedUpload = 0;

          // Step 2: Upload attachments
          for (const fileItem of formAttachments) {
            if (fileItem.data !== undefined) {
              const formData = new FormData();
              formData.append(
                "Files",
                fileItem.data.blobFile,
                fileItem.data.name
              );
              formData.append("id_header_trans", id_header_trans);
              formData.append("type", "Drying");
              formData.append("path", "ts");
              formData.append("created_by", props.employee_id);

              const postRes =
                await ApiTsdpTDAttachments().postTsTransactionUpload(formData);
              if (postRes.status !== 200) {
                failedUpload++;
              }
            }
          }

          // Step 3: Handle upload result
          if (failedUpload === 0) {
            toaster.push(Messages("success", "Success Save Data!"), {
              placement: "topCenter",
              duration: 5000,
            });

            // setFormValue(emptyFormValue);
            // setFormAttachments([]);
            // setCurrentState(1);

            // Reload page after successful save
          } else {
            toaster.push(
              Messages("error", "Error: Failed to upload attachments!"),
              { placement: "topCenter", duration: 5000 }
            );
            setIsSubmitting(false);
          }
        } else {
          console.log("Error in postTsTransactionBinder:", resHeader.message);
          toaster.push(
            Messages(
              "error",
              `Error: ${resHeader.message || "Failed to save data"}!`
            ),
            { placement: "topCenter", duration: 5000 }
          );
          setIsSubmitting(false);
        }
      } else {
        const pengeringanDetailPayload = pengeringanDetail.map((detail) => ({
          type: "Drying",
          value: detail.value,
          id_matrix: detail.id_matrix,
          id_categories: detail.id_categories,
          created_by: props.employee_id,
        }));

        const payload = {
          id_header_trans: formValue.id_header_trans || 0,
          line_type: formValue.line_type,
          id_line: formValue.id_line,
          sediaan_type: formValue.sediaan_type,
          product_code: formValue.product_code,
          product_name: formValue.product_name,
          batch_no: formValue.batch_no,
          production_scale: formValue.production_scale,
          trial_focus: formValue.trial_focus,
          ppi_no: formValue.ppi_no,
          process_date: formValue.process_date,
          process_purpose: formValue.process_purpose,
          background: formValue.background,
          drying_date: formValue.drying_date,
          drying_lod: formValue.drying_lod,
          drying_product_temp: formValue.drying_product_temp,
          drying_exhaust_temp: formValue.drying_exhaust_temp,
          drying_remarks: formValue.drying_remarks,
          spv_employee_id: formValue.spv_employee_id,
          created_by: props.employee_id,
          drying_detail: pengeringanDetailPayload,
        };
        const resHeader = await ApiTsdpTDDrying().postTsTransactionDrying(
          payload
        );

        if (resHeader.status === 200) {
          const id_header_trans = resHeader.data;
          let failedUpload = 0;

          // Step 2: Upload attachments
          for (const fileItem of formAttachments) {
            if (fileItem.data !== undefined) {
              const formData = new FormData();
              formData.append(
                "Files",
                fileItem.data.blobFile,
                fileItem.data.name
              );
              formData.append("id_header_trans", id_header_trans);
              formData.append("type", "Drying");
              formData.append("path", "ts");
              formData.append("created_by", props.employee_id);

              const postRes =
                await ApiTsdpTDAttachments().postTsTransactionUpload(formData);
              if (postRes.status !== 200) {
                failedUpload++;
              }
            }
          }

          // Step 3: Handle upload result
          if (failedUpload === 0) {
            toaster.push(Messages("success", "Success Save Data!"), {
              placement: "topCenter",
              duration: 5000,
            });

            // setFormValue(emptyFormValue);
            // setFormAttachments([]);
            // setCurrentState(1);

            // Reload page after successful save
          } else {
            toaster.push(
              Messages("error", "Error: Failed to upload attachments!"),
              { placement: "topCenter", duration: 5000 }
            );
            setIsSubmitting(false);
          }
        } else {
          console.log("Error in postTsTransactionBinder:", resHeader.message);
          toaster.push(
            Messages(
              "error",
              `Error: ${resHeader.message || "Failed to save data"}!`
            ),
            { placement: "topCenter", duration: 5000 }
          );
          setIsSubmitting(false);
        }
      }
    } catch (error) {
      console.log("Exception in handleAddManualApi:", error);
      toaster.push(
        Messages(
          "error",
          "Error: Something went wrong. Please try again later!"
        ),
        { placement: "topCenter", duration: 5000 }
      );
      setIsSubmitting(false);
    }
    window.location.reload();
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };
  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const getPaginatedData = (currentDataDetail, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return currentDataDetail.slice(start, end);
  };

  const getFilteredData = () => {
    if (
      !formValue.ts_detail_trans ||
      !Array.isArray(formValue.ts_detail_trans)
    ) {
      console.log(
        "ts_detail_trans is empty or not an array:",
        formValue.ts_detail_trans
      );
      return [];
    }

    console.log("Current ts_detail_trans:", formValue.ts_detail_trans);

    if (sortColumn && sortType) {
      return [...formValue.ts_detail_trans].sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];

        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }

        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }

    return formValue.ts_detail_trans;
  };

  const totalRowCount = formValue.ts_detail_trans.length;

  const DetailTable = () => {
    return (
      <Panel bordered bodyFill className="mt-3">
        <Table
          bordered
          cellBordered
          height={250}
          data={getPaginatedData(getFilteredData(), limit, page)}
          sortColumn={sortColumn}
          sortType={sortType}
          onSortColumn={handleSortColumn}>
          <Column width={70} align="center">
            <HeaderCell>No</HeaderCell>
            <Cell>
              {(rowData, rowIndex) => (page - 1) * limit + rowIndex + 1}
            </Cell>
          </Column>
          <Column flexGrow={2} align="center">
            <HeaderCell>Type</HeaderCell>
            <Cell dataKey="type" />
          </Column>
          <Column flexGrow={1}>
            <HeaderCell align="center">Action</HeaderCell>
            <Cell
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}>
              {(rowData, rowIndex) => (
                <div className="space-x-2">
                  <Button
                    appearance="ghost"
                    size="xs"
                    onClick={() => {
                      setSelectedDetail(rowData);
                      setShowSelectedDetailModal(true);
                    }}>
                    View Detail
                  </Button>
                  <Button
                    appearance="ghost"
                    color="red"
                    size="xs"
                    onClick={() => handleRemoveAPIDetail(rowIndex)}>
                    Remove
                  </Button>
                </div>
              )}
            </Cell>
          </Column>
        </Table>
        <div style={{ padding: 20 }}>
          <Pagination
            prev
            next
            first
            last
            ellipsis
            boundaryLinks
            maxButtons={5}
            size="xs"
            layout={["total", "-", "limit", "|", "pager", "skip"]}
            limitOptions={[10, 30, 50]}
            total={totalRowCount}
            limit={limit}
            activePage={page}
            onChangePage={setPage}
            onChangeLimit={handleChangeLimit}
          />
        </div>
      </Panel>
    );
  };

  const handleEditDetail = (index, type) => {
    if (type === "pengeringan") {
      const detailToEdit = pengeringanDetail[index];
      setFormDetailValue({
        id_matrix1: detailToEdit.id_matrix,
        value1: detailToEdit.value,
        id_categories1: detailToEdit.id_categories,
        id_matrix2: null,
        id_categories2: null,
        value2: null,
        id_matrix3: null,
        id_categories3: null,
        value3: null,
      });
      setEditIndex(index);
      setModalGranulasi(true);
    }
  };

  const parseAPIDate = (dateString) => {
    if (!dateString) return null;
    if (dateString instanceof Date) return dateString;

    return new Date(dateString);
  };

  return (
    <div>
      <div>
        <Head>
          <title>TS Create Pengeringan</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item>
              {moduleName ? moduleName : "User Module"}
            </Breadcrumb.Item>
            <Breadcrumb.Item active>TS Create Pengeringan</Breadcrumb.Item>
          </Breadcrumb>

          <Panel bordered className="mb-2">
            <Stack justifyContent="flex-start">
              <h3>TS Create Pengeringan</h3>
            </Stack>
            <hr />
            <hr />
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>
                  Kode Batch <span className="text-red-500">*</span>
                </Form.ControlLabel>

                <AutoComplete
                  data={dataBatchAutoComplete}
                  value={inputValue}
                  placeholder="Masukkan atau pilih kode batch"
                  onChange={(value) => {
                    setInputValue(value);
                    setFormValue({ ...formValue, batch_no: value });
                    if (value) {
                      setErrors({ ...errors, batch_no: null });
                    }
                  }}
                  style={{ width: "100%" }}
                />
                {errors.batch_no && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.batch_no}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Line Type <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <RadioGroup
                  name="line_type"
                  value={formValue.line_type}
                  onChange={(value) => {
                    const numericValue = Number(value);

                    setFormValue((prev) => ({
                      ...prev,
                      line_type: numericValue,
                      id_line: null,
                      ts_detail_trans: [],
                    }));
                    setPengeringanDetail([]);

                    setErrors((prev) => ({
                      ...prev,
                      line_type: null,
                    }));
                  }}
                  disabled={disabledNotFound}>
                  <Radio value={1}>Automate</Radio>
                  <Radio value={0}>Manual</Radio>
                </RadioGroup>
                {errors.line_type && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.line_type}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Line <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  name="id_line"
                  accepter={SelectPicker}
                  value={formValue.id_line}
                  data={
                    formValue.line_type === 1
                      ? dataLineAutomate
                      : dataLineManual
                  }
                  valueKey="id_line"
                  labelKey="line_description"
                  block
                  onChange={(value) => {
                    console.log("Line selected:", value);
                    setFormValue({ ...formValue, id_line: value });
                    setErrors({ ...errors, id_line: null });
                  }}
                  disabled={disabledNotFound}
                />
                {errors.id_line && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.id_line}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Jenis Sediaan <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  name="sediaan_type"
                  accepter={SelectPicker}
                  value={formValue.sediaan_type}
                  data={dataJenisSediaan}
                  valueKey="value"
                  labelKey="label"
                  block
                  onChange={(value) => {
                    setFormValue({ ...formValue, sediaan_type: value });
                    setErrors({ ...errors, sediaan_type: null });
                  }}
                  disabled={disabledNotFound}
                />
                {errors.sediaan_type && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.sediaan_type}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Kode Produk <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  name="product_code"
                  accepter={SelectPicker}
                  value={formValue.product_code}
                  data={dataProduct}
                  valueKey="product_code"
                  labelKey="product_code"
                  block
                  onChange={(value) => {
                    setFormValue({ ...formValue, product_code: value });
                    setErrors({ ...errors, product_code: null });
                  }}
                  disabled={disabledNotFound}
                />
                {errors.product_code && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.product_code}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Nama Produk <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  placeholder="product_name"
                  value={formValue.product_name}
                  onChange={(value) => {
                    setFormValue({ ...formValue, product_name: value });
                    setErrors({ ...errors, product_name: null });
                  }}
                  disabled={disabledNotFound}
                />
                {errors.product_name && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.product_name}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Skala Produksi <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  name="production_scale"
                  accepter={SelectPicker}
                  value={formValue.production_scale}
                  data={dataSkalaProduksi}
                  valueKey="value"
                  labelKey="value"
                  block
                  onChange={(value) => {
                    setFormValue({ ...formValue, production_scale: value });
                    setErrors({ ...errors, production_scale: null });
                  }}
                  disabled={disabledNotFound}
                />
                {errors.production_scale && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.production_scale}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Fokus Trial <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  name="trial_focus"
                  accepter={SelectPicker}
                  value={formValue.trial_focus}
                  data={dataFokusTrial}
                  valueKey="value"
                  labelKey="value"
                  block
                  onChange={(value) => {
                    setFormValue({ ...formValue, trial_focus: value });
                    setErrors({ ...errors, trial_focus: null });
                  }}
                  disabled={disabledNotFound}
                />
                {errors.trial_focus && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.trial_focus}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  No PPI <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  placeholder="ppi_no"
                  value={formValue.ppi_no}
                  onChange={(value) => {
                    setFormValue({ ...formValue, ppi_no: value });
                    setErrors({ ...errors, ppi_no: null });
                  }}
                  disabled={disabledNotFound}
                />
                {errors.ppi_no && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.ppi_no}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Tujuan Proses <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  placeholder="process_purpose"
                  value={formValue.process_purpose}
                  onChange={(value) => {
                    setFormValue({ ...formValue, process_purpose: value });
                    setErrors({ ...errors, process_purpose: null });
                  }}
                  disabled={disabledNotFound}
                />
                {errors.process_purpose && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.process_purpose}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Background <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  placeholder="background"
                  value={formValue.background}
                  onChange={(value) => {
                    setFormValue({ ...formValue, background: value });
                    setErrors({ ...errors, background: null });
                  }}
                  disabled={disabledNotFound}
                />
                {errors.background && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.background}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Tanggal Proses <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  oneTap
                  value={parseAPIDate(formValue.process_date)}
                  format="dd-MM-yyyy"
                  onChange={(value) => {
                    setFormValue({ ...formValue, process_date: value });
                    setErrors({ ...errors, process_date: null });
                  }}
                  block
                  disabled={disabledNotFound}
                  placeholder="Pilih tanggal"
                />
                {errors.process_date && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.process_date}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>
                  Tanggal Pengeringan <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  oneTap
                  value={formValue.drying_date}
                  format="dd-MM-yyyy"
                  onChange={(value) => {
                    setFormValue({ ...formValue, drying_date: value });
                    setErrors({ ...errors, drying_date: null });
                  }}
                  block
                />
                {errors.drying_date && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.drying_date}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  LOD <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    placeholder="drying_lod"
                    value={formValue.drying_lod}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        drying_lod: parseFloat(value),
                      });
                      setErrors({ ...errors, drying_lod: null });
                    }}
                    type="number"
                  />
                  <InputGroup.Addon>%</InputGroup.Addon>
                </InputGroup>
                {errors.drying_lod && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.drying_lod}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Product Temp<span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    placeholder="drying_product_temp"
                    value={formValue.drying_product_temp}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        drying_product_temp: parseFloat(value),
                      });
                      setErrors({ ...errors, drying_product_temp: null });
                    }}
                    type="number"
                  />
                  <InputGroup.Addon>C</InputGroup.Addon>
                </InputGroup>
                {errors.drying_product_temp && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.drying_product_temp}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Exhaust Temp<span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    placeholder="drying_exhaust_temp"
                    value={formValue.drying_exhaust_temp}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        drying_exhaust_temp: parseFloat(value),
                      });
                      setErrors({ ...errors, drying_exhaust_temp: null });
                    }}
                    type="number"
                  />
                  <InputGroup.Addon>C</InputGroup.Addon>
                </InputGroup>
                {errors.drying_exhaust_temp && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.drying_exhaust_temp}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Pengeringan Remarks <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  placeholder="drying_remarks"
                  value={formValue.drying_remarks}
                  onChange={(value) => {
                    setFormValue({ ...formValue, drying_remarks: value });
                    setErrors({ ...errors, drying_remarks: null });
                  }}
                />
                {errors.drying_remarks && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.drying_remarks}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Lampiran</Form.ControlLabel>
                <Uploader
                  key={2}
                  listType="picture-text"
                  action=""
                  defaultFileList={handleGetAttachmentsByType(2)}
                  onUpload={(file) => handleUploadAttachments(file, 2)}
                  onRemove={(file) => handleRemoveAttachments(file)}
                  renderFileInfo={(file, fileElement) => {
                    return (
                      <>
                        <span>File Name: {file.name}</span>
                        <br />
                        <span style={{ color: "gray" }}>{file.size}</span>
                      </>
                    );
                  }}>
                  <Button appearance="ghost">Ambil Gambar</Button>
                </Uploader>
              </Form.Group>
              <Divider></Divider>
              {formValue.line_type === 1 ? (
                <>
                  <Button
                    appearance="primary"
                    onClick={() => setShowDetailModal(true)}
                    disabled={
                      formValue.ts_detail_trans &&
                      formValue.ts_detail_trans.length > 0
                    }>
                    + Data API Pengeringan
                  </Button>
                  <DetailTable />
                </>
              ) : formValue.line_type === 0 ? (
                <>
                  <Form.Group>
                    <Form.ControlLabel>
                      <h4 className="mb-4">Detail Pengeringan</h4>
                    </Form.ControlLabel>
                    <Button
                      appearance="primary"
                      startIcon={<ExpandOutlineIcon />}
                      onClick={() => setModalGranulasi(true)}>
                      Tambah detail Pengeringan
                    </Button>
                  </Form.Group>

                  <Panel bordered>
                    {pengeringanDetail.length > 0 ? (
                      pengeringanDetail.map((detail, index) => (
                        <div key={index}>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}>
                            <div>
                              <p>Matrix: {detail.matrix_label}</p>
                              <p>Value: {detail.value}</p>
                            </div>
                            <div style={{ display: "flex", gap: "8px" }}>
                              <Button
                                appearance="primary"
                                color="blue"
                                size="xs"
                                onClick={() =>
                                  handleEditDetail(index, "granulasi")
                                }>
                                Edit
                              </Button>
                              <Button
                                appearance="primary"
                                color="red"
                                size="xs"
                                onClick={() =>
                                  handleRemoveDetail(index, "granulasi")
                                }>
                                Remove
                              </Button>
                            </div>
                          </div>
                          {index < pengeringanDetail.length - 1 && (
                            <Divider style={{ margin: "10px 0" }} />
                          )}
                        </div>
                      ))
                    ) : (
                      <p>No Detail Added</p>
                    )}
                  </Panel>
                </>
              ) : (
                <p>Detail Pengeringan</p>
              )}

              <Form.Group>
                <Form.ControlLabel>
                  Spv <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  name="spv_employee_id"
                  accepter={SelectPicker}
                  value={formValue.spv_employee_id}
                  data={dataSpv}
                  valueKey="employee_id"
                  labelKey="employee_name"
                  block
                  onChange={(value) => {
                    setFormValue({ ...formValue, spv_employee_id: value });
                    setErrors({ ...errors, spv_employee_id: null });
                  }}
                />
                {errors.spv_employee_id && (
                  <div className="text-red-500 text-sm mt-1">
                    {errors.spv_employee_id}
                  </div>
                )}
              </Form.Group>
            </Form>
            <Button
              appearance="primary"
              style={{ backgroundColor: "#1fd306", marginTop: "5px" }}
              onClick={handleAddManualApi}
              disabled={isSubmitting || !isDetailValid}
              title={
                !isDetailValid
                  ? "Harap tambahkan detail Pengeringan terlebih dahulu"
                  : ""
              }>
              {isSubmitting ? <Loader content="Saving..." /> : "Save"}
            </Button>
          </Panel>

          <Modal
            open={modalGranulasi}
            onClose={() => {
              setModalGranulasi(false);
              setFormDetailValue(emptyDetailForm);
              setEditIndex(null);
            }}>
            <Modal.Header>
              <Modal.Title>
                {editIndex !== null
                  ? "Edit detail Pengeringan"
                  : "Tambah detail Pengeringan"}
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form fluid>
                <Form.Group>
                  <Form.ControlLabel>Matrix</Form.ControlLabel>
                  <Form.Control
                    name="id_matrix1"
                    accepter={SelectPicker}
                    data={tsMatrixCategories.filter(
                      (item) => item.main_category_name === "Pengeringan"
                    )}
                    valueKey="value"
                    labelKey="label"
                    block
                    value={formDetailValue.id_matrix1 || ""}
                    onChange={(value) => {
                      const selectedMatrix = tsMatrixCategories.find(
                        (item) => item.value === value
                      );
                      setFormDetailValue({
                        ...formDetailValue,
                        id_matrix1: value,
                        id_categories1: selectedMatrix?.id_categories || null,
                      });
                    }}
                  />
                </Form.Group>
                <Form.Group>
                  <Form.ControlLabel>Value</Form.ControlLabel>
                  <Form.Control
                    name="value1"
                    value={formDetailValue.value1 || ""}
                    onChange={(value) =>
                      setFormDetailValue({ ...formDetailValue, value1: value })
                    }
                  />
                </Form.Group>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button
                onClick={() => {
                  setModalGranulasi(false);
                  setFormDetailValue(emptyDetailForm);
                  setEditIndex(null);
                }}>
                Cancel
              </Button>
              <Button
                appearance="primary"
                disabled={!formDetailValue.id_matrix1}
                onClick={() => {
                  if (editIndex !== null) {
                    setPengeringanDetail((prev) =>
                      prev.map((item, index) =>
                        index === editIndex
                          ? {
                              id_matrix: formDetailValue.id_matrix1,
                              matrix_label: tsMatrixCategories.find(
                                (item) =>
                                  item.value === formDetailValue.id_matrix1
                              )?.label,
                              value: formDetailValue.value1,
                              id_categories: formDetailValue.id_categories1,
                            }
                          : item
                      )
                    );
                  } else {
                    const newDetail = {
                      id_matrix: formDetailValue.id_matrix1,
                      matrix_label: tsMatrixCategories.find(
                        (item) => item.value === formDetailValue.id_matrix1
                      )?.label,
                      id_categories: formDetailValue.id_categories1,
                      value: formDetailValue.value1,
                    };
                    setPengeringanDetail((prev) => [...prev, newDetail]);
                  }
                  setModalGranulasi(false);
                  setFormDetailValue(emptyDetailForm);
                  setEditIndex(null);
                }}>
                {editIndex !== null ? "Update" : "Save"}
              </Button>
            </Modal.Footer>
          </Modal>

          <Modal
            backdrop="static"
            open={showDetailModal}
            onClose={() => {
              setShowDetailModal(false);
              setSelectedApiType(null);
            }}
            overflow={false}>
            <Modal.Header>
              <Modal.Title>Detail Pengeringan</Modal.Title>
              <Modal.Body>
                <SelectPicker
                  name="selected_api_type"
                  value={selectedApiType}
                  data={dataDetail}
                  valueKey="type_api"
                  labelKey="type_api"
                  block
                  onChange={(value) => setSelectedApiType(value)}
                />
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowDetailModal(false);
                    setSelectedApiType(null);
                  }}
                  appearance="subtle">
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    getDetailApiData(selectedApiType);
                    setSelectedApiType(null);
                  }}
                  appearance="primary">
                  Add
                </Button>
              </Modal.Footer>
            </Modal.Header>
          </Modal>
          <Modal
            backdrop="static"
            open={showSelectedDetailModal}
            onClose={() => {
              setShowSelectedDetailModal(false);
              setSelectedDetail(null);
            }}
            overflow={false}>
            <Modal.Header>
              <Modal.Title>
                {selectedDetail?.type} Data in JSON Format
              </Modal.Title>
              <Modal.Body>
                <Modal.Body>
                  <pre>
                    {selectedDetail?.data &&
                      JSON.stringify(JSON.parse(selectedDetail?.data), null, 2)}
                  </pre>
                </Modal.Body>
              </Modal.Body>
            </Modal.Header>
          </Modal>
        </div>
      </ContainerLayout>
    </div>
  );
}
