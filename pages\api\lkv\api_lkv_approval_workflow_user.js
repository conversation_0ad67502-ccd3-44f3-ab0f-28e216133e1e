import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/approval_workflow_user${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function LkvApprovalWorkflowUser(){
    return{
        getAllApprovalWorkflowUser: createApiFunction("get", "/get/all"),
        getAllApprovalWorkflowUserActive : createApiFunction("get", "/get/all/active"),
        getAllApprovalWorkFlotUserAwid: createApiFunction("post", "/get/all/awid"),
        updatedApprovalWorkflowUser: createApiFunction("post", "/edit")
    }
}