import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  Loader,
  RadioGroup,
  Radio,
  useToaster,
  Notification,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import ApiLineClearanceGranulasi from "@/pages/api/pqr/line_clearance/granule/api_line_clearance_granulasi";
import ApiApprovalClearanceGranulasi from "@/pages/api/pqr/approval_clearance/granule/api_approval_clearance_granulasi";

export default function Clearance_Granulasi() {
  const toaster = useToaster();
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_trans_header");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [ppiDataState, setPPIDataState] = useState([]);
  const [lineClearanceData, setLineClearanceData] = useState([]);
  const [selectedLineClearanceData, setselectedLineClearanceData] = useState(
    []
  );
  const [showEditModal, setShowEditModal] = useState(false);

  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [approvalStatus, setApprovalStatus] = useState(null);
  const [password, setPassword] = useState("");
  const [remarks, setRemarks] = useState("");

  const emptyAddLineClearanceForm = {
    id_clearance: null,
    batch_code: "",
    product_code: "",
    clean_routine: null,
    calibration_routine: null,
    area_routine: null,
    label_routine: null,
    clean_periodic: null,
    calibration_periodic: null,
    area_periodic: null,
    label_preiodic: null,
    dust_periodic: null,
    discharge: null,
    bowl_machine: null,
    lid_container: null,
    floor: null,
    filter_clem: null,
    machine_panel: null,
    spray_gun: null,
    impeller_chopper: null,
    granulator_wetmill: null,
    connector_sifting: null,
    impeller_sifting: null,
    mesh: null,
    seal_sifting: null,
    fbd_sifting: null,
    label_process:null,
    product_container:null,
    create_by: sessionAuth
      ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`
      : "",
  };

  const emptyEditLineClearanceForm = {
    batch_code: "",
    product_code: "",
    clean_routine: null,
    calibration_routine: null,
    area_routine: null,
    label_routine: null,
    clean_periodic: null,
    calibration_periodic: null,
    area_periodic: null,
    label_preiodic: null,
    dust_periodic: null,
    discharge: null,
    bowl_machine: null,
    lid_container: null,
    floor: null,
    filter_clem: null,
    machine_panel: null,
    spray_gun: null,
    impeller_chopper: null,
    granulator_wetmill: null,
    connector_sifting: null,
    impeller_sifting: null,
    mesh: null,
    seal_sifting: null,
    fbd_sifting: null,
    label_process:null,
    product_container:null,
    update_by: sessionAuth
      ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`
      : "",
    id_clearance: null,
  };

  const emptyDeleteLineClearanceForm = {
    id_clearance: null,
    delete_by: sessionAuth
      ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`
      : "",
  };

  const [editLineClearanceForm, setEditLineClearanceForm] = useState(
    emptyEditLineClearanceForm
  );
  const [deleteLineClearanceForm, setDeleteLineClearanceForm] = useState(
    emptyDeleteLineClearanceForm
  );
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const filteredData = lineClearanceData.filter((rowData, i) => {
    const searchFields = [
      "id_clearance",
      "batch_code",
      "product_code",
      "create_date",
      "create_by",
      "update_date",
      "update_by",
      "delete_date",
      "delete_by",
      "is_active",
    ];
  
    const matchesSearch = searchFields.some((field) => {
      const fieldValue = rowData && rowData[field];
      
      return fieldValue !== undefined && fieldValue !== null
        ? fieldValue.toString().toLowerCase().includes(searchKeyword.toLowerCase())
        : false;
    });
  
    return matchesSearch;
  });
  
  

  // Jika data yang difilter kosong, tampilkan pesan
// if (filteredData.length === 0) {
//   console.log("No data matches the search criteria.");
// }

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };
  
  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };
  
  
  const totalRowCount = searchKeyword
    ? filteredData.length
    : lineClearanceData.length;

    const handleGetAllNeedApproveLineClear = async () => {
      try {
        const res = await ApiLineClearanceGranulasi().getAllNeedApproveLineClear();
        console.log("API Response:", res); // Debugging
    
        if (res.status === 200) {
          let data = res.data;
    
          // Jika data adalah objek, ubah menjadi array
          if (!Array.isArray(data)) {
            data = [data];
          }
    
          console.log("Data setelah diperbarui:", data); // Debugging
          setLineClearanceData(data); // Perbarui state dengan data terbaru
        } else {
          console.log("error on handleGetAllNeedApproveLineClear API", res.message);
          setLineClearanceData([]);
        }
      } catch (error) {
        console.log("error on catch handleGetAllNeedApproveLineClear", error);
        setLineClearanceData([]);
      }
    };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    console.log("credential = ", sessionAuth);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/operator")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetAllNeedApproveLineClear();
    }
  }, []);

  const formatDate = (rowData, dateKey) => {
    const formattedDate = rowData[dateKey];

    if (!formattedDate) return "-";

    const datePart = formattedDate.substring(0, 10);
    const timePart = formattedDate.substring(11, 16);

    const dateParts = datePart.split("-");
    const year = dateParts[0];
    const month = dateParts[1];
    const day = dateParts[2];

    return `${day}-${month}-${year} ${timePart}`;
  };

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification
        type={type}
        header={type === "success" ? "Success" : "Error"}
        closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const resetForm = () => {
    setEditLineClearanceForm(emptyEditLineClearanceForm);
    setDeleteLineClearanceForm(emptyDeleteLineClearanceForm);
    setErrorsAddForm({});
  };

  const HandleEditStatusApprovalClearanceApi = async (id_clearance, status_approval, password) => {
    setLoading(true);
    try {
      const finalRemarks = status_approval === 1 ? "" : remarks;
      const approvalBy = `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`;
  
      let res;
      if (status_approval === 1) {
        res = await ApiApprovalClearanceGranulasi().PutUpdateApprovalClear({
          id_clearance,
          status_approval,
          revise_remarks: finalRemarks,
          approval_by: approvalBy,
          employee_id: sessionAuth.employee_id,
          password: password,
        });
      } else {
        res = await ApiApprovalClearanceGranulasi().PutUpdateRevisiClear({
          id_clearance,
          status_approval,
          revise_remarks: finalRemarks,
          approval_by: approvalBy,
          employee_id: sessionAuth.employee_id,
          password: password,
        });
      }
  
      if (res.status === 200) {
        showNotification("success", "Approve status berhasil diperbaharui.");
        setShowApprovalModal(false);
        setRemarks("");
        setPassword("");
        setApprovalStatus(null);

        console.log("Memanggil handleGetAllNeedApproveLineClear untuk refresh data..."); 
        // Refresh data setelah approve atau revisi
        await handleGetAllNeedApproveLineClear(); // Panggil fungsi ini untuk refresh data
  
        // Reset halaman ke 1 setelah data diperbarui
        setPage(1);
      } else {
        console.error("Error on update status: ", res.message);
        if (res.message === "wrong username or password") {
          showNotification("error", "Salah input username atau password. Mohon coba lagi.");
          setShowApprovalModal(true);
        } else {
          setRemarks("");
          showNotification("error", `Error updating approval status: ${res.message}`);
        }
      }
    } catch (error) {
      console.error("Error on update status: ", error.message);
      showNotification("error", `Error updating approval status: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleGetLineClearByID = async (payload) => {
    try {
      const requestPayload = {
        id_clearance: payload,
      };

      const res = await ApiLineClearanceGranulasi().getLineClearByID(
        requestPayload
      );
      console.log("res = ", res);

      if (res.status === 200) {
        console.log("res", res.data);
        setselectedLineClearanceData(res.data);
        setEditLineClearanceForm(res.data);
        setDeleteLineClearanceForm({ id_clearance: res.data.id_clearance });
      } else {
        console.log("error on handleGetLineClearByID API", res.message);
      }
    } catch (error) {
      console.log("error on catch handleGetLineClearByID", error);
    }
  };


  // useEffect(() => {
  //   console.log("Approval Clearance Data : ", lineClearanceData);
  // }, [lineClearanceData]);

  useEffect(() => {
    console.log("Line Clearance Data setelah diperbarui:", lineClearanceData); // Debugging
  }, [lineClearanceData]);

  useEffect(() => {
    console.log("Approval Clearance Data by id = ", selectedLineClearanceData);
  }, [selectedLineClearanceData]);


  return (
    <>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>Line Clearance</Breadcrumb.Item>
                  <Breadcrumb.Item active>Approval Granulasi</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Approval Clearance Granulasi</h5>
              </Stack>
            }></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <Stack spacing={10}>
                    <InputGroup inside>
                      <InputGroup.Addon>
                        <SearchIcon />
                      </InputGroup.Addon>
                      <Input
                        placeholder="cari"
                        value={searchKeyword}
                        onChange={handleSearch}
                      />
                      <InputGroup.Addon
                        onClick={() => {
                          setSearchKeyword("");
                          setPage(1);
                        }}
                        style={{
                          display: searchKeyword ? "block" : "none",
                          color: "red",
                          cursor: "pointer",
                        }}>
                        <CloseOutlineIcon />
                      </InputGroup.Addon>
                    </InputGroup>
                  </Stack>
                </Stack>
              }>
              <Table
                bordered
                cellBordered
                height={400}
                data={getPaginatedData(getFilteredData(), limit, page)} // Gunakan data terbaru
                autoHeight
                className="text-center"
                key={lineClearanceData.length}
                >
                <Column width={180} align="center" fullText>
                  <HeaderCell>ID Clearance</HeaderCell>
                  <Cell dataKey="id_clearance" />
                </Column>
                <Column width={180} fullText>
                  <HeaderCell align="center">Kode Batch</HeaderCell>
                  <Cell dataKey="batch_code" />
                </Column>
                <Column width={180} fullText>
                  <HeaderCell align="center">Kode Produk</HeaderCell>
                  <Cell dataKey="product_code" />
                </Column>
                <Column width={180} fullText dataKey="clean_routine">
                  <HeaderCell align="center">Clean Routine</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.clean_routine == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="clean_routine">
                  <HeaderCell align="center">Calibration Routine</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.calibration_routine == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="area_routine">
                  <HeaderCell align="center">Area Routine</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.area_routine == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="label_routine">
                  <HeaderCell align="center">Label Routine</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.label_routine == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="clean_periodic">
                  <HeaderCell align="center">Clean Periodic</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.clean_periodic == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="calibration_periodic">
                  <HeaderCell align="center">Calibration Periodic</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.calibration_periodic == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="area_periodic">
                  <HeaderCell align="center">Area Periodic</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.area_periodic == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="label_preiodic">
                  <HeaderCell align="center">Label Periodic</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.label_preiodic == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="dust_periodic">
                  <HeaderCell align="center">Dust Periodic</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.dust_periodic == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="discharge">
                  <HeaderCell align="center">Discharge</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.discharge == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="bowl_machine">
                  <HeaderCell align="center">Bowl Machine</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.bowl_machine == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="lid_container">
                  <HeaderCell align="center">Lid Container</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.lid_container == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="floor">
                  <HeaderCell align="center">Floor</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.floor == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="filter_clem">
                  <HeaderCell align="center">Filter Clem</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.filter_clem == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="machine_panel">
                  <HeaderCell align="center">Machine Panel</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.machine_panel == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="spray_gun">
                  <HeaderCell align="center">Spray Gun</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.spray_gun == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="impeller_chopper">
                  <HeaderCell align="center">Impeller Chopper</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.impeller_chopper == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="granulator_wetmill">
                  <HeaderCell align="center">granulator wetmill</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.granulator_wetmill == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="connector_sifting">
                  <HeaderCell align="center">Connector Sifting</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.connector_sifting == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="impeller_sifting">
                  <HeaderCell align="center">Impeller Sifting</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.impeller_sifting == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="mesh">
                  <HeaderCell align="center">Mesh</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.mesh == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="seal_sifting">
                  <HeaderCell align="center">Seal Sifting</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.seal_sifting == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="fbd_sifting">
                  <HeaderCell align="center">Fbd Sifting</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.fbd_sifting == 1) return "Y";
                      else return "N/A";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText>
                  <HeaderCell align="center">Status Approval</HeaderCell>
                  <Cell dataKey="status_approval" />
                </Column>
                <Column width={180} fullText dataKey="approval_by">
                  <HeaderCell align="center">Approval By</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.approval_by) return rowData.approval_by;
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="approval_date">
                  <HeaderCell align="center">Approval Date</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.approval_date)
                        return formatDate(rowData, "approval_date");
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="revise_remarks">
                  <HeaderCell align="center">Revise Remarks</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.revise_remarks) return rowData.revise_remarks;
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText>
                  <HeaderCell align="center">Create Date</HeaderCell>
                  <Cell dataKey="create_date">
                    {(rowData) => formatDate(rowData, "create_date")}
                  </Cell>
                </Column>
                <Column width={180} fullText>
                  <HeaderCell align="center">Create By</HeaderCell>
                  <Cell dataKey="create_by" />
                </Column>
                <Column width={180} fullText dataKey="update_date">
                  <HeaderCell align="center">Update Date</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.update_date)
                        return formatDate(rowData, "update_date");
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="update_by">
                  <HeaderCell align="center">Update By</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.update_by) return rowData.update_by;
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="delete_date">
                  <HeaderCell align="center">Delete Date</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.delete_date)
                        return formatDate(rowData, "delete_date");
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="delete_by">
                  <HeaderCell align="center">Delete By</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.delete_by) return rowData.delete_by;
                      else return "-";
                    }}
                  </Cell>
                </Column>
                <Column width={180} fullText dataKey="is_active">
                  <HeaderCell align="center">Is Active</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.is_active == 1) return "Active";
                      else return "Inactive";
                    }}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell></HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="link"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowApprovalModal(true);
                            handleGetLineClearByID(rowData.id_clearance);
                          }}>
                          Approve
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            {/* modal approve */}
<Modal
  backdrop="static"
  open={showApprovalModal}
  onClose={() => {
    setShowApprovalModal(false);
    setPassword("");
    setRemarks("");
    setApprovalStatus(null);
    setShowEditModal(false);
                setEditLineClearanceForm(emptyEditLineClearanceForm);
                setErrorsEditForm({});
  }}
  overflow={false}
  size="lg" // Ubah size menjadi "lg" untuk menampung konten yang lebih banyak
>
  <Modal.Header>
    <Modal.Title>Konfirmasi Approve</Modal.Title>
  </Modal.Header>
  <Modal.Body>
    <Stack direction="column" spacing={16} alignItems="stretch">
      <Panel bordered style={{ padding: "15px", borderRadius: "6px" }}>
        <Form fluid>
          <Form.Group>
            <Form.ControlLabel>Pilih</Form.ControlLabel>
            <RadioGroup
              inline
              name="approvalAction"
              value={approvalStatus}
              onChange={(value) => {
                setApprovalStatus(value);
                setPassword("");
                setRemarks("");
              }}
            >
              <Radio value={1}>
                <span style={{ fontWeight: "500", color: "#4CAF50" }}>Approve</span>
              </Radio>
              <Radio value={0}>
                <span style={{ fontWeight: "500", color: "#F44336" }}>Revisi</span>
              </Radio>
            </RadioGroup>
          </Form.Group>

          {approvalStatus === 0 && (
            <Form.Group>
              <Form.ControlLabel>Alasan</Form.ControlLabel>
              <Input
                as="textarea"
                rows={3}
                placeholder="Masukkan alasan revisi"
                value={remarks}
                onChange={(value) => setRemarks(value)}
                style={{ width: "100%" }}
              />
            </Form.Group>
          )}

          <Form.Group>
            <InputGroup inside style={{ maxWidth: "300px" }}>
              <Input
                type="password"
                placeholder="Masukkan Password"
                value={password}
                onChange={(value) => setPassword(value)}
              />
            </InputGroup>
          </Form.Group>
        </Form>
        <Modal.Footer>
    <Button
      appearance="subtle"
      onClick={() => {
        setShowApprovalModal(false);
        setPassword("");
        setRemarks("");
        setApprovalStatus(null);
      }}
    >
      Batal
    </Button>
    <Button
  appearance="primary"
  color={approvalStatus === 1 ? "green" : "red"}
  disabled={
    !password.trim() ||
    approvalStatus === null ||
    (approvalStatus === 0 && !remarks.trim())
  }
  style={{
    cursor:
      !password.trim() || approvalStatus === null ||
        (approvalStatus === 0 && !remarks.trim())
        ? "not-allowed"
        : "pointer",
    opacity:
      !password.trim() || approvalStatus === null ||
        (approvalStatus === 0 && !remarks.trim())
        ? 0.6
        : 1
  }}
  onClick={async () => {
    setLoading(true);
    await HandleEditStatusApprovalClearanceApi(
      selectedLineClearanceData.id_clearance,
      approvalStatus,
      password
    );
    setShowApprovalModal(false); // Tutup modal setelah proses selesai
    setPassword("");
    setRemarks("");
    setApprovalStatus(null);
    setLoading(false);
  }}
  loading={loading}
>
  {approvalStatus === 1 ? "Approve" : "Revisi"}
</Button>
  </Modal.Footer>
      </Panel>

      {/* Tambahkan konten dari modal edit di sini */}
      <Panel bordered style={{ padding: "15px", borderRadius: "6px" }}>
        <Form fluid>
          <Form.Group>
            <Form.ControlLabel>Batch Code</Form.ControlLabel>
            <Form.Control
              name="batch_code"
              value={editLineClearanceForm.batch_code}
              onChange={(value) => {
                setEditLineClearanceForm((prevFormValue) => ({
                  ...prevFormValue,
                  batch_code: value,
                }));
                setErrorsEditForm((prevErrors) => ({
                  ...prevErrors,
                  batch_code: undefined,
                }));
              }}
              readOnly // Tambahkan readOnly jika field tidak boleh diubah
            />
          </Form.Group>
          <Form.Group>
            <Form.ControlLabel>Product Code</Form.ControlLabel>
            <Form.Control
              name="product_code"
              value={editLineClearanceForm.product_code}
              readOnly // Tambahkan readOnly jika field tidak boleh diubah
            />
          </Form.Group>

          {/* Pembersihan Rutin */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>
              <p>Pembersihan Rutin, boleh ada sisa produk jika:</p>
              <ul>
                <li>a. Ganti batch prodak</li>
                <li>b. Formula sama tetapi kode produk berbeda.</li>
              </ul>
            </Form.ControlLabel>
            <RadioGroup
              name="clean_routine"
              inline
              value={editLineClearanceForm.clean_routine}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pastikan Kabel Kalibrasi */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Pastikan Kabel kalibrasi dan kualifikasi pada mesin masih berlaku.</Form.ControlLabel>
            <RadioGroup
              name="calibration_routine"
              inline
              value={editLineClearanceForm.calibration_routine}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pastikan Kebersihan Lantai dan Area Mesin */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>
              Pastikan kebersihan lantai dan area mesin High Shear Mixer Granulator GEA (PMA 800) (M01GEAS0001) serta lemari/ meja. Pastikan tidak ada dokumen, prodak antara dan bahan / material, dari produk sebelumnya.
            </Form.ControlLabel>
            <RadioGroup
              name="area_routine"
              inline
              value={editLineClearanceForm.area_routine}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pastikan Tidak Ada Label "SEDANG PROSES" */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Pastikan tidak ada label identitas mesin "SEDANG PROSES" dari produk sebelumnya yang tertinggal di ruangan.</Form.ControlLabel>
            <RadioGroup
              name="label_routine"
              inline
              value={editLineClearanceForm.label_routine}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pembersihan Berkala */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>
              <p>Pembersihan Berkala, Jika:</p>
              <ul>
                <li>a. Ganti produk</li>
                <li>b. Ganti batch tetapi sudah mencapai batas campaign batch (sesuai Supporting Document).</li>
                <li>c. Mesin tidak digunakan lebih dari hari yang sudah ditetapkan (sesuai Supporting Document pembersihan tiap mesin/ alat).</li>
              </ul>
            </Form.ControlLabel>
            <RadioGroup
              name="clean_periodic"
              inline
              value={editLineClearanceForm.clean_periodic}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pastikan Label Kalibrasi dan Kualifikasi */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Pastikan label kalibrasi dan kualifikasi pada mesin masih berlaku.</Form.ControlLabel>
            <RadioGroup
              name="calibration_periodic"
              inline
              value={editLineClearanceForm.calibration_periodic}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pastikan Kebersihan Lantai dan Area Mesin (Periodik) */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>
              Pastikan kebersihan lantai dan area mesin High Shear Mixer Granulator GEA PMA 800 (M0IGEA8000I) serta lemari / meja. Pastikan tidak ada dokumen, produk antara dan bahan / material, dari produk sebelumnya.
            </Form.ControlLabel>
            <RadioGroup
              name="area_periodic"
              inline
              value={editLineClearanceForm.area_periodic}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pastikan Label "SIAP PAKAI" */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Pastikan label "SIAP PAKAI" tersedia dan masih berlaku.</Form.ControlLabel>
            <RadioGroup
              name="label_preiodic"
              inline
              value={editLineClearanceForm.label_preiodic}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pastikan Dust Collector dan Filter */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Pastikan dust collector dan filter low return bersih dan terbebas dari debu serta dalam kondisi yang baik.</Form.ControlLabel>
            <RadioGroup
              name="dust_periodic"
              inline
              value={editLineClearanceForm.dust_periodic}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pastikan Kebersihan Area Mesin */}
          <Form.Group>
            <Form.ControlLabel>
              Pastikan kebersihan area mesin High Shear Mixer Granulator GEA PMA 800 (M0IGEA8000I) sesuai area part dibawah ini:
              <ul>
                <li>- Tidak terdapat sisa produk dari produk sebelumnya.</li>
                <li>- Tidak terdapat debu dan kotoran serta baik untuk digunakan.</li>
              </ul>
            </Form.ControlLabel>
          </Form.Group>

          {/* Discharge */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Discharge</Form.ControlLabel>
            <RadioGroup
              name="discharge"
              inline
              value={editLineClearanceForm.discharge}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Container / Bowl Mesin */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Container / bowl mesin</Form.ControlLabel>
            <RadioGroup
              name="bowl_machine"
              inline
              value={editLineClearanceForm.bowl_machine}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Tutup Container */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Tutup container</Form.ControlLabel>
            <RadioGroup
              name="lid_container"
              inline
              value={editLineClearanceForm.lid_container}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Product Container */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Product container</Form.ControlLabel>
            <RadioGroup
              name="product_container"
              inline
              value={editLineClearanceForm.product_container}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Lantai Panggung */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Lantai panggung</Form.ControlLabel>
            <RadioGroup
              name="floor"
              inline
              value={editLineClearanceForm.floor}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Filter dan Rangka Filter */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Filter dan rangka filter pastikan juga kekuatan dan kekencangan pemasangan klem.</Form.ControlLabel>
            <RadioGroup
              name="filter_clem"
              inline
              value={editLineClearanceForm.filter_clem}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Panel Mesin */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Panel mesin</Form.ControlLabel>
            <RadioGroup
              name="machine_panel"
              inline
              value={editLineClearanceForm.machine_panel}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Spray Gun Binder */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Spray Gun Binder</Form.ControlLabel>
            <RadioGroup
              name="spray_gun"
              inline
              value={editLineClearanceForm.spray_gun}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Impeller dan Chopper */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Impeller dan chopper</Form.ControlLabel>
            <RadioGroup
              name="impeller_chopper"
              inline
              value={editLineClearanceForm.impeller_chopper}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pastikan Kebersihan Area Mesin (Part 2) */}
          <Form.Group>
            <Form.ControlLabel>
              Pastikan kebersihan area mesin High Shear Mixer Granulator GEA PMA 800 (M01GEA80001) sesuai area / part di bawah ini:
              <ul>
                <li>- Tidak terdapat sisa produk dari produk sebelumnya.</li>
                <li>- Tidak terdapat debu dan kotoran serta baik untuk digunakan.</li>
              </ul>
            </Form.ControlLabel>
          </Form.Group>

          {/* Konektor Granulator ke Wet Mill */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Konektor Granulator ke Wet mill</Form.ControlLabel>
            <RadioGroup
              name="granulator_wetmill"
              inline
              value={editLineClearanceForm.granulator_wetmill}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Konektor Antara Selang Transfer dan Ayakan */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Konektor antara selang transfer dan ayakan (jika digunakan)</Form.ControlLabel>
            <RadioGroup
              name="connector_sifting"
              inline
              value={editLineClearanceForm.connector_sifting}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pisau Ayak/Impeller */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Pisau ayak/impeller</Form.ControlLabel>
            <RadioGroup
              name="impeller_sifting"
              inline
              value={editLineClearanceForm.impeller_sifting}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Mesh */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Mesh</Form.ControlLabel>
            <RadioGroup
              name="mesh"
              inline
              value={editLineClearanceForm.mesh}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Seal pada Mesin Ayak */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Seal pada mesin ayak</Form.ControlLabel>
            <RadioGroup
              name="seal_sifting"
              inline
              value={editLineClearanceForm.seal_sifting}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Konektor Antara Ayakan dan FBD */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>Konektor antara ayakan dan FBD</Form.ControlLabel>
            <RadioGroup
              name="fbd_sifting"
              inline
              value={editLineClearanceForm.fbd_sifting}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>

          {/* Pastikan Tidak Ada Label "SEDANG PROSES" (Part 2) */}
          <Form.Group className="flex justify-between items-center">
            <Form.ControlLabel>
              Pastikan tidak ada label identitas mesin "SEDANG PROSES" dari produk sebelumnya yang tertinggal di Arca High Shear Mixer Granulator GEA PMA 800 (MOIGEA80001).
            </Form.ControlLabel>
            <RadioGroup
              name="label_process"
              inline
              value={editLineClearanceForm.label_process}
              readOnly
            >
              <Radio value={1}>Y</Radio>
              <Radio value={0}>N/A</Radio>
            </RadioGroup>
          </Form.Group>
        </Form>
      </Panel>
    </Stack>
  </Modal.Body>
</Modal>

          </div>
        </div>
      </ContainerLayout>
    </>
  );
}
