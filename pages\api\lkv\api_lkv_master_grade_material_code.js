import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/material_code${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function LkvMasterMaterialCode(){
    return{
        getAllMasterMaterialCode: createApiFunction("get", "/get/all"),
        getAllMasterMaterialCodeActive : createApiFunction("get", "/get/all/active"),
        createMasterMaterialCode: createApiFunction("post", "/create"),
        updateMasterMaterialCode: createApiFunction("put", "/edit"),
        updateStatusMasterMaterialCode: createApiFunction("put", "/active")
    }
}