import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/outbound_reagen/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};

export default function OutboundReagenApi(){
    return {
        getOutboundRealization: createApiFunction("post", "get/realization"),
        outboundRealization: createApiFunction("put", "realization"),
        getAllOutbound: createApiFunction("get", "get/all/out"),
        getOutboundLocatorById: createApiFunction("post", "get/id"),
        getAllInbound: createApiFunction("get", "get/all/in"),
        postOutbound: createApiFunction("post", "create"),
    }
}