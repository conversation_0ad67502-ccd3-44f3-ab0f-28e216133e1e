import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import MainContent from '@/components/layout/MainContent';
import {
  Dropdown,
  Button,
  Form,
  ButtonToolbar,
  Toggle,
  TreePicker,
} from 'rsuite';
import Head from 'next/head';
import withReactContent from 'sweetalert2-react-content';
import Swal from 'sweetalert2';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import UseTestApi from '@/pages/api/userApi';
import ReviewerApproverApi from '@/pages/api/reviewerApprover';

export default function AddReviewerApprover() {
  const { GetAllRole, InsertReviewerApprover } = ReviewerApproverApi();
  const { GetTestApi } = UseTestApi();
  const MySwal = withReactContent(Swal);
  const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
  const [treePickerUserData, setTreePickerUserData] = useState([]);
  const [allUserData, setAllUserData] = useState([]);
  const [selectedUser, setSelectedUser] = useState({});
  const [employeeIdCreator, setEmployeeIdCreator] = useState('');
  const [moduleName, setModuleName] = useState('');
  const [allRole, setAllRole] = useState([]);
  const [selectedRole, setSelectedRole] = useState({});
  const [isActive, setIsActive] = useState(false);
  const router = useRouter();
  let path = 'masterdata/ReviewerApprover';
  let { data } = router.query;
  data ? (data = JSON.parse(data)) : data;
  const [breadcrumbsData, setBreadcrumbsData] = useState([]);

  const GetAllUserData = async () => {
    const { Data: userData } = await GetTestApi();

    if (userData !== null && userData !== undefined) {
      let treepickerData = [];
      userData.map((user) => {
        const data = {
          value: user.Employee_Id,
          label: `${user.Name} - [${user.Department}]`,
        };
        treepickerData.push(data);
      });

      setTreePickerUserData(treepickerData);
      setAllUserData(userData);
    }
  };

  const selectEmployeeIdChangeHandler = (value) => {
    const user = allUserData.filter((item) => item.Employee_Id === value);
    setSelectedUser({ ...user[0] });
  };

  const GetAllRoleData = async () => {
    const { data: roleData } = await GetAllRole();

    if (roleData !== null && roleData !== undefined && roleData) {
      setAllRole(roleData);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem('module_name');
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
      router.push('/');
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push('/dashboard');
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path),
    );

    if (validateUserAccess.length === 0) {
      router.push('/dashboard');
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ''
    ) {
      router.push('/dashboard');
      return;
    }

    const pathUrl = router.asPath;
    const asPathNestedRoutes = pathUrl.split('/').filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      asPathNestedRoutes[1],
      asPathNestedRoutes[2],
      asPathNestedRoutes[3],
    ];
    const capitalizeFirstLetter = (str) => {
      return str.charAt(0).toUpperCase() + str.slice(1);
    };
    const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
      const words = item.split(/(?=[A-Z])/);
      return words.map((word) => capitalizeFirstLetter(word)).join(' ');
    });
    setBreadcrumbsData(breadCrumbsResult);

    setModuleName(moduleNameValue);

    setEmployeeIdCreator(dataLogin.employee_id);

    GetAllUserData();
    GetAllRoleData();
  }, []);

  const selectedRoleChangeHandler = (value) => {
    setSelectedRole({ ...value });
  };

  //   submitHandler
  const submitHandler = async () => {
    if (
      selectedRole.Id_Role === null ||
      selectedRole.Id_Role === undefined ||
      selectedRole.Id_Role === '' ||
      selectedUser.Employee_Id === null ||
      selectedUser.Employee_Id === undefined ||
      selectedUser.Name === null ||
      selectedUser.Name === undefined
    ) {
      MySwal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Please fill all required data!',
      });
      return;
    } else {
      MySwal.showLoading();
      const isUserActive = isActive ? 1 : 0;

      const dataInput = {
        employee_id: selectedUser.Employee_Id,
        employee_name: selectedUser.Name,
        department_id: parseInt(selectedUser.Department_Id),
        is_active: isUserActive,
        role_id: parseInt(selectedRole.Id_Role),
        created_by: employeeIdCreator,
      };

      const { message, status } = await InsertReviewerApprover(dataInput);

      if (status === 200) {
        setIsSubmitButtonDisabled(true);
        MySwal.fire({
          position: 'center',
          icon: 'success',
          title: message,
          showConfirmButton: false,
          timer: 2500,
        });
        router.push('/user_module/masterdata/ReviewerApprover');
      } else {
        MySwal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'Update FAILED.',
        });
      }
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Add Review & Approver</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <MainContent>
          <ModuleContentHeader
            breadcrumbs={breadcrumbsData}
            module_name={moduleName}
          />

          <Form layout="horizontal" onSubmit={submitHandler}>
            <Form.Group>
              <Form.ControlLabel>Employee Name :</Form.ControlLabel>
              <TreePicker
                data={treePickerUserData}
                defaultExpandAll
                onChange={selectEmployeeIdChangeHandler}
                style={{ minWidth: 246 }}
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>Employee ID :</Form.ControlLabel>
              <Form.Control
                name="employee_id"
                type="text"
                value={
                  selectedUser.Employee_Id !== undefined &&
                  selectedUser.Employee_Id !== null
                    ? selectedUser.Employee_Id
                    : ''
                }
                style={{ maxWidth: 246 }}
                disabled={true}
              />
            </Form.Group>
            <Form.Group controlId="department">
              <Form.ControlLabel>Department Name :</Form.ControlLabel>
              <Form.Control
                name="department"
                type="text"
                value={
                  selectedUser.Department !== undefined &&
                  selectedUser.Department !== null
                    ? selectedUser.Department
                    : ''
                }
                style={{ maxWidth: 246 }}
                disabled={true}
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>Role :</Form.ControlLabel>
              <Dropdown
                title={
                  selectedRole.Role_Desc
                    ? selectedRole.Role_Desc
                    : '- Select Role -'
                }
                onSelect={selectedRoleChangeHandler}
              >
                <Dropdown.Item eventKey="">- Select Role -</Dropdown.Item>
                {allRole.length > 0 &&
                  allRole.map((role) => (
                    <Dropdown.Item key={role.Id_Role} eventKey={role}>
                      {role.Role_Desc}
                    </Dropdown.Item>
                  ))}
              </Dropdown>
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>Active</Form.ControlLabel>
              <Toggle
                checked={isActive}
                onChange={() => setIsActive((value) => !value)}
              />
            </Form.Group>

            <Form.Group>
              <ButtonToolbar>
                <Button
                  appearance="primary"
                  type="submit"
                  disabled={isSubmitButtonDisabled}
                >
                  Submit
                </Button>
                <Button appearance="default" onClick={() => router.back()}>
                  Cancel
                </Button>
              </ButtonToolbar>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}
