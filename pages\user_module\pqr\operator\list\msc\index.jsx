import { useEffect, useState, useCallback } from "react";
import Head from "next/head";
import { Breadcrumb, Stack, Panel, Tag, Form, Grid, Row, Col, Table, Button, Pagination, Modal, Loader, IconButton, InputGroup, Input, InputNumber, Checkbox, SelectPicker, useToaster, Notification } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiMasterdata_ppi from "@/pages/api/pqr/ppi/api_masterdata_ppi";
import ApiProduct from "@/pages/api/pqr/product/api_masterdata_product";
import ApiMSCWeight from "@/pages/api/pqr/weight_msc/api_weight_msc";

export default function MassaSiapCetakPage() {
    const toaster = useToaster();
    const [searchKeyword, setSearchKeyword] = useState("");
    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [idRouter, setIdRouter] = useState(null);
    const router = useRouter();
    const { IdHeader } = router.query;
    const [isEditMode, setIsEditMode] = useState(false);
    const [isViewMode, setIsViewMode] = useState(false);

    const emptyAddTransactionMscForm = {
        id_trans_header: null,
        berat_msc: null,
        percentage_berat_msc: null,
        remarks: null,
        bobot_min: null,
        bobot_max: null,
        bobot_std: null,
        create_by: sessionAuth ? sessionAuth.employee_name : "",
    };

    const [transactionDetailsDataState, setTransactionDetailDataState] = useState([]);
    const [ppiData, setPpiData] = useState(null);
    const [productData, setProductData] = useState(null);
    const [formDataHeader, setformDataHeader] = useState({});
    const [addTransactionMscForm, setAddTransactionMscForm] = useState(emptyAddTransactionMscForm);
    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [addLoading, setAddLoading] = useState(false);
    const [loading, setLoading] = useState(false);

    const HandleGetDetailTransactionHeader = async (id_trans_header) => {
        try {
            const apiTransactionHeader = ApiTransactionHeader();
            const response = await apiTransactionHeader.getPPITransactionHeaderById({ id_trans_header: parseInt(id_trans_header) });
            if (response.status === 200) {
                const data = response.data;
                setformDataHeader({
                    id_trans_header: data.id_trans_header,
                    id_ppi: data.id_ppi,
                    ppi_name: data.ppi_name,
                    batch_code: data.batch_code,
                    iot_desc: data.iot_desc,
                    line_desc: data.line_desc,
                    remarks: data.remarks,
                    wetmill: data.wetmill,
                    status_transaction: data.status_transaction,
                    create_date: data.create_date ? new Date(data.create_date).toLocaleDateString("en-GB") : "-",
                    create_by: data.create_by || "-",
                    update_date: data.update_date ? new Date(data.update_date).toLocaleDateString("en-GB") : "-",
                    update_by: data.update_by || "-",
                    delete_date: data.delete_date ? new Date(data.delete_date).toLocaleDateString("en-GB") : "-",
                    delete_by: data.delete_by || "-",
                });

                if (data.id_ppi) {
                    const ppiResponse = await HandleGetPPIById(data.id_ppi);

                    if (ppiResponse) {
                        setPpiData(ppiResponse);
                        if (ppiResponse.id_product) {
                            const productResponse = await HandleGetProductById(ppiResponse.id_product);
                            if (productResponse) {
                                setProductData(productResponse);
                            }
                        }
                    }
                }

                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetPPIById = async (id_ppi) => {
        try {
            const api = ApiMasterdata_ppi();
            const response = await api.GetMasterPPIById({ id_ppi: parseInt(id_ppi) });

            if (response.status === 200) {
                const data = response.data;
                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetProductById = async (id_product) => {
        try {
            const api = ApiProduct();
            const response = await api.getProductById({ id_product: parseInt(id_product) });

            if (response.status === 200) {
                const data = response.data;
                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetMSCWeightByIdTransHeader = async (id_trans_header) => {
        try {
            const api = ApiMSCWeight();
            const response = await api.getMSCByIdTransHeader({ id_trans_header: parseInt(id_trans_header) });

            if (response.status === 200) {
                const data = response.data;
                if (data) {
                    setAddTransactionMscForm({
                        id_trans_header: data.id_trans_header,
                        id_weight_msc: data.id_weight_msc,
                        berat_msc: data.berat_msc,
                        percentage_berat_msc: data.percentage_berat_msc,
                        remarks: data.remarks,
                        bobot_min: data.bobot_min,
                        bobot_max: data.bobot_max,
                        bobot_std: data.bobot_std,
                        create_by: sessionAuth ? sessionAuth.employee_name : "",
                    });
                }
                return data;
            } else {
                console.log("gagal mendapatkan data detail", response.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const handleSubmit = async () => {
        const errors = {};

        if (!addTransactionMscForm.berat_msc) {
            errors.berat_msc = "Berat MSC wajib diisi";
        }

        if (!addTransactionMscForm.percentage_berat_msc) {
            errors.percentage_berat_msc = "Persentase Berat MSC wajib diisi";
        }

        if (!addTransactionMscForm.remarks) {
            errors.remarks = "Remarks wajib diisi";
        }

        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }

        if (loading) return;

        setLoading(true);
        try {
            let res;

            if (isEditMode) {
                res = await ApiMSCWeight().updateMSC({
                    ...addTransactionMscForm,
                    id_trans_header: parseInt(IdHeader, 10),
                    id_weight_msc: addTransactionMscForm.id_weight_msc,
                    bobot_min: parseFloat(addTransactionMscForm.bobot_min),
                    bobot_max: parseFloat(addTransactionMscForm.bobot_max),
                    bobot_std: parseFloat(addTransactionMscForm.bobot_std),
                    update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                });
            } else {
                res = await ApiMSCWeight().createMSC({
                    ...addTransactionMscForm,
                    id_trans_header: parseInt(IdHeader, 10),
                    bobot_min: parseFloat(addTransactionMscForm.bobot_min),
                    bobot_max: parseFloat(addTransactionMscForm.bobot_max),
                    bobot_std: parseFloat(addTransactionMscForm.bobot_std),
                    create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                });
            }

            if (res.status === 200) {
                showNotification("success", isEditMode ? "Data Berhasil Diperbarui" : "Data Berhasil Ditambahkan");
                router.push(`/user_module/pqr/operator/list`);
                setAddTransactionMscForm(emptyAddTransactionMscForm);
            } else {
                console.log("gagal " + (isEditMode ? "memperbarui" : "menambah") + " data", res.message);
                showNotification("error", isEditMode ? "Gagal Memperbarui data" : "Gagal Menambah data");
            }
        }
        catch (error) {
            console.log("error gagal " + (isEditMode ? "memperbarui" : "menambah") + " data ", error);
            showNotification("error", "Terjadi Kesalahan Saat " + (isEditMode ? "Memperbarui" : "Menambahkan") + " Data");
        }
        finally {
            setLoading(false);
        }
    };

    const totalRowCount = searchKeyword ? filteredData.length : transactionDetailsDataState.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setSessionAuth(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else if (IdHeader) {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("pqr/operator")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }

            console.log("Router data:", IdHeader);
            setIdRouter(IdHeader);

            HandleGetDetailTransactionHeader(IdHeader);

            const { mode } = router.query;
            if (mode === 'edit') {
                setIsEditMode(true);
                setIsViewMode(false);
                HandleGetMSCWeightByIdTransHeader(IdHeader);
            } else if (mode === 'view') {
                setIsEditMode(false);
                setIsViewMode(true);
                HandleGetMSCWeightByIdTransHeader(IdHeader);
            } else {
                setIsEditMode(false);
                setIsViewMode(false);
            }
        }
    }, [router]);

    useEffect(() => {
        if (productData) {
            setAddTransactionMscForm((prev) => ({
                ...prev,
                bobot_min: parseFloat(productData.bobot_min),
                bobot_max: parseFloat(productData.bobot_max),
                bobot_std: parseFloat(productData.bobot_std),
            }));
        }
    }, [productData]);





    const showNotification = (type, message, duration = 2000) => {
        toaster.push(
            <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
                <p>{message}</p>
            </Notification>,
            { placement: "topEnd", duration }
        );
    };

    return (
        <div>
            <div>
                <Head>
                    <title>Transaksi Massa Siap Cetak</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>PQR</Breadcrumb.Item>
                                    <Breadcrumb.Item>List</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Transaction Header</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <Form layout="vertical">
                                    <Grid fluid>
                                        <Row style={{ marginBottom: "16px" }}>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>ID Transaksi Header</Form.ControlLabel>
                                                    <Form.Control name="id_trans_header" value={formDataHeader.id_trans_header} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                                                    <Form.Control name="ppi_name" value={formDataHeader.ppi_name} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Batch Code</Form.ControlLabel>
                                                    <Form.Control name="batch_code" value={formDataHeader.batch_code} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Wetmill</Form.ControlLabel>
                                                    <Form.Control name="wetmill" value={formDataHeader.wetmill === "Y" ? "Yes" : "No"} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}></Row>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Remarks</Form.ControlLabel>
                                                        <Form.Control name="remarks" value={formDataHeader.remarks} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Status Transaksi</Form.ControlLabel>
                                                        <Form.Control name="status_transaction" value={formDataHeader.status_transaction === 2 ? "Draft" : formDataHeader.status_transaction === 1 ? "Done" : "Dropped"} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                                                        <Form.Control name="product_code" value={productData?.product_code ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}><Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Bobot Minimal</Form.ControlLabel>
                                                    <Form.Control name="bobot_min" value={productData?.bobot_min ?? '-'} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Maximal</Form.ControlLabel>
                                                        <Form.Control name="bobot_max" value={productData?.bobot_max ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Standar</Form.ControlLabel>
                                                        <Form.Control name="bobot_std" value={productData?.bobot_std ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Core Foil</Form.ControlLabel>
                                                        <Form.Control name="bobot_core_foil" value={productData?.bobot_core_foil ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}></Row>
                                            <Row style={{ marginBottom: "24px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                                                        <Form.Control name="create_date" value={formDataHeader.create_date} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                                                        <Form.Control name="created_by" value={formDataHeader.create_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                                                        <Form.Control name="update_date" value={formDataHeader.update_date} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                                                        <Form.Control name="update_by" value={formDataHeader.update_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                                                    <Form.Control name="delete_date" value={formDataHeader.delete_date} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                                                        <Form.Control name="delete_by" value={formDataHeader.delete_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </Row>
                                    </Grid>
                                </Form>
                            </Stack>
                        }
                    />
                    <Panel
                        bordered
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Transaksi Massa Siap Cetak</h5>
                            </Stack>
                        }
                    >
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Berat Massa Siap Cetak (MSC)</Form.ControlLabel>
                                <InputGroup>
                                    <Form.Control
                                        name="berat_msc"
                                        type="number"
                                        min={0}
                                        placeholder="Massa(Kg)"
                                        value={addTransactionMscForm.berat_msc}
                                        onChange={(value) => {
                                            const beratMsc = parseFloat(value);
                                            let percent = '';

                                            if (
                                                !isNaN(beratMsc) &&
                                                productData &&
                                                !isNaN(parseFloat(productData.bobot_std)) &&
                                                parseFloat(productData.bobot_std) !== 0
                                            ) {
                                                percent = ((beratMsc * 100) / parseFloat(productData.bobot_std)).toFixed(2);
                                            }

                                            setAddTransactionMscForm((prev) => ({
                                                ...prev,
                                                berat_msc: parseFloat(value),
                                                percentage_berat_msc: parseFloat(percent),
                                            }));

                                            setErrorsAddForm((prev) => ({
                                                ...prev,
                                                berat_msc: undefined,
                                            }));
                                        }}
                                        readOnly={isViewMode}

                                    />
                                    <InputGroup.Addon>Kg</InputGroup.Addon>
                                </InputGroup>
                                {errorsAddForm.berat_msc && (
                                    <p style={{ color: 'red' }}>{errorsAddForm.berat_msc}</p>
                                )}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Berat Massa Siap Cetak</Form.ControlLabel>
                                <InputGroup>
                                    <Form.Control
                                        readOnly
                                        value={addTransactionMscForm.percentage_berat_msc || 'masukan nilai massa'}
                                        style={{ background: '#f4f4f4' }}
                                    />
                                    <InputGroup.Addon>%</InputGroup.Addon>
                                </InputGroup>
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Syarat Berat Massa Siap Cetak</Form.ControlLabel>
                                <Form.Control

                                    readOnly
                                    value={
                                        productData?.bobot_min && productData?.bobot_max
                                            ? `${productData.bobot_min} - ${productData.bobot_max} Kg`
                                            : '-'
                                    }
                                    style={{ background: '#f4f4f4' }}
                                />
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Keterangan</Form.ControlLabel>
                                <Form.Control
                                    rows={3}
                                    name="remarks"
                                    componentClass="textarea"
                                    placeholder="remarks"
                                    value={addTransactionMscForm.remarks}
                                    onChange={(value) =>
                                        setAddTransactionMscForm((prev) => ({
                                            ...prev,
                                            remarks: value,
                                        }))
                                    }
                                    readOnly={isViewMode}

                                />
                                {errorsAddForm.remarks && (
                                    <p style={{ color: 'red' }}>{errorsAddForm.remarks}</p>
                                )}
                            </Form.Group>
                            <Form.Group>
                                <Stack justifyContent="end" spacing={10}>
                                    <Button
                                        onClick={() => router.back()}
                                        appearance="subtle"
                                        disabled={loading}
                                    >
                                        {isViewMode ? "Kembali" : "Batal"}
                                    </Button>
                                    {!isViewMode && (
                                        <Button
                                            appearance="primary"
                                            onClick={handleSubmit}
                                            disabled={loading}
                                            loading={loading}
                                        >
                                            {loading ? (isEditMode ? "Memperbarui..." : "Mengirim...") : (isEditMode ? "Update" : "Kirim")}
                                        </Button>
                                    )}
                                </Stack>
                            </Form.Group>
                        </Form>
                    </Panel>

                </div>
            </ContainerLayout>
        </div>
    );
}
