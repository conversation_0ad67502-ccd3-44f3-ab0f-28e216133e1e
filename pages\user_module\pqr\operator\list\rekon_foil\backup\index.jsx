import { useEffect, useState, useCallback } from "react";
import Head from "next/head";
import { Breadcrumb, Stack, Panel, Tag, Form, Grid, Row, Col, Table, Button, Pagination, Modal, Loader, IconButton, InputGroup, FlexboxGrid, Input, InputNumber, Checkbox, SelectPicker, useToaster, Notification } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiMasterdata_ppi from "@/pages/api/pqr/ppi/api_masterdata_ppi";
import ApiProduct from "@/pages/api/pqr/product/api_masterdata_product";
import ApiItemProduct from "@/pages/api/pqr/item_product/api_item_product";
import ApiRekonFoil from "@/pages/api/pqr/rekon_foil/api_rekon_foil";
import ApiFoilPQR from "@/pages/api/pqr/foil/api_pqr_foil";

export default function RekonFoilPage() {
    const toaster = useToaster();
    const [searchKeyword, setSearchKeyword] = useState("");
    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [idRouter, setIdRouter] = useState(null);
    const router = useRouter();
    const { IdHeader } = router.query;
    const [isEditMode, setIsEditMode] = useState(false);
    const [isViewMode, setIsViewMode] = useState(false);



    const emptyAddTransactionReconFoilForm = {
        id_foil_header: null,
        id_trans_header: null,
        remarks: null,
        stripping_machine: null,
        create_by: sessionAuth ? sessionAuth.employee_name : "",
        id_foil_detail: [],
    };

    const [transactionDetailsDataState, setTransactionDetailDataState] = useState([]);
    const [ppiData, setPpiData] = useState(null);
    const [productData, setProductData] = useState(null);
    const [formDataHeader, setformDataHeader] = useState({});
    const [addTransactionReconForm, setAddTransactionReconForm] = useState('');
    const [listFoilItems, setListFoilItems] = useState([]);
    const [addTransactionReconFoilForm, setAddTransactionReconFoilForm] = useState(emptyAddTransactionReconFoilForm);
    const [isSubmitDisabled, setIsSubmitDisabled] = useState(true);

    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [addLoading, setAddLoading] = useState(false);
    const [loading, setLoading] = useState(false);

    const [strippingMachineOptions] = useState([
        { label: "HM 01A", value: "HM-01A" },
        { label: "HM 01B", value: "HM-01B" },
    ]);


    const HandleGetDetailTransactionHeader = async (id_trans_header) => {
        try {
            const apiTransactionHeader = ApiTransactionHeader();
            const response = await apiTransactionHeader.getPPITransactionHeaderById({ id_trans_header: parseInt(id_trans_header) });
            if (response.status === 200) {
                const data = response.data;
                setformDataHeader({
                    id_trans_header: data.id_trans_header,
                    id_ppi: data.id_ppi,
                    ppi_name: data.ppi_name,
                    batch_code: data.batch_code,
                    iot_desc: data.iot_desc,
                    line_desc: data.line_desc,
                    remarks: data.remarks,
                    wetmill: data.wetmill,
                    status_transaction: data.status_transaction,
                    create_date: data.create_date ? new Date(data.create_date).toLocaleDateString("en-GB") : "-",
                    create_by: data.create_by || "-",
                    update_date: data.update_date ? new Date(data.update_date).toLocaleDateString("en-GB") : "-",
                    update_by: data.update_by || "-",
                    delete_date: data.delete_date ? new Date(data.delete_date).toLocaleDateString("en-GB") : "-",
                    delete_by: data.delete_by || "-",
                });

                if (data.id_ppi) {
                    const ppiResponse = await HandleGetPPIById(data.id_ppi);

                    if (ppiResponse) {
                        setPpiData(ppiResponse);
                        if (ppiResponse.id_product) {
                            const productResponse = await HandleGetProductById(ppiResponse.id_product);
                            if (productResponse) {
                                setProductData(productResponse);
                            }
                        }
                    }
                }

                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetPPIById = async (id_ppi) => {
        try {
            const api = ApiMasterdata_ppi();
            const response = await api.GetMasterPPIById({ id_ppi: parseInt(id_ppi) });

            if (response.status === 200) {
                const data = response.data;
                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetProductById = async (id_product) => {
        try {
            const api = ApiProduct();
            const response = await api.getProductById({ id_product: parseInt(id_product) });

            if (response.status === 200) {
                const data = response.data;
                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetAllItemProductApi = async () => {
        try {
            const res = await ApiItemProduct().getAllActiveItemProduct();
            if (res.status === 200) {
                const initialFormState = res.data.map(item => ({
                    ...item,
                    bobot_sebelumnya: '',
                    bobot_sisa: '',
                    qty_std: productData.bobot_std,
                    material_awal: '',
                    fkm: '',
                    material_sisa: 0,
                    jumlah_material_terpakai: 0,
                    jumlah_material_dimusnahkan: '',
                    jumlah_material_dikembalikan: ''
                }));
                setListFoilItems(initialFormState);
            } else {
                console.log("Error on GetAllItemProductApi: ", res.message);

            }
        } catch (error) {
            console.log("Error on catch GetAllItemProductApi: ", error.message);
        }
    };


    const HandleGetBatchCode = async (batch_number) => {
        try {
            const [resBatch, resKonversi] = await Promise.all([
                ApiFoilPQR().getRekonSekunder({ batch_number }),
                ApiItemProduct().getAllActiveItemProduct()
            ]);
    
            if (resBatch.status === 200 && resKonversi.status === 200) {
                const konversiData = resKonversi.data;
    
                const initialFormState = resBatch.data.map(item => {
                    const matched = konversiData.find(k => k.item_code === item.item); // assuming match by item code
                    return {
                        ...item,
                        id_item: matched?.id_item,
                        inventory_item_id: item.id_item,
                        item_code: item.item,
                        item_description: item.item_description,
                        nilai_konversi: matched?.nilai_konversi ?? 0, // fallback to 0
                        bobot_sebelumnya: '',
                        bobot_sisa: '',
                        qty_std: parseInt(item.qty),
                        material_awal: '',
                        fkm: '',
                        material_sisa: 0,
                        jumlah_material_terpakai: 0,
                        jumlah_material_dimusnahkan: '',
                        jumlah_material_dikembalikan: '',
                    };
                });
    
                setListFoilItems(initialFormState);
            } else {
                console.log("Error on GetAllItemProductApi or getRekonSekunder");
            }
        } catch (error) {
            console.log("Error on catch GetAllItemProductApi: ", error.message);
        }
    };
    







    const HandleGetRekonFoilByIdTransHeader = async (id_trans_header) => {
        try {
            setLoading(true);
            const api = ApiRekonFoil();

            const response = await api.getRekonFoilByIdTransHeader({ id_trans_header: parseInt(id_trans_header) });

            if (response.status !== 200) {
                throw new Error(response.message || "Gagal mendapatkan data Rekonsiliasi Foil.");
            }

            const data = response.data;
            if (!data) {
                throw new Error("Data Rekonsiliasi Foil tidak ditemukan.");
            }


            setAddTransactionReconFoilForm(prev => ({
                ...prev,
                id_foil_header: data.id_foil_header,
                stripping_machine: data.stripping_machine,
                remarks: data.remarks
            }));


            const itemProductRes = await ApiItemProduct().getAllActiveItemProduct();
            if (itemProductRes.status !== 200) {
                throw new Error("Gagal mendapatkan data master item product.");
            }

            const allMasterItems = itemProductRes.data.data || itemProductRes.data;

            const combinedItems = allMasterItems.map(masterItem => {

                const detailFoilSebelumnya = data.detailFoil.find(df => df.id_item === masterItem.id_item && df.flag_sisa === 0);
                const detailFoilSisa = data.detailFoil.find(df => df.id_item === masterItem.id_item && df.flag_sisa === 1);
                const detailRekon = data.detailRekonsiliasi.find(dr => dr.id_item === masterItem.id_item);


                return {
                    ...masterItem,

                    id_foil_detail_sebelumnya: detailFoilSebelumnya ? detailFoilSebelumnya.id_foil_detail : 0,
                    id_foil_detail_sisa: detailFoilSisa ? detailFoilSisa.id_foil_detail : 0,
                    id_foil_rekon: detailRekon ? detailRekon.id_foil_detail : 0,
                    bobot_sebelumnya: detailFoilSebelumnya ? detailFoilSebelumnya.bobot_material : '',
                    bobot_sisa: detailFoilSisa ? detailFoilSisa.bobot_material : '',

                    qty_std: detailRekon ? detailRekon.material_a : (productData?.bobot_std || 0),
                    material_awal: detailRekon ? detailRekon.material_awal : '',
                    fkm: detailRekon ? detailRekon.fkm : '',
                    material_sisa: detailRekon ? detailRekon.material_sisa : 0,
                    jumlah_material_terpakai: detailRekon ? detailRekon.jumlah_material_terpakai : 0,
                    jumlah_material_dimusnahkan: detailRekon ? detailRekon.jumlah_material_dimusnahkan : '',
                    jumlah_material_dikembalikan: detailRekon ? detailRekon.jumlah_material_dikembalikan : '',
                };
            });


            
            const detailFoil = data.detailFoil;
            const detailRekonsiliasi = data.detailRekonsiliasi;
            
            const groupedFoil = detailFoil.reduce((acc, item) => {
                if (!acc[item.id_item]) {
                    acc[item.id_item] = {
                        id_item: item.id_item,
                        item_code: item.item_code,
                        item_description: item.item_description,
                        create_date: item.create_date,
                        create_by: item.create_by,
                        update_date: item.update_date,
                        update_by: item.update_by,
                        delete_date: item.delete_date,
                        delete_by: item.delete_by,
                        is_active: item.is_active,
                        id_foil_detail_sebelumnya: 0,
                        id_foil_detail_sisa: 0,
                        bobot_sebelumnya: "",
                        bobot_sisa: "",
                        id_foil_rekon: 0,
                        qty_std: 650,
                        material_awal: "",
                        fkm: "",
                        material_sisa: 0,
                        jumlah_material_terpakai: 0,
                        jumlah_material_dimusnahkan: "",
                        jumlah_material_dikembalikan: ""
                    };
                }
            
                if (item.flag_sisa === 0) {
                    acc[item.id_item].id_foil_detail_sebelumnya = item.id_foil_detail;
                    acc[item.id_item].bobot_sebelumnya = item.bobot_material;
                } else {
                    acc[item.id_item].id_foil_detail_sisa = item.id_foil_detail;
                    acc[item.id_item].bobot_sisa = item.bobot_material;
                }
            
                // We can set konversi_m as nilai_konversi if needed
                acc[item.id_item].nilai_konversi = item.konversi_m;
            
                return acc;
            }, {});
            
            detailRekonsiliasi.forEach(item => {
                if (groupedFoil[item.id_item]) {
                    groupedFoil[item.id_item].id_foil_rekon = item.id_foil_detail;
                    groupedFoil[item.id_item].qty_std = item.material_a;
                    groupedFoil[item.id_item].material_awal = item.material_awal;
                    groupedFoil[item.id_item].fkm = item.fkm;
                    groupedFoil[item.id_item].material_sisa = item.material_sisa;
                    groupedFoil[item.id_item].jumlah_material_terpakai = item.jumlah_material_terpakai;
                    groupedFoil[item.id_item].jumlah_material_dimusnahkan = item.jumlah_material_dimusnahkan;
                    groupedFoil[item.id_item].jumlah_material_dikembalikan = item.jumlah_material_dikembalikan;
                }
            });
            
            const finalResult = Object.values(groupedFoil);
            
            

            setListFoilItems(finalResult);

        } catch (error) {
            console.error("Error dalam HandleGetRekonFoilByIdTransHeader:", error);
            showNotification("error", error.message);
        } finally {
            setLoading(false);
        }
    };


    const handleItemInputChange = (index, fieldName, value) => {
        if (value.includes('-')) {
            return;
        }
        const updatedItems = [...listFoilItems];
        const currentItem = { ...updatedItems[index] };

        currentItem[fieldName] = value;

        const bobotCoreFoil = parseFloat(productData?.bobot_core_foil);
        const nilaiKonversi = parseFloat(currentItem.nilai_konversi);

        const bobotSisa = parseFloat(currentItem.bobot_sisa);
        const materialSisa = bobotSisa > 0 ? (bobotSisa - bobotCoreFoil) * nilaiKonversi : 0;
        currentItem.material_sisa = parseFloat(materialSisa.toFixed(2));

        const Q = parseFloat(currentItem.material_awal) || 0;
        const R = parseFloat(currentItem.fkm) || 0;
        const S = currentItem.material_sisa;
        const hasilTerpakai = Q + R - S;
        currentItem.jumlah_material_terpakai = parseFloat(hasilTerpakai.toFixed(2));

        updatedItems[index] = currentItem;


        setListFoilItems(updatedItems);
    };
    const handleSubmit = async () => {

        const errors = {};
        if (!addTransactionReconFoilForm.stripping_machine) {
            errors.stripping_machine = "Pilih mesin stripping terlebih dahulu.";
        }
        if (!addTransactionReconFoilForm.remarks) {
            errors.remarks = "Remarks wajib diisi";
        }
        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }

        setAddLoading(true);

        try {

            if (isEditMode) {
                const updatedBy = sessionAuth ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}` : "";


                const payloadDetailFoil = listFoilItems.map(item => {
                    const bobotSebelumnya = parseFloat(item.bobot_sebelumnya);
                    const bobotSisa = parseFloat(item.bobot_sisa);
                    const nilaiKonversi = parseFloat(item.nilai_konversi);
                    const bobotCoreFoil = parseFloat(productData?.bobot_core_foil);

                    const results = [];
                    if (bobotSebelumnya > 0) {
                        const konversiSebelumnya = (bobotSebelumnya - bobotCoreFoil) * nilaiKonversi;
                        results.push({
                            id_foil_detail: item.id_foil_detail_sebelumnya,
                            id_item: item.id_item,
                            item_code: item.item_code,
                            item_description: item.item_description,
                            bobot_material: bobotSebelumnya,
                            konversi_m: parseFloat(konversiSebelumnya.toFixed(2)),
                            nilai_konversi: nilaiKonversi,
                            flag_sisa: 0,
                        });
                    }
                    if (bobotSisa > 0) {
                        const konversiSisa = (bobotSisa - bobotCoreFoil) * nilaiKonversi;
                        results.push({
                            id_foil_detail: item.id_foil_detail_sisa,
                            id_item: item.id_item,
                            item_code: item.item_code,
                            item_description: item.item_description,
                            bobot_material: bobotSisa,
                            konversi_m: parseFloat(konversiSisa.toFixed(2)),
                            nilai_konversi: nilaiKonversi,
                            flag_sisa: 1,
                        });
                    }
                    return results;
                }).flat();

                const payloadDetailRekon = listFoilItems.map(item => ({
                    id_foil_detail: item.id_foil_rekon,
                    id_item: item.id_item,
                    item_code: item.item_code,
                    item_description: item.item_description,
                    material_a: parseFloat(item.qty_std),
                    material_awal: parseFloat(item.material_awal),
                    fkm: parseFloat(item.fkm),
                    material_sisa: parseFloat(item.material_sisa),
                    jumlah_material_terpakai: parseFloat(item.jumlah_material_terpakai),
                    jumlah_material_dimusnahkan: parseFloat(item.jumlah_material_dimusnahkan),
                    jumlah_material_dikembalikan: parseFloat(item.jumlah_material_dikembalikan),
                }));

                // Siapkan payload final untuk UPDATE
                const finalPayloadUpdate = {
                    id_foil_header: addTransactionReconFoilForm.id_foil_header,
                    stripping_machine: addTransactionReconFoilForm.stripping_machine,
                    remarks: addTransactionReconFoilForm.remarks,
                    update_by: updatedBy,
                    details_foil: payloadDetailFoil,
                    details_rekon: payloadDetailRekon
                };

                console.log("update payload", finalPayloadUpdate);
                const res = await ApiRekonFoil().updateRekonFoil(finalPayloadUpdate);

                if (res.status === 200) {
                    showNotification("success", "Data Rekonsiliasi Foil Berhasil Diperbarui");
                    router.push(`/user_module/pqr/operator/list`);
                } else {
                    console.log("gagal update data", res.message);
                    showNotification("error", "Gagal Memperbarui data");
                }

            } else {
                const createdBy = sessionAuth ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}` : "";
                const payloadDetailFoil = [];
                const bobotCoreFoil = parseFloat(productData?.bobot_core_foil);

                listFoilItems.forEach(item => {
                    const bobotSebelumnya = parseFloat(item.bobot_sebelumnya);
                    const nilaiKonversi = parseFloat(item.nilai_konversi);
                    if (bobotSebelumnya > 0) {
                        const konversiSebelumnya = (bobotSebelumnya - bobotCoreFoil) * nilaiKonversi;
                        payloadDetailFoil.push({
                            id_item: item.id_item,
                            item_code: item.item_code,
                            item_description: item.item_description,
                            bobot_material: bobotSebelumnya,
                            konversi_m: parseFloat(konversiSebelumnya.toFixed(2)),
                            nilai_konversi: nilaiKonversi,
                            flag_sisa: 0,
                            create_by: createdBy
                        });
                    }

                    const bobotSisa = parseFloat(item.bobot_sisa);
                    if (bobotSisa > 0) {
                        const konversiSisa = (bobotSisa - bobotCoreFoil) * nilaiKonversi;
                        payloadDetailFoil.push({
                            id_item: item.id_item,
                            item_code: item.item_code,
                            item_description: item.item_description,
                            bobot_material: bobotSisa,
                            konversi_m: parseFloat(konversiSisa.toFixed(2)),
                            nilai_konversi: nilaiKonversi,
                            flag_sisa: 1,
                            create_by: createdBy
                        });
                    }
                });

                //detail rekon payload
                const payloadDetailRekon = listFoilItems.map(item => ({
                    id_item: item.id_item,
                    item_code: item.item_code,
                    item_description: item.item_description,
                    material_a: parseFloat(item.qty),
                    material_awal: parseFloat(item.material_awal),
                    fkm: parseFloat(item.fkm),
                    material_sisa: parseFloat(item.material_sisa),
                    jumlah_material_terpakai: parseFloat(item.jumlah_material_terpakai),
                    jumlah_material_dimusnahkan: parseFloat(item.jumlah_material_dimusnahkan),
                    jumlah_material_dikembalikan: parseFloat(item.jumlah_material_dikembalikan),
                    create_by: createdBy
                }));
                const finalPayloadCreate = {
                    // Data Header
                    id_trans_header: parseInt(IdHeader, 10),
                    stripping_machine: addTransactionReconFoilForm.stripping_machine,
                    remarks: addTransactionReconFoilForm.remarks,
                    create_by: createdBy,
                    details_foil: payloadDetailFoil,
                    details_rekon: payloadDetailRekon
                };

                console.log("Mengirim Payload CREATE", finalPayloadCreate);
                const res = await ApiRekonFoil().createRekonFoil(finalPayloadCreate);
                if (res.status === 200) {
                    showNotification("success", "Semua Data Rekonsiliasi Foil Berhasil Disimpan");
                    router.push(`/user_module/pqr/operator/list`);
                } else {
                    console.log("gagal create data", res.message);
                    showNotification("error", "Gagal Menyimpan data");
                }
            }
        } catch (error) {
            console.error("Terjadi kesalahan saat submit:", error);
            const modeText = isEditMode ? "Memperbarui" : "Menyimpan";
            showNotification("error", error.message || `Terjadi Kesalahan Saat ${modeText} Data`);
        } finally {
            setAddLoading(false);
        }
    };

    const totalRowCount = searchKeyword ? filteredData.length : transactionDetailsDataState.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setSessionAuth(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else if (IdHeader) {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("pqr/operator")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }

            console.log("Router data:", IdHeader);
            setIdRouter(IdHeader);

            HandleGetDetailTransactionHeader(IdHeader);

            const { mode } = router.query;
            if (mode === 'edit' || mode === 'view') {
                setIsEditMode(mode === 'edit');
                setIsViewMode(mode === 'view');
                HandleGetRekonFoilByIdTransHeader(IdHeader);
            } else {
                setIsEditMode(false);
                setIsViewMode(false);
                // if (productData) {
                //     HandleGetAllItemProductApi();
                // }
            }
        }
    }, [IdHeader, router]);

    useEffect(() => {
        if (productData && !isEditMode && !isViewMode) {
            HandleGetBatchCode(formDataHeader.batch_code);
        }
    }, [productData, isEditMode, isViewMode, formDataHeader]);

    useEffect(() => {
        const validateForm = () => {

            if (!addTransactionReconFoilForm.stripping_machine) {
                return true;
            }
            if (!addTransactionReconFoilForm.remarks) {
                return true;
            }

            for (const item of listFoilItems) {

                if (
                    String(item.bobot_sebelumnya).trim() === '' ||
                    String(item.bobot_sisa).trim() === '' ||
                    String(item.material_awal).trim() === '' ||
                    String(item.fkm).trim() === '' ||
                    String(item.jumlah_material_dimusnahkan).trim() === '' ||
                    String(item.jumlah_material_dikembalikan).trim() === ''
                ) {
                    return true;
                }
            }

            return false;
        };

        // Panggil fungsi validasi dan update state tombol
        setIsSubmitDisabled(validateForm());

    }, [listFoilItems, addTransactionReconFoilForm]);


    const showNotification = (type, message, duration = 2000) => {
        toaster.push(
            <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
                <p>{message}</p>
            </Notification>,
            { placement: "topEnd", duration }
        );
    };

    return (
        <div>
            <div>
                <Head>
                    <title>Rekonsiliasi Foil</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>PQR</Breadcrumb.Item>
                                    <Breadcrumb.Item>List</Breadcrumb.Item>
                                    <Breadcrumb.Item >Transaction Header</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Rekon Kaplet</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <Form layout="vertical">
                                    <Grid fluid>
                                        <Row style={{ marginBottom: "16px" }}>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>ID Transaksi Header</Form.ControlLabel>
                                                    <Form.Control name="id_trans_header" value={formDataHeader.id_trans_header} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                                                    <Form.Control name="ppi_name" value={formDataHeader.ppi_name} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Batch Code</Form.ControlLabel>
                                                    <Form.Control name="batch_code" value={formDataHeader.batch_code} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Wetmill</Form.ControlLabel>
                                                    <Form.Control name="wetmill" value={formDataHeader.wetmill === "Y" ? "Yes" : "No"} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}></Row>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Remarks</Form.ControlLabel>
                                                        <Form.Control name="remarks" value={formDataHeader.remarks} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Status Transaksi</Form.ControlLabel>
                                                        <Form.Control name="status_transaction" value={formDataHeader.status_transaction === 2 ? "Draft" : formDataHeader.status_transaction === 1 ? "Done" : "Dropped"} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                                                        <Form.Control name="product_code" value={productData?.product_code ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>

                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}><Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Bobot Minimal</Form.ControlLabel>
                                                    <Form.Control name="bobot_min" value={productData?.bobot_min ?? '-'} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Maximal</Form.ControlLabel>
                                                        <Form.Control name="bobot_max" value={productData?.bobot_max ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Standar</Form.ControlLabel>
                                                        <Form.Control name="bobot_std" value={productData?.bobot_std ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Core Foil</Form.ControlLabel>
                                                        <Form.Control name="bobot_core_foil" value={productData?.bobot_core_foil ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}></Row>
                                            <Row style={{ marginBottom: "24px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                                                        <Form.Control name="create_date" value={formDataHeader.create_date} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                                                        <Form.Control name="created_by" value={formDataHeader.create_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                                                        <Form.Control name="update_date" value={formDataHeader.update_date} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                                                        <Form.Control name="update_by" value={formDataHeader.update_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                                                    <Form.Control name="delete_date" value={formDataHeader.delete_date} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                                                        <Form.Control name="delete_by" value={formDataHeader.delete_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </Row>
                                    </Grid>
                                </Form>
                            </Stack>
                        }
                    />
                    <Panel
                        bordered
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Rekonsiliasi Foil</h5>
                            </Stack>
                        }
                    >
                        {loading ? (
                            <FlexboxGrid justify="center" align="middle" style={{ padding: '40px 0' }}>
                                <Loader center size="lg" vertical content="Memuat data rekonsiliasi..." />
                            </FlexboxGrid>
                        ) : (
                            <Form fluid>
                                <Form.Group>
                                    <Form.ControlLabel>Pilih Mesin Stripping</Form.ControlLabel>
                                    <SelectPicker
                                        data={strippingMachineOptions}
                                        value={addTransactionReconFoilForm.stripping_machine}
                                        onChange={(value) => {
                                            setAddTransactionReconFoilForm((prev) => ({ ...prev, stripping_machine: value }));
                                            setErrorsAddForm((prev) => ({ ...prev, stripping_machine: undefined }));
                                        }}
                                        style={{ width: "100%" }}
                                        placeholder="Pilih"
                                        disabled={addLoading || isViewMode}
                                    />
                                    {errorsAddForm.stripping_machine && <p style={{ color: "red" }}>{errorsAddForm.stripping_machine}</p>}
                                </Form.Group>
                                <Panel bordered className="mb-3 mt-3" header={<Stack justifyContent="space-between"><h5>Sisa Foil Batch Sebelumnya</h5></Stack>}>
                                    {listFoilItems.map((item, index) => {
                                        const bobotCoreFoil = parseFloat(productData?.bobot_core_foil);
                                        const nilaiD = parseFloat(item.bobot_sebelumnya);
                                        const nilaiKonversi = parseFloat(item.nilai_konversi);
                                        const nilaiE = nilaiD > 0 ? ((nilaiD - bobotCoreFoil) * nilaiKonversi).toFixed(2) : 'nilai Konversi Ke M';

                                        return (
                                            <FlexboxGrid key={`sebelumnya-${item.id_item}`} className="mb-3">
                                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Material - {item.item_code}</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                value={item.bobot_sebelumnya}
                                                                onChange={(value) => handleItemInputChange(index, 'bobot_sebelumnya', value)}
                                                                onWheel={(e) => e.target.blur()}
                                                                placeholder="bobot_sebelumnya(kg)"
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                                <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Konversi ke M</Form.ControlLabel>
                                                        <Input
                                                            readOnly
                                                            style={{ background: '#f4f4f4' }}
                                                            value={nilaiE}
                                                            placeholder="(D - bobot_core_foil) x nilai_konversi = E"
                                                        />
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                            </FlexboxGrid>
                                        );
                                    })}
                                </Panel>

                                <Panel bordered className="mb-3" header={<Stack justifyContent="space-between"><h5>Sisa Foil Sisa</h5></Stack>}>

                                    {listFoilItems.map((item, index) => {

                                        const bobotCoreFoil = parseFloat(productData?.bobot_core_foil);
                                        const nilaiD1 = parseFloat(item.bobot_sisa);
                                        const nilaiKonversi = parseFloat(item.nilai_konversi);
                                        const nilaiE1 = nilaiD1 > 0 ? ((nilaiD1 - bobotCoreFoil) * nilaiKonversi).toFixed(2) : 'nilai Konversi Ke M';

                                        return (
                                            <FlexboxGrid key={`sisa-${item.id_item}`} className="mb-3">
                                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Material - {item.item_code}</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                value={item.bobot_sisa}
                                                                onChange={(value) => handleItemInputChange(index, 'bobot_sisa', value)}
                                                                onWheel={(e) => e.target.blur()}
                                                                placeholder="Bobot_sisa (kg)"
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                                <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Konversi ke M</Form.ControlLabel>
                                                        <Input
                                                            readOnly
                                                            style={{ background: '#f4f4f4' }}
                                                            value={nilaiE1}
                                                            placeholder="(D1 - bobot_core_foil) x nilai_konversi = E1"
                                                        />
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                            </FlexboxGrid>
                                        );
                                    })}
                                </Panel>

                                <Panel bordered className="mb-3" header={<Stack justifyContent="space-between"><h5>Rekonsiliasi</h5></Stack>}>
                                    {listFoilItems.map((item, index) => (
                                        <Panel key={`rekon-${item.id_item}`} bordered className="mb-3" header={<h5>Material - {item.item_code}</h5>}>
                                            <Form.Group>
                                                <Form.ControlLabel>QTY_STD</Form.ControlLabel>
                                                <Input
                                                    readOnly
                                                    style={{ background: '#f4f4f4' }}
                                                    value={item.qty_std}
                                                />
                                            </Form.Group>
                                            <FlexboxGrid className="mb-3 mt-3">
                                                {/* --- Kolom Kiri --- */}
                                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Material Awal</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="material_awal(kg)"
                                                                onWheel={(e) => e.target.blur()}
                                                                value={item.material_awal}
                                                                onChange={(value) => handleItemInputChange(index, 'material_awal', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>FKM</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="fkm(kg)"
                                                                onWheel={(e) => e.target.blur()}
                                                                value={item.fkm}
                                                                onChange={(value) => handleItemInputChange(index, 'fkm', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Material Sisa</Form.ControlLabel>
                                                        <Input
                                                            readOnly
                                                            style={{ background: '#f4f4f4' }}
                                                            value={item.material_sisa}
                                                            placeholder="Akumulasi E1 = S"
                                                        />
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                                {/* --- Kolom Kanan --- */}
                                                <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Jumlah Material Terpakai</Form.ControlLabel>
                                                        <Input
                                                            readOnly
                                                            style={{ background: '#f4f4f4' }}
                                                            value={item.jumlah_material_terpakai}
                                                            placeholder="Q + R - S = T"
                                                        />
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Jumlah Material Dimusnahkan</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="jumlah_material_dimusnahkan(kg)"
                                                                onWheel={(e) => e.target.blur()}
                                                                value={item.jumlah_material_dimusnahkan}
                                                                onChange={(value) => handleItemInputChange(index, 'jumlah_material_dimusnahkan', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Jumlah Material Dikembalikan</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="jumlah_material_dikembalikan (kg)"
                                                                value={item.jumlah_material_dikembalikan}
                                                                onWheel={(e) => e.target.blur()}
                                                                onChange={(value) => handleItemInputChange(index, 'jumlah_material_dikembalikan', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                            </FlexboxGrid>
                                        </Panel>
                                    ))}

                                </Panel>
                                <Form.Group>
                                    <Form.ControlLabel>Keterangan</Form.ControlLabel>
                                    <Input
                                        as="textarea"
                                        rows={3}
                                        name="remarks"
                                        placeholder="Remarks"
                                        value={addTransactionReconFoilForm.remarks}
                                        onChange={(value) => {
                                            setAddTransactionReconFoilForm((prev) => ({ ...prev, remarks: value }));
                                            setErrorsAddForm((prev) => ({ ...prev, remarks: undefined }));
                                        }}
                                        readOnly={isViewMode}
                                    />
                                    {errorsAddForm.remarks && <p style={{ color: "red" }}>{errorsAddForm.remarks}</p>}
                                </Form.Group>


                                <Form.Group>
                                    <Stack justifyContent="end" spacing={10}>
                                        <Button onClick={() => router.back()} appearance="subtle" disabled={addLoading}>
                                            {isViewMode ? "Kembali" : "Batal"}
                                        </Button>
                                        {!isViewMode && (
                                            <Button appearance="primary" onClick={handleSubmit} disabled={isSubmitDisabled || addLoading} loading={addLoading}>
                                                {loading ? (isEditMode ? "Memperbarui..." : "Mengirim...") : (isEditMode ? "Update" : "Kirim")}
                                            </Button>
                                        )}
                                    </Stack>
                                </Form.Group>
                            </Form>
                        )}
                    </Panel>

                </div>
            </ContainerLayout>
        </div>
    );
}