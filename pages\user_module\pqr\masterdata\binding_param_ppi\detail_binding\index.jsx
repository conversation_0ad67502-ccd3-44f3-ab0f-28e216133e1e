import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, Panel, Stack, Tag, Form } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";

export default function index() {
  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [idRouter, setIdRouter] = useState(null);

  const router = useRouter();
  const { Id } = router.query;

  // const [formData, setFormData] = useState({
  //   id_binding: null,
  //   id_ppi: null,
  //   order_no: "",
  //   min_value: null,
  //   max_value: null,
  //   weetmill: "N",
  //   created_date: "",
  //   created_by: "",
  //   updated_date: "",
  //   updated_by: "",
  //   deleted_date: "",
  //   deleted_by: "",
  // });

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else if (Id) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/masterdata")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      setIdRouter(Id);
      HandleGetDetail(Id);
    }
  }, [router]);

  const HandleGetDetail = async (id_binding) => {
    console.log("jalankan process getDetail", id_binding);
  };

  return (
    <div>
      <div>
        <Head>
          <title>Detail Binding Page</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>detail binding</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>tes detail binding {idRouter}</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <h1>tes</h1>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
