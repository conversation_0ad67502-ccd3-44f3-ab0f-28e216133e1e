import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  Loader,
  RadioGroup,
  Radio,
  SelectPicker,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import EditIcon from "@rsuite/icons/Edit";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import { FormControl, FormControlLabel, FormGroup } from "@mui/material";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiPPI from "@/pages/api/pqr/ppi/api_masterdata_ppi";
import APIpqrwerumoc from "@/pages/api/pqr/oc_werum/api_oc_werum";
import ApiBatchRecipe from "@/pages/api/pqr/transaction_h/api_batch_recipe";

export default function OperatorCreationList() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_trans_header");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);

  const [transactionHeadersDataState, setTransactionHeadersDataState] = useState([]);
  const [ppiDataState, setPPIDataState] = useState([]);
  const [batchCodeDataState, setBatchCodeDataState] = useState([]);

  const emptyEditTransactionHeaderForm = {
    id_trans_header: null,
    id_ppi: null,
    batch_code: "",
    iot_desc: "",
    line_desc: "",
    remarks: "",
    wetmill: "N",
    status_transaction: 2,
    create_date: null,
    create_by: "",
    update_date: null,
    update_by: null,
    delete_date: null,
    delete_by: null,
  };

  const [showEditModal, setShowEditModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showRemarksModal, setShowRemarksModal] = useState(false);
  const [showDetailWerumModal, setShowDetailWerumModal] = useState(false);
  const [editTransactionHeaderForm, setEditTransactionHeaderForm] = useState(emptyEditTransactionHeaderForm);
  const [werumDataState, setWerumDataState] = useState([])
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const [selectedRemarks, setSelectedRemarks] = useState("");
  const [selectedTransactionId, setSelectedTransactionId] = useState(null);

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = transactionHeadersDataState.filter((rowData, i) => {
    const searchFields = ["id_trans_header", "ppi_name", "batch_code", "iot_desc", "line_desc", "remarks", "wetmill", "status_transaction", "create_date", "create_by", "update_date", "update_by", "delete_date", "delete_by"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : transactionHeadersDataState.length;

  const HandleGetAllTransactionHeaderApi = async () => {
    try {
      const res = await ApiTransactionHeader().getAllTransactionHeader();

      console.log("res", res);
      if (res.status === 200) {
        setTransactionHeadersDataState(res.data);
      } else {
        console.log("gagal mendapatkan data transaksi ", res.message);
      }
    } catch (error) {
      console.log("gagal mendapatkan data transaksi", error);
    }
  };

  const HandleGetAllPPIApi = async () => {
    try {
      const res = await ApiPPI().getAllActiveMasterPPI();
      if (res.status === 200) {
        const options = res.data.map((ppi) => ({
          label: ppi.ppi_name,
          value: ppi.id_ppi,
          wetmill: ppi.wetmill,
        }));
        setPPIDataState(options);
      } else {
        console.log("gagal mendapatkan data PPI", res.message);
      }
    } catch (error) {
      console.log("gagal mendapatkan data PPI", error);
    }
  };

  const HandleGetAllBatchCode = async () => {
    try {
      const res = await ApiBatchRecipe().getAllBatchCode();

      console.log("res", res);
      if (res.status === 200) {
        setBatchCodeDataState(res.data);
      } else {
        console.log("gagal mendapatkan data kode batch ", res.message);
      }
    } catch (error) {
      console.log("gagal mendapatkan data kode batch", error);
    }
  };

  const HandleEditTransactionHeaderApi = async () => {
    const errors = {};

    // Cek apakah kombinasi batch_code dan id_ppi sudah ada pada transaksi selain yang sedang diedit
    const isDuplicate = transactionHeadersDataState.some(
      (transaction) => transaction.batch_code === editTransactionHeaderForm.batch_code && transaction.id_ppi === editTransactionHeaderForm.id_ppi && transaction.id_trans_header !== editTransactionHeaderForm.id_trans_header
    );

    if (isDuplicate) {
      errors.batch_code = "Batch code and PPI combination already exists";
    }

    if (!editTransactionHeaderForm.id_ppi) {
      errors.id_ppi = "PPI Name is required";
    }
    if (!editTransactionHeaderForm.batch_code) {
      errors.batch_code = "Batch code is required";
    }

    if (!editTransactionHeaderForm.remarks) {
      errors.remarks = "Remarks are required";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);
      return;
    }
    try {
      const res = await ApiTransactionHeader().editTransactionHeader({
        ...editTransactionHeaderForm,
        update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        HandleGetAllTransactionHeaderApi();
        setShowEditModal(false);
      } else {
        console.log("gagal mengubah data", res.message);
      }
    } catch (error) {
      console.log("error gagal mengubah data ", error);
    }
  };

  const HandleEditStatusTransactionHeaderApi = async (id_trans_header, is_active) => {
    try {
      const res = await ApiTransactionHeader().editStatusTransactionHeader({
        id_trans_header,
        is_active,
        delete_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        console.log("update status berhasil", res.message);
        HandleGetAllTransactionHeaderApi();
      } else {
        console.log("gagal update status ", res.message);
      }
    } catch (error) {
      console.log("Error gagal update status ", error);
    }
  };


  const HandleGetDetailPQRWerum = async (batch_number) => {
    try {
      const res = await APIpqrwerumoc().getOcListWerumPqr({
        batch_number: batch_number,
      });

      if (res.status === 200) {
        console.log("update status berhasil:", res.message);
        setWerumDataState(res.data);
      } else {
        console.log("gagal update status: ", res.message);
      }
    } catch (error) {
      console.log("Error gagal update status: ", error);
    }
  };

  const handleShowConfirmModal = (id) => {
    setSelectedTransactionId(id);
    setShowConfirmModal(true);
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/operator")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandleGetAllTransactionHeaderApi();
      HandleGetAllPPIApi();
      HandleGetAllBatchCode();
    }
  }, []);


  return (
    <div>
      <div>
        <Head>
          <title>Transaction Header</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>E Release</Breadcrumb.Item>
                  <Breadcrumb.Item>Creation</Breadcrumb.Item>
                  <Breadcrumb.Item active>E Release Submission</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Pengajuan E Release</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        // setShowAddModal(true);
                        router.push(`/user_module/pqr/operator/creation/list/addTransactionHeader`);
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="cari" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table
                bordered
                cellBordered
                height={400}
                data={getPaginatedData(getFilteredData(), limit, page)}
                sortColumn={sortColumn}
                sortType={sortType}
                onSortColumn={handleSortColumn}
                autoHeight
              >
                <Column width={180} align="center" sortable fullText resizable>
                  <HeaderCell>ID Transaksi Header</HeaderCell>
                  <Cell dataKey="id_trans_header" />
                </Column>
                <Column width={180} sortable fullText resizable>
                  <HeaderCell align="center">Nama PPI</HeaderCell>
                  <Cell dataKey="ppi_name" />
                </Column>
                <Column width={180} sortable fullText resizable>
                  <HeaderCell align="center">Kode Batch</HeaderCell>
                  <Cell dataKey="batch_code" />
                </Column>
                {/* <Column width={250} sortable fullText>
                  <HeaderCell align="center">Deksripsi IOT</HeaderCell>
                  <Cell dataKey="iot_desc" />
                </Column>
                <Column width={250} sortable fullText>
                  <HeaderCell align="center">Deskripsi Line</HeaderCell>
                  <Cell dataKey="line_desc" />
                </Column> */}
                <Column width={250} sortable fullText resizable>
                  <HeaderCell align="center">Catatan</HeaderCell>
                  <Cell dataKey="remarks" />
                </Column>
                <Column width={250} align="center" sortable fullText resizable>
                  <HeaderCell>Wetmill</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      let statusText = "";
                      if (rowData.wetmill === "Y") {
                        statusText = "Yes";
                      } else if (rowData.wetmill === "N") {
                        statusText = "No";
                      }
                      return <>{statusText}</>;
                    }}
                  </Cell>
                </Column>
                <Column width={250} align="center" sortable fullText resizable>
                  <HeaderCell>Status Transaksi</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      let statusText = "";
                      if (rowData.status_transaction === 2) {
                        statusText = "Draft";
                      } else if (rowData.status_transaction === 1) {
                        statusText = "Done";
                      } else if (rowData.status_transaction === 0) {
                        statusText = "Dropped";
                      }
                      return <>{statusText}</>;
                    }}
                  </Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText >
                  <HeaderCell>Tanggal Dibuat</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.delete_by}</>}</Cell>
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={100} fixed="right" align="center">
                  <HeaderCell>Status Approval</HeaderCell>
                  <Cell style={{ padding: "0", margin: "0", display: "flex", alignItems: "center" }}>
                    {(rowData) => {
                      let statusText = "";
                      if (rowData.status_approval === 2) {
                        statusText = "Draft";
                      } else if (rowData.status_approval === 1) {
                        statusText = "Approve";
                      } else if (rowData.status_approval === 0) {
                        return (
                          <Button
                            appearance="ghost"
                            color="red"
                            size="sm"
                            onClick={() => {
                              setShowRemarksModal(true);
                              setSelectedRemarks(rowData.revise_remarks);
                              setSelectedTransactionId(rowData.id_trans_header);
                            }}
                          >
                            Revisi
                          </Button>
                        );
                      }
                      return <>{statusText}</>;
                    }}
                  </Cell>
                </Column>
                <Column width={100} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => {
                      // Kondisi untuk tombol Edit
                      const isEditDisabled = rowData.is_active === 0 || rowData.status_approval === 1;

                      // Kondisi untuk tombol Trash
                      const isTrashDisabled = rowData.is_active === 0 || rowData.status_approval !== 0;

                      return (
                        <div>
                          {/* Tombol Edit */}
                          <Button
                            appearance="subtle"
                            disabled={isEditDisabled}
                            onClick={() => {
                              setShowEditModal(true);
                              setEditTransactionHeaderForm({
                                ...editTransactionHeaderForm,
                                id_ppi: rowData.id_ppi,
                                batch_code: rowData.batch_code,
                                iot_desc: rowData.iot_desc,
                                line_desc: rowData.line_desc,
                                remarks: rowData.remarks,
                                id_trans_header: rowData.id_trans_header,
                                wetmill: rowData.wetmill,
                                update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                              });
                            }}
                            title={isEditDisabled ? (rowData.is_active === 0 ? "Tidak dapat mengedit transaksi yang dinonaktifkan" : "Tidak dapat mengedit transaksi yang sudah disetujui") : "Edit Transaksi"}
                          >
                            <EditIcon />
                          </Button>
                          {/* <Button
                          appearance="subtle"
                          onClick={() => {
                            const idHeader = rowData.id_trans_header;
                            router.push(`/user_module/pqr/creation/list/pqr?IdHeader=${idHeader}`);
                          }}
                        >
                          <SearchIcon style={{ fontSize: "16px" }} />
                        </Button> */}
                          <Button
                            appearance="subtle"
                            onClick={() => handleShowConfirmModal(rowData.id_trans_header)}
                            disabled={isTrashDisabled}
                            title={isTrashDisabled ? (rowData.is_active === 0 ? "Transaksi tidak aktif" : "Hanya transaksi dengan status approval 'Revisi' yang bisa dihapus") : "Hapus Transaksi"}
                          >
                            <TrashIcon style={{ fontSize: "16px" }} />
                          </Button>
                        </div>
                      );
                    }}
                  </Cell>
                </Column>
                {/* <Column width={200} fixed="right" align="center">
                  <HeaderCell>Report</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="primary"
                          color="ghost"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowDetailWerumModal(true)
                            HandleGetDetailPQRWerum(rowData.batch_code)
                          }}
                        >
                          Data Werum
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column> */}
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditTransactionHeaderForm(emptyEditTransactionHeaderForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Ubah Transaksi Header</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                    <SelectPicker
                      data={ppiDataState} // Menggunakan data PPI yang telah di-fetch
                      value={editTransactionHeaderForm.id_ppi} // Nilai yang dipilih untuk edit
                      onChange={(value) => {
                        const selectedPPI = ppiDataState.find((item) => item.value === value);

                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_ppi: value,
                          wetmill: selectedPPI ? selectedPPI.wetmill : "", // Set wetmill if found, else empty
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          id_ppi: undefined,
                        }));
                      }}
                      block
                      placeholder="Select PPI"
                      style={{ width: "100%" }}
                    />
                    {errorsEditForm.id_ppi && <p style={{ color: "red" }}>{errorsEditForm.id_ppi}</p>}
                  </Form.Group>
                  <Form.Group>
                  <Form.ControlLabel>Kode Batch</Form.ControlLabel>
                    <SelectPicker
                      data={batchCodeDataState.map((item) => ({
                        label: `${item.parent_lot_number} - ${item.recipe_description}`,
                        value: `${item.parent_lot_number}`,
                      }))}
                      value={editTransactionHeaderForm.batch_code}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          batch_code: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          batch_code: undefined,
                        }));
                      }}
                      block
                      placeholder="Pilih Kode Batch"
                    />
                    {setErrorsEditForm.batch_code && (
                      <p style={{ color: "red" }}>{setErrorsEditForm.batch_code}</p>
                    )}
                  </Form.Group>
                  {/* <Form.Group>
                    <Form.ControlLabel>Deskripsi IOT</Form.ControlLabel>
                    <Form.Control
                      name="iot_desc"
                      value={editTransactionHeaderForm.iot_desc}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          iot_desc: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          iot_desc: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.iot_desc && <p style={{ color: "red" }}>{errorsEditForm.iot_desc}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Deskripsi Line</Form.ControlLabel>
                    <Form.Control
                      name="line_desc"
                      value={editTransactionHeaderForm.line_desc}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          line_desc: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          line_desc: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.line_desc && <p style={{ color: "red" }}>{errorsEditForm.line_desc}</p>}
                  </Form.Group> */}
                  <Form.Group>
                    <Form.ControlLabel>Catatan</Form.ControlLabel>
                    <Form.Control
                      name="remarks"
                      value={editTransactionHeaderForm.remarks}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          remarks: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          remarks: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.remarks && <p style={{ color: "red" }}>{errorsEditForm.remarks}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Wetmill</Form.ControlLabel>
                    <RadioGroup
                      name="wetmill"
                      inline
                      disabled
                      value={editTransactionHeaderForm.wetmill}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          wetmill: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          wetmill: undefined,
                        }));
                      }}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsEditForm.wetmill && <p style={{ color: "red" }}>{errorsEditForm.wetmill}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditTransactionHeaderForm(emptyEditTransactionHeaderForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleEditTransactionHeaderApi();
                  }}
                  appearance="primary"
                  type="submit"
                >
                  Simpan
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Editing Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
            <Modal
              backdrop="static"
              open={showDetailWerumModal}
              onClose={() => {
                setShowDetailWerumModal(false);
                setWerumDataState([]);
              }}
              overflow={false}
              size={"lg"}
            >
              <Modal.Header>
                <Modal.Title>Detail Data Werum</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Table
                  bordered
                  cellBordered
                  height={400}
                  data={werumDataState}
                  // sortColumn={sortColumn}
                  // sortType={sortType}
                  // onSortColumn={handleSortColumn}
                  autoHeight
                >
                  <Column width={180} align="center" sortable fullText>
                    <HeaderCell>Produk</HeaderCell>
                    <Cell dataKey="PRODUK" />
                  </Column>
                  <Column width={200} align="center" sortable fullText>
                    <HeaderCell>Produk Name</HeaderCell>
                    <Cell dataKey="PRODUK_NAME" />
                  </Column>
                  <Column width={150} align="center" sortable fullText>
                    <HeaderCell>Step</HeaderCell>
                    <Cell dataKey="STEP" />
                  </Column>
                  <Column width={150} align="center" sortable fullText>
                    <HeaderCell>BN</HeaderCell>
                    <Cell dataKey="BN" />
                  </Column>
                  <Column width={180} align="center" sortable fullText>
                    <HeaderCell>Schedule</HeaderCell>
                    <Cell dataKey="SCHEDULE" />
                  </Column>
                  <Column width={180} align="center" sortable fullText>
                    <HeaderCell>Material</HeaderCell>
                    <Cell dataKey="MATERIAL" />
                  </Column>
                  <Column width={200} align="center" sortable fullText>
                    <HeaderCell>Material Name</HeaderCell>
                    <Cell dataKey="MATERIAL_NAME" />
                  </Column>
                  <Column width={150} align="center" sortable fullText>
                    <HeaderCell>Lot QA</HeaderCell>
                    <Cell dataKey="LOT_QA" />
                  </Column>
                  <Column width={150} align="center" sortable fullText>
                    <HeaderCell>Qty</HeaderCell>
                    <Cell dataKey="QTY" />
                  </Column>
                  <Column width={120} align="center" sortable fullText>
                    <HeaderCell>UOM</HeaderCell>
                    <Cell dataKey="UOM" />
                  </Column>
                  <Column width={180} align="center" sortable fullText>
                    <HeaderCell>Tanggal</HeaderCell>
                    <Cell dataKey="TANGGAL" />
                  </Column>
                  <Column width={180} align="center" sortable fullText>
                    <HeaderCell>Weigher</HeaderCell>
                    <Cell dataKey="WEIGHER" />
                  </Column>
                  <Column width={180} align="center" sortable fullText>
                    <HeaderCell>Timbangan</HeaderCell>
                    <Cell dataKey="TIMBANGAN" />
                  </Column>
                  <Column width={200} align="center" sortable fullText>
                    <HeaderCell>Create By</HeaderCell>
                    <Cell dataKey="CREATE_BY" />
                  </Column>
                  <Column width={200} align="center" sortable fullText>
                    <HeaderCell>Date TMB</HeaderCell>
                    <Cell dataKey="DATE_TMB" />
                  </Column>
                  <Column width={200} align="center" sortable fullText>
                    <HeaderCell>Manufaktur Name</HeaderCell>
                    <Cell dataKey="MANUFAKTUR_NAME" />
                  </Column>
                  <Column width={200} align="center" sortable fullText>
                    <HeaderCell>Expiration Date</HeaderCell>
                    <Cell dataKey="EXPIRATION_DATE" />
                  </Column>
                </Table>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowDetailWerumModal(false);
                    setWerumDataState([]);
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
              </Modal.Footer>
            </Modal>
            <Modal
              open={showConfirmModal}
              onClose={() => {
                setShowConfirmModal(false);
              }}
              backdrop="static">
              <Modal.Header>
                <Modal.Title>Konfirmasi Penghapusan</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                Apakah Anda yakin ingin menghapus transaksi ini?
              </Modal.Body>
              <Modal.Footer>
                <Button onClick={() => {
                  setShowConfirmModal(false);
                }}
                  appearance="subtle">
                  Batal
                </Button>
                <Button onClick={() => {
                  HandleEditStatusTransactionHeaderApi(selectedTransactionId, 1);
                  setShowConfirmModal(false);
                }}
                  appearance="primary" color="red">
                  Ya, Hapus
                </Button>
              </Modal.Footer>
            </Modal>
            <Modal
              open={showRemarksModal}
              onClose={() => setShowRemarksModal(false)}
              size="sm"
            >
              <Modal.Header>
                <Modal.Title>Catatan Revisi</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                {selectedRemarks}
              </Modal.Body>
              <Modal.Footer>
                <Button onClick={() => setShowRemarksModal(false)} appearance="subtle">
                  Tutup
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
