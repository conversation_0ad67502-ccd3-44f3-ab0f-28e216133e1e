import { useEffect, useState } from "react";
import Head from "next/head"
import { <PERSON><PERSON><PERSON><PERSON>b, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, ButtonGroup } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import API_MasterDataReagenCapFloor from "@/pages/api/master_data/api_master_data_rc_floor";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import { useRouter } from "next/router";

export default function index() {

    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null)

    const router = useRouter()

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setSessionAuth(dataLogin);
        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            //uncomment if it ready to use

            // const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
            //     item.includes("master_data/master_rc_rack")
            // );

            // if (validateUserAccess.length === 0) {
            //     router.push("/dashboard");
            //     return;
            // }

            // handleGetAllApi();
        }
    }, []);
    return (
        <div>
            <div>
                <Head>
                    <title>Example</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>PQR</Breadcrumb.Item>
                                    <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Example</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Example Page</h5>

                            </Stack>}>
                    </Panel>

                    <Button
                        onClick={() => {
                            router.push({ pathname: '/user_module/pqr/example/page_without_data' });
                            router.push(`/user_module/pqr/example/page_without_data`);
                        }} >
                        Move page
                    </Button>
                    <Button
                        onClick={() => {
                            const idRouter = 1
                            router.push(`/user_module/pqr/example/page_with_data?Id=${idRouter}`);
                        }} >
                        Move page with data
                    </Button>
                </div>
            </ContainerLayout>
        </div>
    )
}
