import ContainerLayout from "@/components/layout/ContainerLayout";
import Head from "next/head";
import React, { useEffect, useState } from "react";
import {
  <PERSON>readcrumb,
  Button,
  Form,
  IconButton,
  Input,
  InputGroup,
  Modal,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  toaster,
  SelectPicker,
  Loader,
} from "rsuite";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import EditIcon from "@rsuite/icons/Edit";
import API_ScmIssueClassification from "@/pages/api/scm/api_scm_issue_classification";
import Messages from "@/components/Messages";
import { useRouter } from "next/router";

export default function ScmIssueClassification() {
  const [moduleName, setModuleName] = useState("");
  const [props, setProps] = useState([]);

  const { HeaderCell, Cell, Column } = Table;
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const [isLoading, setIsLoading] = useState(true);

  const [scmIssueClassification, setScmIssueClassification] = useState([]);
  const [lotNumberOptions, setLotNumberOptions] = useState([]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);

  const router = useRouter();

  const emptyForm = {
    lot_number: null,
    classification: null,
    remarks: null,
  };
  const [formValue, setFormValue] = useState(emptyForm);
  const emptyError = {
    lot_number: null,
    classification: null,
    remarks: null,
  };
  const [errors, setErrors] = useState(emptyError);

  const ClassificationDataDropdown = [
    { label: "MAN", value: "MAN" },
    { label: "METHOD", value: "METHOD" },
    { label: "MACHINE", value: "MACHINE" },
    { label: "ENVIROMENT", value: "ENVIROMENT" },
    { label: "MATERIAL", value: "MATERIAL" },
  ]

  const handleGetAllApi = async () => {
    const response = await API_ScmIssueClassification().getAll();

    if (response.status === 200) {
      setScmIssueClassification(response.data ? response.data : []);
    }
  };

  const handleGetLotNumberApi = async () => {
    const response = await API_ScmIssueClassification().getLotNumber();
    if (response.status === 200) {
      const options = response.data.lot_number.map((lotNumber) => ({
        label: lotNumber,
        value: lotNumber,
      }));
      setLotNumberOptions(options);
    }
  };  

  const handleUpdateStatusApi = async (id, is_active) => {
    await API_ScmIssueClassification().updateStatus({
      id: id,
      is_active: is_active,
      delete_by: props.employee_id,
    });
    handleGetAllApi();
  };

  const handleAddApi = async () => {
    setIsSubmitting(true);
    try {
      const res = await API_ScmIssueClassification().create({
        ...formValue,
        lot_number: formValue.lot_number,
        create_by: props.employee_id,
        delete_by: props.employee_id,
      });

      if (res.status === 200) {
        toaster.push(Messages("success", "Success: Data has been created!"), {
          placement: "topCenter",
          duration: 5000,
        });
        setShowAddModal(false);
        handleGetAllApi();
      }
    } catch (err) {
      console.log("Axios Error: ", err);
      toaster.push(Messages("error", "Error: Please try again later!"), {
        placement: "topCenter",
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
      setFormValue(emptyForm);
      setErrors(emptyError);
    }
  };

  //   const handleUpdateApi = async () => {
  //     setIsSubmitting(true);
  //     try {
  //       const res = await API_ScmIssueClassification().update({
  //         ...formValue,
  //         lot_number: formValue.lot_number,
  //         id: formValue.id,
  //         update_by: props.employee_id,
  //       });

  //       if (res.status === 200) {
  //         toaster.push(Messages("success", "Success: Data has been updated!"), {
  //           placement: "topCenter",
  //           duration: 5000,
  //         });
  //         setShowUpdateModal(false);
  //         handleGetAllApi();
  //       }
  //     } catch (err) {
  //       console.log("Axios Error: ", err);
  //       toaster.push(Messages("error", "Error: Please try again later!"), {
  //         placement: "topCenter",
  //         duration: 5000,
  //       });
  //     } finally {
  //       setIsSubmitting(false);
  //       setFormValue(emptyForm);
  //       setErrors(emptyError);
  //     }
  //   };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("scm/issue_classification")
      );
      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetAllApi();
    }
  }, []);

  //   useEffect(() => {
  //     const fetchLotNumbers = async () => {
  //       setIsLoading(true);
  //       try {
  //         const response = await fetch("http://10.106.1.41:8077/scm/lot-number");
  //         if (!response.ok) {
  //           throw new Error("Failed to fetch lot numbers");
  //         }
  //         const result = await response.json();
  //         const options = result.data.lot_number.map((lot_number) => ({
  //           label: lot_number,
  //           value: lot_number,
  //         }));
  //         setLotNumberOptions(options);
  //       } catch (error) {
  //         console.error("Error fetching lot numbers:", error);
  //       } finally {
  //         setIsLoading(false);
  //       }
  //     };

  //     fetchLotNumbers();
  //   }, []);

  useEffect(() => {
    const fetchLotNumbers = async () => {
      setIsLoading(true);
      try {
        await handleGetLotNumberApi();
      } catch (error) {
        console.error("Error fetching lot numbers:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLotNumbers();
  }, []);

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = scmIssueClassification.filter((rowData, i) => {
    const searchFields = [
      "id",
      "lot_number",
      "classification",
      "remarks",
      "create_by",
      "create_date",
      "update_by",
      "update_date",
      "delete_by",
      "delete_date",
      "is_active",
    ];

    const matchesSearch = searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword
    ? filteredData.length
    : scmIssueClassification.length;

  return (
    <>
      <div>
        <Head>
          <title>SCM Issue Classification</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>SCM</Breadcrumb.Item>
                  <Breadcrumb.Item active>
                    SCM Issue Classification
                  </Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <IconButton
                  icon={<PlusRoundIcon />}
                  appearance="primary"
                  onClick={() => {
                    setShowAddModal(true);
                  }}
                >
                  Add
                </IconButton>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, index) => {
                    return index + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              {/* <Column width={70} align="center">
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id" />
              </Column> */}
              <Column width={200} sortable resizable>
                <HeaderCell align="center">Lot Number</HeaderCell>
                <Cell dataKey="lot_number" />
              </Column>
              <Column width={200} sortable resizable>
                <HeaderCell align="center">Classification</HeaderCell>
                <Cell dataKey="classification" />
              </Column>
              <Column width={200} sortable resizable>
                <HeaderCell align="center">Remarks</HeaderCell>
                <Cell dataKey="remarks" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell align="center">Create By</HeaderCell>
                <Cell dataKey="create_by" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell align="center">Create Date</HeaderCell>
                <Cell align="center" dataKey="create_date" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell align="center">Update By</HeaderCell>
                <Cell dataKey="update_by" />
              </Column>
              <Column width={150} sortable resizable align="center">
                <HeaderCell align="center">Update Date</HeaderCell>
                <Cell dataKey="update_date" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell align="center">Delete By</HeaderCell>
                <Cell dataKey="delete_by" />
              </Column>
              <Column width={150} align="center" sortable resizable>
                <HeaderCell>Delete Date</HeaderCell>
                <Cell dataKey="delete_date" />
              </Column>
              <Column width={100} align="center" sortable resizable>
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={100} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell style={{ padding: "8px" }}>
                  {(rowData) => (
                    <div>
                      <Button
                        appearance="subtle"
                        disabled={rowData.is_active === 0}
                        onClick={() => {
                          setShowUpdateModal(true);
                          setFormValue(rowData);
                        }}
                      >
                        <EditIcon />
                      </Button>
                      <Button
                        appearance="subtle"
                        onClick={() => {
                          handleUpdateStatusApi(
                            rowData.id,
                            rowData.is_active === 1 ? 0 : 1
                          );
                        }}
                      >
                        {rowData.is_active === 1 ? (
                          <TrashIcon style={{ fontSize: "16px" }} />
                        ) : (
                          <ReloadIcon style={{ fontSize: "16px" }} />
                        )}
                      </Button>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Add Modal */}
        <Modal
          backdrop="static"
          open={showAddModal}
          overflow={false}
          onClose={() => {
            setShowAddModal(false);
            setFormValue(emptyForm);
            setErrors(emptyError);
          }}
        >
          <Modal.Header>
            <Modal.Title>Add SCM Issue Classification</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Lot Number</Form.ControlLabel>
                <Form.Control
                  accepter={SelectPicker}
                  data={lotNumberOptions}
                  value={formValue.lot_number}
                  onChange={(value) => {
                    setFormValue({ ...formValue, lot_number: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      lot_number: null,
                    }));
                  }}
                  errorMessage={errors.lot_number}
                  style={{ width: "100%" }}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Classification</Form.ControlLabel>
                <Form.Control
                  name="classification"
                  accepter={SelectPicker}
                  data={ClassificationDataDropdown}
                  value={formValue.classification}
                  onChange={(value) => {
                    setFormValue({ ...formValue, classification: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      classification: null,
                    }));
                  }}
                  errorMessage={errors.classification}
                  style={{ width: "100%" }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Remarks</Form.ControlLabel>
                <Form.Control
                  name="remarks"
                  value={formValue.remarks}
                  onChange={(value) => {
                    setFormValue({ ...formValue, remarks: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      remarks: null,
                    }));
                  }}
                  errorMessage={errors.remarks}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowAddModal(false);
                setFormValue(emptyForm);
                setErrors(emptyError);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleAddApi();
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Update Modal */}
        {/* <Modal
          backdrop="static"
          open={showUpdateModal}
          overflow={false}
          onClose={() => {
            setShowUpdateModal(false);
            setFormValue(emptyForm);
            setErrors(emptyError);
          }}
        >
          <Modal.Header>
            <Modal.Title>Update SCM Issue Classification</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Lot Number</Form.ControlLabel>
                <Form.Control
                  accepter={SelectPicker}
                  data={lotNumberOptions}
                  value={formValue.lot_number}
                  onChange={(value) => {
                    setFormValue({ ...formValue, lot_number: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      lot_number: null,
                    }));
                  }}
                  errorMessage={errors.lot_number}
                  style={{ width: "100%" }}
                  placeholder={isLoading ? "Loading..." : "Select a lot number"}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Classification</Form.ControlLabel>
                <Form.Control
                  name="classification"
                  value={formValue.classification}
                  onChange={(value) => {
                    setFormValue({ ...formValue, classification: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      classification: null,
                    }));
                  }}
                  errorMessage={errors.classification}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Remarks</Form.ControlLabel>
                <Form.Control
                  name="remarks"
                  value={formValue.remarks}
                  onChange={(value) => {
                    setFormValue({ ...formValue, remarks: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      remarks: null,
                    }));
                  }}
                  errorMessage={errors.remarks}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowUpdateModal(false);
                setFormValue(emptyForm);
                setErrors(emptyError);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleUpdateApi(formValue.id);
              }}
              appearance="primary"
              type="submit"
            >
              Update
            </Button>
          </Modal.Footer>
        </Modal> */}
      </ContainerLayout>
    </>
  );
}
