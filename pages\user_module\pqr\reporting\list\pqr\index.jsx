import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  Stack,
  Panel,
  Tag,
  Form,
  Grid,
  Row,
  Col,
  Table,
  Pagination,
  useToaster,
  But<PERSON>,
  Modal,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";
import ApiReporting from "@/pages/api/pqr/reporting_list/api_reporting";
import ApiStep from "@/pages/api/pqr/step/api_masterdata_step";
import TableWerumComponents from "@/components/pqr/TableWerumComponents";
import HeaderRecipeComponents from "@/components/pqr/HeaderRecipeComponents";

export default function Index() {
  const { HeaderCell, Cell, Column } = Table;
  const toaster = useToaster();
  // const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [clickedPanelIndex, setClickedPanelIndex] = useState(null);
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_trans_detail");
  const [sortType, setSortType] = useState("desc");
  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [idRouter, setIdRouter] = useState(null);
  const router = useRouter();
  const { IdHeader } = router.query;

  const initialFormDataHeader = {
    id_trans_header: "",
    id_ppi: "",
    ppi_name: "",
    batch_code: "",
    iot_desc: "",
    line_desc: "",
    remarks: "",
    wetmill: "",
    status_transaction: "",
    create_date: "",
    create_by: "",
    update_date: "",
    update_by: "",
    delete_date: "",
    delete_by: "",
  };

  const [transactionDetailsDataState, setTransactionDetailDataState] = useState(
    []
  );

  const [stepsDataState, setStepsDataState] = useState([]);

  const [formDataHeader, setformDataHeader] = useState(initialFormDataHeader);

  const [showDetailWerumModal, setShowDetailWerumModal] = useState(false);
  const [showDetailReceiptModal, setShowDetailReceiptModal] = useState(false);

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const getPaginatedData = (data, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return data.slice(start, end);
  };

  const getFilteredData = (data = {}, panelIndex) => {
    // console.log(data);

    data = data.map((val) => {
      let sp = 0;
      // console.log("binding =", val.binding_type);
      if (data.binding_type === 1) {
        sp =
          val.min_value !== undefined && val.max_value !== undefined
            ? `${Number(val.min_value).toLocaleString("id-ID")} - ${Number(
                val.max_value
              ).toLocaleString("id-ID")}`
            : "-";
      } else if (val.binding_type === 2) {
        sp =
          val.absolute_value !== undefined && val.absolute_value !== null
            ? Number(val.absolute_value).toLocaleString("id-ID")
            : "-";
      } else if (val.binding_type === 3) {
        sp = val.description_value || "-";
      } else if (val.binding_type === 0) {
        sp =
          val.set_point_value !== undefined && val.set_point_value !== null
            ? Number(val.set_point_value).toLocaleString("id-ID")
            : "-";
      }
      // sp = Number(sp);
      // console.log(sp);
      return {
        ...val,
        standard_parameter: sp,
      };
    });

    if (clickedPanelIndex !== null && panelIndex !== clickedPanelIndex) {
      return data;
    }
    if (sortColumn && sortType) {
      // console.log(sortColumn, sortType);
      return data.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        // if (typeof x === "string" && typeof y === "string") {
        //   try {
        //     const p = parseInt(x.replace(".", ""), 10);
        //     const q = parseInt(y.replace(".", ""), 10);
        //     x = p;
        //     y = q;
        //   } catch (error) {
        //     console.error(error);
        //   }
        // }
        // console.log(typeof x, typeof y);

        // console.log(x, y);
        if (typeof x === "number" && typeof y === "number") {
          if (sortType === "asc") {
            return x - y;
          } else {
            return y - x;
          }
        }
        if (sortType === "asc") {
          return x.localeCompare(y);
        } else {
          return y.localeCompare(x);
        }
      });
    }
  };

  const formatDate = (rowData, dateKey) => {
    const formattedDate = rowData[dateKey];

    if (!formattedDate) return "-";

    const date = new Date(formattedDate);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  const HandleGetTransactionHeaderDetail = async (id_trans_header) => {
    try {
      const apiReporting = ApiReporting();
      const response = await apiReporting.getPPITransactionHeaderDetail({
        id_trans_header: parseInt(id_trans_header),
      });
      if (response.status === 200) {
        const data = response.data;
        setformDataHeader({
          id_trans_header: data.id_trans_header,
          id_ppi: data.id_ppi,
          ppi_name: data.ppi_name,
          batch_code: data.batch_code,
          iot_desc: data.iot_desc,
          line_desc: data.line_desc,
          remarks: data.remarks,
          wetmill: data.wetmill,
          status_transaction: data.status_transaction,
          create_date: data.header_create_date
            ? new Date(data.header_create_date).toLocaleDateString("en-GB")
            : "-",
          create_by: data.header_create_by || "-",
          update_date: data.header_update_date
            ? new Date(data.header_update_date).toLocaleDateString("en-GB")
            : "-",
          update_by: data.header_update_by || "-",
          delete_date: data.header_delete_date
            ? new Date(data.header_delete_date).toLocaleDateString("en-GB")
            : "-",
          delete_by: data.header_delete_by || "-",
        });

        setTransactionDetailDataState(response.data.detail);
        HandleGetAllActiveStepApi(data.id_ppi);
        return data;
      } else {
        console.error("Failed to fetch detail data");
        return null;
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
      return null;
    }
  };

  const HandleGetAllActiveStepApi = async (id_ppi) => {
    try {
      const res = await ApiStep().getAllActiveStepPPI({
        id_ppi: parseInt(id_ppi),
      });
      if (res.status === 200) {
        const options = res.data.map((steps) => ({
          label: steps.step_name,
          value: steps.id_step,
        }));
        setStepsDataState(options);
      } else {
        console.log("Error on GetAllStepApi: ", res.message);
      }
    } catch (error) {
      console.log("Error on catch GetAllStepApi: ", error.message);
    }
  };

  // const HandleGetAllStepWetmillData = async (id_step, id_ppi) => {
  //   try {
  //     let res = null;
  //     if (formDataHeader.wetmill === "Y") {
  //       res = await Api_param_ppi().getStepWetmillY({
  //         id_step,
  //         id_ppi: parseInt(formDataHeader.id_ppi),
  //         id_trans_header: parseInt(idRouter),
  //         create_by: sessionAuth ? sessionAuth.employee_name : "",
  //       });
  //     } else if (formDataHeader.wetmill === "N") {
  //       res = await Api_param_ppi().getStepWetmillN({
  //         id_step,
  //         id_ppi: parseInt(formDataHeader.id_ppi),
  //         id_trans_header: parseInt(idRouter),
  //         create_by: sessionAuth ? sessionAuth.employee_name : "",
  //       });
  //     } else {
  //       console.log("Invalid wetmill value");
  //       return;
  //     }
  //     if (res?.status === 200) {
  //       setStepWetmillDataState(res.data.data_automate || []);
  //       HandleGetTransactionHeaderDetail(IdHeader);
  //       return res.data.data_automate;
  //     } else {
  //       console.error("Failed to fetch step data", res?.message);
  //       setStepWetmillDataState([]);
  //       return [];
  //     }
  //   } catch (error) {
  //     console.error("Error fetching step data:", error);
  //     setStepWetmillDataState([]);
  //     return [];
  //   }
  // };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else if (IdHeader) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/operator")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      console.log("Router data:", IdHeader);
      setIdRouter(IdHeader);

      HandleGetTransactionHeaderDetail(IdHeader);
    }
  }, [router]);

  useEffect(() => {
    console.log("Transaction detail = ", transactionDetailsDataState);
  }, [transactionDetailsDataState]);

  useEffect(() => {
    console.log("CLICKED INDEX = ", clickedPanelIndex);
  }, [clickedPanelIndex]);

  return (
    <div>
      <div>
        <Head>
          <title>Transaction Header Detail</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>E Release</Breadcrumb.Item>
                  <Breadcrumb.Item>List</Breadcrumb.Item>
                  <Breadcrumb.Item active>Transaction Header</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <Form layout="vertical">
                  <Grid fluid>
                    <Row style={{ marginBottom: "16px" }}>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>
                            ID Transaksi Header
                          </Form.ControlLabel>
                          <Form.Control
                            name="id_trans_header"
                            value={formDataHeader.id_trans_header}
                            style={{ width: "100%" }}
                          />
                        </Form.Group>
                      </Col>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                          <Form.Control
                            name="ppi_name"
                            value={formDataHeader.ppi_name}
                            style={{ width: "100%" }}
                          />
                        </Form.Group>
                      </Col>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Batch Code</Form.ControlLabel>
                          <Form.Control
                            name="batch_code"
                            value={formDataHeader.batch_code}
                            style={{ width: "100%" }}
                          />
                        </Form.Group>
                      </Col>
                      <Row style={{ marginBottom: "16px" }}></Row>
                      <Row style={{ marginBottom: "16px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Remarks</Form.ControlLabel>
                            <Form.Control
                              name="remarks"
                              value={formDataHeader.remarks}
                              style={{ width: "100%" }}
                            />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>
                              Status Transaksi
                            </Form.ControlLabel>
                            <Form.Control
                              name="status_transaction"
                              value={
                                formDataHeader.status_transaction === 2
                                  ? "Draft"
                                  : formDataHeader.status_transaction === 1
                                  ? "Done"
                                  : "Dropped"
                              }
                              style={{ width: "100%" }}
                            />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Wetmill</Form.ControlLabel>
                            <Form.Control
                              name="wetmill"
                              value={
                                formDataHeader.wetmill === "Y" ? "Yes" : "No"
                              }
                              style={{ width: "100%" }}
                            />
                          </Form.Group>
                        </Col>
                      </Row>                      
                      <Row style={{ marginBottom: "24px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>
                              Tanggal Dibuat
                            </Form.ControlLabel>
                            <Form.Control
                              name="create_date"
                              value={formDataHeader.create_date}
                              style={{ width: "100%" }}
                            />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                            <Form.Control
                              name="created_by"
                              value={formDataHeader.create_by}
                              style={{ width: "100%" }}
                            />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>
                              Tanggal Diperbarui
                            </Form.ControlLabel>
                            <Form.Control
                              name="update_date"
                              value={formDataHeader.update_date}
                              style={{ width: "100%" }}
                            />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>
                              Diperbarui Oleh
                            </Form.ControlLabel>
                            <Form.Control
                              name="update_by"
                              value={formDataHeader.update_by}
                              style={{ width: "100%" }}
                            />
                          </Form.Group>
                        </Col>
                      </Row>
                      <Col style={{ paddingRight: "16px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                          <Form.Control
                            name="delete_date"
                            value={formDataHeader.delete_date}
                            style={{ width: "100%" }}
                          />
                        </Form.Group>
                      </Col>
                      <Row style={{ marginBottom: "16px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                            <Form.Control
                              name="delete_by"
                              value={formDataHeader.delete_by}
                              style={{ width: "100%" }}
                            />
                          </Form.Group>
                        </Col>
                      </Row>
                      <Row >
                        <Col style={{ paddingRight: "16px" }}>
                            <Button
                              appearance="primary"
                              color="ghost"
                              onClick={() => {
                                setShowDetailWerumModal(true);
                                // HandleGetDetailPQRWerum(rowData.batch_code)
                              }}
                            >
                              Lihat Data Werum
                            </Button>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                            <Button
                              appearance="primary"
                              color="ghost"
                              onClick={() => {
                                setShowDetailReceiptModal(true);
                              }}
                            >
                              Lihat Data Resep
                            </Button>
                        </Col>
                      </Row>
                    </Row>
                  </Grid>
                </Form>
              </Stack>
            }
          />
          {transactionDetailsDataState?.map((stepData, index) => (
            <Panel
              key={index}
              header={
                <Stack justifyContent="space-between">
                  <h5>
                    Reporting {formDataHeader.ppi_name} "{stepData.step_name}"
                  </h5>
                </Stack>
              }
              shaded
              bordered
              className="mb-3"
              onClick={() => setClickedPanelIndex(index)}>
              <Table
                bordered
                cellBordered
                height={300}
                defaultExpandAllRows
                data={getPaginatedData(
                  getFilteredData(stepData.detail_step, index),
                  limit,
                  page
                )}
                sortColumn={sortColumn}
                sortType={sortType}
                onSortColumn={(column, type) => {
                  console.log(column, type);
                  setSortColumn(column);
                  setSortType(type);
                }}
                autoHeight>
                <Column width={300} align="center" sortable resizable>
                  <HeaderCell>Tanggal Dibuat</HeaderCell>
                  <Cell dataKey="detail_create_date">
                    {(rowData) => formatDate(rowData, "detail_create_date")}
                  </Cell>
                </Column>
                <Column width={300} align="center" resizable>
                  <HeaderCell>Nama Parameter</HeaderCell>
                  <Cell dataKey="parameter_name">
                    {(rowData) => rowData.parameter_name || "-"}
                  </Cell>
                </Column>
                <Column width={250} align="center" fullText>
                  <HeaderCell>Standard Parameter</HeaderCell>
                  <Cell dataKey="standard_parameter"></Cell>
                  {/* <Cell dataKey="binding_type">
                    {(rowData) => {
                      console.log(rowData);
                      let valueToDisplay;
                      if (rowData.binding_type === 1) {
                        valueToDisplay =
                          rowData.min_value !== undefined &&
                          rowData.max_value !== undefined
                            ? `${Number(rowData.min_value).toLocaleString(
                                "id-ID"
                              )} - ${Number(rowData.max_value).toLocaleString(
                                "id-ID"
                              )}`
                            : "-";
                      } else if (rowData.binding_type === 2) {
                        valueToDisplay =
                          rowData.absolute_value !== undefined &&
                          rowData.absolute_value !== null
                            ? Number(rowData.absolute_value).toLocaleString(
                                "id-ID"
                              )
                            : "-";
                      } else if (rowData.binding_type === 3) {
                        valueToDisplay = rowData.description_value || "-";
                      } else if (rowData.binding_type === 0) {
                        valueToDisplay =
                          rowData.set_point_value !== undefined &&
                          rowData.set_point_value !== null
                            ? Number(rowData.set_point_value).toLocaleString(
                                "id-ID"
                              )
                            : "-";
                      }
                      console.log(typeof valueToDisplay);
                      return <span>{Number(valueToDisplay)}</span>;
                    }}
                  </Cell> */}
                </Column>
                <Column width={150} align="center" resizable>
                  <HeaderCell>Set Point Flag</HeaderCell>
                  <Cell dataKey="set_point_flag">
                    {(rowData) => {
                      const flagValue = rowData.set_point_flag;

                      if (flagValue == 1) {
                        return "Yes";
                      } else if (flagValue == 0) {
                        return "No";
                      } else {
                        return "-";
                      }
                    }}
                  </Cell>
                </Column>
                <Column width={180} align="center" fullText>
                  <HeaderCell>Nilai Aktual</HeaderCell>
                  <Cell dataKey="set_point_value">
                    {(rowData) => {
                      let valueToDisplay;
                      if (rowData.set_point_value != 0) {
                        valueToDisplay =
                          rowData.set_point_value !== undefined &&
                          rowData.set_point_value !== null
                            ? Number(rowData.set_point_value).toLocaleString(
                                "id-ID"
                              )
                            : "-";
                      } else {
                        valueToDisplay = rowData.actual_value;
                      }
                      return <span>{valueToDisplay}</span>;
                    }}
                  </Cell>
                </Column>
                <Column width={180} align="center" resizable>
                  <HeaderCell>Hasil</HeaderCell>
                  <Cell dataKey="result">
                    {(rowData) => rowData.result || "-"}
                  </Cell>
                </Column>
                <Column width={300} align="center" resizable>
                  <HeaderCell>Dibuat Oleh</HeaderCell>
                  <Cell dataKey="detail_create_by">
                    {(rowData) => rowData.detail_create_by || "-"}
                  </Cell>
                </Column>
                {/* <Column width={120} align="center" sortable resizable>
                  <HeaderCell>Actual Value</HeaderCell>
                  <Cell>{(rowData) => rowData.actual_value || "-"}</Cell>
                </Column> */}
              </Table>
              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={stepData.detail_step?.length || 0}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
          ))}
        </div>
        <Modal 
          backdrop="static" 
          open={showDetailWerumModal} 
          onClose={() => {
            setShowDetailWerumModal(false);
          }} 
          overflow={false} 
          size={"lg"}>
            <Modal.Header>
              <Modal.Title>Detail Data Werum</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <TableWerumComponents
                  batchCode={formDataHeader.batch_code}
                />                
            </Modal.Body>
          </Modal> 

           <Modal 
          backdrop="static" 
          open={showDetailReceiptModal} 
          onClose={() => {
            setShowDetailReceiptModal(false);
          }} 
          overflow={false} 
          size={"lg"}>
            <Modal.Header>
              <Modal.Title>Detail Data Resep</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <HeaderRecipeComponents
                  transactionRecipeHeaderId={formDataHeader.id_trans_header}
                />                
            </Modal.Body>
          </Modal>        
      </ContainerLayout>
    </div>
  );
}
