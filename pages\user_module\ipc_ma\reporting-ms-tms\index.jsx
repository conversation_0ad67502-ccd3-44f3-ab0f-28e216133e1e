import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import {
  DateRangePicker,
  Stack,
  Panel,
  Button,
  Breadcrumb,
  SelectPicker,
  DatePicker,
  Table,
} from "rsuite";

import Api_IPC_MA_DASHBOARD from "@/pages/api/ipc_ma/api_ipc_ma_dashboard.js";
import ApiIpcMethod from "@/pages/api/ipc/api_ipc_method.js";

import XLSX from "xlsx";

import ContainerLayout from "@/components/layout/ContainerLayout";

import ChartDataLabels from "chartjs-plugin-datalabels";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
  ChartDataLabels
);

import { Line } from "react-chartjs-2";

export default function ReportingMAMsTms() {
  const router = useRouter();
  const [moduleName, setModuleName] = useState("");
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const { HeaderCell, Cell, Column } = Table;
  const thirtyDaysAgo = new Date(new Date().setDate(new Date().getDate() - 30));

  const [startDate, setStartDate] = useState(thirtyDaysAgo);
  const [endDate, setEndDate] = useState(new Date());

  const [stateDashboardData, setStateDashboardData] = useState([]);

  const [selectedBatchCode, setSelectedBathCode] = useState("");

  const [stateFilteredDashboardData, setStateFilteredDashboardData] = useState(
    []
  );
  const [
    stateFilteredDashboardReasonData,
    setStateFilteredDashboardReasonData,
  ] = useState([]);

  const [dateForm, setDateForm] = useState(null);

  const [stateReasonData, setStateReasonData] = useState([]);
  const [stateMethodData, setStateMethodData] = useState([]);
  const [selectedMethod, setSelectedMethod] = useState(null);
  const [
    stateFilteredDashboardMethodData,
    setStateFilteredDashboardMethodData,
  ] = useState([]);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("ipc_ma/reporting-ms-tms")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
    }
    setStartDate(thirtyDaysAgo);
    setEndDate(new Date());

    handleFetchData();
  }, []);

  const handleFetchData = async () => {
    setLoading(true);
    const data = await Api_IPC_MA_DASHBOARD().getIpcMAReportExcel({
      start_date: startDate,
      end_date: endDate,
    });
    console.log("data", data || []);
    setData(data.data || []);
    setLoading(false);

    const ipcDashboard = await Api_IPC_MA_DASHBOARD().getIpcMADashboard();
    console.log("ipc data", ipcDashboard);

    setStateDashboardData(ipcDashboard.data || []);

    const resReason = await Api_IPC_MA_DASHBOARD().getIpcMAReason();

    setStateReasonData(resReason.data || []);

    const resMethod = await ApiIpcMethod().getAllActiveIPCMethod();
    setStateMethodData(resMethod.data || []);
  };

  const formatDateToShort = (date) => {
    if (!(date instanceof Date)) {
      date = new Date(date);
    }
    let month = "" + (date.getMonth() + 1);
    let day = "" + date.getDate();
    const year = date.getFullYear();

    if (month.length < 2) month = "0" + month;
    if (day.length < 2) day = "0" + day;

    return [year, month, day].join("-");
  };

  const exportFile = (data) => {
    const formattedData = data.map((item) => ({
      Date: item.formatted_date,
      "TMS Count": item.tms_count,
      "MS Count": item.ms_count,
    }));

    const ws = XLSX.utils.json_to_sheet(formattedData);

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const formattedStartDate = formatDateToShort(startDate);
    const formattedEndDate = formatDateToShort(endDate);
    const fileName =
      "reporting-ms-tms" +
      " " +
      formattedStartDate +
      " to " +
      formattedEndDate +
      ".xlsx";

    XLSX.writeFile(wb, fileName);
  };

  const prepareChartData = (data) => {
    const labels = data.map((item) => item.formatted_date);
    const msCounts = data.map((item) => item.ms_count);
    const tmsCounts = data.map((item) => item.tms_count);

    return {
      labels,
      datasets: [
        {
          label: "MS Count",
          data: msCounts,
          backgroundColor: "#0f7141",
        },
        {
          label: "TMS Count",
          data: tmsCounts,
          backgroundColor: "#d32f2f",
        },
      ],
    };
  };

  const LineChart = ({ chartData }) => {
    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: "right",
        },
        title: {
          display: true,
          text: "MS and TMS Counts Over Time",
        },
        datalabels: {
          anchor: "end",
          align: "end",
          color: function (context) {
            return context.dataset.backgroundColor;
          },
          font: {
            weight: "bold",
          },
          formatter: (value, context) => {
            const formattedValue = value.toLocaleString();
            return formattedValue;
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return <Line data={chartData} options={options} />;
  };

  useEffect(() => {
    console.log("dorp down set", stateDashboardData);
  }, [stateDashboardData]);

  useEffect(() => {
    if (!selectedBatchCode) {
      setStateFilteredDashboardData([]);
      setStateFilteredDashboardReasonData([]);
      setStateFilteredDashboardMethodData([]);
      return;
    }

    let filteredTransactions = stateDashboardData.filter(
      (transaction) => transaction.product_code === selectedBatchCode
    );
    let filteredReason = stateReasonData.filter(
      (item) => item.product_code === selectedBatchCode
    );

    if (selectedMethod) {
      filteredTransactions = filteredTransactions.filter(
        (transaction) => transaction.step === selectedMethod
      );
      filteredReason = filteredReason.filter(
        (item) => item.step === selectedMethod
      );
    }

    setStateFilteredDashboardData(filteredTransactions);
    setStateFilteredDashboardReasonData(filteredReason);
    setStateFilteredDashboardMethodData(filteredTransactions);
  }, [selectedBatchCode, selectedMethod, stateDashboardData, stateReasonData]);

  const droppedDownData = (data) => {
    if (dateForm) {
      const filteredData = data.filter((item) => {
        const itemDate = new Date(item.created_date);
        return itemDate.toDateString() === dateForm.toDateString();
      });
      const uniqueProductCodes = [
        ...new Set(filteredData.map((item) => item.product_code)),
      ];

      const result = uniqueProductCodes.map((code) => ({
        label: code,
        value: code,
      }));

      return result;
    } else {
      return [];
    }
  };

  const droppedDownDataMethod = (data) => {
    if (dateForm) {
      const filteredData = data.filter((item) => {
        const itemDate = new Date(item.created_date);
        return itemDate.toDateString() === dateForm.toDateString();
      });
      const uniqueStepCodes = [
        ...new Set(filteredData.map((item) => item.step)),
      ];

      const result = uniqueStepCodes.map((code) => ({
        label: code,
        value: code,
      }));

      return result;
    } else {
      return [];
    }
  };

  const prepareWeightChartData = (data) => {
    const labels = data.map((item) => item.info + " " + item.status_ipc_ma);
    const avg = data.map((item) => item.ma_avg_weight);
    const min = data.map((item) => item.spec_ma_min_weight);
    const max = data.map((item) => item.spec_ma_max_weight);

    return {
      labels,
      datasets: [
        {
          label: "Avg Weight",
          data: avg,
          backgroundColor: "#FF0000",
          borderColor: "#FF0000",
        },
        {
          label: "Max Spec Weight",
          data: max,
          backgroundColor: "#0000FF",
          borderColor: "#0000FF",
        },
        {
          label: "Min Spec Weight",
          data: min,
          backgroundColor: "#FFA507",
          borderColor: "#FFA500",
        },
      ],
    };
  };

  const LineWeightChart = ({ chartData, title }) => {
    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: "right",
        },
        title: {
          display: true,
          text: title,
          padding: {
            bottom: 50,
            top: 20,
          },
        },
        datalabels: {
          anchor: "end",
          align: "end",
          color: function (context) {
            return context.dataset.backgroundColor;
          },
          font: {
            weight: "bold",
          },
          formatter: (value, context) => {
            const formattedValue = value.toLocaleString();
            return formattedValue;
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return <Line data={chartData} options={options} />;
  };

  return (
    <>
      <div>
        <Head>
          <title>Reporting MA MS and TMS</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item active>
              {moduleName ? moduleName : "User Module"} / Reporting MA MS and
              TMS{" "}
            </Breadcrumb.Item>
          </Breadcrumb>
          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h4>Reporting MA MS and TMS</h4>
            </Stack>
          </Panel>
          <div>
            <Panel bordered>
              <Stack className="gap-2">
                <Stack.Item>
                  <DatePicker
                    value={dateForm}
                    onChange={(value) => {
                      setDateForm(value);
                      setSelectedBathCode(null);
                      setSelectedMethod(null);
                      setStateFilteredDashboardData([]);
                      setStateFilteredDashboardMethodData([]);
                      console.log(value);
                    }}
                    onClose={() => {
                      if (!dateForm) {
                        setDateForm(null);
                        setSelectedBathCode(null);
                        setSelectedMethod(null);
                      }
                      setStateFilteredDashboardData([]);
                      setStateFilteredDashboardMethodData([]);
                    }}
                  />
                </Stack.Item>

                <Stack.Item>
                  <SelectPicker
                    name="batch code"
                    label="batch code"
                    value={selectedBatchCode}
                    block
                    data={droppedDownData(stateDashboardData)}
                    onChange={(value) => {
                      setSelectedBathCode(value);
                      setSelectedMethod(null);
                    }}
                    onClose={() => {
                      if (!selectedBatchCode) {
                        setDateForm(null);
                        setSelectedMethod(null);
                      }
                    }}
                    disabled={!dateForm}
                  />
                </Stack.Item>

                <Stack.Item>
                  <SelectPicker
                    name="method"
                    label="method"
                    value={selectedMethod}
                    block
                    data={droppedDownDataMethod(stateDashboardData)}
                    onChange={(value) => {
                      setSelectedMethod(value);
                    }}
                    onClose={() => {
                      if (!selectedMethod) {
                        setDateForm(null);
                      }
                    }}
                    disabled={!dateForm || !selectedBatchCode}
                  />
                </Stack.Item>

                <Stack.Item>
                  <Button
                    appearance="primary"
                    onClick={() => {
                      router.push(
                        `/user_module/ipc_ma/reporting-ms-tms/PdfPreview?ssr_batch_code=${selectedBatchCode}${
                          selectedMethod ? `&ssr_method=${selectedMethod}` : ""
                        }`
                      );
                    }}
                    disabled={!dateForm}>
                    Export to PDF
                  </Button>
                </Stack.Item>
              </Stack>

              <LineWeightChart
                chartData={prepareWeightChartData(
                  selectedMethod
                    ? stateFilteredDashboardMethodData
                    : stateFilteredDashboardData
                )}
                plugins={[ChartDataLabels]}
                loading={loading}
                title={
                  selectedBatchCode
                    ? `${selectedBatchCode} ${
                        selectedMethod ? `(${selectedMethod})` : ""
                      } IPC: WEIGHT`
                    : "IPC: WEIGHT"
                }
              />

              <Table
                bordered
                cellBordered
                autoHeight
                data={stateFilteredDashboardReasonData}>
                <Column width={70} align="center" fixed>
                  <HeaderCell>No</HeaderCell>
                  <Cell>
                    {(rowData, rowIndex) => {
                      return rowIndex + 1;
                    }}
                  </Cell>
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Product Code</HeaderCell>
                  <Cell dataKey="product_code" />
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Info</HeaderCell>
                  <Cell dataKey="info" />
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Reason</HeaderCell>
                  <Cell dataKey="reason" />
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Operator</HeaderCell>
                  <Cell dataKey="operator_created" />
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Approval by</HeaderCell>
                  <Cell dataKey="approve_details" />
                </Column>
              </Table>
            </Panel>
          </div>
        </div>
      </ContainerLayout>
    </>
  );
}
