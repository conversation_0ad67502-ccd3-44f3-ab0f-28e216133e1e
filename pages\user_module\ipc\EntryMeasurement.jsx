import {useRouter} from 'next/router';
import {useEffect, useState} from 'react';
import ContainerLayout from '@/components/layout/ContainerLayout';
import MainContent from '@/components/layout/MainContent';
import {Dropdown, Button, InputNumber, Modal, ButtonToolbar, Placeholder} from 'rsuite';
import Table from '@/components/Table';
import ipcApi from '@/pages/api/ipcApi';
import Head from 'next/head';
import withReactContent from 'sweetalert2-react-content';
import Swal from 'sweetalert2';
import {loadingAnimationNoClick} from '@/components/SweetAlertLoading';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import { faL } from '@fortawesome/free-solid-svg-icons';
import ApiIPCNew from '@/pages/api/ipc/api_ipc_new';

export default function EntryMeasurement({parentMenuName, childMenuName}) {
  const [loginData, setLoginData] = useState(null);
  const [isModalShow, setIsModalShow] = useState(false);
  const [formDisabled, setFormDisabled] = useState(false);
  const [submitDisabled, setSubmitDisabled] = useState(false);
  const [isStartMeasureDisabled, setIsStartMeasureDisabled] = useState(false);
  const [isSubmitShown, setIsSubmitShown] = useState(false);
  const [instruments, setInstruments] = useState([]);
  const [productCode, setProductCode] = useState([]);
  const [selectedInstrument, setSelectedInstrument] = useState({});
  const [selectedProductCode, setSelectedProductCode] = useState({});
  const [inputtedData, setInputtedData] = useState({});
  const [measurementData, setMeasurementData] = useState([]);
  const [msTotal, setMSTotal] = useState('0');
  const [tmsTotal, setTMSTotal] = useState('0');
  const [isDataExist, setIsDataExist] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [reQuery, setReQuery] = useState(false);
  const [stationData, setStationData] = useState([])
  const [stepData, setStepData] = useState([])
  const [selectedStationData, setSelectedStationData] = useState({});
  const [selectedStepData, setSelectedStepData] = useState({});
  const [codeData, setCodeData] = useState(0)
  const [startDate, setStartDate] = useState("")
  const [totalOutstandingData, setTotalOutstandingData] = useState(0)
  const [showOutstanding, setShowOutstanding] = useState(false)
  const router = useRouter();
  const {
    GetActiveInstrument,
    GetProductCodeForEntry,
    GetEntryMeasurementData,
    GetTotalMSAndTMS,
    InsertIpcHeader,
    InsertIpcDetail,
    InsertIpcDetailPostgre,
    UpdateStagingStatus,
    GetEntryMeasurementTotalMSAndTMS,
    PutClearStaging,
    UpdateStagingStatusMysql,
    GetEntryMeasurementTotalMSAndTMSMysql,
    PutClearStagingMysql
  } = ipcApi();
  const MySwal = withReactContent(Swal);
  let {data} = router.query;
  data ? (data = JSON.parse(data)) : data;
  const path = 'ipc/EntryMeasurement';
  const [moduleName, setModuleName] = useState('');
  var [breadcrumbsData, setBreadcrumbsData] = useState([]);
  //const breadcrumbsData = [moduleName, parentMenuName, childMenuName];

  // GetActiveInstrument for Dropdown
  const DropdownDataInstrument = async () => {
    const {Data} = await GetActiveInstrument();
    if (Data) {
      setInstruments(Data);
    }
  };

  // For Product Code Dropdown
  const DropdownProductCodeData = async () => {
    const {Data} = await GetProductCodeForEntry();
    if (Data) {
      setProductCode(Data);
    }
  };

  //for station data
  const DropdownDataStation = async () => {
    const Data = [{
      Station: "ST1"
    },
    {
      Station: "ST2"
    },
  ]
    if (Data) {
      setStationData(Data);
    }
  };

  //for step data
  const DropdownDataStep = async () => {
    const Data = [{
      Step: "AW"
    },
    {
      Step: "TG"
    },
    {
      Step: "AK"
    },
    {
      Step: "30 menit"
    }
  ]
    if (Data) {
      setStepData(Data);
    }
  };


  // Get Entry Measurement Data and Total TMS and MS dan set state
  const GetMeasurementData = async (inputData) => {
    // const {Data: entryMeasurement} = await GetEntryMeasurementData(inputData);
    // const {Data: totalOverall} = await GetTotalMSAndTMS(inputData);
    //cara baru baca data
    
    //postgre
    // const {Data: entryMeasurementNew, Data_total: totalOverallNew} = await GetEntryMeasurementTotalMSAndTMS(inputData)
    //mysql
    // const {Data: entryMeasurementNew, Data_total: totalOverallNew} = await GetEntryMeasurementTotalMSAndTMSMysql(inputData)

     //postgre
    // const res = await ApiIPCNew().GetEntryMeasurementTotalMSAndTMS(
    //   inputData
    // )

    // if (res?.connectivity_issue) {
    //   return
    // }

    // const entryMeasurementNew = res.Data
    // const totalOverallNew = res.Data_total


    //mysql
    const res = await ApiIPCNew().GetEntryMeasurementTotalMSAndTMSMysql(
      inputData
    )

    if (res?.connectivity_issue) {
      MySwal.fire({
        icon: 'error',
        title: 'Masalah Konektivitas',
         text: res?.error || 'Tidak dapat mengakses Database IPC',
      });
      return
    }

    const entryMeasurementNew = res.Data
    // const totalOverallNew = res.Data_total
    const totalOverallNew = res.DatatTotal

    console.log("entrymeeeasument data", entryMeasurementNew)
    console.log(totalOverallNew)
    if (entryMeasurementNew && totalOverallNew) {
      setIsDataExist(true);
      setMeasurementData(entryMeasurementNew);
      
      totalOverallNew.map((item) => {
        if (item.Status === 'MS') {
          setMSTotal(`${item.Total}`);
        } else {
          setTMSTotal(`${item.Total}`);
        }
      });
    }

    // if (entryMeasurement && totalOverall) {
    //   setIsDataExist(true);
    //   setMeasurementData(entryMeasurement);
    //   totalOverall.map((item) => {
    //     if (item.Status === 'MS') {
    //       setMSTotal(`${item.Total}`);
    //     } else {
    //       setTMSTotal(`${item.Total}`);
    //     }
    //   });
    // }
  };

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    const moduleNameValue = localStorage.getItem('module_name');
    if (!dataLogin) {
      router.push('/');
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push('/dashboard');
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path)
    );
    if (validateUserAccess.length === 0) {
      router.push('/dashboard');
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ''
    ) {
      router.push('/dashboard');
      return;
    }
    const asPathWithoutQuery = router.asPath.split('?')[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split('/')
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      `${asPathNestedRoutes[1]}`,
      `Melakukan Pengukuran`,
    ];
    setBreadcrumbsData(routerBreadCrumbsData);

    // const asPathWithoutQuery = router.asPath.split("?")[0];
    // const asPathNestedRoutes = asPathWithoutQuery
    //   .split("/")
    //   .filter((v) => v.length);
    // console.log("ModuleNameValue", moduleNameValue);
    // let routerBreadCrumbsData = [
    //   moduleNameValue,
    //   asPathNestedRoutes[1],
    //   asPathNestedRoutes[2],
    // ];
    // setBreadcrumbsData(routerBreadCrumbsData);
    // console.log("AsPatNestedRoutes", asPathNestedRoutes);

    setModuleName(moduleNameValue);
    setLoginData(dataLogin);
  }, []);

  // useEffect yang akan dijalankan ketika user klik start measure
  useEffect(() => {
    if (inputtedData.code_instrument && isLoading) {
      GetMeasurementData(inputtedData);
      setIsModalShow(true);
      setReQuery(true);
    } else {
      setIsModalShow(false);
      setReQuery(false);
    }
  }, [isLoading]);

  useEffect(() => {
    if (isModalShow) {
      MySwal.fire({
        title: 'Melakukan Pengukuran...',
        html: `<p style="text-align:left;">MS : <strong>${
          msTotal ? msTotal : '0'
        }</strong><br />TMS : <strong>${
          tmsTotal ? tmsTotal : '0'
        }</strong></p>`,
        allowOutsideClick: false,
        showConfirmButton: true,
        confirmButtonText: 'Berhenti Mengukur', // Text for the confirm button
      }).then((result) => {
        if (result.isConfirmed) {
          console.log('Measurement confirmed.');
          console.log('ms', msTotal);
          console.log('tms', tmsTotal);
          // You can add additional logic after the confirm button is clicked
          setIsLoading(false)
          setIsSubmitShown(true);
          if (msTotal == 0 && tmsTotal == 0) {
            setIsStartMeasureDisabled(false); 
            setMeasurementData([]);
            setMSTotal('');
            setTMSTotal('');
            setIsDataExist(false);
            setIsSubmitShown(false); 
          }else{
            setIsStartMeasureDisabled(true);
            //matikan bila error
            setSubmitDisabled(false)
            submitHandler()
          }          
        }
      });;
    } else {
      MySwal.close();
    }
  }, [isModalShow, msTotal, tmsTotal]);

  useEffect(() => {
    if (parseInt(msTotal) >= 10) {
      setIsLoading(false);
    }
  }, [tmsTotal, msTotal]);

  // userEffect yang berfungsi untuk mengirim ulang query (jika MS < 10)
  useEffect(() => {
    let queryProcess;
    if (reQuery) {
      queryProcess = setInterval(() => {
        GetMeasurementData(inputtedData);
      }, 5000);
    }

    return () => {
      clearInterval(queryProcess);
    };
  }, [reQuery]);

  // useEffect untuk menentukan tampil atau tidaknya button submit dan disabled pada start measure
  useEffect(() => {
    if (msTotal >= 10 && isDataExist === true) {
      setIsSubmitShown(true);
      setIsStartMeasureDisabled(true);
      //matikan bila error
      setSubmitDisabled(false)
    } else {
      setIsSubmitShown(false);
      setIsStartMeasureDisabled(false);
    }
  }, [msTotal, isDataExist]);

  // Initial render data
  useEffect(() => {
    DropdownDataStation();
    DropdownDataStep();
    DropdownDataInstrument();
    DropdownProductCodeData();
  }, []);

  // MS OK submit button handler
  const submitHandler = async () => {
    loadingAnimationNoClick();

    setSubmitDisabled(true);
    // let dataStatus;
    // if (parseInt(tmsTotal) > 0) {
    //   dataStatus = "N";
    // } else {
    //   dataStatus = "C";
    // }

      //set number
      let newProductCode = codeData.toString()
      if (codeData <10) {
        newProductCode = "0000" + codeData.toString()        
      }else if (codeData < 100){
        newProductCode = "000" + codeData.toString()    
      }else if (codeData < 1000){
        newProductCode = "00" + codeData.toString()    
      }else if (codeData < 1000){
        newProductCode = "0" + codeData.toString()    
      }

      const batchCode = newProductCode

      newProductCode = "S" + selectedProductCode.Product_Code + newProductCode

    // data yang akan dikirim
    let insertDataIpcHeader = {
      operator_id: loginData.employee_id,
      amount_ms: parseInt(msTotal),
      amount_tms: parseInt(tmsTotal),
      step : selectedStepData.Step,
      station : selectedStationData.Station,
      newProductCode : newProductCode,
      batchCode: batchCode,
      start_date: startDate,
    };
    // console.log("new object", startDate)

    insertDataIpcHeader.status = parseInt(tmsTotal) === 0 ? 'C' : 'N';

    // send data ipc Header
    const sendData = await InsertIpcHeader(insertDataIpcHeader);
    const {Data: ipcHeaderId} = sendData;
    if (ipcHeaderId) {
      let inputtedData = [];
      let statusUpdatedData = [];
      let inputedStagingData = [];    

      // Looping untuk menginput data ke tabel ipcDetail dan update stagingStatus
      measurementData.map(async (item) => {
        console.log("first item", item)
        const insertDataIpcDetail = {
          id_transaction_h: ipcHeaderId,
          product_code: selectedProductCode.Product_Code,
          hardness: item.Hardness_Value,
          thickness: item.Thickness_Value,
          diameter: item.Diameter_Value,
          status: item.Overall_Status,
          id_trans_staging: item.Id_Setup,
          hardness_status : item.Hardness_Status,
          diameter_status : item.Diameter_Status,
          thickness_status : item.Thickness_Status,  
          time_insert: item.Time_insert,        
        };

        const insertDataIpcStaging = {
          id_setup: item.Id_Setup,
          h_value: item.Hardness_Value,
          d_value: item.Diameter_Value,
          t_value: item.Thickness_Value,
          status: 'C',
          code_instrument: item.Code_Instrument,
          created_date: item.Created_Date,
        };

        const updateStagingStatusData = {
          status: 'C',
          id_setup: item.Id_Setup,
        };

        // send request (update stagingStatus)
        //postgresql
        // const res = await ApiIPCNew().UpdateStagingStatus(
        //   updateStagingStatusData
        // )

        // if (res?.connectivity_issue) {
        //   MySwal.fire({
        //     icon: 'error',
        //     title: 'Masalah Konektivitas',
        //      text: res?.error || 'Tidak dapat mengakses Database IPC',
        //   });
        //   return
        // }

        // const updateStagingStatusReturn = res.Data
        
        //mysql
        const res = await ApiIPCNew().UpdateStagingStatusMysql(
          updateStagingStatusData
        )

        if (res?.connectivity_issue) {
          MySwal.fire({
            icon: 'error',
            title: 'Masalah Konektivitas',
             text: res?.error || 'Tidak dapat mengakses Database IPC',
          });
          return
        }

        const updateStagingStatusReturn = res.Data


        if (updateStagingStatusReturn) {
          statusUpdatedData.push(updateStagingStatusReturn);
        }

        // insert data to postgres DB
        // const { data: insertIpcStagingData } = await InsertIpcStaging(
        //   insertDataIpcStaging
        // );
        // if (
        //   insertIpcStagingData !== null &&
        //   insertIpcStagingData !== undefined
        // ) {
        //   inputedStagingData.push(insertIpcStagingData);
        // }

        // send request (insert ipcDetail)

        console.log("ini data ", insertDataIpcDetail)
        const {Data: ipcDetailReturn} = await InsertIpcDetailPostgre(
          insertDataIpcDetail
        );
        if (ipcDetailReturn) {
          inputtedData.push(ipcDetailReturn);
        }

        // Cek jika semua data sudah terinput maka akan menutup modal
        if (
          inputtedData.length === measurementData.length &&
          statusUpdatedData.length === measurementData.length 
          // &&
          // inputedStagingData.length === measurementData.length
        ) {
          console.log('Passed.');
           //sent Data to be cleared
           let insertInstrumentCleared = {
            code_instrument: selectedInstrument.Code_Instrument,
          };
          //postgresql
          // const {Data: dataCleared} = await PutClearStaging(
          //   insertInstrumentCleared
          // );
          //mysql
          // const {Data: dataCleared} = await PutClearStagingMysql(
          //   insertInstrumentCleared
          // );
          // console.log(dataCleared)


          //postgresql
          // const res = await ApiIPCNew().PutClearStaging(
          //   insertInstrumentCleared
          // )
      
          //  if (res?.connectivity_issue) {
    //   MySwal.fire({
    //     icon: 'error',
    //     title: 'Masalah Konektivitas',
    //      text: res?.error || 'Tidak dapat mengakses Database IPC',
    //   });
    //   return
    // }
      
          // const dataCleared = res.Data

          //mysql
          const res = await ApiIPCNew().PutClearStagingMysql(
            insertInstrumentCleared
          )
      
          if (res?.connectivity_issue) {
            MySwal.fire({
              icon: 'error',
              title: 'Masalah Konektivitas',
               text: res?.error || 'Tidak dapat mengakses Database IPC',
            });
            return
          }
      
          const dataCleared = res.Data


          
        //sent data to be cleared
          MySwal.fire({
            position: 'center',
            icon: 'success',
            title: 'Your transaction is saved.',
            showConfirmButton: true,
            allowOutsideClick: false,
            confirmButtonText: 'OK',
          }).then(async (result) => {           
            if (result.isConfirmed) {              
              setMeasurementData([]);
              setMSTotal('');
              setTMSTotal('');
              setIsDataExist(false);
              setIsSubmitShown(false);
            }
          });
          return;
        }
      });
    }
  };

  // Dropdown instrument selection Handler
  const parameterInstrumentSelectionHandler = (value) => {
    if (isDataExist) {
      if (selectedInstrument.Id_Instrument !== value.Id_Instrument) {
        MySwal.fire({
          icon: 'warning',
          title: 'Are you sure ?',
          text: 'If you change the parameter, all your previous data will be reset.',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          confirmButtonText: 'Yes, change it',
        }).then((result) => {
          if (result.isConfirmed) {
            setMeasurementData([]);
            setMSTotal('');
            setTMSTotal('');
            setIsDataExist(false);
            setIsSubmitShown(false);
            setIsStartMeasureDisabled(false);

            if (value.Id_Instrument) {
              setSelectedInstrument({...value});
            } else {
              setSelectedInstrument({});
            }

            if (value.Id_Setup) {
              setSelectedProductCode({...value});
            } else {
              setSelectedProductCode({});
            }
            MySwal.fire({
              icon: 'success',
              text: 'Parameter changed.',
            });
          }
        });
        return;
      }
    } else {
      setSelectedInstrument({...value});
    }
  };

  // Dropdown productcode selection handler
  const parameterProductCodeSelectionHandler = (value) => {
    if (isDataExist) {
      if (selectedProductCode.Id_Setup !== value.Id_Setup) {
        MySwal.fire({
          icon: 'warning',
          title: 'Are you sure ?',
          text: 'If you change the parameter, all your previous data will not be acknowledge.',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          confirmButtonText: 'Yes, change it',
        }).then((result) => {
          if (result.isConfirmed) {
            setMeasurementData([]);
            setMSTotal('');
            setTMSTotal('');
            setIsDataExist(false);
            setIsSubmitShown(false);
            setIsStartMeasureDisabled(false);

            if (value.Id_Instrument) {
              setSelectedInstrument({...value});
            } else {
              setSelectedInstrument({});
            }

            if (value.Id_Setup) {
              setSelectedProductCode({...value});
            } else {
              setSelectedProductCode({});
            }
            MySwal.fire({
              icon: 'success',
              text: 'Parameter changed.',
            });
          }
        });
        return;
      }
    } else {
      setSelectedProductCode({...value});
    }
  };

  const parameterStationDataSelectionHandler = (value) => {
      console.log(selectedStationData)
      setSelectedStationData({...value});
  };

  const parameterStepDataSelectionHandler = (value) => {
    console.log(selectedStepData)
    setSelectedStepData({...value});
};


  const valiDateAmount = async ()=>{

    if (
      !selectedInstrument.Id_Instrument ||
      !selectedProductCode.Product_Code
    ) {
      MySwal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Please fill all required data!',
      });
      return;
    }

    // inputData
    const inputData = {
      code_instrument: selectedInstrument.Code_Instrument,
      product_code: selectedProductCode.Product_Code,
    };

    //postgre
    // const {Data: entryMeasurementNew, Data_total: totalOverallNew} = await GetEntryMeasurementTotalMSAndTMS(inputData)
    
    //mysql
    // const {Data: entryMeasurementNew, Data_total: totalOverallNew} = await GetEntryMeasurementTotalMSAndTMSMysql(inputData)

    //postgre
    // const res = await ApiIPCNew().GetEntryMeasurementTotalMSAndTMS(
    //   inputData
    // )

    // if (res?.connectivity_issue) {
    //   MySwal.fire({
    //     icon: 'error',
    //     title: 'Masalah Konektivitas',
    //      text: res?.error || 'Tidak dapat mengakses Database IPC',
    //   });
    //   return
    // }

    // const entryMeasurementNew = res.Data
    // const totalOverallNew = res.Data_total


    //mysql
    const res = await ApiIPCNew().GetEntryMeasurementTotalMSAndTMSMysql(
      inputData
    )

    if (res?.connectivity_issue) {
      MySwal.fire({
        icon: 'error',
        title: 'Masalah Konektivitas',
         text: res?.error || 'Tidak dapat mengakses Database IPC',
      });
      return
    }

    const entryMeasurementNew = res.Data
    const totalOverallNew = res.Data_total

    if (entryMeasurementNew === null || entryMeasurementNew === undefined || entryMeasurementNew.length === 0) {
      
      startMeasureHandler()
      return
    } else {
        // Handle the non-empty case here
        console.log("ada data", entryMeasurementNew);
        setTotalOutstandingData(entryMeasurementNew.length)
        setShowOutstanding(true)
        return
    }   
  }

  const clearOutstanding = async ()=>{

    try {
      let insertInstrumentCleared = {
        code_instrument: selectedInstrument.Code_Instrument,
      };
      // const res = await PutClearStaging(
      //   insertInstrumentCleared
      // );

      //postgresql
        // const res = await ApiIPCNew().PutClearStaging(
        //   insertInstrumentCleared
        // )
    
        // if (res?.connectivity_issue) {
        //   MySwal.fire({
        //     icon: 'error',
        //     title: 'Masalah Konektivitas',
        //      text: res?.error || 'Tidak dapat mengakses Database IPC',
        //   });
        //   return
        // }
    
        // const dataCleared = res.Data

        //mysql
        const res = await ApiIPCNew().PutClearStagingMysql(
          insertInstrumentCleared
        )
    
        if (res?.connectivity_issue) {
          MySwal.fire({
            icon: 'error',
            title: 'Masalah Konektivitas',
             text: res?.error || 'Tidak dapat mengakses Database IPC',
          });
          return
        }

    //   {
    //     "Data": {
    //         "code_instrument": "A20230626002"
    //     },
    //     "Status": "Success Update"
    // }

      setShowOutstanding(false)
      setTotalOutstandingData(0)
      if (res.Status === "Success Update") {
        startMeasureHandler()
      }      
    } catch (error) {
      console.log("error", error)
      setShowOutstanding(false)
      setTotalOutstandingData(0)
    }
   
  }

  const startMeasureHandler = () => {
    // validasi jika dropdown kosong (user belum memilih)
    
      const now = new Date();
      const year = now.getUTCFullYear();
      const month = String(now.getUTCMonth() + 1).padStart(2, '0');
      const day = String(now.getUTCDate()).padStart(2, '0');
      const hours = String(now.getUTCHours()).padStart(2, '0');
      const minutes = String(now.getUTCMinutes()).padStart(2, '0');
      const seconds = String(now.getUTCSeconds()).padStart(2, '0');
      const milliseconds = String(now.getUTCMilliseconds()).padStart(3, '0');

      const currentTimestampUTC = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
    // console.log("start measure", currentTimestamp)
    setStartDate(currentTimestampUTC)
    if (
      !selectedInstrument.Id_Instrument ||
      !selectedProductCode.Product_Code
    ) {
      MySwal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Please fill all required data!',
      });
      return;
    }

    // inputData
    const inputData = {
      code_instrument: selectedInstrument.Code_Instrument,
      product_code: selectedProductCode.Product_Code,
    };
    // set nilai inputtedData
    setInputtedData({...inputData});
    // menjalankan useEffect
    setIsLoading((preValue) => !preValue);
  };

  return (
    <>
      <div>
        <Head>
          <title>Entry Measurement</title>
        </Head>
      </div>
      <ContainerLayout
        title="User Module"
        parentMenuData={data?.parentMenuData}
        childMenuData={data?.childMenuData}
        menuIcon={data?.menuIcon}
      >
        <MainContent>
          {/* <div className="mb-3">
            <h4>PIMS - Menu</h4>
            <p>In Process Control &gt; Entry Measurement</p>
          </div> */}
          <ModuleContentHeader
            module_name={moduleName}
            breadcrumbs={breadcrumbsData}
          />

          <div className="p-2">
            <form>
              <div className="row mb-3">
                <label htmlFor="menu_name" className="col-sm-3">
                  Nama Operator :
                </label>
                <label className="col-sm-9">
                  {' '}
                  <strong>{loginData?.employee_name}</strong>
                </label>
              </div>
              <div className="row mb-3">
                <label htmlFor="selectInstrument" className="col-sm-3">
                  Pilih Instrument
                </label>
                <div className="col-sm-9">
                  <Dropdown
                    title={
                      selectedInstrument.Instrument_Name
                        ? selectedInstrument.Instrument_Name
                        : '-- Pilih Instrument --'
                    }
                    onSelect={parameterInstrumentSelectionHandler}
                    disabled={formDisabled}
                  >
                    <Dropdown.Item eventKey="">
                      -- Pilih Instrument --
                    </Dropdown.Item>
                    {instruments.length > 0 &&
                      instruments.map((item) => (
                        <Dropdown.Item key={item.Id_Instrument} eventKey={item}>
                          {item.Instrument_Name}
                        </Dropdown.Item>
                      ))}
                  </Dropdown>
                </div>
              </div>
              <div className="row mb-3">
                <label htmlFor="productCode" className="col-sm-3">
                  Pilih Kode Produk
                </label>
                <div className="col-sm-9">
                  <Dropdown
                    title={                      
                      selectedProductCode.Product_Code
                        ? selectedProductCode.Product_Code
                        : '-- Pilih Kode Produk --'
                    }
                    onSelect={parameterProductCodeSelectionHandler}
                    disabled={formDisabled}
                  >
                    <Dropdown.Item eventKey="">
                      -- Pilih Kode Produk --
                    </Dropdown.Item>
                    {productCode.length > 0 &&
                      productCode.map((item) => (
                        <Dropdown.Item key={item.Id_Setup} eventKey={item}>
                          {item.Product_Code}
                        </Dropdown.Item>
                      ))}
                  </Dropdown>
                </div>
              </div>

              <div className="row mb-3">
                <label htmlFor="stationData" className="col-sm-3">
                  Pilih Station
                </label>
                <div className="col-sm-9">
                  <Dropdown
                    title={
                      selectedStationData.Station
                        ? selectedStationData.Station
                        : '-- Pilih Station --'
                    }
                    onSelect={parameterStationDataSelectionHandler}
                    disabled={formDisabled}
                  >
                    <Dropdown.Item eventKey="">
                      -- Pilih Station --
                    </Dropdown.Item>
                    {stationData.length > 0 &&
                      stationData.map((item) => (
                        <Dropdown.Item key={item.Station} eventKey={item}>
                          {item.Station}
                        </Dropdown.Item>
                      ))}
                  </Dropdown>
                </div>
              </div>

              <div className="row mb-3">
                <label htmlFor="stepData" className="col-sm-3">
                  Pilih Langkah Pengukuran
                </label>
                <div className="col-sm-9">
                  <Dropdown
                    title={
                      selectedStepData.Step
                        ? selectedStepData.Step
                        : '-- Pilih Langkah Pengukuran --'
                    }
                    onSelect={parameterStepDataSelectionHandler}
                    disabled={formDisabled}
                  >
                    <Dropdown.Item eventKey="">
                      -- Pilih Langkah Pengukuran --
                    </Dropdown.Item>
                    {stepData.length > 0 &&
                      stepData.map((item) => (
                        <Dropdown.Item key={item.Step} eventKey={item}>
                          {item.Step}
                        </Dropdown.Item>
                      ))}
                  </Dropdown>
                </div>
              </div>

              <div className="row mb-3">
                <label htmlFor="inputNumber" className="col-sm-3">
                  Isi Kode Batch
                </label>
                <div className="col-sm-9">
                <div style={{ width: 160 }}>
                <InputNumber
                  value={codeData}
                  min={0}
                  //max={10000}
                  scrollable={false}
                  onChange={(v, e) =>
                    setCodeData(v)
                  }
                  required
                />
                  </div>
                </div>
              </div>

              <Button
                appearance="primary"
                onClick={valiDateAmount}
                disabled={isStartMeasureDisabled}
              >
                Memulai Pengukuran
              </Button>

              <Table measureData={measurementData} />

              <div className="mt-5">
                <label className="col-sm-9">
                  <strong>
                    Jumlah MS :{msTotal !== 0 ? `${msTotal}` : `-`}
                  </strong>
                </label>
                <label className="col-sm-9">
                  <strong>
                    Jumlah TMS : {tmsTotal !== 0 ? `${tmsTotal}` : `-`}
                  </strong>
                </label>                
              </div>
              {isSubmitShown && (
                <div>
                  <Button
                    onClick={submitHandler}
                    appearance="primary"
                    disabled={submitDisabled}
                  >
                    Kirim
                  </Button>
                </div>
              )}
            </form>
          </div>


          <Modal open={showOutstanding} onClose={()=>{
            setShowOutstanding(false)
            setTotalOutstandingData(0)
          }}>
             <Modal.Header>
          <Modal.Title>
            Terdapat {totalOutstandingData} pengukuran tertunda.
          </Modal.Title>
        </Modal.Header>
          <Modal.Body>
            <p>
              {totalOutstandingData < 10 ? (
                <>
                  Silakan pilih 'Lanjutkan Pengukuran' untuk melanjutkan proses pengukuran, atau 'Hapus Data' untuk membatalkan dan mengulang pengukuran.
                </>
              ) : (
                <>
                  Karena jumlah pengukuran tertunda lebih dari 10, Anda diharuskan untuk memilih 'Hapus Data' sebelum melanjutkan proses pengukuran.
                </>
              )}
            </p>
          </Modal.Body>
            <Modal.Footer>
              <Button onClick={()=>{
                clearOutstanding()
              }} appearance="primary">
                Hapus Data
              </Button>

              <Button onClick={()=>{
                startMeasureHandler()
                setShowOutstanding(false)
                setTotalOutstandingData(0)
              }} appearance="primary" disabled={totalOutstandingData > 9}>
                Lanjutkan Pengukuran
              </Button>
            </Modal.Footer>
          </Modal>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

// export async function getServerSideProps({ query }) {
//   const { parentMenuName, childMenuName } = query;

//   return {
//     props: {
//       parentMenuName,
//       childMenuName,
//     },
//   };
// }
