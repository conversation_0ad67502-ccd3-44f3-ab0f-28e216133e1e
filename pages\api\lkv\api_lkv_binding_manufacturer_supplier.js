import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/binding_manufacturer_supplier${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function LkvBindingManufacturerSupplier(){
    return{
        getAllBindingManufacturerSupplier: createApiFunction("get", "/get/all"),
        getAllBindingManufacturerSupplierActive : createApiFunction("get", "/get/all/active"),
        createBindingManufacturerSupplier: createApiFunction("post", "/create"),
        updateBindingManufacturerSupplier: createApiFunction("put", "/edit"),
        updateStatusBindingManufacturerSupplier: createApiFunction("put", "/active")
    }
}