import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import styles from "./Message.module.scss";
import API_Notification from "@/pages/api/api_notification";

export default function Notification() {
  const [notifications, setNotifications] = useState([]);
  const [dataLogin, setDataLogin] = useState(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const NotificationsSection = ({ notifications, dataLogin }) => {
    const [activeNotification, setActiveNotification] = useState(null);
    const [activeTab, setActiveTab] = useState("all");
    const [filteredNotifications, setFilteredNotifications] = useState([]);

    useEffect(() => {
      setActiveNotification(notifications[0] || null);
      setFilteredNotifications(notifications);
    }, [notifications]);

    const handleNotificationClick = async (notification, index) => {
      setActiveNotification(notification);
      console.log(notifications);
      console.log(notification);

      if (notification.is_read === 0 && dataLogin) {
        try {
          await API_Notification().readNotification({
            employee_id: dataLogin.employee_id,
            notification_id: notification.id_notification,
          });
          notifications[index].is_read = 1;
          filteredNotifications[index].is_read = 1;
        } catch (error) {
          console.error("Error marking notification as read:", error);
        }
      }
    };

    const handleTabClick = (tab) => {
      setActiveTab(tab);
      const filtered =
        tab === "all"
          ? notifications
          : notifications.filter((n) => n.is_read === (tab === "read" ? 1 : 0));
      setFilteredNotifications(filtered);
    };

    return (
      <div className={styles.notifications}>
        <aside className={styles["notifications__sidebar"]}>
          <div className={styles["notifications__sidebar__header"]}>
            <div className={styles["notifications__sidebar__title"]}>
              Message
            </div>

            {/* <div className={styles["notifications__sidebar__search"]}>
              <input
                type="text"
                placeholder="Search..."
                className={styles["notifications__sidebar__search-input"]}
              />
            </div> */}

            <ul className={styles["notifications__sidebar__tabs"]}>
              <li
                onClick={() => handleTabClick("all")}
                className={`${styles["notifications__sidebar__tabs-item"]} ${
                  activeTab === "all"
                    ? styles["notifications__sidebar__tabs-item--active"]
                    : ""
                }`}
              >
                All
              </li>
              <li
                onClick={() => handleTabClick("read")}
                className={`${styles["notifications__sidebar__tabs-item"]} ${
                  activeTab === "read"
                    ? styles["notifications__sidebar__tabs-item--active"]
                    : ""
                }`}
              >
                Read
              </li>
              <li
                onClick={() => handleTabClick("unread")}
                className={`${styles["notifications__sidebar__tabs-item"]} ${
                  activeTab === "unread"
                    ? styles["notifications__sidebar__tabs-item--active"]
                    : ""
                }`}
              >
                Unread
              </li>
            </ul>
          </div>

          <div className={styles["notifications__sidebar__list"]}>
            {filteredNotifications.length > 0 ? (
              filteredNotifications.map((notification, i) => (
                <div
                  key={notification.id_notification}
                  onClick={() => handleNotificationClick(notification, i)}
                  className={`${styles["notifications__sidebar__item"]} ${
                    notification.is_read
                      ? styles["notifications__sidebar__item--read"]
                      : ""
                  } ${
                    notification.id_notification ===
                    activeNotification.id_notification
                      ? styles["notifications__sidebar__item--active"]
                      : ""
                  }`}
                >
                  <div
                    className={styles["notifications__sidebar__item-detail"]}
                  >
                    <p
                      className={`${
                        styles["notifications__sidebar__item-title"]
                      } ${
                        notification.is_read
                          ? styles["notifications__sidebar__item-title--read"]
                          : ""
                      }`}
                    >
                      {notification.subject}
                    </p>
                    <p
                      className={`${
                        styles["notifications__sidebar__item-date"]
                      } ${
                        notification.is_read
                          ? styles["notifications__sidebar__item-date--read"]
                          : ""
                      }`}
                    >
                      {new Date(notification.created_at).toLocaleString(
                        "en-US",
                        {
                          month: "long",
                          day: "numeric",
                          year: "numeric",
                          hour: "numeric",
                          minute: "numeric",
                          hour12: true,
                        }
                      )}
                    </p>
                    <p
                      className={styles["notifications__sidebar__item-preview"]}
                    >
                      {notification.message}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className={styles["notifications__sidebar__no-result"]}>
                No message found.
              </p>
            )}
          </div>
        </aside>
        <main className={styles["notifications__main"]}>
          <div className={styles["notifications__main__header"]}>
            <div className={styles["notifications__main__header-title"]}>
              {activeNotification ? activeNotification.subject : "No message"}
            </div>
            <div className={styles["notifications__main__header-date"]}>
              {activeNotification &&
                new Date(activeNotification.created_at).toLocaleString(
                  "en-US",
                  {
                    month: "long",
                    day: "numeric",
                    year: "numeric",
                    hour: "numeric",
                    minute: "numeric",
                    hour12: true,
                  }
                )}
            </div>
          </div>
          <div className={styles["notifications__main__sub-header"]}>
            {activeNotification && activeNotification.module && (
              <div
                className={
                  styles["notifications__main__sub-header-left-module"]
                }
              >
                Module: {activeNotification.module}
              </div>
            )}

            {activeNotification && activeNotification.url_path && (
              <div
                className={styles["notifications__main__sub-header-right-goto"]}
                onClick={() => router.push(activeNotification.url_path)}
              >
                Go to &#8594;
              </div>
            )}
          </div>

          <div className={styles["notifications__main__content"]}>
            {activeNotification?.message}
          </div>
        </main>
      </div>
    );
  };

  useEffect(() => {
    const dataLoginFromStorage = JSON.parse(
      localStorage.getItem("dataLoginUser")
    );
    setDataLogin(dataLoginFromStorage); // Set dataLogin from localStorage

    if (!dataLoginFromStorage) {
      router.push("/");
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await API_Notification().getNotificationByEmployeeId({
          employee_id: dataLoginFromStorage.employee_id,
        });
        setNotifications(response.data || []);
      } catch (error) {
        console.error("Fetching notifications failed:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [router]);

  return (
    <div className={styles["notification-page"]}>
      <Head>
        <title>Notification</title>
      </Head>
      <nav className={styles["notification-page__nav"]}>
        <div
          className={styles["notification-page__nav-back"]}
          onClick={() => router.push("/dashboard")}
        >
          &#8592; Back
        </div>
      </nav>

      {loading ? (
        <p>Loading...</p>
      ) : (
        <NotificationsSection
          notifications={notifications}
          dataLogin={dataLogin}
        />
      )}
    </div>
  );
}
