import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/log_activity/log_activity_dashboard/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};

export default function LogActivityDashboardApi(){
    return {
        getAll: createApiFunction("post", "get/all"),
        getModule: createApiFunction("post", "get/log-activity-module"),
    }
}