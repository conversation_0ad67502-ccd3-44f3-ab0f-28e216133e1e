import { useEffect, useState } from "react";
import Head from "next/head";
import { 
     Breadcrumb,
     IconButton,
     Input,
     InputGroup,
     Pagination,
     Panel,
     Stack,
     Table,
     Tag,
     Button,
     Modal,
     Form,
     SelectPicker,
     Grid,
     Row,
     Col,
     FlexboxGrid,
     InputNumber
    } from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";

import { useRouter } from "next/router";
import APIIPCProductScale from '@/pages/api/ipc/api_ipc_scale';
import ipcApi from '@/pages/api/ipcApi';
import ApiIpcStep from "@/pages/api/ipc/api_ipc_step";
import ApiIPCMethod from "@/pages/api/ipc/api_ipc_method";
import APIIpcProductMethod from "@/pages/api/ipc_ma/detail_method/api_ipc_method";
import ApiIpcMethod from "@/pages/api/ipc/api_ipc_method";

export default function index() {
    const router = useRouter();
    const { id } = router.query;
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeywordMethod, setSearchKeywordMethod] = useState("");
    const [sortColumnMethod, setSortColumnMethod] = useState("id_setup_detail");
    const [sortTypeMethod, setSortTypeMethod] = useState("asc");
    const [limitMethod, setLimitMethod] = useState(10);
    const [pageMethod, setPageMethod] = useState(1);

    const [searchKeywordScale, setSearchKeywordScale] = useState("");
    const [sortColumnScale, setSortColumnScale] = useState("id_setup_detail");
    const [sortTypeScale, setSortTypeScale] = useState("asc");
    const [limitScale, setLimitScale] = useState(10);
    const [pageScale, setPageScale] = useState(1);

    const [productScaleDataState, setProductScaleDataState] = useState([]);
    const [productHeaderDataState, setProductHeaderDataState] = useState({
        "Id_Setup": "",
        "Product_Code": "",
        "Hardness_Min": "",
        "Hardness_Max": "",
        "Hardness_UoM": "",
        "Thickness_Min": "",
        "Thickness_Max": "",
        "Thickness_UoM": "",
        "Diameter_Min": "",
        "Diameter_Max": "",
        "Diameter_UoM": "",
        "Start_Date": "",
        "End_Date": "",
        "Remark": "",
        "Ma_min_weight": "",
        "Ma_max_weight": "",
    });

    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [idRouter, setIdRouter] = useState(null);

    const [showAddModalScale, setShowAddModalScale] = useState(false);
    const [showEditModalScale, setShowEditModalScale] = useState(false);

    const [addLoadingScale, setAddLoadingScale] = useState(false);
    const [editLoadingScale, setEditLoadingScale] = useState(false);
    const [statusLoadingScale, setStatusLoadingScale] = useState(false);

    const emptyAddScaleForm = {
        "id_setup": null,
        "step_name": null,
        "scale_min_weight": null,
        "scale_max_weight": null,
        "max_ms":null,
        "create_by": null,
    }

    const emptyEditScaleForm = {
        "id_setup_detail":null, 
        "scale_min_weight":null, 
        "scale_max_weight":null, 
        "max_ms":null,
        "update_by":null, 
    }

    const emptyDeleteScaleForm = {
        "id_setup_detail":null,
        "delete_by":null,
        "is_active":null,
    }

    const [addScaleFormData, setAddScaleFormData] = useState(emptyAddScaleForm);
    const [editScaleFormData, setEditScaleFormData] = useState(emptyEditScaleForm);
    const [deleteScaleFormData, setDeleteScaleFormData] = useState(emptyDeleteScaleForm);

    const [errorAddScaleFormData, setErrorAddScaleFormData] = useState(emptyAddScaleForm);
    const [errorEditScaleFormData, setErrorEditScaleFormData] = useState(emptyEditScaleForm);


    const [stepDropdownData, setStepDropdownData] = useState([
        {
            label: "AW",
            value: "AW",
        },
        {
            label: "TG",
            value: "TG",
        },
        {
            label: "AK",
            value: "AK",
        },
        {
            label: "30 Menit",
            value: "30 Menit",
        },
    ])

    const [productMethodDataState, setProductMethodDataState] = useState([]);

    const [showAddModalMethod, setShowAddModalMethod] = useState(false);
    const [showEditModalMethod, setShowEditModalMethod] = useState(false);

    const [addLoadingMethod, setAddLoadingMethod] = useState(false);
    const [editLoadingMethod, setEditLoadingMethod] = useState(false);
    const [statusLoadingMethod, setStatusLoadingMethod] = useState(null);

    const emptyAddMethodForm = {
        "id_setup":null,
        "id_method":null,
        "method_name":null,
        "ma_min_weight":null,
        "ma_max_weight":null,
        "create_by":null,
    }

    const emptyEditMethodForm = {
      "id_setup_detail" :null,
      "id_method" :null,
      "method_name" :null,
      "ma_min_weight" :null,
      "ma_max_weight" :null,
      "update_by" :null,
    }

    const emptyDeleteMethodForm = {
        "id_setup_detail":null,
        "delete_by":null,
        "is_active":null,
    }

    const [addMethodFormData, setAddMethodFormData] = useState(emptyAddMethodForm);
    const [editMethodFormData, setEditMethodFormData] = useState(emptyEditMethodForm);
    const [deleteMethodFormData, setDeleteMethodFormData] = useState(emptyDeleteMethodForm);

    const [errorAddMethodFormData, setErrorAddMethodFormData] = useState(emptyAddMethodForm);
    const [errorEditMethodFormData, setErrorEditMethodFormData] = useState(emptyEditMethodForm);


    const [methodDropdownData, setMethodDropdownData] = useState([])
    


    const handleSearchMethod = (value) => {
        setSearchKeywordMethod(value);
        setPageMethod(1);
    };

    const handleChangeLimitMethod = (dataKey) => {
      setPageMethod(1);
        setLimitMethod(dataKey);
    };

    const handleSortColumnMethod = (sortColumnMethod, sortTypeMethod) => {
        setTimeout(() => {
            setSortColumnMethod(sortColumnMethod);
            setSortTypeMethod(sortTypeMethod);
        }, 500);
    };

    const filteredDataMethod = productMethodDataState.filter((rowData) => {
    const searchFieldsMethod = [
      "id_setup_detail",
      "id_setup",
      "id_method",
      "method_name",
      "ma_min_weight",
      "ma_max_weight",
      "create_date",
      "create_by",
      "update_date",
      "update_by",
      "delete_date",
      "delete_by",
      "is_active",
    ];

    const matchesSearchMethod = searchFieldsMethod.some((field) => {
        const fieldValue = rowData[field];
        return fieldValue && fieldValue.toString().toLowerCase().includes(searchKeywordMethod.toLowerCase());
    });

    return matchesSearchMethod;
    });

    const getPaginatedDataMethod = (filteredDataMethod, limitMethod, page) => {
        const start = limitMethod * (page - 1);
        const end = start + limitMethod;
        return filteredDataMethod.slice(start, end);
    };

    const getFilteredDataMethod = () => {
    if (sortColumnMethod && sortTypeMethod) {
        return [...filteredDataMethod].sort((a, b) => {
        let x = a[sortColumnMethod];
        let y = b[sortColumnMethod];
        if (typeof x === "string") {
            x = x.toLowerCase();
            y = y.toLowerCase();
        }
        if (sortTypeMethod === "asc") {
            return x > y ? 1 : x < y ? -1 : 0;
        } else {
            return x < y ? 1 : x > y ? -1 : 0;
        }
        });
    }
    return filteredDataMethod;
    };

    const totalRowCountMethod = searchKeywordMethod ? filteredDataMethod.length : productMethodDataState.length;

    const handleSearchScale = (value) => {
      setSearchKeywordScale(value);
      setPageScale(1);
  };

  const handleChangeLimitScale = (dataKey) => {
    setPageScale(1);
      setLimitScale(dataKey);
  };

  const handleSortColumnScale = (sortColumnScale, sortTypeScale) => {
      setTimeout(() => {
          setSortColumnScale(sortColumnScale);
          setSortTypeScale(sortTypeScale);
      }, 500);
  };

  const filteredDataScale = productScaleDataState.filter((rowData) => {
  const searchFieldsScale = [
      "id_setup_detail",
      "id_setup",
      "step_name",
      "scale_min_weight",
      "scale_max_weight",
      "create_date",
      "create_by",
      "update_date",
      "update_by",
      "delete_date",
      "delete_by",
      "is_active",
  ];

  const matchesSearchScale = searchFieldsScale.some((field) => {
      const fieldValue = rowData[field];
      return fieldValue && fieldValue.toString().toLowerCase().includes(searchKeywordScale.toLowerCase());
  });

  return matchesSearchScale;
  });

  const getPaginatedDataScale = (filteredDataScale, limitScale, page) => {
      const start = limitScale * (page - 1);
      const end = start + limitScale;
      return filteredDataScale.slice(start, end);
  };

  const getFilteredDataScale = () => {
  if (sortColumnScale && sortTypeScale) {
      return [...filteredDataScale].sort((a, b) => {
      let x = a[sortColumnScale];
      let y = b[sortColumnScale];
      if (typeof x === "string") {
          x = x.toLowerCase();
          y = y.toLowerCase();
      }
      if (sortTypeScale === "asc") {
          return x > y ? 1 : x < y ? -1 : 0;
      } else {
          return x < y ? 1 : x > y ? -1 : 0;
      }
      });
  }
  return filteredDataScale;
  };

  const totalRowCountScale = searchKeywordScale ? filteredDataScale.length : productScaleDataState.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setSessionAuth(dataLogin);
    
        if (!dataLogin) {
          router.push(dataLogin ? "/dashboard" : "/");
        } else if (id) {
          const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("oee/master/main_category"));
    
          if (validateUserAccess.length === 0) {
            router.push("/dashboard");
            return;
          }
          // Pastikan id_main_category menjadi integer
          const parsedId = parseInt(id); // Convert to integer
          setIdRouter(parsedId);
          handleGetHeader(parsedId);
          handleGetAllScaleProductData(parsedId);   
          handleGetAllMethodProductData(parsedId);   
          HandleGetAllIpcStepApi();  
          HandlegetAllIPCMethodApi();     
        }
      }, [router, id]);

      const HandleGetAllIpcStepApi = async () => {
        try {
          const res = await ApiIpcStep().getAllActiveIpcStep();
          if (res.status === 200) {
              const transformData = res?.data?.map(item => ({
                label: item.step_name,
                value: item.step_name
            })) || [];
            
            setStepDropdownData(transformData);
          } else {
            console.log("Error on GetAllStepApi: ", res.message);
          }
        } catch (error) {
          console.log("Error on catch GetAllStepApi: ", error.message);
        }
      };

      const HandlegetAllIPCMethodApi = async () => {
        try {
          const res = await ApiIPCMethod().getAllActiveIPCMethod();
          if (res.status === 200) {

            const transformData = res?.data?.map(item => ({
              label: item.method_name,
              value: item.id_method
          })) || [];
            setMethodDropdownData(transformData || []);
          } else {
            console.log("error on GetAllApi ", res.message);
          }
        } catch (error) {
          console.log("error on catch GetAllApi", error);
        }
      };

      const handleGetAllScaleProductData = async (id_setup) => {
        try {
            const res = await APIIPCProductScale().getAllDetailProductScale({ id_setup });
            if (res.status === 200) {
                console.log("res ", res)
                setProductScaleDataState(res.data);
            }else{
                console.log(res.message)
            }
        } catch (error) {
            console.log("error ", error)
        }
      }

      const handleGetAllMethodProductData = async (id_setup) => {
        try {
            const res = await APIIpcProductMethod().getAllDetailProductMethod({ id_setup });
            console.log("res ini aja ", res)
            if (res.status === 200) {
                console.log("res ini aja ", res)
                setProductMethodDataState(res.data);
            }else{
                console.log(res.message)
            }
        } catch (error) {
            console.log("error ", error)
        }
      }

      const handleGetHeader = async (id_setup) =>{
        try {
            const res = await ipcApi().GetProductCodeById({ id_setup });
            
            setProductHeaderDataState(res?.Data?.[0] || []);
            
        } catch (error) {
            console.log("error ", error)
        }
      }


      const handleAddScale = async () => {

        
        const errors = {};
        if (!addScaleFormData.step_name) {
                errors.step_name=  "no step data"
        }

        if (!addScaleFormData.scale_min_weight) {
                errors.scale_min_weight=  "no min weight"
        }

        if (!addScaleFormData.scale_max_weight) {
                errors.scale_max_weight=  "no max weight"
        }

        if (Object.keys(errors).length > 0) {
            setErrorAddScaleFormData(errors);
            return;
        }

        try {
          const res = await APIIPCProductScale().createProductScale({ 
            "id_setup": idRouter,
            "step_name": addScaleFormData.step_name,
            "scale_min_weight": parseFloat(addScaleFormData.scale_min_weight),
            "scale_max_weight": parseFloat(addScaleFormData.scale_max_weight),
            "max_ms":parseInt(addScaleFormData.max_ms),
            "create_by": addScaleFormData.create_by,
        });
          if (res.status === 200) {
            console.log("res", res);
            await handleGetAllScaleProductData(idRouter);
            setAddScaleFormData(emptyAddScaleForm);
            setShowAddModalScale(false)
            setErrorAddScaleFormData({})
          } else {
            console.log("Error on Get API: ", res.message);
          }
        } catch (error) {
          console.log("Error on catch Get API", error);
        }
      }

      const handleEditScale = async () => {

        
        const errors = {};

        if (!editScaleFormData.scale_min_weight) {
                errors.scale_min_weight=  "no min weight"
        }

        if (!editScaleFormData.scale_max_weight) {
                errors.scale_max_weight=  "no max weight"
        }

        if (Object.keys(errors).length > 0) {
            setErrorEditScaleFormData(errors);
            return;
        }

        try {
          const res = await APIIPCProductScale().updateProductScale({ 
            "id_setup_detail": editScaleFormData.id_setup_detail,
            "scale_min_weight": parseFloat(editScaleFormData.scale_min_weight),
            "scale_max_weight": parseFloat(editScaleFormData.scale_max_weight),
            "max_ms":parseInt(editScaleFormData.max_ms),
            "update_by": editScaleFormData.update_by,
        });
          if (res.status === 200) {
            console.log("res", res);
            await handleGetAllScaleProductData(idRouter);
            setEditScaleFormData(emptyEditScaleForm);
            setShowEditModalScale(false)
            setErrorEditScaleFormData({})
          } else {
            console.log("Error on Get API: ", res.message);
          }
        } catch (error) {
          console.log("Error on catch Get API", error);
        }
      }

      const handleStatusScale = async (id, is_active) => {


        //`${sessionAuth.employee_id} - ${sessionAuth.employee_name}`
        const newStatus = is_active === 1 ? 0 : 1;

      
        try {
          const res = await APIIPCProductScale().deleteProductScale({ 
            "id_setup_detail": id,
            "is_active": newStatus,
            "delete_by": `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
        });
          if (res.status === 200) {
            console.log("res", res);
            await handleGetAllScaleProductData(idRouter);
          } else {
            console.log("Error on Get API: ", res.message);
          }
        } catch (error) {
          console.log("Error on catch Get API", error);
        }
      }

      const handleAddMethod = async () => {

        
        const errors = {};
        if (!addMethodFormData.method_name) {
                errors.method_name=  "no method name"
        }

        if (!addMethodFormData.id_method) {
                errors.id_method=  "no method name"
        }

        if (!addMethodFormData.ma_min_weight) {
                errors.ma_min_weight=  "no min weight"
        }

        if (!addMethodFormData.ma_max_weight) {
                errors.ma_max_weight=  "no max weight"
        }

        if (Object.keys(errors).length > 0) {
            setErrorAddMethodFormData(errors);
            return;
        }

        try {
          const res = await APIIpcProductMethod().createProductMethod({ 
            "id_setup": idRouter,
            "id_method":addMethodFormData.id_method,
            "method_name": addMethodFormData.method_name,
            "ma_min_weight": parseFloat(addMethodFormData.ma_min_weight),
            "ma_max_weight": parseFloat(addMethodFormData.ma_max_weight),
            "create_by": addMethodFormData.create_by,
        });
          if (res.status === 200) {
            console.log("res", res);
            await handleGetAllMethodProductData(idRouter);
            setAddMethodFormData(emptyAddMethodForm);
            setShowAddModalMethod(false)
            setErrorAddScaleFormData({})
          } else {
            console.log("Error on Get API: ", res.message);
          }
        } catch (error) {
          console.log("Error on catch Get API", error);
        }
      }

      const handleEditMethod = async () => {

        
        const errors = {};

        if (!editMethodFormData.method_name) {
          errors.method_name=  "no method name"
        }

        if (!editMethodFormData.id_method) {
                errors.id_method=  "no method name"
        }

        if (!editMethodFormData.ma_min_weight) {
                errors.ma_min_weight=  "no min weight"
        }

        if (!editMethodFormData.ma_max_weight) {
                errors.ma_max_weight=  "no max weight"
        }

        if (Object.keys(errors).length > 0) {
            setErrorEditMethodFormData(errors);
            return;
        }

        try {
          const res = await APIIpcProductMethod().updateProductMethod({ 
            "id_setup_detail": editMethodFormData.id_setup_detail,
            "id_method" :editMethodFormData.id_method,
            "method_name" :editMethodFormData.method_name,
            "ma_min_weight": parseFloat(editMethodFormData.ma_min_weight),
            "ma_max_weight": parseFloat(editMethodFormData.ma_max_weight),
            "update_by": editMethodFormData.update_by,
        });
          if (res.status === 200) {
            console.log("res", res);
            await handleGetAllMethodProductData(idRouter);
            setEditMethodFormData(emptyEditMethodForm);
            setShowEditModalMethod(false)
            setErrorEditScaleFormData({})
          } else {
            console.log("Error on Get API: ", res.message);
          }
        } catch (error) {
          console.log("Error on catch Get API", error);
        }
      }

      const handleStatusMethod = async (id, is_active) => {


        //`${sessionAuth.employee_id} - ${sessionAuth.employee_name}`
        const newStatus = is_active === 1 ? 0 : 1;

      
        try {
          const res = await APIIpcProductMethod().deleteProductMethod({ 
            "id_setup_detail": id,
            "is_active": newStatus,
            "delete_by": `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
        });
          if (res.status === 200) {
            console.log("res", res);
            await handleGetAllMethodProductData(idRouter);
          } else {
            console.log("Error on Get API: ", res.message);
          }
        } catch (error) {
          console.log("Error on catch Get API", error);
        }
      }

      
      
    

    
  return (
    <div>
      <div>
        <Head>
          <title>Product Detail Page</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>IPC MA</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item>Detail</Breadcrumb.Item>
                  <Breadcrumb.Item active>{productHeaderDataState.Product_Code}</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-4"
            header={
              <Form fluid>
                <FlexboxGrid className="mb-4">
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Product ID</Form.ControlLabel>
                      <Form.Control name="Id_Setup" value={productHeaderDataState.Id_Setup || "-"} readOnly className="mb-3" />
                      <Form.ControlLabel>Product Code {productHeaderDataState.Product_Code}</Form.ControlLabel>
                      <Form.Control name="Product_Code" value={productHeaderDataState.Product_Code }readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>                  
                </FlexboxGrid>
              </Form>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModalScale(true);
                        setAddScaleFormData({ ...addScaleFormData,
                             id_setup: id ,
                             create_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`
                        });
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>
                  <div className="flex gap-2">
                    Product Step
                  </div>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="Search" value={searchKeywordScale} onChange={handleSearchScale} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeywordScale("");
                        setPageScale(1);
                      }}
                      style={{
                        display: searchKeywordScale ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedDataScale(getFilteredDataScale(), limitScale, pageScale)} sortColumn={sortColumnScale} sortType={sortTypeScale} onSortColumn={handleSortColumnScale} autoHeight>
                <Column width={150} align="center" sortable>
                  <HeaderCell>ID Setup Detail</HeaderCell>
                  <Cell dataKey="id_setup_detail" />
                </Column>
                <Column width={100} sortable>
                  <HeaderCell align="center">Step Name</HeaderCell>
                  <Cell dataKey="step_name" />
                </Column>
                <Column width={100} sortable>
                  <HeaderCell align="center">Scale Min Weight</HeaderCell>
                  <Cell dataKey="scale_min_weight" />
                </Column>
                <Column width={100} sortable>
                  <HeaderCell align="center">Scale Max Weight</HeaderCell>
                  <Cell dataKey="scale_max_weight" />
                </Column>
                <Column width={100} sortable>
                  <HeaderCell align="center">Max MS</HeaderCell>
                  <Cell dataKey="max_ms" />
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Creation Date</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Created By</HeaderCell>
                  <Cell dataKey="create_by" />
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Update Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Updated By</HeaderCell>
                  <Cell dataKey="update_by" />
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Deletion Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Deleted By</HeaderCell>
                  <Cell dataKey="delete_by" />
                </Column>
                <Column width={120} sortable resizable align="center">
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Active" : "Inactive"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Actions</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModalScale(true);
                            setEditScaleFormData({
                              ...editScaleFormData,
                              step_name : rowData.step_name,
                              id_setup_detail: rowData.id_setup_detail,
                              scale_min_weight: rowData.scale_min_weight,
                              scale_max_weight: rowData.scale_max_weight,
                              max_ms: rowData.max_ms,
                              update_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
                            });
                          }}
                        >
                          <EditIcon />
                        </Button>
                        <Button appearance="subtle" onClick={() => handleStatusScale(rowData.id_setup_detail, rowData.is_active)} loading={statusLoadingScale === rowData.id_binding_category}>
                          {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCountScale}
                  limit={limitScale}
                  activePage={pageScale}
                  onChangePage={setPageScale}
                  onChangeLimit={handleChangeLimitScale}
                />
              </div>
            </Panel>
            <br/>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModalMethod(true);
                        setAddMethodFormData({ ...addMethodFormData,
                             id_setup: id ,
                             create_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`
                        });
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>
                  <div className="flex gap-2">
                    Product Method
                  </div>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="Search" value={searchKeywordMethod} onChange={handleSearchMethod} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeywordMethod("");
                        setPageMethod(1);
                      }}
                      style={{
                        display: searchKeywordMethod ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedDataMethod(getFilteredDataMethod(), limitMethod, pageMethod)} sortColumn={sortColumnMethod} sortType={sortTypeMethod} onSortColumn={handleSortColumnMethod} autoHeight>
                <Column width={150} align="center" sortable>
                  <HeaderCell>ID Setup Detail</HeaderCell>
                  <Cell dataKey="id_setup_detail" />
                </Column>
                <Column width={100} sortable>
                  <HeaderCell align="center">Method Name</HeaderCell>
                  <Cell dataKey="method_name" />
                </Column>
                <Column width={100} sortable>
                  <HeaderCell align="center">Ma Min Weight</HeaderCell>
                  <Cell dataKey="ma_min_weight" />
                </Column>
                <Column width={100} sortable>
                  <HeaderCell align="center">Ma Max Weight</HeaderCell>
                  <Cell dataKey="ma_max_weight" />
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Creation Date</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Created By</HeaderCell>
                  <Cell dataKey="create_by" />
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Update Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Updated By</HeaderCell>
                  <Cell dataKey="update_by" />
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Deletion Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Deleted By</HeaderCell>
                  <Cell dataKey="delete_by" />
                </Column>
                <Column width={120} sortable resizable align="center">
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Active" : "Inactive"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Actions</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModalMethod(true);
                            setEditMethodFormData({
                              ...editMethodFormData,
                              id_setup_detail :rowData.id_setup_detail,
                              id_method :rowData.id_method,
                              method_name :rowData.method_name,
                              ma_min_weight :rowData.ma_min_weight,
                              ma_max_weight :rowData.ma_max_weight,
                              update_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,                              
                            });
                          }}
                        >
                          <EditIcon />
                        </Button>
                        <Button appearance="subtle" onClick={() => handleStatusMethod(rowData.id_setup_detail, rowData.is_active)} loading={statusLoadingMethod === rowData.id_binding_category}>
                          {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCountMethod}
                  limit={limitMethod}
                  activePage={pageMethod}
                  onChangePage={setPageMethod}
                  onChangeLimit={handleChangeLimitMethod}
                />
              </div>
            </Panel>
            {/* Modal for adding scale product */}
            <Modal
              backdrop="static"
              open={showAddModalScale}
              onClose={() => {
                if (!addLoadingScale) {
                  setShowAddModalScale(false);
                  setAddScaleFormData(emptyAddScaleForm);
                  setErrorAddScaleFormData({});
                }
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Menambah Data Scale Step</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Step Name</Form.ControlLabel>
                    <SelectPicker
                      name="step_name"
                      value={addScaleFormData.step_name}
                      data={stepDropdownData}
                      onChange={(value) => {
                        setAddScaleFormData({ ...addScaleFormData, step_name: value });
                      }}
                      disabled={addLoadingScale}
                      style={{ width: "100%" }}
                    />
                    {errorAddScaleFormData.step_name && <p style={{ color: "red" }}>{errorAddScaleFormData.step_name}</p>}
                  </Form.Group>
                  <Form.Group controlId="scale_weight">
                    <Stack spacing={6}>
                        <Form.ControlLabel>Scale Weight Range: </Form.ControlLabel>
                        <InputNumber
                            name="scale_min_weight"
                            style={{ width: 70 }}
                            value={addScaleFormData.scale_min_weight}
                            onChange={(value) => {                               
                                setAddScaleFormData({ ...addScaleFormData, scale_min_weight: value })
                            }}
                            required={true}
                            />
                        <Form.ControlLabel style={{ width: 10 }}>-</Form.ControlLabel>
                            <InputNumber
                            name="scale_max_weight"
                            style={{ width: 70 }}
                            value={addScaleFormData.scale_max_weight}
                            onChange={(value) => {
                                setAddScaleFormData({ ...addScaleFormData, scale_max_weight: value })
                            }}
                            required={true}
                            />
                        <span>Gram</span>
                    </Stack>
                    {(errorAddScaleFormData.scale_min_weight || errorAddScaleFormData.scale_max_weight) && <p style={{ color: "red" }}>{errorAddScaleFormData.scale_min_weight} - {errorAddScaleFormData.scale_max_weight}  </p>}
                  </Form.Group>
                  <Form.Group controlId="max_ms">
                    <Stack spacing={6}>
                        <Form.ControlLabel>Max MS: </Form.ControlLabel>
                        <InputNumber
                            name="max_ms"
                            style={{ width: 70 }}
                            value={addScaleFormData.max_ms}
                            onChange={(value) => {                               
                                setAddScaleFormData({ ...addScaleFormData, max_ms: value })
                            }}
                            required={true}
                            />
                    </Stack>
                    {(errorAddScaleFormData.max_ms ) && <p style={{ color: "red" }}>{errorAddScaleFormData.max_ms}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModalScale(false);
                    setAddScaleFormData(emptyAddScaleForm);
                    setErrorAddScaleFormData({});
                  }}
                  appearance="subtle"
                  disabled={addLoadingScale}
                >
                  Cancel
                </Button>
                <Button 
                onClick={() =>{
                    setErrorAddScaleFormData({});
                    handleAddScale()
                }} 
                appearance="primary" loading={addLoadingScale} disabled={addLoadingScale}>
                  Add
                </Button>
              </Modal.Footer>
            </Modal>
            {/* Modal for editing scale product */}
            <Modal
              backdrop="static"
              open={showEditModalScale}
              onClose={() => {
                setShowEditModalScale(false);
                setEditScaleFormData(emptyEditScaleForm);
                setErrorEditScaleFormData({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Mengubah data scale step</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                  <Form.ControlLabel>Step Name</Form.ControlLabel>
                    <SelectPicker
                      name="step_name"
                      value={editScaleFormData.step_name}
                      data={stepDropdownData}
                      disabled={true}
                      style={{ width: "100%" }}
                    />
                  </Form.Group>
                  <Form.Group controlId="scale_weight">
                    <Stack spacing={6}>
                        <Form.ControlLabel>Scale Weight Min</Form.ControlLabel>
                            <InputNumber
                            name="scale_min_weight"
                            style={{ width: 70 }}
                            value={editScaleFormData.scale_min_weight}
                            onChange={(value) => setEditScaleFormData({ ...editScaleFormData, scale_min_weight: value })}
                            required={true}
                            />
                        <Form.ControlLabel style={{ width: 10 }}>-</Form.ControlLabel>
                            <InputNumber
                            name="scale_max_weight"
                            style={{ width: 70 }}
                            value={editScaleFormData.scale_max_weight}
                            onChange={(value) => setEditScaleFormData({ ...editScaleFormData, scale_max_weight: value })}
                            required={true}
                            />
                        <span>Gram</span>
                    </Stack>
                    {(errorEditScaleFormData.scale_min_weight || errorEditScaleFormData.scale_max_weight) && <p style={{ color: "red" }}>{errorEditScaleFormData.scale_min_weight} - {errorEditScaleFormData.scale_max_weight}  </p>}
                    </Form.Group>
                    <Form.Group controlId="max_ms">
                    <Stack spacing={6}>
                        <Form.ControlLabel>Max MS: </Form.ControlLabel>
                        <InputNumber
                            name="max_ms"
                            style={{ width: 70 }}
                            value={editScaleFormData.max_ms}
                            onChange={(value) => {                               
                                setEditScaleFormData({ ...editScaleFormData, max_ms: value })
                            }}
                            required={true}
                            />
                    </Stack>
                    {(errorEditScaleFormData.max_ms ) && <p style={{ color: "red" }}>{errorEditScaleFormData.max_ms}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModalScale(false);
                    setEditScaleFormData(emptyEditScaleForm);
                    setErrorEditScaleFormData({});
                  }}
                  appearance="subtle"
                  disabled={editLoadingScale}
                >
                  Cancel
                </Button>
                <Button 
                onClick={()=>{
                  setErrorEditScaleFormData({});
                  handleEditScale()
                }} 
                appearance="primary" loading={editLoadingScale} disabled={editLoadingScale}>
                  Edit
                </Button>
              </Modal.Footer>
            </Modal>
            {/* Modal for adding scale product */}
            <Modal
              backdrop="static"
              open={showAddModalMethod}
              onClose={() => {
                if (!addLoadingMethod) {
                  setShowAddModalMethod(false);
                  setAddMethodFormData(emptyAddMethodForm);
                  setErrorAddMethodFormData({});
                }
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Menambah Data Method</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Method Name</Form.ControlLabel>
                    <SelectPicker
                      name="method_name"
                      value={addMethodFormData.id_method}
                      data={methodDropdownData}
                      onChange={(value) => {
                        const selectedMethod = methodDropdownData.find((item) => item.value === value);
                        setAddMethodFormData({ 
                          ...addMethodFormData, 
                          id_method: value, 
                          method_name: selectedMethod ? selectedMethod.label : "" 
                        });
                      }}
                      disabled={addLoadingScale}
                      style={{ width: "100%" }}
                    />
                    {errorAddMethodFormData.method_name && <p style={{ color: "red" }}>{errorAddMethodFormData.method_name}</p>}
                  </Form.Group>
                  <Form.Group controlId="ma_weight">
                    <Stack spacing={6}>
                        <Form.ControlLabel>Ma Min Weight: </Form.ControlLabel>
                        <InputNumber
                            name="ma_min_weight"
                            style={{ width: 70 }}
                            value={addMethodFormData.ma_min_weight}
                            onChange={(value) => {                               
                                setAddMethodFormData({ ...addMethodFormData, ma_min_weight: value })
                            }}
                            required={true}
                            />
                        <Form.ControlLabel style={{ width: 10 }}>-</Form.ControlLabel>
                            <InputNumber
                            name="ma_max_weight"
                            style={{ width: 70 }}
                            value={addMethodFormData.ma_max_weight}
                            onChange={(value) => {
                                setAddMethodFormData({ ...addMethodFormData, ma_max_weight: value })
                            }}
                            required={true}
                            />
                        <span>%</span>
                    </Stack>
                    {(errorAddMethodFormData.ma_min_weight || errorAddMethodFormData.ma_max_weight) && <p style={{ color: "red" }}>{errorAddMethodFormData.ma_min_weight} - {errorAddMethodFormData.ma_max_weight}  </p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModalMethod(false);
                    setAddMethodFormData(emptyAddMethodForm);
                    setErrorAddMethodFormData({});
                  }}
                  appearance="subtle"
                  disabled={addLoadingMethod}
                >
                  Cancel
                </Button>
                <Button 
                onClick={() =>{
                    setErrorAddMethodFormData({});
                    handleAddMethod()
                }} 
                appearance="primary" loading={addLoadingMethod} disabled={addLoadingMethod}>
                  Add
                </Button>
              </Modal.Footer>
            </Modal>
            
            {/* Modal for editing method product */}
            <Modal
              backdrop="static"
              open={showEditModalMethod}
              onClose={() => {
                setShowEditModalMethod(false);
                setEditMethodFormData(emptyEditMethodForm);
                setErrorEditMethodFormData({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Mengubah data Method step</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                  <Form.ControlLabel>Method Name</Form.ControlLabel>
                    <SelectPicker
                      name="step_name"
                      value={editMethodFormData.id_method}
                      data={methodDropdownData}
                      disabled={true}
                      style={{ width: "100%" }}
                    />
                  </Form.Group>
                  <Form.Group controlId="ma_weight">
                    <Stack spacing={6}>
                        <Form.ControlLabel>Ma Weight Min</Form.ControlLabel>
                            <InputNumber
                            name="ma_min_weight"
                            style={{ width: 70 }}
                            value={editMethodFormData.ma_min_weight}
                            onChange={(value) => setEditMethodFormData({ ...editMethodFormData, ma_min_weight: value })}
                            required={true}
                            />
                        <Form.ControlLabel style={{ width: 10 }}>-</Form.ControlLabel>
                            <InputNumber
                            name="ma_max_weight"
                            style={{ width: 70 }}
                            value={editMethodFormData.ma_max_weight}
                            onChange={(value) => setEditMethodFormData({ ...editMethodFormData, ma_max_weight: value })}
                            required={true}
                            />
                        <span>%</span>
                    </Stack>
                    {(errorEditMethodFormData.ma_min_weight || errorEditMethodFormData.ma_max_weight) && <p style={{ color: "red" }}>{errorEditMethodFormData.ma_min_weight} - {errorEditMethodFormData.ma_max_weight}  </p>}
                    </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModalMethod(false);
                    setEditMethodFormData(emptyEditMethodForm);
                    setErrorEditMethodFormData({});
                  }}
                  appearance="subtle"
                  disabled={editLoadingMethod}
                >
                  Cancel
                </Button>
                <Button 
                onClick={()=>{
                  setErrorEditMethodFormData({});
                  handleEditMethod()
                }} 
                appearance="primary" loading={editLoadingScale} disabled={editLoadingScale}>
                  Edit
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  )
}
