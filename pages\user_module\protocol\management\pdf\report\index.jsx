import { faDownload, faFileDownload } from "@fortawesome/free-solid-svg-icons";
import Head from "next/head";
import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button, Stack, Table, Panel } from "rsuite";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import ProtocolApi from "@/pages/api/protocolApi";
import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";
import { data } from "autoprefixer";
import htmlToPdfMake from 'html-to-pdfmake';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';

pdfMake.vfs = pdfFonts.pdfMake.vfs;
// import Table from "@/components/Table";

export default function PrintProtocolDoc({
    noProtocol,
    headerData,
    detailData,
    t30Data,
    t40Data,
    reviewerOrder,
    approverOrder,
    protocolHistory,
    hasilAnalisa,
    batchNumber,
}) {
    const [stateShowDownload, setStateShowDownload] = useState(true);
    const [clickDownload, setClickDownload] = useState(false);
    const [downloadedBy, setDownloadedBy] = useState('')
    const [downloadedDate, setDownloadedDate] = useState('')
    const { HeaderCell, Cell, Column } = Table;
    let rowNumber = 1;

    const MySwal = withReactContent(Swal);

    const generatePDFNew = async ()=>{
        // const title = htmlToPdfMake(headerData[0].Desc_Purpose);
        const reviewerRows = reviewerOrder.length > 0
        ? reviewerOrder.map(item => [
            { text: item.Approve_By, style: 'tableCell' },
            { text: item.Job_title, style: 'tableCell' },
            {
              text: parseInt(item.Approval_Status) === 0 ? `Rejected at ${item.Approval_Date.substring(0, 10)}` :
                    parseInt(item.Approval_Status) === 1 ? `Approved at ${item.Approval_Date.substring(0, 10)}` :
                    parseInt(item.Approval_Status) === 2 ? `Waiting for Approval` :
                    parseInt(item.Approval_Status) === 3 ? `Waiting for others approval` :
                    parseInt(item.Approval_Status) === 4 ? `Rejected by others` :
                    `Had approved but rejected by others`,
              style: 'tableCell'
            },
            { text: item.Reject_reason, style: 'tableCell' },
          ])
        : [];
    
      // Map approverOrder to table rows
      const approverRows = approverOrder.length > 0
        ? approverOrder.map(item => [
            { text: `${item.Approve_By} - ${item.Job_title}`, style: 'tableCell' },
            { text: item.Job_title, style: 'tableCell' },
            {
              text: parseInt(item.Approval_Status) === 0 ? `Rejected at ${item.Approval_Date.substring(0, 10)}` :
                    parseInt(item.Approval_Status) === 1 ? `Approved at ${item.Approval_Date.substring(0, 10)}` :
                    parseInt(item.Approval_Status) === 2 ? `Waiting for Approval` :
                    parseInt(item.Approval_Status) === 3 ? `Waiting for others approval` :
                    parseInt(item.Approval_Status) === 4 ? `Rejected by others` :
                    `Had approved but rejected by others`,
              style: 'tableCell'
            },
            { text: item.Reject_reason, style: 'tableCell' },
          ])
        : [];

         // Map protocolHistory to table rows
        const protocolHistoryRows = protocolHistory && protocolHistory.length > 0
        ? protocolHistory.map(item => [
            { text: item.No_Protocol, style: 'tableCell' },
            { text: item.Protocol_Version, style: 'tableCell' },
            { text: item.Created_Date.slice(0, 10), style: 'tableCell' },
            { text: item.Remark_Change, style: 'tableCell' },            
            ])
        : [[{ text: 'Tidak ada data perubahan', colSpan: 4, alignment: 'center', margin: [0, 20] }]];


        const batchNumberRows = batchNumber && batchNumber.length > 0
        ? batchNumber.map(batchNumber_data => ({
            text: `Batch No : ${batchNumber_data.Batch_number}`,
            margin: [15, 0, 0, 0]
        }))
        : [];

        const detailDataRows = detailData.map(item => {
            if (item.Input_Method_Id === 1) {
              return [
                { text: item.Parameter_Name, style: 'tableCell' },
                { text: item.Desc, style: 'tableCell' }
              ];
            } else if (item.Input_Method_Id === 2) {
              return [
                { text: item.Parameter_Name, style: 'tableCell' },
                { text: item.Value_Reference, style: 'tableCell' }
              ];
            } else if (item.Input_Method_Id === 3) {
              return [
                { text: item.Parameter_Name, style: 'tableCell' },
                { text: `${item.Min_Value} - ${item.Max_Value}`, style: 'tableCell' }
              ];
            }
            return [];
          });

    

        
        const descPurposeContent = htmlToPdfMake(headerData[0].Desc_Purpose).map(content => ({
            ...content,
            margin: [15, 0, 0, 0] 
          }));
        const descScopeContent = htmlToPdfMake(headerData[0].Desc_Scope).map(content => ({
            ...content,
            margin: [15, 0, 0, 0] 
          }));
        const descDocumentContent = htmlToPdfMake(headerData[0].Desc_Document).map(content => ({
            ...content,
            margin: [15, 0, 0, 0] 
          }));
        const descResponsibilitiesContent = htmlToPdfMake(headerData[0].Desc_Responsibilities).map(content => ({
            ...content,
            margin: [15, 0, 0, 0] 
          }));

        
       

        //   ${headerData[0].Product_Name}
        const productNameContent = htmlToPdfMake(headerData[0].Product_Name).map(content => ({
            ...content,
            margin: [30, 0, 0, 0]
          }));
          
          const productCodeContent = htmlToPdfMake(headerData[0].Product_Code).map(content => ({
            ...content,
            margin: [30, 0, 0, 0]
          }));
        
          const ppiNoContent = htmlToPdfMake(headerData[0].Ppi_No).map(content => ({
            ...content,
            margin: [30, 0, 0, 0]
          }));
        
          const batchSizeContent = htmlToPdfMake(headerData[0].Batch_Size).map(content => ({
            ...content,
            margin: [30, 0, 0, 0]
          }));
          const formulaDataContent = htmlToPdfMake(headerData[0].Formula).map(content => ({
            ...content,
            margin: [15, 0, 0, 0]
          }));
        
        



        var dd = {
            content: [
              { text: `\n${headerData[0].Title}\n\n`, style: 'subheader' },
              {
                style: 'tableExample',
                table: {
                  widths: ['*', '*', '*','*'],
                  body: [
                    ['Name', 'Position', 'Date', 'Reason'],
                    ['Prepared By', '', '',''],
                    [`${headerData[0].Created_by}`, `${headerData[0].Job_title}`, `${headerData[0].Created_Date.substring(0, 10)}`,''],
                    ['Reviewed By', '', '',''],
                    ...reviewerRows,
                    ['Approved By', '', '',''],
                    ...approverRows,
                  ]
                }
              },
              { text: '\nSejarah Perubahan Dokumen\n\n', style: 'subheader' },
              {
                style: 'tableExample',
                table: {
                  widths: ['auto', 'auto', 'auto', '*'],
                  body: [
                    ['No Protocol', 'Version', 'Date', 'Detail'],
                    ...protocolHistoryRows
                  ]
                }
              },
              { text: '\n1. Tujuan\n', style: 'smallHeader' },
              descPurposeContent,
              { text: '\n2. Ruang Lingkup\n', style: 'smallHeader' },
              descScopeContent,
              { text: '\n3. Dokumen acuan\n', style: 'smallHeader' },
              descDocumentContent,
              { text: '\n4. Tanggung Jawab\n', style: 'smallHeader' },
              descResponsibilitiesContent,
              { text: '\n5. Batch No\n', style: 'smallHeader' },
              ...batchNumberRows,
              { text: '\n6. Rancangan Studi\n', style: 'smallHeader' },
              { text: `Nama Produk : `, margin: [15, 0, 0, 0] },
              productNameContent,
              { text: `\nKode Produk : `, margin: [15, 0, 0, 0] },
              productCodeContent,
              { text: `\nNo PPI : `, margin: [15, 0, 0, 0] },
              ppiNoContent,
              { text: `\nBatch Size : `, margin: [15, 0, 0, 0] },
              batchSizeContent,
              {
                style: 'tableExample',
                table: {
                  widths: ['*', '*'],
                  body: [
                    ['Kondisi Penyimpanan', 'Interval Penyimpanan'],
                    ['T30 (30 °C ± 2°C / 75% ± 5% RH)', `${t30Data}`],
                    ['T40 (40 °C ± 2°C / 75% ± 5% RH)', `${t40Data}`],
                  ]
                }
              },
              { text: '\n7. Komposisi Formula\n', style: 'smallHeader' },
              formulaDataContent,
              { text: '\n8. Parameter pengujian dan spesifikasi\n', style: 'smallHeader' },
              {
                style: 'tableExample',
                table: {
                  widths: ['*', '*'],
                  body: [
                    ['Parameter', 'Spesifikasi'],     
                    ...detailDataRows                                   
                  ]
                }
              },
              { text: '\nKeterangan\n', style: 'smallHeader' },
              { text: `Dokumen ini disediakan oleh ${downloadedBy} pada ${downloadedDate}`}
            ],
            styles: {
              header: {
                fontSize: 22,
                bold: true,
                alignment: 'center'
              },
              subheader: {
                fontSize: 18,
                bold: true,
                alignment: 'center'
              },
              smallHeader: {
                bold: true,
              },
              tableExample: {
                margin: [0, 5, 0, 15]
              }
            },
            footer: function(currentPage, pageCount) {
                return {
                  columns: [
                    {
                      width: '*',
                      text: 'This document is printed by refer to SOP Document no : SOP-QC-O060',
                      alignment: 'left',
                      margin: [40, 10, 0, 0]
                    },
                    {
                      width: 'auto',
                      text: 'Page ' + currentPage + ' of ' + pageCount,
                      alignment: 'right',
                      margin: [0, 10, 40, 0]
                    }
                  ]
                };
              },              
              pageMargins: [40, 60, 40, 60]
          };
          pdfMake.createPdf(dd).download(` Print Out Protocol Document :${noProtocol}.pdf`);  

    }

    const generatePDF = async () => {
        // const input = document.getElementById("printedDocument");
        // const pdf = new jsPDF("p", "mm", "a4");

        // const canvas = await html2canvas(input);
        // const imageData = canvas.toDataURL("image/png");

        // //get image width and height
        // const imgProps= pdf.getImageProperties(imageData);
        // const pdfWidth = pdf.internal.pageSize.getWidth();
        // const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
        // //set image height to proposize with a4
        // pdf.addImage(imageData, "PNG", 0, 0, pdfWidth, pdfHeight); // A4 size
        // pdf.save(
        //     `Print Out Reporting Measurement - In Process Control ID [${noProtocol}]`
        // );
        setClickDownload(true);
        setStateShowDownload(false);
    };

    useEffect(() => {
        const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
        if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
        router.push('/');
        return;
        }
        setDownloadedBy(`${dataLogin.employee_id} - ${dataLogin.employee_name}`)


        const today = new Date();

        // Extract year, month, and day
        const year = today.getFullYear();
        const month = (today.getMonth() + 1).toString().padStart(2, '0'); // Months are zero-based
        const day = today.getDate().toString().padStart(2, '0');

        // Format as YYYY-MM-DD
        const formattedDate = `${year}-${month}-${day}`;
        setDownloadedDate(formattedDate)

        if (headerData === null || headerData === undefined || headerData.length === 0) {
            MySwal.fire({
                icon: "error",
                title: "Header Data NOT Found !",
            });
        }
        if (detailData === null || detailData === undefined || detailData.length === 0) {
            MySwal.fire({
                icon: "error",
                title: "Detail Data NOT Found !",
            });
        }
        if (t30Data === null || t30Data === undefined || t30Data.length === 0) {
            MySwal.fire({
                icon: "error",
                title: "T30 Data NOT Found !",
            });
        }
        if (t40Data === null || t40Data === undefined || t40Data.length === 0) {
            MySwal.fire({
                icon: "error",
                title: "T40 Data NOT Found !",
            });
        }
        
        batchNumber.map(item =>{
            console.log("ada data", item.Batch_number)
        })
    }, [headerData, detailData, t30Data, t40Data]);


    useEffect(() => {
        // window.print()
        // if (stateClicked) {
        //   const status = !stateClicked
        //   setStateClicked(status)
        //   //document.getElementById("headdownload").append()
        // }
        // console.log("first")

        if (clickDownload) {
            setClickDownload(false);
            window.print();
        }

        setStateShowDownload(true);
    }, [stateShowDownload]);


    if (stateShowDownload) {
        
        return(
            <div
            style={{
                width: "100%",
                padding: "1em",
                backgroundColor: "#2c2c30",
                boxShadow: "2px 2px 10px #6c6b75",
                position: "-webkit-sticky",
                position: "sticky",
                top: 0,
            }}
        >
            <Stack justifyContent="space-between">
                <Stack>
                    <p style={{ color: "white", fontSize: "1em" }}>
                        Print Out Protocol Document :{" "}
                        {noProtocol}
                    </p>
                </Stack>
                <Stack>
                    <Button title="Download" onClick={generatePDFNew}>
                        <FontAwesomeIcon
                            icon={faFileDownload}
                            style={{ fontSize: 15 }}
                        />
                    </Button>
                </Stack>
            </Stack>
        </div>
        )
    }else{
        return (
            <>
                <Head>
                    <title>
                        Print Out - Protocol Document :                     {noProtocol}
                    </title>
                </Head>
                <div>
                    {stateShowDownload && (
                        <div
                            style={{
                                width: "100%",
                                padding: "1em",
                                backgroundColor: "#2c2c30",
                                boxShadow: "2px 2px 10px #6c6b75",
                                position: "-webkit-sticky",
                                position: "sticky",
                                top: 0,
                            }}
                        >
                            <Stack justifyContent="space-between">
                                <Stack>
                                    <p style={{ color: "white", fontSize: "1em" }}>
                                        Print Out Protocol Document :{" "}
                                        {noProtocol}
                                    </p>
                                </Stack>
                                <Stack>
                                    <Button title="Download" onClick={generatePDFNew}>
                                        <FontAwesomeIcon
                                            icon={faFileDownload}
                                            style={{ fontSize: 15 }}
                                        />
                                    </Button>
                                </Stack>
                            </Stack>
                        </div>
                    )}
                    <div style={{ width: "100%", backgroundColor: "#65656b" }}>
                        <div
                            id="printedDocument"
                            style={{
                                width: "100%",
                                backgroundColor: "white",
                                margin: "auto",
                                padding: "4em 2em 4em 6em",
                            }}
                        >
                            <div>
                                <img
                                    src="/Logo_kalbe_detail.png"
                                    alt="Logo_kalbe_detail.png"
                                    style={{ width: 150 }}
                                />
                            </div>
                            <div style={{ marginBottom: "2em", marginTop: "2em" }}>
                                <p
                                    style={{
                                        textAlign: "center",
                                        fontWeight: "bold",
                                        fontSize: "2em",
                                    }}
                                >
                                    PROTOKOL UJI STABILITAS
                                </p>
                            </div>
                            <div style={{ marginBottom: "2em", marginTop: "2em" }}>
                                <p
                                    style={{
                                        textAlign: "center",
                                        fontWeight: "bold",
                                        fontSize: "24px",
                                    }}
                                >
                                    {headerData[0].Title}
                                </p>
                            </div>
                            <div style={{ width: '100%', overflowX: 'auto' }}>
                                <table style={{ width: '100%', borderCollapse: 'collapse', border: '1px solid #ddd' }}>
                                    <tbody>
                                        <tr>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>Name</td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>Position</td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>Date</td>
                                        </tr>
                                        <tr>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>Prepared by</td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}></td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}></td>
                                        </tr>
                                        <tr style={{ fontStyle: 'italic' }}>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>{headerData[0].Created_by}</td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>{headerData[0].Job_title}</td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>{headerData[0].Created_Date.substring(0, 10)}</td>
                                        </tr>
                                        <tr>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>Reviewed by</td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}></td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}></td>
                                        </tr>
                                        {reviewerOrder.length > 0 && reviewerOrder.map(item => 
                                        <tr style={{ fontStyle: 'italic' }}>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Approve_By}</td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Job_title}</td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                                                {parseInt(item.Approval_Status) == 0 && `Rejected at ${item.Approval_Date.substring(0, 10)}`}
                                                {parseInt(item.Approval_Status) == 1 && `Approved at ${item.Approval_Date.substring(0, 10)}`}
                                                {parseInt(item.Approval_Status) == 2 && `Waiting for Approval`}
                                                {parseInt(item.Approval_Status) == 3 && `Waiting for others approval`}
                                                {parseInt(item.Approval_Status) == 4 && `Rejected by others`}
                                                {parseInt(item.Approval_Status) == 5 && `Had approved but rejected by others`}
                                            </td>
                                        </tr>)}
                                        <tr>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>Approved by</td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}></td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}></td>
                                        </tr>
                                        {approverOrder.length > 0 && approverOrder.map(item => 
                                        <tr style={{ fontStyle: 'italic' }}>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Approve_By + ' - ' + item.Job_title}</td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Job_title}</td>
                                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                                                {parseInt(item.Approval_Status) == 0 && `Rejected at ${item.Approval_Date.substring(0, 10)}`}
                                                {parseInt(item.Approval_Status) == 1 && `Approved at ${item.Approval_Date.substring(0, 10)}`}
                                                {parseInt(item.Approval_Status) == 2 && `Waiting for Approval`}
                                                {parseInt(item.Approval_Status) == 3 && `Waiting for others approval`}
                                                {parseInt(item.Approval_Status) == 4 && `Rejected by others`}
                                                {parseInt(item.Approval_Status) == 5 && `Had approved but rejected by others`}
                                            </td>
                                        </tr>)}
                                    </tbody>
                                </table>
                            </div>
    
                            <div style={{ marginTop: '1rem' }}>
                                <p style={{
                                    textAlign: "center",
                                    fontWeight: "bold",
                                    fontSize: "1.2em",
                                }}>Sejarah Perubahan Dokumen</p>
                                <div style={{ width: '100%', overflowX: 'auto' }}>
                                    {protocolHistory != undefined && protocolHistory != null && protocolHistory?.length > 0 && <table style={{ width: '100%', borderCollapse: 'collapse', border: '1px solid #ddd' }}>
                                        <thead>
                                            <tr>
                                                <th style={{ border: '1px solid #ddd', padding: '8px' }}>No Protocol</th>
                                                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Date</th>
                                                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Detail</th>
                                                <th style={{ border: '1px solid #ddd', padding: '8px' }}>Version</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {protocolHistory.map(item => <tr>
                                                <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.No_Protocol}</td>
                                                <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Created_Date.slice(0, 10)}</td>
                                                <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Remark_Change}</td>
                                                <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Protocol_Version}</td>
                                            </tr>)}
                                        </tbody>
                                    </table>}
                                    {protocolHistory?.length == 0 && <div style={{ textAlign: 'center', padding: '2em 0', textDecoration: 'underline' }}>
                                        <p>Tidak ada data perubahan</p>
                                    </div>}
                                </div>
                            </div>
    
                            <div style={{ marginTop: '1rem' }}>
                                <ol className="list-decimal">
                                    <li><strong>Tujuan</strong><br />
                                        {headerData !== null && headerData !== undefined && headerData.length > 0 && <div  dangerouslySetInnerHTML={{ __html: headerData[0].Desc_Purpose }} />}
                                    </li>
                                    <li><strong>Ruang lingkup</strong><br />
                                        {headerData !== null && headerData !== undefined && headerData.length > 0 && <div dangerouslySetInnerHTML={{ __html: headerData[0].Desc_Scope }} />}
                                    </li>
                                    <li><strong>Dokumen acuan</strong><br />
                                        {headerData !== null && headerData !== undefined && headerData.length > 0 && <div dangerouslySetInnerHTML={{ __html: headerData[0].Desc_Document }} />}
                                    </li>
                                    <li><strong>Tanggung jawab</strong><br />
                                        {headerData !== null && headerData !== undefined && headerData.length > 0 && <div dangerouslySetInnerHTML={{ __html: headerData[0].Desc_Responsibilities }} />}
                                    </li>
                                    
                                    <li><strong>Batch No</strong><br />
                                        <div>
                                            <table style={{ minWidth: '20rem' }}>
                                                <thead>
                                                    <tr>
                                                        <th style={{ minWidth: '8rem' }}></th>
                                                        <th style={{ minWidth: '2rem' }}></th>
                                                        <th style={{ minWidth: '10rem' }}></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {batchNumber !== null && batchNumber !== undefined && batchNumber.length > 0 && 
                                                        batchNumber.map((batchNumber_data) =>{
                                                        return <tr>
                                                            <td>Batch No</td>
                                                            <td>:</td>
                                                            <td>
                                                               <div dangerouslySetInnerHTML={{ __html: batchNumber_data.Batch_number  }} />
                                                            </td>
                                                        </tr>
                                                        })                                                    
                                                     }                                                                                          
                                                </tbody>
                                            </table>
                                        </div>
                                    </li>
                                    <li><strong>Rancangan studi</strong><br />
                                        <div>
                                            <table style={{ minWidth: '20rem' }}>
                                                <thead>
                                                    <tr>
                                                        <th style={{ minWidth: '8rem' }}></th>
                                                        <th style={{ minWidth: '2rem' }}></th>
                                                        <th style={{ minWidth: '10rem' }}></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>Nama produk</td>
                                                        <td>:</td>
                                                        <td>{headerData !== null && headerData !== undefined && headerData.length > 0 && <div dangerouslySetInnerHTML={{ __html: headerData[0].Product_Name }} />}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Kode produk</td>
                                                        <td>:</td>
                                                        <td>{headerData !== null && headerData !== undefined && headerData.length > 0 && <div dangerouslySetInnerHTML={{ __html: headerData[0].Product_Code }} />}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Nomor PPI</td>
                                                        <td>:</td>
                                                        <td>{headerData !== null && headerData !== undefined && headerData.length > 0 && <div dangerouslySetInnerHTML={{ __html: headerData[0].Ppi_No }} />}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Batch size</td>
                                                        <td>:</td>
                                                        <td>{headerData !== null && headerData !== undefined && headerData.length > 0 && <div dangerouslySetInnerHTML={{ __html: headerData[0].Batch_Size }} />}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div style={{ width: '100%', overflowX: 'auto', margin: '2em 0' }}>
                                            <table style={{ width: '100%', borderCollapse: 'collapse', border: '1px solid #ddd' }}>
                                                <thead>
                                                    <tr>
                                                        <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>Kondisi Penyimpanan</th>
                                                        <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>Interval Pemeriksaan</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>T30 (30c +- 2c / 75% +- 5% RH)</td>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{t30Data}</td>
                                                    </tr>
                                                    <tr>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>T40 (40c +- 2c / 75% +- 5% RH)</td>
                                                        <td style={{ border: '1px solid #ddd', padding: '8px' }}>{t40Data}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </li>
                                    <li><strong>Komposisi formula</strong><br />
                                        {headerData !== null && headerData !== undefined && headerData.length > 0 && <div dangerouslySetInnerHTML={{ __html: headerData[0].Formula }} />}
                                    </li>
                                    <li><strong>Parameter pengujian dan spesifikasi</strong><br />
                                        <div style={{ width: '100%', overflowX: 'auto', margin: '2em 0' }}>
                                            <table style={{ width: '100%', borderCollapse: 'collapse', border: '1px solid #ddd' }}>
                                                <thead>
                                                    <tr>
                                                        <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>Parameter</th>
                                                        <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left', textAlign: 'center' }}>Spesifikasi</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {detailData.length > 0 && detailData.map(item => {
    
                                                        // If Description
                                                        if (item.Input_Method_Id == 1) {
                                                            return <tr>
                                                                <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Parameter_Name}</td>
                                                                <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Desc}</td>
                                                            </tr>;
                                                        }
    
                                                        // If Value Reference / Absolute
                                                        if (item.Input_Method_Id == 2) {
                                                            return <tr>
                                                                <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Parameter_Name}</td>
                                                                <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Value_Reference}</td>
                                                            </tr>;
                                                        }
    
                                                        // If Range
                                                        if (item.Input_Method_Id == 3) {
                                                            return <tr>
                                                                <td style={{ border: '1px solid #ddd', padding: '8px' }}>{item.Parameter_Name}</td>
                                                                <td style={{ border: '1px solid #ddd', padding: '8px' }}>{`${item.Min_Value} - ${item.Max_Value}`}</td>
                                                            </tr>;
                                                        }
                                                    })}
                                                </tbody>
                                            </table>
                                        </div>
                                    </li>
                                    <li style={{listStyle:`none`}}><strong>Keterangan</strong><br />
                                        <div style={{ width: '100%', overflowX: 'auto', margin: '2em 0' }}>
                                            <p>Dokumen ini disediakan oleh {downloadedBy} pada {downloadedDate}</p>
                                        </div>
                                    </li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
             {/* Styled JSX */}
            <style jsx global>{`
                ol {
                list-style: revert;
                }
                    
                ul {
                list-style: revert;
                }
            `}</style>
            </>
        );
    }
   
}

export async function getServerSideProps(context) {
    const { query } = context;
    const { noProtocol, userDept, userData } = query;
    const { GetProtocolDataByNoProtocol, GetProtocolHistory, GetHasilAnalisaForReport } = ProtocolApi();

    const dataReqHeader = {
        no_protocol: noProtocol,
        department: parseInt(userDept)
    };

    const { data: protocolData } =
        await GetProtocolDataByNoProtocol(dataReqHeader);

    let headerData = [];
    let detailData = [];
    let t30Data = '';
    let t30Index = 1;
    let t40Data = '';
    let t40Index = 1;
    let reviewerOrder = [];
    let approverOrder = [];
    let batchNumber = [];

    if (protocolData !== undefined && protocolData !== null) {
        const { Header_Data, Detail_Data, T30Data, T40Data, ReviewerOrder, ApproverOrder, BatchNumber_Data } = protocolData;
        headerData = Header_Data !== null ? Header_Data : [];
        detailData = Detail_Data !== null ? Detail_Data : [];
        reviewerOrder = ReviewerOrder !== null ? ReviewerOrder : [];
        approverOrder = ApproverOrder !== null ? ApproverOrder : [];
        batchNumber = BatchNumber_Data!== null ? BatchNumber_Data: [];

        if (T30Data != null && T30Data != undefined && T30Data.length > 0) {
            const t30RawData = T30Data.map(item => item.Cycle_Month);
            t30RawData.sort(function (a, b) {
                return a - b;
            });
            t30RawData.map(item => {
                if (T30Data.length == t30Index) {
                    t30Data += `${item} Bulan`;
                    return;
                }
                t30Data += `${item},`;
                t30Index += 1;
            });
        }

        if (T40Data != null && T40Data != undefined && T40Data.length > 0) {
            const t40RawData = T40Data.map(item => item.Cycle_Month);
            t40RawData.sort(function (a, b) {
                return a - b;
            });
            t40RawData.map(item => {
                if (T40Data.length == t40Index) {
                    t40Data += `${item} Bulan`;
                    return;
                }
                t40Data += `${item},`;
                t40Index += 1;
            });
        }
    }

    let protocolHistory = [];
    const { data: protocolHistoryData } = await GetProtocolHistory({ no_protocol: noProtocol });
    if (protocolHistoryData != undefined && protocolHistoryData != null) {
        protocolHistory = protocolHistoryData;
    }

    let hasilAnalisa = [];
    const { data: hasilAnalisaData } = await GetHasilAnalisaForReport({ no_protocol: noProtocol });
    if (hasilAnalisaData != undefined && hasilAnalisaData != null) {
        hasilAnalisa = hasilAnalisaData;
    }



    

    return {
        props: {
            noProtocol,
            headerData,
            detailData,
            t30Data,
            t40Data,
            reviewerOrder,
            approverOrder,
            protocolHistory,
            hasilAnalisa,
            batchNumber,
        },
    };
}
