import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  try {
    const response = await axios[method](
      `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/${url}`,
      data
    ) .then((res) => { return res.data })
      .catch((err) => { return err.response.data });
    return response;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default function ApiTsHeader() {
  return {
    addTsHeader: createApiFunction("post", "ts-header-trans/add-header-detail-attacthments"),
  };
}
