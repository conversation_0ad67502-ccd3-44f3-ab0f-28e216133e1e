import { useEffect, useState } from "react";
import Head from "next/head";
import {
  <PERSON><PERSON>,
  But<PERSON>,
} from "rsuite";
import { faFileDownload } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import ApiScaleHeader from "@/pages/api/ipc_scale/report/api_ipc_scale";
import { useRouter } from "next/router";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";

pdfMake.vfs = pdfFonts.pdfMake.vfs;

export default function IpcScaleReportingMeasurementPdf() {
  const router = useRouter();
  const { idTransactionH } = router.query;
  const [idRouter, setIdRouter] = useState(null);
  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [ScaleHeaderDataState, setScaleHeaderDataState] = useState([]);
  const [statisticsDataState, setStatisticsDataState] = useState(null);

  const HandleGetScaleDetailWithHeader = async (id_transaction) => {
    try {
      const res = await ApiScaleHeader().getScaleDetailWithStatistics({
        id_transaction: parseInt(id_transaction),
      });
      if (res.status === 200) {
        setScaleHeaderDataState(res.data.data_automate);
        setStatisticsDataState(res.statistics);
      } else {
        console.log("Error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("Error on catch Get All Api", error);
    }
  };

  // useEffect(() => {
  //  console.log("id_Score =-", id_score)
  // }, [id_score])
  

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else if (idTransactionH) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("ipc")
      );
      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      setIdRouter(idTransactionH);
      HandleGetScaleDetailWithHeader(idTransactionH);
    }
  }, [router, idTransactionH]);

  const generatePdfNew = (action) => {
    if (!ScaleHeaderDataState || !Array.isArray(ScaleHeaderDataState)) {
      console.error("Data belum tersedia atau salah format:", ScaleHeaderDataState);
      return;
    }

    const data = ScaleHeaderDataState.find(
      (item) => item.id_transaction == idTransactionH
    );
    console.log(data);
    if (!data) {
      console.warn(
        "Tidak ada data yang cocok dengan id_transaction:",
        idTransactionH
      );
      return;
    }

    // Format tanggal menggunakan singkatan bulan
    let monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    // Format Date Checked (Created_Date)
    let [day, month, year] = data.created_date.split("-");
    let monthAbbreviation = monthNames[parseInt(month, 10) - 1];
    let formattedDateCreated = `${day}-${monthAbbreviation}-${year}`;

    // Format Approve_Date jika tersedia
    let formattedDateReviewed = "";
    if (data.approve_date) {
      const [dayReviewed, monthReviewed, yearReviewed] = data.approve_date.split("-");
      let monthAbbreviationReviewed = monthNames[parseInt(monthReviewed, 10) - 1];
      formattedDateReviewed = `${dayReviewed}-${monthAbbreviationReviewed}-${yearReviewed}`;
    }

    // Siapkan teks footer sesuai instruksi
    const operatedByText = `Operated By: ${data.employee_name} at ${formattedDateCreated}`;
    const reviewedByText = `Reviewed By and Approved By: ${data.approve_by ? `${data.approve_by} at ${formattedDateReviewed}` : 'NA'}`;
    const sopText = 'This document is printed by refer to SOP Document no SOP-PR-O061.00';

    const headerContent = [
      { text: "Measurement Analysis Report", style: "header" },
      { text: `\n\nOperator Name  : ${data.employee_name}`, style: "detail" },
      { text: `Date Checked     : ${formattedDateCreated}`, style: "detail" },
      { text: `Product Code     : ${data.product_code}`, style: "detail" },
      { text: `Station                 : ${data.station}`, style: "detail" },
      { text: `Step                      : ${data.step}`, style: "detail" },
      { text: `Amount TMS       : ${data.amount_tms}`, style: "detail" },
      { text: `Amount MS         : ${data.amount_ms}\n\n`, style: "detail" },
    ];

    const tableBody = [
      [
        "No",
        "Scale Weight",
        "Status",
        "Time",
        "Reason",
      ],
    ];

    ScaleHeaderDataState.forEach((item, index) => {
      tableBody.push([
        index + 1,
        item.scale_weight || "-",
        item.status || "-",
        item.time || "-",
        item.reason || "N/A",
      ]);
    });

    if (statisticsDataState) {
      tableBody.push([
        "Min",
        statisticsDataState.scale_weight_min ?? "-",
        "-",
        "-",
        "-",
      ]);

      tableBody.push([
        "Max",
        statisticsDataState.scale_weight_max ?? "-",
        "-",
        "-",
        "-",
      ]);

      tableBody.push([
        "Average",
        statisticsDataState.scale_weight_avg
          ? statisticsDataState.scale_weight_avg.toFixed(2)
          : "-",
        "-",
        "-",
        "-",
      ]);
      tableBody.push([
        "Difference",
        statisticsDataState.scale_weight_diff ?? "-",
        "-",
        "-",
        "-",
      ]);
    }

    // Definisikan objek PDF (document definition)
    var dd = {
      content: [
        ...headerContent,
        {
          style: "tableExample",
          table: {
            widths: ["auto", "auto", "auto", "auto", "auto"],
            body: tableBody,
          },
        },
      ],
      styles: {
        header: {
          fontSize: 22,
          bold: true,
          alignment: "center",
        },
        tableExample: {
          margin: [0, 5, 0, 15],
        },
      },
      footer: (currentPage, pageCount) => {
        return {
          columns: [
            {
              width: "*",
              text: operatedByText + "\n" + reviewedByText + "\n" + sopText,
              margin: [30, 0, 0, 0],
            },
            {
              width: "auto",
              text: "Page " + currentPage + " of " + pageCount,
              alignment: "right",
              margin: [0, 0, 30, 0],
            },
          ],
        };
      },
      pageMargins: [40, 60, 40, 60],
    };

    if (action === "Download") {
      pdfMake
        .createPdf(dd)
        .download(`Print Out Measurement Analysis - Transaction ID ${idTransactionH}.pdf`);
    } else {
      pdfMake.createPdf(dd).open();
    }
  };


  return (

    <div
      style={{
        width: "100%",
        padding: "1em",
        backgroundColor: "#2c2c30",
        position: "sticky",
        top: 0,
      }}
    >
      <Head>
        <title>Reporting Measurement Analysis</title>
      </Head>
      <Stack justifyContent="space-between">
        <p style={{ color: "white", fontSize: "1em" }}>
          Print Out Reporting Measurement Analysis - Transaction ID : {idTransactionH}
        </p>
        <Stack>
          <Button
            onClick={() => generatePdfNew("preview")}
            style={{ marginRight: "5px" }}
          >
            Preview
          </Button>
          <Button onClick={() => generatePdfNew("Download")}>
            <FontAwesomeIcon
              icon={faFileDownload}
              style={{ fontSize: 15 }}
            /> Download
          </Button>
        </Stack>
      </Stack>
    </div>
  );
}
