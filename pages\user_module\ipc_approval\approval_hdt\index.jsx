import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import ContainerLayout from '@/components/layout/ContainerLayout';
import Head from 'next/head';
import ApprovalMeasurementData from './ApprovalMeasurementData';

export default function ApprovalMeasurement({ parentMenuName, childMenuName }) {
  const router = useRouter();
  const [pageIndex, setPageIndex] = useState('index');
  const [interComponentData, setInterComponentData] = useState({});
  let { data } = router.query;
  data ? (data = JSON.parse(data)) : data;
  const path = 'ipc_approval';

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    if (!dataLogin) {
      router.push('/');
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push('/dashboard');
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path)
    );
    if (validateUserAccess.length === 0) {
      router.push('/dashboard');
      return;
    }
  }, []);

  const setPage = (page) => {
    setPageIndex(page);
  };

  const setInterData = (objectData) => {
    setInterComponentData(objectData);
  };
  return (
    <>
      <div>
        <Head>
          <title>Approval Measurement</title>
        </Head>
      </div>
      <ContainerLayout title="User Module" menuIcon={data?.menuIcon}>
        {pageIndex === 'index' && (
          <ApprovalMeasurementData
            setPage={setPage}
            setInterData={setInterData}
            interComponentData={interComponentData}
            // parentMenuName={parentMenuName}
            // childMenuName={childMenuName}
          />
        )}
      </ContainerLayout>
    </>
  );
}

// export async function getServerSideProps({ query }) {
//   const { parentMenuName, childMenuName } = query;

//   return {
//     props: {
//       parentMenuName,
//       childMenuName,
//     },
//   };
// }
