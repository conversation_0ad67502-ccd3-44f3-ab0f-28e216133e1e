import * as React from "react";
import { styled } from "@mui/material/styles";
import MuiAccordion from "@mui/material/Accordion";
import MuiAccordionSummary from "@mui/material/AccordionSummary";
import MuiAccordionDetails from "@mui/material/AccordionDetails";
import Typography from "@mui/material/Typography";

const Accordion = styled((props) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  "&:not(:last-child)": {
    borderBottom: 0,
  },
  "&:before": {
    display: "none",
  },
}));

const AccordionSummary = styled((props) => <MuiAccordionSummary {...props} />)(
  ({ theme }) => ({
    backgroundColor:
      theme.palette.mode === "dark"
        ? "rgba(255, 255, 255, .05)"
        : "rgba(0, 0, 0, .03)",
    flexDirection: "row-reverse",
    "& .MuiAccordionSummary-expandIconWrapper.Mui-expanded": {
      transform: "rotate(90deg)",
    },
    "& .MuiAccordionSummary-content": {
      marginLeft: theme.spacing(1),
    },
  })
);

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  padding: theme.spacing(2),
  borderTop: "1px solid rgba(0, 0, 0, .125)",
}));

export default function CustomizedAccordions(props) {
  const [menuDataParent, setMenuDataParent] = React.useState([]);
  const [menuDataChild, setMenuDataChild] = React.useState([]);
  const [expanded, setExpanded] = React.useState("panel1");

  const handleChange = (panel) => (event, newExpanded) => {
    setExpanded(newExpanded ? panel : false);
  };

  React.useEffect(() => {
    // memisahkan parent dan child
    setMenuDataParent(props.menuData.filter((item) => item.Is_Parent === 1));
    setMenuDataChild(props.menuData.filter((item) => item.Is_Parent === 0));
  }, [props.menuData]);

  return (
    <div>
      {menuDataParent.map((item) => {
        return (
          <Accordion
            expanded={expanded === `panel${item.Id_Menu}`}
            onChange={handleChange(`panel${item.Id_Menu}`)}
          >
            <AccordionSummary id={`panel${item.Id_Menu}-header`}>
              <Typography>{item.Menu_Name}</Typography>
            </AccordionSummary>
            {/* <AccordionDetails> */}
            {menuDataChild.map((item) => {
              return (
                <button
                  key={item.Id_Menu}
                  className="container border-0 btn btn-secondary"
                >
                  {item.Menu_Name}
                </button>
              );
            })}
          </Accordion>
        );
      })}
    </div>
  );
}
