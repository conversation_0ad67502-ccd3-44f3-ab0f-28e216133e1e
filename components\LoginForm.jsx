import { useState } from "react";
import styles from "./LoginForm.module.css";
import { useRouter } from "next/router";
import { useUser } from "@/contexts/UserContext";
import UseTestApi from "@/pages/api/userApi";

export default function LoginForm() {
  const { user, login, logout } = useUser();
  const [employeeId, setEmployeeId] = useState("");
  const [password, setPassword] = useState("");
  const [isInputInvalid, setIsInputInvalid] = useState(false);

  const { UserLoginApi } = UseTestApi();

  // Email handler
  const employeeIdHandler = (event) => {
    setEmployeeId(event.target.value);
  };

  // Password handler
  const passwordHandler = (event) => {
    setPassword(event.target.value);
  };

  // Sign in handler
  const signinHandler = async (event) => {
    event.preventDefault();

    let signinData = {
      employee_id: employeeId,
      password: password,
    };

    // Send the data
    const login = await User<PERSON>ogin<PERSON>pi(signinData);
    // console.log(login);
    if (login !== null) {
      console.log(login);
    } else {
      console.log("Akun Tidak Ditemukan!");
    }

    // Clearing the form field
    setEmployeeId("");
    setPassword("");
  };

  return (
    <div className="container-fluid d-flex align-items-center justify-content-center h-100">
      <div className={`card mt-5 p-5 shadow-lg ${styles.customCardWidth}`}>
        <div className="text-center card-title">
          <h1>Login</h1>
        </div>
        {isInputInvalid && (
          <div className="bg-danger-subtle text-center p-3 text-danger rounded">
            Akun tidak ditemukan
          </div>
        )}
        <form className="p-5" onSubmit={signinHandler}>
          <div className="mb-3 has-validation">
            <label htmlFor="exa`mpleInputEmail1" className="form-label">
              Employee ID
            </label>
            <input
              type="text"
              className="form-control"
              id="exampleInputEmail1"
              aria-describedby="emailHelp"
              value={employeeId}
              onChange={employeeIdHandler}
              required
            />
            <div className="invalid-feedback">Error message</div>
          </div>

          <div className="mb-3">
            <label htmlFor="exampleInputPassword1" className="form-label">
              Password
            </label>
            <input
              type="password"
              className="form-control"
              id="exampleInputPassword1"
              value={password}
              onChange={passwordHandler}
              required
            />
          </div>
          <div className="mb-3 form-check">
            <input
              type="checkbox"
              className="form-check-input"
              id="exampleCheck1"
            />
            <label className="form-check-label" htmlFor="exampleCheck1">
              Remember Me
            </label>
          </div>
          <button type="submit" className="btn btn-primary">
            Sign in
          </button>
        </form>
      </div>
    </div>
  );
}
