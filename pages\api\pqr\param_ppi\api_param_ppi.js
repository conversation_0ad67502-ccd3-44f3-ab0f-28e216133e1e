import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function Api_param_ppi() {
  return {
    getParamWetmillN: createApiFunction("post", "pqr/pqr_param_ppi/wetmill_n"),
    getParamWetmillY: createApiFunction("post", "pqr/pqr_param_ppi/wetmill_y"),
    getStepWetmillN: createApiFunction("post", "pqr/pqr_param_ppi/step_wetmill_n"),
    getStepWetmillY: createApiFunction("post", "pqr/pqr_param_ppi/step_wetmill_y"),
  };
}
