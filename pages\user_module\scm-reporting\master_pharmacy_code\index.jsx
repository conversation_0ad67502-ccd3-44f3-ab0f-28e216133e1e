import ContainerLayout from '@/components/layout/ContainerLayout'
import Head from 'next/head'
import React, { useEffect, useState } from 'react'
import { <PERSON>readcrumb, Button, Form, IconButton, Input, InputGroup, Modal, Pagination, Panel, Stack, Table, Tag, toaster } from 'rsuite'
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import EditIcon from '@rsuite/icons/Edit';
import API_MasterPharmacyCode from "@/pages/api/scm-reporting/api_master_pharmacy_code";
import Messages from '@/components/Messages';
import { useRouter } from 'next/router';

export default function MasterPharmacyCodePage() {

    const [moduleName, setModuleName] = useState("");
    const [props, setProps] = useState([]);
    const router = useRouter();

    const { HeaderCell, Cell, Column } = Table;
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();

    const [masterPharmacyCode, setMasterPharmacyCode] = useState([]);

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showAddModal, setShowAddModal] = useState(false);
    const [showUpdateModal, setShowUpdateModal] = useState(false);

    const emptyForm = {
        code_pbf: null,
        name_pbf: null
    }
    const [formValue, setFormValue] = useState(emptyForm);
    const emptyError = {
        code_pbf: null,
        name_pbf: null
    }
    const [errors, setErrors] = useState(emptyError);

    const handleGetAllApi = async () => {
        const response = await API_MasterPharmacyCode().getAll();

        if (response.status === 200) {
            setMasterPharmacyCode(response.data ? response.data : []);
        }
    }

    const handleUpdateStatusApi = async (id_pbf, is_active) => {
        await API_MasterPharmacyCode().updateStatus({
            id_pbf: id_pbf,
            is_active: is_active,
            deleted_by: props.employee_id
        })
        handleGetAllApi();
    }

    const handleCreateApi = async () => {
        setIsSubmitting(true);
        try {
            const res = await API_MasterPharmacyCode().create({
                ...formValue,
                created_by: props.employee_id,
            })

            if (res.status === 200) {
                toaster.push(
                    Messages("success", "Success: Data has been created!"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                )
                setShowAddModal(false);
                handleGetAllApi();
            }
        } catch (err) {
            console.log("Axios Error: ", err);
            toaster.push(
                Messages("error", "Error: Please try again later!"),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            )
        } finally {
            setIsSubmitting(false);
            setFormValue(emptyForm);
            setErrors(emptyError);
        }
    }

    const handleUpdateApi = async () => {
        setIsSubmitting(true);
        try {
            const res = await API_MasterPharmacyCode().update({
                ...formValue,
                id_pbf: formValue.id_pbf,
                updated_by: props.employee_id,
            })

            if (res.status === 200) {
                toaster.push(
                    Messages("success", "Success: Data has been updated!"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                )
                setShowUpdateModal(false);
                handleGetAllApi();
            }
        } catch (err) {
            console.log("Axios Error: ", err);
            toaster.push(
                Messages("error", "Error: Please try again later!"),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            )
        } finally {
            setIsSubmitting(false);
            setFormValue(emptyForm);
            setErrors(emptyError);
        }
    }

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("master_pharmacy_code")
            );
            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            handleGetAllApi();
        }
    }, []);

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = masterPharmacyCode
        .filter((rowData, i) => {
            const searchFields = [
                "id_pbf",
                "code_pbf",
                "name_pbf",
                "created_by",
                "created_dt",
                "updated_by",
                "updated_dt",
                "deleted_by",
                "deleted_dt",
                "is_active",
            ];

            const matchesSearch = searchFields.some((field) =>
                rowData[field]
                    ?.toString()
                    .toLowerCase()
                    .includes(searchKeyword.toLowerCase())
            );

            return matchesSearch;
        })

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    }

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : masterPharmacyCode.length;

    const formatTableDate = (date) => {
        const dateObj = new Date(date);
        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, "0");
        const day = String(dateObj.getDate()).padStart(2, "0");
        return `${day}/${month}/${year}`;
    };

    return (
        <>
            <div>
                <Head>
                    <title>Master Pharmacy Code</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className='m-4 pt-2'>
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Quality</Breadcrumb.Item>
                                    <Breadcrumb.Item>SCM</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Master Pharmacy Code</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                        header={
                            <Stack justifyContent='space-between'>
                                <IconButton
                                    icon={<PlusRoundIcon />}
                                    appearance="primary"
                                    onClick={() => {
                                        setShowAddModal(true);
                                    }}>
                                    Add
                                </IconButton>

                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="search"
                                        value={searchKeyword}
                                        onChange={handleSearch}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}
                                    >
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                        }
                    >
                        <Table
                            bordered
                            cellBordered
                            height={400}
                            data={getPaginatedData(getFilteredData(), limit, page)}
                            sortColumn={sortColumn}
                            sortType={sortType}
                            onSortColumn={handleSortColumn}
                        >
                            <Column width={70} align='center' fixed>
                                <HeaderCell>No</HeaderCell>
                                <Cell>
                                    {(rowData, index) => {
                                        return index + 1 + limit * (page - 1);
                                    }}
                                </Cell>
                            </Column>
                            <Column width={70} align='center'>
                                <HeaderCell>ID</HeaderCell>
                                <Cell dataKey='id_pbf' />
                            </Column>
                            <Column width={200} sortable resizable>
                                <HeaderCell align='center'>Code PBF</HeaderCell>
                                <Cell dataKey='code_pbf' />
                            </Column>
                            <Column width={200} sortable resizable>
                                <HeaderCell align='center'>Name PBF</HeaderCell>
                                <Cell dataKey='name_pbf' />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell align='center'>Created By</HeaderCell>
                                <Cell dataKey='created_by' />
                            </Column>
                            <Column width={150} align='center' sortable resizable>
                                <HeaderCell>Created Date</HeaderCell>
                                <Cell>
                                    {(rowData) => (
                                        <span>{formatTableDate(rowData.created_dt)}</span>
                                    )}
                                </Cell>
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell align='center'>Updated By</HeaderCell>
                                <Cell dataKey='updated_by' />
                            </Column>
                            <Column width={150} align='center' sortable resizable>
                                <HeaderCell>Updated Date</HeaderCell>
                                <Cell dataKey='updated_dt' />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell align='center'>Deleted By</HeaderCell>
                                <Cell dataKey='deleted_by' />
                            </Column>
                            <Column width={150} align='center' sortable resizable>
                                <HeaderCell>Deleted Date</HeaderCell>
                                <Cell dataKey='deleted_dt' />
                            </Column>
                            <Column width={100} align='center' sortable resizable>
                                <HeaderCell>Status</HeaderCell>
                                <Cell>
                                    {(rowData) => (
                                        <span
                                            style={{
                                                color: rowData.is_active === 1 ? "green" : "red",
                                            }}
                                        >
                                            {rowData.is_active === 1 ? "Active" : "Inactive"}
                                        </span>
                                    )}
                                </Cell>
                            </Column>
                            <Column width={100} fixed="right" align="center">
                                <HeaderCell>Action</HeaderCell>
                                <Cell style={{ padding: "8px" }}>
                                    {(rowData) => (
                                        <div>
                                            <Button
                                                appearance="subtle"
                                                disabled={rowData.is_active === 0}
                                                onClick={() => {
                                                    setShowUpdateModal(true);
                                                    setFormValue(rowData);
                                                }}
                                            >
                                                <EditIcon />
                                            </Button>
                                            <Button
                                                appearance="subtle"
                                                onClick={() => { handleUpdateStatusApi(rowData.id_pbf, rowData.is_active === 1 ? 0 : 1); }}
                                            >
                                                {rowData.is_active === 1 ? (
                                                    <TrashIcon style={{ fontSize: "16px" }} />
                                                ) : (
                                                    <ReloadIcon style={{ fontSize: "16px" }} />
                                                )}
                                            </Button>
                                        </div>
                                    )}
                                </Cell>
                            </Column>
                        </Table>
                        <div style={{ padding: 20 }}>
                            <Pagination
                                prev
                                next
                                first
                                last
                                ellipsis
                                boundaryLinks
                                maxButtons={5}
                                size="xs"
                                layout={["total", "-", "limit", "|", "pager", "skip"]}
                                limitOptions={[10, 30, 50]}
                                total={totalRowCount}
                                limit={limit}
                                activePage={page}
                                onChangePage={setPage}
                                onChangeLimit={handleChangeLimit}
                            />
                        </div>
                    </Panel>
                </div>

                {/* Add Modal */}
                <Modal
                    backdrop="static"
                    open={showAddModal}
                    overflow={false}
                    onClose={() => {
                        setShowAddModal(false);
                        setFormValue(emptyForm);
                        setErrors(emptyError);
                    }}
                >
                    <Modal.Header>
                        <Modal.Title>Add Master Pharmacy Code</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Code PBF</Form.ControlLabel>
                                <Form.Control
                                    name='code_pbf'
                                    value={formValue.code_pbf}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, code_pbf: value });
                                        setErrors((prevErrors) => ({ ...prevErrors, code_pbf: null }));
                                    }}
                                    errorMessage={errors.code_pbf}
                                />
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Name PBF</Form.ControlLabel>
                                <Form.Control
                                    name='name_pbf'
                                    value={formValue.name_pbf}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, name_pbf: value });
                                        setErrors((prevErrors) => ({ ...prevErrors, name_pbf: null }));
                                    }}
                                    errorMessage={errors.name_pbf}
                                />
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowAddModal(false);
                                setFormValue(emptyForm);
                                setErrors(emptyError);
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleCreateApi();
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Add
                        </Button>
                    </Modal.Footer>
                </Modal>

                {/* Update Modal */}
                <Modal
                    backdrop="static"
                    open={showUpdateModal}
                    overflow={false}
                    onClose={() => {
                        setShowUpdateModal(false);
                        setFormValue(emptyForm);
                        setErrors(emptyError);
                    }}
                >
                    <Modal.Header>
                        <Modal.Title>Update Master Pharmacy Code</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Code PBF</Form.ControlLabel>
                                <Form.Control
                                    name='code_pbf'
                                    value={formValue.code_pbf}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, code_pbf: value });
                                        setErrors((prevErrors) => ({ ...prevErrors, code_pbf: null }));
                                    }}
                                    errorMessage={errors.code_pbf}
                                />
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Name PBF</Form.ControlLabel>
                                <Form.Control
                                    name='name_pbf'
                                    value={formValue.name_pbf}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, name_pbf: value });
                                        setErrors((prevErrors) => ({ ...prevErrors, name_pbf: null }));
                                    }}
                                    errorMessage={errors.name_pbf}
                                />
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowUpdateModal(false);
                                setFormValue(emptyForm);
                                setErrors(emptyError);
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleUpdateApi(formValue.id_pbf);
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Update
                        </Button>
                    </Modal.Footer>
                </Modal>
            </ContainerLayout>
        </>
    )
}
