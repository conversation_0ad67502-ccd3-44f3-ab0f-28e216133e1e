import axios from "axios";

export default function departmentApi() {
  const GetAllDepartment = async () => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/department/getAll`)
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetDepartmentById = async (idDept) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/department/getById`,
        idDept
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const PostDepartment = async (postData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/department/addDept`,
        postData
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const PutDepartment = async (putData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/department/putDept`,
        putData
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  return {
    GetAllDepartment,
    PostDepartment,
    PutDepartment,
    GetDepartmentById,
  };
}
