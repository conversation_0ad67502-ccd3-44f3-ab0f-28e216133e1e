import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiRecipe() {
  return {
    getAllRecipeByIdPPI: createApiFunction("post", "pqr/recipe/get"),
    createRecipe: createApiFunction("post", "pqr/recipe/create-step"),
    updateRecipe: createApiFunction("post", "pqr/recipe/update-step"),
    createParameter: createApiFunction("post", "pqr/recipe/create-parameter"),
    updateParameter: createApiFunction("post", "pqr/recipe/update-parameter"),
    editStatusRecipeStep: createApiFunction("put", "pqr/recipe/edit-status"),
    editStatusRecipeParameter: createApiFunction("put", "pqr/recipe/edit-status-parameter"),
  };
}
