import { createContext, useContext, useState } from "react";
import UseTest<PERSON>pi from "@/pages/api/userApi";

const UserContext = createContext();

export function UserProvider({ children }) {
  const { UserLoginApi } = UseTestApi();
  const [user, setUser] = useState(null);

  const login = async (userData) => {
    const data = await UserLoginApi(userData);
    if (!data) {
      setUser(null);
      return null;
    } else {
      // setUser({employee_id:userData.employee_id, });
      const { Data } = data;
      const sessionData = {
        employee_id: Data[0].Employee_Id,
        name: Data[0].Name,
      };
      setUser(sessionData);
      return sessionData;
    }
  };

  const logout = () => {
    setUser(null);
  };

  return (
    <UserContext.Provider value={{ user, login, logout }}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  return useContext(UserContext);
}
