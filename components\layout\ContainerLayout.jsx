import { useRouter } from "next/router";
import React, { useState, useEffect, useContext } from "react";

// React Suite
import { Container, Sidebar, Sidenav, Content, Navbar, Nav,  IconButton,
  <PERSON>Stack,
  Stack, } from "rsuite";
import DashboardIcon from "@rsuite/icons/Dashboard";
import ListIcon from '@rsuite/icons/List';
import Header from "@/components/layout/Header";

import * as faIcon from "@fortawesome/free-solid-svg-icons";
import {
  MdKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight
} from 'react-icons/md';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

// React Suite
const headerStyles = {
  padding: 18,
  fontSize: 16,
  color: " #fff",
  whiteSpace: "nowrap",
  overflow: "hidden",
};

function ContainerLayout(props) {
  const router = useRouter();
  const [parentMenu, setParentMenu] = useState([]);
  const [childMenu, setChildMenu] = useState([]);
  const [menuIcon, setMenuIcon] = useState("");
  const [expand, setExpand] = React.useState(true);

  const navItemHandler = (route) => {
    router.push(`/${route}`);
  };

  useEffect(() => {
    let setSidebarData = true;

    if (setSidebarData) {
      const parentMenuData = JSON.parse(localStorage.getItem("parentMenu"));
      const childMenuData = JSON.parse(localStorage.getItem("childMenu"));
      const menuIconData = localStorage.getItem("menuIcon");

      setParentMenu(parentMenuData);
      // console.log("parent menus", parentMenuData);
      setChildMenu(childMenuData);
      // console.log("child menus", childMenuData);
      setMenuIcon(menuIconData);
      // console.log("menu icon", menuIconData);
    }

    return () => (setSidebarData = false);
  }, []);

  const NavToggle = ({ expand, onChange }) => {
    return (
      <Stack className="nav-toggle" justifyContent={expand ? 'flex-end' : 'center'}>
        <IconButton
          onClick={onChange}
          appearance="subtle"
          size="lg"
          icon={expand ? <MdKeyboardArrowLeft /> : <MdOutlineKeyboardArrowRight />}
        />
      </Stack>
    );
  };

  return (
    <>
      {/* <div className="sidebar-page h-screen"> */}
      <div className="sidebar-page ">
        <Container>
          <Sidebar
            className="shadow-sm"
            style={{ display: "flex", flexDirection: "column" }}
            width={expand ? 260 : 56}
            collapsible
          >
            <Sidenav.Header>
              <div style={headerStyles}>
                <img src="/Logo_kalbe_detail.png" width={150} />
              </div>
            </Sidenav.Header>
            <NavToggle expand={expand} onChange={() => setExpand(!expand)} />
            <Sidenav
              expanded={expand}
              defaultOpenKeys={["3"]}
              appearance="subtle"
            >
              <Sidenav.Body>
                <Nav>
                  <Nav.Item
                    onClick={() => router.push("/dashboard")}
                    eventKey="1"
                    active
                    icon={<DashboardIcon />}
                    style={{ borderBottom: "0.1em solid lightgrey" }}
                  >
                    Dashboard
                  </Nav.Item>
                  {props?.customNavMenu &&
                    props.customNavMenu.map((item) => (
                      <Nav.Item
                        onClick={navItemHandler.bind(null, `${item.route}`)}
                      >
                        {/* <FontAwesomeIcon
                          icon={faIcon[`${item.icon}`]}
                          style={{ fontSize: 15 }}
                        /> */}
                        icon={<ListIcon />}
                        {item.name}
                      </Nav.Item>
                    ))}

                  {parentMenu &&
                    parentMenu.length > 0 &&
                    props.title === "User Module" &&
                    parentMenu.map((itemParent) => {
                      return (
                        <Nav.Menu
                          trigger="hover"
                          key={itemParent.Id_Menu}
                          title={`${itemParent.Menu_Name}`}
                          placement="rightStart"
                          icon={<ListIcon />}
                          style={{ borderBottom: ".2px solid lightgrey" }}
                        >
                          {itemParent.Child_menu &&
                            itemParent.Child_menu.map((itemChild) => {
                              //if (itemChild.section === itemParent.section) {
                                return (
                                  <Nav.Item
                                    key={itemChild.Id_Menu}
                                    onClick={() =>
                                      router.push({
                                        pathname: `/user_module/${itemChild.Menu_Link_Code}`,
                                      })
                                    }
                                    style={{
                                      borderTop: ".2px solid lightgrey",
                                    }}
                                  >
                                    {itemChild.Menu_Name}
                                  </Nav.Item>
                                );
                              //}
                            })}
                        </Nav.Menu>
                      );
                    })}
                </Nav>
              </Sidenav.Body>
            </Sidenav>
          </Sidebar>

          {/* <Container className="h-screen"> */}
          <Container>
            <Header title={props.title} />
            <Content
              style={{ ...props?.contentStyle }}
              className="overflow-auto"
            >
              {props?.children}
            </Content>
          </Container>
        </Container>
      </div>
    </>
  );
}

export default ContainerLayout;
