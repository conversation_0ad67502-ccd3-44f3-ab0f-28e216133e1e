import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import MainContent from "@/components/layout/MainContent";
import MenuApi from "../api/menuApi";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Head from "next/head";
import {
  Stack,
  Table,
  Panel,
  Pagination,
  IconButton,
  InputGroup,
  Input,
} from "rsuite";
import { paginate } from "@/utils/paginate";
import EditIcon from "@rsuite/icons/Edit";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";

function MenuManagement({ allMenu }) {
  const [menuData, setMenuData] = useState([]);
  const { HeaderCell, Cell, Column } = Table;
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const router = useRouter();
  const [searchKeyword, setSearchKeyword] = useState("");

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const filteredData = menuData.filter((item) => {
    const searchFields = [
      "Menu_Name", 
      "Menu_Link", 
      "Module_Name"];
    const keyword = searchKeyword.trim().toLowerCase();
  
    return searchFields.some((field) => 
      item[field]?.toLowerCase().includes(keyword)
    );
  });
  
  const datas = paginate(filteredData, page, limit);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  }, []);

  useEffect(() => {
    if (allMenu) {
      setMenuData(allMenu);
    }
  }, [allMenu]);

  return (
    <>
      <Head>
        <title>Menu Management</title>
      </Head>
      <ContainerLayout
        title="Menu Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <div
            className="mb-2"
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <IconButton
              icon={<PlusIcon />}
              appearance="primary"
              onClick={() => router.push("/menu_management/addMenu")}
            >
              Add
            </IconButton>

            <div style={{ marginLeft: "auto" }}>
              <InputGroup inside>
                <InputGroup.Addon>
                  <SearchIcon />
                </InputGroup.Addon>
                <Input
                  placeholder="Search"
                  value={searchKeyword}
                  onChange={handleSearch}
                />
                <InputGroup.Addon
                  onClick={() => {
                    setSearchKeyword("");
                    setPage(1);
                  }}
                  style={{
                    display: searchKeyword ? "block" : "none",
                    color: "red",
                    cursor: "pointer",
                  }}
                >
                  <CloseOutlineIcon />
                </InputGroup.Addon>
              </InputGroup>
            </div>
          </div>

          {filteredData.length > 0 ? (
            <Panel>
              <Table
                bordered
                cellBordered
                height={400}
                data={datas}
                onRowClick={(rowData) => {
                  console.log(rowData);
                }}
                sortColumn={sortColumn}
                sortType={sortType}
                onSortColumn={handleSortColumn}
              >
                <Column width={60} align="center" fixed>
                  <HeaderCell>No</HeaderCell>
                  <Cell>
                    {(rowData, rowIndex) => {
                      return rowIndex + 1 + limit * (page - 1);
                    }}
                  </Cell>
                </Column>

                <Column width={300}>
                  <HeaderCell>Menu Name</HeaderCell>
                  <Cell dataKey="Menu_Name" />
                </Column>

                <Column width={300}>
                  <HeaderCell>Menu Link</HeaderCell>
                  <Cell dataKey="Menu_Link" />
                </Column>

                <Column width={200}>
                  <HeaderCell>Module Name</HeaderCell>
                  <Cell dataKey="Module_Name" />
                </Column>

                <Column width={150}>
                  <HeaderCell>Created At</HeaderCell>
                  <Cell>
                    {(rowData) => rowData.Created_At.substring(0, 10)}
                  </Cell>
                </Column>

                <Column width={150}>
                  <HeaderCell>Role</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.Is_Parent == 1) {
                        return "Parent Menu";
                      } else {
                        return "Child Menu";
                      }
                    }}
                  </Cell>
                </Column>

                <Column width={150}>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      if (rowData.Is_Active == 1) {
                        return (
                          <div className="flex items-center">
                            <div className="h-2.5 w-2.5 rounded-full bg-green-500 mr-2"></div>{" "}
                            Active
                          </div>
                        );
                      } else {
                        return (
                          <div className="flex items-center">
                            <div className="h-2.5 w-2.5 rounded-full bg-red-500 mr-2"></div>{" "}
                            Inactive
                          </div>
                        );
                      }
                    }}
                  </Cell>
                </Column>

                <Column width={150} fixed="right">
                  <HeaderCell>...</HeaderCell>

                  <Cell style={{ padding: "6px" }}>
                    {(rowData) => (
                      <Stack direction="row" justifyContent="center">
                        {
                          <IconButton
                            size="md"
                            icon={<EditIcon />}
                            title="Edit"
                            onClick={() => {
                              router.push({
                                pathname: "/menu_management/editMenu",
                                query: { dataIdMenu: `${rowData.Id_Menu}` },
                              });
                            }}
                          />
                        }
                      </Stack>
                    )}
                  </Cell>
                </Column>
              </Table>
              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager"]}
                  total={filteredData.length}
                  limitOptions={[10, 30, 50]}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
          ) : (
            <h1>No Menus Found.</h1>
          )}
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps() {
  const { GetMenuAll } = MenuApi();

  const { Data: allMenuData } = await GetMenuAll();
  let allMenu = [];
  if (allMenuData != undefined) {
    allMenu = allMenuData;
  }

  return {
    props: {
      allMenu,
    },
  };
}

export default MenuManagement;
