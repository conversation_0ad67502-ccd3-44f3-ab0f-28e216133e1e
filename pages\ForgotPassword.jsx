import UseTestApi from "./api/userApi";
import { useState } from "react";
import styles from "../components/LoginForm.module.css";
import { useRouter } from "next/router";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";

export default function ForgotPassword() {
  const router = useRouter();
  const { ForgotPasswordApi } = UseTestApi();
  const MySwal = withReactContent(Swal);
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const [employeeIdValue, setEmployeeIdValue] = useState("");

  const submitHandler = async (event) => {
    event.preventDefault();
    setButtonDisabled(true);

    const reqData = {
      employee_id: employeeIdValue,
    };

    const sendRequest = await ForgotPasswordApi(reqData);

    router.push("/");
    MySwal.fire({
      position: "center",
      icon: "success",
      title: "Success !",
      text: "We've sent you an email to reset your password !",
      allowOutsideClick: false,
      showConfirmButton: false,
      timer: 5000,
    });
  };

  return (
    <>
      <div className="container-fluid d-flex align-items-center justify-content-center h-100 mb-5">
        <div className={`card mt-5 p-5 shadow-lg ${styles.customCardWidth}`}>
          <div className="text-center card-title">
            <h3>Forgot Password</h3>
          </div>
          <form className="p-5" onSubmit={submitHandler}>
            <div className="mb-3 has-validation">
              <label
                htmlFor="exampleInputEmployeeID"
                className="form-label"
                style={{ fontStyle: "italic" }}
              >
                Enter your Employee ID
              </label>
              <input
                type="text"
                className="form-control"
                id="exampleInputEmployeeID"
                aria-describedby="emailHelp"
                value={employeeIdValue}
                onChange={() => setEmployeeIdValue(event.target.value)}
                autoComplete="off"
                autoFocus={true}
                required
              />
            </div>

            <div className="d-grid gap-2">
              <button
                type="submit"
                disabled={buttonDisabled}
                className="btn btn-primary"
              >
                Reset Password
              </button>
            </div>
            <br />
            <button className="btn btn-link" onClick={() => router.push("/")}>
              Back to Login
            </button>
          </form>
        </div>
      </div>
    </>
  );
}
