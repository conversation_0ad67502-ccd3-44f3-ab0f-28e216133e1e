import axios from 'axios';

export default function TsApproval() {
  const GetAllAutoSpv = async (inputData) => {
    let data = [];
    console.log('[inputdata]', inputData)

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-dashboard/auto/spv/needapprove`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };


  const GetAllAutoMgr = async (inputData) => {
    let data = [];

    await axios
      .get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-dashboard/auto/mgr/needapprove`,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetAllManualSpv = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-dashboard/manual/spv/needapprove`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };


  const GetAllManualMgr = async (inputData) => {
    let data = [];

    await axios
      .get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-dashboard/manual/mgr/needapprove`,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };


  const PostApproveManualSpv = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-header-trans1/spv/approve`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostApproveManualMgr = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-header-trans1/mgr/approve`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostApproveAutoSpv = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-header-trans/spv/approve`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostApproveAutoMgr = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-header-trans/mgr/approve`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostOnHoldManualSpv = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-header-trans1/spv/onhold`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostOnHoldManualMgr = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-header-trans1/mgr/onhold`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostOnHoldAutoSpv = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-header-trans/spv/onhold`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostOnHoldAutoMgr = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-header-trans/mgr/onhold`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };


  return {
    GetAllAutoSpv,
    GetAllAutoMgr,
    GetAllManualMgr,
    GetAllManualSpv, 
    PostApproveManualSpv,
    PostApproveManualMgr,
    PostApproveAutoSpv,
    PostApproveAutoMgr,
    PostOnHoldManualSpv,
    PostOnHoldManualMgr,
    PostOnHoldAutoSpv,
    PostOnHoldAutoMgr,
  };
}