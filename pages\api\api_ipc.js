import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  try {
    const response = await axios[method](
      `${process.env.NEXT_PUBLIC_BASE_URL}/v1/ipc/${url}`,
      data
    );
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default function IPC_API() {
  return {
    getIpcReportExcel: createApiFunction("post", "get-report-excel"),
    getIpcDashboard: createApiFunction("get", "get-report-dashboard"),
    getIpcReason: createApiFunction("get", "get-report-reason"),
    getIpcUserDashboard: createApiFunction("post", "report-ipc-user"),
  };
}
