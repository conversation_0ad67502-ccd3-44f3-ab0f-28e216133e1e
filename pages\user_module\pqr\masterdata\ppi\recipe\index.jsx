import { useEffect, useState, useRef } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  PanelGroup,
  Placeholder,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  useToaster,
  Notification,
  ButtonGroup,
  Loader,
  DatePicker,
  RadioGroup,
  Radio,
  SelectPicker,
  FlexboxGrid,
  Grid,
  Col,
  InputNumber,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";

import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import CheckRoundIcon from '@rsuite/icons/CheckRound';
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";

// Import API
import ApiStep from "@/pages/api/pqr/step/api_masterdata_step";
import ApiPQR from "@/pages/api/pqr/parameter/api_PQR";
import ApiRecipe from "@/pages/api/pqr/recipe/api_recipe";
import ApiMasterdata_ppi from "@/pages/api/pqr/ppi/api_masterdata_ppi";
import { useRouter } from "next/router";

export default function Recipe() {
  const { HeaderCell, Cell, Column } = Table;
  const router = useRouter();
  const { Id } = router.query;
  const toaster = useToaster();

  // State Management
  const [loading, setLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("order_no");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [stepsDataState, setStepsDataState] = useState([]);
  const [ppiDataState, setPpiDataState] = useState([]);
  const [prmDataState, setprmDataState] = useState([]);

  const [showConfirmDeleteModal, setShowConfirmDeleteModal] = useState(false);
  const [showRemarksModal, setShowRemarksModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [recipeStepToDelete, setRecipeStepToDelete] = useState(null);
  const [parameterToDelete, setParameterToDelete] = useState(null);
  const [showConfirmDeleteParameterModal, setShowConfirmDeleteParameterModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedParameterDetail, setSelectedParameterDetail] = useState(null);

  const [bindingParamPpiDataState, setBindingParamPpiDataState] = useState([]);
  const [selectedStepName, setSelectedStepName] = useState("");
  const [idRouter, setIdRouter] = useState(null);
  const [password, setPassword] = useState("");
  const [remarks, setRemarks] = useState("");
  const [remarksError, setRemarksError] = useState(false);

  const [formData, setFormData] = useState({
    id_ppi: null,
    ppi_name: null,
    created_date: "",
    created_by: "",
    updated_date: "",
    updated_by: "",
    deleted_date: "",
    deleted_by: "",
    wetmill:null,
  });

  // Form States
  const emptyAddRecipeForm = {
    id_step: null,
    order_no: null,
    is_active: 1,
  };

  const emptyAddParameterForm = {
    id_parameter: null,
    binding_type: null,
    min_value: null,
    max_value: null,
    absolute_value: null,
    description_value: null,
    wetmill: "N",
    set_point_flag: null,
    set_point_value: "",
    is_actual: null,
    created_by: null,
  };

  const emptyEditRecipeForm = {
    id_step: null,
    order_no: null,
    is_active: 1,
  };

  const emptyEditParameterForm = {
    id_parameter: null,
    order_no: null,
    wetmill: null,
    set_point_flag: null,
    set_point_value: "",
    is_actual: null,
    binding_type: null,
    min_value: null,
    max_value: null,
    absolute_value: null,
    description_value: "",
    update_by: "",
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [addRecipeForm, setAddRecipeForm] = useState(emptyAddRecipeForm);
  const [addParameterForm, setAddParameterForm] = useState(emptyAddParameterForm);
  const [editRecipeForm, setEditRecipeForm] = useState(emptyEditRecipeForm);
  const [editParameterForm, setEditParameterForm] = useState(emptyEditParameterForm);
  const [showAddParameterModal, setShowAddParameterModal] = useState(false);
  const [showEditParameterModal, setShowEditParameterModal] = useState(false);

  // Error States
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});
  const [errorsAddParameterForm, setErrorsAddParameterForm] = useState({});
  const [errorsEditParameterForm, setErrorsEditParameterForm] = useState({});

  // Detail Panel States
  const [showDetailPanel, setShowDetailPanel] = useState(false);
  const [newRecipeId, setNewRecipeId] = useState(null);
  const [currentRecipeStepId, setCurrentRecipeStepId] = useState(null);
  const [selectedRecipeDetail, setSelectedRecipeDetail] = useState(null);
  const [addLoading, setAddLoading] = useState(false);
  const [selectedParameterName, setSelectedParameterName] = useState("");
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(false);
  const [recipeSteps, setRecipeSteps] = useState(bindingParamPpiDataState);
  const [updatedSelectedRecipeDetail, setUpdatedSelectedRecipeDetail] = useState(selectedRecipeDetail);

  const detailPanelRef = useRef(null);

  const scrollToDetailPanel = () => {
    if (detailPanelRef.current) {
      detailPanelRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  };



  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };
  const handleShowDetailModal = (parameter) => {
    setSelectedParameterDetail(parameter);
    setSelectedParameterName(parameter.paramater_name);
    setShowDetailModal(true);
  };

  const filteredData = bindingParamPpiDataState.filter((rowData) => {
    const searchFields = ["id_step", "step_name", "is_active"];
    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));
    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : bindingParamPpiDataState.length;

  const orderNoCounts = bindingParamPpiDataState.reduce((acc, row) => {
    const key = row.order_no;
    if (key !== null && key !== undefined) {
      acc[key] = (acc[key] || 0) + 1;
    }
    return acc;
  }, {});

  const parameterYesCounts =
    updatedSelectedRecipeDetail?.recipe_parameter_y?.reduce((acc, row) => {
      const key = row.order_no;
      if (key !== null && key !== undefined) {
        acc[key] = (acc[key] || 0) + 1;
      }
      return acc;
    }, {}) || {};

  const parameterNoCounts =
    updatedSelectedRecipeDetail?.recipe_parameter_n?.reduce((acc, row) => {
      const key = row.order_no;
      if (key !== null && key !== undefined) {
        acc[key] = (acc[key] || 0) + 1;
      }
      return acc;
    }, {}) || {};


  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push("/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("pqr/"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      setIdRouter(Id);
      HandleGetAllStepApi();
      HandleGetAllBindingParamPpiApi(Id);
      HandleGetDetailPPI(Id);
      HandleGetAllPQRApi();
    }
  }, [Id]);

  useEffect(() => {
    console.log("bindingParamPpiDataState", bindingParamPpiDataState)

    if (!bindingParamPpiDataState || bindingParamPpiDataState.length === 0) {
      console.log("bindingParamPpiDataState error")
      setIsSubmitDisabled(true);
      return;
    } else {
      setIsSubmitDisabled(false);

      return;
    }

    //   const hasInvalidStep = bindingParamPpiDataState.some((step) => 
    //   (step.recipe_parameter_y == null || step.recipe_parameter_y.length === 0) &&
    //   (step.recipe_parameter_n == null || step.recipe_parameter_n.length === 0)
    // );


    // console.log("bindingParamPpiDataState hasInvalid",hasInvalidStep)

    // setIsSubmitDisabled(hasInvalidStep);

  }, [bindingParamPpiDataState]);

  useEffect(() => {
    setRecipeSteps([...bindingParamPpiDataState]);
  }, [bindingParamPpiDataState]);


  useEffect(() => {
    if (bindingParamPpiDataState.length > 0 && selectedRecipeDetail) {
      const updatedDetail = bindingParamPpiDataState.find(
        (step) => step.id_recipe_step === selectedRecipeDetail.id_recipe_step
      );

      if (updatedDetail) {
        setUpdatedSelectedRecipeDetail({ ...updatedDetail });
      }
    }
  }, [bindingParamPpiDataState, selectedRecipeDetail]);



  const HandleGetAllBindingParamPpiApi = async (id_ppi) => {
    try {
      const res = await ApiRecipe().getAllRecipeByIdPPI({ id_ppi: parseInt(id_ppi) });

      console.log("res", res);
      if (res.status === 200) {
        setBindingParamPpiDataState([...res.data.recipe_step]);
        console.log("Updated Data State:", res.data.recipe_step);
      } else {
        console.log("error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Api", error);
    }
  };



  const HandleGetAllPQRApi = async () => {
    try {
      const res = await ApiPQR().getAllActivePQR();
      if (res.status === 200) {
        const options = res.data.map((prm) => ({
          label: prm.parameter_name,
          value: prm.id_parameter,
        }));
        console.log("options ", options)
        setprmDataState(options);
      } else {
        console.log("Error fetching Parameter options", res.message);
      }
    } catch (error) {
      console.log("Error fetching Parameter options", error);
    }
  };

  const HandleGetDetailPPI = async (id_ppi) => {
    try {
      const api = ApiMasterdata_ppi();
      const response = await api.GetMasterPPIById({ id_ppi: parseInt(id_ppi) });

      if (response.status === 200) {
        const data = response.data;
        setFormData({
          id_ppi: data.id_ppi,
          ppi_name: data.ppi_name,
          product_code: data.product_code,
          created_date: new Date(data.created_date).toLocaleDateString("en-GB"),
          created_by: data.created_by,
          updated_date: data.updated_date ? new Date(data.updated_date).toLocaleDateString("en-GB") : "-",
          updated_by: data.updated_by || "-",
          deleted_date: data.deleted_date ? new Date(data.deleted_date).toLocaleDateString("en-GB") : "-",
          deleted_by: data.deleted_by || "-",
          wetmill: data.wetmill,
        });
      } else {
        console.error("Failed to fetch detail data");
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
    }
  };

  const HandleGetAllStepApi = async () => {
    try {
      const res = await ApiStep().getAllActiveStep();
      if (res.status === 200) {
        const options = res.data.map((step) => ({
          label: step.step_name,
          value: step.id_step,
        }));
        setStepsDataState(options);
      } else {
        console.log("Error on GetAllStepApi: ", res.message);
      }
    } catch (error) {
      console.log("Error on catch GetAllStepApi: ", error.message);
    }
  };

  const HandleAddStepApi = async () => {
    const errors = {};
    if (!addRecipeForm.id_step) {
      errors.id_step = "Nama Step wajib Diisi";
    }
    if (!addRecipeForm.order_no) {
      errors.order_no = "Order No wajib Diisi";
    }

    const isDuplicate = bindingParamPpiDataState.some((recipe) => recipe.id_step === addRecipeForm.id_step);

    if (isDuplicate) {
      errors.id_step = "Step dengan nama ini sudah ada";
      showNotification("error", "Step dengan nama ini sudah ada");
    }

    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }

    try {
      setIsLoading(true);
      const res = await ApiRecipe().createRecipe({
        ...addRecipeForm,
        id_ppi: parseInt(idRouter),
        create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        showNotification("success", "Recipe step Berhasil Dibuat.");
        await HandleGetAllBindingParamPpiApi(Id);
        setShowAddModal(false);
        setShowDetailPanel(false);
        setSelectedRecipeDetail(null);
        const newRecipeId = res.data.id_recipe_step;
        setNewRecipeId(newRecipeId);
        setAddRecipeForm(emptyAddRecipeForm);
      } else {
        console.log("Error on AddStepApi: ", res.message);
      }
    } catch (error) {
      console.log("Error on AddStepApi: ", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const HandleAddRecipeParameterApi = async () => {
    const errors = {};

    if (!addParameterForm.id_parameter) {
      errors.id_parameter = "Nama Step wajib Diisi";
    }
    if (!addParameterForm.order_no) {
      errors.order_no = "Order No wajib Diisi";
    }

    if (addParameterForm.wetmill === null) {
      errors.wetmill = "Wetmill wajib dipilih";
    }
    if (addParameterForm.set_point_flag === null) {
      errors.set_point_flag = "Set Point wajib dipilih";
    }

    if (addParameterForm.set_point_flag === 1) {
      if (!addParameterForm.set_point_value) {
        errors.set_point_value = "Set Point Value wajib Diisi";
      }
    } else if (addParameterForm.set_point_flag === 0) {
      if (addParameterForm.is_actual === null) {
        errors.is_actual = "Actual wajib dipilih";
      }
      if (!addParameterForm.binding_type) {
        errors.binding_type = "Tipe Binding wajib dipilih";
      }

      if (addParameterForm.binding_type === 1) {
        if (addParameterForm.min_value === null) {
          errors.min_value = "Nilai Min wajib Diisi";
        }
        if (addParameterForm.max_value === null) {
          errors.max_value = "Nilai Max wajib Diisi";
        }
        if (addParameterForm.min_value > addParameterForm.max_value) {
          errors.min_value = "Nilai Min tidak boleh lebih besar dari Max Value";
        }
      }

      if (addParameterForm.binding_type === 2 && addParameterForm.absolute_value === null) {
        errors.absolute_value = "Nilai wajib Diisi";
      }

      if (addParameterForm.binding_type === 3 && !addParameterForm.description_value) {
        errors.description_value = "Nilai Deskripsi wajib Diisi";
      }
    }

    if (Object.keys(errors).length > 0) {
      setErrorsAddParameterForm(errors);
      return;
    }

    try {
      setIsLoading(true);
      const res = await ApiRecipe().createParameter({
        ...addParameterForm,
        id_recipe_step: currentRecipeStepId,
        created_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        await HandleGetAllBindingParamPpiApi(Id);
        const updatedRecipeSteps = await ApiRecipe().getAllRecipeByIdPPI({ id_ppi: parseInt(Id) });
        const updatedDetail = updatedRecipeSteps.data.recipe_step.find((step) => step.id_recipe_step === currentRecipeStepId);
        setSelectedRecipeDetail(updatedDetail);
        showNotification("success", "Parameter Recipe Berhasil Dibuat.");
        setAddParameterForm({...emptyAddParameterForm, wetmill:formData.wetmill});
        setShowAddParameterModal(false);
      } else if (res.status === 400) {
        showNotification("error", "recipe parameter sudah ada");
      } else {
        console.log("error on GetAllApi ", error);
        showNotification("error", "gagal menambah data");
      }
    } catch (error) {
      console.log("error on GetAllApi ", error);
      showNotification("error", "gagal menambah data");
    } finally {
      setIsLoading(false);
    }
  };

  const HandleEditStepApi = async () => {
    const errors = {};
    if (!editRecipeForm.id_step) {
      errors.id_step = "Nama Step wajib Diisi";
    }
    if (!editRecipeForm.order_no) {
      errors.order_no = "Order No wajib Diisi";
    }


    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);
      return;
    }

    try {
      setIsLoading(true);
      const res = await ApiRecipe().updateRecipe({
        ...editRecipeForm,
        id_recipe_step: editRecipeForm.id_recipe_step,
        id_ppi: parseInt(idRouter),
        update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        showNotification("success", "Recipe Step berhasil diperbarui");
        await HandleGetAllBindingParamPpiApi(Id);
        setBindingParamPpiDataState((prevData) => prevData.map((item) => (item.id_recipe_step === editRecipeForm.id_recipe_step ? { ...item, order_no: editRecipeForm.order_no, id_step: editRecipeForm.id_step } : item)));

        setShowEditModal(false);
      } else {
        console.log("error on Recipe Step ", res.message);
        showNotification("error", "Gagal memperbarui Recipe Step");
      }
    } catch (error) {
      console.log("error on GetAllApi ", error);
      showNotification("error", "Terjadi kesalahan saat memperbarui Recipe Step");
    } finally {
      setIsLoading(false);
    }
  };

  const HandleEditParameterApi = async () => {
    const errors = {};
    if (!editParameterForm.id_parameter) {
      errors.id_parameter = "Nama Parameter wajib diisi";
    }
    if (!editParameterForm.order_no) {
      errors.order_no = "Order No wajib Diisi";
    }

    if (editParameterForm.wetmill === null) {
      errors.wetmill = "Wetmill wajib dipilih";
    }
    if (editParameterForm.set_point_flag === null) {
      errors.set_point_flag = "Set Point wajib dipilih";
    }

    if (editParameterForm.set_point_flag === 1) {
      if (!editParameterForm.set_point_value) {
        errors.set_point_value = "Nilai Set Point wajib Diisi";
      }
    } else if (editParameterForm.set_point_flag === 0) {
      if (editParameterForm.is_actual === null) {
        errors.is_actual = "Actual wajib dipilih";
      }
      if (!editParameterForm.binding_type) {
        errors.binding_type = "Binding Type wajib dipilih";
      }

      if (editParameterForm.binding_type === 1) {
        if (editParameterForm.min_value === null) {
          errors.min_value = "Min Value wajib Diisi";
        }
        if (editParameterForm.max_value === null) {
          errors.max_value = "Max Value wajib Diisi";
        }
        if (editParameterForm.min_value > editParameterForm.max_value) {
          errors.min_value = "Min Value tidak boleh lebih besar dari Max Value";
        }
      }

      if (editParameterForm.binding_type === 2 && editParameterForm.absolute_value === null) {
        errors.absolute_value = "Absolute Value wajib Diisi";
      }

      if (editParameterForm.binding_type === 3 && !editParameterForm.description_value) {
        errors.description_value = "Nilai Description wajib Diisi";
      }
    }

    if (Object.keys(errors).length > 0) {
      setErrorsEditParameterForm(errors);
      return;
    }

    try {
      setIsLoading(true);
      const res = await ApiRecipe().updateParameter({
        ...editParameterForm,
        id_recipe_param: editParameterForm.id_recipe_param,
        id_recipe_step: currentRecipeStepId,
        is_active: 1,
        update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        showNotification("success", "Parameter berhasil diperbarui.");
        await HandleGetAllBindingParamPpiApi(Id);
        const updatedRecipeSteps = await ApiRecipe().getAllRecipeByIdPPI({ id_ppi: parseInt(Id) });
        const updatedDetail = updatedRecipeSteps.data.recipe_step.find((step) => step.id_recipe_step === currentRecipeStepId);
        setSelectedRecipeDetail(updatedDetail);
        setEditParameterForm(emptyEditParameterForm);
        setShowEditParameterModal(false);
      } else if (res.status === 400) {
        showNotification("error", "Gagal memperbarui ,Parameter sudah ada.");
      } else {
        console.log("error on GetAllApi ", res.message)
        showNotification("error", "Gagal memperbarui parameter.");
      }
    } catch (error) {
      console.log("error on GetAllApi ", error)
      showNotification("error", "Terjadi kesalahan saat memperbarui parameter.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditStatusRecipeApi = async (id_recipe_step, is_active) => {
    try {
      const res = await ApiRecipe().editStatusRecipeStep({
        id_recipe_step,
        is_active,
        deleted_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        showNotification("success", "Recipe Step berhasil dihapus");
        setShowEditModal(false);
        setShowDetailPanel(false);
        setSelectedRecipeDetail(null);
        setBindingParamPpiDataState((prevData) => prevData.filter((item) => item.id_recipe_step !== id_recipe_step));
        await HandleGetAllBindingParamPpiApi(Id);
      } else {
        showNotification("error", "Gagal menghapus Recipe Step");
      }
    } catch (error) {
      showNotification("error", "Terjadi kesalahan saat menghapus Recipe Step");
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditStatusParameterApi = async (id_recipe_param, is_active) => {
    try {
      const res = await ApiRecipe().editStatusRecipeParameter({
        id_recipe_param,
        is_active,
        deleted_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        await HandleGetAllBindingParamPpiApi(Id);
        const updatedRecipeSteps = await ApiRecipe().getAllRecipeByIdPPI({ id_ppi: parseInt(Id) });
        const updatedDetail = updatedRecipeSteps.data.recipe_step.find((step) => step.id_recipe_step === currentRecipeStepId);
        setSelectedRecipeDetail(updatedDetail);
        showNotification("success", "Recipe Parameter berhasil dihapus");
        setShowConfirmDeleteParameterModal(false);
      } else {
        showNotification("error", "Gagal menghapus Recipe Parameter");
      }
    } catch (error) {
      showNotification("error", "Terjadi kesalahan saat menghapus Recipe Parameter");
    } finally {
      setIsLoading(false);
    }
  };

  const HandleEditStatusApprovalPPIApi = async (id_ppi, status_approval) => {
    setLoading(true);
    try {
      const res = await ApiMasterdata_ppi().editStatusSubmitPPI({
        id_ppi,
        status_approval,
      });

      if (res.status === 200) {
        showNotification("success", "Success Menambahkan PPI Recipe");
        setShowApprovalModal(false);
        router.push(`/user_module/pqr/masterdata/ppi`);

      } else {
        console.error("Error on update status: ", res.message);

      }
    } catch (error) {
      console.error("Error on update status: ", error.message);
      showNotification("error", `Error Dalam mebambahkan PPI`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Halaman Recipe</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data PQR</Breadcrumb.Item>
                  <Breadcrumb.Item active>Halaman Recipe</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Halaman Recipe</h5>
              </Stack>
            }
          ></Panel>
          {/* Detail Section */}
          <Panel bordered>
            <Form fluid>
              {/* bagian atas */}
              <FlexboxGrid className="mb-4">
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>ID PPI</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.id_ppi || "-"} readOnly />
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Kode Produk</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.product_code || "-"} readOnly />
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Nama PPI</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.ppi_name || "-"} readOnly />
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Wetmill</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.wetmill || "-"} readOnly />
                </FlexboxGrid.Item>
              </FlexboxGrid>
              <FlexboxGrid>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Dibuat Tanggal</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.created_date || "-"} readOnly />
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Diupdate Tanggal</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.updated_date || "-"} readOnly />
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                <Form.ControlLabel style={{ fontWeight: "bold" }}>Dibuat Oleh </Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.created_by || "-"} readOnly />
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Diupdate Oleh </Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.updated_by || "-"} readOnly />
                </FlexboxGrid.Item>
                </FlexboxGrid>
                <FlexboxGrid>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Dihapus Tanggal</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.deleted_date || "-"} readOnly />
                </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Dihapus Oleh</Form.ControlLabel>
                    <Form.Control className="mb-3" name="id_ppi" value={formData.deleted_by || "-"} readOnly />
                </FlexboxGrid.Item>
              </FlexboxGrid>
            </Form>
        </Panel>

          {/* datatable recipe step section */}
          <Panel
            bordered
            bodyFill
            className="mt-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Recipe Step</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between" alignItems="center">
                  <div className="flex gap-2">
                    <InputGroup inside>
                      <InputGroup.Addon>
                        <SearchIcon />
                      </InputGroup.Addon>
                      <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                      <InputGroup.Addon
                        onClick={() => {
                          setSearchKeyword("");
                          setPage(1);
                        }}
                        style={{
                          display: searchKeyword ? "block" : "none",
                          color: "red",
                          cursor: "pointer",
                        }}
                      >
                        <CloseOutlineIcon />
                      </InputGroup.Addon>
                    </InputGroup>
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>



                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(bindingParamPpiDataState), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                <Column width={120} align="center" sortable resizable>
                  <HeaderCell>ID Recipe Step</HeaderCell>
                  <Cell dataKey="id_recipe_step" />
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Nama Step</HeaderCell>
                  <Cell dataKey="step_name" />
                </Column>
                <Column width={100} align="center" sortable resizable>
                  <HeaderCell>Order No</HeaderCell>
                  <Cell dataKey="order_no">
                    {(rowData) => {
                      const isDuplicate = orderNoCounts[rowData.order_no] > 1;
                      return (
                        <span style={{ color: isDuplicate ? "red" : "inherit" }}>
                          {rowData.order_no}
                        </span>
                      );
                    }}
                  </Cell>
                </Column>
                <Column width={150} sortable align="center" resizable>
                  <HeaderCell>Tanggal Pembuatan</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.created_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Dibuat Oleh</HeaderCell>
                  <Cell dataKey="created_by" />
                </Column>
                <Column width={150} sortable align="center" resizable>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.updated_date ? new Date(rowData.updated_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => rowData.updated_by || "-"}</Cell>
                </Column>
                <Column width={150} sortable align="center" resizable>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>{(rowData) => (rowData.deleted_date ? new Date(rowData.deleted_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => rowData.deleted_by || "-"}</Cell>
                </Column>
                <Column width={100} sortable align="center" resizable>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModal(true);
                            setEditRecipeForm({
                              ...editRecipeForm,
                              id_recipe_step: rowData.id_recipe_step,
                              order_no: rowData.order_no,
                              id_step: rowData.id_step,
                              update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                            });
                          }}
                        >
                          <EditIcon />
                        </Button>

                        <Button
                          appearance="subtle"
                          onClick={() => {
                            setSelectedRecipeDetail(rowData);
                            setSelectedStepName(rowData.step_name);
                            setShowDetailPanel(true);
                            setAddParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              id_recipe_step: rowData.id_recipe_step,
                              wetmill: formData.wetmill
                            }));
                            setCurrentRecipeStepId(rowData.id_recipe_step);
                            setTimeout(scrollToDetailPanel, 100);
                          }}
                        >
                          <SearchIcon />
                        </Button>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            setRecipeStepToDelete(rowData.id_recipe_step);
                            setShowConfirmDeleteModal(true);
                          }}
                        >
                          <TrashIcon style={{ fontSize: "16px" }} />
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>

            {/* detail parameter */}
            {showDetailPanel && selectedRecipeDetail && (
              <Panel
                className="mt-3"
                ref={detailPanelRef}
                bordered
                header={
                  <Stack justifyContent="space-between">
                    <h5>Rincian Parameter Recipe Step : {selectedStepName}</h5>

                    <IconButton
                      icon={<CloseOutlineIcon style={{ color: "red" }} />}
                      appearance="subtle"
                      onClick={() => {
                        setShowDetailPanel(false);
                        setSelectedRecipeDetail(null);
                        setSelectedStepName("");
                        setCurrentRecipeStepId("");
                      }}
                    />
                  </Stack>
                }
              >
                <IconButton
                  icon={<PlusRoundIcon />}
                  appearance="primary"
                  onClick={() => {
                    setShowAddParameterModal(true);
                  }}
                >
                  Tambah
                </IconButton>
                <div className="mt-3">
                  <FlexboxGrid>
                    {formData.wetmill === "Y"? (
                       <FlexboxGrid.Item as={Col} colspan={24} lg={24} md={24} sm={24} xs={24}>
                       <Panel bordered className="mt-3">
                         <h5>Parameter</h5>
                       </Panel>
 
                       <Panel
                         ref={detailPanelRef}
                         bordered
                         bodyFill
                         header={
                           <Stack justifyContent="space-between">
                             <InputGroup inside>
                               <InputGroup.Addon>
                                 <SearchIcon />
                               </InputGroup.Addon>
                               <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                               <InputGroup.Addon
                                 onClick={() => {
                                   setSearchKeyword("");
                                   setPage(1);
                                 }}
                                 style={{
                                   display: searchKeyword ? "block" : "none",
                                   color: "red",
                                   cursor: "pointer",
                                 }}
                               >
                                 <CloseOutlineIcon />
                               </InputGroup.Addon>
                             </InputGroup>
                           </Stack>
                         }
                       >
                         {updatedSelectedRecipeDetail?.recipe_parameter_y?.length > 0 ? (
                           <Table
 
                             bordered
                             cellBordered
                             height={400}
                             data={updatedSelectedRecipeDetail.recipe_parameter_y}
                             sortColumn={sortColumn}
                             sortType={sortType}
                             onSortColumn={handleSortColumn}>
                             <Column width={200} sortable resizable>
                               <HeaderCell>Nama Parameter</HeaderCell>
                               <Cell dataKey="paramater_name" />
                             </Column>
                             <Column width={100} sortable resizable>
                               <HeaderCell>Order No</HeaderCell>
                               <Cell dataKey="order_no">
                                 {(rowData) => {
                                   const isDuplicate = parameterYesCounts[rowData.order_no] > 1;
                                   return (
                                     <span style={{ color: isDuplicate ? "red" : "inherit" }}>
                                       {rowData.order_no}
                                     </span>
                                   );
                                 }}
                               </Cell>
                             </Column>
 
                             <Column width={100} resizable>
                               <HeaderCell>Flag</HeaderCell>
                               <Cell>
                                 {(rowData) => {
                                   if (rowData.set_point_flag) {
                                     return <span>Set Point</span>;
                                   } else if (rowData.is_actual) {
                                     return <span>data connectivity</span>;
                                   } else {
                                     return <span>Not data connectivity</span>;
                                   }
                                 }}
                               </Cell>
                             </Column>
 
                             <Column width={100} resizable>
                               <HeaderCell>Tipe</HeaderCell>
                               <Cell>
                                 {(rowData) => {
                                   if (rowData.binding_type === 1) {
                                     return <span>Range</span>;
                                   } else if (rowData.binding_type === 2) {
                                     return <span>Absolute</span>;
                                   } else if (rowData.binding_type === 3) {
                                     return <span>Description</span>;
                                   }
                                   return <span>-</span>;
                                 }}
                               </Cell>
                             </Column>
 
                             <Column width={250} resizable>
                               <HeaderCell>Nilai</HeaderCell>
                               <Cell>
                                 {(rowData) => {
                                   if (rowData.set_point_flag) {
                                     return <span>{rowData.set_point_value}</span>;
                                   } else if (rowData.binding_type === 1) {
                                     return (
                                       <span>
                                         {rowData.min_value} - {rowData.max_value}
                                       </span>
                                     );
                                   } else if (rowData.binding_type === 2) {
                                     return <span>{rowData.absolute_value}</span>;
                                   } else if (rowData.binding_type === 3) {
                                     return <span>{rowData.description_value}</span>;
                                   }
                                   return <span>-</span>;
                                 }}
                               </Cell>
                             </Column>
 
                             <Column width={100} resizable>
                               <HeaderCell>Status</HeaderCell>
                               <Cell>
                                 {(rowData) => (
                                   <span
                                     style={{
                                       color: rowData.is_active === 1 ? "green" : "red",
                                     }}
                                   >
                                     {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                                   </span>
                                 )}
                               </Cell>
                             </Column>
                             <Column width={120} fixed="right" align="center">
                               <HeaderCell>Aksi</HeaderCell>
                               <Cell style={{ padding: "8px" }}>
                                 {(rowData) => (
                                   <div>
                                     <Button
                                       appearance="subtle"
                                       disabled={rowData.is_active === 0}
                                       onClick={() => {
                                         console.log("id paramerter", rowData)
                                         setEditParameterForm({
                                           id_recipe_param: rowData.id_recipe_param,
                                           id_parameter: rowData.id_parameter,
                                           order_no: rowData.order_no,
                                           wetmill: rowData.wetmill,
                                           set_point_flag: rowData.set_point_flag,
                                           set_point_value: rowData.set_point_value,
                                           is_actual: rowData.is_actual,
                                           binding_type: rowData.set_point_flag === 1 ? null : rowData.binding_type,
                                           min_value: rowData.min_value,
                                           max_value: rowData.max_value,
                                           absolute_value: rowData.absolute_value,
                                           description_value: rowData.description_value,
                                         });
                                         setShowEditParameterModal(true);
                                       }}
                                     >
                                       <EditIcon />
                                     </Button>
                                     <Button
                                       appearance="subtle"
                                       onClick={() => {
                                         handleShowDetailModal(rowData);
                                       }}
                                     >
                                       <SearchIcon />
                                     </Button>
 
                                     <Button
                                       appearance="subtle"
                                       onClick={() => {
                                         setParameterToDelete(rowData.id_recipe_param);
                                         setShowConfirmDeleteParameterModal(true);
                                       }}
                                     >
                                       <TrashIcon style={{ fontSize: "16px" }} />
                                     </Button>
                                   </div>
                                 )}
                               </Cell>
                             </Column>
                           </Table>
                         ) : (
                           <div
                             style={{
                               display: "flex",
                               justifyContent: "center",
                               alignItems: "center",
                               height: "100%",
                               fontSize: "16px",
                               color: "gray",
                             }}
                           >
                             Tidak ada Data
                           </div>
                         )}
 
                         <div style={{ padding: 20 }}>
                           <Pagination
                             prev
                             next
                             first
                             last
                             ellipsis
                             boundaryLinks
                             maxButtons={5}
                             size="xs"
                             layout={["total", "-", "limit", "|", "pager", "skip"]}
                             limitOptions={[10, 30, 50]}
                             // total={totalRowCount}
                             limit={limit}
                             activePage={page}
                             onChangePage={setPage}
                             onChangeLimit={handleChangeLimit}
                           />
                         </div>
                       </Panel>
                     </FlexboxGrid.Item>
                    ) : (
                      <FlexboxGrid.Item as={Col} colspan={24} lg={24} md={24} sm={24} xs={24}>
                      <Panel bordered className="mt-3">
                        <h5>Parameter</h5>
                      </Panel>

                      <Panel
                        bordered
                        bodyFill
                        header={
                          <Stack justifyContent="space-between">
                            <InputGroup inside>
                              <InputGroup.Addon>
                                <SearchIcon />
                              </InputGroup.Addon>
                              <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                              <InputGroup.Addon
                                onClick={() => {
                                  setSearchKeyword("");
                                  setPage(1);
                                }}
                                style={{
                                  display: searchKeyword ? "block" : "none",
                                  color: "red",
                                  cursor: "pointer",
                                }}
                              >
                                <CloseOutlineIcon />
                              </InputGroup.Addon>
                            </InputGroup>
                          </Stack>
                        }
                      >
                        {updatedSelectedRecipeDetail?.recipe_parameter_n?.length > 0 ? (
                          <Table
                            bordered
                            cellBordered
                            height={400}
                            data={updatedSelectedRecipeDetail.recipe_parameter_n}
                            sortColumn={sortColumn}
                            sortType={sortType}
                            onSortColumn={handleSortColumn}>
                            <Column width={200} sortable resizable>
                              <HeaderCell align="center">Nama Parameter</HeaderCell>
                              <Cell dataKey="paramater_name" />
                            </Column>
                            <Column width={100} sortable resizable>
                              <HeaderCell>Order No</HeaderCell>
                              <Cell dataKey="order_no">
                                {(rowData) => {
                                  const isDuplicate = parameterNoCounts[rowData.order_no] > 1;
                                  return (
                                    <span style={{ color: isDuplicate ? "red" : "inherit" }}>
                                      {rowData.order_no}
                                    </span>
                                  );
                                }}
                              </Cell>
                            </Column>

                            <Column width={100} resizable>
                              <HeaderCell>Flag</HeaderCell>
                              <Cell>
                                {(rowData) => {
                                  if (rowData.set_point_flag) {
                                    return <span>Set Point</span>;
                                  } else if (rowData.is_actual) {
                                    return <span>data connectivity</span>;
                                  } else {
                                    return <span>Not data connectivity</span>;
                                  }
                                }}
                              </Cell>
                            </Column>

                            <Column width={100} resizable>
                              <HeaderCell>Tipe</HeaderCell>
                              <Cell>
                                {(rowData) => {
                                  if (rowData.binding_type === 1) {
                                    return <span>Range</span>;
                                  } else if (rowData.binding_type === 2) {
                                    return <span>Absolute</span>;
                                  } else if (rowData.binding_type === 3) {
                                    return <span>Description</span>;
                                  }
                                  return <span>-</span>;
                                }}
                              </Cell>
                            </Column>

                            <Column width={250} resizable>
                              <HeaderCell>Nilai</HeaderCell>
                              <Cell>
                                {(rowData) => {
                                  if (rowData.set_point_flag) {
                                    return <span>{rowData.set_point_value}</span>;
                                  } else if (rowData.binding_type === 1) {
                                    return (
                                      <span>
                                        {rowData.min_value} - {rowData.max_value}
                                      </span>
                                    );
                                  } else if (rowData.binding_type === 2) {
                                    return <span>{rowData.absolute_value}</span>;
                                  } else if (rowData.binding_type === 3) {
                                    return <span>{rowData.description_value}</span>;
                                  }
                                  return <span>-</span>;
                                }}
                              </Cell>
                            </Column>

                            <Column width={100} resizable>
                              <HeaderCell align="center">Status</HeaderCell>
                              <Cell>
                                {(rowData) => (
                                  <span
                                    style={{
                                      color: rowData.is_active === 1 ? "green" : "red",
                                    }}
                                  >
                                    {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                                  </span>
                                )}
                              </Cell>
                            </Column>
                            <Column width={120} fixed="right" align="center">
                              <HeaderCell>Aksi</HeaderCell>
                              <Cell style={{ padding: "8px" }}>
                                {(rowData) => (
                                  <div>
                                    <Button
                                      appearance="subtle"
                                      disabled={rowData.is_active === 0}
                                      onClick={() => {
                                        setEditParameterForm({
                                          id_recipe_param: rowData.id_recipe_param,
                                          id_parameter: rowData.id_parameter,
                                          order_no: rowData.order_no,
                                          wetmill: rowData.wetmill,
                                          set_point_flag: rowData.set_point_flag,
                                          set_point_value: rowData.set_point_value,
                                          is_actual: rowData.is_actual,
                                          binding_type: rowData.set_point_flag === 1 ? null : rowData.binding_type,
                                          min_value: rowData.min_value,
                                          max_value: rowData.max_value,
                                          absolute_value: rowData.absolute_value,
                                          description_value: rowData.description_value,
                                        });
                                        setShowEditParameterModal(true);
                                      }}
                                    >
                                      <EditIcon />
                                    </Button>
                                    <Button
                                      appearance="subtle"
                                      onClick={() => {
                                        handleShowDetailModal(rowData);
                                      }}
                                    >
                                      <SearchIcon />
                                    </Button>

                                    <Button
                                      appearance="subtle"
                                      onClick={() => {
                                        setParameterToDelete(rowData.id_recipe_param);
                                        setShowConfirmDeleteParameterModal(true);
                                      }}
                                    >
                                      <TrashIcon style={{ fontSize: "16px" }} />
                                    </Button>
                                  </div>
                                )}
                              </Cell>
                            </Column>
                          </Table>
                        ) : (
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              height: "100%",
                              fontSize: "16px",
                              color: "gray",
                            }}
                          >
                            Tidak ada Data
                          </div>
                        )}

                        <div style={{ padding: 20 }}>
                          <Pagination
                            prev
                            next
                            first
                            last
                            ellipsis
                            boundaryLinks
                            maxButtons={5}
                            size="xs"
                            layout={["total", "-", "limit", "|", "pager", "skip"]}
                            limitOptions={[10, 30, 50]}
                            // total={totalRowCount}
                            limit={limit}
                            activePage={page}
                            onChangePage={setPage}
                            onChangeLimit={handleChangeLimit}
                          />
                        </div>
                      </Panel>
                    </FlexboxGrid.Item>
                    )}
                                     
                  </FlexboxGrid>
                </div>
              </Panel>
            )}

            <Panel className="mt-3" header={
              <Stack justifyContent="flex-end">
                <div>
                  <IconButton
                    icon={<CheckRoundIcon />}
                    appearance="primary"
                    color="green"
                    onClick={() => {
                      setShowApprovalModal(true);
                    }}
                    disabled={isSubmitDisabled}
                  >
                    Kirim Ke Supervisor
                  </IconButton>
                </div>
              </Stack>
            }>
            </Panel>



            {/* modal recipe step */}
            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                setShowAddModal(false);
                setAddRecipeForm(emptyAddRecipeForm);
                setErrorsAddForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Tambah Recipe</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama Step</Form.ControlLabel>
                    <SelectPicker
                      data={stepsDataState}
                      value={addRecipeForm.id_step}
                      onChange={(value) => {
                        setAddRecipeForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_step: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          id_step: undefined,
                        }));
                      }}
                      block
                      placeholder="Pilih Step"
                      style={{ width: "100%" }}
                    />
                    {errorsAddForm.id_step && <p style={{ color: "red" }}>{errorsAddForm.id_step}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Order No</Form.ControlLabel>
                    <Form.Control
                      name="order_no"
                      value={addRecipeForm.order_no}
                      onChange={(value) => {
                        if (parseInt(value) >= 0 || value === "") {
                          setAddRecipeForm((prevFormValue) => ({
                            ...prevFormValue,
                            order_no: value === "" ? "" : parseInt(value),
                          }));
                          setErrorsAddForm((prevErrors) => ({
                            ...prevErrors,
                            order_no: undefined,
                          }));
                        }
                      }}
                      type="number"
                      min={0}
                    />
                    {errorsAddForm.order_no && <p style={{ color: "red" }}>{errorsAddForm.order_no}</p>}
                  </Form.Group>
                </Form>
                <div className="mt-5" style={{ color: 'red' }}>
                  <strong>Perhatian:</strong> Perhatikan urutan pengisian step proses,
                  urutan menentukan tampilan pada report yang dihasilkan oleh system
                </div>
              </Modal.Body>

              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddRecipeForm(emptyAddRecipeForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>

                <Button onClick={HandleAddStepApi} appearance="primary" loading={isLoading} disabled={isLoading}>
                  Tambah
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal edit recipe step */}
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditRecipeForm(emptyEditRecipeForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Edit recipe</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama Step</Form.ControlLabel>
                    <SelectPicker
                      data={stepsDataState}
                      value={editRecipeForm.id_step}
                      onChange={(value) => {
                        setEditRecipeForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_step: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          id_step: undefined,
                        }));
                      }}
                      block
                      placeholder="Pilih Step"
                      style={{ width: "100%" }}
                    />
                    {errorsEditForm.id_step && <p style={{ color: "red" }}>{errorsEditForm.id_step}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Order No</Form.ControlLabel>
                    <Form.Control
                      name="order_no"
                      value={editRecipeForm.order_no}
                      onChange={(value) => {
                        if (parseInt(value) >= 0 || value === "") {
                          setEditRecipeForm((prevFormValue) => ({
                            ...prevFormValue,
                            order_no: value === "" ? "" : parseInt(value),
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            order_no: undefined,
                          }));
                        }
                      }}
                      type="number"
                      min={0}
                    />
                    {errorsEditForm.order_no && <p style={{ color: "red" }}>{errorsEditForm.order_no}</p>}
                  </Form.Group>
                </Form>
                <div className="mt-5" style={{ color: 'red' }}>
                  <strong>Perhatian:</strong> Perhatikan urutan pengisian step proses,
                  urutan menentukan tampilan pada report yang dihasilkan oleh system
                </div>
              </Modal.Body>


              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditRecipeForm(emptyEditRecipeForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    HandleEditStepApi();
                  }}
                  appearance="primary"
                  type="submit"
                  loading={isLoading}
                  disabled={isLoading}
                >
                  Edit
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal delete confirm */}
            <Modal
              backdrop="static"
              open={showConfirmDeleteModal}
              onClose={() => {
                setShowConfirmDeleteModal(false);
                setRecipeStepToDelete(null);
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Konfirmasi Hapus</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <p>Apakah Anda yakin ingin menghapus Recipe Step ini?</p>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowConfirmDeleteModal(false);
                    setRecipeStepToDelete(null);
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={async () => {
                    setIsLoading(true);
                    await handleEditStatusRecipeApi(recipeStepToDelete, 1);
                    setShowConfirmDeleteModal(false);
                    setRecipeStepToDelete(null);
                    setIsLoading(false);
                  }}
                  color="red"
                  appearance="primary"
                  loading={isLoading}
                  disabled={isLoading}
                >
                  Hapus
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal add parameter */}
            <Modal
              backdrop="static"
              open={showAddParameterModal}
              onClose={() => {
                setShowAddParameterModal(false);
                setAddParameterForm({...emptyAddParameterForm, wetmill:formData.wetmill});
                setErrorsAddParameterForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Tambah Parameter</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama Parameter</Form.ControlLabel>
                    <SelectPicker
                      data={prmDataState}
                      value={addParameterForm.id_parameter}
                      onChange={(value) => {
                        setAddParameterForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_parameter: value,
                        }));
                        setErrorsAddParameterForm((prevErrors) => ({
                          ...prevErrors,
                          id_parameter: undefined,
                        }));
                      }}
                      block
                      placeholder="Pilih Parameter"
                      style={{ width: "100%" }}
                    />
                    {errorsAddParameterForm.id_parameter && <p style={{ color: "red" }}>{errorsAddParameterForm.id_parameter}</p>}
                  </Form.Group>
                  <Form.Group style={{ marginRight: "15px" }}>
                    <Form.ControlLabel>Order No</Form.ControlLabel>
                    <Form.Control
                      name="order_no"
                      value={addParameterForm.order_no}
                      onChange={(value) => {
                        if (parseInt(value) >= 0 || value === "") {
                          setAddParameterForm((prevFormValue) => ({
                            ...prevFormValue,
                            order_no: value === "" ? "" : parseInt(value),
                          }));
                          setErrorsAddParameterForm((prevErrors) => ({
                            ...prevErrors,
                            order_no: undefined,
                          }));
                        }
                      }}
                      type="number"
                      min={0}
                    />
                    {errorsAddParameterForm.order_no && <p style={{ color: "red" }}>{errorsAddParameterForm.order_no}</p>}
                  </Form.Group>
                  <div style={{ marginBottom: '20px', color: 'red' }}>
                    <strong>Perhatian:</strong> Perhatikan urutan pengisian parameter step proses,
                    urutan menentukan tampilan pada report yang dihasilkan oleh system
                  </div>

                  <FlexboxGrid>
                    <FlexboxGrid.Item colspan={8}>
                      <Form.Group>
                        <Form.ControlLabel>Wetmill</Form.ControlLabel>
                        <RadioGroup
                          name="wetmill"
                          inline
                          value={addParameterForm.wetmill}
                          onChange={(value) => {
                            setAddParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              wetmill: value,
                            }));
                            setErrorsAddParameterForm((prevErrors) => ({
                              ...prevErrors,
                              wetmill: undefined,
                            }));
                          }}
                          disabled
                        >
                          <Radio value="Y">Yes</Radio>
                          <Radio value="N">No</Radio>
                        </RadioGroup>
                        {errorsAddParameterForm.wetmill && <p style={{ color: "red" }}>{errorsAddParameterForm.wetmill}</p>}
                      </Form.Group>
                    </FlexboxGrid.Item>

                    <FlexboxGrid.Item colspan={8}>
                      <Form.Group>
                        <Form.ControlLabel>Set Point Flag</Form.ControlLabel>
                        <RadioGroup
                          inline
                          value={addParameterForm.set_point_flag}
                          onChange={(value) => {
                            setAddParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              set_point_flag: value,
                              set_point_value: value === 1 ? prevFormValue.set_point_value : "",
                              is_actual: value === 0 ? prevFormValue.is_actual : null,
                              binding_type: value === 0 ? prevFormValue.binding_type : null,
                              min_value: null,
                              max_value: null,
                              absolute_value: null,
                              description_value: "",
                            }));
                            setErrorsAddParameterForm((prevErrors) => ({
                              ...prevErrors,
                              set_point_flag: undefined,
                            }));
                          }}
                        >
                          <Radio value={1}>Yes</Radio>
                          <Radio value={0}>No</Radio>
                        </RadioGroup>
                        {errorsAddParameterForm.set_point_flag && <p style={{ color: "red" }}>{errorsAddParameterForm.set_point_flag}</p>}
                      </Form.Group>
                    </FlexboxGrid.Item>

                    {addParameterForm.set_point_flag === 1 && (
                      <Form.Group style={{ marginTop: "10px" }}>
                        <Form.ControlLabel>Nilai Set Point</Form.ControlLabel>
                        <Form.Control
                          name="set_point_value"
                          value={addParameterForm.set_point_value}
                          onChange={(value) =>
                            setAddParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              set_point_value: value,
                            }))
                          }
                          style={{ marginLeft: "10px", width: "auto" }}
                          placeholder="Masukan Nilai"
                        />
                        {errorsAddParameterForm.set_point_value && <p style={{ color: "red" }}>{errorsAddParameterForm.set_point_value}</p>}
                      </Form.Group>
                    )}

                    <FlexboxGrid.Item colspan={8}>
                      {addParameterForm.set_point_flag === 0 && (
                        <Form.Group>
                          <Form.ControlLabel>Actual</Form.ControlLabel>
                          <RadioGroup
                            inline
                            value={addParameterForm.is_actual}
                            onChange={(value) => {
                              setAddParameterForm((prevFormValue) => ({
                                ...prevFormValue,
                                is_actual: value,
                                binding_type: null,
                                min_value: null,
                                max_value: null,
                                absolute_value: null,
                                description_value: "",
                              }));
                              setErrorsAddParameterForm((prevErrors) => ({
                                ...prevErrors,
                                is_actual: undefined,
                              }));
                            }}
                          >
                            <Radio value={1}>Yes</Radio>
                            <Radio value={0}>No</Radio>
                          </RadioGroup>
                          {errorsAddParameterForm.is_actual && <p style={{ color: "red" }}>{errorsAddParameterForm.is_actual}</p>}
                        </Form.Group>
                      )}
                    </FlexboxGrid.Item>
                  </FlexboxGrid>

                  {addParameterForm.is_actual !== null && (
                    <Form.Group>
                      <Form.ControlLabel>Tipe Binding</Form.ControlLabel>
                      <SelectPicker
                        data={
                          addParameterForm.is_actual === 1
                            ? [
                              { label: "Jangkauan", value: 1 },
                              { label: "Nilai Mutlak", value: 2 },
                            ]
                            : [
                              { label: "Jangkauan", value: 1 },
                              { label: "Nilai Mutlak", value: 2 },
                              { label: "Deskripsi", value: 3 },
                            ]
                        }
                        value={addParameterForm.binding_type}
                        onChange={(value) => {
                          let resetValues = {};
                          if (value === 1) {
                            resetValues = { absolute_value: null, description_value: "" };
                          } else if (value === 2) {
                            resetValues = { min_value: null, max_value: null, description_value: "" };
                          } else if (value === 3) {
                            resetValues = { min_value: null, max_value: null, absolute_value: null };
                          }

                          setAddParameterForm((prevFormValue) => ({
                            ...prevFormValue,
                            binding_type: value,
                            ...resetValues,
                          }));
                          setErrorsAddParameterForm((prevErrors) => ({
                            ...prevErrors,
                            binding_type: undefined,
                          }));
                        }}
                        block
                        placeholder="pilih tipe"
                        style={{ width: "100%" }}
                      />
                      {errorsAddParameterForm.binding_type && <p style={{ color: "red" }}>{errorsAddParameterForm.binding_type}</p>}
                    </Form.Group>
                  )}

                  {addParameterForm.binding_type === 1 && (
                    <>
                      <Form.Group>
                        <Form.ControlLabel>Nilai Min</Form.ControlLabel>
                        <Form.Control
                          name="min_value"
                          value={addParameterForm.min_value}
                          onChange={(value) => {
                            setAddParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              min_value: parseFloat(value) || 0,
                            }));
                            setErrorsAddParameterForm((prevErrors) => ({
                              ...prevErrors,
                              min_value: undefined,
                            }));
                          }}
                          accepter={InputNumber}
                        />
                        {errorsAddParameterForm.min_value && <p style={{ color: "red" }}>{errorsAddParameterForm.min_value}</p>}
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Nilai Max</Form.ControlLabel>
                        <Form.Control
                          name="max_value"
                          value={addParameterForm.max_value}
                          onChange={(value) => {
                            setAddParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              max_value: parseFloat(value) || 0,
                            }));
                            setErrorsAddParameterForm((prevErrors) => ({
                              ...prevErrors,
                              max_value: undefined,
                            }));
                          }}
                          accepter={InputNumber}
                        />
                        {errorsAddParameterForm.max_value && <p style={{ color: "red" }}>{errorsAddParameterForm.max_value}</p>}
                      </Form.Group>
                    </>
                  )}

                  {addParameterForm.binding_type === 2 && (
                    <Form.Group>
                      <Form.ControlLabel>Nilai</Form.ControlLabel>
                      <Form.Control
                        name="absolute_value"
                        value={addParameterForm.absolute_value}
                        onChange={(value) => {
                          setAddParameterForm((prevFormValue) => ({
                            ...prevFormValue,
                            absolute_value: parseFloat(value) || 0,
                          }));
                          setErrorsAddParameterForm((prevErrors) => ({
                            ...prevErrors,
                            absolute_value: undefined,
                          }));
                        }}
                        accepter={InputNumber}
                      />
                      {errorsAddParameterForm.absolute_value && <p style={{ color: "red" }}>{errorsAddParameterForm.absolute_value}</p>}
                    </Form.Group>
                  )}

                  {addParameterForm.binding_type === 3 && (
                    <Form.Group>
                      <Form.ControlLabel>Deskripsi</Form.ControlLabel>
                      <Form.Control
                        name="description_value"
                        value={addParameterForm.description_value}
                        onChange={(value) => {
                          setAddParameterForm((prevFormValue) => ({
                            ...prevFormValue,
                            description_value: value,
                          }));
                          setErrorsAddParameterForm((prevErrors) => ({
                            ...prevErrors,
                            description_value: undefined,
                          }));
                        }}
                      />
                      {errorsAddParameterForm.description_value && <p style={{ color: "red" }}>{errorsAddParameterForm.description_value}</p>}
                    </Form.Group>
                  )}
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddParameterModal(false);
                    setAddParameterForm({...emptyAddParameterForm, wetmill:formData.wetmill});
                    setErrorsAddParameterForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleAddRecipeParameterApi();
                  }}
                  appearance="primary"
                  type="submit"
                  loading={isLoading}
                  disabled={isLoading}
                >
                  Tambah
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal edit parameter */}
            <Modal
              backdrop="static"
              open={showEditParameterModal}
              onClose={() => {
                setShowEditParameterModal(false);
                setEditParameterForm(emptyEditParameterForm);
                setErrorsEditParameterForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Edit Parameter</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama Parameter</Form.ControlLabel>
                    <SelectPicker
                      data={prmDataState}
                      value={editParameterForm.id_parameter}
                      onChange={(value) => {
                        setEditParameterForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_parameter: value,
                        }));
                        setErrorsEditParameterForm((prevErrors) => ({
                          ...prevErrors,
                          id_parameter: undefined,
                        }));
                      }}
                      block
                      placeholder="Pilih Parameter"
                      style={{ width: "100%" }}
                    />
                    {errorsEditParameterForm.id_parameter && <p style={{ color: "red" }}>{errorsEditParameterForm.id_parameter}</p>}
                  </Form.Group>
                  <Form.Group style={{ marginRight: "15px" }}>
                    <Form.ControlLabel>Order No</Form.ControlLabel>
                    <Form.Control
                      name="order_no"
                      value={editParameterForm.order_no}
                      onChange={(value) => {
                        if (parseInt(value) >= 0 || value === "") {
                          setEditParameterForm((prevFormValue) => ({
                            ...prevFormValue,
                            order_no: value === "" ? "" : parseInt(value),
                          }));
                          setErrorsEditParameterForm((prevErrors) => ({
                            ...prevErrors,
                            order_no: undefined,
                          }));
                        }
                      }}
                      type="number"
                      min={0}
                    />
                    {errorsEditParameterForm.order_no && <p style={{ color: "red" }}>{errorsEditParameterForm.order_no}</p>}
                  </Form.Group>
                  <div style={{ marginBottom: '20px', color: 'red' }}>
                    <strong>Perhatian:</strong> Perhatikan urutan pengisian parameter step proses,
                    urutan menentukan tampilan pada report yang dihasilkan oleh system
                  </div>

                  <FlexboxGrid>
                    <FlexboxGrid.Item colspan={8}>
                      <Form.Group>
                        <Form.ControlLabel>Wetmill</Form.ControlLabel>
                        <RadioGroup
                          name="wetmill"
                          inline
                          value={editParameterForm.wetmill}
                          onChange={(value) => {
                            setEditParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              wetmill: value,
                            }));
                            setErrorsEditParameterForm((prevErrors) => ({
                              ...prevErrors,
                              wetmill: undefined,
                            }));
                          }}
                          disabled
                        >
                          <Radio value="Y">Yes</Radio>
                          <Radio value="N">No</Radio>
                        </RadioGroup>
                        {errorsEditParameterForm.wetmill && <p style={{ color: "red" }}>{errorsEditParameterForm.wetmill}</p>}
                      </Form.Group>
                    </FlexboxGrid.Item>

                    <FlexboxGrid.Item colspan={8}>
                      <Form.Group>
                        <Form.ControlLabel>Set Point Flag</Form.ControlLabel>
                        <RadioGroup
                          inline
                          value={editParameterForm.set_point_flag}
                          onChange={(value) => {
                            setEditParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              set_point_flag: value,
                              set_point_value: value === 1 ? prevFormValue.set_point_value : "",
                              is_actual: value === 0 ? prevFormValue.is_actual : null,
                              binding_type: value === 0 ? prevFormValue.binding_type : null,
                              min_value: null,
                              max_value: null,
                              absolute_value: null,
                              description_value: "",
                            }));
                            setErrorsEditParameterForm((prevErrors) => ({
                              ...prevErrors,
                              set_point_flag: undefined,
                            }));
                          }}
                        >
                          <Radio value={1}>Yes</Radio>
                          <Radio value={0}>No</Radio>
                        </RadioGroup>
                        {errorsEditParameterForm.set_point_flag && <p style={{ color: "red" }}>{errorsEditParameterForm.set_point_flag}</p>}
                      </Form.Group>
                    </FlexboxGrid.Item>

                    {editParameterForm.set_point_flag === 1 && (
                      <Form.Group style={{ marginTop: "10px" }}>
                        <Form.ControlLabel>Nilai Set Point</Form.ControlLabel>
                        <Form.Control
                          name="set_point_value"
                          value={editParameterForm.set_point_value}
                          onChange={(value) =>
                            setEditParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              set_point_value: value,
                            }))
                          }
                          style={{ marginLeft: "10px", width: "auto" }}
                          placeholder="Masukan Nilai"
                        />
                        {errorsEditParameterForm.set_point_value && <p style={{ color: "red" }}>{errorsEditParameterForm.set_point_value}</p>}
                      </Form.Group>
                    )}

                    <FlexboxGrid.Item colspan={8}>
                      {editParameterForm.set_point_flag === 0 && (
                        <Form.Group>
                          <Form.ControlLabel>Actual</Form.ControlLabel>
                          <RadioGroup
                            inline
                            value={editParameterForm.is_actual}
                            onChange={(value) => {
                              setEditParameterForm((prevFormValue) => ({
                                ...prevFormValue,
                                is_actual: value,
                                binding_type: null,
                                min_value: null,
                                max_value: null,
                                absolute_value: null,
                                description_value: "",
                              }));
                              setErrorsEditParameterForm((prevErrors) => ({
                                ...prevErrors,
                                is_actual: undefined,
                              }));
                            }}
                          >
                            <Radio value={1}>Yes</Radio>
                            <Radio value={0}>No</Radio>
                          </RadioGroup>
                          {errorsEditParameterForm.is_actual && <p style={{ color: "red" }}>{errorsEditParameterForm.is_actual}</p>}
                        </Form.Group>
                      )}
                    </FlexboxGrid.Item>
                  </FlexboxGrid>

                  {editParameterForm.set_point_flag === 0 && (
                    <Form.Group>
                      <Form.ControlLabel>Tipe Binding</Form.ControlLabel>
                      <SelectPicker
                        data={
                          editParameterForm.is_actual === 1
                            ? [
                              { label: "Jangkauan", value: 1 },
                              { label: "Nilai Mutlak", value: 2 },
                            ]
                            : [
                              { label: "Jangkauan", value: 1 },
                              { label: "Nilai Mutlak", value: 2 },
                              { label: "Deskripsi", value: 3 },
                            ]
                        }
                        value={editParameterForm.binding_type}
                        onChange={(value) => {
                          let resetValues = {};
                          if (value === 1) {
                            resetValues = { absolute_value: null, description_value: "" };
                          } else if (value === 2) {
                            resetValues = { min_value: null, max_value: null, description_value: "" };
                          } else if (value === 3) {
                            resetValues = { min_value: null, max_value: null, absolute_value: null };
                          }

                          setEditParameterForm((prevFormValue) => ({
                            ...prevFormValue,
                            binding_type: value,
                            ...resetValues,
                          }));
                          setErrorsEditParameterForm((prevErrors) => ({
                            ...prevErrors,
                            binding_type: undefined,
                          }));
                        }}
                        block
                        placeholder="Pilih Tipe"
                        style={{ width: "100%" }}
                      />
                      {errorsEditParameterForm.binding_type && <p style={{ color: "red" }}>{errorsEditParameterForm.binding_type}</p>}
                    </Form.Group>
                  )}

                  {editParameterForm.binding_type === 1 && (
                    <>
                      <Form.Group>
                        <Form.ControlLabel>Nilai Min</Form.ControlLabel>
                        <Form.Control
                          name="min_value"
                          value={editParameterForm.min_value}
                          onChange={(value) => {
                            setEditParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              min_value: parseFloat(value) || 0,
                            }));
                            setErrorsEditParameterForm((prevErrors) => ({
                              ...prevErrors,
                              min_value: undefined,
                            }));
                          }}
                          accepter={InputNumber}
                        />
                        {errorsEditParameterForm.min_value && <p style={{ color: "red" }}>{errorsEditParameterForm.min_value}</p>}
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Nilai Max</Form.ControlLabel>
                        <Form.Control
                          name="max_value"
                          value={editParameterForm.max_value}
                          onChange={(value) => {
                            setEditParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              max_value: parseFloat(value) || 0,
                            }));
                            setErrorsEditParameterForm((prevErrors) => ({
                              ...prevErrors,
                              max_value: undefined,
                            }));
                          }}
                          accepter={InputNumber}
                        />
                        {errorsEditParameterForm.max_value && <p style={{ color: "red" }}>{errorsEditParameterForm.max_value}</p>}
                      </Form.Group>
                    </>
                  )}

                  {editParameterForm.binding_type === 2 && (
                    <Form.Group>
                      <Form.ControlLabel>Nilai</Form.ControlLabel>
                      <Form.Control
                        name="absolute_value"
                        value={editParameterForm.absolute_value}
                        onChange={(value) => {
                          setEditParameterForm((prevFormValue) => ({
                            ...prevFormValue,
                            absolute_value: parseFloat(value) || 0,
                          }));
                          setErrorsEditParameterForm((prevErrors) => ({
                            ...prevErrors,
                            absolute_value: undefined,
                          }));
                        }}
                        accepter={InputNumber}
                      />
                      {errorsEditParameterForm.absolute_value && <p style={{ color: "red" }}>{errorsEditParameterForm.absolute_value}</p>}
                    </Form.Group>
                  )}

                  {editParameterForm.binding_type === 3 && (
                    <Form.Group>
                      <Form.ControlLabel>Deskripsi</Form.ControlLabel>
                      <Form.Control
                        name="description_value"
                        value={editParameterForm.description_value}
                        onChange={(value) => {
                          setEditParameterForm((prevFormValue) => ({
                            ...prevFormValue,
                            description_value: value,
                          }));
                          setErrorsEditParameterForm((prevErrors) => ({
                            ...prevErrors,
                            description_value: undefined,
                          }));
                        }}
                      />
                      {errorsEditParameterForm.description_value && <p style={{ color: "red" }}>{errorsEditParameterForm.description_value}</p>}
                    </Form.Group>
                  )}
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditParameterModal(false);
                    setEditParameterForm(emptyEditParameterForm);
                    setErrorsEditParameterForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={async () => {
                    HandleEditParameterApi();
                  }}
                  appearance="primary"
                  loading={isLoading}
                  disabled={isLoading}
                >
                  Simpan
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal hapus parameter */}
            <Modal
              backdrop="static"
              open={showConfirmDeleteParameterModal}
              onClose={() => {
                setShowConfirmDeleteParameterModal(false);
                setParameterToDelete(null);
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Konfirmasi Hapus Parameter</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <p>Apakah Anda yakin ingin menghapus parameter ini?</p>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowConfirmDeleteParameterModal(false);
                    setParameterToDelete(null);
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={async () => {
                    setIsLoading(true);
                    await handleEditStatusParameterApi(parameterToDelete, 1);
                    setParameterToDelete(null);
                  }}
                  color="red"
                  appearance="primary"
                  loading={isLoading}
                  disabled={isLoading}
                >
                  Hapus
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal detail parameter */}
            <Modal
              backdrop="static"
              open={showDetailModal}
              onClose={() => {
                setShowDetailModal(false);
                setSelectedParameterDetail(null);
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title style={{ overflow: "visible" }}>Rincian Parameter: {selectedParameterName}</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                {selectedParameterDetail && (
                  <Form fluid>
                    <FlexboxGrid>
                      <FlexboxGrid.Item colspan={12} style={{ paddingRight: "10px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Dibuat Tanggal</Form.ControlLabel>
                          <Form.Control name="created_date" value={new Date(selectedParameterDetail.created_date).toLocaleDateString("en-GB") || "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Diperbarui Tanggal</Form.ControlLabel>
                          <Form.Control name="updated_date" value={selectedParameterDetail.updated_date ? new Date(selectedParameterDetail.updated_date).toLocaleDateString("en-GB") : "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Dihapus Tanggal</Form.ControlLabel>
                          <Form.Control name="deleted_date" value={selectedParameterDetail.deleted_date ? new Date(selectedParameterDetail.deleted_date).toLocaleDateString("en-GB") : "-"} readOnly />
                        </Form.Group>
                      </FlexboxGrid.Item>
                      <FlexboxGrid.Item colspan={12}>
                        <Form.Group>
                          <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                          <Form.Control name="created_by" value={selectedParameterDetail.created_by || "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                          <Form.Control name="updated_by" value={selectedParameterDetail.updated_by || "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                          <Form.Control name="deleted_by" value={selectedParameterDetail.deleted_by || "-"} readOnly />
                        </Form.Group>
                      </FlexboxGrid.Item>
                    </FlexboxGrid>
                  </Form>
                )}
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowDetailModal(false);
                    setSelectedParameterDetail(null);
                  }}
                  appearance="subtle"
                >
                  Tutup
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal approve */}
            <Modal
              backdrop="static"
              open={showApprovalModal}
              onClose={() => {
                setShowApprovalModal(false);
                setPassword("");
                setRemarks("");
              }}
              overflow={false}
              size={"xs"}
            >
              <Modal.Header>
                <Modal.Title>
                  <FlexboxGrid align="middle">

                    <FlexboxGrid.Item>Konfirmasi Submit</FlexboxGrid.Item>
                  </FlexboxGrid>
                </Modal.Title>
              </Modal.Header>

              <Modal.Body>
                <Stack direction="column" spacing={10}>
                  <p style={{ fontSize: "14px", color: "#555" }}>
                    Apakah Anda yakin ingin mengirim Recipe Step ini?
                  </p>

                </Stack>
              </Modal.Body>
              <Modal.Footer>

                <Button
                  appearance="subtle"
                  onClick={() => {
                    setShowApprovalModal(false);
                  }}
                  loading={loading}
                  disabled={loading}
                >
                  Batal
                </Button>


                <Button
                  appearance="primary"
                  color="green"
                  onClick={async () => {
                    setLoading(true);
                    await HandleEditStatusApprovalPPIApi(formData.id_ppi, 2);
                    setLoading(false);
                  }}
                  loading={loading}
                  disabled={loading}
                >
                  Kirim Ke Supervisor
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
