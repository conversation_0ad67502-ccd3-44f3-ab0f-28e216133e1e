import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/reagen/manufacture/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};

export default function ReagenManufactureApi(){
    return {
        getAll: createApiFunction("get", "get/all"),
        create: createApiFunction("post", "create"),
        getById: createApiFunction("get", "get/id"),
        getAllActive: createApiFunction("get", "get/all/active"),
        edit: createApiFunction("put", "edit"),
        editStatus: createApiFunction("put", "edit/status"),
    }
}