import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/status${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function LkvMasterStatus(){
    return{
        getAllMasterStatus: createApiFunction("get", "/get/all"),
        getAllMasterStatusActive : createApiFunction("get", "/get/all/active"),
        createMasterStatus: createApiFunction("post", "/create"),
        updateMasterStatus: createApiFunction("put", "/edit"),
        updateStatusMasterStatus: createApiFunction("put", "/active")
    }
}