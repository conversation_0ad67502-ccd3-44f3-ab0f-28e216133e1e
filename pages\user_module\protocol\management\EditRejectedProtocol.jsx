import { useRouter } from 'next/router';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import Head from 'next/head';
import MainContent from '@/components/layout/MainContent';
import { useState, useEffect } from 'react';
import {
    Form,
    Button,
    ButtonToolbar,
    Stack,
    Checkbox,
    Input,
    IconButton,
    TreePicker,
    InputNumber,
    useToaster
} from 'rsuite';
import CardTest from '@/components/CardTest';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMinus, faPlus } from '@fortawesome/free-solid-svg-icons';
import Messages from "@/components/Messages";
import ReviewerApproverApi from '@/pages/api/reviewerApprover';
import ProtocolApi from '@/pages/api/protocolApi';
import Editor from '@/components/Editor';
import ParameterApi from '@/pages/api/parameterApi';
import Trash  from '@rsuite/icons/Trash';

export default function EditRejectedProtocol({ noProtocol }) {
    const MySwal = withReactContent(Swal);
    const router = useRouter();
    const toaster = useToaster();
    let path = 'protocol/management/EditProtocol';
    let { data } = router.query;
    data ? (data = JSON.parse(data)) : data;
    const [breadcrumbsData, setBreadcrumbsData] = useState([]);
    const [treePickerReviewerData, setTreePickerReviewerData] = useState([]);
    const [treePickerReviewerData2, setTreePickerReviewerData2] = useState([]);
    const [treePickerReviewerData3, setTreePickerReviewerData3] = useState([]);
    const [treePickerReviewerData4, setTreePickerReviewerData4] = useState([]);
    const [treePickerReviewerLock, setTreePickerReviewerLock] = useState([]);
    const [treePickerApproverData, setTreePickerApproverData] = useState([]);
    const [treePickerApproverData2, setTreePickerApproverData2] = useState([]);
    const [treePickerApproverData3, setTreePickerApproverData3] = useState([]);
    const [treePickerApproverData4, setTreePickerApproverData4] = useState([]);
    const [treePickerApproverDataLock, setTreePickerApproverDataLock] = useState([]);
    const [inputFields, setInputFields] = useState([
        {
            order_number: 1,
            desc: '',
            value_reference: 0,
            min_value: 0,
            max_value: 0,
            id_parameter: 0,
        },
    ]);
    const [selectedReviewer, setSelectedReviewer] = useState([]);
    const [selectedApprover, setSelectedApprover] = useState([]);
    const [cycleMonthData, setCycleMonthData] = useState([
        0, 3, 6, 9, 12, 18, 24, 36,
    ]);
    const [t30selected, setT30Selected] = useState([0]);
    const [idProtocol, setIdProtocol] = useState(0);
    const [t40selected, setT40Selected] = useState([0]);
    const [moduleName, setModuleName] = useState('');
    const [tujuan, setTujuan] = useState('');
    const [ruangLingkup, setRuangLingkup] = useState('');
    const [dokumenAcuan, setDokumenAcuan] = useState('');
    const [tanggungJawab, setTanggungJawab] = useState('');
    const [namaProduk, setNamaProduk] = useState('');
    const [protocolVersion, setProtocolVersion] = useState('');
    const [kodeProduk, setKodeProduk] = useState('');
    const [valueSelectedParam, setValueSelectedParam] = useState([2, 8, 13, 7]);
    const [nomorBatch, setNomorBatch] = useState('');
    const [nomorPPI, setNomorPPI] = useState('');
    const [batchSize, setBatchSize] = useState('');
    const [dibuatOleh, setDibuatOleh] = useState('PT. Sakafarma Laboratories');
    const [createdBy, setCreatedBy] = useState('');
    const [komposisiFormula, setKomposisiFormula] = useState('');
    const [isDisabled, setIsDisabled] = useState(true);
    const [departmentId, setDepartmentId] = useState("");
    const [additionalNotes, setAdditionalNotes] = useState('');
    const [protocolDetailData, setProtocolDetailData] = useState([]);
    const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
    const [title, setTitle]= useState('')
    const { GetActiveReviewerByDepartmentId, GetActiveApproverByDepartmentId, GetActiveReviewerForNewProtocol, GetActiveApproverForNewProtocol } =
        ReviewerApproverApi();
    const { GetProtocolDataByNoProtocol, UpdateRejectedProtocol } = ProtocolApi();
    const { GetAllParameter } = ParameterApi();

    // Card Pengujian state
    const [allParameterData, setAllParameterData] = useState([]);
    const [descValue, setDescValue] = useState('');
    const [absoluteValue, setAbsoluteValue] = useState();
    const [rangeMin, setRangeMin] = useState();
    const [rangeMax, setRangeMax] = useState();
    const [dataDetail, setDataDetail] = useState({});
    const [treePickerData, setTreePickerData] = useState([]);
    const [selectedParameter, setSelectedParameter] = useState({});
    const [arrSelectedParameter, setArrSelectedParameter] = useState([
        {
            id_parameter: 0,
            department: 0,
            parameter_name: '',
            is_active: 0,
            input_method: 0,
            created_by: 0,
            method_desc: '',
        },
    ]);
    const [batchNumber, setBatchNumber] = useState([
    {
        batch_number: '',
    },
    ])

    const isChecked = (group, code) => {
        let result;
        if (group === 'T30') {
            for (let item of t30selected) {
                if (code === item) {
                    result = true;
                    break;
                } else {
                    result = false;
                }
            }
        } else {
            for (let item of t40selected) {
                if (code === item) {
                    result = true;
                    break;
                } else {
                    result = false;
                }
            }
        }
        return result;
    };

    const GetReviewer = async (idData) => {
        const { data: userData } = await GetActiveReviewerForNewProtocol(idData);

        if (userData !== null && userData !== undefined) {
            let treePickerData = [];
            userData.map((user) => {
                const data = {
                    value: user.Employee_Id,
                    label: `${user.Employee_Name} - [${user.Department}]`,
                };
                treePickerData.push(data);
            });

            setTreePickerReviewerData(treePickerData);
            setTreePickerReviewerData2(treePickerData);
            setTreePickerReviewerData3(treePickerData);
            setTreePickerReviewerData4(treePickerData);
            setTreePickerReviewerLock(treePickerData);
        }
    };

    const GetAllParameterData = async () => {
        const { data: parameterData } = await GetAllParameter();
        if (parameterData !== null && parameterData !== undefined) {
            let treePickerDataStore = [];
            parameterData.map((parameter) => {
                const data = {
                    value: parameter.Id_Parameter,
                    label: `${parameter.Parameter_Name} - ${parameter.Method_Desc}`,
                };
                treePickerDataStore.push(data);
            });

            setTreePickerData(treePickerDataStore);
            setAllParameterData(parameterData);
        }
    };

    const GetApprover = async (idData) => {
        const { data: userData } = await GetActiveApproverForNewProtocol(idData);

        if (userData !== null && userData !== undefined) {
            let treePickerData = [];
            userData.map((user) => {
                const data = {
                    value: user.Employee_Id,
                    label: `${user.Employee_Name} - [${user.Department}]`,
                };
                treePickerData.push(data);
            });

            setTreePickerApproverData(treePickerData);
            setTreePickerApproverData2(treePickerData);
            setTreePickerApproverData3(treePickerData);
            setTreePickerApproverData4(treePickerData);
            setTreePickerApproverDataLock(treePickerData);
        }
    };

    const GetProtocolData = async (noProtocol, userData, userDept) => {
        const inputData = {
            no_protocol: noProtocol,
            employee_id: userData,
            department: parseInt(userDept)
        };

        const { data: protocolData } = await GetProtocolDataByNoProtocol(inputData);

        if (protocolData !== null && protocolData !== undefined) {
            // Set Header Data
            setTitle(protocolData.Header_Data[0].Title)
            setIdProtocol(protocolData.Header_Data[0].Id_Protocol);
            setTujuan(protocolData.Header_Data[0].Desc_Purpose);
            setProtocolVersion(protocolData.Header_Data[0].Protocol_Version);
            setRuangLingkup(protocolData.Header_Data[0].Desc_Scope);
            setDokumenAcuan(protocolData.Header_Data[0].Desc_Document);
            setTanggungJawab(protocolData.Header_Data[0].Desc_Responsibilities);
            setNamaProduk(protocolData.Header_Data[0].Product_Name);
            setKodeProduk(protocolData.Header_Data[0].Product_Code);
            setNomorBatch(protocolData.Header_Data[0].Batch_No);
            setNomorPPI(protocolData.Header_Data[0].Ppi_No);
            setBatchSize(protocolData.Header_Data[0].Batch_Size);
            setDibuatOleh(protocolData.Header_Data[0].Manufactured_By);
            setKomposisiFormula(protocolData.Header_Data[0].Formula);
            setAdditionalNotes(protocolData.Header_Data[0].Additional_Notes);

            // Set T30 TimeFrame Data
            let t30DataList = [];
            protocolData.T30Data.map((item) => {
                t30DataList.push(item.Cycle_Month);
            });

            // Set T40 TimeFrame Data
            let t40DataList = [];
            protocolData.T40Data.map((item) => {
                t40DataList.push(item.Cycle_Month);
            });

            setT30Selected(t30DataList);
            setT40Selected(t40DataList);

            // Set Detail Data
            setProtocolDetailData(protocolData.Detail_Data);

            // Set input fields
            const inputFieldsContruct = protocolData.Detail_Data.map((item) => {
                let inputFieldsTemplate = {
                    desc: "",
                    id_parameter: 0,
                    max_value: 0,
                    min_value: 0,
                    order_number: 0,
                    value_reference: 0,
                };
                if (item.Input_Method_Id == 1) {
                    // Desc
                    inputFieldsTemplate.desc = item.Desc;
                } else if (item.Input_Method_Id == 2) {
                    // Absolute / Value Reference
                    inputFieldsTemplate.value_reference = item.Value_Reference;
                } else {
                    // Range
                    inputFieldsTemplate.min_value = item.Min_Value;
                    inputFieldsTemplate.max_value = item.Max_Value;
                }

                inputFieldsTemplate.order_number = item.Order_Number;
                inputFieldsTemplate.id_parameter = item.Id_Parameter;
                return inputFieldsTemplate;
            });
            setInputFields(inputFieldsContruct);

            // Set arr Selected Param
            const arrSelectedConstruct = protocolData.Detail_Data.map(item => {
                let arrSelectedParamTemplate = {
                    id_parameter: 0,
                    department: 0,
                    parameter_name: '',
                    is_active: 0,
                    input_method: 0,
                    created_by: 0,
                    method_desc: '',
                };
                arrSelectedParamTemplate.id_parameter = item.Id_Parameter;
                arrSelectedParamTemplate.parameter_name = item.Parameter_Name;
                arrSelectedParamTemplate.is_active = item.Status_Active;
                arrSelectedParamTemplate.input_method = item.Input_Method_Id;
                arrSelectedParamTemplate.method_desc = item.Input_Method_Desc;
                return arrSelectedParamTemplate;
            });
            setArrSelectedParameter(arrSelectedConstruct);

            // Set Selected Parameter existing data
            const selectedParam = protocolData.Detail_Data.map(item => item.Id_Parameter);
            setValueSelectedParam(selectedParam);
            // Set reviewer order
            const reviewerOrderData = protocolData.ReviewerOrderNew.map(itemReviewer => itemReviewer.Employee_Id);
            setSelectedReviewer(reviewerOrderData);
            // Set approver order
            const approverOrderData = protocolData.ApproverOrderNew.map(itemReviewer => itemReviewer.Employee_Id);
            setSelectedApprover(approverOrderData);

            //set batch number
            setBatchNumber(protocolData.BatchNumber_Data)
        }
    };

    useEffect(() => {
        const moduleNameValue = localStorage.getItem('module_name');
        const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
        // Validate dataLogin
        if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
            router.push('/');
            return;
        }
        if (
            moduleNameValue === null ||
            moduleNameValue === undefined ||
            moduleNameValue === ''
        ) {
            router.push('/dashboard');
            return;
        }
        const asPathWithoutQuery = router.asPath.split('?')[0];
        const asPathNestedRoutes = asPathWithoutQuery
            .split('/')
            .filter((v) => v.length);
        let routerBreadCrumbsData = [
            moduleNameValue,
            asPathNestedRoutes[1],
            asPathNestedRoutes[2],
        ];
        const capitalizeFirstLetter = (str) => {
            return str.charAt(0).toUpperCase() + str.slice(1);
        };
        const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
            const words = item.split(/(?=[A-Z])/);
            return words.map((word) => capitalizeFirstLetter(word)).join(' ');
        });
        setBreadcrumbsData(breadCrumbsResult);
        setModuleName(moduleNameValue);
        GetProtocolData(noProtocol, dataLogin.employee_id, dataLogin.department);
        setDepartmentId(dataLogin.department);
        setCreatedBy(dataLogin.employee_id);
        const userDepartmentData = {
            id_department: parseInt(dataLogin.department),
            employee_id: dataLogin.employee_id
        };
        GetReviewer(userDepartmentData);
        GetApprover(userDepartmentData);
        GetAllParameterData();
    }, []);

    const addParameterPengujian = () => {
        let newField = {
            desc: '',
            value_reference: 0,
            min_value: 0,
            max_value: 0,
            id_parameter: 0,
        };

        let newParameterField = {
            id_parameter: 0,
            department: 0,
            parameter_name: '',
            is_active: 0,
            input_method: 0,
            created_by: 0,
            method_desc: '',
        };
        setInputFields([...inputFields, newField]);
        setArrSelectedParameter([...arrSelectedParameter, newParameterField]);
    };

    const removeParameterPengujian = () => {
        let dataInputField = [...inputFields];
        let dataParameterField = [...arrSelectedParameter];
        if (dataInputField.length <= 1) {
            console.log('Cannot Delete !');
        } else {
            dataInputField.pop();
            dataParameterField.pop();
            setInputFields(dataInputField);
            setArrSelectedParameter(dataParameterField);
        }
    };

    const t30ChangeHandler = (event) => {
        const isChecked = event.target.checked;
        const value = parseInt(event.target.value);

        if (isChecked) {
            setT30Selected((prevValue) => [...prevValue, value]);
        } else {
            const newData = t30selected.filter((item) => item !== value);
            setT30Selected(newData);
        }
    };

    const t40ChangeHandler = (event) => {
        const isChecked = event.target.checked;
        const value = parseInt(event.target.value);

        if (isChecked) {
            setT40Selected((prevValue) => [...prevValue, value]);
        } else {
            const newData = t40selected.filter((item) => item !== value);
            setT40Selected(newData);
        }
    };

    const handleFormChange = (index, name, e) => {
        let data = [...inputFields];
        data[index].order_number = index + 1;
        data[index][name] = (e.target.value);
        setInputFields(data);
    };
    const handleFormDesc = (index, v) => {
        let data = [...inputFields];
        // data.desc = v;
        data[index].order_number = index + 1;
        data[index].desc = v;
        setInputFields(data);
    };
    const handleFormParameter = (index, v) => {
        // Jika parameter dihapus dari treepicker
        if (v === null) {
            const resetArrSelectedParameter = {
                id_parameter: 0,
                department: 0,
                parameter_name: '',
                is_active: 0,
                input_method: 0,
                created_by: 0,
                method_desc: '',
            };

            const resetInputFields = {
                desc: '',
                value_reference: 0,
                min_value: 0,
                max_value: 0,
                id_parameter: 0,
            };

            // Reset selected parameter
            let newArrSelectedParam = [...arrSelectedParameter];
            newArrSelectedParam[index] = resetArrSelectedParameter;
            setArrSelectedParameter(newArrSelectedParam);

            // Reset inputFields
            let newInputFields = [...inputFields];
            newInputFields[index] = resetInputFields;
            setInputFields(newInputFields);

            return;
        }

        // Get chosen parameter data
        const parameter = allParameterData.filter(
            (item) => item.Id_Parameter === v,
        );

        // Validate no duplication of parameter
        const idParameter = parameter[0].Id_Parameter;
        const dataInputFields = [...inputFields];
        const existingDataParameter = dataInputFields.filter(
            (item) => item.id_parameter == idParameter,
        );

        if (existingDataParameter.length > 0) {
            toaster.push(
                Messages("warning", "Please use different parameter !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            return;
        }

        // Add new parameter
        let data = [...arrSelectedParameter];
        const newDataParamConstruct = {
            id_parameter: parameter[0].Id_Parameter,
            department: parameter[0].Department,
            parameter_name: parameter[0].Parameter_Name,
            is_active: parameter[0].Is_Active,
            input_method: parameter[0].Input_Method,
            created_by: parameter[0].Created_By,
            method_desc: parameter[0].Method_Desc,
        };
        data[index] = newDataParamConstruct;
        setArrSelectedParameter(data);

        // Set id_parameter in inputFields
        dataInputFields[index].id_parameter = idParameter;
    };

    const selectReviewerChangeHandler = (value, index) => {
        // setSelectedReviewer(value);
        let arrReviewer = [...selectedReviewer];
        const isSelected = arrReviewer.filter(item => item == `${value}`);
        if (isSelected.length > 0) {
            toaster.push(
                Messages("warning", "Please select another reviewer !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }
        arrReviewer[index] = `${value}`;
        setSelectedReviewer(arrReviewer);

        if (value == null) {
            // console.log("ini arr new");
            // let newArr = treePickerReviewerData2
            // let arrNewData = treePickerReviewerLock.filter(item => {
            //   if (!treePickerReviewerData2.includes(item)) {
            //     console.log("ini item " + item);
            //     return item
            //   }
            // })
            // console.log("Nilai newarr : ", newArr);
            // console.log("Nilai arrnewdata :", arrNewData);
            // newArr.push(...arrNewData)
            // console.log("Nilai newarr baru :", newArr);
            // // treePickerReviewerData2.push(arrNewData)
            // // console.log("Nilai arrNewData : ", arrNewData);
            // setTreePickerReviewerData2(newArr)
            // setTreePickerReviewerData3(newArr)

        } else {
            let arrDataUser = treePickerReviewerData.filter(item => item.value !== value);
            setTreePickerReviewerData2(arrDataUser);
            setTreePickerReviewerData3(arrDataUser);
        }
        // console.log("Nilai arrDataUser : ", arrDataUser)

    };
    const selectReviewer2ChangeHandler = (value, index) => {
        // setSelectedReviewer(value);
        let arrReviewer = [...selectedReviewer];
        const isSelected = arrReviewer.filter(item => item == `${value}`);
        if (isSelected.length > 0) {
            toaster.push(
                Messages("warning", "Please select another reviewer !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }
        arrReviewer[index] = `${value}`;
        setSelectedReviewer(arrReviewer);

        if (value == null) {
            // let newArr = treePickerReviewerData3
            // let arrNewData = treePickerReviewerLock.filter(item => {
            //   if (!treePickerReviewerData3.includes(item)) {
            //     return item
            //   }
            // })
            // console.log("Nilai newarr : ", newArr);
            // console.log("Nilai arrnewdata :", arrNewData);
            // newArr.push(...arrNewData)
            // console.log("Nilai newarr baru :", newArr);
            // // treePickerReviewerData3.push(arrNewData)
            // setTreePickerReviewerData3(newArr)
        } else {
            let arrDataUser = treePickerReviewerData2.filter(item => item.value !== value);
            setTreePickerReviewerData3(arrDataUser);
        }
        // if (value !== null) {
        //   let arrDataUser = treePickerReviewerData.filter(item => item.value !== value)
        //   setTreePickerReviewerData3(arrDataUser)
        // }
    };
    const selectReviewer3ChangeHandler = (value, index) => {
        // setSelectedReviewer(value);
        let arrReviewer = [...selectedReviewer];
        const isSelected = arrReviewer.filter(item => item == `${value}`);
        if (isSelected.length > 0) {
            toaster.push(
                Messages("warning", "Please select another reviewer !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }
        arrReviewer[index] = `${value}`;
        setSelectedReviewer(arrReviewer);

        if (value == null) {
            // let newArr = treePickerReviewerData4
            // let arrNewData = treePickerReviewerLock.filter(item => {
            //   if (!treePickerReviewerData4.includes(item)) {
            //     return item
            //   }
            // })
            // newArr.push(...arrNewData)
            // // treePickerReviewerData4.push(arrNewData)
            // setTreePickerReviewerData4(newArr)
        } else {
            let arrDataUser = treePickerReviewerData.filter(item => item.value !== value);
            setTreePickerReviewerData4(arrDataUser);
        }
        // if (value !== null) {
        //   let arrDataUser = treePickerReviewerData.filter(item => item.value !== value)
        //   setTreePickerReviewerData4(arrDataUser)
        // }
    };
    const selectApproverChangeHandler = (value, index) => {
        // setSelectedReviewer(value);
        let arrApprover = [...selectedApprover];
        const isSelected = arrApprover.filter(item => item == `${value}`);
        if (isSelected.length > 0) {
            toaster.push(
                Messages("warning", "Please select another approver !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }
        arrApprover[index] = `${value}`;
        setSelectedApprover(arrApprover);

        if (value == null) {
            // let newArr = treePickerApproverData2
            // let arrNewData = treePickerApproverDataLock.filter(item => {
            //   if (!treePickerApproverData2.includes(item)) {
            //     return item
            //   }
            // })
            // newArr.push(...arrNewData)
            // treePickerApproverData2.push(arrNewData)
            // setTreePickerApproverData2(newArr)
        } else {
            let arrDataUser = treePickerApproverData.filter(item => item.value !== value);
            setTreePickerApproverData2(arrDataUser);
            setTreePickerApproverData3(arrDataUser);
        }
    };
    const selectApproverChangeHandler2 = (value, index) => {
        // setSelectedReviewer(value);
        let arrApprover = [...selectedApprover];
        const isSelected = arrApprover.filter(item => item == `${value}`);
        if (isSelected.length > 0) {
            toaster.push(
                Messages("warning", "Please select another approver !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }
        arrApprover[index] = `${value}`;
        setSelectedApprover(arrApprover);

        if (value == null) {
            // let newArr = treePickerApproverData3
            // let arrNewData = treePickerApproverDataLock.filter(item => {
            //   if (!treePickerApproverData3.includes(item)) {
            //     return item
            //   }
            // })
            // newArr.push(...arrNewData)
            // treePickerApproverData3.push(arrNewData)
            // setTreePickerApproverData3(newArr)
        } else {
            let arrDataUser = treePickerApproverData2.filter(item => item.value !== value);
            setTreePickerApproverData3(arrDataUser);
        }
    };
    const selectApproverChangeHandler3 = (value, index) => {
        // setSelectedReviewer(value);
        let arrApprover = [...selectedApprover];
        const isSelected = arrApprover.filter(item => item == `${value}`);
        if (isSelected.length > 0) {
            toaster.push(
                Messages("warning", "Please select another approver !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }
        arrApprover[index] = `${value}`;
        setSelectedApprover(arrApprover);

        // if (value == null) {
        //   let newArr = treePickerApproverData2
        //   let arrNewData = treePickerApproverDataLock.filter(item => {
        //     if (!treePickerApproverData4.includes(item)) {
        //       return item
        //     }
        //   })
        //   newArr.push(...arrNewData)
        //   treePickerApproverData4.push(arrNewData)
        //   setTreePickerApproverData4(newArr)
        // } else {
        //   let arrDataUser = treePickerApproverData.filter(item => item.value !== value)
        //   setTreePickerApproverData4(arrDataUser)
        // }
        if (value !== null) {
            let arrDataUser = treePickerApproverData.filter(item => item.value !== value);
            setTreePickerApproverData4(arrDataUser);
        }
    };
    const tujuanChangeHandler = (content) => {
        setTujuan(content);
    };
    const ruangLingkupChangeHandler = (content) => {
        setRuangLingkup(content);
    };
    const dokumenAcuanChangeHandler = (content) => {
        setDokumenAcuan(content);
    };
    const tanggungJawabChangeHandler = (content) => {
        setTanggungJawab(content);
    };
    const namaProdukChangeHandler = (content) => {
        setNamaProduk(content);
    };
    const kodeProdukChangeHandler = (content) => {
        setKodeProduk(content);
    };
    const nomorBatchChangeHandler = (content) => {
        setNomorBatch(content);
    };
    const nomorPpiChangeHandler = (content) => {
        setNomorPPI(content);
    };
    const batchSizeChangeHandler = (content) => {
        setBatchSize(content);
    };
    const formulaChangeHandler = (content) => {
        setKomposisiFormula(content);
    };
    const keteranganTambahanChangeHandler = (content) => {
        setAdditionalNotes(content);
    };

    const submitHandler = async () => {
        setIsSubmitButtonDisabled(true);
        if (
            tujuan === '' ||
            ruangLingkup === '' ||
            dokumenAcuan === '' ||
            tanggungJawab === '' ||
            namaProduk === '' ||
            // nomorBatch === '' ||
            nomorPPI === '' ||
            batchSize === '' ||
            selectedReviewer.length === 0 ||
            selectedApprover.length === 0
        ) {
            toaster.push(
                Messages("warning", "Please fill all required data !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }
        // Validate if parameter pengujian is not filled
        const isEmptyDataParameter = inputFields.map(item => {
            if (item.desc == "" && item.min_value == 0 && item.max_value == 0 && item.value_reference == 0) {
                toaster.push(
                    Messages("warning", "Please fill the parameter data !"),
                    {
                        placement: "topEnd",
                        duration: 3000,
                    }
                );
                setIsSubmitButtonDisabled(false);
                return;
            } else if (isNaN(item.min_value) || isNaN(item.max_value) || isNaN(item.value_reference)) {
                toaster.push(
                    Messages("warning", "Invalid parameter data !"),
                    {
                        placement: "topEnd",
                        duration: 3000,
                    }
                );
                setIsSubmitButtonDisabled(false);
                return;
            }
        });
        // Validate reviewer flow data (sequential check)
        let reviewerDataFragment = '';
        const iteratingReviewerData = selectedReviewer.map(item => {
            if (item === '"null"') {
                reviewerDataFragment += 0;
            } else {
                reviewerDataFragment += 1;
            }
        });
        if (reviewerDataFragment.includes('01')) {
            toaster.push(
                Messages("warning", "Invalid reviewer data !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }
        if (!reviewerDataFragment.includes('1')) {
            toaster.push(
                Messages("warning", "Please choose reviewer !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }

        // Validate reviewer flow data (sequential check)
        let approverDataFragment = '';
        const iteratingApproverData = selectedApprover.map(item => {
            if (item === '"null"') {
                approverDataFragment += 0;
            } else {
                approverDataFragment += 1;
            }
        });
        if (approverDataFragment.includes('01')) {
            toaster.push(
                Messages("warning", "Invalid reviewer data !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }
        if (!approverDataFragment.includes('1')) {
            toaster.push(
                Messages("warning", "Please choose approver !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }
        // Validate reviewer data (remove null values)
        let removeNullReviewer = selectedReviewer.filter(item => item !== 'null');
        // Validate approver data (remove null values)
        let removeNullApprover = selectedApprover.filter(item => item !== 'null');

        // Contruct reviewer and approver data 
        let filteredReviewer = removeNullReviewer.map(item => `"${item}"`);
        let filteredApprover = removeNullApprover.map(item => `"${item}"`);

        //remap input fields to float
        let updatedInputFields = inputFields.map(item => {

            return { ...item, min_value : parseFloat(item.min_value), 
            max_value: parseFloat(item.max_value), 
            value_reference: parseFloat(item.value_reference)
            }; 
        })


        // Construct data
        const dataPostProtocol = {
            header_protocol: {
                no_protocol: noProtocol,
                product_name: namaProduk,
                department: parseInt(departmentId),
                created_by: createdBy,
                desc_purpose: tujuan,
                desc_scope: ruangLingkup,
                desc_document: dokumenAcuan,
                desc_responsibilities: tanggungJawab,
                product_code: kodeProduk,
                batch_no: nomorBatch,
                ppi_no: nomorPPI,
                batch_size: batchSize,
                manufactured_by: dibuatOleh,
                formula: komposisiFormula,
                additional_notes: additionalNotes,
                reviewer: `[${filteredReviewer}]`,
                approver: `[${filteredApprover}]`,
                title: title,
            },
            batch_number_protocol: batchNumber,
            detail_protocol: updatedInputFields,
            cycle_protocol_T30_month: t30selected,
            cycle_protocol_T40_month: t40selected,
        };

        setIsSubmitButtonDisabled(true);
        const { status, message } = await UpdateRejectedProtocol(dataPostProtocol);
        if (status === 200) {
            toaster.push(
                Messages("success", "Data saved !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            router.push('/user_module/protocol/management/BrowseProtocol');
            return;
        }

        MySwal.fire({
            icon: 'error',
            title: 'Data insertion failed !',
            text: message,
        });
        setIsSubmitButtonDisabled(false);
        return;
    };

    //control batchNumber
  const addBatchNumber = () =>{
    let newBatchNumber = {
      batch_number:''
    }

    setBatchNumber([...batchNumber, newBatchNumber])
  }

  const removeBatchNumber = () =>{
    let dataBatchNumber = [...batchNumber];
    if (dataBatchNumber.length <= 1) {
      console.log('Cannot Delete !');
    } else {
      dataBatchNumber.pop();
      setBatchNumber(dataBatchNumber);
    }
  }

  const removeBatchNumberIndex = (index) => {
    let dataBatchNumber = [...batchNumber];
    if (dataBatchNumber.length <= 1) {
      console.log('Cannot Delete !');
    } else {
        dataBatchNumber.splice(index, 1);
        setBatchNumber(dataBatchNumber);
    }
  }

  const handleBatchForm = (index, v) => {
    let data = [...batchNumber];
    // data.desc = v;
    data[index].Batch_number = v;
    setBatchNumber(data);
  };

    return (
        <>
            <div>
                <Head>
                    <title>Edit Rejected Protocol</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <MainContent>
                    <ModuleContentHeader
                        breadcrumbs={breadcrumbsData}
                        module_name={moduleName}
                    />

                    <Form layout="horizontal">
                        <Stack alignItems="flex-start" direction="column" spacing={9}>
                            <div>
                                <p>Title Protocol:</p>
                                <Input
                                as="textarea"
                                value={title}
                                onChange={v=>setTitle(v)}
                                style={{ width: 224 }} 
                                />
                            </div>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Tujuan : </p>
                                <Editor
                                    contentValue={tujuan}
                                    valueHandler={tujuanChangeHandler}
                                />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Ruang Lingkup : </p>
                                <Editor
                                    contentValue={ruangLingkup}
                                    valueHandler={ruangLingkupChangeHandler}
                                />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Dokumen Acuan : </p>
                                <Editor
                                    contentValue={dokumenAcuan}
                                    valueHandler={dokumenAcuanChangeHandler}
                                />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Tanggung Jawab : </p>
                                <Editor
                                    contentValue={tanggungJawab}
                                    valueHandler={tanggungJawabChangeHandler}
                                />
                            </Stack>
                            <Stack
                                direction="column"
                                alignItems="flex-start"
                                style={{ marginTop: '1rem' }}
                            >
                                <p className="font-bold">Rancangan Studi : </p>
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p>Nama Produk : </p>
                                <Editor
                                    contentValue={namaProduk}
                                    valueHandler={namaProdukChangeHandler}
                                />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p>Kode Produk : </p>
                                <Editor
                                    contentValue={kodeProduk}
                                    valueHandler={kodeProdukChangeHandler}
                                />
                            </Stack>
                            {/* <Stack direction="column" alignItems="flex-start">
                                <p>Nomor Batch : </p>
                                <Editor
                                    contentValue={nomorBatch}
                                    valueHandler={nomorBatchChangeHandler}
                                />
                            </Stack> */}
                            <Stack direction="column" alignItems="flex-start">
                                <p>Nomor PPI : </p>
                                <Editor
                                    contentValue={nomorPPI}
                                    valueHandler={nomorPpiChangeHandler}
                                />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p>Batch Size : </p>
                                <Editor
                                    contentValue={batchSize}
                                    valueHandler={batchSizeChangeHandler}
                                />
                            </Stack>
                            <Form.Group
                                controlId="dibuatOleh"
                                style={{ marginBottom: '1em' }}
                            >
                                <Form.ControlLabel>
                                    <p className="text-xs">Dibuat Oleh :</p>
                                </Form.ControlLabel>
                                <Form.Control
                                    name="dibuatOleh"
                                    type="text"
                                    value={dibuatOleh}
                                    onChange={(value) => setDibuatOleh(value)}
                                    readOnly
                                    required
                                />
                            </Form.Group>
                        </Stack>

                        <Form.Group
                            controlId="komponenPenyimpanan"
                            style={{ marginTop: '2em' }}
                        >
                            <Form.Group>
                                <Form.ControlLabel>
                                    <strong>Kondisi Penyimpanan :</strong>
                                </Form.ControlLabel>
                            </Form.Group>
                            <table className="table" style={{ maxWidth: 546 }}>
                                <thead>
                                    <tr>
                                        <th scope="col">No</th>
                                        <th scope="col" style={{ minWidth: 250 }}>
                                            Kriteria
                                        </th>
                                        {cycleMonthData.map((value) => (
                                            <th scope="col" className="text-center">
                                                {value}
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td scope="col">1</td>
                                        <td scope="col">T30 (30 &deg;C &plusmn; 2&deg;C / 75% &plusmn; 5% RH)</td>
                                        <td scope="col">
                                            <input
                                                type="checkbox"
                                                name="T30"
                                                value="0"
                                                onChange={t30ChangeHandler}
                                                checked={true}
                                                disabled={true}
                                            />
                                        </td>
                                        {cycleMonthData.map((item) => {
                                            if (item === 0) {
                                                return undefined;
                                            }
                                            return (
                                                <td scope="col">
                                                    <input
                                                        type="checkbox"
                                                        name="T30"
                                                        value={item}
                                                        onChange={t30ChangeHandler}
                                                        checked={isChecked('T30', parseInt(item))}
                                                    />
                                                </td>
                                            );
                                        })}
                                    </tr>
                                    <tr>
                                        <td scope="col">2</td>
                                        <td scope="col">T40 (40 &deg;C &plusmn; 2&deg;C / 75% &plusmn; 5% RH)</td>
                                        <td scope="col">
                                            <input
                                                type="checkbox"
                                                name="T30"
                                                value="0"
                                                onChange={t30ChangeHandler}
                                                checked={true}
                                                disabled={true}
                                            />
                                        </td>
                                        {cycleMonthData.map((item) => {
                                            if (item === 0) {
                                                return undefined;
                                            }
                                            return (
                                                <td scope="col">
                                                    <input
                                                        type="checkbox"
                                                        name="T40"
                                                        value={item}
                                                        onChange={t40ChangeHandler}
                                                        checked={isChecked('T40', parseInt(item))}
                                                    />
                                                </td>
                                            );
                                        })}
                                    </tr>
                                </tbody>
                            </table>
                        </Form.Group>

                        <Form.Group controlId="komposisiFormula">
                            <Stack>
                                <label htmlFor="komposisiFormula">
                                    <strong>Komposisi Formula :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <Editor
                                    contentValue={komposisiFormula}
                                    valueHandler={formulaChangeHandler}
                                />
                            </Stack>
                        </Form.Group>
                        
                        <Form.Group controlId="batchNumberProtocol">
                            <Stack direction="column" alignItems="flex-start">
                                <p className="mb-2">
                                <strong>Batch Number :</strong>
                                </p>
                                <Stack spacing={3} style={{ marginBottom: '0.5em' }}>
                                <IconButton
                                    icon={<FontAwesomeIcon icon={faPlus} />}
                                    color="green"
                                    appearance="primary"
                                    circle
                                    onClick={addBatchNumber}
                                />
                                <IconButton
                                    icon={<FontAwesomeIcon icon={faMinus} />}
                                    color="red"
                                    appearance="primary"
                                    circle
                                    onClick={removeBatchNumber}
                                />
                                </Stack>
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                {batchNumber.map((input, index) => {
                                return (
                                    <Stack
                                    style={{
                                        padding: '0.5em',
                                        border: '0.1em solid #adadad',
                                        borderRadius: '5px 5px 5px 5px',
                                        marginBottom: '0.5em',
                                    }}
                                    >
                                    <Stack
                                        direction="column"
                                        alignItems="flex-start"
                                        style={{ marginLeft: '0.5em' }}
                                    >
                                        <p>
                                        <strong>Batch Number</strong>
                                        </p>
                                        {(
                                            <Stack>
                                            <Input
                                                value={input.Batch_number}
                                                as="textarea"
                                                onChange={(v) => {
                                                handleBatchForm(index, v);
                                                //console.log(v)
                                                }}
                                                type="text"
                                                required
                                                />

                                            <IconButton size='sm' icon={<Trash  />} 
                                                appearance='ghost'
                                                color='red' 
                                                style={{marginLeft:'5px'}}
                                                title='Delete index' onClick={() => 
                                                {
                                                removeBatchNumberIndex(index)           
                                                }} 
                                            />
                                            </Stack>
                                        )}
                                        
                                    </Stack>
                                    </Stack>
                                );
                                })}
                            </Stack>
                        </Form.Group>

                        <Form.Group controlId="parameterPengujianSpesifikasi">
                            <Stack direction="column" alignItems="flex-start">
                                <p className="mb-2">
                                    <strong>Parameter Pengujian & Spesifikasi :</strong>
                                </p>
                                <Stack spacing={3} style={{ marginBottom: '0.5em' }}>
                                    <IconButton
                                        icon={<FontAwesomeIcon icon={faPlus} />}
                                        color="green"
                                        appearance="primary"
                                        circle
                                        onClick={addParameterPengujian}
                                    />
                                    <IconButton
                                        icon={<FontAwesomeIcon icon={faMinus} />}
                                        color="red"
                                        appearance="primary"
                                        circle
                                        onClick={removeParameterPengujian}
                                    />
                                </Stack>
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                {inputFields.map((input, index) => {
                                    return (
                                        <Stack
                                            style={{
                                                padding: '0.5em',
                                                border: '0.1em solid #adadad',
                                                borderRadius: '5px 5px 5px 5px',
                                                marginBottom: '0.5em',
                                            }}
                                        >
                                            <Stack direction="column" alignItems="flex-start">
                                                <p>
                                                    <strong>Parameter</strong>
                                                </p>
                                                <TreePicker
                                                    value={input.id_parameter ? input.id_parameter : ""}
                                                    data={treePickerData}
                                                    defaultExpandAll
                                                    onChange={(v) => handleFormParameter(index, v)}
                                                    style={{ minWidth: 246 }}
                                                />
                                            </Stack>
                                            <Stack
                                                direction="column"
                                                alignItems="flex-start"
                                                style={{ marginLeft: '0.5em' }}
                                            >
                                                <p>
                                                    <strong>Spesifikasi</strong>
                                                </p>
                                                {(arrSelectedParameter[index].input_method ==
                                                    undefined || arrSelectedParameter[index].input_method == null || arrSelectedParameter[index].input_method == 0) && <Input disabled={true} />}
                                                {arrSelectedParameter[index].input_method != undefined && arrSelectedParameter[index].input_method != null && arrSelectedParameter[index].input_method == 2 && (
                                                    <InputNumber
                                                        value={input.value_reference}
                                                        onChange={(v, e) =>
                                                            handleFormChange(index, 'value_reference', e)
                                                        }
                                                        required
                                                    />
                                                )}
                                                {arrSelectedParameter[index].input_method != undefined && arrSelectedParameter[index].input_method != null && arrSelectedParameter[index].input_method == 1 && (
                                                    <Input
                                                        value={input.desc}
                                                        onChange={(v) => {
                                                            handleFormDesc(index, v);
                                                        }}
                                                        type="text"
                                                        required
                                                    />
                                                )}
                                                {arrSelectedParameter[index].input_method != undefined && arrSelectedParameter[index].input_method != null && arrSelectedParameter[index].input_method == 3 && (
                                                    <Stack>
                                                        <InputNumber
                                                            value={input.min_value}
                                                            onChange={(v, e) =>
                                                                handleFormChange(index, 'min_value', e)
                                                            }
                                                            required
                                                            style={{ maxWidth: '7em' }}
                                                        />{' '}
                                                        -{' '}
                                                        <InputNumber
                                                            value={input.max_value}
                                                            onChange={(v, e) =>
                                                                handleFormChange(index, 'max_value', e)
                                                            }
                                                            required
                                                            style={{ maxWidth: '7em' }}
                                                        />
                                                    </Stack>
                                                )}
                                            </Stack>
                                        </Stack>
                                    );
                                })}
                            </Stack>
                        </Form.Group>

                        <Form.Group controlId="keteranganTambahan">
                            <Stack>
                                <label htmlFor="keteranganTambahan">
                                    <strong>Keterangan Tambahan :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <Editor
                                    contentValue={additionalNotes}
                                    valueHandler={keteranganTambahanChangeHandler}
                                />
                            </Stack>
                        </Form.Group>

                        <Form.Group controlId="pilihReviewer">
                            <Stack>
                                <label htmlFor="pilihReviewer">
                                    <strong>Pilih Reviewer 1 :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <TreePicker
                                    data={treePickerReviewerData}
                                    defaultExpandAll
                                    onChange={value => selectReviewerChangeHandler(value, 0)}
                                    value={selectedReviewer[0] ? selectedReviewer[0] : ""}
                                    style={{ minWidth: 246 }}
                                />
                            </Stack>
                        </Form.Group>
                        <Form.Group controlId="pilihReviewer2">
                            <Stack>
                                <label htmlFor="pilihReviewer2">
                                    <strong>Pilih Reviewer 2 :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <TreePicker
                                    data={treePickerReviewerData2}
                                    defaultExpandAll
                                    onChange={value => selectReviewer2ChangeHandler(value, 1)}
                                    value={selectedReviewer[1] ? selectedReviewer[1] : ""}
                                    style={{ minWidth: 246 }}
                                    disabled={selectedReviewer[0] === null || selectedReviewer[0] === undefined || selectedReviewer[0] === ''}
                                />
                            </Stack>
                        </Form.Group>
                        <Form.Group controlId="pilihReviewer2">
                            <Stack>
                                <label htmlFor="pilihReviewer2">
                                    <strong>Pilih Reviewer 3 :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <TreePicker
                                    data={treePickerReviewerData3}
                                    defaultExpandAll
                                    onChange={value => selectReviewer3ChangeHandler(value, 2)}
                                    value={selectedReviewer[2] ? selectedReviewer[2] : ""}
                                    style={{ minWidth: 246 }}
                                    disabled={selectedReviewer[1] === null || selectedReviewer[1] === undefined || selectedReviewer[1] === ''}
                                />
                            </Stack>
                        </Form.Group>
                        <Form.Group controlId="pilihApprover">
                            <Stack>
                                <label htmlFor="pilihApprover">
                                    <strong>Pilih Approver 1 :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <TreePicker
                                    data={treePickerApproverData}
                                    defaultExpandAll
                                    value={selectedApprover[0] ? selectedApprover[0] : ""}
                                    onChange={value => selectApproverChangeHandler(value, 0)}
                                    style={{ minWidth: 246 }}
                                />
                            </Stack>
                        </Form.Group>
                        <Form.Group controlId="pilihApprover">
                            <Stack>
                                <label htmlFor="pilihApprover">
                                    <strong>Pilih Approver 2 :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <TreePicker
                                    data={treePickerApproverData2}
                                    defaultExpandAll
                                    value={selectedApprover[1] ? selectedApprover[1] : ""}
                                    onChange={value => selectApproverChangeHandler2(value, 1)}
                                    disabled={selectedApprover[0] === null || selectedApprover[0] === undefined || selectedApprover[0] === ''}
                                    style={{ minWidth: 246 }}
                                />
                            </Stack>
                        </Form.Group>
                        <Form.Group controlId="pilihApprover">
                            <Stack>
                                <label htmlFor="pilihApprover">
                                    <strong>Pilih Approver 3 :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <TreePicker
                                    data={treePickerApproverData3}
                                    value={selectedApprover[2] ? selectedApprover[2] : ""}
                                    defaultExpandAll
                                    onChange={value => selectApproverChangeHandler3(value, 2)}
                                    disabled={selectedApprover[1] === null || selectedApprover[1] === undefined || selectedApprover[1] === ''}
                                    style={{ minWidth: 246 }}
                                />
                            </Stack>
                        </Form.Group>

                        <Form.Group><Stack direction='row' spacing={6}>
                            <Button
                                onClick={submitHandler}
                                appearance="primary"
                                type="submit"
                                disabled={isSubmitButtonDisabled}
                            >
                                Submit
                            </Button>
                            <Button
                                appearance="default"
                                onClick={() => router.back()}
                                disabled={isSubmitButtonDisabled}
                            >
                                Cancel
                            </Button>
                        </Stack>
                        </Form.Group>
                    </Form>
                </MainContent>
            </ContainerLayout>
        </>
    );
}

export async function getServerSideProps({ query }) {
    const { noProtocol } = query;

    return {
        props: {
            noProtocol,
        },
    };
}