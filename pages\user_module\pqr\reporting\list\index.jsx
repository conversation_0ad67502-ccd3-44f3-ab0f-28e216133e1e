import { useEffect, useState } from "react";
import Head from "next/head";
import {
  <PERSON>readcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  Loader,
  RadioGroup,
  Radio,
  SelectPicker,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import EditIcon from "@rsuite/icons/Edit";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import { FormControl, FormControlLabel, FormGroup } from "@mui/material";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import { DateRangePicker } from "rsuite";

import TableWerumComponents from "@/components/pqr/TableWerumComponents";

export default function ReportingList() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_trans_header");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [transactionHeadersDataState, setTransactionHeadersDataState] =
    useState([]);
  const [ppiDataState, setPPIDataState] = useState([]);

  const [dateRange, setDateRange] = useState(null);
  const [selectedPPI, setSelectedPPI] = useState(null);
  const [selectedBatch, setSelectedBatch] = useState(null);

  const [showDetailWerumModal, setShowDetailWerumModal] = useState(false);
  const [selectedBatchCode, setSelectedBatchCode] = useState(null);

  const emptyAddTransactionHeaderForm = {
    id_trans_header: null,
    id_ppi: null,
    batch_code: "",
    iot_desc: "",
    line_desc: "",
    remarks: "",
    wetmill: "N",
    status_transaction: 2,
    create_date: null,
    create_by: "",
    update_date: null,
    update_by: null,
    delete_date: null,
    delete_by: null,
  };

  const emptyEditTransactionHeaderForm = {
    id_trans_header: null,
    id_ppi: null,
    batch_code: "",
    iot_desc: "",
    line_desc: "",
    remarks: "",
    wetmill: "N",
    status_transaction: 2,
    create_date: null,
    create_by: "",
    update_date: null,
    update_by: null,
    delete_date: null,
    delete_by: null,
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [addTransactionHeaderForm, setAddTransactionHeaderForm] = useState(
    emptyAddTransactionHeaderForm
  );
  const [editTransactionHeaderForm, setEditTransactionHeaderForm] = useState(
    emptyEditTransactionHeaderForm
  );
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  //ppi_name
  const ppiOptions = Array.from(
    new Set(transactionHeadersDataState.map((item) => item.ppi_name))
  ).map((ppiName) => ({ label: ppiName, value: ppiName }));

  //batch_code
  const batchOptions = Array.from(
    new Set(transactionHeadersDataState.map((item) => item.batch_code))
  ).map((batchCode) => ({ label: batchCode, value: batchCode }));

  const getFilteredDate = (data) => {
    let filteredData = data;

    // Date range filter
    if (dateRange && dateRange[0] && dateRange[1]) {
      const startDate = new Date(dateRange[0]).setHours(0, 0, 0, 0);
      const endDate = new Date(dateRange[1]).setHours(23, 59, 59, 999);

      filteredData = filteredData.filter((item) => {
        const itemDate = new Date(item.create_date).getTime(); // Ubah ke timestamp
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    return filteredData;
  };

  const getFilteredData = () => {
    let filteredData = transactionHeadersDataState;

    // Apply search keyword filter
    filteredData = filteredData.filter((rowData) => {
      const searchFields = [
        "id_trans_header",
        "ppi_name",
        "batch_code",
        "iot_desc",
        "line_desc",
        "remarks",
        "wetmill",
        "status_transaction",
        "create_date",
        "create_by",
        "update_date",
        "update_by",
        "delete_date",
        "delete_by",
      ];
      const matchesSearch = searchFields.some((field) =>
        rowData[field]
          ?.toString()
          .toLowerCase()
          .includes(searchKeyword.toLowerCase())
      );
      return matchesSearch;
    });

    // Apply date range filter
    filteredData = getFilteredDate(filteredData);

    // Apply PPI filter
    if (selectedPPI) {
      filteredData = filteredData.filter(
        (item) => item.ppi_name === selectedPPI
      );
    }

    // Apply Batch Code filter
    if (selectedBatch) {
      filteredData = filteredData.filter(
        (item) => item.batch_code === selectedBatch
      );
    }

    // Apply sorting
    if (sortColumn && sortType) {
      filteredData = filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }

    return filteredData;
  };

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const totalRowCount = getFilteredData().length; // Hitung total berdasarkan data yang sudah difilter

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/operator")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      getAllTransactionHeader();
    }
  }, []);

  const getAllTransactionHeader = async () => {
    try {
      const res = await ApiTransactionHeader().getAllTransactionHeader();

      console.log("res", res);
      if (res.status === 200) {
        setTransactionHeadersDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };

  const viewHandler = async (idTransHeader) => {
    const url = `${process.env.NEXT_PUBLIC_PIMS_FE}/user_module/pqr/reporting/list/pdf?idTransHeader=${parseInt(idTransHeader)}`;
    window.open(url, "_blank");
  };

  return (
    <div>
      <div>
        <Head>
          <title>List E Release</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>E Release</Breadcrumb.Item>
                  <Breadcrumb.Item>List</Breadcrumb.Item>
                  <Breadcrumb.Item active>Pengisian E Release</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>List semua E Release</h5>
              </Stack>
            }></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <Stack spacing={10}>
                    <InputGroup inside>
                      <InputGroup.Addon>
                        <SearchIcon />
                      </InputGroup.Addon>
                      <Input
                        placeholder="cari"
                        value={searchKeyword}
                        onChange={handleSearch}
                      />
                      <InputGroup.Addon
                        onClick={() => {
                          setSearchKeyword("");
                          setPage(1);
                        }}
                        style={{
                          display: searchKeyword ? "block" : "none",
                          color: "red",
                          cursor: "pointer",
                        }}>
                        <CloseOutlineIcon />
                      </InputGroup.Addon>
                    </InputGroup>
                    <DateRangePicker
                      appearance="default"
                      placeholder="Pilih Rentang Tanggal"
                      style={{ width: 230 }}
                      value={dateRange}
                      onChange={(range) => {
                        setDateRange(range);
                        setPage(1);
                      }}
                    />
                    <SelectPicker data={ppiOptions} value={selectedPPI} onChange={setSelectedPPI} placeholder="Pilih Nama PPI" style={{ width: 230 }} />
                    <SelectPicker data={batchOptions} value={selectedBatch} onChange={setSelectedBatch} placeholder="Pilih Batch Code" style={{ width: 220 }} />
                  </Stack>
                </Stack>
              }>
              <Table
                bordered
                cellBordered
                height={400}
                data={getPaginatedData(getFilteredData(), limit, page)}
                sortColumn={sortColumn}
                sortType={sortType}
                onSortColumn={handleSortColumn}
                autoHeight>
                <Column width={180} align="center" sortable fullText>
                  <HeaderCell>ID Transaksi Header</HeaderCell>
                  <Cell dataKey="id_trans_header" />
                </Column>
                <Column width={180} sortable fullText>
                  <HeaderCell align="center">Nama PPI</HeaderCell>
                  <Cell dataKey="ppi_name" />
                </Column>
                <Column width={180} sortable fullText>
                  <HeaderCell align="center">Kode Batch</HeaderCell>
                  <Cell dataKey="batch_code" />
                </Column>
                {/* <Column width={250} sortable fullText>
                  <HeaderCell align="center">Deksripsi IOT</HeaderCell>
                  <Cell dataKey="iot_desc" />
                </Column>
                <Column width={250} sortable fullText>
                  <HeaderCell align="center">Deskripsi Line</HeaderCell>
                  <Cell dataKey="line_desc" />
                </Column> */}
                <Column width={250} sortable fullText>
                  <HeaderCell align="center">Catatan</HeaderCell>
                  <Cell dataKey="remarks" />
                </Column>
                <Column width={250} align="center" sortable fullText>
                  <HeaderCell>Wetmill</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      let statusText = "";
                      if (rowData.wetmill === "Y") {
                        statusText = "Yes";
                      } else if (rowData.wetmill === "N") {
                        statusText = "No";
                      }
                      return <>{statusText}</>;
                    }}
                  </Cell>
                </Column>
                <Column width={250} align="center" sortable fullText>
                  <HeaderCell>Status Transaksi</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      let statusText = "";
                      if (rowData.status_transaction === 2) {
                        statusText = "Draft";
                      } else if (rowData.status_transaction === 1) {
                        statusText = "Done";
                      } else if (rowData.status_transaction === 0) {
                        statusText = "Dropped";
                      }
                      return <>{statusText}</>;
                    }}
                  </Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dibuat</HeaderCell>
                  <Cell>
                    {(rowData) =>
                      new Date(rowData.create_date).toLocaleDateString("en-GB")
                    }
                  </Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>
                    {(rowData) =>
                      rowData.update_date
                        ? new Date(rowData.update_date).toLocaleDateString(
                          "en-GB"
                        )
                        : ""
                    }
                  </Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>
                    {(rowData) =>
                      rowData.delete_date
                        ? new Date(rowData.delete_date).toLocaleDateString(
                          "en-GB"
                        )
                        : ""
                    }
                  </Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.delete_by}</>}</Cell>
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}>
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={180} sortable fullText>
                  <HeaderCell align="center">Status Approval</HeaderCell>
                  <Cell dataKey="status_approval">
                    {(rowData) => "Approved"}
                  </Cell>
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Disetujui Oleh</HeaderCell>
                  <Cell dataKey="approval_by">
                    {(rowData) => {
                      return rowData.approval_by || "-";
                    }}
                  </Cell>
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Tanggal Disetujui</HeaderCell>
                  <Cell>
                    {(rowData) =>
                      rowData.approval_date
                        ? new Date(rowData.approval_date).toLocaleDateString(
                          "en-GB"
                        )
                        : ""
                    }
                  </Cell>
                </Column>

                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            const idHeader = rowData.id_trans_header;
                            router.push(
                              `/user_module/pqr/reporting/list/pqr?IdHeader=${idHeader}`
                            );
                          }}>
                          <SearchIcon style={{ fontSize: "16px" }} />
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>

                <Column width={200} fixed="right" align="center">
                  <HeaderCell>Report</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="primary"
                          color="ghost"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setSelectedBatchCode(rowData.batch_code);
                            setShowDetailWerumModal(true);
                            // HandleGetDetailPQRWerum(rowData.batch_code)
                          }}>
                          Data Werum
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
                <Column width={200} fixed="right" align="center">
                  <HeaderCell>Report</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="primary"
                          color="ghost"
                          disabled={rowData.is_active === 0}
                          onClick={() => viewHandler(rowData.id_trans_header)}
                        >
                          Hasil Rekap
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditTransactionHeaderForm(emptyEditTransactionHeaderForm);
                setErrorsEditForm({});
              }}
              overflow={false}>
              <Modal.Header>
                <Modal.Title>Ubah Transaksi Header</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                    <SelectPicker
                      data={ppiDataState} // Menggunakan data PPI yang telah di-fetch
                      value={editTransactionHeaderForm.id_ppi} // Nilai yang dipilih untuk edit
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_ppi: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          id_ppi: undefined,
                        }));
                      }}
                      block
                      placeholder="Select PPI"
                      style={{ width: "100%" }}
                    />
                    {errorsEditForm.id_ppi && (
                      <p style={{ color: "red" }}>{errorsEditForm.id_ppi}</p>
                    )}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Batch Code</Form.ControlLabel>
                    <Form.Control
                      name="batch_code"
                      value={editTransactionHeaderForm.batch_code}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          batch_code: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          batch_code: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.batch_code && (
                      <p style={{ color: "red" }}>
                        {errorsEditForm.batch_code}
                      </p>
                    )}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Deskripsi IOT</Form.ControlLabel>
                    <Form.Control
                      name="iot_desc"
                      value={editTransactionHeaderForm.iot_desc}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          iot_desc: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          iot_desc: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.iot_desc && (
                      <p style={{ color: "red" }}>{errorsEditForm.iot_desc}</p>
                    )}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Deskripsi Line</Form.ControlLabel>
                    <Form.Control
                      name="line_desc"
                      value={editTransactionHeaderForm.line_desc}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          line_desc: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          line_desc: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.line_desc && (
                      <p style={{ color: "red" }}>{errorsEditForm.line_desc}</p>
                    )}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Remarks</Form.ControlLabel>
                    <Form.Control
                      name="remarks"
                      value={editTransactionHeaderForm.remarks}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          remarks: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          remarks: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.remarks && (
                      <p style={{ color: "red" }}>{errorsEditForm.remarks}</p>
                    )}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Wetmill</Form.ControlLabel>
                    <RadioGroup
                      name="wetmill"
                      inline
                      value={editTransactionHeaderForm.wetmill}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          wetmill: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          wetmill: undefined,
                        }));
                      }}>
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsEditForm.wetmill && (
                      <p style={{ color: "red" }}>{errorsEditForm.wetmill}</p>
                    )}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditTransactionHeaderForm(
                      emptyEditTransactionHeaderForm
                    );
                    setErrorsEditForm({});
                  }}
                  appearance="subtle">
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleEditTransactionHeaderApi();
                  }}
                  appearance="primary"
                  type="submit">
                  Edit
                </Button>
                {loading && (
                  <Loader
                    backdrop
                    size="md"
                    vertical
                    content="Editing Data..."
                    active={loading}
                  />
                )}
              </Modal.Footer>
            </Modal>

            <Modal
              backdrop="static"
              open={showDetailWerumModal}
              onClose={() => {
                setShowDetailWerumModal(false);
              }}
              overflow={false}
              size={"lg"}>
              <Modal.Header>
                <Modal.Title>Detail Data Werum</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <TableWerumComponents batchCode={selectedBatchCode} />
              </Modal.Body>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
