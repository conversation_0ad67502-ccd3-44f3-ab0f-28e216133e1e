import { useRouter } from "next/router";
import MainContent from "@/components/layout/MainContent";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { Dropdown } from "rsuite";
import { useEffect, useState } from "react";
import MenuApi from "../api/menuApi";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";

export default function UserAccessSetup({
  emp_id,
  emp_name,
  module_code,
  module_name,
  allParentMenuData,
}) {
  const router = useRouter();
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const MySwal = withReactContent(Swal);
  const [selectedParentMenu, setSelectedParentMenu] = useState([]);
  const [parentMenuData, setParentMenuData] = useState([]);
  module_code = parseInt(module_code);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  },[]);

  useEffect(() => {
    if (allParentMenuData) {
      setParentMenuData(allParentMenuData);
    }
  }, [allParentMenuData]);

  //   dropdown pilih parent menu
  const parentMenuChangeHandler = (value) => {
    setSelectedParentMenu({ ...value });
  };

  // handler submit
  const submitFormHandler = async (event) => {
    event.preventDefault();
    setIsFormDisabled(true);

    if (!selectedParentMenu.Id_Menu) {
      setIsFormDisabled(false);
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please select a parent menu !",
      });
      return;
    }

    const data = {
      emp_id: emp_id,
      emp_name: emp_name,
      module_code: module_code,
      module_name: module_name,
      parent_menu_id: selectedParentMenu.Id_Menu,
      parent_menu_name: selectedParentMenu.Menu_Name,
      parent_menu_is_parent: selectedParentMenu.Is_Parent,
      parent_menu_link_code: selectedParentMenu.Menu_Link_Code,
    };

    router.push({
      pathname: "/user_management/userAccessChildMenu",
      query: { ...data },
    });
  };

  return (
    <>
      <div>
        <Head>
          <title>User Access :: Parent Menu Setup</title>
        </Head>
      </div>
      <ContainerLayout
        title="User Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <h4>Form User Access </h4>
          <h6></h6>
          <div className="p-5">
            <form onSubmit={submitFormHandler}>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Employee ID
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  value={emp_id}
                  id="employeeId"
                  aria-describedby="validationServer03Feedback"
                  disabled={true}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Employee Name
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  value={emp_name}
                  id="employeeName"
                  aria-describedby="validationServer03Feedback"
                  disabled={true}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Module Name
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  value={module_name}
                  id="moduleName"
                  aria-describedby="validationServer03Feedback"
                  disabled={true}
                />
              </div>

              <div className="row mb-3">
                <label>Pilih Parent Menu : </label>
                <div className="col-sm-9">
                  <Dropdown
                    disabled={isFormDisabled}
                    title={
                      selectedParentMenu.Menu_Name
                        ? selectedParentMenu.Menu_Name
                        : "-- Select Parent Menu --"
                    }
                    onSelect={parentMenuChangeHandler}
                  >
                    <Dropdown.Item eventKey="">
                      -- Select Parent Menu --
                    </Dropdown.Item>
                    {parentMenuData.length > 0 &&
                      parentMenuData.map((item) => (
                        <Dropdown.Item key={item.Id_Menu} eventKey={item}>
                          {item.Menu_Name}
                        </Dropdown.Item>
                      ))}
                  </Dropdown>
                </div>
              </div>

              <button
                disabled={isFormDisabled}
                type="submit"
                className="btn btn-primary p-2"
              >
                Submit
              </button>
              <button
                disabled={isFormDisabled}
                type="button"
                className="btn btn-dark mx-2 p-2"
                onClick={() => router.back()}
              >
                Cancel
              </button>
            </form>
          </div>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  const { emp_id, emp_name, module_code, module_name } = query;

  const { GetParentmenuDataApi } = MenuApi();

  const { Data: allParentMenu } = await GetParentmenuDataApi(module_code);
  let allParentMenuData = [];
  if (allParentMenu != undefined) {
    allParentMenuData = allParentMenu;
  }

  return {
    props: {
      emp_id,
      emp_name,
      module_code,
      module_name,
      allParentMenuData,
    },
  };
}
