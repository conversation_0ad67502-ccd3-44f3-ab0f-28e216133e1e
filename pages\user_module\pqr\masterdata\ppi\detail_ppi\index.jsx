import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, Notification, SelectPicker, RadioGroup, Radio, InputNumber, FlexboxGrid, Grid, Col } from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon, Copy as CopyIcon } from "@rsuite/icons";

import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import EditIcon from "@rsuite/icons/Edit";

import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";

//import api section
import ApiMasterdata_ppi from "@/pages/api/pqr/ppi/api_masterdata_ppi";
import ApiPQR from "@/pages/api/pqr/parameter/api_PQR";
import ApiBindingParamPpi from "@/pages/api/pqr/binding_param_ppi/api_binding_param_ppi";

export default function DetailPPI() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("order_no");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const toaster = useToaster();

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);

  const [bindingParamPpiDataState, setBindingParamPpiDataState] = useState([]);
  const [wetmillYData, setWetmillYData] = useState([]);
  const [wetmillNData, setWetmillNData] = useState([]);
  const [ppiDataState, setPpiDataState] = useState([]);
  const [prmDataState, setprmDataState] = useState([]);
  const [bindingIdToDelete, setBindingIdToDelete] = useState(null);

  const [idRouter, setIdRouter] = useState(null);

  //pagination wetmill
  const [pageY, setPageY] = useState(1);
  const [pageN, setPageN] = useState(1);
  const [limitY, setLimitY] = useState(10);
  const [limitN, setLimitN] = useState(10);

  const router = useRouter();
  const { Id } = router.query;

  const [formData, setFormData] = useState({
    id_ppi: null,
    ppi_name: null,
    created_date: "",
    created_by: "",
    updated_date: "",
    updated_by: "",
    deleted_date: "",
    deleted_by: "",
  });

  const emptyAddBindingParamPpiForm = {
    id_parameter: null,
    order_no: null,
    binding_type: null,
    min_value: null,
    max_value: null,
    absolute_value: null,
    description_value: null,
    wetmill: "N",
    created_by: null,
  };

  const emptyEditBindingParamPpiForm = {
    id_parameter: null,
    order_no: null,
    binding_type: null,
    min_value: null,
    max_value: null,
    absolute_value: null,
    description_value: null,
    wetmill: "N",
    created_by: null,
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [addLoading, setAddLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [copy, setCopy] = useState([]);
  const [statusLoading, setStatusLoading] = useState(null);

  const [addBindingParamPpiForm, setAddBindingParamPpiForm] = useState(emptyAddBindingParamPpiForm);
  const [editBindingParamPpiForm, setEditBindingParamPpiForm] = useState(emptyEditBindingParamPpiForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimitY = (dataKey) => {
    setPageY(1);
    setLimitY(dataKey);
  };

  const handleChangeLimitN = (dataKey) => {
    setPageN(1);
    setLimitN(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const filteredData = wetmillYData.filter((rowData, i) => {
    const searchFields = [
      "id_binding",
      "ppi_name",
      "parameter_name",
      "uom",
      "flag_controllable",
      "order_no",
      "min_value",
      "max_value",
      "wetmill",
      "created_date",
      "created_by",
      "updated_date",
      "updated_by",
      "deleted_date",
      "deleted_by",
      "is_active",
    ];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const filteredDataN = wetmillNData.filter((rowData, i) => {
    const searchFields = [
      "id_binding",
      "ppi_name",
      "parameter_name",
      "uom",
      "flag_controllable",
      "is_automate",
      "order_no",
      "min_value",
      "max_value",
      "wetmill",
      "created_date",
      "created_by",
      "updated_date",
      "updated_by",
      "deleted_date",
      "deleted_by",
      "is_active",
    ];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = (data) => {
    if (sortColumn && sortType) {
      return data.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return data;
  };

  const totalRowCountY = searchKeyword ? filteredData.filter((rowData) => rowData.wetmill === "Y").length : wetmillYData.filter((rowData) => rowData.wetmill === "Y").length;
  const totalRowCountN = searchKeyword ? filteredData.filter((rowData) => rowData.wetmill === "N").length : wetmillNData.filter((rowData) => rowData.wetmill === "N").length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push("/");
    } else if (Id) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/masterdata")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      setIdRouter(Id);
      HandleGetDetail(Id);
      HandleGetWetmillN(Id);
      HandleGetWetmillY(Id);
      HandleGetAllBindingParamPpiApi(Id);
      HandleGetAllPPIApi();
      HandleGetAllPQRApi();
    }
  }, [Id]);

  const HandleGetDetail = async (id_ppi) => {
    try {
      const api = ApiMasterdata_ppi();
      const response = await api.GetMasterPPIById({ id_ppi: parseInt(id_ppi) });

      if (response.status === 200) {
        const data = response.data;
        setFormData({
          id_ppi: data.id_ppi,
          ppi_name: data.ppi_name,
          product_code: data.product_code,
          created_date: new Date(data.created_date).toLocaleDateString("en-GB"),
          created_by: data.created_by,
          updated_date: data.updated_date ? new Date(data.updated_date).toLocaleDateString("en-GB") : "-",
          updated_by: data.updated_by || "-",
          deleted_date: data.deleted_date ? new Date(data.deleted_date).toLocaleDateString("en-GB") : "-",
          deleted_by: data.deleted_by || "-",
        });
      } else {
        console.error("Failed to fetch detail data");
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
    }
  };
  const HandleGetWetmillY = async (id_ppi) => {
    try {
      const api = ApiBindingParamPpi();
      const response = await api.getIdPpiActiveBindingParamPpiWetmillY({ id_ppi: parseInt(id_ppi) });

      if (response.status === 200) {
        setWetmillYData(response.data || []); // Set ke array kosong jika data null
      } else if (response.status === 400) {
        // Jika respons 400, set data ke array kosong
        setWetmillYData([]);
        console.log(response.message); // Log pesan dari backend
      } else {
        console.error("Failed to fetch wetmill Y data");
        setWetmillYData([]); // Set ke array kosong untuk kasus lain
      }
    } catch (error) {
      console.error("Error fetching wetmill Y data:", error);
      setWetmillYData([]); // Set ke array kosong jika terjadi error
    }
  };

  const HandleGetWetmillN = async (id_ppi) => {
    try {
      const api = ApiBindingParamPpi();
      const response = await api.getIdPpiActiveBindingParamPpiWetmillN({ id_ppi: parseInt(id_ppi) });

      if (response.status === 200) {
        setWetmillNData(response.data || []);
      } else if (response.status === 400) {
        setWetmillNData([]);
        console.log(response.message);
      } else {
        console.error("Failed to fetch wetmill N data");
        setWetmillNData([]);
      }
    } catch (error) {
      console.error("Error fetching wetmill N data:", error);
      setWetmillNData([]); // Set ke array kosong jika terjadi error
    }
  };

  const HandleGetAllPPIApi = async () => {
    try {
      const res = await ApiMasterdata_ppi().getAllActiveMasterPPI();
      if (res.status === 200) {
        const options = res.data.map((ppi) => ({
          label: ppi.ppi_name,
          value: ppi.id_ppi,
        }));
        setPpiDataState(options);
      } else {
        console.log("Error fetching PPI options", res.message);
      }
    } catch (error) {
      console.log("Error fetching PPI options", error);
    }
  };

  const HandleGetAllPQRApi = async () => {
    try {
      const res = await ApiPQR().getAllActivePQR();
      if (res.status === 200) {
        const options = res.data.map((prm) => ({
          label: prm.parameter_name,
          value: prm.id_parameter,
        }));
        setprmDataState(options);
      } else {
        console.log("Error fetching Parameter options", res.message);
      }
    } catch (error) {
      console.log("Error fetching Parameter options", error);
    }
  };

  const HandleGetAllBindingParamPpiApi = async (id_ppi) => {
    try {
      const res = await ApiBindingParamPpi().getIdPpiActiveBindingParamPpi({ id_ppi: parseInt(id_ppi) });

      console.log("res", res);
      if (res.status === 200) {
        setBindingParamPpiDataState(res.data);
      } else {
        console.log("error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Api", error);
    }
  };

  const HandleAddBindingParamPpiApi = async () => {
    const errors = {};

    if (!addBindingParamPpiForm.id_parameter) {
      errors.id_parameter = "Parameter Name is required";
    }

    // Check for binding type and validate based on the selected type
    if (!addBindingParamPpiForm.binding_type) {
      errors.binding_type = "Binding Type is required";
    } else {
      switch (addBindingParamPpiForm.binding_type) {
        case 1: // Range
          if (addBindingParamPpiForm.min_value === null || addBindingParamPpiForm.min_value === undefined) {
            errors.min_value = "Min Value is required for Range binding type";
          }
          if (addBindingParamPpiForm.max_value === null || addBindingParamPpiForm.max_value === undefined) {
            errors.max_value = "Max Value is required for Range binding type";
          } else if (addBindingParamPpiForm.max_value < addBindingParamPpiForm.min_value) {
            errors.max_value = "Max Value must be greater than Min Value";
          }
          break;

        case 2: // Absolute
          if (addBindingParamPpiForm.absolute_value === null || addBindingParamPpiForm.absolute_value === undefined) {
            errors.absolute_value = "Absolute Value is required for Absolute binding type";
          }
          break;

        case 3: // Description
          if (!addBindingParamPpiForm.description_value) {
            errors.description_value = "Description Value is required for Description binding type";
          }
          break;

        default:
          break;
      }
    }

    if (!addBindingParamPpiForm.order_no) {
      errors.order_no = "Order No is required";
    }

    // Check for wetmill
    if (!addBindingParamPpiForm.wetmill) {
      errors.wetmill = "Wetmill is required";
    }

    // Check for duplicate parameters
    const isDuplicate = bindingParamPpiDataState.some((param) => param.id_ppi === addBindingParamPpiForm.id_ppi && param.id_parameter === addBindingParamPpiForm.id_parameter && param.wetmill === addBindingParamPpiForm.wetmill);

    if (isDuplicate) {
      errors.id_parameter = "Parameter name already exists";
      showNotification("error", "This parameter already been used for this PPI");
    }

    // If there are any errors, set them in the state and stop further execution
    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }

    // Proceed with API call if validation passed
    try {
      setAddLoading(true);
      const res = await ApiBindingParamPpi().createBindingParamPpi({
        ...addBindingParamPpiForm,
        id_ppi: parseInt(idRouter),
        created_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        setAddBindingParamPpiForm(emptyAddBindingParamPpiForm);
        setShowAddModal(false);
        await HandleGetAllBindingParamPpiApi(Id);
        await HandleGetWetmillN(Id);
        await HandleGetWetmillY(Id);
        showNotification("success", "Added successfully");
      } else if (res.status === 400) {
        showNotification("error", "This parameter already been used for this PPI");
      } else {
        showNotification("error", "Failed to add data");
      }
    } catch (error) {
      showNotification("error", "Error adding data");
    } finally {
      setAddLoading(false);
    }
  };

  const HandleEditBindingParamPpiApi = async () => {
    const errors = {};

    // Check each field and only set the error if it's empty

    if (!editBindingParamPpiForm.id_parameter) {
      errors.id_parameter = "Parameter Name is required";
    }

    const isDuplicate = bindingParamPpiDataState.some(
      (param) =>
        param.id_ppi === editBindingParamPpiForm.id_ppi &&
        param.id_parameter === editBindingParamPpiForm.id_parameter &&
        param.wetmill === editBindingParamPpiForm.wetmill &&
        param.id_binding !== editBindingParamPpiForm.id_binding &&
        param.is_active === 1 // Menambahkan validasi untuk is_active
    );

    if (isDuplicate) {
      errors.id_parameter = "Parameter name already exists";
      showNotification("error", "Parameter name already exists");
    }

    if (!editBindingParamPpiForm.order_no) {
      errors.order_no = "Order No is required";
    }
    // Check for binding type and validate based on the selected type
    if (!editBindingParamPpiForm.binding_type) {
      errors.binding_type = "Binding Type is required";
    } else {
      switch (editBindingParamPpiForm.binding_type) {
        case 1: // Range
          if (editBindingParamPpiForm.min_value === null || editBindingParamPpiForm.min_value === undefined) {
            errors.min_value = "Min Value is required for Range binding type";
          }
          if (editBindingParamPpiForm.max_value === null || editBindingParamPpiForm.max_value === undefined) {
            errors.max_value = "Max Value is required for Range binding type";
          } else if (editBindingParamPpiForm.max_value < editBindingParamPpiForm.min_value) {
            errors.max_value = "Max Value must be greater than Min Value";
          }
          break;

        case 2: // Absolute
          if (editBindingParamPpiForm.absolute_value === null || editBindingParamPpiForm.absolute_value === undefined) {
            errors.absolute_value = "Absolute Value is required for Absolute binding type";
          }
          break;

        case 3: // Description
          if (!editBindingParamPpiForm.description_value) {
            errors.description_value = "Description Value is required for Description binding type";
          }
          break;

        default:
          break;
      }
    }
    if (!editBindingParamPpiForm.wetmill) {
      errors.wetmill = "wetmill is required";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);

      return;
    }
    try {
      setEditLoading(true);
      const res = await ApiBindingParamPpi().editBindingParamPpi({
        ...editBindingParamPpiForm,
        id_ppi: parseInt(idRouter),
        updated_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        await HandleGetAllBindingParamPpiApi(Id);
        await HandleGetWetmillN(Id);
        await HandleGetWetmillY(Id);
        setShowEditModal(false);
        showNotification("success", "Data updated successfully");
      } else {
        showNotification("error", "Failed to update data");
      }
    } catch (error) {
      toaster.push({ message: "Error updating data", type: "error" });
    } finally {
      setEditLoading(false);
    }
  };

  const handleEditStatusBindingParamPpiApi = async (id_binding, is_active) => {
    try {
      setStatusLoading(id_binding);
      const res = await ApiBindingParamPpi().editStatusBindingParamPpi({
        id_binding,
        is_active,
        deleted_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        await HandleGetWetmillN(Id);
        await HandleGetWetmillY(Id);
        await HandleGetAllBindingParamPpiApi(Id);
        showNotification("success", "Deleted Data successfully");
      } else {
        showNotification("error", "Failed to delete data");
      }
    } catch (error) {
      showNotification("error", "Error deleting data");
    } finally {
      setAddLoading(false);
    }
  };
  const handleCopyWetmill = async (rowData) => {
    const newWetmillValue = rowData.wetmill === "Y" ? "N" : "Y";
    const updatedRowData = {
      ...rowData,
      wetmill: newWetmillValue,
      id_binding: undefined,
    };

    // Update state
    setCopy((prevData) => {
      if (!Array.isArray(prevData)) {
        return []; // Kembalikan array kosong jika prevData bukan array
      }
      return prevData.map((item) => (item.id === updatedRowData.id ? updatedRowData : item));
    });

    try {
      const res = await ApiBindingParamPpi().createBindingParamPpi({
        ...updatedRowData,
      });

      if (res.status === 200) {
        setAddBindingParamPpiForm(emptyAddBindingParamPpiForm);
        await HandleGetAllBindingParamPpiApi(Id);
        await HandleGetWetmillN(Id);
        await HandleGetWetmillY(Id);
        showNotification("success", "Copy Data successfully");
      } else if (res.status === 400) {
        showNotification("error", "This parameter already been used for this PPI");
      } else {
        showNotification("error", "Failed to add data");
      }
    } catch (error) {
      showNotification("error", "Error adding data");
    } finally {
      setAddLoading(false);
    }
  };

  const HandleCopyAllWetmill = async (sourceWetmill) => {
    const targetWetmill = sourceWetmill === 'Y' ? 'N' : 'Y';

    const payload = {
      id_ppi: parseInt(idRouter),
      created_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
      source_wetmill: sourceWetmill,
      target_wetmill: targetWetmill,
    };

    try {
      setAddLoading(true); // Menggunakan state loading untuk menampilkan indikator loading
      const res = await ApiBindingParamPpi().copyAllWetmillParamPpi(payload);

      if (res.status === 200) {
        await HandleGetAllBindingParamPpiApi(Id);
        await HandleGetWetmillN(Id);
        await HandleGetWetmillY(Id);
        showNotification("success", "Semua binding parameter berhasil disalin");
      } else if (res.status === 400) {
        const errorMessage =
          sourceWetmill === 'Y'
            ? "Semua parameter sudah ada pada tabel wetmill NO"
            : "Semua parameter sudah ada pada tabel wetmill YES";
        showNotification("error", errorMessage)
      } else {
        showNotification("error", "Gagal menyalin binding parameter");
      }
    } catch (error) {
      showNotification("error", error.message || "Terjadi kesalahan saat menyalin binding parameter");
    } finally {
      setAddLoading(false);
    }
  };

  return (
    <div>
      <Head>
        <title>PPI Detail page</title>
      </Head>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item>PPI</Breadcrumb.Item>
                  <Breadcrumb.Item active>Detail</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>PPI Detail {idRouter}</h5>
              </Stack>
            }
          />

          {/* Detail Section */}
          <Panel bordered>
            <Form fluid>
              {/* bagian atas */}
              <FlexboxGrid className="mb-4">
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Id PPI</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.id_ppi || "-"} readOnly />
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Product Code</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.product_code || "-"} readOnly />
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>PPI name</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.ppi_name || "-"} readOnly />
                </FlexboxGrid.Item>
              </FlexboxGrid>
              <FlexboxGrid>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Created Date</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.created_date || "-"} readOnly />
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Updated Date</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.updated_date || "-"} readOnly />
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Created By </Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.created_by || "-"} readOnly />
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Updated By </Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.updated_by || "-"} readOnly />
                </FlexboxGrid.Item>
              </FlexboxGrid>
              <FlexboxGrid>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Deleted Date</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.deleted_date || "-"} readOnly />
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Deleted By</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.deleted_by || "-"} readOnly />
                </FlexboxGrid.Item>
              </FlexboxGrid>
            </Form>
          </Panel>
          {/* datatable section */}
          <div className="mt-3">
            <div>
              <FlexboxGrid>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={24} sm={24} xs={24}>
                  {/* Wetmill YES Table */}
                  <div className="mb-2">
                    <Panel
                      bordered
                      bodyFill
                      className="mb-1"
                      header={
                        <Stack justifyContent="space-between">
                          <h5>Parameter wetmill YES</h5>
                        </Stack>
                      }
                    />
                    <Panel
                      bordered
                      bodyFill
                      header={
                        <Stack justifyContent="space-between">
                          <div className="flex gap-0.5">
                            <IconButton
                              icon={<PlusRoundIcon />}
                              appearance="primary"
                              onClick={() => {
                                setShowAddModal(true);
                              }}
                            >
                              Add
                            </IconButton>
                            <Button
                              appearance="primary"
                              onClick={() => HandleCopyAllWetmill('Y')} // Panggil fungsi langsung
                              disabled={wetmillYData.length === 0 || addLoading} // Nonaktifkan jika data kosong atau sedang loading
                            >
                              Duplicate to NO
                            </Button>
                          </div>
                          <InputGroup inside>
                            <InputGroup.Addon>
                              <SearchIcon />
                            </InputGroup.Addon>
                            <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                            <InputGroup.Addon
                              onClick={() => {
                                setSearchKeyword("");
                                setPage(1);
                              }}
                              style={{
                                display: searchKeyword ? "block" : "none",
                                color: "red",
                                cursor: "pointer",
                              }}
                            >
                              <CloseOutlineIcon />
                            </InputGroup.Addon>
                          </InputGroup>
                        </Stack>
                      }
                    >
                      <Table
                        bordered
                        cellBordered
                        height={400}
                        data={wetmillYData && wetmillYData.length > 0 ? getPaginatedData(getFilteredData(filteredData), limitY, pageY) : []}
                        sortColumn={sortColumn}
                        sortType={sortType}
                        onSortColumn={handleSortColumn}
                        locale={{ emptyMessage: "No data found" }} // Tambahkan pesan kosong
                      >
                        <Column width={100} sortable fullText defaultsortorder="asc">
                          <HeaderCell align="center">Order No</HeaderCell>
                          <Cell dataKey="order_no" align="center" />
                        </Column>
                        <Column width={250} sortable fullText>
                          <HeaderCell align="center">Parameter Name</HeaderCell>
                          <Cell dataKey="parameter_name" />
                        </Column>
                        <Column width={250} sortable fullText>
                          <HeaderCell align="center">Parameter Value</HeaderCell>
                          <Cell>
                            {(rowData) => {
                              let valueToDisplay;

                              if (rowData.binding_type === 1) {
                                valueToDisplay = `${rowData.min_value.toLocaleString("id-ID")} - ${rowData.max_value.toLocaleString("id-ID")}`;
                              } else if (rowData.binding_type === 2) {
                                valueToDisplay = rowData.absolute_value.toLocaleString("id-ID");
                              } else if (rowData.binding_type === 3) {
                                valueToDisplay = rowData.description_value || "-";
                              } else {
                                valueToDisplay = "-";
                              }

                              return <span>{valueToDisplay}</span>;
                            }}
                          </Cell>
                        </Column>

                        <Column width={135} sortable fullText>
                          <HeaderCell align="center">UOM</HeaderCell>
                          <Cell dataKey="uom" />
                        </Column>
                        <Column width={135} sortable fullText>
                          <HeaderCell align="center">Flag Controllable</HeaderCell>
                          <Cell dataKey="flag_controllable">{(rowData) => (rowData.flag_controllable === "Y" ? "Yes" : "No")}</Cell>
                        </Column>
                        <Column width={135} sortable fullText>
                          <HeaderCell align="center">Is Automate</HeaderCell>
                          <Cell dataKey="is_automate">{(rowData) => (rowData.flag_controllable === "Y" ? "Yes" : "No")}</Cell>
                        </Column>
                        <Column width={175} sortable resizable align="center" fullText>
                          <HeaderCell>Created Date</HeaderCell>
                          <Cell>{(rowData) => new Date(rowData.created_date).toLocaleDateString("en-GB")}</Cell>
                        </Column>
                        <Column width={250} sortable resizable fullText>
                          <HeaderCell align="center">Created By</HeaderCell>
                          <Cell>{(rowData) => <>{rowData.created_by}</>}</Cell>
                        </Column>
                        <Column width={175} sortable resizable align="center" fullText>
                          <HeaderCell>Updated Date</HeaderCell>
                          <Cell>{(rowData) => (rowData.updated_date ? new Date(rowData.updated_date).toLocaleDateString("en-GB") : "-")}</Cell>
                        </Column>
                        <Column width={250} sortable resizable fullText>
                          <HeaderCell align="center">Updated By</HeaderCell>
                          <Cell>{(rowData) => rowData.updated_by ?? "-"}</Cell>
                        </Column>
                        <Column width={120} sortable resizable align="center" fullText>
                          <HeaderCell>Status</HeaderCell>
                          <Cell>
                            {(rowData) => (
                              <span
                                style={{
                                  color: rowData.is_active === 1 ? "green" : "red",
                                }}
                              >
                                {rowData.is_active === 1 ? "Active" : "Inactive"}
                              </span>
                            )}
                          </Cell>
                        </Column>
                        <Column fixed="right" align="center" width={120}>
                          <HeaderCell>Action</HeaderCell>
                          <Cell>
                            {(rowData) => (
                              <FlexboxGrid justify="space-between" align="middle">
                                <FlexboxGrid.Item as={Col} colspan={24} lg={8} md={8} sm={8} xs={8}>
                                  <Button
                                    appearance="subtle"
                                    disabled={rowData.is_active === 0}
                                    onClick={() => {
                                      setShowEditModal(true);
                                      setEditBindingParamPpiForm({
                                        ...editBindingParamPpiForm,
                                        id_binding: rowData.id_binding,
                                        id_ppi: rowData.id_ppi,
                                        id_parameter: rowData.id_parameter,
                                        order_no: rowData.order_no,
                                        binding_type: rowData.binding_type,
                                        min_value: rowData.min_value,
                                        max_value: rowData.max_value,
                                        absolute_value: rowData.absolute_value,
                                        description_value: rowData.description_value,
                                        wetmill: rowData.wetmill,
                                        updated_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                                      });
                                    }}
                                  >
                                    <EditIcon style={{ fontSize: "16px" }} />
                                  </Button>

                                  <Button onClick={() => handleCopyWetmill(rowData)}>
                                    <CopyIcon style={{ fontSize: "16px" }} />
                                  </Button>

                                  {/* <Button appearance="subtle" onClick={() => handleEditStatusBindingParamPpiApi(rowData.id_binding, rowData.is_active)}>
                                    {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                                  </Button> */}
                                  <Button
                                    onClick={() => {
                                      setBindingIdToDelete(rowData.id_binding);
                                      setShowDeleteModal(true);
                                    }}
                                  >
                                    <TrashIcon style={{ fontSize: "16px" }} />
                                  </Button>
                                </FlexboxGrid.Item>
                              </FlexboxGrid>
                            )}
                          </Cell>
                        </Column>
                      </Table>
                      <div style={{ padding: 20 }}>
                        <Pagination
                          prev
                          next
                          first
                          last
                          ellipsis
                          boundaryLinks
                          maxButtons={5}
                          size="xs"
                          layout={["total", "-", "limit", "|", "pager", "skip"]}
                          limitOptions={[10, 30, 50]}
                          total={totalRowCountY}
                          limit={limitY}
                          activePage={pageY}
                          onChangePage={setPageY}
                          onChangeLimit={handleChangeLimitY}
                        />
                      </div>
                    </Panel>
                  </div>
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={24} sm={24} xs={24}>
                  {/* Wetmill NO Table */}
                  <div>
                    <Panel
                      bordered
                      bodyFill
                      className="mb-1"
                      header={
                        <Stack justifyContent="space-between">
                          <h5>Parameter wetmill NO</h5>
                        </Stack>
                      }
                    />
                    <Panel
                      bordered
                      bodyFill
                      header={
                        <Stack justifyContent="space-between">
                          <div className="flex gap-2">
                            <Button
                              appearance="primary"
                              onClick={() => HandleCopyAllWetmill('N')} // Panggil fungsi langsung
                              disabled={wetmillNData.length === 0 || addLoading} // Nonaktifkan jika data kosong atau sedang loading
                            >
                              Duplicate to YES
                            </Button>
                          </div>
                          <InputGroup inside>
                            <InputGroup.Addon>
                              <SearchIcon />
                            </InputGroup.Addon>
                            <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                            <InputGroup.Addon
                              onClick={() => {
                                setSearchKeyword("");
                                setPage(1);
                              }}
                              style={{
                                display: searchKeyword ? "block" : "none",
                                color: "red",
                                cursor: "pointer",
                              }}
                            >
                              <CloseOutlineIcon />
                            </InputGroup.Addon>
                          </InputGroup>
                        </Stack>
                      }
                    >
                      <Table
                        bordered
                        cellBordered
                        height={400}
                        data={wetmillNData && wetmillNData.length > 0 ? getPaginatedData(getFilteredData(filteredDataN), limitN, pageN) : []}
                        sortColumn={sortColumn}
                        sortType={sortType}
                        onSortColumn={handleSortColumn}
                      >
                        <Column width={100} sortable fullText defaultsortorder="asc">
                          <HeaderCell align="center">Order No</HeaderCell>
                          <Cell dataKey="order_no" align="center" />
                        </Column>
                        <Column width={250} sortable fullText>
                          <HeaderCell align="center">Parameter Name</HeaderCell>
                          <Cell dataKey="parameter_name" />
                        </Column>
                        <Column width={250} sortable fullText>
                          <HeaderCell align="center">Parameter Value</HeaderCell>
                          <Cell>
                            {(rowData) => {
                              let valueToDisplay;

                              if (rowData.binding_type === 1) {
                                valueToDisplay = `${rowData.min_value.toLocaleString("id-ID")} - ${rowData.max_value.toLocaleString("id-ID")}`;
                              } else if (rowData.binding_type === 2) {
                                valueToDisplay = rowData.absolute_value.toLocaleString("id-ID");
                              } else if (rowData.binding_type === 3) {
                                valueToDisplay = rowData.description_value || "-";
                              } else {
                                valueToDisplay = "-";
                              }

                              return <span>{valueToDisplay}</span>;
                            }}
                          </Cell>
                        </Column>

                        <Column width={135} sortable fullText>
                          <HeaderCell align="center">UOM</HeaderCell>
                          <Cell dataKey="uom" />
                        </Column>
                        <Column width={135} sortable fullText>
                          <HeaderCell align="center">Flag Controllable</HeaderCell>
                          <Cell dataKey="flag_controllable">{(rowData) => (rowData.flag_controllable === "Y" ? "Yes" : "No")}</Cell>
                        </Column>
                        <Column width={135} sortable fullText>
                          <HeaderCell align="center">Is Automate</HeaderCell>
                          <Cell dataKey="is_automate">{(rowData) => (rowData.flag_controllable === "Y" ? "Yes" : "No")}</Cell>
                        </Column>
                        <Column width={175} sortable resizable align="center" fullText>
                          <HeaderCell>Created Date</HeaderCell>
                          <Cell>{(rowData) => new Date(rowData.created_date).toLocaleDateString("en-GB")}</Cell>
                        </Column>
                        <Column width={250} sortable resizable fullText>
                          <HeaderCell align="center">Created By</HeaderCell>
                          <Cell>{(rowData) => <>{rowData.created_by}</>}</Cell>
                        </Column>
                        <Column width={175} sortable resizable align="center" fullText>
                          <HeaderCell>Updated Date</HeaderCell>
                          <Cell>{(rowData) => (rowData.updated_date ? new Date(rowData.updated_date).toLocaleDateString("en-GB") : "-")}</Cell>
                        </Column>
                        <Column width={250} sortable resizable fullText>
                          <HeaderCell align="center">Updated By</HeaderCell>
                          <Cell>{(rowData) => rowData.updated_by ?? "-"}</Cell>
                        </Column>
                        <Column width={120} sortable resizable align="center" fullText>
                          <HeaderCell>Status</HeaderCell>
                          <Cell>
                            {(rowData) => (
                              <span
                                style={{
                                  color: rowData.is_active === 1 ? "green" : "red",
                                }}
                              >
                                {rowData.is_active === 1 ? "Active" : "Inactive"}
                              </span>
                            )}
                          </Cell>
                        </Column>
                        <Column fixed="right" align="center" width={120}>
                          <HeaderCell>Action</HeaderCell>
                          <Cell>
                            {(rowData) => (
                              <FlexboxGrid justify="space-between" align="middle">
                                <FlexboxGrid.Item as={Col} colspan={24} lg={8} md={8} sm={8} xs={8}>
                                  <Button
                                    appearance="subtle"
                                    disabled={rowData.is_active === 0}
                                    onClick={() => {
                                      setShowEditModal(true);
                                      setEditBindingParamPpiForm({
                                        ...editBindingParamPpiForm,
                                        id_binding: rowData.id_binding,
                                        id_ppi: rowData.id_ppi,
                                        id_parameter: rowData.id_parameter,
                                        order_no: rowData.order_no,
                                        binding_type: rowData.binding_type,
                                        min_value: rowData.min_value,
                                        max_value: rowData.max_value,
                                        absolute_value: rowData.absolute_value,
                                        description_value: rowData.description_value,
                                        wetmill: rowData.wetmill,
                                        updated_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                                      });
                                    }}
                                  >
                                    <EditIcon style={{ fontSize: "16px" }} />
                                  </Button>

                                  <Button onClick={() => handleCopyWetmill(rowData)}>
                                    <CopyIcon style={{ fontSize: "16px" }} />
                                  </Button>

                                  <Button
                                    onClick={() => {
                                      setBindingIdToDelete(rowData.id_binding);
                                      setShowDeleteModal(true);
                                    }}
                                  >
                                    <TrashIcon style={{ fontSize: "16px" }} />
                                  </Button>
                                </FlexboxGrid.Item>
                              </FlexboxGrid>
                            )}
                          </Cell>
                        </Column>
                      </Table>
                      <div style={{ padding: 20 }}>
                        <Pagination
                          prev
                          next
                          first
                          last
                          ellipsis
                          boundaryLinks
                          maxButtons={5}
                          size="xs"
                          layout={["total", "-", "limit", "|", "pager", "skip"]}
                          limitOptions={[10, 30, 50]}
                          total={totalRowCountN}
                          limit={limitN}
                          activePage={pageN}
                          onChangePage={setPageN}
                          onChangeLimit={handleChangeLimitN}
                        />
                      </div>
                    </Panel>
                  </div>
                </FlexboxGrid.Item>
              </FlexboxGrid>
            </div>
            {/* modal pop up untuk add binding */}
            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                if (!addLoading) {
                  setShowAddModal(false);
                  setAddBindingParamPpiForm(emptyAddBindingParamPpiForm);
                  setErrorsAddForm({});
                }
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Add Binding</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Parameter Name</Form.ControlLabel>
                    <SelectPicker
                      data={prmDataState}
                      value={addBindingParamPpiForm.id_parameter}
                      onChange={(value) => {
                        setAddBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_parameter: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          id_parameter: undefined,
                        }));
                      }}
                      block
                      placeholder="Select Parameter"
                      style={{ width: "100%" }}
                    />
                    {errorsAddForm.id_parameter && <p style={{ color: "red" }}>{errorsAddForm.id_parameter}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Order No</Form.ControlLabel>
                    <Form.Control
                      name="order_no"
                      value={addBindingParamPpiForm.order_no}
                      onChange={(value) => {
                        if (parseInt(value) >= 0 || value === "") {
                          setAddBindingParamPpiForm((prevFormValue) => ({
                            ...prevFormValue,
                            order_no: value === "" ? "" : parseInt(value),
                          }));
                          setErrorsAddForm((prevErrors) => ({
                            ...prevErrors,
                            order_no: undefined,
                          }));
                        }
                      }}
                      type="number"
                      min={0}
                    />
                    {errorsAddForm.order_no && <p style={{ color: "red" }}>{errorsAddForm.order_no}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Wetmill</Form.ControlLabel>
                    <RadioGroup
                      name="wetmill"
                      inline
                      value={addBindingParamPpiForm.wetmill}
                      onChange={(value) => {
                        setAddBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          wetmill: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          wetmill: undefined,
                        }));
                      }}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsAddForm.wetmill && <p style={{ color: "red" }}>{errorsAddForm.wetmill}</p>}
                  </Form.Group>

                  {/* SelectPicker untuk binding_type */}
                  <Form.Group>
                    <Form.ControlLabel>Binding Type</Form.ControlLabel>
                    <SelectPicker
                      data={[
                        { label: "Range", value: 1 },
                        { label: "Absolute", value: 2 },
                        { label: "Description", value: 3 },
                      ]}
                      value={addBindingParamPpiForm.binding_type}
                      onChange={(value) => {
                        // Reset form fields based on binding_type change
                        let resetValues = {};
                        if (value === 1) {
                          // Reset absolute_value and description_value when selecting Range
                          resetValues = { absolute_value: null, description_value: null };
                        } else if (value === 2) {
                          // Reset min_value, max_value, and description_value when selecting Absolute
                          resetValues = { min_value: null, max_value: null, description_value: null };
                        } else if (value === 3) {
                          // Reset min_value, max_value, and absolute_value when selecting Description
                          resetValues = { min_value: null, max_value: null, absolute_value: null };
                        }

                        setAddBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          binding_type: value,
                          ...resetValues,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          binding_type: undefined,
                        }));
                      }}
                      block
                      placeholder="Select Binding Type"
                      style={{ width: "100%" }}
                    />
                    {errorsAddForm.binding_type && <p style={{ color: "red" }}>{errorsAddForm.binding_type}</p>}
                  </Form.Group>

                  {/* Form yang muncul tergantung dari binding_type yang dipilih */}
                  {addBindingParamPpiForm.binding_type === 1 && (
                    <>
                      <Form.Group>
                        <Form.ControlLabel>Min Value</Form.ControlLabel>
                        <Form.Control
                          name="min_value"
                          value={addBindingParamPpiForm.min_value}
                          onChange={(value) => {
                            setAddBindingParamPpiForm((prevFormValue) => ({
                              ...prevFormValue,
                              min_value: parseFloat(value) || 0,
                            }));
                            setErrorsAddForm((prevErrors) => ({
                              ...prevErrors,
                              min_value: undefined,
                            }));
                          }}
                          accepter={InputNumber}
                        />
                        {errorsAddForm.min_value && <p style={{ color: "red" }}>{errorsAddForm.min_value}</p>}
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Max Value</Form.ControlLabel>
                        <Form.Control
                          name="max_value"
                          value={addBindingParamPpiForm.max_value}
                          onChange={(value) => {
                            setAddBindingParamPpiForm((prevFormValue) => ({
                              ...prevFormValue,
                              max_value: parseFloat(value) || 0,
                            }));
                            setErrorsAddForm((prevErrors) => ({
                              ...prevErrors,
                              max_value: undefined,
                            }));
                          }}
                          accepter={InputNumber}
                        />
                        {errorsAddForm.max_value && <p style={{ color: "red" }}>{errorsAddForm.max_value}</p>}
                      </Form.Group>
                    </>
                  )}
                  {addBindingParamPpiForm.binding_type === 2 && (
                    <Form.Group>
                      <Form.ControlLabel>Absolute Value</Form.ControlLabel>
                      <Form.Control
                        name="absolute_value"
                        value={addBindingParamPpiForm.absolute_value}
                        onChange={(value) => {
                          setAddBindingParamPpiForm((prevFormValue) => ({
                            ...prevFormValue,
                            absolute_value: parseFloat(value) || 0,
                          }));
                          setErrorsAddForm((prevErrors) => ({
                            ...prevErrors,
                            absolute_value: undefined,
                          }));
                        }}
                        accepter={InputNumber}
                      />
                      {errorsAddForm.absolute_value && <p style={{ color: "red" }}>{errorsAddForm.absolute_value}</p>}
                    </Form.Group>
                  )}

                  {addBindingParamPpiForm.binding_type === 3 && (
                    <Form.Group>
                      <Form.ControlLabel>Description Value</Form.ControlLabel>
                      <Form.Control
                        name="description_value"
                        value={addBindingParamPpiForm.description_value}
                        onChange={(value) => {
                          setAddBindingParamPpiForm((prevFormValue) => ({
                            ...prevFormValue,
                            description_value: value,
                          }));
                          setErrorsAddForm((prevErrors) => ({
                            ...prevErrors,
                            description_value: undefined,
                          }));
                        }}
                      />
                      {errorsAddForm.description_value && <p style={{ color: "red" }}>{errorsAddForm.description_value}</p>}
                    </Form.Group>
                  )}
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddBindingParamPpiForm(emptyAddBindingParamPpiForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle"
                  disabled={addLoading}
                >
                  Cancel
                </Button>
                <Button onClick={HandleAddBindingParamPpiApi} appearance="primary" loading={addLoading} disabled={addLoading}>
                  Add
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal pop up untuk edit ppi */}
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditBindingParamPpiForm(emptyEditBindingParamPpiForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Edit Binding</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  {/* Parameter Name */}
                  <Form.Group>
                    <Form.ControlLabel>Parameter Name</Form.ControlLabel>
                    <Form.Control name="parameter_name" value={prmDataState.find((param) => param.value === editBindingParamPpiForm.id_parameter)?.label || "-"} readOnly />
                  </Form.Group>
                  {/* Order No */}
                  <Form.Group>
                    <Form.ControlLabel>Order No</Form.ControlLabel>
                    <Form.Control
                      name="order_no"
                      value={editBindingParamPpiForm.order_no}
                      onChange={(value) => {
                        if (parseInt(value) >= 0 || value === "") {
                          setEditBindingParamPpiForm((prevFormValue) => ({
                            ...prevFormValue,
                            order_no: value === "" ? "" : parseInt(value),
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            order_no: undefined,
                          }));
                        }
                      }}
                      type="number"
                      min={0}
                    />
                    {errorsEditForm.order_no && <p style={{ color: "red" }}>{errorsEditForm.order_no}</p>}
                  </Form.Group>

                  {/* SelectPicker untuk binding_type */}
                  <Form.Group>
                    <Form.ControlLabel>Binding Type</Form.ControlLabel>
                    <SelectPicker
                      data={[
                        { label: "Range", value: 1 },
                        { label: "Absolute", value: 2 },
                        { label: "Description", value: 3 },
                      ]}
                      value={editBindingParamPpiForm.binding_type}
                      onChange={(value) => {
                        // Reset form fields based on binding_type change
                        let resetValues = {};
                        if (value === 1) {
                          // Reset absolute_value and description_value when selecting Range
                          resetValues = { absolute_value: null, description_value: null };
                        } else if (value === 2) {
                          // Reset min_value, max_value, and description_value when selecting Absolute
                          resetValues = { min_value: null, max_value: null, description_value: null };
                        } else if (value === 3) {
                          // Reset min_value, max_value, and absolute_value when selecting Description
                          resetValues = { min_value: null, max_value: null, absolute_value: null };
                        }

                        setEditBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          binding_type: value,
                          ...resetValues,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          binding_type: undefined,
                        }));
                      }}
                      block
                      placeholder="Select Binding Type"
                      style={{ width: "100%" }}
                    />
                    {errorsEditForm.binding_type && <p style={{ color: "red" }}>{errorsEditForm.binding_type}</p>}
                  </Form.Group>

                  {/* Form yang muncul tergantung dari binding_type yang dipilih */}
                  {editBindingParamPpiForm.binding_type === 1 && (
                    <>
                      <Form.Group>
                        <Form.ControlLabel>Min Value</Form.ControlLabel>
                        <Form.Control
                          name="min_value"
                          value={editBindingParamPpiForm.min_value}
                          onChange={(value) => {
                            setEditBindingParamPpiForm((prevFormValue) => ({
                              ...prevFormValue,
                              min_value: parseFloat(value) || 0,
                            }));
                            setErrorsEditForm((prevErrors) => ({
                              ...prevErrors,
                              min_value: undefined,
                            }));
                          }}
                          accepter={InputNumber}
                        />
                        {errorsAddForm.min_value && <p style={{ color: "red" }}>{errorsAddForm.min_value}</p>}
                      </Form.Group>
                      <Form.Group>
                        <Form.ControlLabel>Max Value</Form.ControlLabel>
                        <Form.Control
                          name="max_value"
                          value={editBindingParamPpiForm.max_value}
                          onChange={(value) => {
                            setEditBindingParamPpiForm((prevFormValue) => ({
                              ...prevFormValue,
                              max_value: parseFloat(value) || 0,
                            }));
                            setErrorsEditForm((prevErrors) => ({
                              ...prevErrors,
                              max_value: undefined,
                            }));
                          }}
                          accepter={InputNumber}
                        />
                        {errorsEditForm.max_value && <p style={{ color: "red" }}>{errorsEditForm.max_value}</p>}
                      </Form.Group>
                    </>
                  )}
                  {editBindingParamPpiForm.binding_type === 2 && (
                    <Form.Group>
                      <Form.ControlLabel>Absolute Value</Form.ControlLabel>
                      <Form.Control
                        name="absolute_value"
                        value={editBindingParamPpiForm.absolute_value}
                        onChange={(value) => {
                          setEditBindingParamPpiForm((prevFormValue) => ({
                            ...prevFormValue,
                            absolute_value: parseFloat(value) || 0,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            absolute_value: undefined,
                          }));
                        }}
                        accepter={InputNumber}
                      />
                      {errorsEditForm.absolute_value && <p style={{ color: "red" }}>{errorsEditForm.absolute_value}</p>}
                    </Form.Group>
                  )}

                  {editBindingParamPpiForm.binding_type === 3 && (
                    <Form.Group>
                      <Form.ControlLabel>Description Value</Form.ControlLabel>
                      <Form.Control
                        name="description_value"
                        value={editBindingParamPpiForm.description_value}
                        onChange={(value) => {
                          setEditBindingParamPpiForm((prevFormValue) => ({
                            ...prevFormValue,
                            description_value: value,
                          }));
                          setErrorsEditForm((prevErrors) => ({
                            ...prevErrors,
                            description_value: undefined,
                          }));
                        }}
                      />
                      {errorsEditForm.description_value && <p style={{ color: "red" }}>{errorsEditForm.description_value}</p>}
                    </Form.Group>
                  )}

                  {/* Form wetmill */}
                  <Form.Group>
                    <Form.ControlLabel>Wetmill</Form.ControlLabel>
                    <RadioGroup
                      name="wetmill"
                      inline
                      value={editBindingParamPpiForm.wetmill}
                      onChange={(value) => {
                        setEditBindingParamPpiForm((prevFormValue) => ({
                          ...prevFormValue,
                          wetmill: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          wetmill: undefined,
                        }));
                      }}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsEditForm.wetmill && <p style={{ color: "red" }}>{errorsEditForm.wetmill}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditBindingParamPpiForm(emptyEditBindingParamPpiForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                  disabled={editLoading}
                >
                  Cancel
                </Button>
                <Button onClick={HandleEditBindingParamPpiApi} appearance="primary" loading={editLoading} disabled={editLoading}>
                  Edit
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal pop up konfirmasi hapus */}
            <Modal
              backdrop="static"
              open={showDeleteModal}
              onClose={() => {
                setShowDeleteModal(false);
                setBindingIdToDelete(null);
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Confirmation</Modal.Title>
              </Modal.Header>
              <Modal.Body>Are you sure you want to delete this item?</Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowDeleteModal(false);
                    setBindingIdToDelete(null);
                  }}
                >
                  No
                </Button>
                <Button
                  appearance="primary"
                  color="red"
                  onClick={() => {
                    handleEditStatusBindingParamPpiApi(bindingIdToDelete, 1);
                    setShowDeleteModal(false);
                    setBindingIdToDelete(null);
                  }}
                >
                  Yes
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
