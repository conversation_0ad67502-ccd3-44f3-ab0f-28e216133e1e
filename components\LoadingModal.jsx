import { useEffect } from "react";
import styles from "./style/LoadingModal.module.css";
import { Modal, Button } from "rsuite";

export default function LoadingModal({
  modalTitle = "Alert",
  modalMessage = "",
  modalType = "alert",
  modalOpen,
  closeHandler,
}) {
  return (
    <>
      {/* Modal */}
      {modalType === "success" ? (
        <Modal open={modalOpen} backdrop="static">
          <Modal.Header closeButton={false}>
            <Modal.Title>
              <div style={{ fontSize: "3em" }}>{modalTitle}</div>
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div style={{ fontSize: "1.1em", marginBottom: "2em" }}>
              {modalMessage}
            </div>
          </Modal.Body>
          <Modal.Footer>
            <div id="first">
              <p
                style={{
                  textAlign: "center",
                  bottom: 200,
                  // textShadow: "2px 2px #000000",
                  fontSize: "1em",
                  fontWeight: "bold",
                }}
              >
                Please Wait... <br />
                <br />
                <div className={styles.dna}>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                  <div className={styles.ele}></div>
                </div>
              </p>
            </div>
          </Modal.Footer>
        </Modal>
      ) : (
        <Modal open={modalOpen}>
          <Modal.Header>
            <div style={{ fontSize: "3em" }}>{modalTitle}</div>
          </Modal.Header>
          <Modal.Body>
            <div style={{ fontSize: "1.1em" }}>{modalMessage}</div>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                closeHandler();
              }}
              appearance="primary"
            >
              Close
            </Button>
          </Modal.Footer>
        </Modal>
      )}
    </>
  );
}
