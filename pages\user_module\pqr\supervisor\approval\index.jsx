import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, useToaster, Notification, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, Loader, RadioGroup, Radio, SelectPicker, Grid, Row, Col } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
// import HeaderRecipeComponentsTest from "@/components/pqr/HeaderRecipeComponentsTest";
import IotComponentsOracleTest from "@/components/pqr/IotComponentsOracleTest";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiSupervisorApproval from "@/pages/api/pqr/supervisor/api_supervisor";
import ApiBatchRecipe from "@/pages/api/pqr/transaction_h/api_batch_recipe";

export default function SupervisorApproval() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_trans_header");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const toaster = useToaster();
  const [loading, setLoading] = useState(false);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [transactionHeadersDataState, setTransactionHeadersDataState] = useState([]);
  const [remarks, setRemarks] = useState("");
  const [remarksError, setRemarksError] = useState(false);
  const [showRemarksModal, setShowRemarksModal] = useState(false);

  const [showTransactionDetailModal, setShowTransactionDetailModal] = useState(false);
  const [TransactionDetailDataState, setTransactionDetailDataState] = useState([]);
  const [recipeDataState, setRecipeDataState] = useState([]);
  const [recipeDataForm, setRecipeDataForm] = useState([]);

  const [selectedTransactionId, setSelectedTransactionId] = useState(null);
  const [password, setPassword] = useState("");

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = transactionHeadersDataState.filter((rowData, i) => {
    const searchFields = ["id_trans_header", "ppi_name", "batch_code", "iot_desc", "line_desc", "remarks", "wetmill", "status_transaction", "create_date", "create_by", "update_date", "update_by", "delete_date", "delete_by"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : transactionHeadersDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("pqr/supervisor"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandlegetAllNeedApproveTransactionHeaderApi();
    }
  }, []);

  const HandlegetAllNeedApproveTransactionHeaderApi = async () => {
    try {
      const res = await ApiTransactionHeader().getAllNeedApproveTransactionHeader();

      console.log("res", res);
      if (res.status === 200) {
        setTransactionHeadersDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };

  const HandleEditStatusApprovalApi = async (id_trans_header, status_approval, password) => {
    setLoading(true);
    try {
      const finalRemarks = status_approval === 1 ? "" : remarks;

      const res = await ApiSupervisorApproval().editApprovalStatus({
        id_trans_header,
        status_approval,
        revise_remarks: finalRemarks,
        approval_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
        employee_id: sessionAuth.employee_id,
        password: password,
        ppi_release: recipeDataForm,
      });

      if (res.status === 200) {
        showNotification("success", "Approval Berhasil.");
        HandlegetAllNeedApproveTransactionHeaderApi();
        setShowTransactionDetailModal(false);
        setShowRemarksModal(false);
        setRemarks("");
        setPassword("");
      } else {
        console.error("Error on update status Approval ", res.message);
        if (res.message === "wrong username or password") {
          showNotification("error", "Username atau password salah");
          setShowTransactionDetailModal(true);
        } else {
          setRemarks("");
          console.log("gagal update status Approval", res.message);
          showNotification("error", `Gagal Update Status Approval`);
        }
      }
    } catch (error) {
      console.log("Error gagal update status Approval", error);
      toaster.push({ message: "Gagal Update Status Approval", type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const HandleGetRecipe = async (batch_code) => {
    try {
      const res = await ApiBatchRecipe().getRecipeByBatchCode({ lot_number: batch_code });
      if (res.status === 200) {
        setRecipeDataState(res.data);
        setRecipeDataForm(res.data);
      } else {
        console.log("Error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("Error on catch Get All Api", error);
    }
  };

  const HandleGetTransactionHeaderDetail = async (id_trans_header) => {
    try {
      const res = await ApiTransactionHeader().getPPITransactionHeaderById({
        id_trans_header: id_trans_header,
      });

      if (res.status === 200) {
        console.log("Data received: ", res.data);
        setTransactionDetailDataState([res.data]);
        if (res.data.batch_code) {
          HandleGetRecipe(res.data.batch_code);
        }
      } else {
        console.log("Error on update status: ", res.message);
      }
    } catch (error) {
      console.log("Error on update status: ", error.message);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Supervisor Approval</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>E Release</Breadcrumb.Item>
                  <Breadcrumb.Item>Supervisor</Breadcrumb.Item>
                  <Breadcrumb.Item active>Approval</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Supervisor Persetujuan E Release</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2"></div>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="cari" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                <Column width={180} align="center" sortable fullText resizable>
                  <HeaderCell>ID Transaksi Header</HeaderCell>
                  <Cell dataKey="id_trans_header" />
                </Column>
                <Column width={180} sortable fullText resizable>
                  <HeaderCell align="center">Nama PPI</HeaderCell>
                  <Cell dataKey="ppi_name" />
                </Column>
                <Column width={180} sortable fullText resizable>
                  <HeaderCell align="center">Kode Batch</HeaderCell>
                  <Cell dataKey="batch_code" />
                </Column>
                {/* <Column width={250} sortable fullText>
                  <HeaderCell align="center">Deksripsi IOT</HeaderCell>
                  <Cell dataKey="iot_desc" />
                </Column>
                <Column width={250} sortable fullText>
                  <HeaderCell align="center">Deskripsi Line</HeaderCell>
                  <Cell dataKey="line_desc" />
                </Column> */}
                <Column width={250} sortable fullText resizable>
                  <HeaderCell align="center">Catatan</HeaderCell>
                  <Cell dataKey="remarks" />
                </Column>
                <Column width={250} align="center" sortable fullText resizable>
                  <HeaderCell>Wetmill</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      let statusText = "";
                      if (rowData.wetmill === "Y") {
                        statusText = "Yes";
                      } else if (rowData.wetmill === "N") {
                        statusText = "No";
                      }
                      return <>{statusText}</>;
                    }}
                  </Cell>
                </Column>
                <Column width={250} align="center" sortable fullText resizable>
                  <HeaderCell>Status Transaksi</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      let statusText = "";
                      if (rowData.status_transaction === 2) {
                        statusText = "Draft";
                      } else if (rowData.status_transaction === 1) {
                        statusText = "Done";
                      } else if (rowData.status_transaction === 0) {
                        statusText = "Dropped";
                      }
                      return <>{statusText}</>;
                    }}
                  </Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dibuat</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.delete_by}</>}</Cell>
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={150} fixed="right" align="center">
                  <HeaderCell>Rincian</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="primary"
                          color="ghost"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setSelectedTransactionId(rowData.id_trans_header);
                            HandleGetTransactionHeaderDetail(rowData.id_trans_header);
                            setShowTransactionDetailModal(true);
                          }}
                        >
                          Action
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            <Modal
              backdrop="static"
              open={showRemarksModal}
              onClose={() => {
                setShowRemarksModal(false);
                setRemarks("");
                setPassword("");
              }}
              overflow={false}
              size="sm"
            >
              <Modal.Header>
                <Modal.Title>Isi Catatan Revisi</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <div className="form-group">
                  <label htmlFor="remarks"></label>
                  <textarea
                    id="remarks"
                    className="form-control"
                    placeholder="Masukkan Catatan revisi"
                    value={remarks}
                    onChange={(e) => {
                      setRemarks(e.target.value);
                      setRemarksError(false);
                    }}
                    style={{ width: "100%", minHeight: "100px" }}
                  />
                  {remarksError && <p style={{ color: "red" }}>Catatan wajib diisi.</p>}
                </div>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowRemarksModal(false);
                    setRemarks("");
                    setPassword("");
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  appearance="primary"
                  onClick={async () => {
                    if (!remarks.trim()) {
                      setRemarksError(true);
                      return;
                    }
                    if (!password.trim()) {
                      showNotification("error", "Password wajib diisi");
                      return;
                    }
                    setLoading(true);
                    await HandleEditStatusApprovalApi(selectedTransactionId, 0, password);
                    setLoading(false);
                    setShowRemarksModal(false);
                    setPassword("");
                  }}
                  loading={loading}
                  disabled={loading}
                >
                  Submit
                </Button>
              </Modal.Footer>
            </Modal>

            <Modal
              backdrop="static"
              open={showTransactionDetailModal}
              onClose={() => {
                setShowTransactionDetailModal(false);
                setPassword("");
                setRemarks("");
                setTransactionDetailDataState([]);
              }}
              overflow={false}
              size={"lg"}
            >
              <Modal.Header>
                <Modal.Title>Rincian Data Transaction</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Panel
                  bordered
                  bodyFill
                  className="mb-3"
                  header={
                    <Stack justifyContent="space-between">
                      <Form layout="vertical">
                        <Grid fluid>
                          <Row style={{ marginBottom: "16px" }}>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>ID Transaksi Header</Form.ControlLabel>
                                <Form.Control name="id_trans_header" value={TransactionDetailDataState[0]?.id_trans_header || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                                <Form.Control name="ppi_name" value={TransactionDetailDataState[0]?.ppi_name || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Kode Batch</Form.ControlLabel>
                                <Form.Control name="batch_code" value={TransactionDetailDataState[0]?.batch_code || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Wetmill</Form.ControlLabel>
                                <Form.Control name="wetmill" value={TransactionDetailDataState[0]?.wetmill === "Y" ? "Yes" : "No"} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                          </Row>

                          <Row style={{ marginBottom: "16px" }}>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Catatan</Form.ControlLabel>
                                <Form.Control name="remarks" value={TransactionDetailDataState[0]?.remarks || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Status Transaksi</Form.ControlLabel>
                                <Form.Control
                                  name="status_transaction"
                                  value={TransactionDetailDataState[0]?.status_transaction === 2 ? "Draft" : TransactionDetailDataState[0]?.status_transaction === 1 ? "Done" : "Dropped"}
                                  style={{ width: "100%" }}
                                  readOnly
                                />
                              </Form.Group>
                            </Col>
                          </Row>

                          <Row style={{ marginBottom: "24px" }}>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                                <Form.Control name="create_date" value={TransactionDetailDataState[0]?.create_date ? new Date(TransactionDetailDataState[0].create_date).toLocaleDateString("en-GB") : ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                                <Form.Control name="created_by" value={TransactionDetailDataState[0]?.create_by || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                                <Form.Control name="update_date" value={TransactionDetailDataState[0]?.update_date ? new Date(TransactionDetailDataState[0].update_date).toLocaleDateString("en-GB") : ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                                <Form.Control name="update_by" value={TransactionDetailDataState[0]?.update_by || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                          </Row>

                          <Row style={{ marginBottom: "16px" }}>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                                <Form.Control name="delete_date" value={TransactionDetailDataState[0]?.delete_date ? new Date(TransactionDetailDataState[0].delete_date).toLocaleDateString("en-GB") : ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                                <Form.Control name="delete_by" value={TransactionDetailDataState[0]?.delete_by || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                          </Row>
                        </Grid>
                      </Form>
                    </Stack>
                  }
                />

                <IotComponentsOracleTest lot_number={TransactionDetailDataState[0]?.batch_code} />
              </Modal.Body>
              <Modal.Footer>
                <div style={{ display: "flex", justifyContent: "flex-end", width: "100%" }}>
                  <InputGroup style={{ width: "150px", marginBottom: "16px" }}>
                    <Input type="password" placeholder="Masukan Password" value={password} onChange={(value) => setPassword(value)} />
                  </InputGroup>
                </div>
                <Button
                  appearance="primary"
                  color="red"
                  onClick={() => {
                    if (!password.trim()) {
                      showNotification("error", "Password wajib diisi");
                      return;
                    }
                    setShowTransactionDetailModal(false);
                    setShowRemarksModal(true);
                  }}
                  loading={loading}
                  disabled={loading || !password.trim()}
                >
                  Revisi
                </Button>
                <Button
                  appearance="primary"
                  color="green"
                  onClick={async () => {
                    if (!password.trim()) {
                      showNotification("error", "Password wajib diisi");
                      return;
                    }
                    setLoading(true);
                    await HandleEditStatusApprovalApi(selectedTransactionId, 1, password);
                    setPassword("");
                    setLoading(false);
                  }}
                  loading={loading}
                  disabled={loading || !password.trim()}
                >
                  Approve
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
