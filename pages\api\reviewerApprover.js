import axios from 'axios';

export default function ReviewerApproverApi() {
  const GetAllReviewerApprover = async () => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/getAll`)
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetReviewerApproverById = async (idData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/getById`,
        idData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetAllRole = async () => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/getAllRole`,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetReviewerByDepartmentId = async (idData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/getReviewerByDepartment`,
        idData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetActiveReviewerByDepartmentId = async (idData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/getActiveReviewerByDepartment`,
        idData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetActiveApproverForNewProtocol = async (idData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/getActiveApproverForNewProtocol`,
        idData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };
  const GetActiveApproverForEditProtocol = async (idData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/getActiveApproverForEditProtocol`,
        idData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetActiveReviewerForNewProtocol = async (idData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/getActiveReviewerForNewProtocol`,
        idData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetActiveReviewerForEditProtocol = async (idData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/getActiveReviewerForNewProtocol`,
        idData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetApproverByDepartmentId = async (idData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/getApproverByDepartment`,
        idData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetActiveApproverByDepartmentId = async (idData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/getActiveApproverByDepartment`,
        idData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const InsertReviewerApprover = async (insertData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/insertReviewerApprover`,
        insertData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const UpdateReviewerApprover = async (updateData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/reviewerApprover/updateReviewerApprover`,
        updateData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  return {
    GetAllReviewerApprover,
    GetReviewerApproverById,
    GetAllRole,
    InsertReviewerApprover,
    UpdateReviewerApprover,
    GetReviewerByDepartmentId,
    GetApproverByDepartmentId,
    GetActiveReviewerByDepartmentId,
    GetActiveApproverByDepartmentId,
    GetActiveReviewerForNewProtocol,
    GetActiveApproverForNewProtocol,
    GetActiveReviewerForEditProtocol,
    GetActiveApproverForEditProtocol
  };
}
