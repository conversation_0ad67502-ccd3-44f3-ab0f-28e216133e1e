import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  try {
    const response = await axios[method](
      `${process.env.NEXT_PUBLIC_BASE_URL}/v2/eform-masterlist-atb-pmtms/${url}`,
      data
    );
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default function EformMasterlistAtbPmtmsApi() {
  return {
    getAll: createApiFunction("get", "get-all"),
    add: createApiFunction("post", "add"),
    edit: createApiFunction("put", "edit"),
  };
}
