import { useState, useEffect } from 'react';
import ProtocolApi from '@/pages/api/protocolApi';
import Head from 'next/head';
import ContainerLayout from '@/components/layout/ContainerLayout';
import MainContent from '@/components/layout/MainContent';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import { useRouter } from 'next/router';
import {
    Button,
    Stack,
    Breadcrumb,
    Tag,
    Table,
    Panel,
    Input,
    InputGroup,
    Pagination,
    Divider,
    Modal,
    Form,
    Schema,
    useToaster,
    DatePicker,
    SelectPicker,
    IconButton,
} from "rsuite";
import { paginate } from '@/utils/paginate';
import FileDownloadIcon from '@rsuite/icons/FileDownload';
import SearchIcon from '@rsuite/icons/Search';

export default function ProtocolEditApproval() {
    const router = useRouter();
    const [allProtocolHeaderData, setAllProtocolHeaderData] = useState([]);
    const { GetProtocolEditHeaderDataByEmployeeId } = ProtocolApi();
    const [moduleName, setModuleName] = useState('');
    const { HeaderCell, Cell, Column } = Table;
    const [breadcrumbsData, setBreadcrumbsData] = useState([]);
    const [userDept, setUserDept] = useState('');
    const [page, setPage] = useState(1);
    const [limit, setLimit] = useState(10);
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    let path = 'protocol/management/ProtocolEditApproval';

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setSortColumn(sortColumn);
        setSortType(sortType);
    };

    // data to be displayed in the table
    var datas = paginate(allProtocolHeaderData, page, limit);

    const GetAllProtocolData = async (employeeId) => {
        const inputData = {
            employee_id: employeeId,
        };
        const { data: data } = await GetProtocolEditHeaderDataByEmployeeId(inputData);

        if (data !== null && data.length > 0) {
            let indexNo = 1;
            const dataProtocol = data.map(item => {
                // add indexNo
                const newData = { indexNo, ...item };
                indexNo++;
                return newData;
            });

            setAllProtocolHeaderData(dataProtocol);
            return;
        }
    };

    useEffect(() => {
        const moduleNameValue = localStorage.getItem('module_name');
        const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
        if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
            router.push('/');
            return;
        }

        // validate access for this page
        if (!dataLogin.menu_link_code) {
            router.push('/dashboard');
            return;
        }
        const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
            item.includes(path),
        );
        if (validateUserAccess.length === 0) {
            router.push('/dashboard');
            return;
        }

        if (
            moduleNameValue === null ||
            moduleNameValue === undefined ||
            moduleNameValue === ''
        ) {
            router.push('/dashboard');
            return;
        }

        const asPathWithoutQuery = router.asPath.split('?')[0];
        const asPathNestedRoutes = asPathWithoutQuery
            .split('/')
            .filter((v) => v.length);
        let routerBreadCrumbsData = [
            moduleNameValue,
            asPathNestedRoutes[1],
            asPathNestedRoutes[2],
        ];
        const capitalizeFirstLetter = (str) => {
            return str.charAt(0).toUpperCase() + str.slice(1);
        };
        const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
            const words = item.split(/(?=[A-Z])/);
            return words.map((word) => capitalizeFirstLetter(word)).join(' ');
        });
        setBreadcrumbsData(breadCrumbsResult);

        setModuleName(moduleNameValue);

        GetAllProtocolData(dataLogin.employee_id);

        setUserDept(dataLogin.department);
    }, []);

    return (
        <>
            <div>
                <Head>
                    <title>Protocol Edit Approval</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <MainContent>
                    <ModuleContentHeader
                        breadcrumbs={breadcrumbsData}
                        module_name={moduleName}
                    />
                    {allProtocolHeaderData.length > 0 && (
                        <Panel>
                            <Table
                                bordered
                                cellBordered
                                height={400}
                                data={datas}
                                sortColumn={sortColumn}
                                sortType={sortType}
                                onSortColumn={handleSortColumn}
                            >
                                <Column width={60} align="center" fixed>
                                    <HeaderCell>No</HeaderCell>
                                    <Cell>
                                        {(rowData, rowIndex) => {
                                            return rowIndex + 1 + limit * (page - 1);
                                        }}
                                    </Cell>
                                </Column>

                                <Column width={150}>
                                    <HeaderCell>No Protocol</HeaderCell>
                                    <Cell dataKey="No_Protocol" />
                                </Column>

                                <Column width={150}>
                                    <HeaderCell>Diajukan Oleh</HeaderCell>
                                    <Cell dataKey="Created_by" />
                                </Column>

                                <Column width={50}>
                                    <HeaderCell>Versi</HeaderCell>
                                    <Cell dataKey="Protocol_Version" />
                                </Column>

                                <Column width={150}>
                                    <HeaderCell>Tipe Protokol</HeaderCell>
                                    <Cell dataKey="Department_Name" />
                                </Column>

                                <Column width={150}>
                                    <HeaderCell>Status</HeaderCell>
                                    <Cell>
                                        {rowData => 'Ready to approve'}
                                    </Cell>
                                </Column>

                                <Column width={150} fixed="right">
                                    <HeaderCell>...</HeaderCell>

                                    <Cell style={{ padding: "6px" }}>
                                        {(rowData) => (
                                            <Stack direction='row' justifyContent='center'>
                                                {<IconButton size='sm' icon={<SearchIcon />} title='Tinjau dokumen' appearance='ghost' color='orange' onClick={() => {
                                                    router.push({
                                                        pathname:
                                                            '/user_module/protocol/management/ProtocolEditApprovalDetail',
                                                        query: { noProtocol: rowData.No_Protocol },
                                                    });
                                                }} />}
                                                <Divider vertical />
                                                {<IconButton size='sm' icon={<FileDownloadIcon />} appearance='ghost' color='green' title='Print dokumen' onClick={() => {
                                                    router.push({ pathname: '/user_module/protocol/management/PrintProtocolDoc', query: { noProtocol: rowData.No_Protocol, userDept: parseInt(userDept) } });
                                                }} />}
                                            </Stack>
                                        )}
                                    </Cell>
                                </Column>
                            </Table>
                            <div style={{ padding: 20 }}>
                                <Pagination
                                    prev
                                    next
                                    first
                                    last
                                    ellipsis
                                    boundaryLinks
                                    maxButtons={5}
                                    size="xs"
                                    layout={["total", "-", "limit", "|", "pager"]}
                                    total={
                                        allProtocolHeaderData.length
                                    }
                                    limitOptions={[10, 30, 50]}
                                    limit={limit}
                                    activePage={page}
                                    onChangePage={setPage}
                                    onChangeLimit={handleChangeLimit}
                                />
                            </div>
                        </Panel>
                    )}
                    {allProtocolHeaderData.length == 0 && <Stack justifyContent='center'>
                        <p style={{ fontWeight: 'bold' }}>No Protocol Document Data.</p>
                    </Stack>}
                </MainContent>
            </ContainerLayout>
        </>
    );
}
