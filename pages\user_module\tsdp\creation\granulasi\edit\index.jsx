import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  InputGroup,
  Panel,
  Stack,
  Button,
  Form,
  useToaster,
  DatePicker,
  Uploader,
  Row,
  Col,
  RadioGroup,
  Radio,
  SelectPicker,
  Table,
  Pagination,
  Modal,
  Divider,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";

import { useRouter } from "next/router";
import Messages from "@/components/Messages";
import EditIcon from "@rsuite/icons/Edit";
import ReloadIcon from "@rsuite/icons/Reload";
import TrashIcon from "@rsuite/icons/Trash";
import ExpandOutlineIcon from "@rsuite/icons/ExpandOutline";
import ApiTsdpTH from "@/pages/api/tsdp/transaction_h/api_tsdp_transaction_h";
import ApiTsdpTDAttachments from "@/pages/api/tsdp/api_tsdp_td_attachments";
import ApiTsDetail from "@/pages/api/ts/api_ts_detail";
import API_TsMatrixCategoriesParameter from "@/pages/api/api_ts-matrix-categories-parameter";
import { userAgent } from "next/server";
import ApiTsReport from "@/pages/api/ts/api_ts-report";
import ApiTsdpAuto from "@/pages/api/tsdp/api_tsdp_td_auto";
import ApiTsdpManual from "@/pages/api/tsdp/api_tsdp_td_manual";
import ApiTsdpTDGranulasi from "@/pages/api/tsdp/api_tsdp_td_granulasi";

export default function EditBinder() {
  const router = useRouter();
  const toaster = useToaster();
  const [headerData, setHeaderData] = useState([]);
  const [idHeaderTrans1, setIdHeaderTrans1] = useState();
  const [formAttachments, setFormAttachments] = useState([]);
  const [idDetailTrans, setIdDetailTrans] = useState(0);
  const [sessionAuth, setSessionAuth] = useState(null);
  const [moduleName, setModuleName] = useState("");
  const [errorsGranuleForm, setErrorsGranuleForm] = useState({});
  const [granuleData, setGranuleData] = useState({});
  const [dataSpv, setDataSpv] = useState([]);
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  //---AUTOMATE LINE STATES---

  const { HeaderCell, Cell, Column } = Table;
  const [formDetail, setFormDetail] = useState({});
  const [formAddDetail, setFormAddDetail] = useState([]);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedDetail, setSelectedDetail] = useState(null);
  const [showSelectedDetailModal, setShowSelectedDetailModal] = useState(false);
  const [selectedApiType, setSelectedApiType] = useState(null);
  const [dataDetail, setDataDetail] = useState([]);

  //---MANUAL LINE STATES---

  const [tsMatrixCategories, setTsMatrixCategories] = useState([]);
  const [modalPengeringan, setModalPengeringan] = useState(false);
  const [granulasiDetail, setGranulasiDetail] = useState([]);
  const [editIndex, setEditIndex] = useState(null);
  const [isEditingNewDetail, setIsEditingNewDetail] = useState(false);
  const emptyDetailForm = { id_matrix2: null, value2: null };
  const [formDetailValue, setFormDetailValue] = useState(emptyDetailForm);

  //modal detail staged edited/removal states
  const [stagedRemovals, setStagedRemovals] = useState(new Set());
  const [originalManualDetails, setOriginalManualDetails] = useState({});
  // const [editedCells, setEditedCells] = useState({});
  const [stagedChanges, setStagedChanges] = useState({});

  // NEWDETAILTABLE STATES
  const [addLimit, setAddLimit] = useState(10);
  const [addPage, setAddPage] = useState(1);

  // DETAILTABLE STATES
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  //manual detail table states
  const [manualLimit, setManualLimit] = useState(10);

  const fetchDropdownData = async () => {
    try {
      const resSpv = await ApiTsDetail().getAllActiveSpv();
      setDataSpv(resSpv.data || []);
      const resAllDetail = await ApiTsDetail().getAllDetail(); // Fetch data for the modal dropdown
      setDataDetail(resAllDetail.data ? resAllDetail.data : []);
    } catch (error) {
      console.error("Error fetching dropdown data:", error);
    }
  };

  const validateAddDetail = () => {
    const currentState = "Granulasi";
    const isValid = (
      Array.isArray(granuleData.granule_detail)
        ? [...granuleData.granule_detail]
        : []
    )
      .concat(formAddDetail)
      .some((detail) => detail.type === currentState);
    return isValid;
  };

  const handleRemoveDetail = (index) => {
    setFormAddDetail((prevFormDetail) => [
      ...prevFormDetail.slice(0, index),
      ...prevFormDetail.slice(index + 1),
    ]);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const handleChangeAddLimit = (dataKey) => {
    setAddPage(1);
    setAddLimit(dataKey);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const getFilteredData = (detail) => {
    if (sortColumn && sortType) {
      return [...detail].sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") x = x.charCodeAt();
        if (typeof y === "string") y = y.charCodeAt();
        if (sortType === "asc") return x - y;
        else return y - x;
      });
    }
    return detail;
  };

  const getAddPaginatedData = () => {
    const filtered = getFilteredData(formAddDetail) || [];
    const start = addLimit * (addPage - 1);
    const end = start + addLimit;
    return filtered.slice(start, end);
  };

  const getPaginatedData = () => {
    const filtered = getFilteredData(granuleData.granule_detail) || [];
    const start = limit * (page - 1);
    const end = start + limit;
    return filtered.slice(start, end);
  };

  const getDetailApiData = async (type) => {
    const res = await ApiTsDetail().getApiData({
      type_api: type,
    });

    if (res.status === 200) {
      setFormAddDetail((prevFormDetail) => {
        const existingDetail = prevFormDetail || [];
        const currentState = "Granulasi"; // Hardcoded for this context
        const typeExists = existingDetail.some(
          (detail) => detail.type === currentState
        );

        return typeExists
          ? existingDetail
          : [
              ...existingDetail,
              {
                id_header_trans: granuleData.id_header_trans,
                type: currentState,
                data: res.data,
              },
            ];
      });
      setShowDetailModal(false);
    }
  };

  const totalAddRowCount = formAddDetail?.length || 0;
  const totalRowCount = granuleData.granule_detail?.length || 0;

  const handleGetTsMatrixCategoriesApi = async () => {
    try {
      const response =
        await API_TsMatrixCategoriesParameter().getAllTsMatrixCategoriesParameter();

      if (response.status === 200 && response.data) {
        const formattedData = response.data?.map((item) => ({
          label: item.parameter_name,
          value: item.id_matrix,
          id_categories: item.id_categories,
          main_category_name: item.main_category_name,
        }));

        setTsMatrixCategories(formattedData);
      }
    } catch (error) {
      console.error("Error fetching matrix categories:", error);
    }
  };

  const handleDetailGranulasi = () => {
    const selectedMatrix = tsMatrixCategories.find(
      (item) => item.value === formDetailValue.id_matrix2
    );
    const newDetail = {
      id_matrix: formDetailValue.id_matrix2,
      matrix_label: selectedMatrix?.label,
      id_categories: selectedMatrix?.id_categories,
      value: formDetailValue.value2,
    };
    setGranulasiDetail((prevDetails) => [...prevDetails, newDetail]);
  };

  const handleEditNewDetail = (index, type) => {
    const detail = granulasiDetail[index];
    setEditIndex(index);
    setIsEditingNewDetail(true);
    setFormDetailValue({
      id_matrix2: detail.id_matrix,
      value2: detail.value,
    });
    setModalPengeringan(true);
  };

  const handleRemoveNewDetail = (indexToRemove) => {
    setGranulasiDetail((prevDetails) =>
      prevDetails.filter((_, index) => index !== indexToRemove)
    );
  };

  const handleEditExistingDetail = (detail) => {
    setIsEditingNewDetail(false);
    setFormDetailValue({
      id_matrix2: detail.id_matrix,
      value2: detail.value,
      id_detail_trans_m: detail.id_detail_trans_m,
    });

    setModalPengeringan(true);
  };

  const handleDeactiveExistingDetail = (detail) => {
    const detailId = detail.id_detail_trans_m;

    setStagedRemovals((prev) => new Set(prev).add(detailId));

    setGranuleData((prevGranuleData) => ({
      ...prevGranuleData,
      granule_detail: prevGranuleData.granule_detail.map((d) =>
        d.id_detail_trans_m === detailId ? { ...d, is_active: 0 } : d
      ),
    }));

    setStagedChanges((prevStagedChanges) => ({
      ...prevStagedChanges,
      [detailId]: {
        ...granuleData.granule_detail.find(
          (d) => d.id_detail_trans_m === detailId
        ),
        is_active: 0,
      },
    }));
  };

  const handleRevertRowChanges = (detailId) => {
    if (String(detailId).startsWith("temp_new_")) {
      setGranulasiDetail((prevDetails) =>
        prevDetails.filter((d) => d.id_detail_trans_m !== detailId)
      );
      return;
    }

    setStagedRemovals((prev) => {
      const newSet = new Set(prev);
      newSet.delete(detailId);
      return newSet;
    });

    setStagedChanges((prevStagedChanges) => {
      const newChanges = { ...prevStagedChanges };
      delete newChanges[detailId];
      return newChanges;
    });

    setGranuleData((prevGranuleData) => {
      const updatedGranuleDetails = prevGranuleData.granule_detail.map(
        (detail) => {
          if (detail.id_detail_trans_m === detailId) {
            const original = originalManualDetails[detailId];
            if (original) {
              return { ...original };
            }
          }
          return detail;
        }
      );
      return { ...prevGranuleData, granule_detail: updatedGranuleDetails };
    });
  };

  const NewDetailTable = () => {
    return (
      <Panel bordered bodyFill className="mt-3">
        <h6 className="m-3">Add New Details</h6>
        <Table
          bordered
          cellBordered
          height={250}
          data={getAddPaginatedData()}
          sortColumn={sortColumn}
          sortType={sortType}
          onSortColumn={handleSortColumn}>
          <Column width={70} align="center">
            <HeaderCell>No</HeaderCell>
            <Cell>
              {(rowData, rowIndex) => (addPage - 1) * addLimit + rowIndex + 1}
            </Cell>
          </Column>
          <Column flexGrow={2} align="center">
            <HeaderCell>Type</HeaderCell>
            <Cell dataKey="type" />
          </Column>
          <Column flexGrow={1}>
            <HeaderCell align="center">Action</HeaderCell>
            <Cell
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}>
              {(rowData, rowIndex) => (
                <div className="space-x-2">
                  <Button
                    appearance="ghost"
                    size="xs"
                    onClick={() => {
                      setSelectedDetail(rowData);
                      setShowSelectedDetailModal(true);
                    }}>
                    View Detail
                  </Button>
                  <Button
                    appearance="ghost"
                    color="red"
                    size="xs"
                    onClick={() => handleRemoveDetail(rowIndex)}>
                    Remove
                  </Button>
                </div>
              )}
            </Cell>
          </Column>
        </Table>
        <div style={{ padding: 20 }}>
          <Pagination
            prev
            next
            first
            last
            ellipsis
            boundaryLinks
            maxButtons={5}
            size="xs"
            layout={["total", "-", "limit", "|", "pager", "skip"]}
            limitOptions={[10, 30, 50]}
            total={totalAddRowCount}
            limit={addLimit}
            activePage={addPage}
            onChangePage={setAddPage}
            onChangeLimit={handleChangeAddLimit}
          />
        </div>
      </Panel>
    );
  };

  const DetailTable = () => {
    return (
      <Panel bordered bodyFill className="mt-3">
        <h6 className="m-3">Inserted Details</h6>
        <Table
          bordered
          cellBordered
          height={400}
          data={getPaginatedData()}
          sortColumn={sortColumn}
          sortType={sortType}
          onSortColumn={handleSortColumn}>
          <Column width={70} align="center">
            <HeaderCell>No</HeaderCell>
            <Cell>
              {(rowData, rowIndex) => (page - 1) * limit + rowIndex + 1}
            </Cell>
          </Column>
          <Column flexGrow={1} align="center">
            <HeaderCell>Type</HeaderCell>
            <Cell dataKey="type" />
          </Column>
          <Column flexGrow={2} align="center">
            <HeaderCell>Parameter</HeaderCell>
            <Cell dataKey="parameter" />
          </Column>
          <Column flexGrow={1} align="center">
            <HeaderCell>Value</HeaderCell>
            <Cell dataKey="value" />
          </Column>
        </Table>
        <div style={{ padding: 20 }}>
          <Pagination
            prev
            next
            first
            last
            ellipsis
            boundaryLinks
            maxButtons={5}
            size="xs"
            layout={["total", "-", "limit", "|", "pager", "skip"]}
            limitOptions={[10, 30, 50]}
            total={totalRowCount}
            limit={limit}
            activePage={page}
            onChangePage={setPage}
            onChangeLimit={handleChangeLimit}
          />
        </div>
      </Panel>
    );
  };

  const AutomateLineDetails = () => (
    <>
      <Button
        appearance="primary"
        onClick={() => setShowDetailModal(true)}
        disabled={validateAddDetail()}
        className="my-3">
        + Data Granulasi
      </Button>
      <NewDetailTable />
      <DetailTable />
    </>
  );

  const ManualLineDetails = () => {
    const getEditedStatus = (rowData, fieldName) => {
      if (String(rowData.id_detail_trans_m).startsWith("temp_new_")) {
        return false;
      }
      const originalDetail = originalManualDetails[rowData.id_detail_trans_m];
      if (!originalDetail) return false;

      if (fieldName === "parameter_name") {
        return originalDetail.id_matrix !== rowData.id_matrix;
      }
      return String(originalDetail[fieldName]) !== String(rowData[fieldName]);
    };
    return (
      <>
        <Form.Group>
          <Button
            appearance="primary"
            startIcon={<ExpandOutlineIcon />}
            onClick={() => {
              setModalPengeringan(true);
              setIsEditingNewDetail(true);
              setFormDetailValue(emptyDetailForm);
            }}>
            Tambah Detail Granulasi
          </Button>
        </Form.Group>

        <Panel bordered className="mb-3">
          {granulasiDetail.length > 0 ? (
            <>
              <h4 className="mb-4">New Details</h4>
              {granulasiDetail?.map((detail, index) => (
                <div key={detail.id_detail_trans_m}>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}>
                    <div>
                      <p>Matrix: {detail.matrix_label}</p>
                      <p>Value: {detail.value}</p>
                    </div>
                    <div style={{ display: "flex", gap: "8px" }}>
                      <Button
                        appearance="ghost"
                        color="blue"
                        size="xs"
                        onClick={() => handleEditNewDetail(index)}>
                        Edit
                      </Button>
                      <Button
                        appearance="ghost"
                        color="red"
                        size="xs"
                        onClick={() => handleRemoveNewDetail(index)}>
                        Remove
                      </Button>
                    </div>
                  </div>
                  {index < granulasiDetail.length - 1 && (
                    <Divider style={{ margin: "10px 0" }} />
                  )}
                </div>
              ))}
            </>
          ) : (
            <p>No New Details Added</p>
          )}
        </Panel>

        <Panel bordered>
          <Table
            bordered
            cellBordered
            data={getPaginatedData()}
            autoHeight
            sortColumn={sortColumn}
            sortType={sortType}
            onSortColumn={handleSortColumn}>
            <Column
              width={150}
              resizable
              align="center"
              sortable
              dataKey="id_detail_trans_m">
              <HeaderCell>ID Detail Trans Manual</HeaderCell>
              <Cell dataKey="id_detail_trans_m" />
            </Column>

            <Column width={200} resizable align="center">
              <HeaderCell>Parameter</HeaderCell>
              <Cell dataKey="parameter_name">
                {(rowData) => {
                  return (
                    <span
                      style={{
                        transition: "all 0.2s ease-in-out",
                      }}>
                      {rowData.parameter_name}
                    </span>
                  );
                }}
              </Cell>
            </Column>

            <Column width={150} resizable align="center">
              <HeaderCell>Value</HeaderCell>
              <Cell dataKey="value">
                {(rowData) => {
                  return (
                    <span
                      style={{
                        transition: "all 0.2s ease-in-out",
                      }}>
                      {rowData.value}
                    </span>
                  );
                }}
              </Cell>
            </Column>

            <Column width={80} resizable align="center">
              <HeaderCell>UOM</HeaderCell>
              <Cell dataKey="parameter_uom" />
            </Column>
            <Column width={120} resizable align="center">
              <HeaderCell>Category</HeaderCell>
              <Cell dataKey="main_category_name" />
            </Column>
            <Column width={175} resizable align="center">
              <HeaderCell>Created By</HeaderCell>
              <Cell>{(rowData) => rowData.created_by || "-"}</Cell>
            </Column>
            <Column width={175} resizable align="center">
              <HeaderCell>Created At</HeaderCell>
              <Cell>
                {(rowData) => rowData.created_at?.split("T")[0] || "-"}
              </Cell>
            </Column>
            <Column width={175} resizable align="center">
              <HeaderCell>Updated By</HeaderCell>
              <Cell>{(rowData) => rowData.updated_by || "-"}</Cell>
            </Column>
            <Column width={175} resizable align="center">
              <HeaderCell>Updated At</HeaderCell>
              <Cell>
                {(rowData) => rowData.updated_at?.split("T")[0] || "-"}
              </Cell>
            </Column>
            <Column width={175} resizable align="center">
              <HeaderCell>Deleted By</HeaderCell>
              <Cell>{(rowData) => rowData.deleted_by || "-"}</Cell>
            </Column>
            <Column width={175} resizable align="center">
              <HeaderCell>Deleted At</HeaderCell>
              <Cell>
                {(rowData) => rowData.deleted_at?.split("T")[0] || "-"}
              </Cell>
            </Column>

            <Column width={120} resizable fixed="right" align="center">
              <HeaderCell>Status</HeaderCell>
              <Cell>
                {(rowData) => {
                  const hasUnsavedChanges =
                    stagedRemovals.has(rowData.id_detail_trans_m) ||
                    getEditedStatus(rowData, "parameter_name") ||
                    getEditedStatus(rowData, "value");

                  const isStagedForRemoval = stagedRemovals.has(
                    rowData.id_detail_trans_m
                  );

                  return (
                    <div>
                      {isStagedForRemoval ? (
                        <span style={{ color: "red" }}>On Delete</span>
                      ) : hasUnsavedChanges ? (
                        <span style={{ color: "green" }}>On Edit</span>
                      ) : (
                        <span> - </span>
                      )}
                    </div>
                  );
                }}
              </Cell>
            </Column>
            <Column width={100} resizable fixed="right" align="center">
              <HeaderCell>Action</HeaderCell>
              <Cell>
                {(rowData) => {
                  const hasUnsavedChanges =
                    stagedRemovals.has(rowData.id_detail_trans_m) ||
                    getEditedStatus(rowData, "parameter_name") ||
                    getEditedStatus(rowData, "value");

                  return (
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        gap: "8px",
                        transition: "background-color 0.2s ease-in-out",
                      }}>
                      <Button
                        appearance="subtle"
                        size="xs"
                        onClick={() => handleEditExistingDetail(rowData)}
                        disabled={hasUnsavedChanges}>
                        <EditIcon style={{ fontSize: "16px" }} />
                      </Button>

                      {/* Trash/Reload toggle button */}
                      <Button
                        appearance="subtle"
                        size="xs"
                        color={hasUnsavedChanges ? "green" : "red"}
                        onClick={() =>
                          hasUnsavedChanges
                            ? handleRevertRowChanges(rowData.id_detail_trans_m)
                            : handleDeactiveExistingDetail(rowData)
                        }>
                        {hasUnsavedChanges ? (
                          <ReloadIcon style={{ fontSize: "16px" }} />
                        ) : (
                          <TrashIcon style={{ fontSize: "16px" }} />
                        )}
                      </Button>
                    </div>
                  );
                }}
              </Cell>
            </Column>
          </Table>
          <div style={{ padding: 20 }}>
            <Pagination
              prev
              next
              first
              last
              ellipsis
              boundaryLinks
              maxButtons={5}
              size="xs"
              layout={["total", "-", "pager", "skip"]}
              total={totalRowCount}
              limit={manualLimit}
              activePage={page}
              onChangePage={setPage}
              onChangeLimit={handleChangeAddLimit}
            />
          </div>
        </Panel>
      </>
    );
  };

  useEffect(() => {
    console.log("data spv = ", dataSpv);
  }, [dataSpv]);

  const handleUpdateGranulasiData = async () => {
    try {
      const errors = {};
      const requiredFields = [
        "granule_date",
        "granule_ampere",
        "granule_power",
        "granule_remarks",
        "spv_employee_id",
      ];
      for (const field of requiredFields) {
        if (
          !granuleData[field] ||
          (typeof granuleData[field] === "string" &&
            granuleData[field].trim() === "")
        ) {
          errors[field] = `Kolom ${field.replace(/_/g, " ")} wajib diisi!`;
        }
      }

      if (Object.keys(errors).length > 0) {
        setErrorsGranuleForm(errors);
        toaster.push(
          Messages("error", "Mohon isi semua kolom yang wajib diisi!"),
          { placement: "topCenter", duration: 3000 }
        );
        return;
      }

      const basePayload = {
        granule_date: granuleData.granule_date,
        granule_ampere: parseFloat(granuleData.granule_ampere),
        granule_power: parseFloat(granuleData.granule_power),
        granule_remarks: granuleData.granule_remarks,
        spv_employee_id: granuleData.spv_employee_id,
        updated_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
        revised_remarks: granuleData.revised_remarks,
        id_detail_trans: parseInt(idDetailTrans),
        line_type: Number(headerData.line_type),
      };

      let mainUpdatePayload = { ...basePayload };

      if (Number(headerData.line_type) === 0) {
        const existingGranulasiDetail = [];
        const newGranulasiDetail = [];

        Object.values(stagedChanges).forEach((detail) => {
          existingGranulasiDetail.push({
            id_detail_trans_m: detail.id_detail_trans_m,
            type: detail.type,
            value: String(detail.value),
            id_matrix: detail.id_matrix,
            id_categories: detail.id_categories,
            is_active: detail.is_active,
            updated_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
          });
        });

        (granulasiDetail || []).forEach((detail) => {
          newGranulasiDetail.push({
            id_header_trans: granuleData.id_header_trans,
            type: detail.type || "Granulasi",
            value: String(detail.value),
            id_matrix: detail.id_matrix,
            id_categories: detail.id_categories,
            created_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
            is_active: 1,
          });
        });

        mainUpdatePayload.granule_detail = existingGranulasiDetail;
        mainUpdatePayload.create_detail = newGranulasiDetail;
      }
      //AUTOMATE LINE
      else {
        mainUpdatePayload.granule_detail = [];
      }

      const mainUpdateRes =
        await ApiTsdpTDGranulasi().UpdateTsTransactionGranulasi(
          mainUpdatePayload
        );

      if (mainUpdateRes.status !== 200) {
        toaster.push(
          Messages(
            "error",
            `Error saving granulasi data: ${mainUpdateRes.message}`
          ),
          { placement: "topCenter", duration: 3000 }
        );
        return;
      }

      // AUTOMATE LINE new details add
      if (Number(headerData.line_type) === 1) {
        if (formAddDetail.length > 0) {
          const newAutoDetailsPayload = formAddDetail.map((detail) => {
            let parsedDataString;
            try {
              parsedDataString =
                typeof detail.data === "string"
                  ? detail.data
                  : JSON.stringify(detail.data);
            } catch (e) {
              console.error(
                "Error stringifying JSON for auto detail value:",
                e
              );
              parsedDataString = JSON.stringify({});
            }

            return {
              id_header_trans: granuleData.id_header_trans,
              type: "Granulasi",
              description: detail.description || "",
              parameter: detail.parameter || "full_data",
              value: parsedDataString,
              created_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
            };
          });

          const autoDetailRes =
            await ApiTsdpAuto().postCreateTransactionAutoDetail(
              newAutoDetailsPayload
            );

          if (autoDetailRes.status !== 200) {
            toaster.push(
              Messages(
                "error",
                `Error adding new automate details: ${
                  autoDetailRes.message || "Unknown error"
                }`
              ),
              { placement: "topCenter", duration: 3000 }
            );
            return;
          }
        }
      }

      let failedUploads = 0;
      let failedStatusUpdates = 0;

      // --- UPDATE STATUS OF EXISTING ATTACHMENTS ---
      const originalAttachments = await ApiTsdpTDAttachments()
        .getTsTransactionById({
          id_detail_trans: parseInt(granuleData.id_detail_trans),
        })
        .then((res) => res.data || []);

      for (const updatedAttachment of formAttachments) {
        // Find the original state of this attachment
        const originalAttachment = originalAttachments.find(
          (orig) => orig.id_attachments === updatedAttachment.id_attachments
        );

        console.log(
          "originalAttachment status = ",
          originalAttachment?.is_active
        );
        console.log(
          "updatedAttachment status = ",
          updatedAttachment?.is_active
        );

        // if the updatedAttachment's status changed
        if (updatedAttachment.is_active === 0) {
          const payload = {
            deleted_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
            id_attachments: updatedAttachment.id_attachments,
          };

          // deactivated
          console.log(
            `Deactivating attachment ID: ${updatedAttachment.id_attachments}`
          );
          const statusRes = await ApiTsdpTDAttachments().softDeleteAttachment(
            payload
          );

          if (statusRes.status !== 200) {
            failedStatusUpdates++;
          }
        }
      }
      // --- UPLOAD NEW ATTACHMENTS ---
      const newAttachments = formAttachments.filter(
        (file) => file.data !== undefined
      );
      console.log(`Found ${newAttachments.length} new attachments to upload.`);
      for (const fileItem of newAttachments) {
        console.log("masuk sini");
        const formData = new FormData();
        formData.append("Files", fileItem.data.blobFile, fileItem.data.name);
        formData.append("id_header_trans", headerData.id_header_trans);
        formData.append("type", "Granulasi");
        formData.append("path", "tsdp");
        formData.append("created_by", sessionAuth.employee_id);

        const postRes = await ApiTsdpTDAttachments().postTsTransactionUpload(
          formData
        );

        if (postRes.status !== 200) {
          failedUploads++;
        }
      }

      if (failedUploads > 0) {
        toaster.push(
          Messages("error", `${failedUploads} attachments failed to upload.`),
          { duration: 5000 }
        );
      } else {
        toaster.push(
          Messages(
            "success",
            "Granule data and attachments saved successfully!"
          ),
          { duration: 3000 }
        );
        router.push(`/user_module/tsdp/creation/granulasi/list`);
      }
    } catch (error) {
      console.error("Error updating granulasi data: ", error);
      toaster.push(Messages("error", "An unexpected error occurred."), {
        placement: "topCenter",
        duration: 3000,
      });
    }
  };

  const fetchAttachmentDataByID = async (granuleId) => {
    try {
      const res = await ApiTsdpTDAttachments().getTsTransactionById({
        id_header_trans: parseInt(granuleId),
        type: "Granulasi",
      });

      if (res.status === 200) {
        setFormAttachments(res.data);
      } else {
        console.error("Error fetching binder data: ", res.message);
      }
    } catch (error) {
      console.log("Error fetching binder data : ", error);
    }
  };

  const fetchAllPageData = async (granuleId) => {
    try {
      const granuleRes =
        await ApiTsdpTDGranulasi().getTsTransactionGranulasiById({
          id_detail_trans: parseInt(granuleId),
        });

      console.log("granuleRes = ", granuleRes.status);

      if (granuleRes.status !== 200) {
        toaster.push(Messages("error", "Failed to fetch granule record."));
        return;
      }

      const fetchedGranuleData = granuleRes.data;

      setGranuleData(fetchedGranuleData);
      fetchAttachmentDataByID(fetchedGranuleData.id_header_trans);

      const headerRes = await ApiTsdpTH().getTsTransHeaderById({
        id_header_trans: parseInt(fetchedGranuleData.id_header_trans),
      });

      if (headerRes.status !== 200) {
        toaster.push(Messages("error", "Failed to fetch header record."));
        return;
      }
      const fetchedHeaderData = headerRes.data;
      console.log("granuledetaill = ", fetchedGranuleData.granule_detail);
      setHeaderData(fetchedHeaderData);

      // MANUAL LINE:
      if (Number(fetchedHeaderData.line_type) === 0) {
        const initialOriginals = {};
        fetchedGranuleData.granule_detail.forEach((detail) => {
          initialOriginals[detail.id_detail_trans_m] = { ...detail }; // Store full original object
          initialOriginals[detail.id_detail_trans_m].value = String(
            detail.value
          );
        });
        setOriginalManualDetails(initialOriginals);

        setStagedChanges({});
        setStagedRemovals(new Set());
        setGranulasiDetail([]);
      }
    } catch (error) {
      console.error("Error during initial data fetch:", error);
      toaster.push(
        Messages("error", "An error occurred while fetching page data.")
      );
    }
  };

  const fetchManualData = async () => {
    const result = await ApiTsReport().getReport2();
    setIdHeaderTrans1(result.data || []);
  };

  useEffect(() => {
    console.log("idheadertrans 1 = ", idHeaderTrans1);
  }, [idHeaderTrans1]);

  useEffect(() => {
    console.log("granule data = ", granuleData);
  }, [granuleData]);

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push("/");
    }
  }, []);

  useEffect(() => {
    if (router.isReady) {
      console.log("router is ready", router.query);

      const granuleDataId = router.query.data;
      console.log("granuleData = ", granuleDataId);

      if (granuleDataId) {
        fetchAllPageData(granuleDataId);
        setIdDetailTrans(granuleDataId);
      }
      fetchDropdownData();
      fetchManualData();
      handleGetTsMatrixCategoriesApi();
    }
  }, [router.isReady, router.query]);

  const handleUploadAttachments = (file) => {
    setFormAttachments((prevAttachments) => [
      ...prevAttachments,
      {
        type: "Granulasi",
        data: file,
      },
    ]);
  };

  const handleRemoveAttachments = (fileKey) => {
    setFormAttachments((prevAttachments) =>
      prevAttachments.filter(
        (attachment) => attachment?.data?.fileKey !== fileKey
      )
    );
  };

  const handleActivateDeactivate = (idAttachment, isActive) => {
    setFormAttachments((prevAttachments) =>
      prevAttachments.map((attachment) => {
        if (attachment.id_attachments === idAttachment) {
          return { ...attachment, is_active: isActive ? 0 : 1 };
        }
        return attachment;
      })
    );
  };

  const getAttachmentsForDisplay = () => {
    const attachments = formAttachments || [];

    const formatFileSize = (bytes) => {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    return attachments.map((attachment) => {
      if (attachment.id_attachments) {
        const fileName = attachment.path.substring(
          attachment.path.lastIndexOf("/") + 1
        );
        return {
          name: fileName,
          url: `${process.env.NEXT_PUBLIC_STORAGE}/${attachment.path}`,
          id_attachments: attachment.id_attachments,
          is_active: attachment.is_active,
          size: null,
        };
      } else {
        return {
          name: attachment.data.name,
          url: URL.createObjectURL(attachment.data.blobFile),
          flag: "new",
          fileKey: attachment.data.fileKey,
          size: formatFileSize(attachment.data.blobFile.size),
        };
      }
    });
  };

  const attachmentsToDisplay = getAttachmentsForDisplay();

  const AttachmentDisplay = ({ attachment }) => (
    <Panel
      bordered
      className="mt-3 flex flex-col"
      style={{ width: 230, height: 350, position: "relative" }}
      shaded>
      <span style={{ flexGrow: 1 }}>
        <img
          src={attachment.url}
          alt={attachment.name}
          style={{
            maxWidth: 200,
            maxHeight: 250,
            objectFit: "contain",
            objectPosition: "center",
            display: "block",
            margin: "0 auto",
          }}
        />
        <p
          className="word-break mt-1"
          style={{ maxHeight: 20, overflow: "hidden" }}>
          File Name : {attachment.name}
        </p>
        {attachment.size && (
          <p style={{ color: "grey", fontSize: "12px", marginTop: "4px" }}>
            File Size : {attachment.size}
          </p>
        )}
      </span>
      {attachment.flag === "new" ? (
        <div style={{ position: "absolute", bottom: 10, right: 10 }}>
          <Button
            appearance="ghost"
            color="red"
            onClick={() => handleRemoveAttachments(attachment.fileKey)}>
            <TrashIcon />
          </Button>
        </div>
      ) : (
        <div style={{ position: "absolute", bottom: 10, left: 10, right: 10 }}>
          <Button
            appearance="ghost"
            size="sm"
            color={attachment.is_active === 0 ? "green" : "red"}
            onClick={() =>
              handleActivateDeactivate(
                attachment.id_attachments,
                attachment.is_active
              )
            }
            block>
            {attachment.is_active === 0 ? "Activate" : "Deactivate"}
          </Button>
        </div>
      )}
    </Panel>
  );

  return (
    <div>
      <Head>
        <title>TSDP Creation Granulasi Edit</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item>TSDP</Breadcrumb.Item>
            <Breadcrumb.Item>Creation</Breadcrumb.Item>
            <Breadcrumb.Item>Granulasi</Breadcrumb.Item>
            <Breadcrumb.Item active>Edit</Breadcrumb.Item>
          </Breadcrumb>

          <Panel bordered className="mb-2 mt-4">
            <Stack justifyContent="flex-start">
              <h3>TS Edit Granulasi</h3>
            </Stack>
            <hr className="my-4" />
            <hr className="my-4" />

            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Kode Batch</Form.ControlLabel>
                <Form.Control
                  name="batch_no"
                  value={headerData.batch_no || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Line Type</Form.ControlLabel>
                <RadioGroup
                  name="line_type"
                  value={headerData.line_type ?? "-"}
                  readOnly
                  disabled>
                  <Radio value={1}>Automate</Radio>
                  <Radio value={0}>Manual</Radio>
                </RadioGroup>
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Line</Form.ControlLabel>
                <Form.Control
                  name="id_line"
                  value={headerData.id_line || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Jenis Sediaan</Form.ControlLabel>
                <Form.Control
                  name="sediaan_type"
                  value={headerData.sediaan_type || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                <Form.Control
                  name="product_code"
                  value={headerData.product_code || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Nama Produk</Form.ControlLabel>
                <Form.Control
                  name="product_name"
                  value={headerData.product_name || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Skala Produksi</Form.ControlLabel>
                <Form.Control
                  name="production_scale"
                  value={headerData.production_scale || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Fokus Trial</Form.ControlLabel>
                <Form.Control
                  name="trial_focus"
                  value={headerData.trial_focus || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>No PPI</Form.ControlLabel>
                <Form.Control
                  name="ppi_no"
                  value={headerData.ppi_no || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Tujuan Proses</Form.ControlLabel>
                <Form.Control
                  name="process_purpose"
                  value={headerData.process_purpose || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Background</Form.ControlLabel>
                <Form.Control
                  name="background"
                  value={headerData.background || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Tanggal Proses</Form.ControlLabel>
                <Form.Control
                  name="process_date"
                  value={headerData.process_date?.split("T")[0] || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Tanggal Granulasi <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  block
                  oneTap
                  value={
                    granuleData.granule_date
                      ? new Date(granuleData.granule_date)
                      : null
                  }
                  format="dd-MM-yyyy"
                  onChange={(value) => {
                    setGranuleData((prevFormValue) => ({
                      ...prevFormValue,
                      granule_date: value,
                    }));
                    setErrorsGranuleForm((prevErrors) => ({
                      ...prevErrors,
                      granule_date: undefined,
                    }));
                  }}
                  name="granule_date"
                />
                {errorsGranuleForm.granule_date && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsGranuleForm.granule_date}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>
                  Ampere <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="granule_ampere"
                    placeholder="granule_ampere"
                    value={granuleData.granule_ampere || ""}
                    onChange={(value) => {
                      setGranuleData((prevFormValue) => ({
                        ...prevFormValue,
                        granule_ampere: value,
                      }));
                      setErrorsGranuleForm((prevErrors) => ({
                        ...prevErrors,
                        granule_ampere: undefined,
                      }));
                    }}
                    type="number"
                  />
                  <InputGroup.Addon>A</InputGroup.Addon>
                </InputGroup>
                {errorsGranuleForm.granule_ampere && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsGranuleForm.granule_ampere}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>
                  Power <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="granule_power"
                    placeholder="granule_power"
                    value={granuleData.granule_power || ""}
                    onChange={(value) => {
                      setGranuleData((prevFormValue) => ({
                        ...prevFormValue,
                        granule_power: value,
                      }));
                      setErrorsGranuleForm((prevErrors) => ({
                        ...prevErrors,
                        granule_power: undefined,
                      }));
                    }}
                    type="number"
                  />
                  <InputGroup.Addon>Kwh</InputGroup.Addon>
                </InputGroup>
                {errorsGranuleForm.granule_power && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsGranuleForm.granule_power}
                  </div>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>
                  Granulasi Remarks <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="granule_remarks"
                    placeholder="granule_remarks"
                    value={granuleData.granule_remarks || ""}
                    onChange={(value) => {
                      setGranuleData((prevFormValue) => ({
                        ...prevFormValue,
                        granule_remarks: value,
                      }));
                      setErrorsGranuleForm((prevErrors) => ({
                        ...prevErrors,
                        granule_remarks: undefined,
                      }));
                    }}
                  />
                </InputGroup>
                {errorsGranuleForm.granule_remarks && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsGranuleForm.granule_remarks}
                  </div>
                )}
              </Form.Group>
              {granuleData.status_approval === 0 && (
                <Form.Group>
                  <Form.ControlLabel>Revised Remarks</Form.ControlLabel>
                  <InputGroup
                    style={{
                      borderColor: "red",
                    }}>
                    <Form.Control
                      readOnly
                      name="revised_remarks"
                      placeholder="revised_remarks"
                      value={granuleData.revised_remarks || ""}
                      onChange={(value) => {
                        setGranuleData((prevFormValue) => ({
                          ...prevFormValue,
                          revised_remarks: value,
                        }));
                        setErrorsGranuleForm((prevErrors) => ({
                          ...prevErrors,
                          revised_remarks: undefined,
                        }));
                      }}
                      type="text"
                    />
                  </InputGroup>
                </Form.Group>
              )}
              <Form.Group>
                <Form.ControlLabel>
                  Spv <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  name="spv_employee_id"
                  accepter={SelectPicker}
                  value={granuleData.spv_employee_id || ""}
                  data={dataSpv}
                  valueKey="employee_id"
                  labelKey="employee_name"
                  block
                  onChange={(value) => {
                    setGranuleData({ ...granuleData, spv_employee_id: value });
                    setErrorsGranuleForm((prevErrors) => ({
                      ...prevErrors,
                      spv_employee_id: undefined,
                    }));
                  }}
                />
                {errorsGranuleForm.spv_employee_id && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsGranuleForm.spv_employee_id}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Lampiran</Form.ControlLabel>
                <Uploader
                  listType="picture-text"
                  action=""
                  onUpload={handleUploadAttachments}
                  removable={false}
                  fileListVisible={false}>
                  <Button appearance="ghost">Ambil Gambar</Button>
                </Uploader>
                <Row>
                  {attachmentsToDisplay.map((attachment, index) => (
                    <Col md={5} sm={12} key={index} className="mr-5">
                      <AttachmentDisplay attachment={attachment} />
                    </Col>
                  ))}
                </Row>
              </Form.Group>

              <hr className="my-4" />
              <h4 className="my-4">Detail Parameters</h4>

              {headerData.line_type === 1 && <AutomateLineDetails />}
              {headerData.line_type === 0 && <ManualLineDetails />}
            </Form>

            <Button
              appearance="primary"
              style={{ backgroundColor: "#1fd306", marginTop: "20px" }}
              onClick={handleUpdateGranulasiData}>
              Save
            </Button>
          </Panel>
        </div>
        <Modal
          backdrop="static"
          open={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedApiType(null);
          }}
          overflow={false}>
          <Modal.Header>
            <Modal.Title>Detail Granulasi</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <SelectPicker
              name="selected_api_type"
              value={selectedApiType}
              data={dataDetail}
              valueKey="type_api"
              labelKey="type_api"
              block
              onChange={(value) => setSelectedApiType(value)}
            />
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowDetailModal(false);
                setSelectedApiType(null);
              }}
              appearance="subtle">
              Cancel
            </Button>
            <Button
              onClick={() => {
                getDetailApiData(selectedApiType);
                setSelectedApiType(null);
              }}
              appearance="primary">
              Add
            </Button>
          </Modal.Footer>
        </Modal>
        {/* AUTOMATE LINE DETAIL MODAL */}
        <Modal
          backdrop="static"
          open={showSelectedDetailModal}
          onClose={() => {
            setShowSelectedDetailModal(false);
            setSelectedDetail(null);
          }}
          overflow={false}>
          <Modal.Header>
            <Modal.Title>
              {selectedDetail?.type} Data in JSON Format
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <pre>
              {selectedDetail?.data &&
                JSON.stringify(JSON.parse(selectedDetail?.data), null, 2)}
            </pre>
          </Modal.Body>
        </Modal>

        {/* MANUAL LINE MODAL */}
        <Modal
          open={modalPengeringan}
          onClose={() => {
            setModalPengeringan(false);
            setFormDetailValue(emptyDetailForm);
          }}>
          <Modal.Header>
            <Modal.Title>Detail Granulasi</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Matrix</Form.ControlLabel>
                <Form.Control
                  name="id_matrix2"
                  accepter={SelectPicker}
                  data={tsMatrixCategories.filter(
                    (item) => item.main_category_name === "Granulasi"
                  )}
                  valueKey="value"
                  labelKey="label"
                  block
                  value={formDetailValue.id_matrix2 || ""}
                  onChange={(value) => {
                    const selectedMatrix = tsMatrixCategories.find(
                      (item) => item.value === value
                    );
                    setFormDetailValue({
                      ...formDetailValue,
                      id_matrix2: value,
                      id_categories2: selectedMatrix
                        ? selectedMatrix.id_categories
                        : null,
                    });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Value</Form.ControlLabel>
                <Form.Control
                  name="value2"
                  value={formDetailValue.value2 || ""}
                  onChange={(value) =>
                    setFormDetailValue({ ...formDetailValue, value2: value })
                  }
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              appearance="primary"
              disabled={!formDetailValue.id_matrix2}
              onClick={() => {
                if (editIndex !== null) {
                  const updatedDetails = [...granulasiDetail];
                  const selectedMatrix = tsMatrixCategories.find(
                    (item) => item.value === formDetailValue.id_matrix2
                  );
                  updatedDetails[editIndex] = {
                    ...updatedDetails[editIndex],
                    id_matrix: formDetailValue.id_matrix2,
                    id_categories: selectedMatrix?.id_categories,
                    matrix_label: selectedMatrix?.label,
                    value: String(formDetailValue.value2),
                  };
                  setGranulasiDetail(updatedDetails);
                } else if (formDetailValue.id_detail_trans_m) {
                  const detailId = formDetailValue.id_detail_trans_m;
                  const selectedMatrix = tsMatrixCategories.find(
                    (item) => item.value === formDetailValue.id_matrix2
                  );

                  const updatedContent = {
                    id_matrix: formDetailValue.id_matrix2,
                    id_categories: selectedMatrix?.id_categories,
                    parameter_name: selectedMatrix?.label,
                    value: String(formDetailValue.value2),
                  };

                  setGranuleData((prevGranuleData) => ({
                    ...prevGranuleData,
                    granule_detail: prevGranuleData.granule_detail.map((d) =>
                      d.id_detail_trans_m === detailId
                        ? { ...d, ...updatedContent }
                        : d
                    ),
                  }));

                  setStagedChanges((prevStagedChanges) => ({
                    ...prevStagedChanges,
                    [detailId]: {
                      ...(originalManualDetails[detailId] || {}),
                      ...granuleData.granule_detail.find(
                        (d) => d.id_detail_trans_m === detailId
                      ),
                      ...updatedContent,
                      is_active: stagedRemovals.has(detailId)
                        ? 0
                        : originalManualDetails[detailId]?.is_active || 1,
                    },
                  }));
                } else {
                  handleDetailGranulasi();
                }
                setModalPengeringan(false);
                setFormDetailValue(emptyDetailForm);
                setEditIndex(null);
                setIsEditingNewDetail(false);
              }}>
              Save
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </div>
  );
}
