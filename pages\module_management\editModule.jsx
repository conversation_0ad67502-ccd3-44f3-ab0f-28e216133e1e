import MainContent from "@/components/layout/MainContent";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import ModuleApi from "../api/moduleApi";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";

function EditModule({ dataToEdit, moduleCode }) {
  const { UpdateModuleApi } = ModuleApi();
  const router = useRouter();
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const MySwal = withReactContent(Swal);
  const [moduleName, setModuleName] = useState("");
  const [moduleIcon, setModuleIcon] = useState("");

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  },[]);

  useEffect(() => {
    if (dataToEdit.length > 0) {
      setModuleName(dataToEdit[0].Module_Name);
      setModuleIcon(dataToEdit[0].Module_Icon);
    }
  }, [dataToEdit]);

  // Handler
  function moduleNameHandler(event) {
    setModuleName(event.target.value);
  }
  function moduleIconHandler(event) {
    setModuleIcon(event.target.value);
  }

  const updateModuleHandler = async (event) => {
    setIsFormDisabled(true);
    event.preventDefault();

    let dataModule = {
      module_code: parseInt(moduleCode),
      module_name: moduleName,
      module_icon: moduleIcon,
    };

    const formData = new FormData();
    formData.append("module_name", moduleName);
    formData.append("module_icon", moduleIcon);

    // Send ke backend
    const { Data } = await UpdateModuleApi(dataModule);

    if (Data) {
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "Module edited successfully.",
        allowOutsideClick: false,
        timer: 2500,
      });
      router.push("/module_management");
    } else {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Module edit FAILED.",
      });
      setIsFormDisabled(false);
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Edit Module</title>
        </Head>
      </div>
      <ContainerLayout
        title="Module Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <h4>Form Edit Module</h4>
          <div className="p-5">
            <form onSubmit={updateModuleHandler}>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Module Name
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  id="employeeId"
                  value={moduleName}
                  onChange={moduleNameHandler}
                  disabled={isFormDisabled}
                  autoComplete="off"
                  aria-describedby="validationServer03Feedback"
                  required
                />
              </div>
              <div className="mb-3">
                <label htmlFor="module_icon" className="form-label">
                  Module Icon
                </label>
                <input
                  type="text"
                  className="form-control mb-5"
                  id="module_icon"
                  aria-describedby="emailHelp"
                  value={moduleIcon}
                  disabled={isFormDisabled}
                  autoComplete="off"
                  onChange={moduleIconHandler}
                  accept=".png, .jpg"
                />
              </div>

              <button
                disabled={isFormDisabled}
                type="submit"
                className="btn btn-primary p-2"
              >
                Update Module
              </button>
              <button
                type="button"
                className="btn btn-dark mx-2 p-2"
                onClick={() => router.back()}
              >
                Cancel
              </button>
            </form>
          </div>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  // const moduleCode = query.editModule[1];
  const { moduleCode } = query;
  const { GetModuleById } = ModuleApi();

  const reqData = {
    module_code: parseInt(moduleCode),
  };

  const { Data: dataToEditResult } = await GetModuleById(reqData);
  let dataToEdit = [];
  if (dataToEditResult != undefined) {
    dataToEdit = dataToEditResult;
  }

  return {
    props: {
      dataToEdit,
      moduleCode,
    },
  };
}

export default EditModule;
