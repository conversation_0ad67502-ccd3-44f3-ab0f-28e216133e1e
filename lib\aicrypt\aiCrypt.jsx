const ALGORITHM = process.env.NEXT_PUBLIC_ALGORITHM;
const CIPHER_KEY = process.env.NEXT_PUBLIC_CIPHER_KEY_FE;
const BLOCK_SIZE = 16;
  
const crypto = require('crypto');

export function aicrypt(plainText) {
    const iv = crypto.randomBytes(BLOCK_SIZE);
    const cipher = crypto.createCipheriv(ALGORITHM, CIPHER_KEY, iv);
    let cipherText;
    try {
        cipherText = cipher.update(plainText, 'utf8', 'hex');
        cipherText += cipher.final('hex');
        cipherText = iv.toString('hex') + cipherText
    } catch (e) {
        cipherText = null;
    }
    return cipherText;
}

export function iacrypt(cipherText) {
    const contents = Buffer.from(cipherText, 'hex');
    const iv = contents.slice(0, BLOCK_SIZE);
    const textBytes = contents.slice(BLOCK_SIZE);

    const decipher = crypto.createDecipheriv(ALGORITHM, CIPHER_KEY, iv);
    let decrypted = decipher.update(textBytes, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}