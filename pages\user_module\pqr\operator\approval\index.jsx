import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, useToaster, Notification, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, Loader, RadioGroup, Radio, SelectPicker, Grid, Row, Col } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import ApiMSCWeight from "@/pages/api/pqr/weight_msc/api_weight_msc";

export default function OperatorApprovalWeightMSC() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_weight_msc");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const toaster = useToaster();
  const [loading, setLoading] = useState(false);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [weightMSCDataState, setWeightMSCDataState] = useState([]);
  const [remarks, setRemarks] = useState("");
  const [remarksError, setRemarksError] = useState(false);
  const [showRemarksModal, setShowRemarksModal] = useState(false);

  const [showWeightDetailModal, setShowWeightDetailModal] = useState(false);
  const [weightDetailDataState, setWeightDetailDataState] = useState([]);

  const [selectedWeightId, setSelectedWeightId] = useState(null);
  const [password, setPassword] = useState("");

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = weightMSCDataState.filter((rowData, i) => {
    const searchFields = ["id_weight_msc", "id_trans_header", "batch_code", "remarks", "approve_status", "create_date", "create_by", "update_date", "update_by", "approve_by"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : weightMSCDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("pqr/operator"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandleGetAllNeedApproveWeightMSCApi();
    }
  }, []);

  const HandleGetAllNeedApproveWeightMSCApi = async () => {
    try {
      const res = await ApiMSCWeight().getNeedApproveMSC();

      console.log("res", res);
      if (res.status === 200) {
        setWeightMSCDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };

  const HandleEditStatusApprovalApi = async (id_weight_msc, approve_status, password) => {
    setLoading(true);
    try {
      const finalRemarks = approve_status === 1 ? "" : remarks;

      const res = await ApiMSCWeight().updateApprovalStatus({
        id_weight_msc,
        approve_status,
        approve_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
        approve_remarks: finalRemarks,
        employee_id: sessionAuth.employee_id,
        password: password,
      });

      if (res.status === 200) {
        showNotification("success", "Approval Berhasil.");
        HandleGetAllNeedApproveWeightMSCApi();
        setShowWeightDetailModal(false);
        setShowRemarksModal(false);
        setRemarks("");
        setPassword("");
      } else {
        console.error("Error on update status Approval ", res.message);
        if (res.message === "wrong username or password") {
          showNotification("error", "Username atau password salah");
          setShowWeightDetailModal(true);
        } else {
          setRemarks("");
          console.log("gagal update status Approval", res.message);
          showNotification("error", `Gagal Update Status Approval`);
        }
      }
    } catch (error) {
      console.log("Error gagal update status Approval", error);
      toaster.push({ message: "Gagal Update Status Approval", type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const HandleGetWeightMSCDetail = async (id_weight_msc) => {
    try {
      // Find the weight detail from the current data state
      const weightDetail = weightMSCDataState.find(item => item.id_weight_msc === id_weight_msc);

      if (weightDetail) {
        console.log("Data received: ", weightDetail);
        setWeightDetailDataState([weightDetail]);
      } else {
        console.log("Error: Weight detail not found");
      }
    } catch (error) {
      console.log("Error on get weight detail: ", error.message);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Operator Approval Weight MSC</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>E Release</Breadcrumb.Item>
                  <Breadcrumb.Item>Operator</Breadcrumb.Item>
                  <Breadcrumb.Item active>Approval</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Operator Persetujuan Berat MSC</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2"></div>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="cari" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                <Column width={120} align="center" sortable fullText resizable>
                  <HeaderCell>ID Berat MSC</HeaderCell>
                  <Cell dataKey="id_weight_msc" />
                </Column>
                <Column width={150} align="center" sortable fullText resizable>
                  <HeaderCell>ID Transaksi Header</HeaderCell>
                  <Cell dataKey="id_trans_header" />
                </Column>
                <Column width={180} sortable fullText resizable>
                  <HeaderCell align="center">Kode Batch</HeaderCell>
                  <Cell dataKey="batch_code" />
                </Column>
                <Column width={250} sortable fullText resizable>
                  <HeaderCell align="center">Catatan</HeaderCell>
                  <Cell dataKey="remarks" />
                </Column>
                <Column width={150} align="center" sortable fullText resizable>
                  <HeaderCell>Status Persetujuan</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      let statusText = "";
                      let statusColor = "";
                      if (rowData.approve_status === 2) {
                        statusText = "Menunggu";
                        statusColor = "orange";
                      } else if (rowData.approve_status === 1) {
                        statusText = "Disetujui";
                        statusColor = "green";
                      } else if (rowData.approve_status === 0) {
                        statusText = "Ditolak";
                        statusColor = "red";
                      }
                      return <Tag color={statusColor}>{statusText}</Tag>;
                    }}
                  </Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dibuat</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Disetujui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.approve_by || "-"}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Disetujui</HeaderCell>
                  <Cell>{(rowData) => (rowData.approve_date ? new Date(rowData.approve_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={150} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="primary"
                          color="ghost"
                          disabled={rowData.is_active === 0 || rowData.approve_status !== 2}
                          onClick={() => {
                            setSelectedWeightId(rowData.id_weight_msc);
                            HandleGetWeightMSCDetail(rowData.id_weight_msc);
                            setShowWeightDetailModal(true);
                          }}
                        >
                          Action
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>

            <Modal
              backdrop="static"
              open={showRemarksModal}
              onClose={() => {
                setShowRemarksModal(false);
                setRemarks("");
                setPassword("");
              }}
              overflow={false}
              size="sm"
            >
              <Modal.Header>
                <Modal.Title>Isi Catatan Penolakan</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <div className="form-group">
                  <label htmlFor="remarks"></label>
                  <textarea
                    id="remarks"
                    className="form-control"
                    placeholder="Masukkan Catatan penolakan"
                    value={remarks}
                    onChange={(e) => {
                      setRemarks(e.target.value);
                      setRemarksError(false);
                    }}
                    style={{ width: "100%", minHeight: "100px" }}
                  />
                  {remarksError && <p style={{ color: "red" }}>Catatan wajib diisi.</p>}
                </div>
                <div className="form-group" style={{ marginTop: "16px" }}>
                  <Input
                    type="password"
                    placeholder="Masukan Password"
                    value={password}
                    onChange={(value) => setPassword(value)}
                    style={{ width: "100%" }}
                  />
                </div>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowRemarksModal(false);
                    setRemarks("");
                    setPassword("");
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  appearance="primary"
                  color="red"
                  onClick={async () => {
                    if (!remarks.trim()) {
                      setRemarksError(true);
                      return;
                    }
                    if (!password.trim()) {
                      showNotification("error", "Password wajib diisi");
                      return;
                    }
                    setLoading(true);
                    await HandleEditStatusApprovalApi(selectedWeightId, 0, password);
                    setLoading(false);
                    setShowRemarksModal(false);
                    setPassword("");
                  }}
                  loading={loading}
                  disabled={loading}
                >
                  Tolak
                </Button>
              </Modal.Footer>
            </Modal>

            <Modal
              backdrop="static"
              open={showWeightDetailModal}
              onClose={() => {
                setShowWeightDetailModal(false);
                setPassword("");
                setRemarks("");
                setWeightDetailDataState([]);
              }}
              overflow={false}
              size={"lg"}
            >
              <Modal.Header>
                <Modal.Title>Rincian Data Berat MSC</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Panel
                  bordered
                  bodyFill
                  className="mb-3"
                  header={
                    <Stack justifyContent="space-between">
                      <Form layout="vertical">
                        <Grid fluid>
                          <Row style={{ marginBottom: "16px" }}>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>ID Berat MSC</Form.ControlLabel>
                                <Form.Control name="id_weight_msc" value={weightDetailDataState[0]?.id_weight_msc || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>ID Transaksi Header</Form.ControlLabel>
                                <Form.Control name="id_trans_header" value={weightDetailDataState[0]?.id_trans_header || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Kode Batch</Form.ControlLabel>
                                <Form.Control name="batch_code" value={weightDetailDataState[0]?.batch_code || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                          </Row>

                          <Row style={{ marginBottom: "16px" }}>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Catatan</Form.ControlLabel>
                                <Form.Control name="remarks" value={weightDetailDataState[0]?.remarks || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Status Persetujuan</Form.ControlLabel>
                                <Form.Control
                                  name="approve_status"
                                  value={weightDetailDataState[0]?.approve_status === 2 ? "Menunggu" : weightDetailDataState[0]?.approve_status === 1 ? "Disetujui" : "Ditolak"}
                                  style={{ width: "100%" }}
                                  readOnly
                                />
                              </Form.Group>
                            </Col>
                          </Row>

                          <Row style={{ marginBottom: "24px" }}>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                                <Form.Control name="create_date" value={weightDetailDataState[0]?.create_date ? new Date(weightDetailDataState[0].create_date).toLocaleDateString("en-GB") : ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                                <Form.Control name="created_by" value={weightDetailDataState[0]?.create_by || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                                <Form.Control name="update_date" value={weightDetailDataState[0]?.update_date ? new Date(weightDetailDataState[0].update_date).toLocaleDateString("en-GB") : ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                                <Form.Control name="update_by" value={weightDetailDataState[0]?.update_by || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                          </Row>

                          <Row style={{ marginBottom: "16px" }}>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Disetujui Oleh</Form.ControlLabel>
                                <Form.Control name="approve_by" value={weightDetailDataState[0]?.approve_by || ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                              <Form.Group>
                                <Form.ControlLabel>Tanggal Disetujui</Form.ControlLabel>
                                <Form.Control name="approve_date" value={weightDetailDataState[0]?.approve_date ? new Date(weightDetailDataState[0].approve_date).toLocaleDateString("en-GB") : ""} style={{ width: "100%" }} readOnly />
                              </Form.Group>
                            </Col>
                          </Row>
                        </Grid>
                      </Form>
                    </Stack>
                  }
                />
              </Modal.Body>
              <Modal.Footer>
                <div style={{ display: "flex", justifyContent: "flex-end", width: "100%" }}>
                  <InputGroup style={{ width: "150px", marginBottom: "16px" }}>
                    <Input type="password" placeholder="Masukan Password" value={password} onChange={(value) => setPassword(value)} />
                  </InputGroup>
                </div>
                <Button
                  appearance="primary"
                  color="red"
                  onClick={() => {
                    if (!password.trim()) {
                      showNotification("error", "Password wajib diisi");
                      return;
                    }
                    setShowWeightDetailModal(false);
                    setShowRemarksModal(true);
                  }}
                  loading={loading}
                  disabled={loading || !password.trim()}
                >
                  Tolak
                </Button>
                <Button
                  appearance="primary"
                  color="green"
                  onClick={async () => {
                    if (!password.trim()) {
                      showNotification("error", "Password wajib diisi");
                      return;
                    }
                    setLoading(true);
                    await HandleEditStatusApprovalApi(selectedWeightId, 1, password);
                    setPassword("");
                    setLoading(false);
                  }}
                  loading={loading}
                  disabled={loading || !password.trim()}
                >
                  Setujui
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
