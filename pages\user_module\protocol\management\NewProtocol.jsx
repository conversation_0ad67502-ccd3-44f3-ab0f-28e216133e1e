import { useRouter } from 'next/router';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import Head from 'next/head';
import MainContent from '@/components/layout/MainContent';
import { useState, useEffect } from 'react';
import {
  Form,
  Button,
  ButtonToolbar,
  Stack,
  Checkbox,
  Input,
  IconButton,
  TreePicker,
  InputNumber,
  useToaster
} from 'rsuite';
import CardTest from '@/components/CardTest';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMinus, faPlus } from '@fortawesome/free-solid-svg-icons';
import Messages from "@/components/Messages";
import ReviewerApproverApi from '@/pages/api/reviewerApprover';
import ProtocolApi from '@/pages/api/protocolApi';
import Editor from '@/components/Editor';
import ParameterApi from '@/pages/api/parameterApi';
import Trash  from '@rsuite/icons/Trash';

export default function NewProtocol() {
  const MySwal = withReactContent(Swal);
  const router = useRouter();
  const toaster = useToaster();
  let path = 'protocol/management/NewProtocol';
  let { data } = router.query;
  data ? (data = JSON.parse(data)) : data;
  const [breadcrumbsData, setBreadcrumbsData] = useState([]);
  const [treePickerReviewerData, setTreePickerReviewerData] = useState([]);
  const [treePickerReviewerData2, setTreePickerReviewerData2] = useState([]);
  const [treePickerReviewerData3, setTreePickerReviewerData3] = useState([]);
  const [treePickerReviewerData4, setTreePickerReviewerData4] = useState([]);
  const [treePickerReviewerLock, setTreePickerReviewerLock] = useState([]);
  const [treePickerApproverData, setTreePickerApproverData] = useState([]);
  const [treePickerApproverData2, setTreePickerApproverData2] = useState([]);
  const [treePickerApproverData3, setTreePickerApproverData3] = useState([]);
  const [treePickerApproverData4, setTreePickerApproverData4] = useState([]);
  const [treePickerApproverDataLock, setTreePickerApproverDataLock] = useState([]);
  const [title, setTitle] = useState('')
  const [inputFields, setInputFields] = useState([
    {
      order_number: 1,
      desc: '',
      value_reference: 0,
      min_value: 0,
      max_value: 0,
      id_parameter: 0,
    },
  ]);
  const [selectedReviewer, setSelectedReviewer] = useState([]);
  const [selectedApprover, setSelectedApprover] = useState([]);
  const [cycleMonthData, setCycleMonthData] = useState([
    0, 3, 6, 9, 12, 18, 24, 36,
  ]);
  const [t30selected, setT30Selected] = useState([0]);
  const [t40selected, setT40Selected] = useState([0]);
  const [moduleName, setModuleName] = useState('');
  const [tujuan, setTujuan] = useState('');
  const [ruangLingkup, setRuangLingkup] = useState('');
  const [dokumenAcuan, setDokumenAcuan] = useState('');
  const [tanggungJawab, setTanggungJawab] = useState('');
  const [namaProduk, setNamaProduk] = useState('');
  const [kodeProduk, setKodeProduk] = useState('');
  const [nomorBatch, setNomorBatch] = useState('');
  const [nomorPPI, setNomorPPI] = useState('');
  const [batchSize, setBatchSize] = useState('');
  const [dibuatOleh, setDibuatOleh] = useState('PT. Sakafarma Laboratories');
  const [createdBy, setCreatedBy] = useState('');
  const [komposisiFormula, setKomposisiFormula] = useState('');
  const [isDisabled, setIsDisabled] = useState(true);
  const [departmentId, setDepartmentId] = useState("");
  const [additionalNotes, setAdditionalNotes] = useState('');
  const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
  const { GetActiveReviewerByDepartmentId, GetActiveApproverByDepartmentId, GetActiveReviewerForNewProtocol, GetActiveApproverForNewProtocol } =
    ReviewerApproverApi();
  const { PostProtocol } = ProtocolApi();
  const { GetAllParameter } = ParameterApi();

  // Card Pengujian state
  const [allParameterData, setAllParameterData] = useState([]);
  const [descValue, setDescValue] = useState('');
  const [absoluteValue, setAbsoluteValue] = useState();
  const [rangeMin, setRangeMin] = useState();
  const [rangeMax, setRangeMax] = useState();
  const [dataDetail, setDataDetail] = useState({});
  const [treePickerData, setTreePickerData] = useState([]);
  const [selectedParameter, setSelectedParameter] = useState({});
  const [arrSelectedParameter, setArrSelectedParameter] = useState([
    {
      id_parameter: 0,
      department: 0,
      parameter_name: '',
      is_active: 0,
      input_method: 0,
      created_by: 0,
      method_desc: '',
    },
  ]);
const [batchNumber, setBatchNumber] = useState([
  {
    batch_number: '',
  },
])


  const GetReviewer = async (idData) => {
    const { data: userData } = await GetActiveReviewerForNewProtocol(idData);

    if (userData !== null && userData !== undefined) {
      let treePickerData = [];
      userData.map((user) => {
        const data = {
          value: user.Employee_Id,
          label: `${user.Employee_Name} - [${user.Department}]`,
        };
        treePickerData.push(data);
      });

      setTreePickerReviewerData(treePickerData);
      setTreePickerReviewerData2(treePickerData);
      setTreePickerReviewerData3(treePickerData);
      setTreePickerReviewerData4(treePickerData);
      setTreePickerReviewerLock(treePickerData);
    }
  };

  const GetAllParameterData = async () => {
    const { data: parameterData } = await GetAllParameter();
    if (parameterData !== null && parameterData !== undefined) {
      let treePickerDataStore = [];
      parameterData.map((parameter) => {
        const data = {
          value: parameter.Id_Parameter,
          label: `${parameter.Parameter_Name} - ${parameter.Method_Desc}`,
        };
        treePickerDataStore.push(data);
      });

      setTreePickerData(treePickerDataStore);
      setAllParameterData(parameterData);
    }
  };

  const GetApprover = async (idData) => {
    const { data: userData } = await GetActiveApproverForNewProtocol(idData);

    if (userData !== null && userData !== undefined) {
      let treePickerData = [];
      userData.map((user) => {
        const data = {
          value: user.Employee_Id,
          label: `${user.Employee_Name} - [${user.Department}]`,
        };
        treePickerData.push(data);
      });

      setTreePickerApproverData(treePickerData);
      setTreePickerApproverData2(treePickerData);
      setTreePickerApproverData3(treePickerData);
      setTreePickerApproverData4(treePickerData);
      setTreePickerApproverDataLock(treePickerData);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem('module_name');
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
      router.push('/');
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push('/dashboard');
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path),
    );
    if (validateUserAccess.length === 0) {
      router.push('/dashboard');
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ''
    ) {
      router.push('/dashboard');
      return;
    }

    const asPathWithoutQuery = router.asPath.split('?')[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split('/')
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      asPathNestedRoutes[1],
      asPathNestedRoutes[2],
    ];
    const capitalizeFirstLetter = (str) => {
      return str.charAt(0).toUpperCase() + str.slice(1);
    };
    const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
      const words = item.split(/(?=[A-Z])/);
      return words.map((word) => capitalizeFirstLetter(word)).join(' ');
    });
    setBreadcrumbsData(breadCrumbsResult);

    setModuleName(moduleNameValue);

    setCreatedBy(dataLogin.employee_id);
    const userDepartmentData = {
      id_department: dataLogin.department,
      employee_id: dataLogin.employee_id
    };

    setDepartmentId(dataLogin.department);

    GetReviewer(userDepartmentData);
    GetApprover(userDepartmentData);
    GetAllParameterData();
  }, []);

  const addParameterPengujian = () => {
    let newField = {
      desc: '',
      value_reference: 0,
      min_value: 0,
      max_value: 0,
      id_parameter: 0,
    };

    let newParameterField = {
      id_parameter: 0,
      department: 0,
      parameter_name: '',
      is_active: 0,
      input_method: 0,
      created_by: 0,
      method_desc: '',
    };
    setInputFields([...inputFields, newField]);
    setArrSelectedParameter([...arrSelectedParameter, newParameterField]);
  };

  const removeParameterPengujian = () => {
    let dataInputField = [...inputFields];
    let dataParameterField = [...arrSelectedParameter];
    if (dataInputField.length <= 1) {
      console.log('Cannot Delete !');
    } else {
      dataInputField.pop();
      dataParameterField.pop();
      setInputFields(dataInputField);
      setArrSelectedParameter(dataParameterField);
    }
  };

  const t30ChangeHandler = (event) => {
    const isChecked = event.target.checked;
    const value = parseInt(event.target.value);

    if (isChecked) {
      setT30Selected((prevValue) => [...prevValue, value]);
    } else {
      const newData = t30selected.filter((item) => item !== value);
      setT30Selected(newData);
    }
  };

  const t40ChangeHandler = (event) => {
    const isChecked = event.target.checked;
    const value = parseInt(event.target.value);

    if (isChecked) {
      setT40Selected((prevValue) => [...prevValue, value]);
    } else {
      const newData = t40selected.filter((item) => item !== value);
      setT40Selected(newData);
    }
  };

  const handleFormChange = (index, name, e) => {
    let data = [...inputFields];
    data[index].order_number = index + 1;
    data[index][name] = parseFloat(e.target.value);
    setInputFields(data);
  };
  const handleFormDesc = (index, v) => {
    let data = [...inputFields];
    // data.desc = v;
    data[index].order_number = index + 1;
    data[index].desc = v;
    setInputFields(data);
  };
  const handleFormParameter = (index, v) => {
    console.log('[value]',v)
    // Jika parameter dihapus dari treepicker
    if (v === null) {
      console.log("ini")
      const resetArrSelectedParameter = {
        id_parameter: 0,
        department: 0,
        parameter_name: '',
        is_active: 0,
        input_method: 0,
        created_by: 0,
        method_desc: '',
      };

      const resetInputFields = {
        desc: '',
        value_reference: 0,
        min_value: 0,
        max_value: 0,
        id_parameter: 0,
      };

      // Reset selected parameter
      let newArrSelectedParam = [...arrSelectedParameter];
      newArrSelectedParam[index] = resetArrSelectedParameter;
      setArrSelectedParameter(newArrSelectedParam);

      // Reset inputFields
      let newInputFields = [...inputFields];
      newInputFields[index] = resetInputFields;
      setInputFields(newInputFields);

      return;
    }

    // Get chosen parameter data
    const parameter = allParameterData.filter(
      (item) => item.Id_Parameter === v,
    );

    // Validate no duplication of parameter
    const idParameter = parameter[0].Id_Parameter;
    const dataInputFields = [...inputFields];
    const existingDataParameter = dataInputFields.filter(
      (item) => item.id_parameter == idParameter,
    );

    if (existingDataParameter.length > 0) {
      toaster.push(
        Messages("warning", "Please use different parameter !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      return;
    }

    // Add new parameter
    let data = [...arrSelectedParameter];
    data[index] = parameter[0];
    setArrSelectedParameter(data);

    // Set id_parameter in inputFields
    dataInputFields[index].id_parameter = idParameter;
  };

  const selectReviewerChangeHandler = (value, index) => {
    // setSelectedReviewer(value);
    let arrReviewer = [...selectedReviewer];
    arrReviewer[index] = `"${value}"`;
    setSelectedReviewer(arrReviewer);

    if (value == null) {
      // console.log("ini arr new");
      // let newArr = treePickerReviewerData2
      // let arrNewData = treePickerReviewerLock.filter(item => {
      //   if (!treePickerReviewerData2.includes(item)) {
      //     console.log("ini item " + item);
      //     return item
      //   }
      // })
      // console.log("Nilai newarr : ", newArr);
      // console.log("Nilai arrnewdata :", arrNewData);
      // newArr.push(...arrNewData)
      // console.log("Nilai newarr baru :", newArr);
      // // treePickerReviewerData2.push(arrNewData)
      // // console.log("Nilai arrNewData : ", arrNewData);
      // setTreePickerReviewerData2(newArr)
      // setTreePickerReviewerData3(newArr)

    } else {
      let arrDataUser = treePickerReviewerData.filter(item => item.value !== value);
      setTreePickerReviewerData2(arrDataUser);
      setTreePickerReviewerData3(arrDataUser);
    }
    // console.log("Nilai arrDataUser : ", arrDataUser)

  };
  const selectReviewer2ChangeHandler = (value, index) => {
    // setSelectedReviewer(value);
    let arrReviewer = [...selectedReviewer];
    arrReviewer[index] = `"${value}"`;
    setSelectedReviewer(arrReviewer);

    if (value == null) {
      // let newArr = treePickerReviewerData3
      // let arrNewData = treePickerReviewerLock.filter(item => {
      //   if (!treePickerReviewerData3.includes(item)) {
      //     return item
      //   }
      // })
      // console.log("Nilai newarr : ", newArr);
      // console.log("Nilai arrnewdata :", arrNewData);
      // newArr.push(...arrNewData)
      // console.log("Nilai newarr baru :", newArr);
      // // treePickerReviewerData3.push(arrNewData)
      // setTreePickerReviewerData3(newArr)
    } else {
      let arrDataUser = treePickerReviewerData2.filter(item => item.value !== value);
      setTreePickerReviewerData3(arrDataUser);
    }
    // if (value !== null) {
    //   let arrDataUser = treePickerReviewerData.filter(item => item.value !== value)
    //   setTreePickerReviewerData3(arrDataUser)
    // }
  };
  const selectReviewer3ChangeHandler = (value, index) => {
    // setSelectedReviewer(value);
    let arrReviewer = [...selectedReviewer];
    arrReviewer[index] = `"${value}"`;
    setSelectedReviewer(arrReviewer);

    if (value == null) {
      // let newArr = treePickerReviewerData4
      // let arrNewData = treePickerReviewerLock.filter(item => {
      //   if (!treePickerReviewerData4.includes(item)) {
      //     return item
      //   }
      // })
      // newArr.push(...arrNewData)
      // // treePickerReviewerData4.push(arrNewData)
      // setTreePickerReviewerData4(newArr)
    } else {
      let arrDataUser = treePickerReviewerData.filter(item => item.value !== value);
      setTreePickerReviewerData4(arrDataUser);
    }
    // if (value !== null) {
    //   let arrDataUser = treePickerReviewerData.filter(item => item.value !== value)
    //   setTreePickerReviewerData4(arrDataUser)
    // }
  };
  const selectApproverChangeHandler = (value, index) => {
    // setSelectedReviewer(value);
    let arrApprover = [...selectedApprover];
    arrApprover[index] = `"${value}"`;
    setSelectedApprover(arrApprover);

    if (value == null) {
      // let newArr = treePickerApproverData2
      // let arrNewData = treePickerApproverDataLock.filter(item => {
      //   if (!treePickerApproverData2.includes(item)) {
      //     return item
      //   }
      // })
      // newArr.push(...arrNewData)
      // treePickerApproverData2.push(arrNewData)
      // setTreePickerApproverData2(newArr)
    } else {
      let arrDataUser = treePickerApproverData.filter(item => item.value !== value);
      setTreePickerApproverData2(arrDataUser);
      setTreePickerApproverData3(arrDataUser);
    }
  };
  const selectApproverChangeHandler2 = (value, index) => {
    // setSelectedReviewer(value);
    let arrApprover = [...selectedApprover];
    arrApprover[index] = `"${value}"`;
    setSelectedApprover(arrApprover);

    if (value == null) {
      // let newArr = treePickerApproverData3
      // let arrNewData = treePickerApproverDataLock.filter(item => {
      //   if (!treePickerApproverData3.includes(item)) {
      //     return item
      //   }
      // })
      // newArr.push(...arrNewData)
      // treePickerApproverData3.push(arrNewData)
      // setTreePickerApproverData3(newArr)
    } else {
      let arrDataUser = treePickerApproverData2.filter(item => item.value !== value);
      setTreePickerApproverData3(arrDataUser);
    }
  };
  const selectApproverChangeHandler3 = (value, index) => {
    // setSelectedReviewer(value);
    let arrApprover = [...selectedApprover];
    arrApprover[index] = `"${value}"`;
    setSelectedApprover(arrApprover);

    // if (value == null) {
    //   let newArr = treePickerApproverData2
    //   let arrNewData = treePickerApproverDataLock.filter(item => {
    //     if (!treePickerApproverData4.includes(item)) {
    //       return item
    //     }
    //   })
    //   newArr.push(...arrNewData)
    //   treePickerApproverData4.push(arrNewData)
    //   setTreePickerApproverData4(newArr)
    // } else {
    //   let arrDataUser = treePickerApproverData.filter(item => item.value !== value)
    //   setTreePickerApproverData4(arrDataUser)
    // }
    if (value !== null) {
      let arrDataUser = treePickerApproverData.filter(item => item.value !== value);
      setTreePickerApproverData4(arrDataUser);
    }
  };
  const tujuanChangeHandler = (content) => {
    setTujuan(content);
  };
  const ruangLingkupChangeHandler = (content) => {
    setRuangLingkup(content);
  };
  const dokumenAcuanChangeHandler = (content) => {
    setDokumenAcuan(content);
  };
  const tanggungJawabChangeHandler = (content) => {
    setTanggungJawab(content);
  };
  const namaProdukChangeHandler = (content) => {
    console.log(content)
    setNamaProduk(content);
  };
  const kodeProdukChangeHandler = (content) => {
    setKodeProduk(content);
  };
  const nomorBatchChangeHandler = (content) => {
    setNomorBatch(content);
  };
  const nomorPpiChangeHandler = (content) => {
    setNomorPPI(content);
  };
  const batchSizeChangeHandler = (content) => {
    setBatchSize(content);
  };
  const formulaChangeHandler = (content) => {
    setKomposisiFormula(content);
  };
  const keteranganTambahanChangeHandler = (content) => {
    setAdditionalNotes(content);
  };

  const submitHandler = async () => {
    setIsSubmitButtonDisabled(true);
    if (
      title === '' ||
      tujuan === '' ||
      ruangLingkup === '' ||
      dokumenAcuan === '' ||
      tanggungJawab === '' ||
      namaProduk === '' ||
      // nomorBatch === '' ||
      nomorPPI === '' ||
      batchSize === '' ||
      selectedReviewer.length === 0 ||
      selectedApprover.length === 0
    ) {
      toaster.push(
        Messages("warning", "Please fill all required data !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      setIsSubmitButtonDisabled(false);
      return;
    }
    // Validate if parameter pengujian is not filled
    const isEmptyDataParameter = inputFields.map(item => {
      if (item.desc == "" && item.min_value == 0 && item.max_value == 0 && item.value_reference == 0) {
        toaster.push(
          Messages("warning", "Please fill the parameter data !"),
          {
            placement: "topEnd",
            duration: 3000,
          }
        );
        setIsSubmitButtonDisabled(false);
        return;
      } else if (isNaN(item.min_value) || isNaN(item.max_value) || isNaN(item.value_reference)) {
        toaster.push(
          Messages("warning", "Invalid parameter data !"),
          {
            placement: "topEnd",
            duration: 3000,
          }
        );
        setIsSubmitButtonDisabled(false);
        return;
      }
    });
    // Validate reviewer flow data (sequential check)
    let reviewerDataFragment = '';
    const iteratingReviewerData = selectedReviewer.map(item => {
      if (item === '"null"') {
        reviewerDataFragment += 0;
      } else {
        reviewerDataFragment += 1;
      }
    });
    if (reviewerDataFragment.includes('01')) {
      // MySwal.fire({
      //   icon: 'error',
      //   title: 'Invalid Reviewer Data !',
      //   text: 'Reviewer must be sequential.'
      // });
      toaster.push(
        Messages("warning", "Invalid reviewer data !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      setIsSubmitButtonDisabled(false);
      return;
    }
    if (!reviewerDataFragment.includes('1')) {
      // MySwal.fire({
      //   icon: 'error',
      //   title: 'Invalid Reviewer Data !',
      //   text: 'You should choose at least 1 reviewer.'
      // });
      toaster.push(
        Messages("warning", "Please choose reviewer !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      setIsSubmitButtonDisabled(false);
      return;
    }

    // Validate reviewer flow data (sequential check)
    let approverDataFragment = '';
    const iteratingApproverData = selectedApprover.map(item => {
      if (item === '"null"') {
        approverDataFragment += 0;
      } else {
        approverDataFragment += 1;
      }
    });
    if (approverDataFragment.includes('01')) {
      toaster.push(
        Messages("warning", "Invalid reviewer data !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      setIsSubmitButtonDisabled(false);
      return;
    }
    if (!approverDataFragment.includes('1')) {
      toaster.push(
        Messages("warning", "Please choose approver !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      setIsSubmitButtonDisabled(false);
      return;
    }
    // Validate reviewer data (remove null values)
    let filteredReviewer = selectedReviewer.filter(item => item !== '"null"'
    );
    // Validate approver data (remove null values)
    let filteredApprover = selectedApprover.filter(item => item !== '"null"');
    // Construct data
    const dataPostProtocol = {
      header_protocol: {
        title: title,
        product_name: namaProduk,
        department: parseInt(departmentId),
        created_by: createdBy,
        desc_purpose: tujuan,
        desc_scope: ruangLingkup,
        desc_document: dokumenAcuan,
        desc_responsibilities: tanggungJawab,
        product_code: kodeProduk,
        batch_no: nomorBatch,
        ppi_no: nomorPPI,
        batch_size: batchSize,
        manufactured_by: dibuatOleh,
        formula: komposisiFormula,
        additional_notes: additionalNotes,
        reviewer: `[${filteredReviewer}]`,
        approver: `[${filteredApprover}]`,
      },
      batch_number_protocol: batchNumber,
      detail_protocol: inputFields,
      cycle_protocol_T30_month: t30selected,
      cycle_protocol_T40_month: t40selected,
    };

    console.log(dataPostProtocol)

    setIsSubmitButtonDisabled(true);
    const { status, message } = await PostProtocol(dataPostProtocol);
    if (status === 200) {
      toaster.push(
        Messages("success", "Data saved !"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      router.push('/user_module/protocol/management/BrowseProtocol');
      return;
    }

    MySwal.fire({
      icon: 'error',
      title: 'Data insertion failed !',
      text: message,
    });
    setIsSubmitButtonDisabled(false);
    return;
  };

  const DeleteArrayParameter =  (index) => {
    console.log('[delete index] ', index)

    let dataInputField = [...inputFields];
    let dataParameterField = [...arrSelectedParameter];
    console.log('[dataInputField]',dataInputField)
    console.log('[dataParameterField]',dataParameterField)
    if (dataInputField.length <= 1) {
      console.log('Cannot Delete !');
    } else {
      // dataInputField.pop();
      // dataParameterField.pop();
      dataInputField.splice(index, 1);
      dataParameterField.splice(index, 1);
      console.log('[delete][dataInputField]',dataInputField)
      console.log('[delete][dataParameterField]',dataParameterField)
      setInputFields(dataInputField);
      setArrSelectedParameter(dataParameterField);
    }
  }


  //control batchNumber
  const addBatchNumber = () =>{
    let newBatchNumber = {
      batch_number:''
    }

    setBatchNumber([...batchNumber, newBatchNumber])
  }

  const removeBatchNumber = () =>{
    let dataBatchNumber = [...batchNumber];
    if (dataBatchNumber.length <= 1) {
      console.log('Cannot Delete !');
    } else {
      dataBatchNumber.pop();
      setBatchNumber(dataBatchNumber);
    }
  }

  const removeBatchNumberIndex = (index) => {
    let dataBatchNumber = [...batchNumber];
    if (dataBatchNumber.length <= 1) {
      console.log('Cannot Delete !');
    } else {
      dataBatchNumber.splice(index, 1);
      setBatchNumber(dataBatchNumber);
    }
  }

  const handleBatchForm = (index, v) => {
    let data = [...batchNumber];
    // data.desc = v;
    data[index].batch_number = v;
    setBatchNumber(data);
  };

  return (
    <>
      <div>
        <Head>
          <title>Add New Protocol</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <MainContent>
          <ModuleContentHeader
            breadcrumbs={breadcrumbsData}
            module_name={moduleName}
          />

          <Form layout="horizontal">
            <Stack alignItems="flex-start" direction="column" spacing={9}>
              <Stack direction="column" alignItems="flex-start">
              <div>
                    <p>Title Protocol:</p>
                    <Input
                    as="textarea"
                    value={title}
                    onChange={v=>setTitle(v)}
                    style={{ width: 224 }} 
                    />
                </div>
                <p className="font-bold">Tujuan : </p>
                <Editor
                  contentValue={tujuan}
                  valueHandler={tujuanChangeHandler}
                />
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p className="font-bold">Ruang Lingkup : </p>
                <Editor
                  contentValue={ruangLingkup}
                  valueHandler={ruangLingkupChangeHandler}
                />
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p className="font-bold">Dokumen Acuan : </p>
                <Editor
                  contentValue={dokumenAcuan}
                  valueHandler={dokumenAcuanChangeHandler}
                />
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p className="font-bold">Tanggung Jawab : </p>
                <Editor
                  contentValue={tanggungJawab}
                  valueHandler={tanggungJawabChangeHandler}
                />
              </Stack>
              <Stack
                direction="column"
                alignItems="flex-start"
                style={{ marginTop: '1rem' }}
              >
                <p className="font-bold">Rancangan Studi : </p>
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p>Nama Produk : </p>
                <Editor
                  contentValue={namaProduk}
                  valueHandler={namaProdukChangeHandler}
                />
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p>Kode Produk : </p>
                <Editor
                  contentValue={kodeProduk}
                  valueHandler={kodeProdukChangeHandler}
                />
              </Stack>
              {/* <Stack direction="column" alignItems="flex-start">
                <p>Nomor Batch : </p>
                <Editor
                  contentValue={nomorBatch}
                  valueHandler={nomorBatchChangeHandler}
                />
              </Stack> */}
              <Stack direction="column" alignItems="flex-start">
                <p>Nomor PPI : </p>
                <Editor
                  contentValue={nomorPPI}
                  valueHandler={nomorPpiChangeHandler}
                />
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                <p>Batch Size : </p>
                <Editor
                  contentValue={batchSize}
                  valueHandler={batchSizeChangeHandler}
                />
              </Stack>
              <Form.Group
                controlId="dibuatOleh"
                style={{ marginBottom: '1em' }}
              >
                <Form.ControlLabel>
                  <p className="text-xs">Dibuat Oleh :</p>
                </Form.ControlLabel>
                <Form.Control
                  name="dibuatOleh"
                  type="text"
                  value={dibuatOleh}
                  onChange={(value) => setDibuatOleh(value)}
                  readOnly
                  required
                />
              </Form.Group>
            </Stack>

            <Form.Group
              controlId="komponenPenyimpanan"
              style={{ marginTop: '2em' }}
            >
              <Form.Group>
                <Form.ControlLabel>
                  <strong>Kondisi Penyimpanan :</strong>
                </Form.ControlLabel>
              </Form.Group>
              <table className="table" style={{ maxWidth: 546 }}>
                <thead>
                  <tr>
                    <th scope="col">No</th>
                    <th scope="col" style={{ minWidth: 250 }}>
                      Kriteria
                    </th>
                    {cycleMonthData.map((value) => (
                      <th scope="col" className="text-center">
                        {value}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td scope="col">1</td>
                    <td scope="col">T30 (30 &deg;C &plusmn; 2&deg;C / 75% &plusmn; 5% RH)</td>
                    <td scope="col">
                      <input
                        type="checkbox"
                        name="T30"
                        value="0"
                        onChange={t30ChangeHandler}
                        checked={true}
                        disabled={true}
                      />
                    </td>
                    {cycleMonthData.map((item) => {
                      if (item === 0) {
                        return undefined;
                      }
                      return (
                        <td scope="col">
                          <input
                            type="checkbox"
                            name="T30"
                            value={item}
                            onChange={t30ChangeHandler}
                          />
                        </td>
                      );
                    })}
                  </tr>
                  <tr>
                    <td scope="col">2</td>
                    <td scope="col">T40 (40 &deg;C &plusmn; 2&deg;C / 75% &plusmn; 5% RH)</td>
                    <td scope="col">
                      <input
                        type="checkbox"
                        name="T30"
                        value="0"
                        onChange={t30ChangeHandler}
                        checked={true}
                        disabled={true}
                      />
                    </td>
                    {cycleMonthData.map((item) => {
                      if (item === 0) {
                        return undefined;
                      }
                      return (
                        <td scope="col">
                          <input
                            type="checkbox"
                            name="T40"
                            value={item}
                            onChange={t40ChangeHandler}
                          />
                        </td>
                      );
                    })}
                  </tr>
                </tbody>
              </table>
            </Form.Group>

            <Form.Group controlId="komposisiFormula">
              <Stack>
                <label htmlFor="komposisiFormula">
                  <strong>Komposisi Formula :</strong>
                </label>
              </Stack>
              <Stack>
                <Editor
                  contentValue={komposisiFormula}
                  valueHandler={formulaChangeHandler}
                />
              </Stack>
            </Form.Group>

            <Form.Group controlId="batchNumberProtocol">
              <Stack direction="column" alignItems="flex-start">
                <p className="mb-2">
                  <strong>Batch Number :</strong>
                </p>
                <Stack spacing={3} style={{ marginBottom: '0.5em' }}>
                  <IconButton
                    icon={<FontAwesomeIcon icon={faPlus} />}
                    color="green"
                    appearance="primary"
                    circle
                    onClick={addBatchNumber}
                  />
                  <IconButton
                    icon={<FontAwesomeIcon icon={faMinus} />}
                    color="red"
                    appearance="primary"
                    circle
                    onClick={removeBatchNumber}
                  />
                </Stack>
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                {batchNumber.map((input, index) => {
                  return (
                    <Stack
                      style={{
                        padding: '0.5em',
                        border: '0.1em solid #adadad',
                        borderRadius: '5px 5px 5px 5px',
                        marginBottom: '0.5em',
                      }}
                    >
                      <Stack
                        direction="column"
                        alignItems="flex-start"
                        style={{ marginLeft: '0.5em' }}
                      >
                        <p>
                          <strong>Batch Number</strong>
                        </p>
                        {(
                            <Stack>
                              <Input
                                value={input.batch_number}
                                as="textarea"
                                onChange={(v) => {
                                   handleBatchForm(index, v);
                                  //console.log(v)
                                }}
                                type="text"
                                required
                                />

                              <IconButton size='sm' icon={<Trash  />} 
                                appearance='ghost'
                                color='red' 
                                style={{marginLeft:'5px'}}
                                title='Delete index' onClick={() => 
                                {
                                  removeBatchNumberIndex(index)           
                                }} 
                              />
                            </Stack>
                          )}
                           
                      </Stack>
                    </Stack>
                  );
                })}
              </Stack>
            </Form.Group>

            <Form.Group controlId="parameterPengujianSpesifikasi">
              <Stack direction="column" alignItems="flex-start">
                <p className="mb-2">
                  <strong>Parameter Pengujian & Spesifikasi :</strong>
                </p>
                <Stack spacing={3} style={{ marginBottom: '0.5em' }}>
                  <IconButton
                    icon={<FontAwesomeIcon icon={faPlus} />}
                    color="green"
                    appearance="primary"
                    circle
                    onClick={addParameterPengujian}
                  />
                  <IconButton
                    icon={<FontAwesomeIcon icon={faMinus} />}
                    color="red"
                    appearance="primary"
                    circle
                    onClick={removeParameterPengujian}
                  />
                </Stack>
              </Stack>
              <Stack direction="column" alignItems="flex-start">
                {inputFields.map((input, index) => {
                  return (
                    <Stack
                      style={{
                        padding: '0.5em',
                        border: '0.1em solid #adadad',
                        borderRadius: '5px 5px 5px 5px',
                        marginBottom: '0.5em',
                      }}
                    >
                      <Stack direction="column" alignItems="flex-start">
                        <p>
                          <strong>Parameter</strong>
                        </p>
                        <TreePicker
                          value={input.id_parameter}
                          data={treePickerData}
                          defaultExpandAll
                          onChange={(v) => handleFormParameter(index, v)}
                          style={{ minWidth: 246 }}
                        />
                      </Stack>
                      <Stack
                        direction="column"
                        alignItems="flex-start"
                        style={{ marginLeft: '0.5em' }}
                      >
                        <p>
                          <strong>Spesifikasi</strong>
                        </p>
                        {arrSelectedParameter[index].Method_Desc ==
                          undefined && <Input disabled={true} />}
                        {arrSelectedParameter[index].Method_Desc !==
                          undefined &&
                          arrSelectedParameter[index].Method_Desc !== null &&
                          arrSelectedParameter[
                            index
                          ].Method_Desc.toLowerCase() === 'absolute' && (
                            <Stack>
                              <InputNumber
                                value={input.value_reference}
                                onChange={(v, e) =>
                                  handleFormChange(index, 'value_reference', e)
                                }
                                required
                              />
                              <IconButton size='sm' icon={<Trash  />} 
                                appearance='ghost'
                                color='red' 
                                style={{marginLeft:'5px'}}
                                title='Delete index' onClick={() => 
                                {
                                  DeleteArrayParameter(index)           
                                }} 
                              />
                            </Stack>
                          )}
                        {arrSelectedParameter[index].Method_Desc !==
                          undefined &&
                          arrSelectedParameter[index].Method_Desc !== null &&
                          arrSelectedParameter[
                            index
                          ].Method_Desc.toLowerCase() === 'description' && (
                            <Stack>
                              <Input
                                value={input.desc}
                                as="textarea"
                                onChange={(v) => {
                                  handleFormDesc(index, v);
                                }}
                                type="text"
                                required
                                />

                              <IconButton size='sm' icon={<Trash  />} 
                                appearance='ghost'
                                color='red' 
                                style={{marginLeft:'5px'}}
                                title='Delete index' onClick={() => 
                                {
                                  DeleteArrayParameter(index)           
                                }} 
                              />
                            </Stack>
                          )}
                        {arrSelectedParameter[index].Method_Desc !==
                          undefined &&
                          arrSelectedParameter[index].Method_Desc !== null &&
                          arrSelectedParameter[
                            index
                          ].Method_Desc.toLowerCase() === 'range' && (
                            <Stack>
                              <InputNumber
                                value={input.min_value}
                                onChange={(v, e) =>
                                  handleFormChange(index, 'min_value', e)
                                }
                                required
                                style={{ maxWidth: '7em' }}
                              />{' '}
                              -{' '}
                              <InputNumber
                                value={input.max_value}
                                onChange={(v, e) =>
                                  handleFormChange(index, 'max_value', e)
                                }
                                required
                                style={{ maxWidth: '7em' }}
                              />
                              <IconButton size='sm' icon={<Trash  />} 
                                appearance='ghost'
                                color='red' 
                                style={{marginLeft:'5px'}}
                                title='Delete index' onClick={() => 
                                  {
                                    DeleteArrayParameter(index)           
                                  }} />
                            </Stack>
                          )}
                           
                      </Stack>
                    </Stack>
                  );
                })}
              </Stack>
            </Form.Group>

            <Form.Group controlId="keteranganTambahan">
              <Stack>
                <label htmlFor="keteranganTambahan">
                  <strong>Keterangan Tambahan :</strong>
                </label>
              </Stack>
              <Stack>
                <Editor
                  contentValue={additionalNotes}
                  valueHandler={keteranganTambahanChangeHandler}
                />
              </Stack>
            </Form.Group>

            <Form.Group controlId="pilihReviewer">
              <Stack>
                <label htmlFor="pilihReviewer">
                  <strong>Pilih Reviewer 1 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerReviewerData}
                  defaultExpandAll
                  onChange={value => selectReviewerChangeHandler(value, 0)}
                  style={{ minWidth: 246 }}
                />
              </Stack>
            </Form.Group>
            <Form.Group controlId="pilihReviewer2">
              <Stack>
                <label htmlFor="pilihReviewer2">
                  <strong>Pilih Reviewer 2 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerReviewerData2}
                  defaultExpandAll
                  onChange={value => selectReviewer2ChangeHandler(value, 1)}
                  style={{ minWidth: 246 }}
                  disabled={selectedReviewer[0] === null || selectedReviewer[0] === undefined || selectedReviewer[0] === ''}
                />
              </Stack>
            </Form.Group>
            <Form.Group controlId="pilihReviewer2">
              <Stack>
                <label htmlFor="pilihReviewer2">
                  <strong>Pilih Reviewer 3 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerReviewerData3}
                  defaultExpandAll
                  onChange={value => selectReviewer3ChangeHandler(value, 2)}
                  style={{ minWidth: 246 }}
                  disabled={selectedReviewer[1] === null || selectedReviewer[1] === undefined || selectedReviewer[1] === ''}
                />
              </Stack>
            </Form.Group>
            <Form.Group controlId="pilihApprover">
              <Stack>
                <label htmlFor="pilihApprover">
                  <strong>Pilih Approver 1 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerApproverData}
                  defaultExpandAll
                  onChange={value => selectApproverChangeHandler(value, 0)}
                  style={{ minWidth: 246 }}
                />
              </Stack>
            </Form.Group>
            <Form.Group controlId="pilihApprover">
              <Stack>
                <label htmlFor="pilihApprover">
                  <strong>Pilih Approver 2 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerApproverData2}
                  defaultExpandAll
                  onChange={value => selectApproverChangeHandler2(value, 1)}
                  disabled={selectedApprover[0] === null || selectedApprover[0] === undefined || selectedApprover[0] === ''}
                  style={{ minWidth: 246 }}
                />
              </Stack>
            </Form.Group>
            <Form.Group controlId="pilihApprover">
              <Stack>
                <label htmlFor="pilihApprover">
                  <strong>Pilih Approver 3 :</strong>
                </label>
              </Stack>
              <Stack>
                <TreePicker
                  data={treePickerApproverData3}
                  defaultExpandAll
                  onChange={value => selectApproverChangeHandler3(value, 2)}
                  disabled={selectedApprover[1] === null || selectedApprover[1] === undefined || selectedApprover[1] === ''}
                  style={{ minWidth: 246 }}
                />
              </Stack>
            </Form.Group>

            <Form.Group><Stack direction='row' spacing={6}>
              <Button
                onClick={submitHandler}
                appearance="primary"
                type="submit"
                disabled={isSubmitButtonDisabled}
              >
                Submit
              </Button>
              <Button
                appearance="default"
                onClick={() => router.back()}
                disabled={isSubmitButtonDisabled}
              >
                Cancel
              </Button>
            </Stack>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}
