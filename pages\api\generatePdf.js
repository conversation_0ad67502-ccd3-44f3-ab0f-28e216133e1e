import PDFDocument from "pdfkit-table";
import fs from "fs";
import path from "path";

export default async function handler(req, res) {
  // Membuat instance PDFDocument
  const doc = new PDFDocument();

  const { dataTable, dataHeader } = req.body;
  const dataDetail = JSON.parse(dataTable);
  const dataSpecs = JSON.parse(dataHeader);

  // Menghasilkan nama file PDF dengan timestamp saat ini
  const fileName = `Print PIMS - IPC Reporting Measurement ID [${dataSpecs.Id_Transaction_H}].pdf`;

  const filePath = path.join(process.cwd(), "public", fileName);
  // const filePath = path.join(process.cwd(), "pages/pdf_viewer", fileName);

  console.log("Nilai filename : ", filePath);

  // Pipe PDF ke file di sisi server
  const writeStream = fs.createWriteStream(filePath);
  doc.pipe(writeStream);

  // Path gambar dari folder public
  const imagePath = path.join(process.cwd(), "public", "kalbe.jpeg");

  // Menggambar gambar pada header
  doc.image(imagePath, 50, 50, { width: 100 });

  // Teks yang akan ditampilkan
  const text = "REPORTING MEASUREMENT";

  // Menghitung lebar teks
  const textWidth = doc.widthOfString(text);

  // Menghitung lebar halaman
  const pageWidth = doc.page.width;

  // Menghitung posisi X untuk menengahkan teks
  const x = (pageWidth - textWidth) / 2;

  // Menambahkan teks di tengah-tengah halaman
  doc.font("Helvetica-Bold").fontSize(15).text(text, x, 145);
  doc.moveDown();

  doc.font("Helvetica").fontSize(10).text("Operator Name : ", 50);
  doc
    .moveUp()
    .font("Helvetica-Bold")
    .fontSize(10)
    .text(dataSpecs.Employee_Name, 210);

  doc.font("Helvetica").fontSize(10).text("Date Checked : ", 50);
  doc
    .moveUp()
    .font("Helvetica-Bold")
    .fontSize(10)
    .text(dataSpecs.Created_Date, 210);

  doc.font("Helvetica").fontSize(10).text("Product Code : ", 50);
  doc
    .moveUp()
    .font("Helvetica-Bold")
    .fontSize(10)
    .text(dataSpecs.Product_Code, 210);

  doc.font("Helvetica").fontSize(10).text("Amount MS : ", 50);
  doc
    .moveUp()
    .font("Helvetica-Bold")
    .fontSize(10)
    .text(dataSpecs.Amount_MS, 210);

  doc.font("Helvetica").fontSize(10).text("Amount TMS : ", 50);
  doc
    .moveUp()
    .font("Helvetica-Bold")
    .fontSize(10)
    .text(dataSpecs.Amount_TMS, 210);
  doc.moveDown();

  let counter = 0;
  const result = dataDetail.map((item) => {
    const hardness_status = item.Hardness_Status;
    const thickness_status = item.Thickness_Status;
    const diameter_status = item.Diameter_Status;
    const overall_status = item.Overall_Status;
    const reason = item.Reason;
    counter++;

    const data = {
      no: `${counter}`,
      hardness: { label: item.Hardness_Value },
      thickness: { label: item.Thickness_Value },
      diameter: { label: item.Diameter_Value },
      status: { label: item.Overall_Status },
      reason: { label: item.Reason },
    };

    const hardness_style =
      hardness_status === "TMS" ? { color: "red" } : { color: "black" };
    data.hardness.options = hardness_style;
    const thickness_style =
      thickness_status === "TMS" ? { color: "red" } : { color: "black" };
    data.thickness.options = thickness_style;
    const diameter_style =
      diameter_status === "TMS" ? { color: "red" } : { color: "black" };
    data.diameter.options = diameter_style;
    const overall_status_style =
      overall_status === "TMS" ? { color: "red" } : { color: "black" };
    data.status.options = overall_status_style;
    const reasonText = reason === "N/A" ? "-" : reason;
    data.reason.label = reasonText;

    return data;
  });

  // table
  const table = {
    headers: [
      {
        label: "No",
        property: "no",
        width: 25,
        renderer: null,
        align: "center",
      },
      {
        label: "Hardness",
        property: "hardness",
        width: 75,
        renderer: null,
        align: "center",
      },
      {
        label: "Thickness",
        property: "thickness",
        width: 75,
        renderer: null,
        align: "center",
      },
      {
        label: "Diameter",
        property: "diameter",
        width: 75,
        renderer: null,
        align: "center",
      },
      {
        label: "Status",
        property: "status",
        width: 75,
        renderer: null,
        align: "center",
      },
      {
        label: "Reason",
        property: "reason",
        width: 100,
        renderer: null,
        align: "center",
      },
    ],
    datas: result,
  };

  const tableStart = (pageWidth - 425) / 2;

  doc.table(table, { x: tableStart });
  doc.moveDown();

  doc.font("Helvetica").fontSize(10).text("Operated By : ", 50);
  doc
    .moveUp()
    .font("Helvetica-Bold")
    .fontSize(10)
    .text(`${dataSpecs.Employee_Name}`, 210);

  doc.font("Helvetica").fontSize(10).text("Reviewed and Approved By : ", 50);
  doc
    .moveUp()
    .font("Helvetica-Bold")
    .fontSize(10)
    .text(
      `${dataSpecs.Approve_By === "" ? "" : dataSpecs.Approve_By} ${
        dataSpecs.Approve_By === "" ? "-" : "at"
      } ${dataSpecs.Approve_By === "" ? "" : dataSpecs.Approve_Date}`,
      210
    );

  doc.end();

  writeStream.on("finish", () => {
    // const fileURL = `http://localhost:3000/pdf_viewer/${fileName}`;
    const fileURL = `/${fileName}`;
    res.status(200).json({ fileURL });

    setTimeout(() => {
      fs.unlink(filePath, (error) => {
        if (error) {
          console.error("Error deleting file:", error);
        } else {
        }
      });
    }, 20000);
  });
}
