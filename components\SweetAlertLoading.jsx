import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";

const MySwal = withReactContent(Swal);

// Modifikasi sweet alert loading supaya backdrop tidak bisa diklik
export const loadingAnimationNoClick = (duration = 2000) => {
  return MySwal.fire({
    title: "Loading...",
    timer: duration,
    allowOutsideClick: false,
    showConfirmButton: false,
    onBeforeOpen: () => {
      MySwal.showLoading();
    },
  });
};

export const hideLoadingAnimation = () => {
  return MySwal.close();
};
