import React, { useState, useEffect } from "react";
import {
  Table,
  But<PERSON>,
  Modal,
  Divider,
  Pagination,
  Input,
  InputGroup,
  Stack,
} from "rsuite";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";

import ApiTsReport from "@/pages/api/ts/api_ts-report";
import XLSX from "xlsx";

const { Column, HeaderCell, Cell } = Table;

export default function TsReportAutomateLine() {
  const [data, setData] = useState([]);
  const [dataFromRow, setDataFromRow] = useState({}); // data from row
  const [showDetail, setShowDetail] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      const result = await ApiTsReport().getReport1();
      setData(result.data || []);

      console.log(result);
    };
    fetchData();
  }, []);

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id_header_trans",
    "id_line",
    "sediaan_type",
    "product_code",
    "product_name",
    "batch_no",
    "binder_date",
    "binder_mix_amount",
    "binder_mix_time",
    "binder_remarks",
    "granule_date",
    "granule_ampere",
    "granule_power",
    "granule_remarks",
    "drying_date",
    "drying_lod",
    "drying_product_temp",
    "drying_exhaust_temp",
    "drying_time",
    "drying_remarks",
    "sifting_date",
    "sifting_screen_quadro",
    "sifting_bin_tumbler",
    "sifting_impeller_speed_1",
    "sifting_impeller_speed_2",
    "sifting_remarks",
    "final_mix_date",
    "final_mix_time_mix_1",
    "final_mix_time_mix_2",
    "ts_conclusion",
    "ts_followup",
    "ts_created_at",
    "ts_created_by",
    "ts_keyword",
    "ts_print_amount",
  ];

  const datas =
    data && data.length > 0
      ? data
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .some((val) => val.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const dataSearch = data?.filter((item) => {
    const str = searchKeyword.toLowerCase();
    return propertiesToFilter
      .map((property) => item[property] || "") // Use empty string as a default for undefined values
      .some((val) => val.toString().toLowerCase().includes(str));
  });

  // function to export data based on selected row, header and detail (2 sheets (header and detail) in 1 excel file))
  async function exportData(rowData) {
    const header = rowData;
    const detail = rowData.detail_trans;

    if (!detail || detail.length === 0) {
      alert("No detail data to export");
      return;
    }

    const headerData = [header];
    const detailData = detail;

    const headerWs = XLSX.utils.json_to_sheet(headerData);
    const detailWs = XLSX.utils.json_to_sheet(detailData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, headerWs, "Header");
    XLSX.utils.book_append_sheet(wb, detailWs, "Detail");

    const id_header_trans = header.id_header_trans;
    const date = new Date();
    const YYMMDD = `${date.getFullYear().toString().slice(-2)}${(
      date.getMonth() + 1
    )
      .toString()
      .padStart(2, "0")}${date.getDate().toString().padStart(2, "0")}`;

    XLSX.writeFile(
      wb,
      `ts-report-automate-line-${YYMMDD}-${id_header_trans}.xlsx`
    );

    await ApiTsReport().increasePrintAmount({
      id_header_trans: id_header_trans,
    });

    // refresh data
    const result = await ApiTsReport().getReport1();
    setData(result.data || []);
  }

  return (
    <>
      <Stack justifyContent="flex-end" direction="row" className="mb-4">
        <InputGroup inside>
          <InputGroup.Addon>
            <SearchIcon />
          </InputGroup.Addon>
          <Input
            placeholder="Search"
            value={searchKeyword}
            onChange={handleSearch}
          />
          <InputGroup.Addon
            onClick={() => {
              setSearchKeyword("");
              setPage(1);
            }}
            style={{
              display: searchKeyword ? "block" : "none",
              color: "red",
              cursor: "pointer",
            }}
          >
            <CloseOutlineIcon />
          </InputGroup.Addon>
        </InputGroup>
      </Stack>

      <Table
        bordered
        cellBordered
        rowKey="id_header_trans"
        height={500}
        data={datas}
        sortColumn={sortColumn}
        sortType={sortType}
        onSortColumn={handleSortColumn}
      >
        <Column resizable sortable>
          <HeaderCell>ID Header Trans</HeaderCell>
          <Cell dataKey="id_header_trans" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>ID Line</HeaderCell>
          <Cell dataKey="id_line" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sediaan Type</HeaderCell>
          <Cell dataKey="sediaan_type" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Product Code</HeaderCell>
          <Cell dataKey="product_code" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Product Name</HeaderCell>
          <Cell dataKey="product_name" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Batch No</HeaderCell>
          <Cell dataKey="batch_no" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Binder Date</HeaderCell>
          <Cell dataKey="binder_date" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Binder Mix Amount</HeaderCell>
          <Cell dataKey="binder_mix_amount" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Binder Mix Time</HeaderCell>
          <Cell dataKey="binder_mix_time" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Binder Remarks</HeaderCell>
          <Cell dataKey="binder_remarks" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Granule Date</HeaderCell>
          <Cell dataKey="granule_date" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Granule Ampere</HeaderCell>
          <Cell dataKey="granule_ampere" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Granule Power</HeaderCell>
          <Cell dataKey="granule_power" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Granule Remarks</HeaderCell>
          <Cell dataKey="granule_remarks" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying Date</HeaderCell>
          <Cell dataKey="drying_date" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying LOD</HeaderCell>
          <Cell dataKey="drying_lod" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying Product Temp</HeaderCell>
          <Cell dataKey="drying_product_temp" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying Exhaust Temp</HeaderCell>
          <Cell dataKey="drying_exhaust_temp" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying Time</HeaderCell>
          <Cell dataKey="drying_time" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying Remarks</HeaderCell>
          <Cell dataKey="drying_remarks" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Date</HeaderCell>
          <Cell dataKey="sifting_date" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Screen Quadro</HeaderCell>
          <Cell dataKey="sifting_screen_quadro" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Bin Tumbler</HeaderCell>
          <Cell dataKey="sifting_bin_tumbler" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Impeller Speed 1</HeaderCell>
          <Cell dataKey="sifting_impeller_speed_1" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Impeller Speed 2</HeaderCell>
          <Cell dataKey="sifting_impeller_speed_2" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Remarks</HeaderCell>
          <Cell dataKey="sifting_remarks" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Final Mix Date</HeaderCell>
          <Cell dataKey="final_mix_date" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Final Mix Time Mix 1</HeaderCell>
          <Cell dataKey="final_mix_time_mix_1" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Final Mix Time Mix 2</HeaderCell>
          <Cell dataKey="final_mix_time_mix_2" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>TS Conclusion</HeaderCell>
          <Cell dataKey="ts_conclusion" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>TS Follow Up</HeaderCell>
          <Cell dataKey="ts_followup" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>TS Created At</HeaderCell>
          <Cell dataKey="ts_created_at" />
        </Column>

        <Column resizable width={200} sortable>
          <HeaderCell>TS Created By</HeaderCell>
          <Cell dataKey="ts_created_by" />
        </Column>

        <Column resizable width={200} sortable>
          <HeaderCell>TS Keyword</HeaderCell>
          <Cell dataKey="ts_keyword" />
        </Column>

        <Column sortable>
          <HeaderCell>TS Print Amount</HeaderCell>
          <Cell dataKey="ts_print_amount" />
        </Column>

        <Column align="center" width={210} fixed="right">
          <HeaderCell>...</HeaderCell>
          <Cell style={{ padding: "2px" }}>
            {(rowData) => (
              <div>
                <Button
                  appearance="link"
                  onClick={() => {
                    setShowDetail(true, setDataFromRow(rowData));
                  }}
                >
                  View Detail
                </Button>
                <Divider vertical />
                <Button
                  appearance="link"
                  onClick={() => {
                    exportData(rowData);
                  }}
                >
                  Download
                </Button>
              </div>
            )}
          </Cell>
        </Column>
      </Table>
      <div style={{ padding: 20 }}>
        <Pagination
          prev
          next
          first
          last
          ellipsis
          boundaryLinks
          maxButtons={5}
          size="xs"
          layout={["total", "-", "limit", "|", "pager", "skip"]}
          total={
            dataSearch && dataSearch.length > 0
              ? dataSearch.length
              : data.length
          }
          pages={Math.ceil(data.length / limit)}
          activePage={page}
          limitOptions={[10, 20, 30, 40, 50]}
          limit={limit}
          onSelect={(page) => setPage(page)}
          onChangeLimit={handleChangeLimit}
          onChangePage={handleChangeLimit}
        />
      </div>

      <Modal
        backdrop="static"
        keyboard={false}
        open={showDetail}
        onClose={() => setShowDetail(false)}
      >
        <Modal.Header>
          <Modal.Title>Detail & Attachment</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-4">
            <strong>Detail</strong>
            {dataFromRow &&
              dataFromRow.detail_trans &&
              dataFromRow.detail_trans.map((detail, index) => (
                <div key={index} className="border-1 p-2 my-1 rounded-md">
                  <p>Type : {detail?.type.toUpperCase()}</p>
                  <p>Parameter : {detail.parameter}</p>
                  <p>Description : {detail.description}</p>
                  <p>Value : {detail.value}</p>
                </div>
              ))}
          </div>
          <Divider />
          <div>
            <strong>Attachments</strong>
            {dataFromRow &&
              dataFromRow.attachments &&
              dataFromRow.attachments.map((attach, index) => (
                <div key={index} className="border-1 p-2 my-1 rounded-md">
                  <p>Value : {attach.id_attachments}</p>
                  <p>Attachment : {attach.type}</p>
                  <p>Image : {attach.path}</p>
                </div>
              ))}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowDetail(false)} appearance="primary">
            Ok
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}
