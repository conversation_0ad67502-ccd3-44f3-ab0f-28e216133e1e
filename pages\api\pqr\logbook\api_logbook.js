import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiLogbook() {
  return {
    GetAllLogbook: createApiFunction("post", "pqr/logbook/list"),
    GetAllActiveLogbook: createApiFunction("post", "pqr/logbook/list-active"),
    GetAllNeedApproveLogbook: createApiFunction("post", "pqr/logbook/list-approve"),
    PostLogbook: createApiFunction("post", "pqr/logbook/create"),
    PutActivityLogbook: createApiFunction("put", "pqr/logbook/edit-activity"),
    PutApproveLogbook: createApiFunction("put", "pqr/logbook/edit-approve"),
    PutStatusLogbook: createApiFunction("put", "pqr/logbook/edit-status"),
  };
}
