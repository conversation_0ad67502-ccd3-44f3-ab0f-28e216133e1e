import { useEffect, useState } from "react";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Icon<PERSON>utton,
  Stack,
  Breadcrumb,
  Tag,
  Panel,
  Table,
  InputGroup,
  Input,
  Pagination,
  Button,
  Form,
  useToaster,
  Modal,
  ButtonGroup
} from "rsuite";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import API_MasterDataMasterDataReagenCategory from "@/pages/api/master_data/api_masterdata_master_data_reagen_category";
import { useRouter } from "next/router";

export default function MasterDataMasterDataReagenCategoryPage() {
  const [moduleName, setModuleName] = useState("");
  const [reagenDataCategory, setReagenDataCategory] = useState([]);
  const [props, setProps] = useState([]);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [sortColumn, setSortColumn] = useState();
  const [sortCategory, setSortCategory] = useState();

  const { HeaderCell, Cell, Column } = Table;

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const [formErrors, setFormErrors] = useState({});

  const toaster = useToaster();
  const router = useRouter();

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortCategory) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortCategory(sortCategory);
    }, 500);
  };

  const handleSubmit = (apiFunction) => {
    if (!formValue.category_desc) {
      setFormErrors({ category_desc: "Reagen Category Description is required." });
      return;
    }
    setFormErrors({});
    apiFunction();
  };

  const handleGetAllApi = async () => {
    try {
      const reagenDataCategory =
        await API_MasterDataMasterDataReagenCategory().getAll();
      console.log(reagenDataCategory);

      setReagenDataCategory(reagenDataCategory.data || []);
    } catch (error) {
      setReagenDataCategory([]);
    }
  };

  const emptyFormValue = {
    category_desc: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const handleEditCategory = async (selectedIdReagenCategory) => {
    try {
      const result = await API_MasterDataMasterDataReagenCategory().edit({
        ...formValue,
        id_category: selectedIdReagenCategory,
        updated_by: props.employee_id,
        updated_name: props.employee_id + "-" + props.employee_name,
      });

      if (result.status === 200) {
        handleGetAllApi();
        setShowEditModal(false);

        toaster.push(Messages("success", "Success editing Reagen Data Category!"), {
          placement: "topCenter",
          duration: 5000,
        });
        setFormValue(emptyFormValue);
      } else if (result.status === 400) {
        toaster.push(
          Messages(
            "error",
            `Error: "${result.message}". Please try again later!`
          ),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
      }
    } catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const filteredData = reagenDataCategory
    .filter((rowData, i) => {
      const searchFields = [
        "id_category",
        "category_desc",
        "created_dt",
        "created_by",
        "updated_dt",
        "updated_by",
        "deleted_dt",
        "deleted_by",
        "is_active",
      ];

      const matchesSearch = searchFields.some((field) =>
        rowData[field]
          ?.toString()
          .toLowerCase()
          .includes(searchKeyword.toLowerCase())
      );

      return matchesSearch;
    })

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  }

  const totalRowCount = searchKeyword
    ? filteredData.length
    : reagenDataCategory.length;
  const handleExportExcel = () => {
    try {
      const headerMapping = {
        id_category: "ID",
        category_desc: "Category Description",
        created_dt: "Created Date",
        created_by: "Created By",
        updated_dt: "Updated Date",
        updated_by: "Updated By",
        deleted_dt: "Deleted Date",
        deleted_by: "Deleted By",
        is_active: "Is Active",
      };

      const formattedData = reagenDataCategory.map((item) => {
        const formattedItem = {};
        for (const key in item) {
          if (headerMapping[key]) {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
        return formattedItem;
      });

      const ws = XLSX.utils.json_to_sheet(formattedData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Reagen Data");

      const date = dateFormatterDash(new Date());
      XLSX.writeFile(wb, `ReagenDataCategory ${date}.xlsx`);

      toaster.push(
        Messages("success", "Reagen data file (.xlsx) downloaded successfully"),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    } catch (error) {
      console.error("Error exporting Excel:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const getData = () => {
    if (sortColumn && sortCategory) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortCategory === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const handleAddReagen = async () => {
    try {
      const result = await API_MasterDataMasterDataReagenCategory().add({
        ...formValue,
        created_by: props.employee_id,
        created_name: props.employee_id + "-" + props.employee_name,
      });

      if (result.status === 200) {
        handleGetAllApi();
        setShowAddModal(false);

        toaster.push(
          Messages("success", "Success adding Master Data Reagen!"),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
      } else if (result.status === 400) {
        toaster.push(
          Messages(
            "error",
            `Error: "${result.message}". Please try again later!`
          ),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
      }
    } catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };
  
  const [deleteCategoryForm, setDeleteCategoryForm] = useState({
    id_category: null,
    deleted_by: null,
    reason:null,
    category_desc:null
  });

  const handleEditStatusReagen = async (selectedIdReagenCategory) => {
    console.log("selected id", selectedIdReagenCategory);
    await API_MasterDataMasterDataReagenCategory().editStatus({
      id_category: selectedIdReagenCategory,
      deleted_by: props.employee_id,
      reason:deleteCategoryForm.reason || "",
    });
    handleGetAllApi();
    setShowDeleteModal(false);
    setDeleteCategoryForm({})
  };

  useEffect(() => {
    handleGetAllApi();

    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("master_data/master_data_reagen_category")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetAllApi();
    }
  }, []);

  const handleCategoryClick = () => {
    router.push("/user_module/master_data/master_data_reagen_category")
  }

  const handleDimensionClick = () => {
    router.push("/user_module/master_data/master_data_reagen_dimension")
  }

  const handleTypeClick = () => {
    router.push("/user_module/master_data/master_data_reagen_type")
  }

  const handleCriteriaClick = () => {
    router.push("/user_module/master_data/master_data_reagen_criteria")
  }

  return (
    <>
      <div>
        <Head>
          <title>Master Data Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>Reagen Category</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Reagen Category</h5>
                <Stack className="flex gap-2">
                  <ButtonGroup>
                    <Button
                      onClick={handleCategoryClick}
                      appearance="primary"
                      active>
                      Reagen Category
                    </Button>
                    <Button
                      onClick={handleDimensionClick}
                      appearance="primary">
                      Reagen Dimension
                    </Button>
                    <Button
                      appearance="primary"
                      onClick={handleTypeClick}>
                      Reagen Type
                    </Button>
                    <Button
                      appearance="primary"
                      onClick={handleCriteriaClick}>
                      Reagen Criteria
                    </Button>
                  </ButtonGroup>
                </Stack>
              </Stack>}>
          </Panel>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusRoundIcon />}
                    appearance="primary"
                    onClick={() => {
                      setShowAddModal(true);
                    }}
                  >
                    Add
                  </IconButton>

                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getData(), limit, page)}
              sortColumn={sortColumn}
              sortCategory={sortCategory}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id_category" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Category Description</HeaderCell>
                <Cell dataKey="category_desc" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Created Date</HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Created By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.created_by} - {rowData.created_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Updated Date</HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Updated By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.updated_by} - {rowData.updated_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Deleted Date</HeaderCell>
                <Cell dataKey="deleted_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Deleted By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.deleted_by} - {rowData.deleted_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={100} sortable resizable>
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={100} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell style={{ padding: "8px" }}>
                  {(rowData) => (
                    <div>
                      <Button
                        appearance="link"
                        disabled={rowData.is_active === 0}
                        onClick={() => {
                          setShowEditModal(true);
                          setFormValue(rowData);
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        appearance="subtle"
                        onClick={() => {

                          if(rowData.is_active === 1 ){
                              setDeleteCategoryForm({
                                ...deleteCategoryForm,
                                id_category: rowData.id_category,
                                deleted_by: props.employee_id,
                                reason:"",
                                category_desc: rowData.category_desc
                              })
                              setShowDeleteModal(true)
                          }else{
                            handleEditStatusReagen(rowData.id_category)
                          }
                         
                          
                          
                          
                        }}
                      >
                        {rowData.is_active === 1 ? (
                          <TrashIcon style={{ fontSize: "16px" }} />
                        ) : (
                          <ReloadIcon style={{ fontSize: "16px" }} />
                        )}
                      </Button>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Add Modal Add Reagen Data Category*/}
        <Modal
          backdrop="static"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Add Master Reagen Data</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Category Description</Form.ControlLabel>
                <Form.Control
                  name="category_desc"
                  value={formValue.category_desc}
                  onChange={(value) => {
                    setFormValue((prevFormValue) => ({
                      ...prevFormValue,
                      category_desc: value,
                    }));
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      category_desc: undefined,
                    }));
                  }}
                  errorMessage={formErrors.category_desc}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                // Clear form errors immediately
                setFormErrors({});

                // Hide modal and reset form values
                setShowAddModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>

            <Button
              onClick={() => {
                handleSubmit(handleAddReagen);
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        {/* EDIT REAGEN CATEGORY */}
        <Modal
          backdrop="static"
          open={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Edit Reagen Category</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Category Description</Form.ControlLabel>
                <Form.Control
                  name="category_desc"
                  value={formValue.category_desc}
                  onChange={(value) => {
                    setFormValue({ ...formValue, category_desc: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      category_desc: undefined,
                    }));
                  }}
                  errorMessage={formErrors.category_desc}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setFormErrors({});
                setShowEditModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleSubmit(() => handleEditCategory(formValue.id_category));
              }}
              appearance="primary"
              type="submit"
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>

          {/* Delete Modal*/}
          <Modal
          backdrop="static"
          open={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setDeleteCategoryForm({});
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Delete Category {deleteCategoryForm.category_desc}</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Reason</Form.ControlLabel>
                <Form.Control
                  name="category_desc"
                  value={deleteCategoryForm.reason}
                  onChange={(value) => {
                    setDeleteCategoryForm((prevFormValue) => ({
                      ...prevFormValue,
                      reason: value,
                    }));
                  }}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {

                // Hide modal and reset form values
                setShowDeleteModal(false);
                setDeleteCategoryForm({});
              }}
              appearance="subtle"
            >
              Cancel
            </Button>

            <Button
              onClick={() => {
                handleEditStatusReagen(deleteCategoryForm.id_category);
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
