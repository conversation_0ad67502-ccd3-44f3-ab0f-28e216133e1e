import { useEffect, useState, useRef } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  PanelGroup,
  Placeholder,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  useToaster,
  Notification,
  ButtonGroup,
  Loader,
  DatePicker,
  RadioGroup,
  Radio,
  SelectPicker,
  FlexboxGrid,
  Grid,
  Col,
  InputNumber,
  Textarea,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";

import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import CheckRoundIcon from '@rsuite/icons/CheckRound';
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";

// Import API

import ApiRecipe from "@/pages/api/pqr/recipe/api_recipe";
import ApiMasterdata_ppi from "@/pages/api/pqr/ppi/api_masterdata_ppi";
import { useRouter } from "next/router";

export default function SupervisorApprovalRecipe() {
  const { HeaderCell, Cell, Column } = Table;
  const router = useRouter();
  const { Id } = router.query;
  const toaster = useToaster();

  // State Management
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_step");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);


  const [showConfirmDeleteModal, setShowConfirmDeleteModal] = useState(false);
  const [showRemarksModal, setShowRemarksModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [recipeStepToDelete, setRecipeStepToDelete] = useState(null);
  const [parameterToDelete, setParameterToDelete] = useState(null);
  const [showConfirmDeleteParameterModal, setShowConfirmDeleteParameterModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedParameterDetail, setSelectedParameterDetail] = useState(null);

  const [bindingParamPpiDataState, setBindingParamPpiDataState] = useState([]);
  const [selectedStepName, setSelectedStepName] = useState("");
  const [idRouter, setIdRouter] = useState(null);
  const [password, setPassword] = useState("");
  const [approvalStatus, setApprovalStatus] = useState(null);
  const [remarks, setRemarks] = useState("");
  const [remarksError, setRemarksError] = useState(false);

  const [formData, setFormData] = useState({
    id_ppi: null,
    ppi_name: null,
    wetmill:null,
    created_date: "",
    created_by: "",
    updated_date: "",
    updated_by: "",
    deleted_date: "",
    deleted_by: "",
  });

  // Form States
  const emptyAddRecipeForm = {
    id_step: null,
    is_active: 1,
  };

  const emptyAddParameterForm = {
    id_parameter: null,
    binding_type: null,
    min_value: null,
    max_value: null,
    absolute_value: null,
    description_value: null,
    wetmill: "N",
    set_point_flag: null,
    set_point_value: "",
    is_actual: null,
    created_by: null,
  };

  const emptyEditRecipeForm = {
    id_step: null,
    is_active: 1,
  };
  const emptyEditParameterForm = {
    id_parameter: null,
    order_no: null,
    wetmill: null,
    set_point_flag: null,
    set_point_value: "",
    is_actual: null,
    binding_type: null,
    min_value: null,
    max_value: null,
    absolute_value: null,
    description_value: "",
    update_by: "",
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [addRecipeForm, setAddRecipeForm] = useState(emptyAddRecipeForm);
  const [addParameterForm, setAddParameterForm] = useState(emptyAddParameterForm);
  const [editRecipeForm, setEditRecipeForm] = useState(emptyEditRecipeForm);
  const [editParameterForm, setEditParameterForm] = useState(emptyEditParameterForm);
  const [showAddParameterModal, setShowAddParameterModal] = useState(false);
  const [showEditParameterModal, setShowEditParameterModal] = useState(false);

  // Error States
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});
  const [errorsAddParameterForm, setErrorsAddParameterForm] = useState({});
  const [errorsEditParameterForm, setErrorsEditParameterForm] = useState({});

  // Detail Panel States
  const [showDetailPanel, setShowDetailPanel] = useState(false);
  const [newRecipeId, setNewRecipeId] = useState(null);
  const [currentRecipeStepId, setCurrentRecipeStepId] = useState(null);
  const [selectedRecipeDetail, setSelectedRecipeDetail] = useState(null);
  const [addLoading, setAddLoading] = useState(false);
  const [orderNumbers, setOrderNumbers] = useState([]);
  const [selectedParameterName, setSelectedParameterName] = useState("");
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(false);
  const [recipeSteps, setRecipeSteps] = useState(bindingParamPpiDataState);
  const [updatedSelectedRecipeDetail, setUpdatedSelectedRecipeDetail] = useState(selectedRecipeDetail);

  const detailPanelRef = useRef(null);

  const scrollToDetailPanel = () => {
    if (detailPanelRef.current) {
      detailPanelRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  };

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };
  const handleShowDetailModal = (parameter) => {
    setSelectedParameterDetail(parameter);
    setSelectedParameterName(parameter.paramater_name);
    setShowDetailModal(true);
  };

  const filteredData = bindingParamPpiDataState.filter((rowData) => {
    const searchFields = ["id_step", "step_name", "is_active"];
    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));
    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : bindingParamPpiDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push("/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("pqr/"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      setIdRouter(Id);
      HandleGetAllBindingParamPpiApi(Id);
      HandleGetDetailPPI(Id);
    }
  }, [Id]);

  useEffect(() => {
    const existingOrderNumbers = bindingParamPpiDataState.map((item) => item.order_no);
    setOrderNumbers(existingOrderNumbers);

    if (!bindingParamPpiDataState || bindingParamPpiDataState.length === 0) {
      setIsSubmitDisabled(true);
      return;
    }else{
      setIsSubmitDisabled(false);
      
      return;
    }
  
    
    
  }, [bindingParamPpiDataState]);

  useEffect(() => {
    setRecipeSteps([...bindingParamPpiDataState]);
  }, [bindingParamPpiDataState]);


  useEffect(() => {
    if (bindingParamPpiDataState.length > 0 && selectedRecipeDetail) {
      const updatedDetail = bindingParamPpiDataState.find(
        (step) => step.id_recipe_step === selectedRecipeDetail.id_recipe_step
      );

      if (updatedDetail) {
        setUpdatedSelectedRecipeDetail({ ...updatedDetail });
      }
    }
  }, [bindingParamPpiDataState, selectedRecipeDetail]);



  const HandleGetAllBindingParamPpiApi = async (id_ppi) => {
    try {
      const res = await ApiRecipe().getAllRecipeByIdPPI({ id_ppi: parseInt(id_ppi) });

      console.log("res", res);
      if (res.status === 200) {
        setBindingParamPpiDataState([...res.data.recipe_step]);
        console.log("Updated Data State:", res.data.recipe_step);
      } else {
        console.log("error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Api", error);
    }
  };

  const HandleGetDetailPPI = async (id_ppi) => {
    try {
      const api = ApiMasterdata_ppi();
      const response = await api.GetMasterPPIById({ id_ppi: parseInt(id_ppi) });

      if (response.status === 200) {
        const data = response.data;
        setFormData({
          id_ppi: data.id_ppi,
          ppi_name: data.ppi_name,
          product_code: data.product_code,
          created_date: new Date(data.created_date).toLocaleDateString("en-GB"),
          created_by: data.created_by,
          updated_date: data.updated_date ? new Date(data.updated_date).toLocaleDateString("en-GB") : "-",
          updated_by: data.updated_by || "-",
          deleted_date: data.deleted_date ? new Date(data.deleted_date).toLocaleDateString("en-GB") : "-",
          deleted_by: data.deleted_by || "-",
          wetmill: data.wetmill
        });
      } else {
        console.error("Failed to fetch detail data");
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
    }
  };

  const HandleEditStatusApprovalPPIApi = async (id_ppi, status_approval, password) => {
    setLoading(true);
    try {
      const finalRemarks = status_approval === 1 ? "" : remarks;
      const approvalBy = status_approval === 1 ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}` : " ";

      const res = await ApiMasterdata_ppi().editStatusApprovalPPI({
        id_ppi,
        status_approval,
        revise_remarks: finalRemarks,
        approval_by: approvalBy,
        employee_id: sessionAuth.employee_id,
        password: password,
      });

      if (res.status === 200) {
        showNotification("success", "Approval status updated successfully.");
        setShowApprovalModal(false);
        setRemarks("");
        setPassword("");
        setApprovalStatus(null);
        router.push(`/user_module/pqr/masterdata/approval/`);
      } else {
        console.error("Error on update status: ", res.message);
        if (res.message === "wrong username or password") {
          showNotification("error", "Username atau Password salah silahkan coba lagi");
          setShowApprovalModal(true);
        } else {
          setRemarks("");
          console.log("error on GetAllApi ", res.message);
          showNotification("error", `Error dalam update`);
        }
      }
    } catch (error) {
      console.error("Error on update status: ", error.message);
      showNotification("error", `Error dalam update`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Halaman Supervisor Approval Recipe</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>E Release</Breadcrumb.Item>
                  <Breadcrumb.Item>Supervisor</Breadcrumb.Item>
                  <Breadcrumb.Item>Approval</Breadcrumb.Item>
                  <Breadcrumb.Item>PPI</Breadcrumb.Item>
                  <Breadcrumb.Item active>Halaman Supervisor Approval Recipe</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Halaman Supervisor Approval Recipe</h5>
              </Stack>
            }
          ></Panel>
          {/* Detail Section */}
          <Panel bordered>
            <Form fluid>
              {/* bagian atas */}
              <FlexboxGrid className="mb-4">
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>ID PPI</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.id_ppi || "-"} readOnly />
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Kode Produk</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.product_code || "-"} readOnly />
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Nama PPI</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.ppi_name || "-"} readOnly />
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Wetmill</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.wetmill || "-"} readOnly />
                </FlexboxGrid.Item>
              </FlexboxGrid>
              <FlexboxGrid>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Dibuat Tanggal</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.created_date || "-"} readOnly />
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Diupdate Tanggal</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.updated_date || "-"} readOnly />
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                <Form.ControlLabel style={{ fontWeight: "bold" }}>Dibuat Oleh </Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.created_by || "-"} readOnly />
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Diupdate Oleh </Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.updated_by || "-"} readOnly />
                </FlexboxGrid.Item>
                </FlexboxGrid>
                <FlexboxGrid>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Dihapus Tanggal</Form.ControlLabel>
                  <Form.Control className="mb-3" name="id_ppi" value={formData.deleted_date || "-"} readOnly />
                </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Dihapus Oleh</Form.ControlLabel>
                    <Form.Control className="mb-3" name="id_ppi" value={formData.deleted_by || "-"} readOnly />
                </FlexboxGrid.Item>
              </FlexboxGrid>
            </Form>
        </Panel>

          {/* datatable recipe step section */}
          <Panel
            bordered
            bodyFill
            className="mt-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Recipe Step</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between" alignItems="center">
                  <div className="flex gap-2">
                    <InputGroup inside>
                      <InputGroup.Addon>
                        <SearchIcon />
                      </InputGroup.Addon>
                      <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                      <InputGroup.Addon
                        onClick={() => {
                          setSearchKeyword("");
                          setPage(1);
                        }}
                        style={{
                          display: searchKeyword ? "block" : "none",
                          color: "red",
                          cursor: "pointer",
                        }}
                      >
                        <CloseOutlineIcon />
                      </InputGroup.Addon>
                    </InputGroup>

                  </div>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(bindingParamPpiDataState), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                <Column width={120} align="center" sortable resizable>
                  <HeaderCell>ID Recipe Step</HeaderCell>
                  <Cell dataKey="id_recipe_step" />
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Nama Step</HeaderCell>
                  <Cell dataKey="step_name" />
                </Column>
                <Column width={100} align="center" sortable resizable>
                  <HeaderCell>Order No</HeaderCell>
                  <Cell dataKey="order_no" />
                </Column>
                <Column width={150} sortable align="center" resizable>
                  <HeaderCell>Tanggal Pembuatan</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.created_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Dibuat Oleh</HeaderCell>
                  <Cell dataKey="created_by" />
                </Column>
                <Column width={150} sortable align="center" resizable>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.updated_date ? new Date(rowData.updated_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => rowData.updated_by || "-"}</Cell>
                </Column>
                <Column width={150} sortable align="center" resizable>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>{(rowData) => (rowData.deleted_date ? new Date(rowData.deleted_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={150} sortable resizable>
                  <HeaderCell>Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => rowData.deleted_by || "-"}</Cell>
                </Column>
                <Column width={100} sortable align="center" resizable>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={70} fixed="right" align="center" resizable>
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            setSelectedRecipeDetail(rowData);
                            setSelectedStepName(rowData.step_name);
                            setShowDetailPanel(true);
                            setAddParameterForm((prevFormValue) => ({
                              ...prevFormValue,
                              id_recipe_step: rowData.id_recipe_step,
                            }));
                            setCurrentRecipeStepId(rowData.id_recipe_step);
                            setTimeout(scrollToDetailPanel, 100);
                          }}
                        >
                          <SearchIcon />
                        </Button>

                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>

            {/* detail parameter */}
            {showDetailPanel && selectedRecipeDetail && (
              <Panel
                className="mt-3"
                ref={detailPanelRef}
                bordered
                header={
                  <Stack justifyContent="space-between">
                    <h5>Rician Parameter Recipe Step : {selectedStepName}</h5>

                    <IconButton
                      icon={<CloseOutlineIcon style={{ color: "red" }} />}
                      appearance="subtle"
                      onClick={() => {
                        setShowDetailPanel(false);
                        setSelectedRecipeDetail(null);
                        setSelectedStepName("");
                        setCurrentRecipeStepId("");
                      }}
                    />
                  </Stack>
                }
              >

                <div className="mt-3">
                  <FlexboxGrid>
                  {formData.wetmill === "Y"? (
                     <FlexboxGrid.Item as={Col} colspan={24} lg={24} md={24} sm={24} xs={24}>
                     <Panel bordered className="mt-3">
                       <h5>Parameter</h5>
                     </Panel>

                     <Panel
                       ref={detailPanelRef}
                       bordered
                       bodyFill
                       header={
                         <Stack justifyContent="space-between">
                           <InputGroup inside>
                             <InputGroup.Addon>
                               <SearchIcon />
                             </InputGroup.Addon>
                             <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                             <InputGroup.Addon
                               onClick={() => {
                                 setSearchKeyword("");
                                 setPage(1);
                               }}
                               style={{
                                 display: searchKeyword ? "block" : "none",
                                 color: "red",
                                 cursor: "pointer",
                               }}
                             >
                               <CloseOutlineIcon />
                             </InputGroup.Addon>
                           </InputGroup>
                         </Stack>
                       }
                     >
                       {updatedSelectedRecipeDetail?.recipe_parameter_y?.length > 0 ? (
                         <Table
                           autoHeight
                           bordered
                           cellBordered
                           data={updatedSelectedRecipeDetail.recipe_parameter_y}
                           sortColumn={sortColumn}
                           sortType={sortType}
                           onSortColumn={handleSortColumn}>
                           <Column width={200} sortable resizable>
                             <HeaderCell align="center">Nama Parameter</HeaderCell>
                             <Cell dataKey="paramater_name" />
                           </Column>
                           <Column width={100} sortable resizable>
                             <HeaderCell align="center">Order No</HeaderCell>
                             <Cell dataKey="order_no" />
                           </Column>

                           <Column width={100} resizable>
                             <HeaderCell align="center">Flag</HeaderCell>
                             <Cell>
                               {(rowData) => {
                                 if (rowData.set_point_flag) {
                                   return <span>Set Point</span>;
                                  } else if (rowData.is_actual) {
                                    return <span>data connectivity</span>;
                                  } else {
                                    return <span>Not data connectivity</span>;
                                  }
                               }}
                             </Cell>
                           </Column>

                           <Column width={100} resizable>
                             <HeaderCell align="center">Type</HeaderCell>
                             <Cell>
                               {(rowData) => {
                                 if (rowData.binding_type === 1) {
                                   return <span>Range</span>;
                                 } else if (rowData.binding_type === 2) {
                                   return <span>Absolute</span>;
                                 } else if (rowData.binding_type === 3) {
                                   return <span>Description</span>;
                                 }
                                 return <span>-</span>;
                               }}
                             </Cell>
                           </Column>

                           <Column width={250} resizable>
                             <HeaderCell align="center">Nilai</HeaderCell>
                             <Cell>
                               {(rowData) => {
                                 if (rowData.set_point_flag) {
                                   return <span>{rowData.set_point_value}</span>;
                                 } else if (rowData.binding_type === 1) {
                                   return (
                                     <span>
                                       {rowData.min_value} - {rowData.max_value}
                                     </span>
                                   );
                                 } else if (rowData.binding_type === 2) {
                                   return <span>{rowData.absolute_value}</span>;
                                 } else if (rowData.binding_type === 3) {
                                   return <span>{rowData.description_value}</span>;
                                 }
                                 return <span>-</span>;
                               }}
                             </Cell>
                           </Column>

                           <Column width={100} resizable>
                             <HeaderCell align="center">Status</HeaderCell>
                             <Cell>
                               {(rowData) => (
                                 <span
                                   style={{
                                     color: rowData.is_active === 1 ? "green" : "red",
                                   }}
                                 >
                                   {rowData.is_active === 1 ? "Active" : "Inactive"}
                                 </span>
                               )}
                             </Cell>
                           </Column>
                           <Column width={70} fixed="right" align="center" resizable>
                             <HeaderCell align="center">Aksi</HeaderCell>
                             <Cell style={{ padding: "8px" }}>
                               {(rowData) => (
                                 <div>

                                   <Button
                                     appearance="subtle"
                                     onClick={() => {
                                       handleShowDetailModal(rowData);
                                     }}
                                   >
                                     <SearchIcon />
                                   </Button>


                                 </div>
                               )}
                             </Cell>
                           </Column>
                         </Table>
                       ) : (
                         <div
                           style={{
                             display: "flex",
                             justifyContent: "center",
                             alignItems: "center",
                             height: "100%",
                             fontSize: "16px",
                             color: "gray",
                           }}
                         >
                           Tidak ada Data
                         </div>
                       )}

                       <div style={{ padding: 20 }}>
                         <Pagination
                           prev
                           next
                           first
                           last
                           ellipsis
                           boundaryLinks
                           maxButtons={5}
                           size="xs"
                           layout={["total", "-", "limit", "|", "pager", "skip"]}
                           limitOptions={[10, 30, 50]}
                           // total={totalRowCount}
                           limit={limit}
                           activePage={page}
                           onChangePage={setPage}
                           onChangeLimit={handleChangeLimit}
                         />
                       </div>
                     </Panel>
                   </FlexboxGrid.Item>
                  ):(
                    <FlexboxGrid.Item as={Col} colspan={24} lg={24} md={24} sm={24} xs={24}>
                      <Panel bordered className="mt-3">
                        <h5>Parameter</h5>
                      </Panel>

                      <Panel
                        bordered
                        bodyFill
                        header={
                          <Stack justifyContent="space-between">
                            <InputGroup inside>
                              <InputGroup.Addon>
                                <SearchIcon />
                              </InputGroup.Addon>
                              <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                              <InputGroup.Addon
                                onClick={() => {
                                  setSearchKeyword("");
                                  setPage(1);
                                }}
                                style={{
                                  display: searchKeyword ? "block" : "none",
                                  color: "red",
                                  cursor: "pointer",
                                }}
                              >
                                <CloseOutlineIcon />
                              </InputGroup.Addon>
                            </InputGroup>
                          </Stack>
                        }
                      >
                        {updatedSelectedRecipeDetail?.recipe_parameter_n?.length > 0 ? (
                          <Table
                            autoHeight
                            bordered
                            cellBordered
                            data={updatedSelectedRecipeDetail.recipe_parameter_n}
                            sortColumn={sortColumn}
                            sortType={sortType}
                            onSortColumn={handleSortColumn}>
                            <Column width={200} sortable resizable>
                              <HeaderCell align="center">Nama Parameter</HeaderCell>
                              <Cell dataKey="paramater_name" />
                            </Column>
                            <Column width={100} sortable resizable>
                              <HeaderCell align="center">Order No</HeaderCell>
                              <Cell dataKey="order_no" />
                            </Column>

                            <Column width={100} resizable>
                              <HeaderCell align="center">Flag</HeaderCell>
                              <Cell>
                                {(rowData) => {
                                  if (rowData.set_point_flag) {
                                    return <span>Set Point</span>;
                                  } else if (rowData.is_actual) {
                                    return <span>data connectivity</span>;
                                  } else {
                                    return <span>Not data connectivity</span>;
                                  }
                                }}
                              </Cell>
                            </Column>

                            <Column width={100} resizable>
                              <HeaderCell align="center">Tipe</HeaderCell>
                              <Cell>
                                {(rowData) => {
                                  if (rowData.binding_type === 1) {
                                    return <span>Range</span>;
                                  } else if (rowData.binding_type === 2) {
                                    return <span>Absolute</span>;
                                  } else if (rowData.binding_type === 3) {
                                    return <span>Description</span>;
                                  }
                                  return <span>-</span>;
                                }}
                              </Cell>
                            </Column>

                            <Column width={250} resizable>
                              <HeaderCell align="center">Nilai</HeaderCell>
                              <Cell>
                                {(rowData) => {
                                  if (rowData.set_point_flag) {
                                    return <span>{rowData.set_point_value}</span>;
                                  } else if (rowData.binding_type === 1) {
                                    return (
                                      <span>
                                        {rowData.min_value} - {rowData.max_value}
                                      </span>
                                    );
                                  } else if (rowData.binding_type === 2) {
                                    return <span>{rowData.absolute_value}</span>;
                                  } else if (rowData.binding_type === 3) {
                                    return <span>{rowData.description_value}</span>;
                                  }
                                  return <span>-</span>;
                                }}
                              </Cell>
                            </Column>

                            <Column width={100} resizable>
                              <HeaderCell align="center">Status</HeaderCell>
                              <Cell>
                                {(rowData) => (
                                  <span
                                    style={{
                                      color: rowData.is_active === 1 ? "green" : "red",
                                    }}
                                  >
                                    {rowData.is_active === 1 ? "Active" : "Inactive"}
                                  </span>
                                )}
                              </Cell>
                            </Column>
                            <Column width={70} fixed="right" align="center" >
                              <HeaderCell align="center">Aksi</HeaderCell>
                              <Cell style={{ padding: "8px" }}>
                                {(rowData) => (
                                  <div>

                                    <Button
                                      appearance="subtle"
                                      onClick={() => {
                                        handleShowDetailModal(rowData);
                                      }}
                                    >
                                      <SearchIcon />
                                    </Button>


                                  </div>
                                )}
                              </Cell>
                            </Column>
                          </Table>
                        ) : (
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              height: "100%",
                              fontSize: "16px",
                              color: "gray",
                            }}
                          >
                            Tidak ada Data
                          </div>
                        )}

                        <div style={{ padding: 20 }}>
                          <Pagination
                            prev
                            next
                            first
                            last
                            ellipsis
                            boundaryLinks
                            maxButtons={5}
                            size="xs"
                            layout={["total", "-", "limit", "|", "pager", "skip"]}
                            limitOptions={[10, 30, 50]}
                            // total={totalRowCount}
                            limit={limit}
                            activePage={page}
                            onChangePage={setPage}
                            onChangeLimit={handleChangeLimit}
                          />
                        </div>
                      </Panel>
                    </FlexboxGrid.Item>
                  )}
                   
                    
                  </FlexboxGrid>
                </div>
              </Panel>
            )}

            <Panel className="mt-3" header={
              <Stack justifyContent="flex-end">
                <div>
                  <IconButton
                    icon={<CheckRoundIcon />}
                    appearance="primary"
                    color="green"
                    onClick={() => {
                      setShowApprovalModal(true);
                    }}
                    disabled={isSubmitDisabled}
                  >
                    Approve
                  </IconButton>
                </div>
              </Stack>
            }>
            </Panel>



            {/* modal detail parameter */}
            <Modal
              backdrop="static"
              open={showDetailModal}
              onClose={() => {
                setShowDetailModal(false);
                setSelectedParameterDetail(null);
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title style={{ overflow: "visible" }}>Rincian Parameter : {selectedParameterName}</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                {selectedParameterDetail && (
                  <Form fluid>
                    <FlexboxGrid>
                      <FlexboxGrid.Item colspan={12} style={{ paddingRight: "10px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Dibuat Tanggal</Form.ControlLabel>
                          <Form.Control name="created_date" value={new Date(selectedParameterDetail.created_date).toLocaleDateString("en-GB") || "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Diperbarui Tanggal</Form.ControlLabel>
                          <Form.Control name="updated_date" value={selectedParameterDetail.updated_date ? new Date(selectedParameterDetail.updated_date).toLocaleDateString("en-GB") : "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Dihapus Tanggal</Form.ControlLabel>
                          <Form.Control name="deleted_date" value={selectedParameterDetail.deleted_date ? new Date(selectedParameterDetail.deleted_date).toLocaleDateString("en-GB") : "-"} readOnly />
                        </Form.Group>
                      </FlexboxGrid.Item>
                      <FlexboxGrid.Item colspan={12}>
                        <Form.Group>
                          <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                          <Form.Control name="created_by" value={selectedParameterDetail.created_by || "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                          <Form.Control name="updated_by" value={selectedParameterDetail.updated_by || "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                          <Form.Control name="deleted_by" value={selectedParameterDetail.deleted_by || "-"} readOnly />
                        </Form.Group>
                      </FlexboxGrid.Item>
                    </FlexboxGrid>
                  </Form>
                )}
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowDetailModal(false);
                    setSelectedParameterDetail(null);
                  }}
                  appearance="subtle"
                >
                  Tutup
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal approve */}
            <Modal
              backdrop="static"
              open={showApprovalModal}
              onClose={() => {
                setShowApprovalModal(false);
                setPassword("");
                setRemarks("");
                setApprovalStatus(null);
              }}
              overflow={false}
              size="sm"
            >
              <Modal.Header>
                <Modal.Title>Konfirmasi Approve</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Stack direction="column" spacing={16} alignItems="stretch">
                  <Panel bordered style={{ padding: "15px", borderRadius: "6px" }}>
                    <Form fluid>
                      <Form.Group>
                        <Form.ControlLabel>Pilih</Form.ControlLabel>
                        <RadioGroup
                          inline
                          name="approvalAction"
                          value={approvalStatus}
                          onChange={(value) => {
                            setApprovalStatus(value);
                            setPassword("");
                            setRemarks("");

                          }}
                        >
                          <Radio value={1}>
                            <span style={{ fontWeight: "500", color: "#4CAF50" }}>Approve</span>
                          </Radio>
                          <Radio value={0}>
                            <span style={{ fontWeight: "500", color: "#F44336" }}>Revisi</span>
                          </Radio>
                        </RadioGroup>
                      </Form.Group>

                      {approvalStatus === 0 && (
                        <Form.Group>
                          <Form.ControlLabel>Alasan</Form.ControlLabel>
                          <Input
                            as="textarea"
                            rows={3}
                            placeholder="Masukkan alasan revisi"
                            value={remarks}
                            onChange={(value) => setRemarks(value)}
                            style={{ width: "100%" }}
                          />
                        </Form.Group>
                      )}

                      <Form.Group>
                        <InputGroup inside style={{ maxWidth: "300px" }}>
                          <Input
                            type="password"
                            placeholder="Masukkan Password"
                            value={password}
                            onChange={(value) => setPassword(value)}
                          />
                        </InputGroup>
                      </Form.Group>
                    </Form>
                  </Panel>
                </Stack>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  appearance="subtle"
                  onClick={() => {
                    setShowApprovalModal(false);
                    setPassword("");
                    setRemarks("");
                    setApprovalStatus(null);
                  }}
                >
                  Batal
                </Button>
                <Button
                  appearance="primary"
                  color={approvalStatus === 1 ? "green" : "red"}
                  disabled={
                    !password.trim() ||
                    approvalStatus === null ||
                    (approvalStatus === 0 && !remarks.trim())
                  }
                  style={{
                    cursor:
                      !password.trim() || approvalStatus === null ||
                        (approvalStatus === 0 && !remarks.trim())
                        ? "not-allowed"
                        : "pointer",
                    opacity:
                      !password.trim() || approvalStatus === null ||
                        (approvalStatus === 0 && !remarks.trim())
                        ? 0.6
                        : 1
                  }}
                  onClick={async () => {
                    setLoading(true);
                    await HandleEditStatusApprovalPPIApi(
                      formData.id_ppi,
                      approvalStatus,
                      password
                    );
                    setShowApprovalModal(false);
                    setPassword("");
                    setRemarks("");
                    setApprovalStatus(null);
                    setLoading(false);
                  }}
                  loading={loading}
                >
                  {approvalStatus === 1 ? "Approve" : "Revisi"}
                </Button>
              </Modal.Footer>
            </Modal>


          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
