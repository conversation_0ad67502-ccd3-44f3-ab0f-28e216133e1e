import { useState, useEffect } from 'react';
import ProtocolApi from '@/pages/api/protocolApi';
import Head from 'next/head';
import ContainerLayout from '@/components/layout/ContainerLayout';
import MainContent from '@/components/layout/MainContent';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import { useRouter } from 'next/router';
import isBefore from 'date-fns/isBefore';
import { addDays, format } from 'date-fns';
// import Table from '@/components/Table';
import {
    Button,
    Stack,
    Breadcrumb,
    Tag,
    Table,
    Panel,
    Input,
    InputGroup,
    Pagination,
    Divider,
    Modal,
    Form,
    Schema,
    useToaster,
    DatePicker,
    SelectPicker,
} from "rsuite";
import Messages from '@/components/Messages';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import { paginate } from '@/utils/paginate';

export default function SetupCycle() {
    const router = useRouter();
    const MySwal = withReactContent(Swal);
    const { HeaderCell, Cell, Column } = Table;
    const [selectedNoProtocol, setSelectedNoProtocol] = useState('');
    const [isDisabled, setIsDisabled] = useState(false);
    const [selectedDate, setSelectedDate] = useState(null);
    const [allProtocolHeaderData, setAllProtocolHeaderData] = useState([]);
    const { GetProtocolBeforeSetupCycle, UpdateSetupCycleProtocol } = ProtocolApi();
    const [userDept, setUserDept] = useState('');
    const [moduleName, setModuleName] = useState('');
    const [breadcrumbsData, setBreadcrumbsData] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [protocolData, setProtocolData] = useState([]);
    const [showUpdateModal, setShowUpdateModal] = useState(false);
    const [loading, setIsLoading] = useState(false);
    const toaster = useToaster();
    const pageSize = 10;
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    let path = 'protocol/management/SetupCycle';

    // pagination button handler function
    const onPageChange = (page) => {
        setCurrentPage(page);
    };

    // calling paginate function and save the paginated data
    const paginatedData = paginate(allProtocolHeaderData, currentPage, pageSize);

    const GetAllProtocolBeforeSetupCycle = async (idDept) => {
        const reqData = {
            id_dept: parseInt(idDept)
        };

        const { data: data } = await GetProtocolBeforeSetupCycle(reqData);

        if (data !== null && data.length > 0) {
            setProtocolData(data);
        } else {
            setProtocolData([]);
        }
    };

    // Date change handler
    const dateFormatter = (date) => {
        const today = new Date(date);
        const year = `${today.getFullYear()}`;
        const month = String(today.getMonth() + 1).padStart(2, "0");
        const day = String(today.getDate()).padStart(2, "0");
        const result = `${year}-${month}-${day}`;
        return result;
    };

    useEffect(() => {
        const moduleNameValue = localStorage.getItem('module_name');
        const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
        if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
            router.push('/');
            return;
        }

        // validate access for this page
        if (!dataLogin.menu_link_code) {
            router.push('/dashboard');
            return;
        }
        const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
            item.includes(path),
        );
        if (validateUserAccess.length === 0) {
            router.push('/dashboard');
            return;
        }

        if (
            moduleNameValue === null ||
            moduleNameValue === undefined ||
            moduleNameValue === ''
        ) {
            router.push('/dashboard');
            return;
        }

        const asPathWithoutQuery = router.asPath.split('?')[0];
        const asPathNestedRoutes = asPathWithoutQuery
            .split('/')
            .filter((v) => v.length);
        let routerBreadCrumbsData = [
            moduleNameValue,
            asPathNestedRoutes[1],
            asPathNestedRoutes[2],
        ];
        const capitalizeFirstLetter = (str) => {
            return str.charAt(0).toUpperCase() + str.slice(1);
        };
        const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
            const words = item.split(/(?=[A-Z])/);
            return words.map((word) => capitalizeFirstLetter(word)).join(' ');
        });
        setBreadcrumbsData(breadCrumbsResult);
        setUserDept(dataLogin.department);
        setModuleName(moduleNameValue);

        GetAllProtocolBeforeSetupCycle(dataLogin.department);

    }, []);

    const updateSetupCycle = async (updateData) => {
        const { status, message } = await UpdateSetupCycleProtocol(updateData);
        if (status == 200) {
            toaster.push(
                Messages("success", "Successfully update setup cycle !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            GetAllProtocolBeforeSetupCycle(userDept);
        } else {
            toaster.push(
                Messages("error", "Error update setup cycle !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
        }
    };

    return (
        <>
            <div>
                <Head>
                    <title>Setup Cycle</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <MainContent>
                    <ModuleContentHeader
                        breadcrumbs={breadcrumbsData}
                        module_name={moduleName}
                    />
                    {protocolData?.length > 0 ? <Table
                        bordered
                        cellBordered
                        height={400}
                        data={protocolData}
                        onRowClick={(rowData) => {
                            console.log(rowData);
                        }}
                    >
                        <Column width={60} align="center" fixed>
                            <HeaderCell>No</HeaderCell>
                            <Cell>
                                {(rowData, rowIndex) => {
                                    return rowIndex + 1 + limit * (page - 1);
                                }}
                            </Cell>
                        </Column>

                        <Column width={150}>
                            <HeaderCell>No Protocol</HeaderCell>
                            <Cell dataKey="no_protocol" />
                        </Column>

                        <Column width={150}>
                            <HeaderCell>Created By</HeaderCell>
                            <Cell dataKey="created_by" />
                        </Column>

                        <Column width={150}>
                            <HeaderCell>Created Date</HeaderCell>
                            <Cell>
                                {rowData => rowData.created_date.substring(0, 10)}
                            </Cell>
                        </Column>

                        <Column width={80}>
                            <HeaderCell>Version</HeaderCell>
                            <Cell dataKey="protocol_version" />
                        </Column>

                        <Column width={200}>
                            <HeaderCell>Status</HeaderCell>
                            <Cell dataKey="status_protocol" />
                        </Column>

                        <Column width={80} fixed="right">
                            <HeaderCell>...</HeaderCell>

                            <Cell style={{ padding: "6px" }}>
                                {(rowData) => (
                                    <Button appearance="link" onClick={() => {
                                        setSelectedNoProtocol(rowData.no_protocol);
                                        setShowUpdateModal(true);
                                    }} disabled={isDisabled}>
                                        Edit
                                    </Button>
                                )}
                            </Cell>
                        </Column>
                    </Table> :
                        <Stack direction='row' justifyContent='center'>
                            <p className='font-semibold'>No data found</p>
                        </Stack>}

                </MainContent>

                {/* Update Setup Cycle Modal */}
                <Modal
                    backdrop="static"
                    role="alertdialog"
                    open={showUpdateModal}
                    onClose={() => {
                        setShowUpdateModal(false);
                    }}
                    size="xs"
                >
                    <Modal.Header>
                        <Modal.Title>Setup Cycle 0</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>No Protocol</Form.ControlLabel>
                                <Form.Control
                                    name="No Protocol"
                                    accepter={Input}
                                    readOnly
                                    block
                                    value={selectedNoProtocol}
                                />
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Setup Date</Form.ControlLabel>
                                <Form.Control
                                    name="Setup Date"
                                    accepter={DatePicker}
                                    disabledDate={date => {
                                        const today = new Date();
                                        const yesterday = new Date(today);
                                        yesterday.setDate(today.getDate() - 1);
                                        return isBefore(date, yesterday);
                                    }} style={{ width: 200 }}
                                    block
                                    value={selectedDate}
                                    onChange={(value) => {
                                        setSelectedDate(value);
                                    }}
                                    required
                                />
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowUpdateModal(false);
                                setSelectedNoProtocol('');
                                setSelectedDate(null);
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                if (selectedDate == null) {
                                    toaster.push(
                                        Messages("warning", "Date cannot be empty !"),
                                        {
                                            placement: "topEnd",
                                            duration: 3000,
                                        }
                                    );
                                    return;
                                }
                                const formattedDate = dateFormatter(selectedDate);

                                updateSetupCycle({ no_protocol: selectedNoProtocol, date_setup: formattedDate });

                                setSelectedDate(null);
                                setSelectedNoProtocol('');
                                setShowUpdateModal(false);
                            }}
                            appearance="primary"
                            type="submit"
                            loading={loading}
                        >
                            Submit
                        </Button>
                    </Modal.Footer>
                </Modal>
            </ContainerLayout>
        </>
    );
}
