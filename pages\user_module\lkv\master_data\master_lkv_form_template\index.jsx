import React, { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  ButtonToolbar,
  Modal,
  Schema,
  Message,
  Form,
  useToaster,
  SelectPicker,
  DatePicker,
  Loader,
  CheckboxGroup,
  Checkbox,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import EditIcon from "@rsuite/icons/Edit";
import LkvMasterLkvFormTemplate from "@/pages/api/lkv/api_lkv_master_lkv_form_template";
import LkvMasterTemplateType from "@/pages/api/lkv/api_lkv_master_template_type";
import LkvMasterRequestType from "@/pages/api/lkv/api_lkv_master_request_type";
import LkvMasterStatus from "@/pages/api/lkv/api_lkv_master_status";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import XLSX from "xlsx";
import { useRouter } from "next/router";

const { StringType, NumberType } = Schema.Types;

const model = Schema.Model({
  description: StringType().isRequired(),
  status: NumberType().isRequired(),
  template: NumberType().isRequired(),
  request: NumberType().isRequired(),
});

const initialData = {
  description: "",
  status: "",
  template: "",
  request: "",
  halal: 0,
  mandatory: 0,
  material: 0,
  cpro: 0,
  product: 0,
  toll: 0,
  GA: 0,
  inderect: 0,
};

const Textarea = React.forwardRef((props, ref) => (
  <Input {...props} as="textarea" ref={ref} />
));

export default function Index() {
  const [moduleName, setModuleName] = useState("");
  const { HeaderCell, Cell, Column } = Table;
  const [loading, setLoading] = useState(false);
  const [masterData, setMasterData] = useState([]);
  const [formTitle, setFormTitle] = useState("");
  const [idData, setIdData] = useState();
  const [openForm, setOpenForm] = useState(false);
  const toaster = useToaster();
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const formRef = React.useRef();
  const [isBackendDown, setIsBackendDown] = useState(false);
  const [status, setStatus] = useState(0);
  const router = useRouter();
  const [props, setProps] = useState([]);
  const [formData, setFormData] = useState(initialData);
  const [templateDesc, setTemplateDesc] = useState("");
  const [templateData, setTemplateData] = useState([]);
  const [requestData, setRequestData] = useState([]);
  const [statusData, setStatusData] = useState([]);

  const Messages = (type, text) => (
    <Message type={type} showIcon>
      {text}
    </Message>
  );

  // form

  const handleOpenForm = (title) => {
    setOpenForm(true);
    setFormTitle(title);
  };

  const handleForm = () => {
    if (formTitle === "Add") {
      if (!formRef.current.check()) {
        console.error("Form Error!!!");
        console.log("error di submit");
        return;
      }
      handleAddApi();
    } else if (formTitle === "Delete") {
      handleEditStatusApi();
    } else if (formTitle === "Active") {
      handleEditStatusApi();
    } else if (formTitle === "Update") {
      //   if (!formRef.current.check()) {
      //     console.error("Form Error");
      //     return;
      //   }
      handleEditApi();
    }
  };

  // pagination

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = masterData.filter((rowData, i) => {
    const searchFields = [
      "template_desc",
      "status_desc",
      "request_desc",
      "created_by",
      "updated_by",
    ];

    const matchesSearch = searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );

    return matchesSearch;
  });

  const totalRowCount = searchKeyword ? filteredData.length : masterData.length;

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  // options
  const optionsTemplate = templateData.map((item) => ({
    value: item.id_template_type,
    label: item.template_desc,
  }));

  const optionsRequest = requestData.map((item) => ({
    value: item.id_request_type,
    label: item.request_desc,
  }));

  const optionsStatus = statusData.map((item) => ({
    value: item.id_status,
    label: item.status_desc,
  }));

  const handleCheckboxChange = (name) => {
    setFormData((prevData) => ({
      ...prevData,
      [name]: prevData[name] === 1 ? 0 : 1,
    }));
  };

  // api

  // const dummyUser = "system";

  const fetchMasterData = async () => {
    const data = await LkvMasterLkvFormTemplate().getAllMasterLkvFormTemplate();
    setMasterData(data.data ? data.data : []);
  };

  const fetchTemplateData = async () => {
    const data = await LkvMasterTemplateType().getAllMasterTemplateTypeActive();
    setTemplateData(data.data ? data.data : []);
  };

  const fetchRequestData = async () => {
    const data = await LkvMasterRequestType().getAllMasterRequestTypeActive();
    setRequestData(data.data ? data.data : []);
  };

  const fetchStatusData = async () => {
    const data = await LkvMasterStatus().getAllMasterStatusActive();
    setStatusData(data.data ? data.data : []);
  };

  const handleAddApi = async () => {
    if (
      !formData.description ||
      !formData.request ||
      !formData.template ||
      !formData.status
    ) {
      console.log("some field are empty");
      return;
    }

    try {
      const data = await LkvMasterLkvFormTemplate().createMasterLkvFormTemplate(
        {
          created_by: props.employee_id,
          id_template_type: formData.template,
          id_request_type: formData.request,
          id_status: formData.status,
          description: formData.description,
          is_mandatory: formData.mandatory,
          is_material: formData.material,
          is_cpro: formData.cpro,
          is_halal: formData.halal,
          is_product: formData.product,
          is_toll: formData.toll,
          is_ga: formData.GA,
          is_inderect: formData.inderect,
        }
      );

      if (data.status === 200) {
        toaster.push(Messages("success", "Success creating Data!"), {
          placement: "topCenter",
          duration: 5000,
        });

        setOpenForm(false);
        fetchMasterData();
      } else {
        console.log("error creating data", data.message);
        toaster.push(
          Messages("error", `Error: something error. Please try again later!`),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
      }

      setFormData(initialData);
    } catch (error) {
      console.error("Error creating data:", error);
      toaster.push(
        Messages("error", `Error: something error Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
      setFormData(initialData);
    }
  };

  const handleEditApi = async () => {
    if (!formData.description) {
      console.log("some field are empty");
      return;
    }

    try {
      const data = await LkvMasterLkvFormTemplate().updateMasterLkvFormTemplate(
        {
          id: idData,
          created_by: props.employee_id,
          description: formData.description,
        }
      );

      if (data.status === 200) {
        toaster.push(Messages("success", "Success edit Data!"), {
          placement: "topCenter",
          duration: 5000,
        });

        setOpenForm(false);
        fetchMasterData();
      } else {
        console.log("error edit data", data.message);
        toaster.push(
          Messages("error", `Error: something error. Please try again later!`),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
      }

      setFormData(initialData);
    } catch (error) {
      toaster.push(
        Messages("error", `Error: something error Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
      setFormData(initialData);
    }
  };

  const handleEditStatusApi = async () => {
    try {
      const data =
        await LkvMasterLkvFormTemplate().updateStatusMasterLkvFormTemplate({
          id: idData,
          is_active: status,
          created_by: props.employee_id,
        });

      if (data.status === 200) {
        toaster.push(Messages("success", "Success update status!"), {
          placement: "topCenter",
          duration: 5000,
        });

        setOpenForm(false);
        fetchMasterData();
      } else {
        console.log("error update status data", data.message);
        toaster.push(
          Messages("error", `Error: something error. Please try again later!`),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
      }
    } catch (error) {
      console.log("error : ", error);
      toaster.push(
        Messages("error", `Error: something error Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("lkv/master_data/master_template_type")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      fetchMasterData();
      fetchTemplateData();
      fetchRequestData();
      fetchStatusData();
    }

    // fetchMasterData();
  }, []);

  // excel

  const handleExportExcel = () => {
    if (masterData.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }

    const headerMapping = {
      id_template: "ID Master Lkv Form Template",
      template_desc: "Template",
      request_desc: "Request Type",
      status_desc: "Status",
      description: "Description",
      is_mandatory: "Mandatory",
      is_material: "Material",
      is_cpro: "CPRO",
      is_halal: "HALAL",
      is_product: "Product",
      is_toll: "Toll",
      is_ga: "GA",
      is_inderect: "Indirect",
      created_dt: "Created Date",
      created_by: "Created By",
      updated_dt: "Updated Date",
      updated_by: "Updated By",
      deleted_dt: "Deleted Date",
      deleted_by: "Deleted By",
      is_active: "Status",
    };

    const formattedData = masterData.map((item) => {
      const formattedItem = {};
      for (const key in item) {
        if (headerMapping[key]) {
          if (key === "is_active") {
            formattedItem[headerMapping[key]] =
              item[key] === 1 ? "Active" : "Inactive";
          } else if (key === "is_halal") {
            formattedItem[headerMapping[key]] = item[key] === 1 ? "YES" : "NO";
          } else if (key === "is_mandatory") {
            formattedItem[headerMapping[key]] = item[key] === 1 ? "YES" : "NO";
          } else if (key === "is_material") {
            formattedItem[headerMapping[key]] = item[key] === 1 ? "YES" : "NO";
          } else if (key === "is_cpro") {
            formattedItem[headerMapping[key]] = item[key] === 1 ? "YES" : "NO";
          } else if (key === "is_product") {
            formattedItem[headerMapping[key]] = item[key] === 1 ? "YES" : "NO";
          } else if (key === "is_toll") {
            formattedItem[headerMapping[key]] = item[key] === 1 ? "YES" : "NO";
          } else if (key === "is_ga") {
            formattedItem[headerMapping[key]] = item[key] === 1 ? "YES" : "NO";
          } else if (key === "is_inderect") {
            formattedItem[headerMapping[key]] = item[key] === 1 ? "YES" : "NO";
          } else {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
      }
      return formattedItem;
    });

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const filename = `LKV Master Lkv Form Template ${date}.xlsx`;

    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <div>
        <Head>
          <title>Master Lkv Form Template</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Lkv</Breadcrumb.Item>
                  <Breadcrumb.Item active>
                    Master Lkv Form Template
                  </Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusRoundIcon />}
                    appearance="primary"
                    onClick={() => handleOpenForm("Add")}
                  >
                    Add Template
                  </IconButton>
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
              height={400}
            >
              <Column width={70} align="left" fixed sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  ID
                </HeaderCell>
                <Cell dataKey="id_template" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Description
                </HeaderCell>
                <Cell dataKey="description" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Tag
                </HeaderCell>
                <Cell>
                  {(rowData) => {
                    const values = [];
                    if (rowData.is_mandatory === 1) values.push("Mandatory");
                    if (rowData.is_material === 1) values.push("Material");
                    if (rowData.is_cpro === 1) values.push("CPRO");
                    if (rowData.is_halal === 1) values.push("halal");
                    if (rowData.is_product === 1) values.push("Product");
                    if (rowData.is_toll === 1) values.push("Toll");
                    if (rowData.is_ga === 1) values.push("GA");
                    if (rowData.is_inderect === 1) values.push("Indirect");
                   
                    return values.join(", ");
                  }}
                </Cell>
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Template
                </HeaderCell>
                <Cell dataKey="template_desc" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Request
                </HeaderCell>
                <Cell dataKey="request_desc" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Status
                </HeaderCell>
                <Cell dataKey="status_desc" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Created By
                </HeaderCell>
                <Cell dataKey="created_by" />
              </Column>
              <Column width={150} align="left" resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Created Date
                </HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Updated By
                </HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>
              <Column width={150} align="left" resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Updated Date
                </HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Delete By
                </HeaderCell>
                <Cell dataKey="deleted_by" />
              </Column>
              <Column width={150} align="center" resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Delete Date
                </HeaderCell>
                <Cell dataKey="deleted_dt" />
              </Column>
              <Column width={100} sortable resizable fixed="right">
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={150} resizable fixed="right">
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Operation
                </HeaderCell>

                <Cell style={{ padding: "6px" }} resizable>
                  {(rowData) => (
                    <div>
                      <ButtonToolbar>
                        <Button
                          onClick={() => {
                            setIdData(rowData.id_template),
                              setFormData({
                                description: rowData.description,
                              }),
                              handleOpenForm("Update");
                          }}
                        >
                          <EditIcon style={{ color: "blue" }} />
                        </Button>
                        {rowData.is_active === 1 && (
                          <Button
                            onClick={() => {
                              handleOpenForm("Delete"),
                                setIdData(rowData.id_template);
                              setStatus(0);
                              setTemplateDesc(rowData.description);
                            }}
                          >
                            <TrashIcon style={{ color: "red" }} />
                          </Button>
                        )}
                        {rowData.is_active === 0 && (
                          <Button
                            onClick={() => {
                              handleOpenForm("Active"),
                                setIdData(rowData.id_template);
                              setStatus(1);
                              setTemplateDesc(rowData.description);
                            }}
                          >
                            <ReloadIcon style={{ color: "green" }} />
                          </Button>
                        )}
                      </ButtonToolbar>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
        <Modal
          open={openForm}
          backdrop="statis"
          onClose={() => {
            setOpenForm(false);
            setFormData(initialData);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>{formTitle}</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {formTitle === "Delete" && (
              <p>Are you sure to delete this "{templateDesc}" ?</p>
            )}
            {formTitle === "Active" && (
              <p>Are you sure to active this "{templateDesc}" ?</p>
            )}
            {formTitle !== "Delete" && formTitle !== "Active" && (
              <Form
                model={model}
                ref={formRef}
                formValue={formData}
                onChange={(formValue) => setFormData(formValue)}
                fluid
              >
                {(formTitle === "Add" || formTitle === "Update") && (
                  <Form.Group controlId="description">
                    <Form.ControlLabel>Description</Form.ControlLabel>
                    <Form.Control
                      name="description"
                      placeholder="description"
                      rows={3}
                      accepter={Textarea}
                    />
                  </Form.Group>
                )}
                {formTitle === "Add" && (
                  <div>
                    <Form.Group controlId="template">
                      <Form.ControlLabel>Template Type</Form.ControlLabel>
                      <Form.Control
                        name="template"
                        accepter={SelectPicker}
                        data={optionsTemplate}
                        block
                        placeholder="Select Template"
                      />
                    </Form.Group>
                    <Form.Group controlId="request">
                      <Form.ControlLabel>Request Type</Form.ControlLabel>
                      <Form.Control
                        name="request"
                        accepter={SelectPicker}
                        data={optionsRequest}
                        block
                        placeholder="Select Request"
                      />
                    </Form.Group>
                    <Form.Group controlId="status">
                      <Form.ControlLabel>Status</Form.ControlLabel>
                      <Form.Control
                        name="status"
                        accepter={SelectPicker}
                        data={optionsStatus}
                        block
                        placeholder="Select Status"
                      />
                    </Form.Group>
                    <Form.Group controlId="checkbox">
                      <Form.ControlLabel>LKV Form:</Form.ControlLabel>
                      <div>
                        <Checkbox
                          checked={formData.mandatory === 1}
                          onChange={() => handleCheckboxChange("mandatory")}
                        >
                          Mandatory
                        </Checkbox>
                        <Checkbox
                          checked={formData.material === 1}
                          onChange={() => handleCheckboxChange("material")}
                        >
                          Material
                        </Checkbox>
                        <Checkbox
                          checked={formData.cpro === 1}
                          onChange={() => handleCheckboxChange("cpro")}
                        >
                          CPRO
                        </Checkbox>
                        <Checkbox
                          checked={formData.halal === 1}
                          onChange={() => handleCheckboxChange("halal")}
                        >
                          Halal
                        </Checkbox>
                      </div>
                      <div>
                        <Checkbox
                          checked={formData.product === 1}
                          onChange={() => handleCheckboxChange("product")}
                        >
                          Product
                        </Checkbox>
                        <Checkbox
                          checked={formData.toll === 1}
                          onChange={() => handleCheckboxChange("toll")}
                        >
                          TOLL
                        </Checkbox>
                        <Checkbox
                          checked={formData.GA === 1}
                          onChange={() => handleCheckboxChange("GA")}
                        >
                          GA
                        </Checkbox>
                        <Checkbox
                          checked={formData.inderect === 1}
                          onChange={() => handleCheckboxChange("inderect")}
                        >
                          Indirect
                        </Checkbox>
                      </div>
                    </Form.Group>
                  </div>
                )}
              </Form>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setOpenForm(false);
                setFormData(initialData);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button appearance="primary" onClick={handleForm}>
              {formTitle === "Delete"
                ? "Delete"
                : formTitle === "Update"
                ? "Edit"
                : formTitle === "Active"
                ? "Active"
                : "Add"}
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
