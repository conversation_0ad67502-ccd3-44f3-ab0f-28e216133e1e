import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";

import { 
  <PERSON>ton, 
  Stack, 
  Panel, 
  ButtonGroup, 
  Breadcrumb ,
  Form,
  Textarea,
  ButtonToolbar,
  Input,
  Divider,
  InputPicker,
  InputGroup,
  InputNumber,
  Tag,
  SelectPicker,
  DatePicker,
  useToaster
} from "rsuite";

import ContainerLayout from "@/components/layout/ContainerLayout";

import ApiTsDetail from "@/pages/api/ts/api_ts_detail";
import Messages from "@/components/Messages";

export default function TsEditAutomate({data}) {
  const router = useRouter();

  const [moduleName, setModuleName] = useState("");

  const [props, setProps] = useState([]);

  const [canFetch, setCanFetch] = useState(false)

  const [dataDetail, setDataDetail] = useState({})

  const [dataSpv, setDataSpv] = useState([])

  const [dataProduct, setDataProduct] = useState([])

  const toaster = useToaster();

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    // const fetchData = async () => {
    //   const body = {
    //     employee_id: dataLogin.employee_id
    //   }
    //   const resultAutoLine = await ApiTsApproval().GetAllAutoSpv(body)
    //   const resultManualLine = await ApiTsApproval().GetAllManualSpv(body)

    //   console.log("[result auto parent]",resultAutoLine)
    //   //console.log("[result manual parent]",resultManualLine)
    //   setDataAutoLine(resultAutoLine.data || []);
    //   SetDataManualLine(resultManualLine.data || [])
    // };
    // fetchData();

    //nyalakan setelah coding
    
    // if (!dataLogin) {
    //   router.push(dataLogin ? "/dashboard" : "/");
    // } else {
    //   const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
    //     item.includes("masterdata/TsMainCategories")
    //   );

    //   if (validateUserAccess.length === 0) {
    //     router.push("/dashboard");
    //     return;
    //   }
    // }
    console.log("id ", data)
    setCanFetch(true)
    fetchData()
  }, []);

  const fetchData = async () => {
    const result = await ApiTsDetail().getDetailAuto({
      id_reference: parseInt(data),
    })

    const resultSpv = await ApiTsDetail().getAllActiveSpv()

    const resultProd = await ApiTsDetail().getAllOfProduct()

    setDataDetail(result.data ? result.data : {})
    setDataSpv(resultSpv.data ? resultSpv.data : {})
    setDataProduct(resultProd.data ? resultProd.data : {})
  }

  useEffect(()=>{
    const result = (dataDetail.bobot_granul/dataDetail.bobot_teoritis) * 100
    console.log("result ", parseInt(result))
    setDataDetail({...dataDetail, rendemen:parseInt(result)})
  },[dataDetail.bobot_granul,dataDetail.bobot_teoritis])

  const dataSkalaProduksi = [
    'Pilot',
    'Commercial'
  ].map(item => ({ label: item, value: item }));
  
  const dataJenisSediaan = ['Kapsul', 'Tablet', 'Sirup']
  .map(
    item => ({ label: item, value: item })
  );

  const dataFokusTrial = ['Carry Over', 'Diversifikasi', 'Others']
  .map(
    item => ({ label: item, value: item })
  );

  const dataScreenQuadro = ['10','20','30']
  .map(
    item => ({ label: item, value: item })
  );

  const dataBinTumbler = ['10','20','30']
  .map(
    item => ({ label: item, value: item })
  );

  const handleStatusAttachmentChange = (index) => {
    const newData = { ...dataDetail }; 
    newData.attachments[index].is_active = newData.attachments[index].is_active === 1 ? 0 : 1; 
    setDataDetail(newData); 
  };

  const handleUpdate = async () =>{
    const body = {
        "id_header_trans": dataDetail.id_header_trans,
        "id_line": dataDetail.id_line,
        "sediaan_type": dataDetail.sediaan_type,
        "product_code": dataDetail.product_code,
        "product_name": dataDetail.product_name,
        "batch_no": dataDetail.batch_no,
        "binder_date": dataDetail.binder_date,
        "binder_mix_amount": dataDetail.binder_mix_amount,
        "binder_mix_time": dataDetail.binder_mix_time,
        "granule_date": dataDetail.granule_date,
        "granule_ampere": dataDetail.granule_ampere,
        "granule_power": dataDetail.granule_power,
        "drying_date": dataDetail.drying_date,
        "drying_lod": dataDetail.drying_lod,
        "drying_product_temp": dataDetail.drying_product_temp,
        "drying_exhaust_temp": dataDetail.drying_exhaust_temp,
        "drying_time": dataDetail.drying_time,
        "drying_remarks": dataDetail.drying_remarks,
        "sifting_date": dataDetail.sifting_date,
        "sifting_screen_quadro": dataDetail.sifting_screen_quadro,
        "sifting_bin_tumbler": dataDetail.sifting_bin_tumbler,
        "sifting_impeller_speed_1": dataDetail.sifting_impeller_speed_1,
        "sifting_impeller_speed_2": dataDetail.sifting_impeller_speed_2,
        "final_mix_date": dataDetail.final_mix_date,
        "final_mix_time_mix_1": dataDetail.final_mix_time_mix_1,
        "final_mix_time_mix_2": dataDetail.final_mix_time_mix_2,
        "ts_conclusion": dataDetail.ts_conclusion,
        "ts_followup": dataDetail.ts_followup,
        "bobot_granul": dataDetail.bobot_granul,
        "bobot_teoritis": dataDetail.bobot_teoritis,
        "rendemen": dataDetail.rendemen,
        "spv_employee_id": dataDetail.spv_employee_id,
        "production_scale": dataDetail.production_scale,
        "trial_focus": dataDetail.trial_focus,
        "ppi_no": dataDetail.ppi_no,
        "process_date": dataDetail.process_date,
        "process_purpose": dataDetail.process_purpose,
        "background": dataDetail.background,
        "discussion": dataDetail.discussion,
        "analyzed_data": dataDetail.analyzed_data,
        "update_by": props.employee_id,
        "ts_attachments": dataDetail.attachments
    }

    //console.log("body", body)
    const result = await ApiTsDetail().putDetailAuto(body)
    
    if (result.status == 200) {
      toaster.push(
        Messages("success", "Success Update"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      fetchData()
      return
    }else{
      console.log("error submit update", result.message)
      toaster.push(
        Messages("warning", "Error when submit update"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      return
    }
  }


  return (
    <>
      <div>
        <Head>
          <title>Ts Edit Automate</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item active>
              {moduleName ? moduleName : "User Module"}
            </Breadcrumb.Item>
          </Breadcrumb>
          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h3>TS Edit Automate Line</h3>
            </Stack>
            <Divider/>
            <Stack justifyContent="flex-start">
              <Tag color="blue">{dataDetail.ts_code}</Tag>
              <Tag color="green" style={{marginLeft:"5px"}}>{dataDetail.line_description}</Tag>
            </Stack>
          </Panel>

          <Form fluid>
          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h5>Header</h5>
            </Stack>              
            <Form.Group controlId="jenis_sediaan">
              <Form.ControlLabel>Jenis Sediaan</Form.ControlLabel>
              <Form.Control 
                  name="jenis_sediaan" 
                  accepter={SelectPicker}
                  value={dataDetail.sediaan_type}
                  data={dataJenisSediaan}
                  valueKey="label"
                  labelKey="value"
                  block
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, sediaan_type: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>

            {/* need api */}
            <Form.Group controlId="product_code">
              <Form.ControlLabel>Kode Produk</Form.ControlLabel>
              <Form.Control 
                  name="product_code" 
                  accepter={SelectPicker}
                  value={dataDetail.product_code}
                  data={dataProduct}
                  valueKey="product_code"
                  labelKey="product_code"
                  block
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, product_code: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>

            <Form.Group controlId="product_name">
              <Form.ControlLabel>Nama Produk</Form.ControlLabel>
              <Form.Control
                  placeholder="product_name"
                  value={dataDetail.product_name}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, product_name: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>

            <Form.Group controlId="batch_no">
              <Form.ControlLabel>Kode Batch</Form.ControlLabel>
              <Form.Control
                  placeholder="batch_no"
                  value={dataDetail.batch_no}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, batch_no: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>

            <Form.Group controlId="production_scale">
              <Form.ControlLabel>Kode Produk</Form.ControlLabel>
              <Form.Control 
                  name="production_scale" 
                  accepter={SelectPicker}
                  value={dataDetail.production_scale}
                  data={dataSkalaProduksi}
                  valueKey="value"
                  labelKey="value"
                  block
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, production_scale: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>
            
            <Form.Group controlId="focus_trial">
              <Form.ControlLabel>Fokus Trial</Form.ControlLabel>
              <Form.Control 
                  name="focus_trial" 
                  accepter={SelectPicker}
                  value={dataDetail.focus_trial}
                  data={dataFokusTrial}
                  valueKey="value"
                  labelKey="value"
                  block
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, focus_trial: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>
            
            <Form.Group controlId="ppi_no">
              <Form.ControlLabel>No PPI</Form.ControlLabel>
              <Form.Control
                  placeholder="ppi_no"
                  value={dataDetail.ppi_no}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, ppi_no: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>

            <Form.Group controlId="process_purpose">
              <Form.ControlLabel>Tujuan Proses</Form.ControlLabel>
              <Form.Control
                  placeholder="process_purpose"
                  value={dataDetail.process_purpose}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, process_purpose: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>

            <Form.Group controlId="background">
              <Form.ControlLabel>Background</Form.ControlLabel>
              <Form.Control
                  placeholder="background"
                  value={dataDetail.background}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, background: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>


            <Form.Group controlId="process_date">
              <Form.ControlLabel>Tanggal Proses</Form.ControlLabel>
              <DatePicker
                    oneTap
                    value={new Date(dataDetail.process_date)}
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, process_date: value });
                    }}
                    block
                  />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>      
          </Panel>

          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h5>Binder</h5>
            </Stack>    
            <Form.Group controlId="binder_date">
              <Form.ControlLabel>Tanggal Binder</Form.ControlLabel>
              <DatePicker
                    oneTap
                    value={new Date(dataDetail.binder_date)}
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, binder_date: value });
                    }}
                    block
                  />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 


            <Form.Group controlId="binder_mix_amount">
              <Form.ControlLabel>Jumlah Pelarut</Form.ControlLabel>              
              <InputGroup>
                <Form.Control
                    placeholder="binder_mix_amount"
                    value={dataDetail.binder_mix_amount}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, binder_mix_amount: parseFloat(value) });
                    }}
                    type="number"
                />                
                <InputGroup.Addon> L</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="binder_mix_time">
              <Form.ControlLabel>Waktu Aduk</Form.ControlLabel>
              <InputGroup>
              <Form.Control
                  placeholder="binder_mix_time"
                  value={dataDetail.binder_mix_time}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, binder_mix_time: parseFloat(value) });
                  }}
                  type="number"
              />
               <InputGroup.Addon> Menit</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="binder_remarks">
              <Form.ControlLabel>Binder Remarks</Form.ControlLabel>
              <Form.Control
                  placeholder="binder_remarks"
                  value={dataDetail.binder_remarks}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, binder_remarks: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 
          </Panel>

          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h5>Granulasi</h5>
            </Stack>    
            <Form.Group controlId="granule_date">
              <Form.ControlLabel>Tanggal Granulasi</Form.ControlLabel>
              <DatePicker
                    oneTap
                    value={new Date(dataDetail.granule_date)}
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, granule_date: value });
                    }}
                    block
                  />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 


            <Form.Group controlId="granule_ampere">
              <Form.ControlLabel>Ampere</Form.ControlLabel>              
              <InputGroup>
                <Form.Control
                    placeholder="granule_ampere"
                    value={dataDetail.granule_ampere}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, granule_ampere: parseFloat(value) });
                    }}
                    type="number"
                />                
                <InputGroup.Addon> A</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="granule_power">
              <Form.ControlLabel>Power</Form.ControlLabel>
              <InputGroup>
              <Form.Control
                  placeholder="granule_power"
                  value={dataDetail.granule_power}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, granule_power: parseFloat(value) });
                  }}
                  type="number"
              />
               <InputGroup.Addon> Kwh</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="granule_remarks">
              <Form.ControlLabel>Granulasi Remarks</Form.ControlLabel>
              <Form.Control
                  placeholder="granule_remarks"
                  value={dataDetail.granule_remarks}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, granule_remarks: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 
          </Panel>

          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h5>Pengeringan</h5>
            </Stack>    
            <Form.Group controlId="drying_date">
              <Form.ControlLabel>Tanggal Pengeringan</Form.ControlLabel>
              <DatePicker
                    oneTap
                    value={new Date(dataDetail.drying_date)}
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, drying_date: value });
                    }}
                    block
                  />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 


            <Form.Group controlId="drying_lod">
              <Form.ControlLabel>LOD</Form.ControlLabel>              
              <InputGroup>
                <Form.Control
                    placeholder="drying_lod"
                    value={dataDetail.drying_lod}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, drying_lod: parseFloat(value) });
                    }}
                    type="number"
                />                
                <InputGroup.Addon> %</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="drying_product_temp">
              <Form.ControlLabel>Product Temp</Form.ControlLabel>
              <InputGroup>
              <Form.Control
                  placeholder="drying_product_temp"
                  value={dataDetail.drying_product_temp}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, drying_product_temp: parseFloat(value) });
                  }}
                  type="number"
              />
               <InputGroup.Addon> C</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="drying_exhaust_temp">
              <Form.ControlLabel>Exhaust Temp</Form.ControlLabel>
              <InputGroup>
              <Form.Control
                  placeholder="drying_exhaust_temp"
                  value={dataDetail.drying_exhaust_temp}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, drying_exhaust_temp: parseFloat(value) });
                  }}
                  type="number"
              />
               <InputGroup.Addon> C</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="drying_remarks">
              <Form.ControlLabel>Pengeringan Remarks</Form.ControlLabel>
              <Form.Control
                  placeholder="drying_remarks"
                  value={dataDetail.drying_remarks}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, drying_remarks: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 
          </Panel>

          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h5>Pengayakan</h5>
            </Stack>    
            <Form.Group controlId="sifting_date">
              <Form.ControlLabel>Tanggal Pengayakan</Form.ControlLabel>
              <DatePicker
                    oneTap
                    value={new Date(dataDetail.sifting_date)}
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, sifting_date: value });
                    }}
                    block
                  />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="sifting_screen_quadro">
              <Form.ControlLabel>Screen Quadro</Form.ControlLabel>
              <Form.Control 
                  name="sifting_screen_quadro" 
                  accepter={SelectPicker}
                  value={dataDetail.sifting_screen_quadro}
                  data={dataScreenQuadro}
                  valueKey="value"
                  labelKey="value"
                  block
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, sifting_screen_quadro: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>

            <Form.Group controlId="sifting_bin_tumbler">
              <Form.ControlLabel>Bin Tumbler</Form.ControlLabel>
              <Form.Control 
                  name="sifting_bin_tumbler" 
                  accepter={SelectPicker}
                  value={dataDetail.sifting_bin_tumbler}
                  data={dataBinTumbler}
                  valueKey="value"
                  labelKey="value"
                  block
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, sifting_bin_tumbler: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>


            <Form.Group controlId="sifting_impeller_speed_1">
              <Form.ControlLabel>Impeller Speed Quadro 1</Form.ControlLabel>              
              <InputGroup>
                <Form.Control
                    placeholder="sifting_impeller_speed_1"
                    value={dataDetail.sifting_impeller_speed_1}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, sifting_impeller_speed_1: parseFloat(value) });
                    }}
                    type="number"
                />                
                <InputGroup.Addon> RPM</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="sifting_impeller_speed_2">
              <Form.ControlLabel>Impeller Speed Quadro 2</Form.ControlLabel>              
              <InputGroup>
                <Form.Control
                    placeholder="sifting_impeller_speed_2"
                    value={dataDetail.sifting_impeller_speed_2}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, sifting_impeller_speed_2: parseFloat(value) });
                    }}
                    type="number"
                />                
                <InputGroup.Addon> RPM</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="drying_remarks">
              <Form.ControlLabel>Pengayakan Remarks</Form.ControlLabel>
              <Form.Control
                  placeholder="drying_remarks"
                  value={dataDetail.drying_remarks}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, drying_remarks: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 
          </Panel>

          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h5>Final Mix</h5>
            </Stack>    
            <Form.Group controlId="final_mix_date">
              <Form.ControlLabel>Tanggal Final Mix</Form.ControlLabel>
              <DatePicker
                    oneTap
                    value={new Date(dataDetail.final_mix_date)}
                    format="dd-MM-yyyy"
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, final_mix_date: value });
                    }}
                    block
                  />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 


            <Form.Group controlId="final_mix_time_mix_1">
              <Form.ControlLabel>Waktu Aduk 1</Form.ControlLabel>              
              <InputGroup>
                <Form.Control
                    placeholder="final_mix_time_mix_1"
                    value={dataDetail.final_mix_time_mix_1}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, final_mix_time_mix_1: parseFloat(value) });
                    }}
                    type="number"
                />                
                <InputGroup.Addon> menit</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="final_mix_time_mix_2">
              <Form.ControlLabel>Waktu Aduk 2</Form.ControlLabel>              
              <InputGroup>
                <Form.Control
                    placeholder="final_mix_time_mix_2"
                    value={dataDetail.final_mix_time_mix_2}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, final_mix_time_mix_2: parseFloat(value) });
                    }}
                    type="number"
                />                
                <InputGroup.Addon> menit</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="ts_conclusion">
              <Form.ControlLabel>Kesimpulan</Form.ControlLabel>
              <Form.Control
                  placeholder="ts_conclusion"
                  value={dataDetail.ts_conclusion}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, ts_conclusion: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="ts_followup">
              <Form.ControlLabel>Tindak Lanjut</Form.ControlLabel>
              <Form.Control
                  placeholder="ts_followup"
                  value={dataDetail.ts_followup}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, ts_followup: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="bobot_granul">
              <Form.ControlLabel>Bobot Granul</Form.ControlLabel>              
              <InputGroup>
                <Form.Control
                    placeholder="bobot_granul"
                    value={dataDetail.bobot_granul}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, bobot_granul: parseFloat(value) });
                    }}
                    type="number"
                />                
                <InputGroup.Addon> kg</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="bobot_teoritis">
              <Form.ControlLabel>Bobot Teoritis</Form.ControlLabel>              
              <InputGroup>
                <Form.Control
                    placeholder="bobot_teoritis"
                    value={dataDetail.bobot_teoritis}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, bobot_teoritis: parseFloat(value) });
                    }}
                    type="number"
                />                
                <InputGroup.Addon> kg</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="rendemen">
              <Form.ControlLabel>Rendemen</Form.ControlLabel>              
              <InputGroup>
                <Form.Control
                    placeholder="rendemen"
                    value={dataDetail.rendemen}
                    onChange={(value) => {
                      setDataDetail({ ...dataDetail, rendemen: parseFloat(value) });
                    }}
                    type="number"
                    readOnly={true}
                />                
                <InputGroup.Addon> %</InputGroup.Addon>
              </InputGroup>
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            {/* <Form.Group controlId="drying_remarks">
              <Form.ControlLabel>Pengayakan Remarks</Form.ControlLabel>
              <Form.Control
                  placeholder="drying_remarks"
                  value={dataDetail.drying_remarks}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, drying_remarks: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>  */}


            {/* api backend spv */}
            
          </Panel>

          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h5>Analisa Data</h5>
            </Stack>    

            <Form.Group controlId="discussion">
              <Form.ControlLabel>Diskusi</Form.ControlLabel>
              <Form.Control
                  placeholder="discussion"
                  value={dataDetail.discussion}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, discussion: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            <Form.Group controlId="analyzed_data">
              <Form.ControlLabel>Analisa Data</Form.ControlLabel>
              <Form.Control
                  placeholder="analyzed_data"
                  value={dataDetail.analyzed_data}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, analyzed_data: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group> 

            {/* <Form.Group controlId="drying_remarks">
              <Form.ControlLabel>Pengayakan Remarks</Form.ControlLabel>
              <Form.Control
                  placeholder="drying_remarks"
                  value={dataDetail.drying_remarks}
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, drying_remarks: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>  */}


            {/* api backend spv */}
            <Form.Group controlId="spv_employee_id">
              <Form.ControlLabel>Spv</Form.ControlLabel>
              <Form.Control 
                  name="spv_employee_id" 
                  accepter={SelectPicker}
                  value={dataDetail.spv_employee_id}
                  data={dataSpv}
                  valueKey="employee_id"
                  labelKey="employee_name"
                  block
                  onChange={(value) => {
                    setDataDetail({ ...dataDetail, spv_employee_id: value });
                  }}
              />
              <Form.HelpText>Required</Form.HelpText>
            </Form.Group>
            
          </Panel>

          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h5>Attachments</h5>
            </Stack>  
            <div>
              {dataDetail &&
                dataDetail.attachments &&
                dataDetail.attachments.map((attach, index) => (
                  <div key={index} className="border-1 p-2 my-1 rounded-md">
                    <p>Value : {attach.id_attachments}</p>
                    <p>Attachment : {attach.type}</p>
                    <p>Status : {attach.is_active === 1 ? 'Active' : 'Inactive'}</p>
                    {/* <p>Image : {`${process.env.NEXT_PUBLIC_STORAGE}/${attach.path}`}</p> */}
                    {/* redirect url img ts*/}
                    <img className="object-contain" src={`${process.env.NEXT_PUBLIC_STORAGE}/${attach.path}`} alt={attach.type} />
                    {attach.is_active == 1 &&
                    <Button className="mt-1" color="red" appearance="primary"
                    onClick={() =>{handleStatusAttachmentChange(index)}}
                    >Delete</Button>
                    }
                    {attach.is_active == 0  &&
                    <Button className="mt-1" color="green" appearance="primary"
                    onClick={() =>{handleStatusAttachmentChange(index)}}
                    >Re Active</Button>
                    }
                  </div>
                ))}
            </div>
          </Panel>

          <Form.Group>
        <ButtonToolbar>
        <Button onClick={() => 
            router.push({
              pathname:
                  '/user_module/ts/TsReport',
            })
          }  appearance="default">Cancel</Button>
        <Button 
        onClick={() => 
          handleUpdate()
        } 
        appearance="primary">Submit</Button>
      </ButtonToolbar>
    </Form.Group>
  </Form>
        </div>
      </ContainerLayout>
    </>
  );
}

// Fetch data on the server side
export async function getServerSideProps(context) {
  const { query } = context;
  const data = query.data || ''; // Access the data parameter from the query

  // Return the data as props
  return {
    props: {
      data,
    },
  };
}