import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiMain() {
  return {
    getAllMain: createApiFunction("get", "oee/master_main/list"),
    getAllActiveMain: createApiFunction("get", "oee/master_main/list-active"),
    getMasterMainById: createApiFunction("post", "oee/master_main/id"),
    createMain: createApiFunction("post", "oee/master_main/create"),
    editMain: createApiFunction("put", "oee/master_main/edit"),
    editStatusMain: createApiFunction("put", "oee/master_main/edit-status"),
  };
}
