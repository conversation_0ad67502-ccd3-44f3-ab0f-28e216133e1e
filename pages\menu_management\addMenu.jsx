import MainContent from "@/components/layout/MainContent";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import ContainerLayout from "@/components/layout/ContainerLayout";
import MenuApi from "../api/menuApi";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import { Button, Dropdown, Stack, Form, Input, Radio, RadioGroup, SelectPicker } from "rsuite";
import ModuleApi from "../api/moduleApi";
// import { Input } from "postcss";

function AddMenu({ allModule }) {
  const router = useRouter();
  const MySwal = withReactContent(Swal);
  const { PostMenu } = MenuApi();
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [menuName, setMenuName] = useState("");
  const [isActive, setIsActive] = useState("1");
  const [isParent, setIsParent] = useState("1");
  const [menuLinkCode, setMenuLinkCode] = useState("");
  const [selectedModuleCode, setSelectedModuleCode] = useState(null);
  const [createdby, setCreatedBy] = useState("");
  const [menuIcon, setMenuIcon] = useState("");

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // set createdby
    setCreatedBy(dataLogin["employee_id"]);

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  },[]);

  const addMenuHandler = async (event) => {
    setIsFormDisabled(true);
    event.preventDefault();

    // Validation
    if (menuName == "" || menuIcon == "" || menuLinkCode == "" || selectedModuleCode == null || selectedModuleCode == undefined) {
      setIsFormDisabled(false);
      return MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please fill all required data !",
      });
    }

    // Mengubah nilai string menjadi integer
    let is_active = parseInt(isActive);
    let is_parent = parseInt(isParent);
    let module_code = parseInt(selectedModuleCode);

    let dataUser = {
      menu_name: menuName,
      is_active: is_active,
      is_parent: is_parent,
      menu_link_code: menuLinkCode,
      module_code: module_code,
      createdby: createdby,
      menu_icon: menuIcon,
    };

    // Send ke backend
    const { Data } = await PostMenu(dataUser);

    if (Data) {
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "Menu inserted successfully.",
        allowOutsideClick: false,
        timer: 2500,
      });
      router.push("/menu_management");
    } else {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Menu insert FAILED.",
      });
      setIsFormDisabled(false);
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Add Menu</title>
        </Head>
      </div>
      <ContainerLayout
        title="Menu Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <Stack style={{ marginBottom: '1.5rem' }}><p className="font-bold text-xl">Add Menu</p>
            <hr /></Stack>
          <Form fluid>
            <Form.Group>
              <Form.ControlLabel htmlFor="menu_name">Menu Name</Form.ControlLabel>
              <Form.Control
                id="menu_name"
                name="menu_name"
                accepter={Input}
                data={menuName}
                valueKey="menu_name"
                labelKey="menu_name"
                block
                required
                onChange={(value) => {
                  setMenuName(value);
                }}
              />
            </Form.Group>

            <Stack direction="row" spacing={50}>
              <Form.Group>
                <Form.ControlLabel>Is Active</Form.ControlLabel>
                <RadioGroup name="isActiveList">
                  <Radio value="1" checked={isActive == "1" ? true : false} onChange={value => setIsActive(value)}>Yes</Radio>
                  <Radio value="2" checked={isActive == "0" ? true : false} onChange={value => setIsActive(value)}>No</Radio>
                </RadioGroup>
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Role</Form.ControlLabel>
                <RadioGroup name="isParentList">
                  <Radio value="1" checked={isParent == "1" ? true : false} onChange={value => setIsParent(value)}>Parent</Radio>
                  <Radio value="0" checked={isParent == "0" ? true : false} onChange={value => setIsParent(value)}>Child</Radio>
                </RadioGroup>
              </Form.Group>
            </Stack>

            <Form.Group>
              <Form.ControlLabel htmlFor="menu_link_code">Menu Link Code</Form.ControlLabel>
              <Form.Control
                id="menu_link_code"
                name="menu_link_code"
                accepter={Input}
                data={menuLinkCode}
                valueKey="menu_link_code"
                labelKey="menu_link_code"
                block
                required
                onChange={(value) => {
                  setMenuLinkCode(value);
                }}
              />
            </Form.Group>

            <Form.Group>
              <Form.ControlLabel>Module Code</Form.ControlLabel>
              <Form.Control
                name="module_code"
                accepter={SelectPicker}
                value={selectedModuleCode}
                data={allModule}
                valueKey="Module_Code"
                labelKey="Module_Name"
                block
                onChange={(value) => {
                  setSelectedModuleCode(value);
                }}
              />
            </Form.Group>

            <Form.Group>
              <Form.ControlLabel htmlFor="menu_icon">Menu Icon</Form.ControlLabel>
              <Form.Control
                id="menu_icon"
                name="menu_icon"
                accepter={Input}
                data={menuIcon}
                valueKey="menu_icon"
                labelKey="menu_icon"
                block
                onChange={(value) => {
                  setMenuIcon(value);
                }}
              />
            </Form.Group>

            <Form.Group>
              <Stack direction="row" spacing={6}>
                <Button appearance="primary" disabled={isFormDisabled} onClick={addMenuHandler} type="submit">
                  Submit
                </Button>
                <Button appearance="subtle" disabled={isFormDisabled} onClick={() => router.back()}>
                  Cancel
                </Button>
              </Stack>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export default AddMenu;

export async function getServerSideProps() {
  const { GetModuleAll } = ModuleApi();

  const { Data: allModuleData } = await GetModuleAll();
  let allModule = [];
  if (allModuleData != undefined) {
    allModule = allModuleData;
  }

  return {
    props: {
      allModule,
    },
  };
}
