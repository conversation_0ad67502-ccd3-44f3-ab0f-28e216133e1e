import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Button,
  IconButton,
  Stack,
  Breadcrumb,
  Tag,
  Table,
  Panel,
  Input,
  InputGroup,
  Pagination,
  Modal,
  useToaster,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";
import dateTimeFormatter from "@/lib/function/date-time-formatter";
import dateFormatterDash from "@/lib/function/date-formatter-dash";

import API_EformMasterPeriodicTesting from "@/pages/api/e_form/api_eform_master_periodic_testing";

export default function EFormMasterPeriodicTestingPage() {
  const router = useRouter();
  const toaster = useToaster();
  const [props, setProps] = useState([]);
  const { HeaderCell, Cell, Column } = Table;

  const [moduleName, setModuleName] = useState("");
  const [data, setData] = useState([]);

  const [showModal, setShowModal] = useState(false);
  const [mode, setMode] = useState(null);

  const [selectedRow, setSelectedRow] = useState(null);

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const typeData = ["PM", "RM"].map((item) => ({ label: item, value: item }));

  useEffect(() => {
    const fetchData = async () => {
      const res_data = await API_EformMasterPeriodicTesting().getAll();
      setData(res_data.data || []);
    };

    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("e-form/master_periodic_testing")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      fetchData();
    }
  }, []);

  const emptyFormValue = {
    product_name: null,
    product_code: null,
    parameter_lot: null,
    no_fup: null,
    created_by: null,
    updated_by: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id_master_periodic_testing",
    "product_name",
    "product_code",
    "parameter_lot",
    "no_fup",
    "created_at",
    "created_by",
    "updated_at",
    "updated_by",
  ];

  const datas =
    data && data.length > 0
      ? data
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .concat(
                item.created_at
                  ? dateTimeFormatter(
                      item.created_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .concat(
                item.updated_at
                  ? dateTimeFormatter(
                      item.updated_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .some((val) => val?.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const searchData = data
    ? data.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];

  const handleOpenModal = (mode) => {
    setMode(mode);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setFormValue(emptyFormValue);
    setMode(null);
  };

  const handleActions = async () => {
    try {
      let result;
      if (mode === "add") {
        result = await API_EformMasterPeriodicTesting().add({
          ...formValue,
          created_by: props.employee_id,
        });
      } else if (mode === "edit") {
        result = await API_EformMasterPeriodicTesting().edit({
          ...formValue,
          id_master_periodic_testing: selectedRow,
          updated_by: props.employee_id,
        });
      }

      const res = await API_EformMasterPeriodicTesting().getAll();
      setData(res.data);

      // Show the toast message
      toaster.push(
        Messages(
          "success",
          `Success ${
            mode === "add" ? "adding" : "editing"
          } E-Form Master Periodic Testing!`
        ),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );

      handleCloseModal();
    } catch (error) {
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );
    }
  };

  const handleExportExcel = (data) => {
    if (!data || data.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }

    const formattedData = data.map((item) => ({
      ID: item.id_master_periodic_testing,
      "Product Name": item.product_name,
      "Product Code": item.product_code,
      "Parameter Lot": item.parameter_lot,
      "No. FUP": item.no_fup,
      "Created At": item.created_at
        ? dateTimeFormatter(item.created_at, "id-ID", "seconds")
        : "-",
      "Created By": item.created_by,
      "Updated At": item.updated_at
        ? dateTimeFormatter(item.updated_at, "id-ID", "seconds")
        : "-",
      "Updated By": item.updated_by,
    }));

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");
    const date = dateFormatterDash(new Date());
    const filename = `E-Form Master Periodic Testing ${date}.xlsx`;
    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <Head>
        <title>E-Form Master Periodic Testing</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>E-Form</Breadcrumb.Item>
                  <Breadcrumb.Item active>
                    Master Periodic Testing
                  </Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusIcon />}
                    appearance="primary"
                    onClick={() => handleOpenModal("add")}
                  >
                    Add
                  </IconButton>
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={() => handleExportExcel(data)}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              data={datas}
              bordered
              cellBordered
              autoHeight
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={60} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>

              <Column fullText width={60} sortable resizable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id_master_periodic_testing" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Product Name</HeaderCell>
                <Cell dataKey="product_name" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Product Code</HeaderCell>
                <Cell dataKey="product_code" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Parameter Lot</HeaderCell>
                <Cell dataKey="parameter_lot" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>No. FUP</HeaderCell>
                <Cell dataKey="no_fup" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Created At</HeaderCell>
                <Cell dataKey="created_at">
                  {(rowData) => {
                    return rowData?.created_at
                      ? dateTimeFormatter(
                          rowData?.created_at,
                          "id-ID",
                          "seconds"
                        )
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell dataKey="created_by" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Updated At</HeaderCell>
                <Cell dataKey="updated_at">
                  {(rowData) => {
                    return rowData?.updated_at
                      ? dateTimeFormatter(
                          rowData?.updated_at,
                          "id-ID",
                          "seconds"
                        )
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column fullText width={150} sortable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>

              <Column width={80} fixed="right" align="center">
                <HeaderCell>...</HeaderCell>
                <Cell>
                  {(rowData) => {
                    function handleEditAction() {
                      setSelectedRow(rowData.id_master_periodic_testing);
                      setFormValue({
                        ...rowData,
                        date_review_from: rowData.date_review_from
                          ? new Date(rowData.date_review_from)
                          : null,
                        date_review_to: rowData.date_review_to
                          ? new Date(rowData.date_review_to)
                          : null,
                        publish_date: rowData.publish_date
                          ? new Date(rowData.publish_date)
                          : null,
                      });
                      setShowModal(true);
                      setMode("edit");
                    }
                    return (
                      <Button
                        onClick={handleEditAction}
                        appearance="link"
                        className="p-0"
                      >
                        Edit
                      </Button>
                    );
                  }}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                total={searchKeyword ? searchData.length : data.length}
                limitOptions={[10, 30, 50]}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        <Modal
          backdrop="static"
          open={showModal}
          overflow={false}
          onClose={() => handleCloseModal()}
        >
          <Modal.Header>
            <Modal.Title>
              {mode === "add" ? "Add" : "Edit"} E-Form Master Periodic Testing
            </Modal.Title>
          </Modal.Header>
          <Modal.Body className="flex flex-col gap-3">
            <div>
              <label>Product Name</label>
              <Input
                placeholder="Product Name"
                value={formValue.product_name}
                onChange={(value) => {
                  setFormValue({ ...formValue, product_name: value });
                }}
                maxLength={100}
              />
            </div>

            <div>
              <label>Product Code</label>
              <Input
                placeholder="Product Code"
                value={formValue.product_code}
                onChange={(value) => {
                  setFormValue({ ...formValue, product_code: value });
                }}
                maxLength={100}
              />
            </div>

            <div>
              <label>No. FUP</label>
              <Input
                placeholder="No. FUP"
                value={formValue.no_fup}
                onChange={(value) => {
                  setFormValue({ ...formValue, no_fup: value });
                }}
                maxLength={100}
              />
            </div>

            <div>
              <label>Parameter Lot</label>
              <Input
                as="textarea"
                placeholder="Parameter Lot"
                value={formValue.parameter_lot}
                onChange={(value) => {
                  setFormValue({ ...formValue, parameter_lot: value });
                }}
                maxLength={1000}
              />
              {/* char counter */}
              <div className="flex justify-end">
                <span className="text-xs text-gray-400">
                  {formValue.parameter_lot?.length || 0}/1000
                </span>
              </div>
            </div>

            <div className="flex flex-col gap-2">
              <span className="text-red-500">* Required</span>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                handleCloseModal();
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleActions();
              }}
              appearance="primary"
            >
              {mode === "add" ? "Add" : "Edit"}
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
