import { useRouter } from "next/router";
import { useState, useEffect } from "react";
import MainContent from "@/components/layout/MainContent";
import { Dropdown, Button, Form, ButtonToolbar, Toggle } from "rsuite";
import ipcApi from "@/pages/api/ipcApi";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import ContainerLayout from "@/components/layout/ContainerLayout";
import ModuleContentHeader from "@/components/ModuleContentHeader";

export default function AddInstrument() {
  const MySwal = withReactContent(Swal);
  const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
  const [instrumentName, setInstrumentName] = useState("");
  const [instrumentCode, setInstrumentCode] = useState("");
  const [isActive, setIsActive] = useState(false);
  const [location, setLocation] = useState("");
  const [modalOpen, setModalOpen] = useState(false);
  const [moduleName, setModuleName] = useState("");
  const { InsertNewInstrument } = ipcApi();
  const router = useRouter();
  let path = "masterdata/Instrument";
  let { data } = router.query;
  data ? (data = JSON.parse(data)) : data;
  var [breadcrumbsData, setBreadcrumbsData] = useState([]);
  const [sessionAuth, setSessionAuth] = useState(null);
  // const breadcrumbsData = [
  //   moduleName,
  //   "Setup Master Data",
  //   "Add New Instrument",
  // ];

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    console.log("datalogin ", dataLogin)
    if (dataLogin === null || dataLogin === undefined || dataLogin === "") {
      router.push("/");
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push("/dashboard");
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path)
    );
    if (validateUserAccess.length === 0) {
      router.push("/dashboard");
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ""
    ) {
      router.push("/dashboard");
      return;
    }

    const asPathWithoutQuery = router.asPath.split("?")[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split("/")
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      `Setup ${asPathNestedRoutes[1]}`,
      `Add ${asPathNestedRoutes[2]}`,
    ];
    setBreadcrumbsData(routerBreadCrumbsData);

    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
  }, []);

  //   submitHandler
  const submitHandler = async () => {
    if (instrumentName === "" || instrumentCode === "" || location === "") {
      // modalHandler("alert", "Please fill all required data !");
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please fill all required data!",
      });
      return;
    } else {
      MySwal.showLoading();
      const isInstrumentActive = isActive ? "Y" : "N";
      const dataInput = {
        instrument_name: instrumentName,
        location: location,
        code_instrument: instrumentCode,
        is_active: isInstrumentActive,
        create_by:  sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      };

      const { Data: insertingInstrument } = await InsertNewInstrument(
        dataInput
      );
      if (insertingInstrument) {
        setIsSubmitButtonDisabled(true);
        MySwal.fire({
          position: "center",
          icon: "success",
          title: "Instrument inserted successfully.",
          showConfirmButton: false,
          timer: 2500,
        });
        router.push("/user_module/masterdata/Instrument");
      } else {
        MySwal.fire({
          icon: "error",
          title: "Oops...",
          text: "Instrument insert FAILED.",
        });
      }
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Add Instrument</title>
        </Head>
      </div>
      {/* Modal */}

      <ContainerLayout title="User Module">
        <MainContent>
          {/* <div className="mb-3">
            <h4>PIMS - Menu</h4>
            <p>Setup Master Data &gt; Add New Instrument</p>
          </div> */}
          <ModuleContentHeader
            breadcrumbs={breadcrumbsData}
            module_name={moduleName}
          />

          <Form layout="horizontal" onSubmit={submitHandler}>
            <Form.Group controlId="instrument_name">
              <Form.ControlLabel>Instrument Name</Form.ControlLabel>
              <Form.Control
                name="instrument_name"
                type="text"
                value={instrumentName}
                onChange={(value) => setInstrumentName(value)}
              />
            </Form.Group>
            <Form.Group controlId="location">
              <Form.ControlLabel>Location</Form.ControlLabel>
              <Dropdown
                title={location !== "" ? location : "-- Select Item --"}
                onSelect={(value) => setLocation(value)}
              >
                <Dropdown.Item eventKey="">-- Select Item --</Dropdown.Item>
                <Dropdown.Item eventKey="Production">Production</Dropdown.Item>
              </Dropdown>
            </Form.Group>
            <Form.Group controlId="instrument_code">
              <Form.ControlLabel>Instrument Code</Form.ControlLabel>
              <Form.Control
                name="instrument_code"
                type="text"
                value={instrumentCode}
                onChange={(value) => setInstrumentCode(value)}
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>Active</Form.ControlLabel>
              <Toggle
                checked={isActive}
                onChange={() => setIsActive((value) => !value)}
              />
            </Form.Group>

            <Form.Group>
              <ButtonToolbar>
                <Button
                  appearance="primary"
                  type="submit"
                  disabled={isSubmitButtonDisabled}
                >
                  Submit
                </Button>
                <Button appearance="default" onClick={() => router.back()}>
                  Cancel
                </Button>
              </ButtonToolbar>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}
