import axios from "axios";

// Function untuk membuat API call
const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiBindingCategory() {
  return {
    getAllBindingCategory: createApiFunction("get", "oee/binding_category/list"),
    getAllActiveBindingCategory: createApiFunction("get", "oee/binding_category/list-active"),
    getBindingCategoryById: createApiFunction("post", "oee/binding_category/id"),
    createBindingCategory: createApiFunction("post", "oee/binding_category/create"),
    editBindingCategory: createApiFunction("put", "oee/binding_category/edit"),
    editStatusBindingCategory: createApiFunction("put", "oee/binding_category/edit-status"),
  };
}
