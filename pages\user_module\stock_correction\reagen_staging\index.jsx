import { useEffect, useState } from "react";
import Head from "next/head"
import { <PERSON><PERSON><PERSON><PERSON>b, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, useToaster, SelectPicker, Checkbox, ButtonGroup, Form } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import { useRouter } from "next/router";
import API_StockCorrection from "@/pages/api/stock_correction/api_stock_correction";

export default function ReagenStagingPage() {

    const [moduleName, setModuleName] = useState("");
    const { HeaderCell, Cell, Column } = Table;
    const [reagenStaging, setReagenStaging] = useState([]);
    const [reagenName, setReagenName] = useState([]);
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [props, setProps] = useState([]);
    const [deleteLocator, setDeleteLocator] = useState([]);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const emptyFormValue = {
        id_reagen_correction: null,
        data: [
            {
                id_inbound_reagen_locator: null,
            }
        ]
    };
    const [formValue, setFormValue] = useState(emptyFormValue);
    const emptyFormValueHeader = {
        remarks: null,
    }
    const [formValueHeader, setFormValueHeader] = useState(emptyFormValueHeader);
    const [errors, setErrors] = useState({});
    const toaster = useToaster();
    const router = useRouter();


    const handleGetNameApi = async () => {
        const res = await API_StockCorrection().getName();
        console.log(res);
        setReagenName(res.data ? res.data : []);
    }

    const handleGetAllApi = async (reagenName) => {
        const res = await API_StockCorrection().getUsed({ reagen_name: reagenName });
        console.log(res);
        setReagenStaging(res.data ? res.data : []);
    };

    const handleGetActiveCorrectionApi = async () => {
        const res = await API_StockCorrection().getActiveBySupervisor();
        console.log("aktif", res);

        if (res.data && res.data.id_reagen_correction) {
            setFormValue({
                ...emptyFormValue,
                id_reagen_correction: res.data.id_reagen_correction
            })
        }
    }

    const updateFormValue = (deleteLocator) => {
        const idInboundReagenLocator = deleteLocator.map(item => ({
            id_inbound_reagen_locator: item.id_inbound_reagen_locator
        }));
        console.log("id inbound", idInboundReagenLocator)
        setFormValue({
            ...formValue,
            data: idInboundReagenLocator
        });
    };

    //handleDeleteApi
    const handleDeleteApi = async () => {
        try {
            console.log("form value", formValue)
            const result = await API_StockCorrection().createStaging({
                ...formValue
            });

            console.log("result", result)

            if (result.status === 200) {
                handleGetAllApi();
                setShowDeleteModal(false);
                setDeleteLocator([]);
                setFormValue(emptyFormValue);
                handleGetActiveCorrectionApi();

                toaster.push(
                    Messages("success", "Stock Correction Request Success"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
            } else if (result.status === 400) {
                toaster.push(
                    Messages("error", `Error: "There is no Active Correction Request"`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setShowDeleteModal(false);
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    };

    const submitCorrection = async () =>{
        try {
            if (!formValueHeader.remarks) {
                setErrors({ remarks: 'Remarks is required.' });
                return;
            }
            setErrors({});
            setFormValue(emptyFormValueHeader)
            console.log("form value", formValue)
            const correction_header = {
                remarks: formValueHeader.remarks,
                submitted_by: props.employee_id,
            }
            const result = await API_StockCorrection().submitCorrection({
                correction_header:correction_header,
                correction_detail:formValue
            });

            console.log("result", result)

            if (result.status === 200) {
                handleGetAllApi();
                setShowDeleteModal(false);
                setDeleteLocator([]);
                setFormValue(emptyFormValue);
                handleGetActiveCorrectionApi();

                toaster.push(
                    Messages("success", "Stock Correction Request Success"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
            } else if (result.status === 400) {
                toaster.push(
                    Messages("error", `Error: "Cannot submit please check if you have ongoing correction"`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setShowDeleteModal(false);
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    }

    const handleSearch = (value) => {
        setSearchKeyword(value);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = reagenStaging
        .filter((rowData, i) => {
            const searchFields = [
                "id_inbound_reagen_locator",
                "reagen_name",
                "rack_desc",
                "floor_level_desc",
                "row_level_desc",
                "expired_date"
            ];

            const matchesSearch = searchFields.some((field) =>
                rowData[field]
                    ?.toString()
                    .toLowerCase()
                    .includes(searchKeyword.toLowerCase())
            );
            return matchesSearch;
        })

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    }


    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
    
                if (sortColumn === "expired_date") {
                    x = new Date(x); // Convert to Date object
                    y = new Date(y);
                } else {
                    if (typeof x === "string") x = x.charCodeAt();
                    if (typeof y === "string") y = y.charCodeAt();
                }
    
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };
    

    const totalRowCount = searchKeyword ? getFilteredData().length : reagenStaging.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("stock_correction/reagen_staging")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            handleGetNameApi();
            handleGetActiveCorrectionApi();
        }
    }, []);

    const handleReagenNameClick = () => {
        router.push("/user_module/stock_correction/reagen_staging")
    }

    const handleRackDescriptionClick = () => {
        router.push("/user_module/stock_correction/reagen_staging_by_rack")
    }

    const handleViewDetailClick = () => {
        router.push("/user_module/stock_correction/edit_correction_detail")
    }

    return (
        <>
            <div>
                <Head>
                    <title>Reagen Staging</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Quality</Breadcrumb.Item>
                                    <Breadcrumb.Item>Stock Correction</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Reagen Staging</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Reagen Staging by Name</h5>
                                <Stack className="flex gap-2">
                                    <ButtonGroup>
                                        <Button
                                            onClick={handleReagenNameClick}
                                            appearance="primary"
                                            active>
                                            Search by Reagen Name
                                        </Button>
                                        <Button
                                            onClick={handleRackDescriptionClick}
                                            appearance="primary">
                                            Search by Rack Description
                                        </Button>
                                        {/* <Button
                                            appearance="primary"
                                            onClick={handleViewDetailClick}>
                                            View Detail
                                        </Button> */}
                                    </ButtonGroup>
                                </Stack>
                            </Stack>}>
                    </Panel>

                    <Panel
                        bordered
                        bodyFill
                        header={
                            <Stack justifyContent="space-between">
                                <div className="flex gap-2">
                                    <SelectPicker
                                        placeholder="Reagen Name"
                                        appearance="default"
                                        className="w-52"
                                        data={reagenName}
                                        labelKey="reagen_name"
                                        valueKey="reagen_name"
                                        onChange={(value) => {
                                            handleGetAllApi(value);
                                            setDeleteLocator([]);
                                        }}
                                    />
                                    <IconButton
                                        icon={<CloseOutlineIcon />}
                                        appearance="primary"
                                        onClick={() => {
                                            setShowDeleteModal(true);
                                            updateFormValue(deleteLocator);
                                        }}>
                                        Submit
                                    </IconButton>
                                </div>

                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="search"
                                        value={searchKeyword}
                                        onChange={handleSearch}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}>
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                        }
                    >
                        <Table
                            bordered
                            cellBordered
                            height={400}
                            data={getPaginatedData(getFilteredData(), limit, page)}
                            sortColumn={sortColumn}
                            sortType={sortType}
                            onSortColumn={handleSortColumn}
                        >
                            <Column width={70} align="center" fixed>
                                <HeaderCell>No</HeaderCell>
                                <Cell>
                                    {(rowData, rowIndex) => {
                                        return rowIndex + 1 + limit * (page - 1);
                                    }}
                                </Cell>
                            </Column>
                            <Column width={140} align="center" sortable>
                                <HeaderCell>ID Locator</HeaderCell>
                                <Cell dataKey="id_inbound_reagen_locator" />
                            </Column>
                            <Column width={220} align="center" sortable>
                                <HeaderCell>Reagen Name</HeaderCell>
                                <Cell dataKey="reagen_name" />
                            </Column>
                            <Column width={220} align="center" sortable>
                                <HeaderCell>Expired Date</HeaderCell>
                                <Cell dataKey="expired_date">
                                    {(rowData) => new Date(rowData.expired_date).toLocaleDateString('en-GB')}
                                </Cell>
                            </Column>
                            <Column width={220} align="center" sortable>
                                <HeaderCell>Rack</HeaderCell>
                                <Cell dataKey="rack_desc" />
                            </Column>
                            <Column width={120} align="center" sortable>
                                <HeaderCell>Floor</HeaderCell>
                                <Cell dataKey="floor_level_desc" />
                            </Column>
                            <Column width={120} align="center" sortable>
                                <HeaderCell>Row</HeaderCell>
                                <Cell dataKey="row_level_desc" />
                            </Column>
                            <Column width={120} fixed="right" align="center">
                                <HeaderCell>Submit Correction</HeaderCell>
                                <Cell style={{ padding: "8px" }}>
                                    {(rowData) => (
                                        <div>
                                            <Checkbox
                                                checked={deleteLocator.includes(rowData)}
                                                onChange={() => {
                                                    if (deleteLocator.includes(rowData)) {
                                                        setDeleteLocator(prevDeleteLocator =>
                                                            prevDeleteLocator.filter(item => item !== rowData)
                                                        );
                                                    } else {
                                                        setDeleteLocator(prevDeleteLocator => [...prevDeleteLocator, rowData]);
                                                    }
                                                }}
                                            />
                                        </div>
                                    )}
                                </Cell>
                            </Column>
                        </Table>
                        <div style={{ padding: 20 }}>
                            <Pagination
                                prev
                                next
                                first
                                last
                                ellipsis
                                boundaryLinks
                                maxButtons={5}
                                size="xs"
                                layout={["total", "-", "limit", "|", "pager", "skip"]}
                                limitOptions={[10, 30, 50]}
                                total={totalRowCount}
                                limit={limit}
                                activePage={page}
                                onChangePage={setPage}
                                onChangeLimit={handleChangeLimit}
                            />
                        </div>
                    </Panel>
                </div>

                <Modal
                    backdrop="static"
                    open={showDeleteModal}
                    onClose={() =>{ 
                        setShowDeleteModal(false)
                        setFormValueHeader(emptyFormValueHeader)
                        setErrors({})
                    }
                    }
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Deletion Details</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                    <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Remarks</Form.ControlLabel>
                                <Form.Control
                                    name="remarks"
                                    value={formValueHeader.remarks || ''}
                                    onChange={(value) => {
                                        setFormValueHeader({ ...formValueHeader, remarks: value })
                                        setErrors((prevErrors) => ({
                                            ...prevErrors,
                                            remarks: null,
                                        }))
                                    }}
                                />
                                {errors.remarks && <p style={{ color: 'red' }}>{errors.remarks}</p>}
                            </Form.Group>
                        </Form>
                        <br/>
                        <p className="mb-2"><span className="fw-bold">Detail Locator : </span></p>
                        {deleteLocator.length > 0 ? (
                            deleteLocator.map((item) => (
                                <div key={item.id_inbound_reagen_locator}>
                                    <Panel bordered className="mb-3">
                                        <p className="mb-2"><span className="fw-bold">ID Inbound Reagen Locator : </span>{item.id_inbound_reagen_locator}</p>
                                        <p className="mb-2"><span className="fw-bold">Reagen Name : </span>{item.reagen_name}</p>
                                        <p className="mb-2"><span className="fw-bold">Rack : </span>{item.rack_desc}</p>
                                        <p className="mb-2"><span className="fw-bold">Floor : </span>{item.floor_level_desc}</p>
                                        <p className="mb-2"><span className="fw-bold">Row : </span>{item.row_level_desc}</p>
                                    </Panel>
                                </div>
                            ))
                        ) :
                            (
                                <p>No Locator Selected</p>
                            )
                        }
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowDeleteModal(false)
                                setFormValueHeader(emptyFormValueHeader)
                                setErrors({})
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            disabled={deleteLocator.length === 0}
                            onClick={() => {
                                // handleDeleteApi();
                                submitCorrection();
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Submit
                        </Button>
                    </Modal.Footer>
                </Modal>

            </ContainerLayout>
        </>
    )
}
