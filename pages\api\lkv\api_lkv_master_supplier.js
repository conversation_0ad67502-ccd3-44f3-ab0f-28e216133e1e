import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/supplier${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function LkvMasterSupllier(){
    return{
        getAllMasterSupllier: createApiFunction("get", "/get/all"),
        getAllMasterSupllierActive : createApiFunction("get", "/get/all/active"),
        createMasterSupllier: createApiFunction("post", "/create"),
        updateMasterSupllier: createApiFunction("put", "/edit"),
        updateStatusMasterSupllier: createApiFunction("put", "/active")
    }
}