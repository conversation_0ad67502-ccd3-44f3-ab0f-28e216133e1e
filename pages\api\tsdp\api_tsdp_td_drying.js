import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](
    `${process.env.NEXT_PUBLIC_REAGEN}/ts/${url}`,
    data
  )
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiTsdpTDDrying() {
  return {
    getAllTsTransactionDrying: createApiFunction(
      "get",
      "transaction-d-drying/list"
    ),
    getTsTransactionDryingById: createApiFunction(
      "post",
      "transaction-d-drying/id"
    ),
    postTsTransactionDrying: createApiFunction(
      "post",
      "transaction-d-drying/create"
    ),
    postTsTransactionDryingAuto: createApiFunction(
      "post",
      "transaction-d-drying/create-auto"
    ),
    UpdateTsTransactionDrying: createApiFunction(
      "put",
      "transaction-d-drying/edit"
    ),
    UpdateStatusTsTransactionDrying: createApiFunction(
      "put",
      "transaction-d-drying/edit-status"
    ),
  };
}
