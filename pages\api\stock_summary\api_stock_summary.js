import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_PIMSORA_SERVICE}/${url}`,
        data
    )
        .then((res) => {return res.data})
        .catch((err) => {return err.response.data});
    return response;
};

export default function ApiStockSummaryOra(){
    return{
        getOcList :createApiFunction("get","stock_summary/oc/list"),
		getPgList :createApiFunction("get","stock_summary/pg/list"),
		getUnderstockList :createApiFunction("get","stock_summary/understock/list"),
		getUnderstockList: createApiFunction("get","stock_summary/overstock/list"),
		getAllStock: createApiFunction("get","stock_summary/stock/list"),
		createStockList: createApiFunction("post","stock_summary/create"),
		updateStockList: createApiFunction("put","stock_summary/update"),
    }
}