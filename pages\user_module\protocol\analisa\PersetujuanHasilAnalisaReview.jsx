import { useState, useEffect } from 'react';
import ProtocolApi from '@/pages/api/protocolApi';
import Head from 'next/head';
import ContainerLayout from '@/components/layout/ContainerLayout';
import MainContent from '@/components/layout/MainContent';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import { useRouter } from 'next/router';
import {
    Button,
    Stack,
    Breadcrumb,
    Tag,
    Table,
    Panel,
    Input,
    InputGroup,
    Pagination,
    Divider,
    Modal,
    Form,
    Schema,
    useToaster,
    DatePicker,
    SelectPicker,
    IconButton,
} from "rsuite";
import { paginate } from '@/utils/paginate';
import FileDownloadIcon from '@rsuite/icons/FileDownload';
import SearchIcon from '@rsuite/icons/Search';
import CloseOutlineIcon from '@rsuite/icons/CloseOutline';

export default function PersetujuanHasilAnalisaReview() {
    const router = useRouter();
    const [allProtocolHeaderData, setAllProtocolHeaderData] = useState([]);
    const { GetProtocolHasilAnalisaReviewer } = ProtocolApi();
    const [moduleName, setModuleName] = useState('');
    const { HeaderCell, Cell, Column } = Table;
    const [breadcrumbsData, setBreadcrumbsData] = useState([]);
    const [userDept, setUserDept] = useState('');
    const [page, setPage] = useState(1);
    const [limit, setLimit] = useState(10);
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [searchKeyword, setSearchKeyword] = useState("");
    let path = 'protocol/analisa/PersetujuanHasilAnalisaReview';

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setSortColumn(sortColumn);
        setSortType(sortType);
    };

    // data to be displayed in the table
    var filteredData = allProtocolHeaderData.filter(item => item.No_Protocol.toLowerCase().includes(searchKeyword.toLowerCase()) || item.Created_By.toLowerCase().includes(searchKeyword.toLowerCase()) || item.Protocol_Status.toLowerCase().includes(searchKeyword.toLowerCase()));
    var datas = paginate(filteredData, page, limit);

    const GetDataForApproval = async (deptId) => {
        const inputData = {
            id_dept: parseInt(deptId),
        };
        const { data: data } = await GetProtocolHasilAnalisaReviewer(inputData);

        if (data !== null && data?.length > 0) {
            const newData = data.map(item => {
                let status = '';
                if (item.Status_Request_Result == 3) {
                    status = 'Ready for review';
                } else if (item.Status_Request_Result == 2) {
                    status = 'Ready for approval';
                } else if (item.Status_Request_Result == 1) {
                    status = 'Approved';
                } else if (item.Status_Request_Result == 0 || rowData.Status_Request_Result == 5) {
                    status = 'Rejected';
                } else {
                    status = 'Waiting';
                }

                item.Protocol_Status = status;
                return item;
            });
            setAllProtocolHeaderData(newData);
        } else {
            setAllProtocolHeaderData([]);
        }
    };

    useEffect(() => {
        console.log("Nilai all protocol header data : ", allProtocolHeaderData);
    }, [allProtocolHeaderData]);

    useEffect(() => {
        const moduleNameValue = localStorage.getItem('module_name');
        const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
        if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
            router.push('/');
            return;
        }

        // validate access for this page
        if (!dataLogin.menu_link_code) {
            router.push('/dashboard');
            return;
        }
        const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
            item.includes(path),
        );
        if (validateUserAccess.length === 0) {
            router.push('/dashboard');
            return;
        }

        if (
            moduleNameValue === null ||
            moduleNameValue === undefined ||
            moduleNameValue === ''
        ) {
            router.push('/dashboard');
            return;
        }

        const asPathWithoutQuery = router.asPath.split('?')[0];
        const asPathNestedRoutes = asPathWithoutQuery
            .split('/')
            .filter((v) => v.length);
        let routerBreadCrumbsData = asPathNestedRoutes.filter((item, index) => index != 0);
        routerBreadCrumbsData.unshift(moduleNameValue);
        const capitalizeFirstLetter = (str) => {
            return str.charAt(0).toUpperCase() + str.slice(1);
        };
        const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
            const words = item.split(/(?=[A-Z])/);
            return words.map((word) => capitalizeFirstLetter(word)).join(' ');
        });
        setBreadcrumbsData(breadCrumbsResult);

        setModuleName(moduleNameValue);

        GetDataForApproval(dataLogin.department);

        setUserDept(dataLogin.department);
    }, []);

    return (
        <>
            <div>
                <Head>
                    <title>Persetujuan Hasil Analisa: Review</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <MainContent>
                    <ModuleContentHeader
                        breadcrumbs={breadcrumbsData}
                        module_name={moduleName}
                    />
                    {allProtocolHeaderData?.length > 0 ? (
                        <>
                            <Stack direction='row' justifyContent='flex-end'>
                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="Search"
                                        value={searchKeyword}
                                        onChange={value => {
                                            setSearchKeyword(value);
                                            setPage(1);
                                        }}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}
                                    >
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                            <Panel>
                                <Table
                                    bordered
                                    cellBordered
                                    height={400}
                                    data={datas}
                                    onRowClick={(rowData) => {
                                        console.log(rowData);
                                    }}
                                    sortColumn={sortColumn}
                                    sortType={sortType}
                                    onSortColumn={handleSortColumn}
                                >
                                    <Column width={60} align="center" fixed>
                                        <HeaderCell>No</HeaderCell>
                                        <Cell>
                                            {(rowData, rowIndex) => {
                                                return rowIndex + 1 + limit * (page - 1);
                                            }}
                                        </Cell>
                                    </Column>

                                    <Column width={150}>
                                        <HeaderCell>No Protocol</HeaderCell>
                                        <Cell dataKey="No_Protocol" />
                                    </Column>

                                    <Column width={150} resizable>
                                        <HeaderCell>Batch Number</HeaderCell>
                                        <Cell dataKey='Batch_number'  />
                                    </Column>

                                    <Column width={100}>
                                        <HeaderCell>Cycle Month</HeaderCell>
                                        <Cell dataKey="Cycle_Month" />
                                    </Column>

                                    <Column width={250}>
                                        <HeaderCell>Kondisi Penyimpanan</HeaderCell>
                                        <Cell>
                                            {rowData => {
                                                const name = rowData.Timeframe_Name.slice(0, 3);
                                                if (name == 'T30') {
                                                    return <Tag color="cyan">{rowData.Timeframe_Name}</Tag>;
                                                } else {
                                                    return <Tag color="violet">{rowData.Timeframe_Name}</Tag>;
                                                }
                                            }}
                                        </Cell>
                                    </Column>

                                    <Column width={150}>
                                        <HeaderCell>Dibuat Oleh</HeaderCell>
                                        <Cell dataKey="Created_By" />
                                    </Column>

                                    <Column width={50}>
                                        <HeaderCell>Versi</HeaderCell>
                                        <Cell dataKey="Protocol_Version" />
                                    </Column>

                                    <Column width={150}>
                                        <HeaderCell>Tipe Protokol</HeaderCell>
                                        <Cell dataKey="Department_Name" />
                                    </Column>

                                    <Column width={150}>
                                        <HeaderCell>Status</HeaderCell>
                                        <Cell dataKey='Protocol_Status' />
                                    </Column>

                                    <Column width={150} fixed="right">
                                        <HeaderCell>...</HeaderCell>

                                        <Cell style={{ padding: "6px" }}>
                                            {(rowData) => (
                                                <Stack direction='row' justifyContent='center'>
                                                    {<IconButton size='md' icon={<SearchIcon />} title='Tinjau dokumen' appearance='ghost' color='orange' onClick={() => {
                                                        router.push({
                                                            pathname:
                                                                '/user_module/protocol/analisa/PersetujuanHasilAnalisaReviewDetail',
                                                            query: { noProtocol: rowData.No_Protocol, idSetupCycle: rowData.Id_Setup_Cycle, id_detail_batch: rowData.Id_detail_batch, id_request_result : rowData.Id_request_result  },
                                                        });
                                                    }} />}
                                                </Stack>
                                            )}
                                        </Cell>
                                    </Column>
                                </Table>
                                <div style={{ padding: 20 }}>
                                    <Pagination
                                        prev
                                        next
                                        first
                                        last
                                        ellipsis
                                        boundaryLinks
                                        maxButtons={5}
                                        size="xs"
                                        layout={["total", "-", "limit", "|", "pager"]}
                                        total={
                                            allProtocolHeaderData.length
                                        }
                                        limitOptions={[10, 30, 50]}
                                        limit={limit}
                                        activePage={page}
                                        onChangePage={setPage}
                                        onChangeLimit={handleChangeLimit}
                                    />
                                </div>
                            </Panel>
                        </>
                    ) : <Stack direction='row' justifyContent='center'>
                        <p className='font-semibold'>No data found.</p>
                    </Stack>}
                    {/* {allProtocolHeaderData.length == 0 && <Stack justifyContent='center'>
                        <p style={{ fontWeight: 'bold' }}>No Protocol Document Data.</p>
                    </Stack>} */}
                </MainContent>
            </ContainerLayout>
        </>
    );
}
