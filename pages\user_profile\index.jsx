import ContainerLayout from "@/components/layout/ContainerLayout";
import MainContent from "@/components/layout/MainContent";
import Head from "next/head";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Stack } from "rsuite";
import UseTestApi from "../api/userApi";

export default function UserProfileData() {
  const router = useRouter();
  const { GetByIdTestApi } = UseTestApi();
  const [userData, setUserData] = useState([]);
  const [employeeId, setEmployeeId] = useState("");

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }
    setEmployeeId(dataLogin.employee_id);
  }, []);

  const getUserdata = async (dataId) => {
    const { Data } = await GetByIdTestApi(dataId);

    if (Data) {
      return setUserData(Data);
    }
  };

  useEffect(() => {
    if (employeeId !== "") {
      getUserdata(employeeId);
    }
  }, [employeeId]);

  return (
    <>
      <Head>
        <title>User Profile</title>
      </Head>
      <ContainerLayout title="Profile">
        <MainContent>
          <div className="mb-5 p-2" style={{ borderLeft: "0.5em solid teal" }}>
            <h4>User Profile</h4>
          </div>
          <div className="row">
            <div className="col-sm-4">
              <img src="/user.png" style={{ width: 200 }} />
            </div>
            {userData.length > 0 && (
              <div className="col-sm-8">
                <div className="mb-3">
                  <p style={{ fontSize: "3em" }}>{userData[0].Name}</p>
                  <p>{`${userData[0].Department} - ${userData[0].Division}`}</p>
                </div>
                <Stack spacing={6}>
                  <Button
                    appearance="default"
                    active
                    onClick={() =>
                      router.push({
                        pathname: `/user_profile/editUserProfile/`,
                        query: { emp_id: employeeId },
                      })
                    }
                  >
                    Change Profile
                  </Button>
                  <Button
                    appearance="primary"
                    color="red"
                    onClick={() =>
                      router.push({
                        pathname: "/user_management/changePassword",
                        query: { data: employeeId },
                      })
                    }
                  >
                    Change Password
                  </Button>
                </Stack>
              </div>
            )}
          </div>
        </MainContent>
      </ContainerLayout>
    </>
  );
}
