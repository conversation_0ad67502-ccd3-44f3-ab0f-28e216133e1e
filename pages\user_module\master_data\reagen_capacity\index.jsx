import { useEffect, useState } from "react";
import Head from "next/head"
import { <PERSON><PERSON><PERSON><PERSON>b, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, SelectPicker, Grid, Row, Col } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import API_MasterDataReagenCapacity from "@/pages/api/master_data/api_reagen_capacity";
import API_MasterDataReagenCapRack from "@/pages/api/master_data/api_master_data_rc_rack";
import API_MasterDataReagenCapFloor from "@/pages/api/master_data/api_master_data_rc_floor";
import API_MasterDataReagenCapRow from "@/pages/api/master_data/api_master_data_rc_row";
import API_MasterDataReagenDimension from "@/pages/api/master_data/api_masterdata_master_data_reagen_dimension";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash"
import { useRouter } from "next/router";

export default function MasterDataReagenCapacityPage() {

    const [moduleName, setModuleName] = useState("");
    const { HeaderCell, Cell, Column } = Table;
    const [capacity, setCapacity] = useState([]);
    const [rack, setRack] = useState([]);
    const [floor, setFloor] = useState([]);
    const [row, setRow] = useState([]);
    const [dimension, setDimension] = useState([]);
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [props, setProps] = useState([]);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showAddModal, setShowAddModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const emptyFormValue = {
        id_rack: null,
        id_floor: null,
        id_row: null,
        id_dimension: null,
        max_capacity: null
    }
    const [formValue, setFormValue] = useState(emptyFormValue);
    const toaster = useToaster();
    const [errors, setErrors] = useState({});
    const router = useRouter();

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const handleGetAllApi = async () => {
        const res = await API_MasterDataReagenCapacity().getAll();
        console.log(res);
        setCapacity(res.data ? res.data : []);
    };

    const filteredData = capacity
        .filter((rowData, i) => {
            const searchFields = [
                "id_capacity",
                "rack_desc",
                "floor_level_desc",
                "row_level_desc",
                "criteria_desc",
                "dimension_desc",
                "max_capacity",
                "created_dt",
                "created_by",
                "created_name",
                "updated_dt",
                "updated_by",
                "updated_name",
                "deleted_dt",
                "deleted_by",
                "deleted_name",
                "is_active",
            ];

            const matchesSearch = searchFields.some((field) =>
                rowData[field]
                    ?.toString()
                    .toLowerCase()
                    .includes(searchKeyword.toLowerCase())
            );

            return matchesSearch;
        })

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    }

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : capacity.length;

    const fetchMasterData = async () => {
        const res_rack = await API_MasterDataReagenCapRack().getAllActive();
        setRack(res_rack.data ? res_rack.data : []);

        const res_floor = await API_MasterDataReagenCapFloor().getAllActive();
        setFloor(res_floor.data ? res_floor.data : []);

        const res_row = await API_MasterDataReagenCapRow().getAllActive();
        setRow(res_row.data ? res_row.data : []);

        const res_dimension = await API_MasterDataReagenDimension().getAllActive();
        setDimension(res_dimension.data ? res_dimension.data : []);
    }

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("master_data/reagen_capacity")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            handleGetAllApi();
            fetchMasterData();
        }
    }, []);

    const [deleteFormValue, setDeleteFormValue] = useState({
        id_capacity: null,
        deleted_by: null,
        reason: "",
        rack_desc:null,
        floor_level_desc:null,
        row_level_desc:null,
        criteria_desc:null,
        dimension_desc:null,
        max_capacity:null
      });


    const handleEditStatusCapacity = async (selectedIdCapacity) => {
        await API_MasterDataReagenCapacity().editStatus({
            id_capacity: selectedIdCapacity,
            deleted_by: props.employee_id,
            reason: deleteFormValue.reason
        });
        handleGetAllApi();
        setShowDeleteModal(false);
        setDeleteFormValue({});
    };

    const handleEditApi = async (selectedIdCapacity) => {
        try {
            const result = await API_MasterDataReagenCapacity().edit({
                ...formValue,
                id_capacity: selectedIdCapacity,
                updated_by: props.employee_id,
            });

            if (result.status === 200) {
                handleGetAllApi();
                setShowEditModal(false);

                toaster.push(Messages("success", "Success editing Capacity Data!"), {
                    placement: "topCenter",
                    duration: 5000,
                });
                setFormValue(emptyFormValue);
            } else if (result.status === 400) {
                toaster.push(
                    Messages("error", `Error: "${result.message}". Please try again later!`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setFormValue(emptyFormValue);
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    };

    const handleAddApi = async () => {
        try {
            const result = await API_MasterDataReagenCapacity().create({
                ...formValue,
                created_by: props.employee_id,
            });

            if (result.status === 200) {
                handleGetAllApi();
                setShowAddModal(false);

                toaster.push(
                    Messages("success", "Success adding Capacity Capacity!"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setFormValue(emptyFormValue);
            } else if (result.status === 400) {
                toaster.push(
                    Messages("error", `Error: "${result.message}". Please try again later!`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setFormValue(emptyFormValue);
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    };

    const handleExportExcel = () => {

        if (capacity.length === 0) {
            toaster.push(Messages("error", "No data to export!"), {
                placement: "topCenter",
                duration: 5000,
            });
            return;
        }

        const headerMapping = {
            id_capacity: "ID Capacity",
            rack_desc: "Rack Description",
            floor_level_desc: "Floor Level Description",
            row_level_desc: "Row Level Description",
            criteria_desc: "Criteria Description",
            dimension_desc: "Dimension Description",
            max_capacity: "Maximum Capacity",
            created_dt: "Created Date",
            created_by: "Created By",
            created_name: "Created Name",
            updated_dt: "Updated Date",
            updated_by: "Updated By",
            updated_name: "Updated Name",
            deleted_dt: "Deleted Date",
            deleted_by: "Deleted By",
            deleted_name: "Deleted Name",
            is_active: "Status",
        };

        const formattedData = capacity.map((item) => {
            const formattedItem = {};
            for (const key in item) {
                if (headerMapping[key]) {
                    if (key === 'is_active') {
                        formattedItem[headerMapping[key]] = item[key] === 1 ? 'Active' : 'Inactive';
                    } else {
                        formattedItem[headerMapping[key]] = item[key];
                    }
                }
            }
            return formattedItem;
        });

        const ws = XLSX.utils.json_to_sheet(formattedData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "data");

        const date = dateFormatterDash(new Date());
        const filename = `Master Data Reagen Capacity Capacity ${date}.xlsx`;

        XLSX.writeFile(wb, filename);
    }

    const handleSubmit = (apiFunction) => {
        if (!formValue.id_rack) {
            setErrors({ id_rack: 'Rack must be selected.' });
            return;
        }
        if (!formValue.id_floor) {
            setErrors({ id_floor: 'Floor must be selected.' });
            return;
        }
        if (!formValue.id_row) {
            setErrors({ id_row: 'Row must be selected.' });
            return;
        }
        if (!formValue.id_dimension) {
            setErrors({ id_dimension: 'Dimension must be selected.' });
            return;
        }
        if (!formValue.max_capacity) {
            setErrors({ max_capacity: 'Maximum Capacity is required.' });
            return;
        }
        if (formValue.max_capacity > 2147483647) {
            setErrors({ max_capacity: 'Maximum Capacity exceeds the maximum value.' });
            return;
        }
        setErrors({});
        apiFunction();
    };

    return (
        <>
            <div>
                <Head>
                    <title>Capacity</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Quality</Breadcrumb.Item>
                                    <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Reagen Capacity</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                        header={
                            <Stack justifyContent="space-between">
                                <div className="flex gap-2">
                                    <IconButton
                                        icon={<PlusRoundIcon />}
                                        appearance="primary"
                                        onClick={() => {
                                            setShowAddModal(true);
                                        }}>
                                        Add
                                    </IconButton>
                                    <IconButton
                                        icon={<FileDownloadIcon />}
                                        appearance="primary"
                                        onClick={handleExportExcel}
                                    >
                                        Download (.xlsx)
                                    </IconButton>
                                </div>

                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="search"
                                        value={searchKeyword}
                                        onChange={handleSearch}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}>
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                        }
                    >
                        <Table
                            bordered
                            cellBordered
                            height={400}
                            data={getPaginatedData(getFilteredData(), limit, page)}
                            sortColumn={sortColumn}
                            sortType={sortType}
                            onSortColumn={handleSortColumn}
                        >
                            <Column width={70} align="center" fixed>
                                <HeaderCell>No</HeaderCell>
                                <Cell>
                                    {(rowData, rowIndex) => {
                                        return rowIndex + 1 + limit * (page - 1);
                                    }}
                                </Cell>
                            </Column>
                            <Column width={100} align="center" sortable>
                                <HeaderCell>ID Capacity</HeaderCell>
                                <Cell dataKey="id_capacity" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Rack Description</HeaderCell>
                                <Cell dataKey="rack_desc" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Row Description</HeaderCell>
                                <Cell dataKey="row_level_desc" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Floor Description</HeaderCell>
                                <Cell dataKey="floor_level_desc" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Criteria Description</HeaderCell>
                                <Cell dataKey="criteria_desc" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Dimension Description</HeaderCell>
                                <Cell dataKey="dimension_desc" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Max Capacity</HeaderCell>
                                <Cell dataKey="max_capacity" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Created Date</HeaderCell>
                                <Cell dataKey="created_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Created By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        `${rowData.created_by} - ${rowData.created_name}`
                                    )}
                                </Cell>
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Updated Date</HeaderCell>
                                <Cell dataKey="updated_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Updated By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        `${rowData.updated_by} - ${rowData.updated_name}`
                                    )}
                                </Cell>
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Deleted Date</HeaderCell>
                                <Cell dataKey="deleted_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Deleted By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        `${rowData.deleted_by} - ${rowData.deleted_name}`
                                    )}
                                </Cell>
                            </Column>
                            <Column width={100} sortable resizable>
                                <HeaderCell>Status</HeaderCell>
                                <Cell>
                                    {(rowData) => (
                                        <span
                                            style={{
                                                color: rowData.is_active === 1 ? "green" : "red",
                                            }}
                                        >
                                            {rowData.is_active === 1 ? "Active" : "Inactive"}
                                        </span>
                                    )}
                                </Cell>
                            </Column>
                            <Column width={100} fixed="right" align="center">
                                <HeaderCell>Action</HeaderCell>
                                <Cell style={{ padding: "8px" }}>
                                    {(rowData) => (
                                        <div>
                                            <Button
                                                appearance="link"
                                                disabled={rowData.is_active === 0}
                                                onClick={() => {
                                                    setShowEditModal(true);
                                                    setFormValue(rowData);
                                                }}
                                            >
                                                Edit
                                            </Button>
                                            <Button
                                                appearance="subtle"
                                                onClick={() =>
                                                    {
                                                        if (rowData.is_active ===1){
                                                            setShowDeleteModal(true)
                                                            setDeleteFormValue({
                                                                ...deleteFormValue,
                                                                id_capacity: rowData.id_capacity,
                                                                deleted_by: props.employee_id,
                                                                reason:"",
                                                                rack_desc: rowData.rack_desc,
                                                                floor_level_desc: rowData.floor_level_desc,
                                                                row_level_desc: rowData.row_level_desc,
                                                                criteria_desc: rowData.criteria_desc,
                                                                dimension_desc: rowData.dimension_desc,
                                                                max_capacity: rowData.max_capacity
                                                            })
                                                        }else{
                                                            handleEditStatusCapacity(rowData.id_capacity)
                                                        }
                                                    }
                                                }
                                            >
                                                {rowData.is_active === 1 ? (
                                                    <TrashIcon style={{ fontSize: "16px" }} />
                                                ) : (
                                                    <ReloadIcon style={{ fontSize: "16px" }} />
                                                )}
                                            </Button>
                                        </div>
                                    )}
                                </Cell>
                            </Column>
                        </Table>
                        <div style={{ padding: 20 }}>
                            <Pagination
                                prev
                                next
                                first
                                last
                                ellipsis
                                boundaryLinks
                                maxButtons={5}
                                size="xs"
                                layout={["total", "-", "limit", "|", "pager", "skip"]}
                                limitOptions={[10, 30, 50]}
                                total={totalRowCount}
                                limit={limit}
                                activePage={page}
                                onChangePage={setPage}
                                onChangeLimit={handleChangeLimit}
                            />
                        </div>
                    </Panel>
                </div>

                {/* add modal */}
                <Modal
                    backdrop="static"
                    open={showAddModal}
                    onClose={() => {
                        setShowAddModal(false);
                        setFormValue(emptyFormValue);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Add Capacity</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Rack</Form.ControlLabel>
                                <Form.Control
                                    name="id_rack"
                                    value={formValue.id_rack}
                                    accepter={SelectPicker}
                                    block
                                    data={rack}
                                    valueKey="id_rack"
                                    labelKey="rack_desc"
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, id_rack: value })
                                        setErrors((prevErrors) => ({
                                            ...prevErrors,
                                            id_rack: null,
                                        }))
                                    }}
                                />
                                {errors.id_rack && <p style={{ color: 'red' }}>{errors.id_rack}</p>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Floor</Form.ControlLabel>
                                <Form.Control
                                    name="id_floor"
                                    value={formValue.id_floor}
                                    accepter={SelectPicker}
                                    block
                                    data={floor}
                                    valueKey="id_floor"
                                    labelKey="floor_level_desc"
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, id_floor: value })
                                        setErrors((prevErrors) => ({
                                            ...prevErrors,
                                            id_floor: null,
                                        }))
                                    }}
                                />
                                {errors.id_floor && <p style={{ color: 'red' }}>{errors.id_floor}</p>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Row</Form.ControlLabel>
                                <Form.Control
                                    name="id_row"
                                    value={formValue.id_row}
                                    accepter={SelectPicker}
                                    block
                                    data={row}
                                    valueKey="id_row"
                                    labelKey="row_level_desc"
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, id_row: value })
                                        setErrors((prevErrors) => ({
                                            ...prevErrors,
                                            id_row: null,
                                        }))
                                    }}
                                />
                                {errors.id_row && <p style={{ color: 'red' }}>{errors.id_row}</p>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Dimension</Form.ControlLabel>
                                <Form.Control
                                    name="id_dimension"
                                    value={formValue.id_dimension}
                                    accepter={SelectPicker}
                                    block
                                    data={dimension}
                                    valueKey="id_dimension"
                                    labelKey="dimension_desc"
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, id_dimension: value })
                                        setErrors((prevErrors) => ({
                                            ...prevErrors,
                                            id_dimension: null,
                                        }))
                                    }}
                                />
                                {errors.id_dimension && <p style={{ color: 'red' }}>{errors.id_dimension}</p>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Maximum Capacity</Form.ControlLabel>
                                <Form.Control
                                    name="max_capacity"
                                    value={formValue.max_capacity || ''}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, max_capacity: parseInt(value) })
                                        setErrors((prevErrors) => ({
                                            ...prevErrors,
                                            max_capacity: null,
                                        }))
                                    }}
                                />
                                {errors.max_capacity && <p style={{ color: 'red' }}>{errors.max_capacity}</p>}
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowAddModal(false);
                                setFormValue(emptyFormValue);
                                setErrors({});
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleSubmit(handleAddApi);
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Add
                        </Button>
                    </Modal.Footer>
                </Modal>

                {/* Edit Modal */}
                <Modal
                    backdrop="static"
                    open={showEditModal}
                    onClose={() => {
                        setShowEditModal(false);
                        setFormValue(emptyFormValue);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Edit Capacity</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Maximum Capacity</Form.ControlLabel>
                                <Form.Control
                                    name="max_capacity"
                                    value={formValue.max_capacity || ''}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, max_capacity: parseInt(value) })
                                        setErrors((prevErrors) => ({
                                            ...prevErrors,
                                            max_capacity: null,
                                        }))
                                    }}
                                />
                                {errors.max_capacity && <p style={{ color: 'red' }}>{errors.max_capacity}</p>}
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowEditModal(false);
                                setFormValue(emptyFormValue);
                                setErrors({});
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleSubmit(() => handleEditApi(formValue.id_capacity));
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Edit
                        </Button>
                    </Modal.Footer>
                </Modal>


                <Modal
                    backdrop="static"
                    open={showDeleteModal}
                    onClose={() => {
                        setShowDeleteModal(false);
                        setDeleteFormValue({});
                    }}
                    overflow={false}
                    >
                    <Modal.Header>
                        <Modal.Title>Delete Master Capacity {deleteFormValue.rack_desc}</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                    <Form layout="vertical">
                    <Grid fluid>
                        <Row style={{ marginBottom: "16px" }}>
                            <Col style={{ paddingRight: "16px" }}>
                            <Form.Group>
                                <Form.ControlLabel>Floor Desc</Form.ControlLabel>
                                <Form.Control disabled value={deleteFormValue.floor_level_desc} style={{ width: "100%" }} />
                            </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                            <Form.Group>
                                <Form.ControlLabel>Row Desc</Form.ControlLabel>
                                <Form.Control disabled value={deleteFormValue.row_level_desc} style={{ width: "100%" }} />
                            </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                            <Form.Group>
                                <Form.ControlLabel>Max Capacity</Form.ControlLabel>
                                <Form.Control disabled value={deleteFormValue.max_capacity} style={{ width: "100%" }} />
                            </Form.Group>
                            </Col>
                        </Row>
                        <Row style={{ marginBottom: "16px" }}>
                            <Col style={{ paddingRight: "16px" }}>
                            <Form.Group>
                                <Form.ControlLabel>Dimension Desc</Form.ControlLabel>
                                <Form.Control disabled value={deleteFormValue.dimension_desc} style={{ width: "100%" }} />
                            </Form.Group>
                            </Col>
                            <Col style={{ paddingRight: "16px" }}>
                            <Form.Group>
                                <Form.ControlLabel>Criteria Desc</Form.ControlLabel>
                                <Form.Control disabled value={deleteFormValue.criteria_desc} style={{ width: "100%" }} />
                            </Form.Group>
                            </Col>
                        </Row>
                    </Grid>
                    </Form>
                        <Form fluid>
                        <Form.Group>
                            <Form.ControlLabel>Reason</Form.ControlLabel>
                            <Form.Control
                            name="reason"
                            value={deleteFormValue.reason}
                            onChange={(value) => {
                                setDeleteFormValue((prevFormValue) => ({
                                ...prevFormValue,
                                reason: value,
                                }));
                            }}
                            />
                        </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                        onClick={() => {
                            setShowDeleteModal(false);
                            setDeleteFormValue({});
                        }}
                        appearance="subtle"
                        >
                        Cancel
                        </Button>

                        <Button
                        onClick={() => {
                            handleEditStatusCapacity(deleteFormValue.id_capacity);
                        }}
                        appearance="primary"
                        type="submit"
                        >
                        Add
                        </Button>
                    </Modal.Footer>
                </Modal>

            </ContainerLayout>
        </>
    )
}
