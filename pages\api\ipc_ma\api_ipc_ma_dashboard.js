import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_REAGEN}/v2/ipc/dashboard-ma/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function Api_IPC_MA_DASHBOARD() {
  return {
    getIpcMAReportExcel: createApiFunction("post", "get-report-excel"),
    getIpcMADashboard: createApiFunction("get", "get-report-dashboard"),
    getIpcMAReason: createApiFunction("get", "get-report-reason"),
  };
}
