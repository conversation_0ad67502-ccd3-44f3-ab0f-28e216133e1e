import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";

import {
  Button,
  IconButton,
  Stack,
  Breadcrumb,
  Tag,
  Table,
  Panel,
  Input,
  InputGroup,
  Pagination,
  Divider,
  Modal,
  Form,
  Schema,
  useToaster,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";

import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";

import API_TsParameter from "@/pages/api/ts/api_ts-parameter";

export default function ParameterTS() {
  const router = useRouter();
  const { HeaderCell, Cell, Column } = Table;

  const [isLoading, setIsLoading] = useState(true); // Initialize as true
  const [moduleName, setModuleName] = useState("");
  const [parameterTsData, setParameterTsData] = useState([]);

  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [showActivateModal, setShowActivateModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);

  const [selectedParameterTsId, setSelectedParameterTsId] = useState(null);
  const [selectedParameterTsActive, setSelectedParameterTsActive] =
    useState(null);

  const toaster = useToaster();

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [props, setProps] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      const result = await API_TsParameter().getAllParameterTs();
      setParameterTsData(result.data ? result.data : []);
      setIsLoading(false);
      // console.log("result", result);
    };

    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setProps(dataLogin);
    const moduleNameValue = localStorage.getItem("module_name");

    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      fetchData();
    }
  }, []);

  const [formValue, setFormValue] = useState({
    parameter_name: "",
    parameter_uom: "",
    created_by: "",
    update_by: "",
    deleted_by: "",
    is_active: null,
  });

  const requireInputRule = Schema.Types.StringType().isRequired(
    "This field is required."
  );

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id_parameter",
    "parameter_name",
    "parameter_uom",
    "created_at",
    "created_by",
    "updated_at",
    "updated_by",
    "deleted_at",
    "deleted_by",
  ];

  const datas =
    parameterTsData && parameterTsData.length > 0
      ? parameterTsData
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .concat(
                item.is_active === 1
                  ? "Active"
                  : item.is_active === 0
                  ? "Inactive"
                  : null
              )
              .some((val) => val.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const dataSearchParameterTs = parameterTsData
    ? parameterTsData.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];

  const handleDeleteParameterTs = async () => {
    try {
      // Make the delete API call with the selectedParameterTsId
      let result = await API_TsParameter().sfDeleteParameterTs({
        id_parameter: selectedParameterTsId,
        deleted_by: props.employee_id,
      });
      if (result?.status !== 200) {
        throw "Delete TS Parameter failed";
      }

      setFormValue({ parameter_name: null, parameter_uom: null });

      // Fetch the updated TS Parameter list
      const res = await API_TsParameter().getAllParameterTs();
      setParameterTsData(res.data);

      // Close the delete modal
      setShowDeactivateModal(false);

      // Show the toast message
      toaster.push(Messages("success", "Success deactivating TS Parameter!"), {
        placement: "topCenter",
        duration: 5000,
      });
    } catch (error) {
      // console.error("Error deactivating TS Parameter:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleActivateParameterTs = async () => {
    try {
      // Make the activate API call with the selectedParameterTsId
      let result = await API_TsParameter().updateParameterTs({
        ...formValue,
        id_parameter: selectedParameterTsId,
        updated_by: props.employee_id,
        is_active: 1,
      });
      if (result?.status !== 200) {
        throw "Activate TS Parameter failed";
      }

      setFormValue({ parameter_name: null, parameter_uom: null });

      // Fetch the updated TS Parameter list
      const res = await API_TsParameter().getAllParameterTs();
      setParameterTsData(res.data);

      // Close the activate modal
      setShowActivateModal(false);

      // Show the toast message
      toaster.push(Messages("success", "Success activating TS Parameter!"), {
        placement: "topCenter",
        duration: 5000,
      });
    } catch (error) {
      // console.error("Error activating TS Parameter:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleAddParameterTs = async () => {
    // console.log("Add TS Parameter with data:", formValue);
    try {
      // Make the add API call with the formValue
      let result = await API_TsParameter().addParameterTs({
        ...formValue,
        created_by: props.employee_id,
        is_active: 1,
      });

      if (result?.status !== 200) {
        throw "Add TS Parameter failed";
      }

      // Fetch the updated TS Parameter list
      const res = await API_TsParameter().getAllParameterTs();
      setParameterTsData(res.data);

      // Close the add modal
      setShowAddModal(false);

      // Show the toast message
      toaster.push(Messages("success", "Success adding a new TS Parameter!"), {
        placement: "topCenter",
        duration: 5000,
      });

      // Reset the form value
      setFormValue({
        parameter_name: null,
        parameter_uom: null,
      });
    } catch (error) {
      // console.error("Error adding TS Parameter:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleUpdateParameterTs = async () => {
    try {
      // Make the update API call with the selectedParameterTsId and formValue
      let result = await API_TsParameter().updateParameterTs({
        ...formValue,
        id_parameter: selectedParameterTsId,
        updated_by: props.employee_id,
        is_active: selectedParameterTsActive,
      });
      if (result?.status !== 200) {
        throw "Update TS Parameter failed";
      }

      // Fetch the updated TS Parameter list
      const res = await API_TsParameter().getAllParameterTs();
      setParameterTsData(res.data);

      // Close the update modal
      setShowUpdateModal(false);

      // Show the toast message
      toaster.push(Messages("success", "Success updating TS Parameter!"), {
        placement: "topCenter",
        duration: 5000,
      });

      // Reset the form value
      setFormValue({
        parameter_name: null,
        parameter_uom: null,
      });
    } catch (error) {
      // console.error("Error updating TS Parameter:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Master Data TS Parameter</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Research and Development</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>TS Parameter</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <IconButton
                  icon={<PlusIcon />}
                  appearance="primary"
                  onClick={() => {
                    setShowAddModal(true);
                  }}
                >
                  Add
                </IconButton>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              data={datas}
              bordered
              cellBordered
              height={400}
              onRowClick={(rowData) => {
                // console.log(rowData);
              }}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>

              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id_parameter" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Parameter Name</HeaderCell>
                <Cell dataKey="parameter_name" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Parameter UOM</HeaderCell>
                <Cell dataKey="parameter_uom" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created At</HeaderCell>
                <Cell dataKey="created_at" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell dataKey="created_by" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated At</HeaderCell>
                <Cell dataKey="updated_at" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Deleted At</HeaderCell>
                <Cell dataKey="deleted_at" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Deleted By</HeaderCell>
                <Cell dataKey="deleted_by" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Is Active?</HeaderCell>
                <Cell dataKey="is_active">
                  {(rowData) => (
                    <span>
                      {rowData.is_active === 1 ? (
                        <div className="flex items-center">
                          <div className="h-2.5 w-2.5 rounded-full bg-green-500 mr-2"></div>{" "}
                          Active
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <div className="h-2.5 w-2.5 rounded-full bg-red-500 mr-2"></div>{" "}
                          Inactive
                        </div>
                      )}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={150} fixed="right">
                <HeaderCell>Action</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span>
                      <a
                        className="cursor-pointer"
                        onClick={() => {
                          setSelectedParameterTsId(rowData.id_parameter);
                          setSelectedParameterTsActive(rowData.is_active);
                          setFormValue({
                            parameter_name: rowData.parameter_name,
                            parameter_uom: rowData.parameter_uom,
                          });
                          setShowUpdateModal(true);
                        }}
                      >
                        Edit
                      </a>
                      <Divider vertical />
                      {rowData.is_active === 1 ? (
                        <a
                          onClick={() => {
                            setSelectedParameterTsId(rowData.id_parameter);
                            setFormValue({
                              parameter_name: rowData.parameter_name,
                              parameter_uom: rowData.parameter_uom,
                            });
                            setShowDeactivateModal(true);
                          }}
                          className="cursor-pointer text-red-500 hover:text-red-500"
                        >
                          Deactivate
                        </a>
                      ) : (
                        <a
                          onClick={() => {
                            setSelectedParameterTsId(rowData.id_parameter);
                            setFormValue({
                              parameter_name: rowData.parameter_name,
                              parameter_uom: rowData.parameter_uom,
                            });
                            setShowActivateModal(true);
                          }}
                          className="cursor-pointer text-green-700 hover:text-green-700"
                        >
                          Activate
                        </a>
                      )}
                    </span>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                total={
                  searchKeyword
                    ? dataSearchParameterTs.length
                    : parameterTsData.length
                }
                limitOptions={[10, 30, 50]}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Deactivate Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showDeactivateModal}
          onClose={() => {
            setShowDeactivateModal(false);
            setFormValue({ parameter_name: null, parameter_uom: null });
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Deactivate TS Parameter</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>
              Are you sure you want to deactivate this TS Parameter? <br />
              Parameter Name: <b>{formValue.parameter_name}</b> <br />
              Parameter UOM: <b>{formValue.parameter_uom}</b>
            </p>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowDeactivateModal(false);
                setFormValue({ parameter_name: null, parameter_uom: null });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteParameterTs}
              color="red"
              appearance="primary"
            >
              Deactivate
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Activate Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showActivateModal}
          onClose={() => {
            setShowActivateModal(false);
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Activate TS Parameter</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>
              Are you sure you want to activate this TS Parameter? <br />
              Parameter Name: <b>{formValue.parameter_name}</b> <br />
              Parameter UOM: <b>{formValue.parameter_uom}</b>
            </p>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowActivateModal(false);
                setFormValue({ parameter_name: null, parameter_uom: null });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button onClick={handleActivateParameterTs} appearance="primary">
              Activate
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Add Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue({ parameter_name: null, parameter_uom: null });
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Add TS Parameter</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.Group>
                  <Form.ControlLabel>Parameter Name</Form.ControlLabel>
                  <Form.Control
                    name="parameter_name"
                    value={formValue.parameter_name}
                    onChange={(value) => {
                      setFormValue({ ...formValue, parameter_name: value });
                    }}
                    rule={requireInputRule}
                    required
                  />
                </Form.Group>
                <Form.ControlLabel>Parameter UOM</Form.ControlLabel>
                <Form.Control
                  name="parameter_uom"
                  value={formValue.parameter_uom}
                  onChange={(value) => {
                    setFormValue({ ...formValue, parameter_uom: value });
                  }}
                  rule={requireInputRule}
                  required
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowAddModal(false);
                setFormValue({ parameter_name: null, parameter_uom: null });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (
                  formValue.parameter_name === null ||
                  formValue.parameter_name === ""
                ) {
                  toaster.push(
                    Messages("warning", "Parameter Name cannot be empty"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                } else if (
                  formValue.parameter_uom === null ||
                  formValue.parameter_uom === ""
                ) {
                  toaster.push(
                    Messages("warning", "Parameter UOM cannot be empty"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                }
                handleAddParameterTs();
                setShowAddModal(false);
                setFormValue({ parameter_name: null, parameter_uom: null });
                // console.log("Add TS Parameter with data:", formValue);
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Edit Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showUpdateModal}
          onClose={() => {
            setShowUpdateModal(false);
            setFormValue({ parameter_name: null, parameter_uom: null });
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Edit TS Parameter</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Parameter Name</Form.ControlLabel>
                <Form.Control
                  name="parameter_name"
                  value={formValue.parameter_name}
                  onChange={(value) => {
                    setFormValue({ ...formValue, parameter_name: value });
                  }}
                  rule={requireInputRule}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Parameter UOM</Form.ControlLabel>
                <Form.Control
                  name="parameter_uom"
                  value={formValue.parameter_uom}
                  onChange={(value) => {
                    setFormValue({ ...formValue, parameter_uom: value });
                  }}
                  rule={requireInputRule}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowUpdateModal(false);
                setFormValue({ parameter_name: null, parameter_uom: null });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (
                  formValue.parameter_name === null ||
                  formValue.parameter_name === ""
                ) {
                  toaster.push(
                    Messages("warning", "TS Parameter Name cannot be empty"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                } else if (
                  formValue.parameter_uom === null ||
                  formValue.parameter_uom === ""
                ) {
                  toaster.push(
                    Messages("warning", "TS Parameter Code cannot be empty"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                }
                handleUpdateParameterTs();

                setShowUpdateModal(false);
                setFormValue({ parameter_name: null, parameter_uom: null });
                // console.log("Update TS Parameter with data:", formValue);
              }}
              appearance="primary"
              type="submit"
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>

      {/* {isLoading ? (
        <Loader
          center
          size="md"
          content="Loading..."
          backdrop
          speed="normal"
          vertical
        />
      ) : (
        <div></div>
      )} */}
    </>
  );
}
