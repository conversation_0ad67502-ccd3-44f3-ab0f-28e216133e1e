import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  useToaster,
  SelectPicker,
  DatePicker,
  Loader,
  Tooltip,
   Whisper ,

} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import { useRouter } from "next/router";
import API_OutboundReagen from "@/pages/api/outbound_reagen/api_outbound_reagen";

export default function ListOutboundReagenPage() {
  const [moduleName, setModuleName] = useState("");
  const { HeaderCell, Cell, Column } = Table;
  const [outbound, setOutbound] = useState([]);
  const [detail, setDetail] = useState([]);
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const [props, setProps] = useState([]);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showDetailPopModal, setShowDetailPopModal] = useState(false);
  const emptyFormValue = {
    id_criteria_reagen: null,
    id_inbound_reagen_locator: null,
    rack_desc: null,
    floor_level_desc: null,
    row_level_desc: null,
    amount: null,
  };
  const [formValue, setFormValue] = useState(emptyFormValue);
  const toaster = useToaster();
  const router = useRouter();
  const [inbound, setInbound] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [mapping, setMapping] = useState([]);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [overInput, setOverInput] = useState(0);
  const [reagenName, setReagenName] = useState(null)

  

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const handleGetAllApi = async () => {
    const res = await API_OutboundReagen().getAllOutbound();
    console.log(res);
    setOutbound(res.data ? res.data : []);
  };

  const handleGetAllInboundApi = async () => {
    const res = await API_OutboundReagen().getAllInbound();
    console.log(res);
    setInbound(res.data ? res.data : []);
  };

  const filteredData = outbound
    .filter((rowData, i) => {
    const searchFields = [
      "id_outbound_reagen",
      "reagen_name",
      "amount",
      "status",
      "created_dt",
      "created_by",
      "created_name",
      "updated_dt",
      "updated_by",
      "updated_name",
      "deleted_dt",
      "deleted_by",
      "deleted_name",
      "is_active",
    ];

    const matchesSearch = searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : outbound.length;

  const handleAddApi = async () => {
    try {
      const result = await API_OutboundReagen().postOutbound({
        ...formValue,
        created_by: props.employee_id,
        created_name: props.employee_id + " - " + props.employee_name,
      });
      console.log("first", result)

      setDetail(result.data ? result.data : []);
      setOverInput(result.input_message ? result.input_message : "");

      if (result.status === 200) {
        handleGetAllApi();
        handleGetAllInboundApi();
        setShowAddModal(false);
        setShowDetailPopModal(true);

        toaster.push(
          Messages("success", "Success adding Inbound Reagen!"), 
        {
          placement: "topCenter",
          duration: 5000,
        });
        setFormValue(emptyFormValue);
      } else if (result.status === 400) {
        toaster.push(
          Messages("error", `Error: "${result.message}". Please try again later!`),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
        setShowAddModal(false);
      }
    } catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);
      toaster.push(
        Messages("error", `Error: Please try again later!`), 
      {
        placement: "topCenter",
        duration: 5000,
      }
    );
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("outbound_reagen/list_outbound_reagen")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetAllApi();
      handleGetAllInboundApi();
    }
  }, []);

  useEffect(() => {
    console.log("detail", detail)
  }, [detail])

  const handleGetDetailApi = async (selectedIdOutbound) => {
    const result = await API_OutboundReagen().getOutboundLocatorById({
      id_outbound_reagen: selectedIdOutbound,
    });
    setDetail(result.data);
  };

  const handleExportExcel = () => {
    if (outbound.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }

    const headerMapping = {
      id_outbound_reagen: "ID Outbound Reagen",
      id_criteria_reagen: "ID Criteria Reagen",
      amount: "Amount",
      status: "Realization Status",
      created_dt: "Created Date",
      created_by: "Created By",
      created_name: "Created Name",
      updated_dt: "Updated Date",
      updated_by: "Updated By",
      updated_name: "Updated Name",
      deleted_dt: "Deleted Date",
      deleted_by: "Deleted By",
      deleted_name: "Deleted Name",
      is_active: "Status",
    };

    const formattedData = outbound.map((item) => {
      const formattedItem = {};
      for (const key in item) {
        if (headerMapping[key]) {
          if (key === "is_active") {
            formattedItem[headerMapping[key]] =
              item[key] === 1 ? "Active" : "Inactive";
          } else if (key === "status") {
            formattedItem[headerMapping[key]] =
              item[key] === 0 ? "Not Realized" : "Realized";
          } else {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
      }
      return formattedItem;
    });

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const filename = `Outbound Reagen ${date}.xlsx`;

    XLSX.writeFile(wb, filename);
  };

  const handleSubmit = async (apiFunction) => {
    setLoading(true);
    try {
      if (!formValue.id_criteria_reagen) {
        setErrors({ id_criteria_reagen: "Criteria Reagen must be selected." });
        return;
      }
      if (!formValue.amount) {
        setErrors({ amount: "Amount is required." });
        return;
      }
      if (formValue.amount > 2147483647) {
        setErrors({ amount: "Amount exceeds the maximum value." });
        return;
      }
      setErrors({});
      await apiFunction();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Outbound Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Outbound Reagen</Breadcrumb.Item>
                  <Breadcrumb.Item active>List Outbound Reagen</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusRoundIcon />}
                    appearance="primary"
                    onClick={() => {
                      setShowAddModal(true);
                    }}
                  >
                    Add
                  </IconButton>
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={150} align="center" sortable>
                <HeaderCell>ID Outbound Reagen</HeaderCell>
                <Cell dataKey="id_outbound_reagen" />
              </Column>

              <Column width={150} align="center" sortable>
                <HeaderCell>Reagen Name</HeaderCell>
                <Cell dataKey="reagen_name" />
              </Column>


              <Column width={150} sortable resizable>
                <HeaderCell>Realization Status</HeaderCell>
                <Cell>
                {(rowData) => (
                    <Whisper 
                      followCursor 
                      speaker={<Tooltip>{rowData.messages === "" ? `You Have Requested for ${rowData.requested_amount}` : rowData.messages}</Tooltip>}
                    >
                      <Button>
                        <span
                          style={{
                            color: rowData.amount - rowData.locator_count === 0 ? "green" : "red",
                          }}
                        >
                          {`${rowData.locator_count} of ${rowData.amount} is Realized`}
                        </span>
                      </Button>
                    </Whisper>
                  )}

                </Cell>
              </Column>
              <Column width={150} sortable resizable align="center">
                <HeaderCell>Amount</HeaderCell>
                <Cell dataKey="amount" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Created Date</HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>
              <Column width={250} sortable resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell>
                  {(rowData) =>
                    `${rowData.created_by} - ${rowData.created_name}`
                  }
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Updated Date</HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>
              <Column width={250} sortable resizable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell>
                  {(rowData) =>
                    `${rowData.updated_by} - ${rowData.updated_name}`
                  }
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Deleted Date</HeaderCell>
                <Cell dataKey="deleted_dt" />
              </Column>
              <Column width={250} sortable resizable>
                <HeaderCell>Deleted By</HeaderCell>
                <Cell>
                  {(rowData) =>
                    `${rowData.deleted_by} - ${rowData.deleted_name}`
                  }
                </Cell>
              </Column>
              <Column width={100} sortable resizable>
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={130} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell style={{ padding: "8px" }}>
                  {(rowData) => (
                    <div>
                      <Button
                        appearance="link"
                        onClick={() => {
                          setShowDetailModal(true);
                          setOverInput(rowData.messages)
                          setReagenName(rowData.reagen_name)
                          setDetail([])
                          handleGetDetailApi(rowData.id_outbound_reagen);
                        }}
                      >
                        Detail
                      </Button>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Detail Modal */}
        <Modal
          backdrop="static"
          open={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Outbound Reagen Detail</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>{reagenName}</p>
            <p>{overInput}</p>            
            {detail.map((item) => (
              <div key={item.id_capacity}>
                <Panel bordered className="mb-3">
                  <p className="mb-2">
                    <span className="fw-bold">
                      ID Inbound Reagen Locator :{" "}
                    </span>
                    {item.id_inbound_reagen_locator}
                  </p>
                  <p className="mb-2">
                    <span className="fw-bold">Rack : </span>
                    {item.rack_desc}
                  </p>
                  <p className="mb-2">
                    <span className="fw-bold">Floor Level : </span>
                    {item.floor_level_desc}
                  </p>
                  <p className="mb-2">
                    <span className="fw-bold">Row Level : </span>
                    {item.row_level_desc}
                  </p>
                  <p className="mb-2">
                    <span className="fw-bold">Amount : </span>
                    {item.amount}
                  </p>
                  <p className="mb-2">
                    <span className="fw-bold">Realization Status : </span>
                    <span
                      style={{ color: item.status === 0 ? "red" : "green" }}
                    >
                      {item.status === 0 ? "Not Realized" : "Realized"}
                    </span>
                  </p>
                </Panel>
              </div>
            ))}
          </Modal.Body>
        </Modal>

        {/* Add Modal */}
        <Modal
          backdrop="static"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Add Outbound Reagen</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Criteria Reagen</Form.ControlLabel>
                <SelectPicker
                  name="id_criteria_reagen"
                  value={formValue.id_criteria_reagen}
                  block
                  data={inbound}
                  valueKey="id_criteria_reagen"
                  labelKey="reagen_name"
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_criteria_reagen: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      id_criteria_reagen: null,
                    }));
                  }}
                />
                {errors.id_criteria_reagen && (
                  <p style={{ color: "red" }}>{errors.id_criteria_reagen}</p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Amount</Form.ControlLabel>
                <Form.Control
                  name="amount"
                  value={formValue.amount || ""}
                  onChange={(value) => {
                    setFormValue({ ...formValue, amount: parseInt(value) });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      amount: null,
                    }));
                  }}
                />
                {errors.amount && (
                  <p style={{ color: "red" }}>{errors.amount}</p>
                )}
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowAddModal(false);
                setFormValue(emptyFormValue);
                setErrors({});
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleSubmit(handleAddApi);
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
            {loading && (
              <Loader
                backdrop
                size="md"
                vertical
                content="Adding Data..."
                active={loading}
              />
            )}
          </Modal.Footer>
        </Modal>

       {/* Detail Modal */}
       <Modal
          backdrop="static"
          open={showDetailPopModal}
          onClose={() => {
            setShowDetailPopModal(false);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Outbound Reagen Detail</Modal.Title>
          </Modal.Header>
          <Modal.Body>
           <p>{overInput}</p>
            {detail.map((item) => (
              <div key={item.id_capacity}>
                <Panel bordered className="mb-3">
                  <p className="mb-2">
                    <span className="fw-bold">
                      ID Inbound Reagen Locator :{" "}
                    </span>
                    {item.id_inbound_reagen_locator}
                  </p>
                  <p className="mb-2">
                    <span className="fw-bold">Rack : </span>
                    {item.rack_desc}
                  </p>
                  <p className="mb-2">
                    <span className="fw-bold">Floor Level : </span>
                    {item.floor_level_desc}
                  </p>
                  <p className="mb-2">
                    <span className="fw-bold">Row Level : </span>
                    {item.row_level_desc}
                  </p>
                </Panel>
              </div>
            ))}
          </Modal.Body>
        </Modal>
      </ContainerLayout>
    </>
  );
}
