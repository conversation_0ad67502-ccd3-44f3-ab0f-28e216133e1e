import Header from "@/components/layout/Header";
import MainContent from "@/components/layout/MainContent";
import { useRouter } from "next/router";
import UseTestApi from "../api/userApi";
import { useEffect, useState } from "react";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import styles from "@/components/LoginForm.module.css";
import Swal from "sweetalert2";

export default function ChangePassword({ dataUser }) {
  const router = useRouter();
  const [employeeId, setEmployeeId] = useState("");
  const [employeeName, setEmployeeName] = useState("");
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [oldPassword, setOldPassword] = useState("");
  const [oldPasswordErr, setOldPasswordErr] = useState(false);
  const [newPassword1, setNewPassword1] = useState("");
  const [newPassword2, setNewPassword2] = useState("");
  const [isPasswordMatched, setIsPasswordMatched] = useState(true);
  const [isPasswordValid, setIsPasswordValid] = useState(true);
  const MySwal = withReactContent(Swal);

  const { UpdateFirstLogin } = UseTestApi();

  // useEffect(() => {
  //   const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
  //   if (!dataLogin) {
  //     router.push("/");
  //     return;
  //   }
  // }, []);

  useEffect(() => {
    if (dataUser.length > 0) {
      setEmployeeName(dataUser[0].Name ? dataUser[0].Name : "");
      setEmployeeId(dataUser[0].Employee_Id ? dataUser[0].Employee_Id : "" );
    }
    
  }, [dataUser]);

  useEffect(() => {
    if (newPassword1 !== newPassword2) {
      setIsPasswordMatched(false);
    } else {
      setIsPasswordMatched(true);
    }
  }, [newPassword1, newPassword2]);

  // Mengecek validitas password
  useEffect(() => {
    const passwordToCheck = newPassword1;
    var minimumChars = 6;
    var regularExpression =
      /^(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{6,16}$/;

    if (
      passwordToCheck.length < minimumChars ||
      !regularExpression.test(passwordToCheck)
    ) {
      setIsPasswordValid(false);
      return;
    }
    setIsPasswordValid(true);
  }, [newPassword1]);

  // Handler
  const submitHandler = async (event) => {
    event.preventDefault();
    setIsFormDisabled(true);

     

    const userData = {
      employee_id: employeeId,
      old_password: oldPassword,
      new_password: newPassword1,
    };

    // Same old password
    if (userData.old_password === userData.new_password) {
      setIsFormDisabled(false);
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "New Password can't be the same as Old Password !",
      });
      return;
    }
    

    const updatePassword = await UpdateFirstLogin(userData);
    const { Data, Token, FirstLogin } = updatePassword;
    const { error } = updatePassword;
    if (Data) {
      setIsFormDisabled(true);
      const dataLoginUser = {
        employee_id: Data[0].Employee_Id,
        employee_name: Data[0].Name,
        email: Data[0].Email,
        menu_link_code: Data[0].Menu_Link_Code,
        module_code: Data[0].Module_Code,
        department: Data[0].Id_Department,
        token: Token,
      };

      
        // localStorage.setItem("dataLoginUser", JSON.stringify(dataLoginUser));        
        MySwal.fire({
          allowOutsideClick: false,
          position: "center",
          icon: "success",
          title: "Change Password Success !",
          text: "Please Try Login Again",
          showConfirmButton: false,
          timer: 2500,
        });
        router.push("/");
        return;
    }
    if (error) {
      MySwal.fire({
        icon: "error",
        title: "Login Failed !",
        text: error,
      });
      setIsFormDisabled(false);
      return;
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Change Password</title>
        </Head>
      </div>
      <div className="container-fluid d-flex align-items-center justify-content-center h-100 mb-5">
      <div className={`card mt-5 p-5 shadow-lg ${styles.customCardWidth}`}>
      <div className="text-center card-title">
      <div title="Change Password">
        <div>
          <h4>Form Ganti Password</h4>
          <div className="p-5">
            <form onSubmit={submitHandler}>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Employee ID
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  id="employeeId"
                  value={employeeId}
                  aria-describedby="validationServer03Feedback"
                  disabled={true}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="employee_name" className="form-label">
                  Employee Name
                </label>
                <input
                  type="text"
                  className="form-control mb-5"
                  id="employee_name"
                  aria-describedby="emailHelp"
                  value={employeeName}
                  disabled={true}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="old_password" className="form-label">
                  Old Password
                </label>
                <input
                  type="password"
                  className="form-control"
                  id="old_password"
                  aria-describedby="emailHelp"
                  value={oldPassword}
                  onChange={(event) => setOldPassword(event.target.value)}
                  required
                />
                {oldPasswordErr && (
                  <div
                    id="validationServer03Feedback"
                    className="invalid-feedback"
                  >
                    Old Password is WRONG !
                  </div>
                )}
              </div>
              <div className="mb-3">
                <label htmlFor="new_password1" className="form-label">
                  New Password
                </label>
                <input
                  type="password"
                  className={`form-control ${
                    !isPasswordMatched || !isPasswordValid ? "is-invalid" : ""
                  }`}
                  id="new_password1"
                  aria-describedby="emailHelp"
                  value={newPassword1}
                  onChange={(event) => setNewPassword1(event.target.value)}
                  required
                />
              </div>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Retype New Password
                </label>
                <input
                  type="password"
                  className={`form-control ${
                    !isPasswordMatched || !isPasswordValid ? "is-invalid" : ""
                  }`}
                  id="new_password2"
                  value={newPassword2}
                  onChange={(event) => setNewPassword2(event.target.value)}
                  aria-describedby="validationServer03Feedback"
                  required
                />
                <div>
                  Password requirements :
                  <ul>
                    <li>6 characters minimum</li>
                    <li>
                      Contain minimum 1 uppercase character, 1 lowercase
                      character, number, and symbol (!@#$%^&*)
                    </li>
                  </ul>
                </div>
                {/* {!isPasswordMatched && (
                  <div
                    id="validationServer03Feedback"
                    className="invalid-feedback"
                  >
                    Password does not match.
                  </div>
                )} */}
              </div>

              {/* Button */}
              <button
                disabled={
                  isFormDisabled || !isPasswordMatched || !isPasswordValid
                }
                type="submit"
                className="btn btn-primary p-2"
              >
                Change Password
              </button>
            </form>
          </div>
        </div>
      </div>
      </div>
      </div>
    </div>
    </>
  );
}

export async function getServerSideProps({ query }) {
  const { data: emp_id } = query;
  const { GetByIdTestApi } = UseTestApi();

  const { Data: dataUserResult } = await GetByIdTestApi(emp_id);
  let dataUser = [];
  if (dataUserResult != undefined) {
    dataUser = dataUserResult;
  }

  return {
    props: {
      dataUser,
    },
  };
}
