import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/master_lkv_form_template${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function LkvMasterLkvFormTemplate(){
    return{
        getAllMasterLkvFormTemplate: createApiFunction("get", "/get/all"),
        getAllMasterLkvFormTemplateActive : createApiFunction("get", "/get/all/active"),
        createMasterLkvFormTemplate: createApiFunction("post", "/create"),
        updateMasterLkvFormTemplate: createApiFunction("put", "/edit"),
        updateStatusMasterLkvFormTemplate: createApiFunction("put", "/active")
    }
}