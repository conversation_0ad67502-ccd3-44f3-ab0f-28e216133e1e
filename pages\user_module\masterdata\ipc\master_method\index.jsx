import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  Loader,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiIPCMethod from "@/pages/api/ipc/api_ipc_method";
import { useRouter } from "next/router";

export default function MasterMethod() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_method");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [IPCMetohdDataState, setIPCMetohdDataState] = useState([]);

  const emptyAddIPCMethodForm = {
    method_name: null,
  };

  const emptyEditIPCMethodForm = {
    method_name: null,
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [addIPCMethodForm, setAddIPCMethodForm] = useState(emptyAddIPCMethodForm);
  const [editIPCMethodForm, setEditIPCMethodForm] = useState(emptyEditIPCMethodForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = IPCMetohdDataState.filter((rowData, i) => {
    const searchFields = ["id_method", "method_name", "create_date", "create_by", "update_date", "update_by", "delete_date", "delete_by", "is_active"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : IPCMetohdDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("ipc/")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandlegetAllIPCMethodApi();
    }
  }, []);

  const HandlegetAllIPCMethodApi = async () => {
    try {
      const res = await ApiIPCMethod().getAllIPCMethod();

      console.log("res", res);
      if (res.status === 200) {
        setIPCMetohdDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };

  const HandleAddIPCMethodApi = async () => {
    const errors = {};

    if (!addIPCMethodForm.method_name) {
      errors.method_name = "IPC Method name is required";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }
    setLoading(true);
    try {
      const res = await ApiIPCMethod().createIPCMethod({
        ...addIPCMethodForm,
        create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        setAddIPCMethodForm(emptyAddIPCMethodForm);
        setShowAddModal(false);
        HandlegetAllIPCMethodApi();
      } else {
        console.log("error on AddIPCMethodApi ", res.message);
      }
    } catch (error) {
      console.log("error on AddIPCMethodApi ", error);
    } finally {
      setLoading(false);
    }
  };

  const HandleEditIPCMethodApi = async () => {
    const errors = {};

    if (!editIPCMethodForm.method_name) {
      errors.method_name = "IPC Method name is required";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);
      return;
    }
    try {
      const res = await ApiIPCMethod().editIPCMethod({
        ...editIPCMethodForm,
        update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        HandlegetAllIPCMethodApi();
        setShowEditModal(false);
      } else {
        console.log("error on editIPCMethodApi ", res.message);
      }
    } catch (error) {
      console.log("error on editIPCMethodApi ", res.message);
    }
  };

  const handleEditStatusIPCMethodApi = async (id_method, is_active) => {
    try {
      const res = await ApiIPCMethod().editStatusIPCMethod({
        id_method,
        is_active,
        delete_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        console.log("Status update success:", res.message);
        HandlegetAllIPCMethodApi();
      } else {
        console.log("Error on update status: ", res.message);
      }
    } catch (error) {
      console.log("Error on update status: ", error.message);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>IPC Method</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>IPC Method</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>Halaman IPC Method</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Halaman IPC Method</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >

              <Table
                bordered
                cellBordered
                height={400}
                data={getPaginatedData(getFilteredData(), limit, page)}
                sortColumn={sortColumn}
                sortType={sortType}
                onSortColumn={handleSortColumn}
                autoHeight
              >
                <Column width={120} align="center" sortable fullText>
                  <HeaderCell>ID IPC Method</HeaderCell>
                  <Cell dataKey="id_method" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Nama IPC Method</HeaderCell>
                  <Cell dataKey="method_name" />
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Pembuatan</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dibuat oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.delete_by}</>}</Cell>
                </Column>
                <Column width={100} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModal(true);
                            setEditIPCMethodForm({
                              ...editIPCMethodForm,
                              method_name: rowData.method_name,
                              start_date: rowData.start_date,
                              end_date: rowData.end_date,
                              id_method: rowData.id_method,
                              update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                            });
                          }}
                        >
                          <EditIcon />
                        </Button>
                        <Button appearance="subtle" onClick={() => handleEditStatusIPCMethodApi(rowData.id_method, rowData.is_active)}>
                          {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>

            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                setShowAddModal(false);
                setAddIPCMethodForm(emptyAddIPCMethodForm);
                setErrorsAddForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Tambah IPC Method</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama IPC Method</Form.ControlLabel>
                    <Form.Control
                      name="method_name"
                      value={addIPCMethodForm.method_name}
                      onChange={(value) => {
                        setAddIPCMethodForm((prevFormValue) => ({
                          ...prevFormValue,
                          method_name: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          method_name: undefined,
                        }));
                      }}
                    />
                    {errorsAddForm.method_name && <p style={{ color: "red" }}>{errorsAddForm.method_name}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddIPCMethodForm(emptyAddIPCMethodForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleAddIPCMethodApi();
                  }}
                  appearance="primary"
                  type="submit"
                  loading={loading} // Menampilkan loading state pada tombol
                  disabled={loading} // Menonaktifkan tombol saat loading
                >
                  Tambah
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Adding Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditIPCMethodForm(emptyEditIPCMethodForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Ubah IPC Method</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama IPC Method</Form.ControlLabel>
                    <Form.Control
                      name="method_name"
                      value={editIPCMethodForm.method_name}
                      onChange={(value) => {
                        setEditIPCMethodForm((prevFormValue) => ({
                          ...prevFormValue,
                          method_name: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          method_name: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.method_name && <p style={{ color: "red" }}>{errorsEditForm.method_name}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditIPCMethodForm(emptyEditIPCMethodForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleEditIPCMethodApi();
                  }}
                  appearance="primary"
                  type="submit"
                >
                  Ubah
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Editing Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
