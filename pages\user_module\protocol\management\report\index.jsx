import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import { DateRangePicker, Stack, Panel, Button, Breadcrumb, Calendar, Whisper, Popover, Badge  } from "rsuite";

//import API_IPC from "@/pages/api/api_ipc";
import API_Protocol from "@/pages/api/protocolApi";
import XLSX from "xlsx";

import ContainerLayout from "@/components/layout/ContainerLayout";

import ChartDataLabels from "chartjs-plugin-datalabels";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
  ArcElement,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
  ChartDataLabels,
  ArcElement
);

import { Pie } from "react-chartjs-2";
import { faMasksTheater } from "@fortawesome/free-solid-svg-icons";

export default function ReportingMsTms() {
  const router = useRouter();
  const [moduleName, setModuleName] = useState("");
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [dataCalendar, setDataCalendar] = useState([])
 

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");
    setModuleName(moduleNameValue);
    

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("protocol/management/report")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
    }

    handleFetchData(dataLogin.department);
    handleFetchCalendar(dataLogin.department);
  }, []);

  const handleFetchData = async (dpt) => {
    setLoading(true);
    const body = {
      department:dpt
    }
    const data = await API_Protocol().GetReportDashboard(body);
    setData(data.data || []);
    setLoading(false);
  };

  const handleFetchCalendar = async (dpt) =>{
    const body = {
      department:dpt
    }

    const data = await API_Protocol().GetCalendar(body);
    setDataCalendar(data.data || []);
  }

  const prepareChartData = (data) => {
    const labels = [];
    const Done_analisa_value = data.Done_analisa;
    const Inprogess_approval_value = data.Inprogess_approval;
    const Ready_analisa_value = data.Ready_analisa;
    const dataArray = [];
    const color = []

    // cek apakah value ada
    if (Done_analisa_value != 0) {
        dataArray.push(Done_analisa_value); 
        color.push('#0f7141');
        labels.push('Analisa Selesai')
    }
   if (Inprogess_approval_value != 0) {
        dataArray.push(Inprogess_approval_value);
        color.push('#ffd60a');
        labels.push('Analisa Sedang berlangsung') 
    }
    if (Ready_analisa_value != 0) {
        dataArray.push(Ready_analisa_value); 
        color.push('#d32f2f'); 
        labels.push('Siap di Analisa')
    }
    

    return {
        labels: labels,
          datasets: [{
            label: 'Dashboard',
            data: dataArray,
            backgroundColor: color,
            hoverOffset: 4
          }]
    };
  };

  const PieChart = ({ chartData }) => {
    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: "bottom",
        },
        title: {
          display: true,
          text: "Report Dashboard",
        },
        datalabels: {
          anchor: 'center', // Label anchor position
          align: 'center', // Label alignment
          offset: 0, // Label offset from the center
          color: 'rgba(255, 255, 255, 0.8)', // Set a darker text color with some transparency

          font: {
            weight: "bold",
          },
        //   formatter: (value, context) => {
        //     const formattedValue = value.toLocaleString();
        //     return formattedValue;
        //   },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return <Pie data={chartData} options={options} />;
  };

  //get data for calendar
  //check date day by day
  function getTodoList(date) {
    const formattedDate = new Date(date).toISOString().split('T')[0];

    //return array of matchingProtocol on effective date
    const matchingProtocol = dataCalendar.find(item => {
        const effectiveDate = new Date(item.Effective_date).toISOString().split('T')[0];
        return effectiveDate == formattedDate;
    });
    
    if (matchingProtocol) {        
        return matchingProtocol.Protocol_details
    }else{
      return []
    }
    
  }

  //render the cell of date
  function renderCell(date) {
      const list = getTodoList(date);
      const displayList = list.filter((item, index) => index < 2);

      if (list.length) {
        const moreCount = list.length - displayList.length;
        let moreText = `See ${moreCount} More`
        if (moreCount <1){
          moreText = `See More`
        }
        const moreItem = (
          <li>
            <Whisper
              placement="top"
              trigger="click"
              speaker={
                <Popover>
                  {list.map((item, index) => (
                    <p key={index}>
                      <b>{item.No_protocol}</b> ({item.Cycle_month}) - {item.Timeframe_code} - {item.Status_cycle}
                    </p>
                  ))}
                </Popover>
              }
            >
              <a><Badge content={moreText} style={{marginTop:10}} color="green"></Badge></a>
            </Whisper>
          </li>
        );

        return (
          <ul className="calendar-todo-list" style={{paddingLeft:0, fontSize:12}}>
            {displayList.map((item, index) => (
              <li key={index} style={{    overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",}}>
                <Badge color={item.Color} />{item.No_protocol}
              </li>
            ))}
             
             {/* {moreCount ? moreItem : null} */}
             {moreItem}
            
            
          </ul>
        );
      }

      return null;
    }

  return (
    <>
      <div>
        <Head>
          <title>Reporting Protocol</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item active>
              {moduleName ? moduleName : "User Module"} / Reporting Protocol
            </Breadcrumb.Item>
          </Breadcrumb>
          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h4>Reporting Protocol</h4>
            </Stack>
          </Panel>
          <div>
            <Panel bordered>
              <Stack spacing={6}>
                <div style={{ width: '100%', margin: 'auto', textAlign: 'center' }}>
                  <PieChart
                    chartData={prepareChartData(data)}
                    plugins={[ChartDataLabels]}
                    loading={loading}
                    width="100%"  
                    height="50%" 
                  />
                </div>
                <div style={{ width: '100%', margin: 'auto', textAlign: 'center' }}>
                  <div style={{ width: '100%', height: "50%", margin: 'auto' }}>
                    <Calendar bordered renderCell={renderCell} />
                  </div>
                </div>
              </Stack>
            </Panel>
          </div>
        </div>
    </ContainerLayout>
    </>
  );
}
