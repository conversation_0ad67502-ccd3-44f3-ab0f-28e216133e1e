import { useEffect, useState } from "react";
import Head from "next/head";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "rsuite";
import { useRouter } from "next/router";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import ApiLineClearanceGranulasi from "@/pages/api/pqr/line_clearance/granule/api_line_clearance_granulasi";
import LineClearPdfComponent from "@/components/pqr/LineClearPdfComponent";

pdfMake.vfs = pdfFonts.pdfMake.vfs;

export default function LineClearPdf() {
  const router = useRouter();
  const { id_clearance } = router.query;
  const [idRouter, setIdRouter] = useState(null);
  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [LineClearanceData, setLineClearanceData] = useState(null);

  const HandleGetLineClearByID = async (id_clearance) => {
    try {
      const res = await await ApiLineClearanceGranulasi().getLineClearByID({
        id_clearance: parseInt(id_clearance),
      });
      if (res.status === 200) {
        console.log("res", res.data);
        setLineClearanceData(res.data);
      } else {
        console.log("Error on Get Line Clear by ID Api ", res.message);
      }
    } catch (error) {
      console.log("Error on catch Get Line Clear by ID Api", error);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else if (id_clearance) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/line_clearance")
      );
      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      setIdRouter(id_clearance);
      HandleGetLineClearByID(id_clearance);
    }
  }, [router, id_clearance]);

  return (
    <div
      style={{
        width: "100%",
        padding: "1em",
        backgroundColor: "#2c2c30",
        position: "sticky",
        top: 0,
      }}>
      <Head>
        <title>Line Clearance - Granulasi Report </title>
      </Head>
      <Stack justifyContent="space-between">
        <p style={{ color: "white", fontSize: "1em" }}>
          Print Out Line Clearance - Granulasi Report
        </p>
        <Stack>
          <LineClearPdfComponent
            LineClearanceData={LineClearanceData}
            sessionAuth={sessionAuth}
          />
        </Stack>
      </Stack>
    </div>
  );
}
