import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, Notification, Loader, SelectPicker, DatePicker } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import ApiMachineStripping from "@/pages/api/oee/oee_machine_param/api_oee_machine_stripping";
import ApiMachineOeeHeaderStripping from "@/pages/api/oee/machine_oee_stripping/api_machine_oee_stripping";
import { useRouter } from "next/router";

export default function MachineStrippingPage() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const toaster = useToaster();
  const router = useRouter();

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [machineStrippingDataState, setMachineStrippingDataState] = useState([]);
  const [machineStrippingHeaderDataState, setMachineStrippingHeaderDataState] = useState([]);
  const [machineStrippingHeaderDataMachineIdState, setMachineStrippingHeaderDataMachineIdState] = useState([]);
  const [startDate, setStartDate] = useState(null);

  const emptyMachineStrippingForm = {
    id_machine_oee: null,
    category_type: "",
    machine_name: "",
    start_time: "",
    end_time: "",
    duration: "",
    created_by: "",
  };

  const [addLoading, setAddLoading] = useState(false);
  const [loading, setLoading] = useState(false);

  const [errorsAddForm, setErrorsAddForm] = useState({});

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const [categoryTypeOptions] = useState([
    { label: "Planned (P)", value: "P" },
    { label: "Unplanned (U)", value: "U" },
  ]);

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = machineStrippingDataState.filter((rowData) => {
    const searchFields = ["machine_name", "start_date", "end_date", "duration"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    // Parse the start_date from the rowData
    const [day, month, yearTime] = rowData.start_date.split("-");
    const [year, time] = yearTime.split(" ");
    const parsedStartDate = new Date(`${year}-${month}-${day}T${time}:00`);

    const matchesDateFilter = startDate ? parsedStartDate.toDateString() === startDate.toDateString() : true;

    return matchesSearch && matchesDateFilter;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword || startDate ? filteredData.length : machineStrippingDataState.length;
  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("oee/creation/list-stripping"));
      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandleGetAllApiMachineStrippingApi();
      HandleGetAllHeaderStrippingApi();
    }
  }, []);

  const HandleGetAllApiMachineStrippingApi = async () => {
    setLoading(true);
    try {
      const res = await ApiMachineStripping().GetAllOeeMachineStripping();

      if (res.status === 200) {
        const machineData = res.data.machine_data || [];

        setMachineStrippingDataState(machineData);
      } else {
        console.log("Error on Get All Api:", res.message);
        setMachineStrippingDataState([]);
      }
    } catch (error) {
      console.error("Error on catch Get All Api:", error);
      setMachineStrippingDataState([]);
    } finally {
      setLoading(false);
    }
  };

  const HandleGetAllHeaderStrippingApi = async () => {
    try {
      const res = await ApiMachineOeeHeaderStripping().GetAllMachineOeeListStripping();

      console.log("res", res);
      if (res.status === 200) {
        const machineIds = res.data.machine_ids;
        console.log(machineIds);
        setMachineStrippingHeaderDataMachineIdState(res.data.machine_ids);
        setMachineStrippingHeaderDataState(res.data.machine_ids);
      } else {
        console.log("error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Api", error);
    }
  };

  const HandleAddHeaderStrippingApi = async (newMachineStrippingForm) => {
    const errors = {};

    // Jika ada kesalahan, set dalam state
    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }
    try {
      setAddLoading(true);
      const res = await ApiMachineOeeHeaderStripping().CreateMachineOeeListStripping({
        ...newMachineStrippingForm,
        created_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        showNotification("success", "Added successfully");
        const newIdOeeHeader = res.id;
        router.push(`/user_module/oee/creation/list-stripping/detail?Id=${newIdOeeHeader}`);
      } else if (res.status === 400) {
        showNotification("error", "created failed, id machine oee already exists");
      } else {
        showNotification("error", "Failed to add data");
      }
    } catch (error) {
      showNotification("error", "Error adding data");
    } finally {
      setAddLoading(false);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>HM1 Stripping</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>OEE</Breadcrumb.Item>
                  <Breadcrumb.Item>Creation</Breadcrumb.Item>
                  <Breadcrumb.Item>List</Breadcrumb.Item>
                  <Breadcrumb.Item active>HM1 Stripping</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>HM1 Stripping</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <DatePicker
                    selected={startDate}
                    onChange={(date) => {
                      setStartDate(date);
                      setPage(1);
                    }}
                    placeholder="pilih tanggal mulai"
                  />
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              {loading ? (
                <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                  <Loader size="sm" content="Loading..." />
                </div>
              ) : (
                <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                  <Column width={70} align="center" fixed>
                    <HeaderCell>No</HeaderCell>
                    <Cell>
                      {(rowData, rowIndex) => {
                        return rowIndex + 1 + limit * (page - 1);
                      }}
                    </Cell>
                  </Column>

                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Durasi (Menit)</HeaderCell>
                    <Cell dataKey="duration" />
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Waktu Mulai</HeaderCell>
                    <Cell dataKey="start_date" />
                  </Column>
                  <Column width={200} sortable fullText>
                    <HeaderCell align="center">Waktu Berakhir</HeaderCell>
                    <Cell dataKey="end_date" />
                  </Column>
                  <Column width={100} sortable fullText>
                    <HeaderCell align="center">Input Data</HeaderCell>
                    <Cell>{(rowData) => <div style={{ textAlign: "center" }}>{machineStrippingHeaderDataState[rowData.machine_id] ? "Y" : "N"}</div>}</Cell>
                  </Column>
                  <Column width={125} align="center">
                    <HeaderCell>Aksi</HeaderCell>
                    <Cell style={{ padding: "8px" }}>
                      {(rowData) => (
                        <div>
                          <Button
                            appearance="link"
                            disabled={rowData.is_active === 0}
                            onClick={async () => {
                              const existingHeader = machineStrippingHeaderDataState[rowData.machine_id];

                              if (existingHeader) {
                                // Jika Input Data bernilai "Y", arahkan ke URL edit
                                router.push(`/user_module/oee/creation/list-stripping/detail/edit?Id=${existingHeader}`);
                              } else {
                                // Jika Input Data bernilai "N", proses seperti sebelumnya
                                const parseDateTime = (dateString) => {
                                  const [date, time] = dateString.split(" ");
                                  const [day, month, year] = date.split("-");
                                  const [hours, minutes] = time.split(":");

                                  return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}T${hours.padStart(2, "0")}:${minutes.padStart(2, "0")}:00Z`;
                                };

                                const newMachineStrippingForm = {
                                  machine_name: rowData.machine_name,
                                  start_time: parseDateTime(rowData.start_date),
                                  end_time: parseDateTime(rowData.end_date),
                                  duration: rowData.duration,
                                  id_machine_oee: rowData.machine_id,
                                };

                                // Panggil fungsi untuk menambahkan header
                                await HandleAddHeaderStrippingApi(newMachineStrippingForm);
                              }
                            }}
                          >
                            Input Breakdown
                          </Button>
                        </div>
                      )}
                    </Cell>
                  </Column>
                </Table>
              )}

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
