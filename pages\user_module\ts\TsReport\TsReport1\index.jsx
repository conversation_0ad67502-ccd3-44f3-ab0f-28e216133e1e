import React, { useState, useEffect } from "react";
import {
  Table,
  Button,
  Modal,
  Divider,
  Pagination,
  Input,
  InputGroup,
  Stack,
  Form,
  useToaster
} from "rsuite";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";

import ApiTsReport from "@/pages/api/ts/api_ts-report";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import { useRouter } from 'next/router';

const { Column, HeaderCell, Cell } = Table;

export default function TsReportAutomateLine(props) {
  const router = useRouter()
  const [data, setData] = useState([]);
  const [dataFromRow, setDataFromRow] = useState({}); // data from row
  const [showDetail, setShowDetail] = useState(false);
 
  const [showAddKeyword, setShowAddKeyword] = useState(false);
  const [keywordForm, setKeywordForm] = useState([])
  const [keyWord, setKeyword] = useState('')

  const [showSearchKeyword, setShowSearchKeyword] = useState(false);
  const [searchKeywordForm, setSearchKeywordForm] = useState([])
  const [searchKeyWordValue, setSearchKeywordValue] = useState('')

  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [historyData, setHistoryData] = useState({
    spv_approved_dt: null,
    spv_employee_id: null,
    spv_name: null,
    mgr_approved_dt: null,
    mgr_employee_id: null,
    mgr_name: null
  });

  const [showLinkModal, setShowLinkModal] = useState(false);
  const [linkData, setLinkData] = useState({
    "id_header_trans" :null,
    "links" :null,
  })

  const [detailQuery, setDetailQuery] = useState([])

  const toaster = useToaster();

  const fetchData = async () => {
    const result = await ApiTsReport().getReport1();
    setData(result.data || []);

    console.log(result);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "ts_code",
    "id_header_trans",
    "id_line",
    "sediaan_type",
    "product_code",
    "product_name",
    "batch_no",
    "binder_date",
    "binder_mix_amount",
    "binder_mix_time",
    "binder_remarks",
    "granule_date",
    "granule_ampere",
    "granule_power",
    "granule_remarks",
    "drying_date",
    "drying_lod",
    "drying_product_temp",
    "drying_exhaust_temp",
    "drying_time",
    "drying_remarks",
    "sifting_date",
    "sifting_screen_quadro",
    "sifting_bin_tumbler",
    "sifting_impeller_speed_1",
    "sifting_impeller_speed_2",
    "sifting_remarks",
    "final_mix_date",
    "final_mix_time_mix_1",
    "final_mix_time_mix_2",
    "ts_conclusion",
    "ts_followup",
    "ts_created_at",
    "ts_created_by",
    "ts_keyword",
    "ts_print_amount",
  ];

  const datas =
    data && data.length > 0
      ? data
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .some((val) => val.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const dataSearch = data?.filter((item) => {
    const str = searchKeyword.toLowerCase();
    return propertiesToFilter
      .map((property) => item[property] || "") // Use empty string as a default for undefined values
      .some((val) => val.toString().toLowerCase().includes(str));
  });

  console.log("datafromrow", dataFromRow)

  // function to export data based on selected row, header and detail (2 sheets (header and detail) in 1 excel file))
  async function exportData(rowData) {
    const header = rowData;
    const detail = rowData.detail_trans;

    if (!detail || detail.length === 0) {
      alert("No detail data to export");
      return;
    }

    const dataDetail = await ApiTsReport().getReportDetail1({
      id_header_trans: rowData.id_header_trans
    })

    let resultDetail = []

    if (dataDetail.status == 200) {
      console.log("data detail", dataDetail)
      resultDetail = dataDetail.data ||[]
    }

    const headerData = [header];
    const detailData = detail;

    const headerWs = XLSX.utils.json_to_sheet(headerData);
    const detailWs = XLSX.utils.json_to_sheet(detailData);
    const detailResultWs = XLSX.utils.json_to_sheet(resultDetail);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, headerWs, "Header");
    XLSX.utils.book_append_sheet(wb, detailWs, "Detail");
    XLSX.utils.book_append_sheet(wb, detailResultWs, "Detail Result");

    const id_header_trans = header.id_header_trans;
    const date = new Date();
    const YYMMDD = `${date.getFullYear().toString().slice(-2)}${(
      date.getMonth() + 1
    )
      .toString()
      .padStart(2, "0")}${date.getDate().toString().padStart(2, "0")}`;

    XLSX.writeFile(
      wb,
      `ts-report-automate-line-${YYMMDD}-${id_header_trans}.xlsx`
    );

    await ApiTsReport().increasePrintAmount({
      id_header_trans: id_header_trans,
    });

    // refresh data
    const result = await ApiTsReport().getReport1();
    setData(result.data || []);
  }


  //show detail keywords
  const getDetailKeyword = async (id_reference) =>{
      const result = await ApiTsReport().getDetailKeywordAuto({
        id_reference: id_reference,
      })
      setKeywordForm(result.data ? result.data :[])
  }

  //submit keyword
  const submitKeyword = async (id_reference) =>{
      if (keywordForm.length < 1){
        toaster.push(
          Messages("warning", "Keyword Must be inputed"),
          {
            placement: "topEnd",
            duration: 3000,
          }
        );
        return
      }
      const result = await ApiTsReport().postDetailKeywordAuto({
        id_reference: id_reference,
        keywords: keywordForm,
        created_by: props.employee_id,
      })
      
      if (result.status == 200){
        Messages("success", "Keyword has been inputed"),
        {
          placement: "topEnd",
          duration: 3000,
        }
        setShowAddKeyword(false);
      }else{
        console.log("Submit Keyword error ", result.message)
        Messages("warning", "Error when submit keyword"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      }
  }

  const submitLink = async (id_reference) =>{
    if (linkData.links.length < 1){
      toaster.push(
        Messages("warning", "Link Must be inputed"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      return
    }
    const result = await ApiTsReport().postDetailLinkAuto({
      ...linkData
    })
    
    if (result.status == 200){
      Messages("success", "Link has been inputed"),
      {
        placement: "topEnd",
        duration: 3000,
      }
      setShowLinkModal(false);
      fetchData()
    }else{
      console.log("Submit Link error ", result.message)
      Messages("warning", "Error when submit keyword"),
      {
        placement: "topEnd",
        duration: 3000,
      }
    }
}

  //search ts by keyword
  const submitSearchKeyword = async (id_reference) =>{
    if (searchKeywordForm.length < 1){
      toaster.push(
        Messages("warning", "Search Keyword Must be inputed"),
        {
          placement: "topEnd",
          duration: 3000,
        }
      );
      return
    }
    const result = await ApiTsReport().getReportByKeywordAuto({
      keywords: searchKeywordForm,
    })
    
    if (result.status == 200){
      Messages("success", "Search Keyword has been inputed"),
      {
        placement: "topEnd",
        duration: 3000,
      }
      setData(result.data || []);
      setShowAddKeyword(false);
      setSearchKeywordValue("")
      setSearchKeywordForm([])
    }else{
      console.log("Search Keyword error ", result.message)
      Messages("warning", "Error when submit keyword"),
      {
        placement: "topEnd",
        duration: 3000,
      }
    }
  }



  const detailApi = async ()=>{
    const res = await ApiTsReport().getReportDetail1({
      id_header_trans: dataFromRow.id_header_trans
    })

    let resultDetail = []

    if (res.status == 200) {
      console.log("data detail", res)
      resultDetail = res.data ||[]
      setDetailQuery(resultDetail)
    }
  }
  useEffect(()=>{
    
    if (showDetail) {
      detailApi()
    }
  },[showDetail])

  return (
    <>
      <Stack justifyContent="flex-end" direction="row" className="mb-4">
        <InputGroup inside>
          <InputGroup.Addon>
            <SearchIcon />
          </InputGroup.Addon>
          <Input
            placeholder="Search"
            value={searchKeyword}
            onChange={handleSearch}
          />
          <InputGroup.Addon
            onClick={() => {
              setSearchKeyword("");
              setPage(1);
            }}
            style={{
              display: searchKeyword ? "block" : "none",
              color: "red",
              cursor: "pointer",
            }}
          >
            <CloseOutlineIcon />
          </InputGroup.Addon>
        </InputGroup>
        <Button
          className="ml-1"
          appearance="primary"
          onClick={() => {
            setShowSearchKeyword(true)
          }}
        >
          Search by Keyword
        </Button>
      </Stack>

      <Table
        bordered
        cellBordered
        rowKey="id_header_trans"
        height={500}
        data={datas}
        sortColumn={sortColumn}
        sortType={sortType}
        onSortColumn={handleSortColumn}
      >
        <Column resizable sortable>
          <HeaderCell>Ts Code</HeaderCell>
          <Cell dataKey="ts_code" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>ID Line</HeaderCell>
          <Cell dataKey="id_line" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sediaan Type</HeaderCell>
          <Cell dataKey="sediaan_type" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Product Code</HeaderCell>
          <Cell dataKey="product_code" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Product Name</HeaderCell>
          <Cell dataKey="product_name" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Batch No</HeaderCell>
          <Cell dataKey="batch_no" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Binder Date</HeaderCell>
          <Cell dataKey="binder_date" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Binder Mix Amount</HeaderCell>
          <Cell dataKey="binder_mix_amount" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Binder Mix Time</HeaderCell>
          <Cell dataKey="binder_mix_time" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Binder Remarks</HeaderCell>
          <Cell dataKey="binder_remarks" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Granule Date</HeaderCell>
          <Cell dataKey="granule_date" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Granule Ampere</HeaderCell>
          <Cell dataKey="granule_ampere" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Granule Power</HeaderCell>
          <Cell dataKey="granule_power" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Granule Remarks</HeaderCell>
          <Cell dataKey="granule_remarks" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying Date</HeaderCell>
          <Cell dataKey="drying_date" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying LOD</HeaderCell>
          <Cell dataKey="drying_lod" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying Product Temp</HeaderCell>
          <Cell dataKey="drying_product_temp" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying Exhaust Temp</HeaderCell>
          <Cell dataKey="drying_exhaust_temp" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying Time</HeaderCell>
          <Cell dataKey="drying_time" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Drying Remarks</HeaderCell>
          <Cell dataKey="drying_remarks" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Date</HeaderCell>
          <Cell dataKey="sifting_date" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Screen Quadro</HeaderCell>
          <Cell dataKey="sifting_screen_quadro" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Bin Tumbler</HeaderCell>
          <Cell dataKey="sifting_bin_tumbler" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Impeller Speed 1</HeaderCell>
          <Cell dataKey="sifting_impeller_speed_1" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Impeller Speed 2</HeaderCell>
          <Cell dataKey="sifting_impeller_speed_2" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Sifting Remarks</HeaderCell>
          <Cell dataKey="sifting_remarks" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Final Mix Date</HeaderCell>
          <Cell dataKey="final_mix_date" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Final Mix Time Mix 1</HeaderCell>
          <Cell dataKey="final_mix_time_mix_1" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>Final Mix Time Mix 2</HeaderCell>
          <Cell dataKey="final_mix_time_mix_2" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>TS Conclusion</HeaderCell>
          <Cell dataKey="ts_conclusion" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>TS Follow Up</HeaderCell>
          <Cell dataKey="ts_followup" />
        </Column>

        <Column resizable sortable>
          <HeaderCell>TS Created At</HeaderCell>
          <Cell dataKey="ts_created_at" />
        </Column>

        <Column resizable width={200} sortable>
          <HeaderCell>TS Created By</HeaderCell>
          <Cell dataKey="ts_created_by" />
        </Column>

        <Column resizable width={200} sortable>
          <HeaderCell>TS Keyword</HeaderCell>
          <Cell dataKey="ts_keyword" />
        </Column>

        <Column sortable>
          <HeaderCell>TS Print Amount</HeaderCell>
          <Cell dataKey="ts_print_amount" />
        </Column>

        <Column sortable resizable>
          <HeaderCell>Link Remarks</HeaderCell>
          <Cell dataKey="link_remarks" />
        </Column>

        <Column width={180} fixed="right"table>
          <HeaderCell>Status Approval</HeaderCell>
          <Cell dataKey="status_approval_text" />
        </Column>

        <Column align="center" width={600} fixed="right">
          <HeaderCell>...</HeaderCell>
          <Cell style={{ padding: "2px" }}>
            {(rowData) => (
              <div>
                <Button
                  appearance="link"
                  onClick={() => {
                    setShowDetail(true, setDataFromRow(rowData));
                  }}
                >
                  View Detail
                </Button>
                <Divider vertical />
                <Button
                  appearance="link"
                  onClick={() => {
                    exportData(rowData);
                  }}
                >
                  Download
                </Button>
                <Divider vertical />
                <Button
                  appearance="link"
                  onClick={() => {
                    //console.log("rowdata", rowData)
                    setShowAddKeyword(true, setDataFromRow(rowData), getDetailKeyword(rowData.id_header_trans))
                  }}
                >
                  Add Keyword
                </Button>
                <Divider vertical />
                <Button
                  appearance="link"
                  onClick={() => {
                    //console.log("rowdata", rowData)
                    setShowHistoryModal(true)
                    setHistoryData({
                      ...historyData,
                      spv_approved_dt: rowData.spv_approved_dt,
                      spv_employee_id: rowData.spv_employee_id,
                      mgr_approved_dt: rowData.mgr_approved_dt,
                      mgr_employee_id: rowData.mgr_employee_id,
                      spv_name: rowData.spv_name,
                      mgr_name: rowData.mgr_name
                    })
                  }}
                >
                  History
                </Button>
                <Divider vertical />
                <Button
                  appearance="link"
                  onClick={() => {
                    //console.log("rowdata", rowData)
                    setShowLinkModal(true)
                    setLinkData({
                      ...linkData,
                      id_header_trans: rowData.id_header_trans,
                      links: rowData.link_remarks || ""
                    })
                  }}
                >
                  Add Links
                </Button>
              </div>
            )}
          </Cell>
        </Column>
      </Table>
      <div style={{ padding: 20 }}>
        <Pagination
          prev
          next
          first
          last
          ellipsis
          boundaryLinks
          maxButtons={5}
          size="xs"
          layout={["total", "-", "limit", "|", "pager", "skip"]}
          total={
            dataSearch && dataSearch.length > 0
              ? dataSearch.length
              : data.length
          }
          pages={Math.ceil(data.length / limit)}
          activePage={page}
          limitOptions={[10, 20, 30, 40, 50]}
          limit={limit}
          onSelect={(page) => setPage(page)}
          onChangeLimit={handleChangeLimit}
          onChangePage={handleChangeLimit}
        />
      </div>

      <Modal
        backdrop="static"
        keyboard={false}
        open={showDetail}
        onClose={() => setShowDetail(false)}
      >
        <Modal.Header>
          <Modal.Title>Detail & Attachment</Modal.Title>
        </Modal.Header>
        <Modal.Body>
        <div >            
            <div className="border-1 p-2 my-1 rounded-md">
            <strong>Header</strong>
            <div className="flex justify-between gap-4 w-full">
              <div className="flex flex-col gap-3 w-full">
                  <div>
                    <label>Ts Code: </label>
                    <Input
                    as="textarea"
                      readOnly
                      value={dataFromRow.ts_code}
                      maxLength={50}
                    />
                  </div>
                
                  <div>
                    <label>line_description:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.line_description}
                    maxLength={50}
                    />
                  </div>
                  <div>
                      <label>product_code:</label>
                      <Input
                      as="textarea"
                      readOnly
                      value={dataFromRow.product_code}
                      maxLength={50}
                      />
                  </div>
                  <div>
                    <label>batch_no:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.batch_no}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>production_scale:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.production_scale}
                    maxLength={50}
                    />
                </div>
                
                <div>
                    <label>process_purpose:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.process_purpose}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>background:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.background}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>ts_created_at:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.ts_created_at}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>Discussion:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.discussion}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>ts_followup:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.ts_followup}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>ts_keyword:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.ts_keyword}
                    maxLength={50}
                    />
                </div>
              </div>

              <div className="flex flex-col gap-3 w-full">
                <div>
                    <label>id_line: </label>
                    <Input
                    as="textarea"
                      readOnly
                      value={dataFromRow.id_line}
                      maxLength={50}
                    />
                </div>
                <div>
                    <label>sediaan_type:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.sediaan_type}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>product_name:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.product_name}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>trial_focus:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.trial_focus}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>ppi_no:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.ppi_no}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>process_date:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.process_date}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>ts_conclusion:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.ts_conclusion}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>ts_created_by:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.ts_created_by}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>analyzed data:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.analyzed_data}
                    maxLength={50}
                    />
                </div>
                <div>
                  <label>ts_print_amount:</label>
                  <Input
                  as="textarea"
                  readOnly
                  value={dataFromRow.ts_print_amount}
                  maxLength={50}
                  />
              </div>
                
              </div>
            </div>            
            </div>

            <div className="border-1 p-2 my-1 rounded-md">
            <strong>Binder</strong>
            <div className="flex justify-between gap-4 w-full">
              <div className="flex flex-col gap-3 w-full">
                  <div>
                      <label>binder_date:</label>
                      <Input
                      as="textarea"
                      readOnly
                      value={dataFromRow.binder_date}
                      maxLength={50}
                      />
                  </div>
                
                  <div>
                    <label>binder_mix_amount:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.binder_mix_amount}
                    maxLength={50}
                    />
                  </div>
                  
                  
              </div>

              <div className="flex flex-col gap-3 w-full">
                <div>
                    <label>binder_mix_time:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.binder_mix_time}
                    maxLength={50}
                    />
                </div>

                <div>
                    <label>binder_remarks:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.binder_remarks}
                    maxLength={50}
                    />
                </div>              
              </div>
            </div>            
            </div>

            <div className="border-1 p-2 my-1 rounded-md">
            <strong>Granule</strong>
            <div className="flex justify-between gap-4 w-full">
              <div className="flex flex-col gap-3 w-full">
                <div>
                    <label>granule_date:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.granule_date}
                    maxLength={50}
                    />
                </div>

                <div>
                    <label>granule_ampere:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.granule_ampere}
                    maxLength={50}
                    />
                </div>    
              </div>

              <div className="flex flex-col gap-3 w-full">
                <div>
                    <label>granule_power:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.granule_power}
                    maxLength={50}
                    />
                </div>

                <div>
                    <label>granule_remarks:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.granule_remarks}
                    maxLength={50}
                    />
                </div>           
              </div>
            </div>            
            </div>

            <div className="border-1 p-2 my-1 rounded-md">
            <strong>Drying</strong>
            <div className="flex justify-between gap-4 w-full">
              <div className="flex flex-col gap-3 w-full">
                <div>
                    <label>drying_date:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.drying_date}
                    maxLength={50}
                    />
                </div>

                <div>
                    <label>drying_lod:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.drying_lod}
                    maxLength={50}
                    />
                </div>

                <div>
                    <label>drying_time:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.drying_time}
                    maxLength={50}
                    />
                </div>   
              </div>

              <div className="flex flex-col gap-3 w-full">
                <div>
                    <label>drying_product_temp:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.drying_product_temp}
                    maxLength={50}
                    />
                </div>

                <div>
                    <label>drying_exhaust_temp:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.drying_exhaust_temp}
                    maxLength={50}
                    />
                </div>

                <div>
                    <label>drying_remarks:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.drying_remarks}
                    maxLength={50}
                    />
                </div>          
              </div>
            </div>            
            </div>

            <div className="border-1 p-2 my-1 rounded-md">
            <strong>Sifting</strong>
            <div className="flex justify-between gap-4 w-full">
              <div className="flex flex-col gap-3 w-full">
                  <div>
                      <label>sifting_date:</label>
                      <Input
                      as="textarea"
                      readOnly
                      value={dataFromRow.sifting_date}
                      maxLength={50}
                      />
                  </div>

                  <div>
                      <label>sifting_screen_quadro:</label>
                      <Input
                      as="textarea"
                      readOnly
                      value={dataFromRow.sifting_screen_quadro}
                      maxLength={50}
                      />
                  </div>

                  <div>
                    <label>sifting_bin_tumbler:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.sifting_bin_tumbler}
                    maxLength={50}
                    />
                </div>  
              </div>

              <div className="flex flex-col gap-3 w-full">
              <div>
                  <label>sifting_impeller_speed_1:</label>
                  <Input
                  as="textarea"
                  readOnly
                  value={dataFromRow.sifting_impeller_speed_1}
                  maxLength={50}
                  />
              </div>

              <div>
                  <label>sifting_impeller_speed_2:</label>
                  <Input
                  as="textarea"
                  readOnly
                  value={dataFromRow.sifting_impeller_speed_2}
                  maxLength={50}
                  />
              </div>

              <div>
                  <label>sifting_remarks:</label>
                  <Input
                  as="textarea"
                  readOnly
                  value={dataFromRow.sifting_remarks}
                  maxLength={50}
                  />
              </div>          
              </div>
            </div>            
            </div>

            <div className="border-1 p-2 my-1 rounded-md">
            <strong>Final Mix Time</strong>
            <div className="flex justify-between gap-4 w-full">
              <div className="flex flex-col gap-3 w-full">
                <div>
                    <label>final_mix_time_mix_1:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.final_mix_time_mix_1}
                    maxLength={50}
                    />
                </div>
                <div>
                  <label>final_mix_date:</label>
                  <Input
                  as="textarea"
                  readOnly
                  value={dataFromRow.final_mix_date}
                  maxLength={50}
                  />
              </div>
              </div>

              <div className="flex flex-col gap-3 w-full">
                <div>
                    <label>final_mix_time_mix_2:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.final_mix_time_mix_2}
                    maxLength={50}
                    />
                </div>         
              </div>
            </div>            
            </div>

            <div className="border-1 p-2 my-1 rounded-md">
            <strong>Rendemen</strong>
            <div className="flex justify-between gap-4 w-full">
              <div className="flex flex-col gap-3 w-full">
                <div>
                    <label>bobot_granul:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.bobot_granul}
                    maxLength={50}
                    />
                </div>
                <div>
                    <label>rendemen:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.rendemen}
                    maxLength={50}
                    />
                </div>
              </div>

              <div className="flex flex-col gap-3 w-full">
                <div>
                    <label>bobot_teoritis:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.bobot_teoritis}
                    maxLength={50}
                    />
                </div>      
              </div>
            </div>            
            </div>

            <div className="border-1 p-2 my-1 rounded-md">
            <strong>Approval</strong>
            <div className="flex justify-between gap-4 w-full">
              <div className="flex flex-col gap-3 w-full">
                <div>
                    <label>spv_employee_id:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.spv_employee_id}
                    maxLength={50}
                    />
                </div>

                <div>
                    <label>mgr_employee_id:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.mgr_employee_id}
                    maxLength={50}
                    />
                </div>

              </div>

              <div className="flex flex-col gap-3 w-full">                  
                <div>
                    <label>spv_approved_dt:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.spv_approved_dt}
                    maxLength={50}
                    />
                </div>

                <div>
                    <label>mgr_approved_dt:</label>
                    <Input
                    as="textarea"
                    readOnly
                    value={dataFromRow.mgr_approved_dt}
                    maxLength={50}
                    />
                </div>
    
              </div>
            </div>            
            </div>
          </div>
          <Divider />
          <div className="mb-4">
            <strong>Detail Ts</strong>
            {detailQuery &&
              detailQuery.map((detail, index) => (
                <div key={index} className="border-1 p-2 my-1 rounded-md">
                  <p>Type : {detail?.type.toUpperCase()}</p>
                  <p>Parameter : {detail.parameter}</p>
                  <p>Description : {detail.description}</p>
                  <p>Value : {detail.value}</p>
                </div>
              ))}
          </div>
          
          <Divider />
          <div className="mb-4">
            <strong>Detail</strong>
            {dataFromRow &&
              dataFromRow.detail_trans &&
              dataFromRow.detail_trans.map((detail, index) => (
                <div key={index} className="border-1 p-2 my-1 rounded-md">
                  <p>Type : {detail?.type.toUpperCase()}</p>
                  <p>Parameter : {detail.parameter}</p>
                  <p>Description : {detail.description}</p>
                  <p>Value : {detail.value}</p>
                </div>
              ))}
          </div>
          <Divider />
          <div>
            <strong>Attachments</strong>
            {dataFromRow &&
              dataFromRow.attachments &&
              dataFromRow.attachments.map((attach, index) => (
                <div key={index} className="border-1 p-2 my-1 rounded-md">
                  <p>Value : {attach.id_attachments}</p>
                  <p>Attachment : {attach.type}</p>
                  {/* <p>Image : {`${process.env.NEXT_PUBLIC_STORAGE}/${attach.path}`}</p> */}
                  {/* redirect url img ts*/}
                  <img className="object-contain" src={`${process.env.NEXT_PUBLIC_STORAGE}/${attach.path}`} alt={attach.type} />
                </div>
              ))}
          </div>
        </Modal.Body>
        <Modal.Footer>
          {dataFromRow.status_approval != 1 &&
          <Button onClick={() => 
            router.push({
              pathname:
                  '/user_module/ts/editTs/automate',
              query: {data : dataFromRow.id_header_trans},
            })
          } appearance="primary">
            Edit
          </Button>
          }
          <Button onClick={() => setShowDetail(false)} appearance="primary">
            Ok
          </Button>        
        </Modal.Footer>
      </Modal>

      {/* Add keyword Modal */}
      <Modal
          backdrop="static"
          role="alertdialog"
          open={showAddKeyword}
          onClose={() => {
            setShowAddKeyword(false);
            setKeywordForm([]);
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Add Keyword</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.Group>
                  <Form.ControlLabel>Add Keyword for {dataFromRow.id_header_trans}</Form.ControlLabel>
                  <Form.Control
                    name="keyword"
                    //value={formValue.main_category_name}
                    onChange={(value) => {
                        setKeyword(value)
                    }}
                    required
                  />
                </Form.Group>
                {keywordForm.map((item, index) => (
                  <div className="flex items-center ">
                  <li key={index}>{item}</li>
                  <Button
                      appearance="link"
                      color="red"
                      onClick={() => {
                        const newArray = keywordForm.filter((itemFrom, indexFrom) => indexFrom !== index);
                        setKeywordForm(newArray);
                      }}
                    >
                     x
                  </Button>
                  </div>
                ))}
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowAddKeyword(false);
                setKeywordForm([])
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (
                  keyWord === null ||
                  keyWord === ""
                ) {
                  toaster.push(
                    Messages("warning", "Keyword Must be inputed"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                }
                setKeywordForm([...keywordForm, keyWord]);
              }}
              appearance="primary"
            >
              Add
            </Button>
            <Button
              onClick={() => {
                // setKeywordForm([]) 
                submitKeyword(dataFromRow.id_header_trans)
              }
              }
              appearance="primary"
              type="submit"
            >
              Submit
            </Button>
          </Modal.Footer>
        </Modal>


      {/* Link Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showLinkModal}
          onClose={() => {
            setShowLinkModal(false);
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>add Link QTTP, CQA, CPP</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.Group>
                  <Form.ControlLabel>Link for {dataFromRow.id_header_trans}</Form.ControlLabel>
                  <Form.Control
                    name="keyword"
                    value={linkData.links}
                    onChange={(value) => {
                        setLinkData({...linkData, links: value})
                    }}
                    required
                  />
                </Form.Group>
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowLinkModal(false);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (
                  linkData.links === null ||
                  linkData.links === ""
                ) {
                  toaster.push(
                    Messages("warning", "Link Must be inputed"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                }

                //submitlink
                submitLink()
              }}
              appearance="primary"
            >
              Submit
            </Button>
          </Modal.Footer>
        </Modal>


      {/* History Modal */}
      <Modal
      backdrop="static"
      role="alertdialog"
      open={showHistoryModal}
      onClose={() => setShowHistoryModal(false)}
      size="xs"
    >
      <Modal.Header>
        <Modal.Title>Approval History</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div style={{ lineHeight: "1.8em" }}>
          <strong>Supervisor</strong>
          <div>
            Status:&nbsp;
            <span>
              {!historyData.spv_employee_id
                ? "Draft"
                : !historyData.spv_approved_dt
                ? "Waiting for Approval"
                : `Approved on ${new Date(historyData.spv_approved_dt).toLocaleString()}`}
            </span>
          </div>
          <div>Employee ID: {`${historyData.spv_employee_id} - ${historyData.spv_name}` || "-"}</div>

          <hr />

          <strong>Manager</strong>
          <div>
            Status:&nbsp;
            <span>
              {!historyData.spv_approved_dt
                ? "Pending Supervisor Approval"
                : !historyData.mgr_employee_id
                ? "Waiting for Approval"
                : `Approved on ${new Date(historyData.mgr_approved_dt).toLocaleString()}`}
            </span>
          </div>
          <div>Employee ID: {`${historyData.mgr_employee_id} - ${historyData.mgr_name}`|| "-"}</div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button
          onClick={() => setShowHistoryModal(false)}
          appearance="subtle"
        >
          Close
        </Button>
      </Modal.Footer>
    </Modal>



        {/* Search keyword Modal */}
      <Modal
          backdrop="static"
          role="alertdialog"
          open={showSearchKeyword}
          onClose={() => {
            setShowSearchKeyword(false);
            setSearchKeywordForm([]);
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Search Keyword</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.Group>
                  <Form.Control
                    name="searchKeyword"
                    //value={formValue.main_category_name}
                    onChange={(value) => {
                        setSearchKeywordValue(value)
                    }}
                    required
                  />
                </Form.Group>
                {searchKeywordForm.map((item, index) => (
                  <div className="flex items-center ">
                  <li key={index}>{item}</li>
                  <Button
                      appearance="link"
                      color="red"
                      onClick={() => {
                        const newArray = searchKeywordForm.filter((itemFrom, indexFrom) => indexFrom !== index);
                        setSearchKeywordForm(newArray);
                      }}
                    >
                     x
                  </Button>
                  </div>
                ))}
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowSearchKeyword(false);
                setSearchKeywordForm([]);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (
                  searchKeyWordValue === null ||
                  searchKeyWordValue === ""
                ) {
                  toaster.push(
                    Messages("warning", "Search Keyword Must be inputed"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                }
                setSearchKeywordForm([...searchKeywordForm, searchKeyWordValue]);
              }}
              appearance="primary"
            >
              Add
            </Button>
            <Button
              onClick={() => {
                // setKeywordForm([]) 
                submitSearchKeyword()
              }
              }
              appearance="primary"
              type="submit"
            >
              Search Keyword
            </Button>
          </Modal.Footer>
        </Modal>
    </>
  );
}
