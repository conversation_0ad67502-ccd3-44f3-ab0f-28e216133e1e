import { useEffect, useState } from "react";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Icon<PERSON>utton,
  Stack,
  Breadcrumb,
  Tag,
  Panel,
  Table,
  InputGroup,
  Input,
  Pagination,
  Button,
  Form,
  useToaster,
  Modal,
  SelectPicker,
  PanelGroup,
} from "rsuite";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import API_RequestDataListRequestData from "@/pages/api/request_reagen/api_requestdata_list_request_data";
import API_MasterDataMasterDataMappingReagen from "@/pages/api/master_data/api_masterdata_master_data_mapping_reagen";
import ReagenManufactureApi from "@/pages/api/master_data/api_reagen_manufacture";

export default function RequestReagenListRequestReagenPage() {
  const [moduleName, setModuleName] = useState("");
  const [reagenDataRequest, setReagenDataRequest] = useState([]);
  const [props, setProps] = useState([]);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [sortColumn, setSortColumn] = useState();
  const [sortRequest, setSortRequest] = useState();

  const { HeaderCell, Cell, Column } = Table;

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [showDetailData, setShowDetailData] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const [reqList, setReqList] = useState([]);
  const [manufactureList, setManufactureList] = useState([])

  const [formErrors, setFormErrors] = useState({});
  const [loading,setLoading] = useState(false)

  const toaster = useToaster();

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortRequest) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortRequest(sortRequest);
    }, 500);
  };

  const errorMessageStyle = {
    color: "red",
    fontSize: "12px",
    marginTop: "5px",
  };

  const handleGetAllApi = async () => {
    try {
      const reagenDataRequest = await API_RequestDataListRequestData().getAll();
      console.log(reagenDataRequest);

      setReagenDataRequest(reagenDataRequest.data || []);
    } catch (error) {
      setReagenDataRequest([]);
    }
  };

  const emptyFormValue = {
    id_request_reagen: null,
    id_criteria_reagen: null,
    id_manufacture: null,
    submit_by: null,
    created_by: null,
    updated_by: null,
    raise_remarks: null,
    raised_by: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const emptyDetailFormValue = {
    id_detail_request: null,
    vendor_name: null,
    price: null,
    link_attachments: null,
    remarks: null,
  };

  const [detailFormValue, setDetailFormValue] = useState(emptyDetailFormValue);

  const handleEditRequest = async (selectedIdRequestReagen) => {
    const requiredEditFields = ["raise_remarks"];
    const errors = {};

    // Check required fields
    requiredEditFields.forEach((field) => {
      if (!formValue[field]) {
        errors[field] = `${field
          .split("_")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ")} is required.`;
      }
    });

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      const result = await API_RequestDataListRequestData().editRaise({
        ...formValue,
        id_request_reagen: selectedIdRequestReagen,
        updated_by: props.employee_id,
        raised_by: props.employee_id,
        updated_name: props.employee_id + " - " + props.employee_name,
      });

      if (result.status === 200) {
        handleGetAllApi();
        setShowEditModal(false);
        toaster.push(
          Messages("success", "Success editing Reagen Request Data!"),
          { placement: "topCenter", duration: 5000 }
        );
        setFormValue(emptyFormValue);
      } else if (result.status === 400) {
        const errorMessage = result.message || "Unknown error";
        if (errorMessage === "Data already updated") {
          toaster.push(
            Messages("error", "Data already updated. You can't edit again."),
            { placement: "topCenter", duration: 5000 }
          );
        } else {
          toaster.push(
            Messages(
              "error",
              `Error: "${errorMessage}"`
            ),
            { placement: "topCenter", duration: 5000 }
          );
        }
        setFormValue(emptyFormValue);
      }
    } catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);
      toaster.push(
        Messages("error", `Error: "${error}"`),
        { placement: "topCenter", duration: 5000 }
      );
    }
  };

  const filteredData = reagenDataRequest
    .filter((rowData, i) => {
      const searchFields = [
        "id_request_reagen",
        "id_criteria_reagen",
        "reagen_name",
        "manufacture_desc",
        "item_list_no",
        "reagen_name",
        "catalogue",
        "msds",
        "cas_no",
        "submit_date",
        "submit_by",
        "raised_status",
        "raised_date",
        "raised_by",
        "fu_raise",
        "fu_date",
        "raise_remarks",
        "request_status",
        "created_dt",
        "created_by",
        "updated_dt",
        "updated_by",
        "deleted_dt",
        "deleted_by",
        "is_active",
      ];

      const matchesSearch = searchFields.some((field) =>
        rowData[field]
          ?.toString()
          .toLowerCase()
          .includes(searchKeyword.toLowerCase())
      );

      return matchesSearch;
    })
    .filter((v, i) => {
      // Pagination logic
      const start = limit * (page - 1);
      const end = start + limit;

      return i >= start && i < end;
    });

  const totalRowCount = searchKeyword
    ? filteredData.length
    : reagenDataRequest.length;

  const handleExportExcel = () => {
    try {
      const headerRequest = {
        id_request_reagen: "ID Request Reagen",
        // id_criteria_reagen: "ID Criteria Reagen",
        reagen_name: "Reagen Name",
        manufacture_desc: "Brand Description",
        item_list_no: "Item List No",
        catalogue: "Catalogue",
        msds: "MSDS",
        cas_no: "CAS No",
        submit_date: "Submit Date",
        submit_by: "Submit By",
        raise_remarks: "Raise Remarks",
        raised_by: "Raised By",
        raised_date: "Raised Date",
        raised_status: "Raised Status",
        fu_raise: "FU Raise",
        fu_date: "FU Date",
        request_status: "Request Status",
        created_dt: "Created Date",
        created_by: "Created By",
        updated_dt: "Updated Date",
        updated_by: "Updated By",
        deleted_dt: "Deleted Date",
        deleted_by: "Deleted By",
        is_active: "Is Active",
      };

      const formattedData = reagenDataRequest.map((item) => {
        const formattedItem = {};
        for (const key in item) {
          if (headerRequest[key]) {
            formattedItem[headerRequest[key]] = item[key];
          }
        }
        return formattedItem;
      });

      const ws = XLSX.utils.json_to_sheet(formattedData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Reagen Data");

      const date = dateFormatterDash(new Date());
      XLSX.writeFile(wb, `RequestReagenData ${date}.xlsx`);

      toaster.push(
        Messages(
          "success",
          "Request Reagen data file (.xlsx) downloaded successfully"
        ),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    } catch (error) {
      console.error("Error exporting Excel:", error);
      toaster.push(
        Messages("error", `Error: "${error}"`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const getData = () => {
    if (sortColumn && sortRequest) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortRequest === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const handleAddReagen = async () => {
    setLoading(true);
    const requiredAddFields = ["id_criteria_reagen"];

    const errors = {};

    // Check required fields
    requiredAddFields.forEach((field) => {
      if (!formValue[field]) {
        errors[field] = `${field
          .split("_")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ")} is required.`;
      }
    });

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    const manufacture = manufactureList.find(item => item.id_manufacture === formValue.id_manufacture);
    const reagen = reqList.find(item => item.id_criteria_reagen === formValue.id_criteria_reagen);
    

    try {
      const result = await API_RequestDataListRequestData().add({
        ...formValue,
        submit_by: props.employee_id,
        manufacture_desc : manufacture.manufacture_desc,
        reagen_name : reagen.reagen_name,
        created_by: props.employee_id,
        created_name: props.employee_id + "-" + props.employee_name
      });

      if (result.status === 200) {
        handleGetAllApi();
        setShowAddModal(false);

        toaster.push(
          Messages("success", "Success adding Request Reagen!"),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
        setFormErrors({});
      } else if (result.status === 400) {
        toaster.push(
          Messages(
            "error",
            `Error: "${result.message}"`
          ),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
        setFormErrors({});
      }
    } catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);
      toaster.push(
        Messages("error", `Error: "${error}"`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }

    setLoading(false);
  };

  //   const handleEditRequestStatus = async (selectedIdRequestStatus) => {
  //     console.log("selected id", selectedIdRequestStatus);
  //     await API_RequestDataListRequestData().editStatus({
  //       id_request_reagen: selectedIdRequestStatus,
  //       deleted_by: props.employee_id,
  //     });
  //     handleGetAllApi();
  //   };

  const fetchDetailData = async (id_request_reagen) => {
    try {
      const resDetailData = await API_RequestDataListRequestData().getDetail({
        id_request_reagen: id_request_reagen,
      });
      return resDetailData.data || [];
    } catch (error) {
      console.error("Error fetching detail data:", error);
      toaster.push(
        Messages("error", `Error: ${error.message}`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
      return [];
    }
  };

  const handleDetailRequest = async (id_request_reagen) => {
    try {
      const detailData = await fetchDetailData(id_request_reagen);
      if (detailData.length > 0) {
        setShowDetailData(detailData);
        setDetailFormValue({
          id_detail_request: detailData[0].id_detail_request,
          vendor_name: detailData[0].vendor_name,
          price: detailData[0].price,
          link_attachments: detailData[0].link_attachments,
          remarks: detailData[0].remarks,
        });
        setShowDetailModal(true);
      } else {
        throw new Error("No detail data found for this request.");
      }
    } catch (error) {
      console.error("Error handling detail request:", error);
    }
  };

  const fetchData = async () => {
    const res_req_list =
    await API_MasterDataMasterDataMappingReagen().getAllActive();

    const manufacture_list = await ReagenManufactureApi().getAllActive()
    
    setReqList(res_req_list.data || []);

    //setManufactureList(manufacture_list.data || []);
    setManufactureList(manufacture => [ {
      id_manufacture: 0,
      manufacture_desc: "SEARCH FOR ANOTHER BRAND",
      created_dt: "",
      created_by: "",
      created_name: "",
      updated_dt: "",
      updated_by: "",
      updated_name: "",
      deleted_dt: "",
      deleted_by: "",
      deleted_name: "",
      is_active: 0
  },...manufacture_list.data || []]);
  };


  useEffect(() => {
  
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("request_reagen/list_request_reagen")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetAllApi();
      fetchData();
    }
  }, []);


  return (
    <>
      <div>
        <Head>
          <title>Request Data Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Request Reagen</Breadcrumb.Item>
                  <Breadcrumb.Item active>List Request Reagen</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusRoundIcon />}
                    appearance="primary"
                    onClick={() => {
                      setShowAddModal(true);
                      setFormErrors({});
                    }}
                  >
                    Add
                  </IconButton>

                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              align="center"
              height={400}
              data={getData()}
              sortColumn={sortColumn}
              sortRequest={sortRequest}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={150} align="center" sortable resizable>
                <HeaderCell>ID Request Reagen</HeaderCell>
                <Cell dataKey="id_request_reagen" />
              </Column>
              {/* <Column width={150} align="center" sortable resizable>
                <HeaderCell>ID Criteria Reagen</HeaderCell>
                <Cell dataKey="id_criteria_reagen" />
              </Column> */}
              <Column width={150} sortable resizable>
                <HeaderCell>Reagen Name</HeaderCell>
                <Cell dataKey="reagen_name" />
              </Column>
              
              <Column width={200} sortable resizable>
                <HeaderCell>Request Status</HeaderCell>
                <Cell align="center">
                  {(rowData) => {
                    let statusText = "";
                    let color = "";

                    switch (rowData.request_status) {
                      case 1:
                        statusText = "Purchasing Submit";
                        color = "green";
                        break;
                      case 2:
                        statusText = "Waiting for purchasing raised";
                        color = "red";
                        break;
                      case 3:
                        statusText = "Request fulfilled";
                        color = "green";
                        break;
                      default:
                        statusText = "Waiting for purchasing";
                        color = "red";
                        break;
                    }

                    return <span style={{ color }}>{statusText}</span>;
                  }}
                </Cell>
              </Column>
              <Column width={200} sortable resizable>
                <HeaderCell>Brand Description</HeaderCell>
                <Cell dataKey="manufacture_desc" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Item List No</HeaderCell>
                <Cell dataKey="item_list_no" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Catalogue</HeaderCell>
                <Cell dataKey="catalogue" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>MSDS</HeaderCell>
                <Cell dataKey="msds" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>CAS No</HeaderCell>
                <Cell dataKey="cas_no" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Submit Date</HeaderCell>
                <Cell dataKey="submit_date" />
              </Column>
              <Column width={200} sortable resizable>
                <HeaderCell>Submit - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.created_by} - {rowData.created_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Raise Remarks</HeaderCell>
                <Cell dataKey="raise_remarks" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Raised By</HeaderCell>
                <Cell dataKey="raised_by" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Raised Date</HeaderCell>
                <Cell dataKey="raised_date" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Raised Status</HeaderCell>
                <Cell align="center">
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.raised_status === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.raised_status === 1 ? "Raised" : "Not Raised"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>FU Raise</HeaderCell>
                <Cell dataKey="fu_raise" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>FU Date</HeaderCell>
                <Cell dataKey="fu_date" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Create Date</HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Created By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.created_by} - {rowData.created_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Updated Date</HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Updated By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.updated_by} - {rowData.updated_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Deleted Date</HeaderCell>
                <Cell dataKey="deleted_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Deleted By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.deleted_by} - {rowData.deleted_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={100} sortable resizable>
                <HeaderCell>Status</HeaderCell>
                <Cell align="center">
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={150} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell style={{ padding: "8px" }}>
                  {(rowData) => (
                    <div>
                      <Button
                        appearance="link"
                        disabled={
                          // rowData.request_status === 0 ||
                          // rowData.raised_status === 1
                          rowData.request_status === 0 
                        }
                        onClick={() => {
                          setFormErrors({});
                          setShowEditModal(true);
                          setFormValue(rowData);
                        }}
                      >
                        Raise
                      </Button>

                      <Button
                        appearance="link"
                        onClick={() =>
                          handleDetailRequest(rowData.id_request_reagen)
                        }
                        disabled={rowData.request_status === 0}
                      >
                        Detail
                      </Button>

                      {/* <Button
                        appearance="subtle"
                        onClick={() =>
                          handleEditReagenRequestStatus(rowData.id_request_reagen)
                        }
                      >
                        {rowData.is_active === 1 ? (
                          <TrashIcon style={{ fontSize: "16px" }} />
                        ) : (
                          <ReloadIcon style={{ fontSize: "16px" }} />
                        )}
                      </Button> */}
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Add Modal Add Reagen Request Data */}
        <Modal
          backdrop="static"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Add Master Reagen Request Data</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Criteria Reagen</Form.ControlLabel>
                <SelectPicker
                  name="id_criteria_reagen"
                  value={formValue.id_criteria_reagen}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_criteria_reagen: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      id_criteria_reagen: null,
                    }));
                  }}
                  block
                  data={reqList}
                  labelKey="reagen_name"
                  valueKey="id_criteria_reagen"
                />
                 {formErrors.id_criteria_reagen && (
                  <div style={errorMessageStyle}>
                    {formErrors.id_criteria_reagen}
                  </div>
                )}
                <Form.ControlLabel>Brand</Form.ControlLabel>
                <SelectPicker
                  name="id_manufacture"
                  value={formValue.id_manufacture}
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_manufacture: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      id_manufacture: null,
                    }));
                  }}
                  block
                  data={manufactureList}
                  labelKey="manufacture_desc"
                  valueKey="id_manufacture"
                />
                {formErrors.id_manufacture && (
                  <div style={errorMessageStyle}>
                    {formErrors.id_manufacture}
                  </div>
                )}
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                // Clear form errors immediately
                setFormErrors({});

                setShowAddModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>

            <Button
              onClick={handleAddReagen}
              appearance="primary"
              type="submit"
              disabled={loading}
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        {/* EDIT MODAL REAGEN REQUEST */}
        <Modal
          backdrop="static"
          open={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Raise Reagen Request</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Raise Remarks</Form.ControlLabel>
                <Form.Control
                  name="raise_remarks"
                  value={formValue.raise_remarks}
                  onChange={(value) => {
                    setFormValue({ ...formValue, raise_remarks: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      raise_remarks: null,
                    }));
                  }}
                />
                {formErrors.raise_remarks && (
                  <div style={errorMessageStyle}>
                    {formErrors.raise_remarks}
                  </div>
                )}
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setFormErrors({});
                setShowEditModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleEditRequest(formValue.id_request_reagen);
              }}
              appearance="primary"
              type="button"
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>

        <Modal
          open={showDetailModal}
          onClose={() => setShowDetailModal(false)}
          overflow={true}
        >
          <Modal.Header>
            <Modal.Title>Raise Reagen Request Detail</Modal.Title>
          </Modal.Header>
          <PanelGroup>
            <Modal.Body style={{ maxHeight: "60vh", overflowY: "auto" }}>
              {showDetailData && showDetailData.length > 0
                ? showDetailData.map((detail, index) => (
                    <div key={index} className="mb-3">
                      <Panel bordered className="p-1" shaded>
                        <p className="mb-2 font-semibold">
                          ID Detail Request: {detail.id_detail_request}
                        </p>
                        <p className="mb-2 font-semibold">
                          <span className="font-semibold">Action Status:</span>
                          <span
                            className={`text-${detail.action_status === 0}-500`}
                          >
                            {" "}
                            {detail.action_status === 0
                              ? "No Action"
                              : detail.action_status === 1
                              ? "New Price"
                              : "Update Price"}
                          </span>
                        </p>
                        <p className="mb-2">
                          <span className="font-semibold">Vendor Name:</span>{" "}
                          {detail.vendor_name}
                        </p>
                        <p className="mb-2">
                          <span className="font-semibold">Price:</span>{" "}
                          {detail.price}
                        </p>
                        <p className="mb-2">
                          <span className="font-semibold">
                            Link Attachment:
                          </span>{" "}
                          <a
                            href={detail.link_attachments}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {detail.link_attachments}
                          </a>
                        </p>

                        <p>
                          <span className="font-semibold">Remarks:</span>{" "}
                          {detail.remarks}
                        </p>

                        <p>
                          <span className="font-semibold">Item Code:</span>{" "}
                          {detail.item_code}
                        </p>

                        <p>
                          <span className="font-semibold">Cas No:</span>{" "}
                          {detail.cas_no}
                        </p>

                        <p>
                          <span className="font-semibold">Status: {detail.is_active === 1 ? "Active" : "Deactive"}</span>{" "}
                        </p>
                        <p>
                          <span className="font-semibold">Latest Submit by:</span>{" "}
                          {detail.updated_name !="" ? detail.updated_name : detail.created_name} at{" "}
                          {detail.updated_dt !="" ? detail.updated_dt : detail.created_dt}
                        </p>

                        
                      </Panel>
                    </div>
                  ))
                : null}
            </Modal.Body>
          </PanelGroup>
          <Modal.Footer>
            <Button
              onClick={() => setShowDetailModal(false)}
              appearance="primary"
            >
              Back
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
