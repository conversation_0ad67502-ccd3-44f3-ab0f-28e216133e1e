// import Header from "@/components/layout/Header";
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import ContainerLayout from '@/components/layout/ContainerLayout';
import UserModuleContainer from '@/components/UserModuleContainer';
import Head from 'next/head';
import Header from '@/components/layout/Header';
function Dashboard() {
  const router = useRouter();

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    if (!dataLogin) {
      router.push('/');
      return;
    }
  }, []);

  return (
    <>
      <div>
        <Head>
          <title>Dashboard</title>
        </Head>
      </div>
      <ContainerLayout title="Dashboard">
        <UserModuleContainer />
      </ContainerLayout>
    </>
  );
}
// }

export default Dashboard;
