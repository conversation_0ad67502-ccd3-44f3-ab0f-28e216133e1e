import axios from "axios";

// Function untuk membuat API call
const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

const createApiFunctionOracle = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_PIMSORA_SERVICE}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function APIMaintenanceImpeller() {
  return {
    getListImpeller: createApiFunction("post", "maintenance/impeller/list"),
    getListDate: createApiFunction("post", "maintenance/impeller/list-date"),
    getListImpellerData: createApiFunction("post", "maintenance/impeller/list-data"),
    getAverageImpeller: createApiFunction("post", "maintenance/impeller/average"),
    getMaxImpeller: createApiFunction("post", "maintenance/impeller/max"),
    getOverspeed: createApiFunction("post", "maintenance/impeller/overspeed"),
    getImpellerNextSchedule: createApiFunctionOracle("post", "maintenance/impeller/next-schedule"),
    getImpellerAsset: createApiFunctionOracle("post", "maintenance/impeller/asset"),
  };
}
