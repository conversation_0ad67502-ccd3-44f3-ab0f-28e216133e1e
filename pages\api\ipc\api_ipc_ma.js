import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/ipc/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function APIIPCProductMA(){
    return{
        getProductEntry: createApiFunction("get", "ipc_product/product-entry-ma"),
        getEntryData : createApiFunction("post", "ipc_product/entry-ma"),
        getEntryDataMYSQL : createApiFunction("post", "ipc_product/mysql/entry-ma"),
        EntryHeaderData : createApiFunction("post", "ipc_product/entry-ma-header"),
        EntryDetailData : createApiFunction("post", "ipc_product/entry-ma-detail"),
        EntryDetailDataMYSQL : createApiFunction("post", "ipc_product/mysql/entry-ma-detail"),
        clearStaging : createApiFunction("put", "ipc_product/entry-ma-clear"),
        clearStagingMYSQL : createApiFunction("put", "ipc_product/mysql/entry-ma-clear"),
        getApproval : createApiFunction("get", "approval/need-approve"),
        getApprovalDate : createApiFunction("post", "approval/need-approve-date"),
        getApprovalDetail : createApiFunction("post", "approval/need-approve-detail"),
        putApprove : createApiFunction("put", "approval/approve"),
        getReasonMA : createApiFunction("get", "ipc_reason/ma/get-all-active"),
    }
}