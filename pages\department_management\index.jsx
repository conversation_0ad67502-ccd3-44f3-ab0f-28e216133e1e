import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import { IconButton, Stack, Table, Panel, Pagination } from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import ContainerLayout from "@/components/layout/ContainerLayout";
import API_Department from "@/pages/api/departmentApi";

function DepartmentManagement({ allDepartment }) {
  const router = useRouter();
  const [departments, setDepartments] = useState([]);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);

  const { HeaderCell, Cell, Column } = Table;

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
    }

    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  }, []);

  useEffect(() => {
    if (allDepartment) {
      setDepartments(allDepartment);
    }
  }, [allDepartment]);

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleChangePage = (page) => {
    setPage(page);
  };

  const paginatedDepartments = departments.slice(
    (page - 1) * limit,
    page * limit
  );

  return (
    <>
      <Head>
        <title>Department Management</title>
      </Head>

      <ContainerLayout
        title="Department Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <div className="m-4 pt-2">
          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <IconButton
                  icon={<PlusIcon />}
                  appearance="primary"
                  onClick={() => router.push("/department_management/add")}
                >
                  Add New Department
                </IconButton>
              </Stack>
            }
          >
            <Table
              data={paginatedDepartments}
              bordered
              cellBordered
              height={450}
              wordWrap="break-word"
              onRowClick={(rowData) => console.log(rowData)}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>

              {/* <Column width={70} align="center">
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="Id_Setup" />
              </Column> */}

              <Column width={350}>
                <HeaderCell>Department Name</HeaderCell>
                <Cell dataKey="Department_Name" />
              </Column>

              <Column width={220}>
                <HeaderCell>Created At</HeaderCell>
                <Cell>
                  {(rowData) => {
                    if (!rowData.Created_At) {
                      return "-";
                    }
                    const date = new Date(rowData.Created_At);
                    return date.toLocaleString("id-ID", {
                      day: "numeric",
                      month: "short",
                      year: "numeric",
                      hour12: false,
                      hour: "2-digit",
                      minute: "2-digit",
                      second: "2-digit",
                    });
                  }}
                </Cell>
              </Column>

              <Column width={220}>
                <HeaderCell>Updated At</HeaderCell>
                <Cell>
                  {(rowData) => {
                    if (!rowData.Updated_At) {
                      return "-";
                    }
                    const date = new Date(rowData.Updated_At);
                    return date.toLocaleString("id-ID", {
                      day: "numeric",
                      month: "short",
                      year: "numeric",
                      hour12: false,
                      hour: "2-digit",
                      minute: "2-digit",
                      second: "2-digit",
                    });
                  }}
                </Cell>
              </Column>

              <Column width={200}>
                <HeaderCell>Is Active?</HeaderCell>
                <Cell dataKey="Is_Active">
                  {(rowData) => (
                    <span>
                      {rowData.Is_Active === 1 ? (
                        <div className="flex items-center">
                          <div className="h-2.5 w-2.5 rounded-full bg-green-500 mr-2"></div>{" "}
                          Active
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <div className="h-2.5 w-2.5 rounded-full bg-red-500 mr-2"></div>{" "}
                          Inactive
                        </div>
                      )}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={100} fixed="right">
                <HeaderCell>...</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span>
                      <a
                        className="cursor-pointer"
                        onClick={() =>
                          router.push({
                            pathname: "/department_management/edit/",
                            query: { idDepartment: `${rowData?.Id_Setup}` },
                          })
                        }
                      >
                        Edit
                      </a>
                    </span>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                total={departments.length}
                limitOptions={[10, 30, 50]}
                limit={limit}
                activePage={page}
                onChangePage={handleChangePage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps() {
  const { GetAllDepartment } = API_Department();
  const { data: departments } = await GetAllDepartment();

  const allDepartment = departments || [];

  return {
    props: {
      allDepartment,
    },
  };
}

export default DepartmentManagement;
