import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiMachineOeeHeaderStripping() {
  return {
    GetAllMachineOeeListStripping: createApiFunction("get", "oee/machine_oee_stripping/list"),
    GetAllActiveMachineOeeListStripping: createApiFunction("get", "oee/machine_oee_stripping/list-active"),
    GetMachineOeeListStrippingById: createApiFunction("post", "oee/machine_oee_stripping/id"),
    CreateMachineOeeListStripping: createApiFunction("post", "oee/machine_oee_stripping/create"),
    EditMachineOeeListStripping: createApiFunction("put", "oee/machine_oee_stripping/edit"),
    EditStatusMachineOeeListStripping: createApiFunction("put", "oee/machine_oee_stripping/edit-status"),
  };
}
