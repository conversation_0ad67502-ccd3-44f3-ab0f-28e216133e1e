import ContainerLayout from '@/components/layout/ContainerLayout'
import Head from 'next/head'
import React, { useEffect, useState } from 'react'
import { Breadcrumb,Panel, Stack, Tag } from 'rsuite'

export default function SampleTracker() {

    const [moduleName, setModuleName] = useState("");
    const [props, setProps] = useState([]);


    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("scm/scm_dashboard")
            );
            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
        }
    }, []);


    return (
        <>
            <div>
                <Head>
                    <title>EKV Trend Dashboard</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className='m-4 pt-2'>
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Quality</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Sample TrackerDashboard</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill                        
                    >
                        <div style={{ width: "100%", height: "100vh" }}>
                            <iframe 
                            style={{
                            width: "100%",
                            height: "100%",
                            border: "none",
                            }}
                            src="https://lookerstudio.google.com/embed/reporting/c5725cca-fdbf-44fb-a4ca-40154d94620b/page/fquXE" 
                            // frameborder="0" 
                            // style="border:0" 
                            allowfullscreen 
                            sandbox="allow-storage-access-by-user-activation allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"></iframe>
                            </div>
                    </Panel>
                </div>
            </ContainerLayout>
        </>
    )
}
