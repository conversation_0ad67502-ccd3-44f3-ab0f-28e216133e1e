import { useEffect, useState } from "react";
import Head from "next/head";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "rsuite";
import { faFileDownload } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import ApiRecipe from "@/pages/api/pqr/recipe/api_recipe";
import ApiMasterdata_ppi from "@/pages/api/pqr/ppi/api_masterdata_ppi";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";

pdfMake.vfs = pdfFonts.pdfMake.vfs;

export default function PdfRecipeComponent({ idPPI, sessionAuth }) {  
  const [bindingParamPpiDataState, setBindingParamPpiDataState] = useState([]);
  const [formData, setFormData] = useState({
    id_ppi: null,
    ppi_name: null,
    product_code: null,
    wetmill:null,
    created_date: "",
    created_by: "",
    updated_date: "",
    updated_by: "",
    deleted_date: "",
    deleted_by: "",
  });

  const HandleGetAllBindingParamPpiApi = async (id_ppi) => {
    try {
      const res = await ApiRecipe().getAllRecipeByIdPPI({ id_ppi: parseInt(id_ppi) });
      console.log("res", res);
      if (res.status === 200) {
        setBindingParamPpiDataState([...res.data.recipe_step]);
        console.log("Updated Data State:", res.data.recipe_step);
      } else {
        console.log("error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Api", error);
    }
  };

  const HandleGetDetailPPI = async (id_ppi) => {
    try {
      const api = ApiMasterdata_ppi();
      const response = await api.GetMasterPPIById({ id_ppi: parseInt(id_ppi) });

      if (response.status === 200) {
        const data = response.data;
        setFormData({
          id_ppi: data.id_ppi,
          ppi_name: data.ppi_name,
          product_code: data.product_code,
          wetmill: data.wetmill,
          created_date: new Date(data.created_date).toLocaleDateString("en-GB"),
          created_by: data.created_by,
          updated_date: data.updated_date ? new Date(data.updated_date).toLocaleDateString("en-GB") : "-",
          updated_by: data.updated_by || "-",
          deleted_date: data.deleted_date ? new Date(data.deleted_date).toLocaleDateString("en-GB") : "-",
          deleted_by: data.deleted_by || "-",
        });
      } else {
        console.error("Failed to fetch detail data");
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
    }
  };

  useEffect(() => {
      HandleGetAllBindingParamPpiApi(idPPI);
      HandleGetDetailPPI(idPPI);
    
  }, [idPPI]);

  const generatePdfNew = (action) => {
    if (!bindingParamPpiDataState || !Array.isArray(bindingParamPpiDataState)) {
      console.error("Data belum tersedia atau salah format:", bindingParamPpiDataState);
      return;
    }

    // Header sesuai contoh PDF
    const getDisplayValue = (param) => {
      if (param.binding_type === 1) {
        return `${param.min_value} - ${param.max_value}`;
      } else if (param.binding_type === 2) {
        return param.absolute_value;
      } else if (param.binding_type === 3) {
        return param.description_value;
      } else {
        return param.set_point_value || "-";
      }
    };
    const formattedDate = (dateStr) => {
      if (!dateStr) return "-";
      const [day, month, year] = dateStr.split("/");
      // Mengambil 2 digit terakhir dari tahun
      const shortYear = year.slice(-2);
      return `${day}-${month}-${shortYear}`;
    };
    const createdBy = `${formData.created_by} pada ${formattedDate(formData.created_date)}`;
    const approvedBy = bindingParamPpiDataState && bindingParamPpiDataState.approval_by && bindingParamPpiDataState.approval_date
      ? `${bindingParamPpiDataState.approval_by} pada ${formattedDate(bindingParamPpiDataState.approval_date)}`
      : "-";
    const headerSection = [
      { text: `Nama PPI          : ${formData.ppi_name || '-'}`, style: "detail" },
      { text: `Product              : ${formData.product_code || '-'}`, style: "detail" },
      { text: `Wetmill              : ${formData.wetmill || '-'}`, style: "detail" },
      { text: `Dibuat Oleh       : ${createdBy}`, style: "detail" },
      { text: `Disetujui Oleh   : ${approvedBy}`, style: "detail" },
      { text: "\n" }
    ];

    let contentArray = [];
    contentArray.push(...headerSection);

    // Iterasi tiap langkah kerja (step) dan tampilkan tabel parameter untuk setiap step
    bindingParamPpiDataState.forEach((step) => {
      // Header untuk langkah kerja
      contentArray.push({ text: `Langkah Kerja : ${step.step_name}`, style: "detail", margin: [0, 10, 0, 5] });

      // Gabungkan parameter dari recipe_parameter_y dan recipe_parameter_n (jika ada)
      let params = [];
      if (step.recipe_parameter_y && Array.isArray(step.recipe_parameter_y)) {
        params = params.concat(step.recipe_parameter_y);
      }
      if (step.recipe_parameter_n && Array.isArray(step.recipe_parameter_n)) {
        params = params.concat(step.recipe_parameter_n);
      }

      // Urutkan berdasarkan order_no
      params.sort((a, b) => a.order_no - b.order_no);

      // Buat body tabel dengan header kolom
      let tableBody = [
        [
          { text: "No", },
          { text: "Nama Parameter", },
          { text: "Nilai Set Point", },
          { text: "Data Connectivity", },
          { text: "Persyaratan", },
        ]
      ];

      params.forEach((param, index) => {
        tableBody.push([
          index + 1,
          param.paramater_name +  ` (${param?.uom || "-"})`|| "-",
          // (param?.parameter_name ? `${param.parameter_name} (${param?.uom || "-"})` : "-"),
          param.set_point_flag === 1 ? "Y" : "N",
          param.is_actual === 1 ? "Y" : "N",
          getDisplayValue(param)
        ]);
      });

      // Masukkan tabel untuk step ini ke konten PDF
      contentArray.push({
        style: "tableExample",
        table: {
          widths: ["auto", "*", "auto", "auto", "auto"],
          body: tableBody,
        },
      });
    });

    // Format tanggal dibuat agar memakai tanda strip (dd-mm-yyyy)
    const now = new Date();
    const formattedCurrentDate = now.toLocaleDateString("en-GB").split("/").join("-");
    const formattedCurrentTime = now.toLocaleTimeString("en-GB");
    const footerText = `Dokumen ini disediakan oleh : ${sessionAuth.employee_name} pada ${formattedCurrentDate} ${formattedCurrentTime}`;

    var dd = {
      content: contentArray,
      styles: {
        headerDetail: {
          fontSize: 12,
          bold: true,
          margin: [0, 2, 0, 2]
        },
        stepHeader: {
          fontSize: 12,
          bold: true,
          decoration: "underline"
        },
        tableHeader: {
          bold: true,
          fontSize: 11,
          color: "black"
        },
        tableExample: {
          margin: [0, 5, 0, 15]
        }
      },
      footer: (currentPage, pageCount) => {
        return {
          columns: [
            { text: footerText, alignment: "left", margin: [30, 0, 0, 0] },
            { text: `Page ${currentPage} of ${pageCount}`, alignment: "right", margin: [0, 0, 30, 0] }
          ]
        };
      },
      pageMargins: [40, 60, 40, 60]
    };

    if (action === "Download") {
      pdfMake.createPdf(dd).download(`Print Out PPI - Transaction ID ${idPPI}.pdf`);
    } else {
      pdfMake.createPdf(dd).open();
    }
  };

  return (
    <div
      style={{
        width: "100%",
        padding: "1em",
        backgroundColor: "#2c2c30",
        position: "sticky",
        top: 0,
      }}
    >
      <Head>
        <title>Reporting Approval PPI</title>
      </Head>
      <Stack justifyContent="space-between">
        <p style={{ color: "white", fontSize: "1em" }}>
          Print Out Reporting Approval PPI - PPI ID : {idPPI}
        </p>
        <Stack>
          <Button onClick={() => generatePdfNew("preview")} style={{ marginRight: "5px" }}>
            Preview
          </Button>
          <Button onClick={() => generatePdfNew("Download")}>
            <FontAwesomeIcon icon={faFileDownload} style={{ fontSize: 15 }} /> Download
          </Button>
        </Stack>
      </Stack>
    </div>
  );
}
