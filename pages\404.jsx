// pages/404.js

import { useEffect } from "react";
import { useRouter } from "next/router";
import { Button } from "rsuite";
import styles from "../components/style/LoadingModal.module.css";
import Head from "next/head";

const Custom404 = () => {
  const router = useRouter();

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }
  });
  return (
    <>
      <Head>
        <title>404 Page NOT FOUND</title>
      </Head>
      <div className="container-fluid d-flex align-items-center justify-content-center h-100 mb-5">
        <div className={`card mt-5 p-5 shadow-lg ${styles.customCardWidth}`}>
          <div className="mb-5">
            <img src="/Logo_kalbe_detail.png" style={{ width: 200 }} />
          </div>
          <div className="mb-2">
            <h4>404 Page NOT FOUND</h4>
          </div>
          <Button
            appearance="primary"
            onClick={() => router.push("/dashboard")}
          >
            Back to Dashboard
          </Button>
        </div>
      </div>
      ;
    </>
  );
};

export default Custom404;
