import axios from "axios";

export default function MenuApi() {
  // API mendapatkan Semua Menu
  const GetMenuAll = async () => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/menu/get/all`)
      .then((res) => {
        return (data = res.data);
      })
      .catch((err) => console.log(err));

    return data;
  };

  // API mendapatkan Menu menurut ID tertentu
  const GetMenuByIdApi = async (menuIdData) => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/menu/get/id`, menuIdData)
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  const GetParentmenuDataApi = async (inputData) => {
    let reqData = {
      module_code: parseInt(inputData),
    };
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/menu/get/parentMenu`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // API mendapatkan Child Menu dari parent_link_code
  const GetChildMenuDataApi = async (parentLinkCode) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/menu/get/childMenu`,
        parentLinkCode
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // API untuk melakukan Update Menu
  const UpdateMenuApi = async (updatedMenu) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/menu/update/menu`,
        updatedMenu
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  const GetUserMenu = async (userData) => {
    let reqData = {
      employee_id: userData.employee_id,
      module_code: userData.module_code,
    };

    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/menu/get/userMenuApi`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        console.log(err);
        data = err.response.data;
      });

    return data;
  };

  const PostMenu = async (menuData) => {
    let data = [];
    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/menu/post/menu`, menuData)
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };

  const GetMenuByModuleId = async (id) => {
    const reqData = {
      module_code: id,
    };
    let data = [];
    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/menu/get/bymodule`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };

  return {
    GetMenuAll,
    GetUserMenu,
    PostMenu,
    GetMenuByIdApi,
    UpdateMenuApi,
    GetMenuByModuleId,
    GetParentmenuDataApi,
    GetChildMenuDataApi,
  };
}
