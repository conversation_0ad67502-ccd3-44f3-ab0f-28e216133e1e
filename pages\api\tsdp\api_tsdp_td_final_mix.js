import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](
    `${process.env.NEXT_PUBLIC_REAGEN}/ts/${url}`,
    data
  )
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiTsdpTDFinalMix() {
  return {
    getAllTsTransactionFinalMix: createApiFunction(
      "get",
      "transaction-d-finalmix/list"
    ),
    getAllTsTransactionFinalMixById: createApiFunction(
      "post",
      "transaction-d-finalmix/id"
    ),
    postTsTransactionFinalMix: createApiFunction(
      "post",
      "transaction-d-finalmix/create"
    ),
    UpdateTsTransactionFinalMix: createApiFunction(
      "put",
      "transaction-d-finalmix/edit"
    ),
    UpdateStatusTsTransactionFinalMix: createApiFunction(
      "put",
      "transaction-d-finalmix/edit-status"
    ),
  };
}
