import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](
    `${process.env.NEXT_PUBLIC_REAGEN}/ts/${url}`,
    data
  )
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiTsdpTDAttachments() {
  return {
    postTsTransactionUpload: createApiFunction(
      "post",
      "master-transaction-attachment/upload"
    ),
    getTsTransactionById: createApiFunction(
      "post",
      "master-transaction-attachment/id"
    ),
    softDeleteAttachment: createApiFunction(
      "put",
      "master-transaction-attachment/s-delete"
    ),
    undeleteAttachment: createApiFunction(
      "put",
      "master-transaction-attachment/s-undelete"
    ),
    getAllTransactionAttachment: createApiFunction(
      "get",
      "master-transaction-attachment/list-attachment"
    ),
  };
}
