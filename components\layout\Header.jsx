import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUserCircle, faBell } from "@fortawesome/free-solid-svg-icons";

import { Dropdown } from "rsuite";
import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";

import API_Notification from "@/pages/api/api_notification";
import SessionAPI from "@/pages/api/session";

export default function Header() {
  const router = useRouter();
  const MySwal = withReactContent(Swal);
  const [employee_id, setEmployee_id] = useState("")
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [totalNotif, setTotalNotif] = useState(0);

  const logoutHandler = async () => {
    MySwal.fire({
      icon: "warning",
      title: "Are you sure you want to sign out?",
      text: "Signing out will end your current session.",
      showCancelButton: true,
      confirmButtonColor: "#D50000",
      confirmButtonText: "Sign Out",
    }).then(async (result) => { // Make this callback async
      if (result.isConfirmed) {
        try {
          const res = await SessionAPI().logout({
            employee_id: employee_id
          });
          
          if (res.status == 200) {
            localStorage.clear();
            router.push("/"); 
          } else{
            console.log("res error", res)
          }         
        } catch (error) {
          console.error("Logout failed", error);
          // Optionally show an error message to the user
        }
      }
    });
  };

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    setName(dataLogin.employee_name);
    setEmail(dataLogin.email);
    setEmployee_id(dataLogin.employee_id)

    const fetchData = async () => {
      const response = await API_Notification().countNotificationByEmployeeId({
        employee_id: dataLogin.employee_id,
      });
      setTotalNotif(response.data || 0);
      // console.log(response);
    };
    fetchData();
  }, []);
  return (
    <div className="flex w-full p-2 shadow-sm justify-end">
      <div className="flex items-center gap-4">
        <div
          className="relative cursor-pointer"
          onClick={() => router.push("/message")}
        >
          <FontAwesomeIcon icon={faBell} style={{ fontSize: 20 }} />
          {totalNotif > 0 && (
            <div className="absolute -top-1 -right-1 bg-red-500 rounded-full w-4 h-4 text-xs text-white flex items-center justify-center line-clamp-1">
              {totalNotif > 9 ? "9+" : totalNotif}
            </div>
          )}
        </div>
        <Dropdown
          title={
            <div className="flex items-center gap-2">
              <FontAwesomeIcon icon={faUserCircle} style={{ fontSize: 20 }} />
              <span>{name !== "" ? name : ""}</span>
            </div>
          }
          placement="bottomEnd"
        >
          <Dropdown.Item panel style={{ padding: "10px" }}>
            <p>Signed in as</p>
            <p className="font-semibold capitalize">
              {name !== "" ? name : ""}
            </p>
            <p className="text-xs lowercase">{email !== "" ? email : ""}</p>
          </Dropdown.Item>
          <Dropdown.Separator />
          <Dropdown.Item onClick={() => router.push("/user_profile/")}>
            Profile
          </Dropdown.Item>
          <Dropdown.Item onClick={logoutHandler} style={{ color: "#D50000" }}>
            Sign Out
          </Dropdown.Item>
        </Dropdown>
      </div>
    </div>
  );
}
