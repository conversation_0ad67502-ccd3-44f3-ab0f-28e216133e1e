import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/scm_reporting/scm_lov_type/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};

export default function ScmLovTypeApi(){
    return {
        getAll: createApiFunction("post", "get/all"),
        create: createApiFunction("post", "create"),
        update: createApiFunction("put", "update"),
        updateStatus: createApiFunction("put", "update/status"),
    }
}