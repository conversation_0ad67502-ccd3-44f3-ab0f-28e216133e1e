import axios from 'axios';

export default function ProtocolApi() {
  const GetAllProtocolHeaderByApproverId = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolByApproverId`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetAllProtocolHeaderDataByDepartmentId = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getAllProtocolHeaderDataByDepartmentId`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolDataForAnalysisReport = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolDataForAnalysisReport`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolDataForAnalysisRevision = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolDataForAnalysisRevision`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetDataDetailAnalysisRevision = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getDataDetailAnalysisRevision`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostHasilAnalisaRevision = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postHasilAnalisaRevision`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolDataForSetupAnalysis = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolDataForSetupAnalysis`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostApproveSetupAnalisa = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postApproveSetupAnalisa`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolHasilAnalisaForApproval = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolHasilAnalisaForApproval`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolHasilAnalisaReviewer = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolHasilAnalisaReviewer`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolHasilAnalisaApprover = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolHasilAnalisaApprover`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolDataByNoProtocol = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolByNoProtocol`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolHeaderDataByEmployeeId = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolHeaderByEmployeeId`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolDataForApproval = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolDataForApproval`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolBeforeSetupCycle = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolBeforeSetupCycle`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetHasilAnalisaValue = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getHasilAnalisaValue`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostProtocol = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postProtocol`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostHasilAnalisa = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postHasilAnalisa`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostApprovalHasilAnalisa = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postApprovalHasilAnalisa`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostRejectHasilAnalisa = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postRejectHasilAnalisa`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostApprovalVarianceHasilAnalisa = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postApproveVarianceHasilAnalisa`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetHasilAnalisaForReport = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getHasilAnalisaForReport`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };


  const GetReport = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getReportProtocol`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetReportExcel = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getreportexcel`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  // const PostApprovalVarianceHasilAnalisa = async (inputData) => {
  //   let data = [];

  //   await axios
  //     .post(
  //       `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postApproveVarianceHasilAnalisa`,
  //       inputData,
  //     )
  //     .then((res) => (data = res.data))
  //     .catch((err) => {
  //       data = err.response.data;
  //       console.log(err);
  //     });

  //   return data;
  // };

  const PostProtocolDocumentApproval = async (inputData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postProtocolDocumentApproval`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostProtocolApproval = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postProtocolApproval`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostProtocolReject = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postProtocolReject`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostProtocolDocumentReject = async (inputData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postProtocolDocumentReject`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostProtocolEditDocumentReject = async (inputData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postProtocolEditDocumentReject`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const UpdateSetupCycleProtocol = async (inputData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/putUpdateSetupCycle`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const UpdateProtocol = async (inputData) => {
    let data = [];
    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/putProtocol`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });
    return data;
  };


  const PutDeleteProtocol = async (inputData) => {
    let data = [];
    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/delete`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });
    return data;
  };

  const PutPrintCounter = async (inputData) => {
    let data = [];
    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/counter`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });
    return data;
  };

  const UpdateRejectedProtocol = async (inputData) => {
    let data = [];
    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/putRejectedProtocol`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });
    return data;
  };

  const GetProtocolEditHeaderDataByEmployeeId = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolEditHeaderByEmployeeId`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolEditByNoProtocol = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolEditByNoProtocol`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostProtocolEditDocumentApproval = async (inputData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/postProtocolEditDocumentApproval`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetDataIsProtocolIsInEditProcess = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getDataProtocolIsInEditProcess`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetProtocolHistory = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getProtocolHistory`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetReportDashboard = async (inputData) =>{
    let data = [];

    await axios
    .post(
      `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getDashboard`,
      inputData,
    )
    .then((res) => (data = res.data))
    .catch((err) => {
      data = err.response?.data;
      console.log(err);
    });

  return data;

  }

  const GetCalendar = async (inputData) =>{
  let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/protocol/getCalendar`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;

  }

  return {
    PostProtocol,
    GetHasilAnalisaValue,
    GetAllProtocolHeaderByApproverId,
    GetProtocolDataByNoProtocol,
    PostProtocolApproval,
    PostProtocolReject,
    GetProtocolDataForApproval,
    GetProtocolHeaderDataByEmployeeId,
    PostProtocolDocumentApproval,
    PostProtocolDocumentReject,
    GetAllProtocolHeaderDataByDepartmentId,
    GetProtocolBeforeSetupCycle,
    UpdateSetupCycleProtocol,
    GetProtocolDataForAnalysisReport,
    PostHasilAnalisa,
    GetProtocolHasilAnalisaForApproval,
    PostApprovalHasilAnalisa,
    PostRejectHasilAnalisa,
    PostApprovalVarianceHasilAnalisa,
    GetProtocolDataForSetupAnalysis,
    PostApproveSetupAnalisa,
    GetProtocolDataForAnalysisRevision,
    GetDataDetailAnalysisRevision,
    PostHasilAnalisaRevision,
    UpdateProtocol,
    GetProtocolEditHeaderDataByEmployeeId,
    GetProtocolEditByNoProtocol,
    PostProtocolEditDocumentApproval,
    PostProtocolEditDocumentReject,
    GetDataIsProtocolIsInEditProcess,
    UpdateRejectedProtocol,
    GetProtocolHistory,
    GetHasilAnalisaForReport,
    GetProtocolHasilAnalisaApprover,
    GetProtocolHasilAnalisaReviewer,
    GetReport,
    GetReportDashboard,
    GetReportExcel,
    GetCalendar,
    PutDeleteProtocol,
    PutPrintCounter
  };
}
