import { useRouter } from 'next/router';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import Head from 'next/head';
import MainContent from '@/components/layout/MainContent';
import { useState, useEffect } from 'react';
import {
    Form,
    Button,
    Stack,
    Input,
    InputNumber,
    Divider,
    Checkbox,
    CheckboxGroup,
    useToaster,
    Panel,
    Tag
} from 'rsuite';
import Editor from '@/components/Editor';
import ProtocolApi from '@/pages/api/protocolApi';
import UseTestApi from '@/pages/api/userApi';
import Messages from '@/components/Messages';
import { faL } from '@fortawesome/free-solid-svg-icons';

export default function PengisianHasilAnalisaDetail({ noProtocol, idSetupCycle, id_detail_batch }) {
    const toaster = useToaster();
    const router = useRouter();
    const [protocolDetailValue, setProtocolDetailValue] = useState([]);
    let path = 'protocol/analisa/PengisianHasilAnalisa';
    let { data } = router.query;
    data ? (data = JSON.parse(data)) : data;
    const [breadcrumbsData, setBreadcrumbsData] = useState([]);
    const [complyValue, setComplyValue] = useState([]);
    const [cycleMonthData, setCycleMonthData] = useState([
        0, 3, 6, 9, 12, 18, 24, 36,
    ]);
    const [t30selected, setT30Selected] = useState([0]);
    const [t40selected, setT40Selected] = useState([0]);
    const [moduleName, setModuleName] = useState('');
    const [tujuan, setTujuan] = useState('');
    const [ruangLingkup, setRuangLingkup] = useState('');
    const [dokumenAcuan, setDokumenAcuan] = useState('');
    const [tanggungJawab, setTanggungJawab] = useState('');
    const [namaProduk, setNamaProduk] = useState('');
    const [kodeProduk, setKodeProduk] = useState('');
    const [nomorBatch, setNomorBatch] = useState('');
    const [nomorPPI, setNomorPPI] = useState('');
    const [batchSize, setBatchSize] = useState('');
    const [dibuatOleh, setDibuatOleh] = useState('');
    const [userId, setUserId] = useState('');
    const [komposisiFormula, setKomposisiFormula] = useState('');
    const [isDisabled, setIsDisabled] = useState(true);
    const [additionalNotes, setAdditionalNotes] = useState('');
    const [title, setTitle] = useState('')
    const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
    const [cycleMonthAndTimeframe, setCycleMonthAndTimeframe] = useState({
        cycle_month: 0,
        timeframe: ""
    });
    const { UserLoginApi } = UseTestApi();
    const [protocolDetailData, setProtocolDetailData] = useState([]);
    const [batchName , setBatchName] = useState('')
    const {
        GetProtocolDataByNoProtocol,
        PostHasilAnalisa
    } = ProtocolApi();

    // Handler awal checked
    const isChecked = (group, code) => {
        let result;
        if (group === 'T30') {
            for (let item of t30selected) {
                if (code === item) {
                    result = true;
                    break;
                } else {
                    result = false;
                }
            }
        } else {
            for (let item of t40selected) {
                if (code === item) {
                    result = true;
                    break;
                } else {
                    result = false;
                }
            }
        }
        return result;
    };

    const GetProtocolData = async (noProtocol, userData, userDept) => {
        const inputData = {
            no_protocol: noProtocol,
            employee_id: userData,
            department: parseInt(userDept),
            id_setup_cycle: parseInt(idSetupCycle)
        };

        const { data: protocolData } = await GetProtocolDataByNoProtocol(inputData);

        if (protocolData !== null && protocolData !== undefined) {
            // Set Header Data
            setTitle(protocolData.Header_Data[0].Title)
            setTujuan(protocolData.Header_Data[0].Desc_Purpose);
            setRuangLingkup(protocolData.Header_Data[0].Desc_Scope);
            setDokumenAcuan(protocolData.Header_Data[0].Desc_Document);
            setTanggungJawab(protocolData.Header_Data[0].Desc_Responsibilities);
            setNamaProduk(protocolData.Header_Data[0].Product_Name);
            setKodeProduk(protocolData.Header_Data[0].Product_Code);
            setNomorBatch(protocolData.Header_Data[0].Batch_No);
            setNomorPPI(protocolData.Header_Data[0].Ppi_No);
            setBatchSize(protocolData.Header_Data[0].Batch_Size);
            setDibuatOleh(protocolData.Header_Data[0].Manufactured_By);
            setKomposisiFormula(protocolData.Header_Data[0].Formula);
            setAdditionalNotes(protocolData.Header_Data[0].Additional_Notes);

            // Set T30 TimeFrame Data
            let t30DataList = [];
            protocolData.T30Data.map((item) => {
                t30DataList.push(item.Cycle_Month);
            });

            // Set T40 TimeFrame Data
            let t40DataList = [];
            protocolData.T40Data.map((item) => {
                t40DataList.push(item.Cycle_Month);
            });

            setT30Selected(t30DataList);
            setT40Selected(t40DataList);

            // Set Detail Data
            setProtocolDetailData(protocolData.Detail_Data);

            // Set Cycle Month and Timeframe
            const dataCycleMonthAndTimeframe = {
                cycle_month: protocolData.CycleMonthAndTimeframe.Cycle_Month,
                timeframe: protocolData.CycleMonthAndTimeframe.Timeframe
            };
            setCycleMonthAndTimeframe(dataCycleMonthAndTimeframe);

            // Filter data 
            const filteredData = protocolData.BatchNumber_Data.filter(item => item.Id_detail_batch == id_detail_batch);

            // Extract the Batch_number from the filtered data
            const batchNumber = filteredData.map(item => item.Batch_number);

            setBatchName(batchNumber)

        }
    };

    useEffect(() => {
        const moduleNameValue = localStorage.getItem('module_name');
        const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
        if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
            router.push('/');
            return;
        }

        // validate access for this page
        if (!dataLogin.menu_link_code) {
            router.push('/dashboard');
            return;
        }
        const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
            item.includes(path),
        );
        if (validateUserAccess.length === 0) {
            router.push('/dashboard');
            return;
        }

        if (
            moduleNameValue === null ||
            moduleNameValue === undefined ||
            moduleNameValue === ''
        ) {
            router.push('/dashboard');
            return;
        }

        const asPathWithoutQuery = router.asPath.split('?')[0];
        const asPathNestedRoutes = asPathWithoutQuery
            .split('/')
            .filter((v) => v.length);
        let routerBreadCrumbsData = asPathNestedRoutes.filter((item, index) => index != 0);
        routerBreadCrumbsData.unshift(moduleNameValue);
        const capitalizeFirstLetter = (str) => {
            return str.charAt(0).toUpperCase() + str.slice(1);
        };
        const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
            const words = item.split(/(?=[A-Z])/);
            return words.map((word) => capitalizeFirstLetter(word)).join(' ');
        });
        setBreadcrumbsData(breadCrumbsResult);

        setModuleName(moduleNameValue);

        setUserId(dataLogin.employee_id);

        GetProtocolData(noProtocol, dataLogin.employee_id, dataLogin.department);

    }, []);

    useEffect(() => {
        if (protocolDetailValue?.length > 0) {
            protocolDetailValue.map(itemValue => {
                //console.log(itemValue)
                const dataMaster = protocolDetailData.filter(itemData => itemData.Unique_Identifier == itemValue.unique_identifier);
                const dataComplyFilter = complyValue.filter(itemComply => itemComply == itemValue.unique_identifier);
                const isValueExist = dataComplyFilter.length > 0 ? true : false;
                let isComply = false;

                // Processing the value
                if (itemValue.input_method_id == 3) {
                    // Range
                    isComply = (itemValue.range_value <= dataMaster[0].Max_Value && itemValue.range_value >= dataMaster[0].Min_Value) ? true : false;
                }
                if (itemValue.input_method_id == 2) {
                    // Absolute
                    isComply = itemValue.value_ref == dataMaster[0].Value_Reference ? true : false;
                }
                if (itemValue.input_method_id == 1) {
                    //isComply = complyManual
                    isComply = complyValue.includes(itemValue.unique_identifier);
                }

                // Comparing with complyValue
                if (isValueExist && !isComply) {
                    // Removing data from complyValue state
                    const data = complyValue.filter(itemComply => itemComply != itemValue.unique_identifier);
                    setComplyValue(data);
                }
                if (!isValueExist && isComply) {
                    // Adding data into complyValue state
                    const data = [...complyValue];
                    data.push(itemValue.unique_identifier);
                    setComplyValue(data);
                }
            });
        }
    }, [protocolDetailValue]);

    const t30ChangeHandler = (event) => {
        const isChecked = event.target.checked;
        const value = parseInt(event.target.value);

        if (isChecked) {
            setT30Selected((prevValue) => [...prevValue, value]);
        } else {
            const newData = t30selected.filter((item) => item !== value);
            setT30Selected(newData);
        }
    };

    const t40ChangeHandler = (event) => {
        const isChecked = event.target.checked;
        const value = parseInt(event.target.value);

        if (isChecked) {
            setT40Selected((prevValue) => [...prevValue, value]);
        } else {
            const newData = t40selected.filter((item) => item !== value);
            setT40Selected(newData);
        }
    };

    const hasilChangeHandler = (unique_identifier, value, input_method_id) => {
        let data = {
            unique_identifier: unique_identifier,
            input_method_id: parseInt(input_method_id),
            value_ref: 0,
            range_value: 0,
            desc: ""
        };

        const selectedArray = protocolDetailValue.filter(item => item.unique_identifier == unique_identifier);
        if (selectedArray.length > 0) {
            data = selectedArray[0];
        }
        if (input_method_id == 3) {
            // Range min value
            data.range_value = parseFloat(value);
        }
        if (input_method_id == 2) {
            // Absolute
            data.value_ref = parseFloat(value);
        }
        if (input_method_id == 1) {
            // Description
            data.desc = value;
        }

        const filteredData = protocolDetailValue.filter(item => item.unique_identifier != unique_identifier);
        setProtocolDetailValue([...filteredData, data]);
    };

    const submitHandler = async () => {
        setIsSubmitButtonDisabled(true);

        // Validasi nilai kosong
        if (protocolDetailValue.length != protocolDetailData.length) {
            toaster.push(
                Messages("error", "Please fill all required data !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }

        let isNaNValueExist = false;
        const protocolDetailValueNaNCheck = protocolDetailValue.map(itemValue => {
            if (isNaN(itemValue.range_value)) {
                isNaNValueExist = true;
            }
            if (isNaN(itemValue.value_ref)) {
                isNaNValueExist = true;
            }
        });
        if (isNaNValueExist) {
            toaster.push(
                Messages("error", "Please fill all required data !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            setIsSubmitButtonDisabled(false);
            return;
        }

        // Constructing overall_status
        let complyExpected = protocolDetailValue;
        let isIncomplete = false;
        if (complyExpected.length > 0) {
            if (complyValue.length < complyExpected.length) {
                isIncomplete = true;
            }
        }

        const detailData = protocolDetailValue.map(itemValue => {
            let resultValue = 0;

            // constructing result_value
            if (itemValue.input_method_id == 3) {
                // Range
                resultValue = parseFloat(itemValue.range_value);
            } else if (itemValue.input_method_id == 2) {
                // Absolute
                resultValue = parseFloat(itemValue.value_ref);
            } else {
                // Desc
                resultValue = itemValue.desc;
            }

            // constructing complies_flag
            const complyData = complyValue.includes(itemValue.unique_identifier);
            return {
                result_value: `${resultValue}`,
                complies_flag: complyData ? 1 : 0,
                unique_identifier: itemValue.unique_identifier,
                input_method_id: itemValue.input_method_id
            };
        });

        let data = {
            employee_id : userId,
            trans_request: {
                no_protocol: noProtocol,
                id_setup_cycle: parseInt(idSetupCycle),
                overall_status: isIncomplete ? 0 : 1,
                id_detail_batch: parseInt(id_detail_batch),
                batch_number: batchName[0],
            },
            detail_cycle_result: detailData
        };
        console.log(data)

        const { status, message } = await PostHasilAnalisa(data);
        if (status === 200) {
            toaster.push(
                Messages("success", message ? message : "Insertion success !"),
                {
                    placement: "topEnd",
                    duration: 3000,
                }
            );
            router.push('/user_module/protocol/analisa/PengisianHasilAnalisa');
            return;
        }

        toaster.push(
            Messages("error", message ? message : "Insertion failed !"),
            {
                placement: "topEnd",
                duration: 3000,
            }
        );
        setIsSubmitButtonDisabled(false);
        return;
    };

    return (
        <>
            <div>
                <Head>
                    <title>Pengisian Hasil Analisa : Detail</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <MainContent>
                    <ModuleContentHeader
                        breadcrumbs={breadcrumbsData}
                        module_name={moduleName}
                    />
                    <Stack style={{ margin: "2rem 0" }}>
                        <Panel shaded>
                            <Stack direction='row'>
                                Cycle Month : <Tag color='red'>{cycleMonthAndTimeframe.cycle_month}</Tag>
                                <Divider vertical />
                                Timeframe : <Tag color='orange'>{cycleMonthAndTimeframe.timeframe}</Tag>
                                <Divider vertical />
                                Batch number : {batchName}
                            </Stack>
                        </Panel>
                    </Stack>
                    <Form layout="horizontal">
                        <Stack alignItems="flex-start" direction="column" spacing={9}>
                            <div>
                                <p>Title Protocol:</p>
                                <Input
                                as="textarea"
                                value={title}
                                onChange={v=>setTitle(v)}
                                readOnly={true}
                                style={{ width: 224 }} 
                                />
                            </div>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Tujuan : </p>
                                <Editor contentValue={tujuan} isReadOnly={true} />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Ruang Lingkup : </p>
                                <Editor contentValue={ruangLingkup} isReadOnly={true} />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Dokumen Acuan : </p>
                                <Editor contentValue={dokumenAcuan} isReadOnly={true} />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p className="font-bold">Tanggung Jawab : </p>
                                <Editor contentValue={tanggungJawab} isReadOnly={true} />
                            </Stack>
                            <Stack
                                direction="column"
                                alignItems="flex-start"
                                style={{ marginTop: '1rem' }}
                            >
                                <p className="font-bold">Rancangan Studi : </p>
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p>Nama Produk : </p>
                                <Editor contentValue={namaProduk} isReadOnly={true} />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p>Kode Produk : </p>
                                <Editor contentValue={kodeProduk} isReadOnly={true} />
                            </Stack>
                            {/* <Stack direction="column" alignItems="flex-start">
                                <p>Nomor Batch : </p>
                                <Editor contentValue={nomorBatch} isReadOnly={true} />
                            </Stack> */}
                            <Stack direction="column" alignItems="flex-start">
                                <p>Nomor PPI : </p>
                                <Editor contentValue={nomorPPI} isReadOnly={true} />
                            </Stack>
                            <Stack direction="column" alignItems="flex-start">
                                <p>Batch Size : </p>
                                <Editor contentValue={batchSize} isReadOnly={true} />
                            </Stack>
                            <Form.Group
                                controlId="dibuatOleh"
                                style={{ marginBottom: '1em' }}
                            >
                                <Form.ControlLabel>
                                    <p className="text-xs">Dibuat Oleh :</p>
                                </Form.ControlLabel>
                                <Form.Control
                                    name="dibuatOleh"
                                    type="text"
                                    value={dibuatOleh}
                                    readOnly={true}
                                    required
                                />
                            </Form.Group>
                        </Stack>

                        <Form.Group
                            controlId="komponenPenyimpanan"
                            style={{ marginTop: '2em' }}
                        >
                            <Form.Group>
                                <Form.ControlLabel>
                                    <strong>Kondisi Penyimpanan :</strong>
                                </Form.ControlLabel>
                            </Form.Group>
                            <table className="table" style={{ maxWidth: 546 }}>
                                <thead>
                                    <tr>
                                        <th scope="col">No</th>
                                        <th scope="col" style={{ minWidth: 250 }}>
                                            Kriteria
                                        </th>
                                        {cycleMonthData.map((value) => (
                                            <th scope="col" className="text-center">
                                                {value}
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td scope="col">1</td>
                                        <td scope="col">T30 (30 &deg;C &plusmn; 2&deg;C / 75% &plusmn; 5% RH)</td>
                                        {cycleMonthData.map((item) => (
                                            <td scope="col">
                                                <input
                                                    type="checkbox"
                                                    name="T30"
                                                    value={item}
                                                    onChange={t30ChangeHandler}
                                                    checked={isChecked('T30', parseInt(item))}
                                                    disabled={true}
                                                />
                                            </td>
                                        ))}
                                    </tr>
                                    <tr>
                                        <td scope="col">2</td>
                                        <td scope="col">T40 (40 &deg;C &plusmn; 2&deg;C / 75% &plusmn; 5% RH)</td>
                                        {cycleMonthData.map((item) => (
                                            <td scope="col">
                                                <input
                                                    type="checkbox"
                                                    name="T40"
                                                    value={item}
                                                    onChange={t40ChangeHandler}
                                                    checked={isChecked('T40', parseInt(item))}
                                                    disabled={true}
                                                />
                                            </td>
                                        ))}
                                    </tr>
                                </tbody>
                            </table>
                        </Form.Group>

                        <Form.Group controlId="komposisiFormula">
                            <Stack>
                                <label htmlFor="komposisiFormula">
                                    <strong>Komposisi Formula :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <Editor contentValue={komposisiFormula} isReadOnly={true} />
                            </Stack>
                        </Form.Group>

                        <Form.Group controlId="keteranganTambahan">
                            <Stack>
                                <label htmlFor="keteranganTambahan">
                                    <strong>Keterangan Tambahan :</strong>
                                </label>
                            </Stack>
                            <Stack>
                                <Editor contentValue={additionalNotes} isReadOnly={true} />
                            </Stack>
                        </Form.Group>

                        <Divider />

                        <Form.Group controlId="parameterPengujianSpesifikasi">
                            <p className="mb-2">
                                <strong>Hasil Analisa :</strong>
                            </p>
                            <Stack direction="column" alignItems="flex-start">

                                {protocolDetailData?.length > 0 &&
                                    protocolDetailData.map((item) => {

                                        return (
                                            <Stack
                                                style={{
                                                    padding: '0.5em',
                                                    border: '0.1em solid #adadad',
                                                    borderRadius: '5px 5px 5px 5px',
                                                    marginBottom: '0.5em',
                                                }}
                                            >
                                                <Stack direction="column" alignItems="flex-start">
                                                    <p>
                                                        <strong>Parameter</strong>
                                                    </p>
                                                    <Input value={item.Parameter_Name} readOnly />
                                                </Stack>
                                                <Divider vertical />
                                                <Stack
                                                    direction="column"
                                                    alignItems="flex-start"
                                                    style={{ marginLeft: '0.5em' }}
                                                >
                                                    <p>
                                                        <strong>Kriteria Standard</strong>
                                                    </p>
                                                    {item.Input_Method_Desc == undefined && (
                                                        <Input disabled={true} />
                                                    )}
                                                    {item.Input_Method_Desc !== undefined &&
                                                        item.Input_Method_Desc !== null &&
                                                        item.Input_Method_Desc.toLowerCase() ===
                                                        'absolute' && (
                                                            <InputNumber
                                                                value={item.Value_Reference}
                                                                readOnly={true}
                                                            />
                                                        )}
                                                    {item.Input_Method_Desc !== undefined &&
                                                        item.Input_Method_Desc !== null &&
                                                        item.Input_Method_Desc.toLowerCase() ===
                                                        'description' && (
                                                            <Input
                                                                as="textarea"
                                                                value={item.Desc}
                                                                type="text"
                                                                readOnly={true}
                                                            />
                                                        )}
                                                    {item.Input_Method_Desc !== undefined &&
                                                        item.Input_Method_Desc !== null &&
                                                        item.Input_Method_Desc.toLowerCase() ===
                                                        'range' && (
                                                            <Stack spacing={3}>
                                                                <InputNumber
                                                                    value={item.Min_Value}
                                                                    readOnly={true}
                                                                    style={{ maxWidth: '6em' }}
                                                                />{' '}
                                                                -{' '}
                                                                <InputNumber
                                                                    value={item.Max_Value}
                                                                    readOnly={true}
                                                                    style={{ maxWidth: '6em' }}
                                                                />
                                                            </Stack>
                                                        )}
                                                </Stack>
                                                <Divider vertical />
                                                <Stack direction='column' alignItems='flex-start' className='px-2 py-2 border-2 border-yellow-200 rounded-md bg-yellow-100'>
                                                    <p><strong>Hasil</strong></p>
                                                    {item.Input_Method_Desc !== undefined &&
                                                        item.Input_Method_Desc !== null &&
                                                        item.Input_Method_Desc.toLowerCase() ===
                                                        'absolute' && (
                                                            <InputNumber
                                                                onChange={value => hasilChangeHandler(item.Unique_Identifier, value, item.Input_Method_Id)}
                                                            />
                                                        )}
                                                    {item.Input_Method_Desc !== undefined &&
                                                        item.Input_Method_Desc !== null &&
                                                        item.Input_Method_Desc.toLowerCase() ===
                                                        'range' && (<Stack spacing={6}>
                                                            <InputNumber style={{ width: '6em' }} onChange={value => hasilChangeHandler(item.Unique_Identifier, value, item.Input_Method_Id, false)} />
                                                        </Stack>
                                                        )}
                                                    {item.Input_Method_Desc !== undefined &&
                                                        item.Input_Method_Desc !== null &&
                                                        item.Input_Method_Desc.toLowerCase() ===
                                                        'description' && (
                                                            <Input
                                                                as="textarea"
                                                                onChange={value => hasilChangeHandler(item.Unique_Identifier, value, item.Input_Method_Id)}
                                                            />
                                                        )}
                                                </Stack>
                                                {item.Input_Method_Desc !== undefined && item.Input_Method_Desc !== null && item.Input_Method_Desc.toLowerCase() !== 'description' &&
                                                    <Stack direction='row'>
                                                        <Divider vertical />
                                                        <Stack direction='column' alignItems='flex-start'>
                                                            <p><strong>Comply</strong></p>
                                                            <CheckboxGroup name="checkBoxComply" value={complyValue}>

                                                                <Checkbox key={item.Unique_Identifier} value={item.Unique_Identifier} readOnly />

                                                            </CheckboxGroup>
                                                        </Stack>
                                                    </Stack>
                                                    ||
                                                    <Stack direction='row'>
                                                        <Divider vertical />
                                                        <Stack direction='column' alignItems='flex-start'>
                                                            <p><strong>Comply</strong></p>
                                                            <CheckboxGroup
                                                                inline
                                                                name="checkBoxComply"
                                                                value={complyValue}
                                                                onChange={value => {
                                                                    setComplyValue(value);
                                                                }}
                                                            >
                                                                <Checkbox key={item.Unique_Identifier} value={item.Unique_Identifier} />
                                                            </CheckboxGroup>
                                                        </Stack>
                                                    </Stack>
                                                }
                                            </Stack>
                                        );
                                    })}
                            </Stack>
                        </Form.Group>

                        <>
                            <Form.Group>
                                <Button
                                    onClick={submitHandler}
                                    appearance="primary"
                                    type="submit"
                                    disabled={isSubmitButtonDisabled}
                                >
                                    Submit
                                </Button>
                                <Button
                                    appearance="default"
                                    onClick={() => {
                                        setIsSubmitButtonDisabled(true);
                                        router.back();
                                    }}
                                    disabled={isSubmitButtonDisabled}
                                >
                                    Cancel
                                </Button>
                            </Form.Group>
                        </>
                    </Form>
                </MainContent>
            </ContainerLayout>
        </>
    );
}

export async function getServerSideProps({ query }) {
    const { noProtocol, idSetupCycle, id_detail_batch } = query;

    return {
        props: {
            noProtocol,
            idSetupCycle,
            id_detail_batch,
        },
    };
}
