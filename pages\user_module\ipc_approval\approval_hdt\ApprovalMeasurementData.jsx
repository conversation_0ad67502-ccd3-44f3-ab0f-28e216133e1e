import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import MainContent from "@/components/layout/MainContent";
import {
  Dropdown,
  Button,
  Modal,
  Placeholder,
  Form,
  Stack,
  DatePicker,
  Input,
} from "rsuite";
import { RemindIcon } from "@rsuite/icons";
import Table from "@/components/Table";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import * as faIcon from "@fortawesome/free-solid-svg-icons";
import ipcApi from "@/pages/api/ipcApi";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import {
  loadingAnimationNoClick,
  hideLoadingAnimation,
} from "@/components/SweetAlertLoading";
import UseTestApi from "@/pages/api/userApi";
import ModuleContentHeader from "@/components/ModuleContentHeader";

export default function ApprovalMeasurementData({
  setInterData,
  setPage,
  interComponentData,
  parentMenuName,
  childMenuName,
}) {
  const [employeeId, setEmployeeId] = useState("");
  const router = useRouter();
  const MySwal = withReactContent(Swal);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const {
    GetIpcHeaderByDate,
    GetMsReason,
    GetIpcDetailData,
    PostApprovalApi,
    ValidateApproval,
    GetIpcHeaderAllDataNeedApprove,
  } = ipcApi();
  const [formattedDateValue, setFormattedDateValue] = useState("");
  const [isRequest, setIsRequest] = useState(false);
  const [ipcHeaderData, setIpcHeaderData] = useState([]);
  const [ipcDetailData, setIpcDetailData] = useState([]);
  const [detailReason, setDetailReason] = useState([]);
  const [reasonData, setReasonData] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [password, setPassword] = useState("");
  const [moduleName, setModuleName] = useState("");
  var [breadcrumbsData, setBreadcrumbsData] = useState([]);
  //const breadcrumbsData = [moduleName, parentMenuName, childMenuName];

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");
    if (dataLogin === "" || dataLogin === null || dataLogin === undefined) {
      router.push("/");
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ""
    ) {
      router.push("/dashboard");
      return;
    }

    const asPathWithoutQuery = router.asPath.split("?")[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split("/")
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      `${asPathNestedRoutes[1]}`,
      `${asPathNestedRoutes[2]}`,
    ];
    setBreadcrumbsData(routerBreadCrumbsData);

    setModuleName(moduleNameValue);
    setEmployeeId(dataLogin.employee_id);
  }, []);

  useEffect(() => {
    if (isRequest) {
      if (formattedDateValue !== "") {
        GetIpcHeaderData(formattedDateValue);
      }
    }
  }, [isRequest, formattedDateValue]);

  useEffect(() => {
    // convert date
    const formattedDate = dateFormatter(selectedDate);
    setFormattedDateValue(formattedDate);
  }, [selectedDate]);


  useEffect(()=>{
    GetIpcNeedApprove()
  },[])

  const GetIpcHeaderData = async (date) => {
    const { Data } = await GetIpcHeaderByDate(date);
    if (Data) {
      const dateFormattedData = Data.map((item) => {
        const initialDate = item.Date;
        const formattedDate = initialDate.split("T")[0];
        return { ...item, Date: formattedDate };
      });
      setIpcHeaderData(dateFormattedData);
    } else {
      setIpcHeaderData([]);
      // MySwal.fire({
      //   icon: "error",
      //   title: "Oops...",
      //   text: "NO DATA FOUND in that specified date time !",
      // });
    }

    setIsRequest(false);
  };

  const GetIpcNeedApprove = async () => {
    try {
      const { Data } = await GetIpcHeaderAllDataNeedApprove();
      if (Data) {
        const dateFormattedData = Data.map((item) => {
          const initialDate = item.Date;
          const formattedDate = initialDate.split("T")[0];
          return { ...item, Date: formattedDate };
        });
        setIpcHeaderData(dateFormattedData);
      } else {
        setIpcHeaderData([]);
      }
      
    } catch (error) {
      console.log(error)
      setIpcHeaderData([]);
    }
    
  };

  let { data } = router.query;
  data = data ? JSON.parse(data) : null;

  const fetchMsReason = async () => {
    const { Data } = await GetMsReason();
    return Data;
  };

  const detailReasonController = (detailData) => {
    // menangkap detail reason dari table
    if (detailData) {
      setDetailReason(detailData);
    }
  };

  const fetchIpcDetail = async (objectData) => {
    let reqData = {
      id_transaction: objectData.id_transaction,
    };

    const { Data } = await GetIpcDetailData(reqData);
    return Data;
  };

  // Date change handler
  const dateFormatter = (date) => {
    const today = new Date(date);
    const year = `${today.getFullYear()}`;
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");
    const result = `${year}-${month}-${day}`;
    return result;
  };

  const handleClose = () => {
    setIsModalOpen(false);
  };

  const refreshHandler = (event) => {
    event.preventDefault();
    // validasi user sudah memilih date
    if (!selectedDate) {
      // MySwal.fire({
      //   icon: "error",
      //   title: "Oops...",
      //   text: "You have NOT selected any date time !",
      // });
      GetIpcNeedApprove()
      return;
    }

    setIsRequest(true);
  };

  const showMeasurementDetail = async (objectData) => {
    if (objectData.id_transaction) {
      MySwal.showLoading();
      const dataReason = await fetchMsReason();
      const dataDetail = await fetchIpcDetail(objectData);

      setReasonData(dataReason);
      setIpcDetailData(dataDetail);

      setIsModalOpen(true);
      MySwal.close();
      return;
    } else {
      MySwal.fire({
        title: "Error",
        icon: "warning",
        text: "No Data found.",
      });
      return;
    }
  };

  const submitApprovalHandler = async () => {
    const tmsAmount = ipcDetailData.filter(
      (item) => item.Overall_Status === "TMS"
    );

    if (tmsAmount.length !== detailReason.length) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "You HAVE NOT set reasons for all TMS data !",
      });
      return;
    }

    if (password === "") {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please enter your password !",
      });
      return;
    }

    const reqData = {
      emp_id: employeeId,
      id_transaction: interComponentData.id_transaction,
      reason_details: detailReason,
    };

    const validationApproval = {
      employee_id: employeeId,
      password: password,
    };

    const validateUser = await ValidateApproval(validationApproval);
    const { Data: userData } = validateUser;
    const { error } = validateUser;

    if (userData) {
      loadingAnimationNoClick();
      const { Data } = await PostApprovalApi(reqData);
      if (Data) {
        MySwal.fire({
          position: "center",
          icon: "success",
          title: "Your transaction is saved.",
          showConfirmButton: false,
          timer: 2500,
        });
        //setIpcHeaderData([]);
        setIsModalOpen(false);
        GetIpcNeedApprove()

        //setIsRequest(true);
      }
      return;
    }
    if (error) {
      MySwal.fire({
        icon: "error",
        title: "Validation FAILED !",
        text: error,
      });
      return;
    }

    // const validateUser = await UserLoginApi(validationApproval);
    // if (!validateUser) {
    //   MySwal.fire({
    //     icon: "error",
    //     title: "Invalid Password !",
    //   });
    //   return;
    // } else {
    //   loadingAnimationNoClick();
    //   const { Data } = await PostApprovalApi(reqData);
    //   if (Data) {
    //     MySwal.fire({
    //       position: "center",
    //       icon: "success",
    //       title: "Your transaction is saved.",
    //       showConfirmButton: false,
    //       timer: 2500,
    //     });
    //     setIpcHeaderData([]);
    //     setIsModalOpen(false);

    //     setIsRequest(true);
    //   }
    // }
  };

  return (
    <>
      <div>
        <Head>
          <title>Approval Reason Setup</title>
        </Head>
      </div>

      <MainContent>
        {/* <div className="mb-3">
          <h4>PIMS - Menu</h4>
          <p>In Process Control &gt; Approval Measurement</p>
        </div> */}
        <ModuleContentHeader
          breadcrumbs={breadcrumbsData}
          module_name={moduleName}
        />

        <div className="p-2">
          <form>
            <Form.Group controlId="select_date">
              <Stack spacing={10}>
                <Form.ControlLabel>Select Date : </Form.ControlLabel>
                <DatePicker
                  value={selectedDate}
                  onChange={(value) => setSelectedDate(value)}
                />
                <Form.ControlLabel>
                  <Button appearance="primary" onClick={refreshHandler}>
                    Refresh
                  </Button>
                </Form.ControlLabel>
                <Form.ControlLabel>
                  <Button appearance="primary" color="green" onClick={()=>{
                    router.push("/user_module/ipc_approval/approval_ma")
                  }}>
                    Approval Moisture Analyzer
                  </Button>
                </Form.ControlLabel>
                <Form.ControlLabel>
                  <Button appearance="primary" color="green" onClick={()=>{
                    router.push("/user_module/ipc_approval/approval_scale")
                  }}>
                    Approval Timbangan
                  </Button>
                </Form.ControlLabel>
              </Stack>
            </Form.Group>
          </form>
        </div>

        <Table
          ipcHeader={ipcHeaderData}
          setPage={setPage}
          setInterData={setInterData}
          interComponentData={interComponentData}
          showMeasurementDetail={showMeasurementDetail}
        />

        <Modal size="full" open={isModalOpen} onClose={handleClose}>
          <Modal.Header>
            <Modal.Title>
              <h5>Set Approval Reason</h5>
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Table
              ipcDetailData={ipcDetailData}
              reasonData={reasonData}
              detailReasonHandler={detailReasonController}
            />
          </Modal.Body>
          <Modal.Footer>
            <Stack direction="row" justifyContent="space-between">
              <Stack>
                <Form.Group controlId="password-for-validation">
                  <Stack spacing={6}>
                    <Form.ControlLabel>
                      Enter your password :{" "}
                    </Form.ControlLabel>
                    <Input
                      type="password"
                      name="password-for-validation"
                      id="password-for-validation"
                      placeholder="Password"
                      onChange={(value) => setPassword(value)}
                    />
                  </Stack>
                </Form.Group>
              </Stack>

              <Stack>
                <Button onClick={handleClose} appearance="subtle">
                  Cancel
                </Button>
                <Button onClick={submitApprovalHandler} appearance="primary">
                  Ok
                </Button>
              </Stack>
            </Stack>
          </Modal.Footer>
        </Modal>
      </MainContent>
    </>
  );
}
