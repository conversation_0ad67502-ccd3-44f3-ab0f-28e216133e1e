import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, SelectPicker, Grid, Row, Col, FlexboxGrid } from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiMain from "@/pages/api/oee/main/api_main";
import ApiBinding from "@/pages/api/oee/binding/api_binding";
import ApiMasterChildCategory from "@/pages/api/oee/master_child_category/api_master_child_category";
import { useRouter } from "next/router";

export default function index() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_main_category");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [idRouter, setIdRouter] = useState(null);
  const [mainBindingCategoryDataState, setMainBindingCategoryDataState] = useState([]);
  const [masterChildCategoryDataState, setMasterChildCategoryuDataState] = useState([]);

  const router = useRouter();
  const { Id } = router.query;

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [addLoading, setAddLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(null);

  const emptyAddDetailCategoryForm = {
    id_main_category: Id,
    id_child_category: "",
  };

  const emptyEditDetailCategoryForm = {
    id_binding_category: "",
    id_main_category: Id,
    id_child_category: "",
  };

  const [addDetailCategoryForm, setAddDetailCategoryForm] = useState(emptyAddDetailCategoryForm);
  const [editDetailCategoryForm, setEditDetailCategoryForm] = useState(emptyEditDetailCategoryForm);
  const [errorsAddDetailForm, setErrorsAddDetailForm] = useState({});
  const [errorsEditDetailForm, setErrorsEditDetailForm] = useState({});
  const [formDataMain, setFormDataMain] = useState({});

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = mainBindingCategoryDataState.filter((rowData) => {
    const searchFields = ["id_binding_category", "child_category_name", "create_date", "create_by", "update_date", "update_by", "delete_date", "delete_by", "is_active"];

    const matchesSearch = searchFields.some((field) => {
      const fieldValue = rowData[field];
      return fieldValue && fieldValue.toString().toLowerCase().includes(searchKeyword.toLowerCase());
    });

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return [...filteredData].sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.toLowerCase();
          y = y.toLowerCase();
        }
        if (sortType === "asc") {
          return x > y ? 1 : x < y ? -1 : 0;
        } else {
          return x < y ? 1 : x > y ? -1 : 0;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : mainBindingCategoryDataState.length;

  const HandleGetMainCategory = async (id_main_category) => {
    try {
      const apiMainCategory = ApiMain();
      const response = await apiMainCategory.getMasterMainById({ id_main_category: parseInt(id_main_category) });
      if (response.status === 200) {
        const data = response.data;
        setFormDataMain({
          id_main_category: data.id_main_category,
          category_name: data.category_name,
          category_type: data.category_type,
          is_active: data.is_active,
          create_date: data.create_date ? new Date(data.create_date).toLocaleDateString("en-GB") : "-",
          create_by: data.create_by || "-",
          update_date: data.update_date ? new Date(data.update_date).toLocaleDateString("en-GB") : "-",
          update_by: data.update_by || "-",
          delete_date: data.delete_date ? new Date(data.delete_date).toLocaleDateString("en-GB") : "-",
          delete_by: data.delete_by || "-",
        });
        return data;
      } else {
        console.error("Failed to fetch detail data");
        return null;
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
      return null;
    }
  };

  const HandleGetMainBindingCategory = async (id_main_category) => {
    try {
      const res = await ApiBinding().getBindingCategoryById({ id_main_category: parseInt(id_main_category, 10) });

      console.log("res", res);
      if (res.status === 200) {
        setMainBindingCategoryDataState(res.data);
      } else {
        console.log("Error on Get API: ", res.message);
      }
    } catch (error) {
      console.log("Error on catch Get API", error);
    }
  };

  const HandleGetAllMasterChildCategoryApi = async () => {
    try {
      const res = await ApiMasterChildCategory().GetAllMasterChildCategory();

      console.log("res", res);
      if (res.status === 200) {
        setMasterChildCategoryuDataState(res.data);
      } else {
        console.log("Error on Get API: ", res.message);
      }
    } catch (error) {
      console.log("Error on catch Get API", error);
    }
  };

  const HandleAddMainDetailCategory = async () => {
    const errors = {};

    // Validasi fields
    if (!addDetailCategoryForm.id_child_category) {
      errors.category_name = "Category name is required";
    }

    // Cek jika id_child_category sudah ada dalam data
    const isDuplicate = mainBindingCategoryDataState.some((row) => row.id_child_category === addDetailCategoryForm.id_child_category);

    if (isDuplicate) {
      errors.id_child_category = "Child Category ID already exists";
    }

    if (Object.keys(errors).length > 0) {
      setErrorsAddDetailForm(errors);
      return;
    }

    try {
      setAddLoading(true);

      // Pastikan id_main_category dikirim sebagai integer
      const id_main_category = parseInt(addDetailCategoryForm.id_main_category, 10);
      const res = await ApiBinding().createBindingCategory({
        ...addDetailCategoryForm,
        id_main_category,
        create_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
      });

      if (res.status === 201 || res.status === 200) {
        setAddDetailCategoryForm(emptyAddDetailCategoryForm);
        setShowAddModal(false);
        await HandleGetMainBindingCategory(id_main_category); // Update after successful creation
      }
    } catch (error) {
      console.error("Error adding main category:", error);
    } finally {
      setAddLoading(false);
    }
  };

  const HandleEditMainDetailCategory = async () => {
    const errors = {};

    if (!editDetailCategoryForm.id_child_category) {
      errors.id_child_category = "Child category is required";
    }

    // Cek jika id_child_category sudah ada dalam data
    const isDuplicate = mainBindingCategoryDataState.some(
      (row) => row.id_child_category === editDetailCategoryForm.id_child_category && row.id_binding_category !== editDetailCategoryForm.id_binding_category // Cek selain yang sedang diedit
    );

    if (isDuplicate) {
      errors.id_child_category = "Child Category ID already exists";
    }

    if (Object.keys(errors).length > 0) {
      setErrorsEditDetailForm(errors);
      return;
    }

    try {
      setEditLoading(true);

      const id_main_category = parseInt(editDetailCategoryForm.id_main_category, 10);
      const res = await ApiBinding().editBindingCategory({
        id_main_category,
        id_binding_category: editDetailCategoryForm.id_binding_category,
        id_child_category: editDetailCategoryForm.id_child_category,
        update_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
      });

      if (res.status === 200) {
        await HandleGetMainBindingCategory(id_main_category);
        setShowEditModal(false);
      }
    } catch (error) {
      console.error("Error updating binding category:", error);
    } finally {
      setEditLoading(false);
    }
  };

  const HandleEditStatusMainDetailCategory = async (id_binding_category, is_active) => {
    try {
      // Pastikan id_main_category diambil dari state atau router
      const id_main_category = parseInt(Id, 10); // Mengambil id_main_category dari router atau state

      const res = await ApiBinding().editStatusBindingCategory({
        id_binding_category,
        is_active,
        delete_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
      });

      if (res.status === 200) {
        console.log("Status update success:", res.message);
        await HandleGetMainBindingCategory(id_main_category); // Refresh data
      } else {
        console.log("Error on update status: ", res.message);
      }
    } catch (error) {
      console.log("Error on update status: ", error.message);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else if (Id) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("oee/master/main_category"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      // Pastikan id_main_category menjadi integer
      const parsedId = parseInt(Id, 10); // Convert to integer
      setIdRouter(parsedId);
      HandleGetMainBindingCategory(parsedId);
      HandleGetAllMasterChildCategoryApi();
      HandleGetMainCategory(parsedId);
      setAddDetailCategoryForm((prevForm) => ({
        ...prevForm,
        id_main_category: parsedId, // Assign integer value
      }));
    }
  }, [router, Id]);

  return (
    <div>
      <div>
        <Head>
          <title>Main Category Detail Page</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>OEE</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item>Main Category</Breadcrumb.Item>
                  <Breadcrumb.Item active>Detail</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-4"
            header={
              <Form fluid>
                <FlexboxGrid className="mb-4">
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>ID Main Category</Form.ControlLabel>
                      <Form.Control name="id_main_category" value={formDataMain.id_main_category || "-"} readOnly className="mb-3" />
                      <Form.ControlLabel>Category Type</Form.ControlLabel>
                      <Form.Control name="category_type" value={formDataMain.category_type === "P" ? "Planned" : formDataMain.category_type === "U" ? "Unplanned" : "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Category Name</Form.ControlLabel>
                      <Form.Control name="category_name" value={formDataMain.category_name || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                </FlexboxGrid>
                <FlexboxGrid>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                      <Form.Control name="create_date" value={formDataMain.create_date || "-"} readOnly className="mb-3" />
                      <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                      <Form.Control name="update_date" value={formDataMain.update_date || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                      <Form.Control name="create_by" value={formDataMain.create_by || "-"} readOnly className="mb-3" />
                      <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                      <Form.Control name="update_by" value={formDataMain.update_by || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                      <Form.Control name="delete_date" value={formDataMain.delete_date || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                      <Form.Control name="delete_by" value={formDataMain.delete_by || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                </FlexboxGrid>
              </Form>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="Search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn} autoHeight>
                <Column width={150} align="center" sortable>
                  <HeaderCell>ID Binding Category</HeaderCell>
                  <Cell dataKey="id_binding_category" />
                </Column>
                <Column width={250} sortable>
                  <HeaderCell align="center">Child Category Name</HeaderCell>
                  <Cell dataKey="child_category_name" />
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Creation Date</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Created By</HeaderCell>
                  <Cell dataKey="create_by" />
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Update Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Updated By</HeaderCell>
                  <Cell dataKey="update_by" />
                </Column>
                <Column width={175} sortable resizable align="center">
                  <HeaderCell>Deletion Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "-")}</Cell>
                </Column>
                <Column width={250} sortable resizable>
                  <HeaderCell align="center">Deleted By</HeaderCell>
                  <Cell dataKey="delete_by" />
                </Column>
                <Column width={120} sortable resizable align="center">
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Active" : "Inactive"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Actions</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModal(true);
                            setEditDetailCategoryForm({
                              ...editDetailCategoryForm,
                              id_binding_category: rowData.id_binding_category,
                              id_main_category: rowData.id_main_category,
                              id_child_category: rowData.id_child_category,
                            });
                          }}
                        >
                          <EditIcon />
                        </Button>
                        <Button appearance="subtle" onClick={() => HandleEditStatusMainDetailCategory(rowData.id_binding_category, rowData.is_active)} loading={statusLoading === rowData.id_binding_category}>
                          {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            {/* Modal for adding main category */}
            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                if (!addLoading) {
                  setShowAddModal(false);
                  setAddDetailCategoryForm(emptyAddDetailCategoryForm);
                  setErrorsAddDetailForm({});
                }
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Add Binding Category</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Child Category</Form.ControlLabel>
                    <SelectPicker
                      name="id_child_category"
                      value={addDetailCategoryForm.id_child_category}
                      data={masterChildCategoryDataState.map((category) => ({
                        label: category.child_category_name,
                        value: category.id_child_category,
                      }))}
                      onChange={(value) => {
                        setAddDetailCategoryForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_child_category: value,
                        }));
                        setErrorsAddDetailForm((prevErrors) => ({
                          ...prevErrors,
                          id_child_category: undefined, // Hapus error jika valid
                        }));
                      }}
                      disabled={addLoading}
                      style={{ width: "100%" }}
                    />
                    {errorsAddDetailForm.id_child_category && <p style={{ color: "red" }}>{errorsAddDetailForm.id_child_category}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddDetailCategoryForm(emptyAddDetailCategoryForm);
                    setErrorsAddDetailForm({});
                  }}
                  appearance="subtle"
                  disabled={addLoading}
                >
                  Cancel
                </Button>
                <Button onClick={HandleAddMainDetailCategory} appearance="primary" loading={addLoading} disabled={addLoading}>
                  Add
                </Button>
              </Modal.Footer>
            </Modal>
            {/* Modal for editing main category */}
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditDetailCategoryForm(emptyEditDetailCategoryForm);
                setErrorsEditDetailForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Edit Binding Category</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Child Category</Form.ControlLabel>
                    <SelectPicker
                      name="id_child_category"
                      value={editDetailCategoryForm.id_child_category} // Value is bound to the form state
                      data={masterChildCategoryDataState.map((category) => ({
                        label: category.child_category_name,
                        value: category.id_child_category,
                      }))}
                      onChange={(value) => {
                        setEditDetailCategoryForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_child_category: value, // Update form state with selected value
                        }));
                        setErrorsEditDetailForm((prevErrors) => ({
                          ...prevErrors,
                          id_child_category: undefined, // Remove error when valid
                        }));
                      }}
                      disabled={editLoading} // Disable during loading state
                      style={{ width: "100%" }}
                    />
                    {errorsEditDetailForm.id_child_category && <p style={{ color: "red" }}>{errorsEditDetailForm.id_child_category}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditDetailCategoryForm(emptyEditDetailCategoryForm);
                    setErrorsEditDetailForm({});
                  }}
                  appearance="subtle"
                  disabled={editLoading}
                >
                  Cancel
                </Button>
                <Button onClick={HandleEditMainDetailCategory} appearance="primary" loading={editLoading} disabled={editLoading}>
                  Edit
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
