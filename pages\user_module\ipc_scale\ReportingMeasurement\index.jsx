import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, SelectPicker, DatePicker } from "rsuite";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";

import ApiScaleHeader from "@/pages/api/ipc_scale/report/api_ipc_scale";
import ipcApi from "@/pages/api/ipcApi";

export default function IpcScaleReportingMeasurement() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_parameter");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();

  const { GetProductCodeForEntry } = ipcApi();

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [ScaleHeaderDataState, setScaleHeaderDataState] = useState([]);
  const [productCodes, setProductCodes] = useState([]);
  const [batchCodes, setBatchCodes] = useState([]);
  const [fixReportData, setFixReportData] = useState([]);
  const [filterParams, setFilterParams] = useState({
    product_code: null,
    batch_code: null,
    filter_date: null,
  });

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = ScaleHeaderDataState.filter((rowData, i) => {
    const searchFields = ["id_transaction", "product_code", "batch_code", "step", "station", "amount_ms", "amount_tms", "start_date", "employee_name"];
    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));
    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : ScaleHeaderDataState.length;

  const viewHandler = async (idTransactionH) => {
    const url = `${process.env.NEXT_PUBLIC_PIMS_FE}/user_module/ipc_scale/ReportingMeasurement/pdf?idTransactionH=${parseInt(idTransactionH)}`;
    window.open(url, "_blank");
  };

  const formatDateToDDMMYYYY = (dateStr) => {
    const date = new Date(dateStr);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  const HandleGetAllScaleHeader = async () => {
    try {
      const res = await ApiScaleHeader().getAllScaleHeader();
      console.log("res", res);
      if (res.status === 200) {
        setScaleHeaderDataState(res.data);
        setFixReportData(res.data);

        const distinctBatchCodes = [...new Set(res.data.map((transaction) => transaction.product_code))] // Pastikan ini sesuai dengan nama field di data
          .map((code) => ({ label: code, value: code }));
        setBatchCodes(distinctBatchCodes);
      } else {
        console.log("error on getAllScaleHeader ", res.message);
      }
    } catch (error) {
      console.log("error on catch getAllScaleHeader", error);
    }
  };

  const GetAllActiveProductCode = async () => {
    const { Data } = await GetProductCodeForEntry();
    if (Data) {
      console.log("produc code s", Data);
      const distinctProductCodes = Data.map((code) => ({ label: code.Product_Code, value: code.Product_Code }));
      console.log(distinctProductCodes);
      setProductCodes(distinctProductCodes);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("ipc"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandleGetAllScaleHeader();
      GetAllActiveProductCode();
    }
  }, []);

  useEffect(() => {
    const formattedFilterDate = filterParams.filter_date ? formatDateToDDMMYYYY(filterParams.filter_date) : null;

    const filteredData = ScaleHeaderDataState.filter((item) => {
      const isProductCodeMatch = !filterParams.product_code || item.product_code.includes(filterParams.product_code);
      const isBatchCodeMatch = !filterParams.batch_code || item.product_code === filterParams.batch_code;
      const formattedItemDate = item.start_date.split(" ")[0];
      const isDateMatch = !formattedFilterDate || formattedItemDate === formattedFilterDate;

      return isProductCodeMatch && isBatchCodeMatch && isDateMatch;
    });

    if (filterParams.batch_code || filterParams.product_code || filterParams.filter_date) {
      setScaleHeaderDataState(filteredData);
    } else {
      setScaleHeaderDataState(fixReportData);
    }
  }, [filterParams]);

  return (
    <div>
      <div>
        <Head>
          <title>Reporting Measurement</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>IPC Scale</Breadcrumb.Item>
                  <Breadcrumb.Item active>Report Measurement Timbangan</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Report Measurement Timbangan</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <SelectPicker
                      name="product code"
                      label="product Code"
                      value={filterParams.product_code}
                      block
                      data={productCodes}
                      valueKey="value"
                      labelKey="label"
                      onChange={(value) => {
                        setFilterParams({ ...filterParams, product_code: value });
                      }}
                    />
                    <br />
                    <SelectPicker
                      name="batch code"
                      label="Batch Code"
                      value={filterParams.batch_code}
                      block
                      data={batchCodes}
                      valueKey="value"
                      labelKey="label"
                      onChange={(value) => {
                        setFilterParams({ ...filterParams, batch_code: value });
                      }}
                    />
                    <br />
                    <DatePicker
                      value={filterParams.filter_date}
                      onChange={(value) => {
                        setFilterParams({ ...filterParams, filter_date: value });
                      }}
                    />
                  </div>

                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn} autoHeight>
                <Column width={120} align="center" sortable fullText>
                  <HeaderCell>ID Transaction</HeaderCell>
                  <Cell dataKey="id_transaction" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Product Code</HeaderCell>
                  <Cell dataKey="product_code" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Batch Code</HeaderCell>
                  <Cell dataKey="batch_code" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Step</HeaderCell>
                  <Cell dataKey="step" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Station</HeaderCell>
                  <Cell dataKey="station" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Jumlah MS</HeaderCell>
                  <Cell dataKey="amount_ms" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Jumlah TMS</HeaderCell>
                  <Cell dataKey="amount_tms" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Tanggal Dibuat</HeaderCell>
                  <Cell dataKey="start_date" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Operator Name</HeaderCell>
                  <Cell dataKey="employee_name" />
                </Column>
                <Column width={70} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button appearance="link" onClick={() => viewHandler(rowData.id_transaction)}>
                          <FileDownloadIcon style={{ fontSize: "16px" }} />
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
