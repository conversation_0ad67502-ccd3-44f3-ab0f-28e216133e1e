// Replace this key with a secure secret key
const secretKey = 'ay3UyE2UpVuXMOUxxEwDyFsqlGihYN5rZ4cz7oDabv+8QIqTLb2SnjA9RUduOlIx';

// Function to encrypt data
export const encryptData = (data) => {
  const cipher = crypto.createCipher('aes-256-cbc', secretKey);
  let encrypted = cipher.update(data, 'utf-8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
};

// Function to decrypt data
export const decryptData = (data) => {
  const decipher = crypto.createDecipher('aes-256-cbc', secretKey);
  let decrypted = decipher.update(data, 'hex', 'utf-8');
  decrypted += decipher.final('utf-8');
  return decrypted;
};