import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](
    `${process.env.NEXT_PUBLIC_REAGEN}/ts/${url}`,
    data
  )
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiTsdpTDSifting() {
  return {
    getAllTsTransactionSifting: createApiFunction(
      "get",
      "transaction-d-sifting/list"
    ),
    getTsTransactionSiftingById: createApiFunction(
      "post",
      "transaction-d-sifting/id"
    ),
    postTsTransactionSifting: createApiFunction(
      "post",
      "transaction-d-sifting/create"
    ),
    postTsTransactionSiftingAuto: createApiFunction(
      "post",
      "transaction-d-sifting/create-auto"
    ),
    UpdateTsTransactionSifting: createApiFunction(
      "put",
      "transaction-d-sifting/edit"
    ),
    UpdateStatusTsTransactionSifting: createApiFunction(
      "put",
      "transaction-d-sifting/edit-status"
    ),
  };
}
