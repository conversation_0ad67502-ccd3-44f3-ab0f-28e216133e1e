import axios from "axios";
// import fs from `fs-extra`;

export default function ModuleApi() {
  // API Mendapatkan Data Module
  const GetModule = async (id) => {
    const reqData = {
      employee_id: id,
    };

    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/module/get/module`,
        reqData
      )
      .then((res) => {
        return (data = res.data);
      })
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  // getModuleAll API
  const GetModuleAll = async () => {
    let data = [];
    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/module/get/all`)
      .then((res) => {
        return (data = res.data);
      })
      .catch((err) => console.log(err));
    return data;
  };

  // API Menambahkan Module
  const PostModuleApi = async (newModuleData) => {
    const moduleData = {
      module_name: newModuleData.module_name,
      module_icon: newModuleData.module_icon,
    };
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/module/post/module`,
        moduleData
      )
      .then((res) => {
        return (data = res.data);
      })
      .catch((err) => console.log(err));

    return data;
  };

  // API mendapatkan data module di tabel transmap
  const GetTransmapModuleApi = async (id) => {
    const reqData = {
      employee_id: id,
    };

    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/module/transmap/getmodule`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };

  // API Menambahkan Module ke Table Transmapping
  const PostTransmapApi = async (dataPost) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/module/transmap/module`,
        dataPost
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // API Menarik Data Module berdasarkan `module_code` nya
  const GetModuleById = async (requestData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/module/get/id`,
        requestData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };

  // API untuk mengupdate data module
  const UpdateModuleApi = async (updatedData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/module/update/module`,
        updatedData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };

  return {
    GetModule,
    GetModuleAll,
    PostModuleApi,
    GetModuleById,
    UpdateModuleApi,
    PostTransmapApi,
    GetTransmapModuleApi,
  };
}
