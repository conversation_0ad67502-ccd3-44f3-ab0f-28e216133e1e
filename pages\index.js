import LoginPage from "@/components/LoginPage";
import Head from "next/head";
import { useEffect, useState } from "react";
import ForgotPassword from "./ForgotPassword";
import { useRouter } from "next/router";

export default function Home() {
  const [pageIndex, setPageIndex] = useState("login");
  const router = useRouter();

  useEffect(()=>{
      const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));

      if (!dataLogin) {
          router.push(dataLogin ? "/dashboard" : "/");
      } else {
        router.push("/dashboard");
      }
  },[])

  const setupPageIndex = (index) => {
    setPageIndex(index);
  };
  return (
    <>
      <Head>
        <link rel="icon" href="/Logo_kalbe_header.png" />
        <title>KCH : PIMS</title>
      </Head>
      <LoginPage />
    </>
  );
}
