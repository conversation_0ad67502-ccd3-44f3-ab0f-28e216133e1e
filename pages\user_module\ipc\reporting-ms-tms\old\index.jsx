import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import { DateRange<PERSON>icker, Stack, Panel, Button, Breadcrumb } from "rsuite";

import API_IPC from "@/pages/api/api_ipc";
import XLSX from "xlsx";

import ContainerLayout from "@/components/layout/ContainerLayout";

import ChartDataLabels from "chartjs-plugin-datalabels";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
  ChartDataLabels
);

import { Line } from "react-chartjs-2";

export default function ReportingMsTms() {
  const router = useRouter();
  const [moduleName, setModuleName] = useState("");
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  // Calculate the date for 30 days ago
  const thirtyDaysAgo = new Date(new Date().setDate(new Date().getDate() - 30));

  // Set the start date to 30 days ago and end date to the current date
  const [startDate, setStartDate] = useState(thirtyDaysAgo);
  const [endDate, setEndDate] = useState(new Date());

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("ipc/reporting-ms-tms")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
    }
    setStartDate(thirtyDaysAgo);
    setEndDate(new Date());

    handleFetchData();
  }, []);

  const handleFetchData = async () => {
    setLoading(true);
    const data = await API_IPC().getIpcReportExcel({
      start_date: startDate,
      end_date: endDate,
    });
    console.log("data", data || []);
    setData(data.data || []);
    setLoading(false);
  };

  const formatDateToShort = (date) => {
    if (!(date instanceof Date)) {
      date = new Date(date);
    }
    let month = "" + (date.getMonth() + 1);
    let day = "" + date.getDate();
    const year = date.getFullYear();

    if (month.length < 2) month = "0" + month;
    if (day.length < 2) day = "0" + day;

    return [year, month, day].join("-");
  };

  const exportFile = (data) => {
    // Map data to a new array
    const formattedData = data.map((item) => ({
      Date: item.formatted_date,
      "TMS Count": item.tms_count,
      "MS Count": item.ms_count,
    }));

    // Generate worksheet from the new array
    const ws = XLSX.utils.json_to_sheet(formattedData);

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const formattedStartDate = formatDateToShort(startDate);
    const formattedEndDate = formatDateToShort(endDate);
    const fileName =
      "reporting-ms-tms" +
      " " +
      formattedStartDate +
      " to " +
      formattedEndDate +
      ".xlsx";

    XLSX.writeFile(wb, fileName);
  };

  const prepareChartData = (data) => {
    const labels = data.map((item) => item.formatted_date);
    const msCounts = data.map((item) => item.ms_count);
    const tmsCounts = data.map((item) => item.tms_count);

    return {
      labels,
      datasets: [
        {
          label: "MS Count",
          data: msCounts,
          backgroundColor: "#0f7141",
        },
        {
          label: "TMS Count",
          data: tmsCounts,
          backgroundColor: "#d32f2f",
        },
      ],
    };
  };

  const LineChart = ({ chartData }) => {
    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: "top",
        },
        title: {
          display: true,
          text: "MS and TMS Counts Over Time",
        },
        datalabels: {
          anchor: "end",
          align: "end",
          color: function (context) {
            return context.dataset.backgroundColor;
          },
          font: {
            weight: "bold",
          },
          formatter: (value, context) => {
            const formattedValue = value.toLocaleString();
            return formattedValue;
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return <Line data={chartData} options={options} />;
  };

  return (
    <>
      <div>
        <Head>
          <title>Reporting MS and TMS</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item active>
              {moduleName ? moduleName : "User Module"} / Reporting MS and TMS
            </Breadcrumb.Item>
          </Breadcrumb>
          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h4>Reporting MS and TMS</h4>
            </Stack>
          </Panel>
          <div>
            <Panel bordered>
              <Stack className="gap-2">
                <Stack.Item>
                  <p>Select date: </p>
                </Stack.Item>
                <Stack.Item>
                  <DateRangePicker
                    appearance="default"
                    value={[startDate, endDate]}
                    onChange={(value) => {
                      if (value === null) {
                        setStartDate(new Date());
                        setEndDate(new Date());
                        setData([]);
                      } else {
                        setStartDate(value?.[0]);
                        setEndDate(value?.[1]);
                      }
                    }}
                  />
                </Stack.Item>
                <Stack.Item>
                  <Button
                    appearance="primary"
                    onClick={() => handleFetchData()}
                  >
                    Fetch Data
                  </Button>
                </Stack.Item>
                <Stack.Item>
                  <Button
                    appearance="primary"
                    onClick={() => exportFile(data)}
                    disabled={data.length <= 0}
                  >
                    Export to Excel
                  </Button>
                </Stack.Item>
              </Stack>

              <LineChart
                chartData={prepareChartData(data)}
                plugins={[ChartDataLabels]}
                loading={loading}
              />
            </Panel>
          </div>
        </div>
      </ContainerLayout>
    </>
  );
}
