import { useEffect, useState } from "react";
import Head from "next/head";
import XLSX from "xlsx";
import {
  I<PERSON><PERSON><PERSON><PERSON>,
  Stack,
  Breadcrumb,
  Tag,
  Panel,
  Table,
  InputGroup,
  Input,
  Pagination,
  Button,
  Form,
  useToaster,
  Modal,
  Divider,
  Loader,
  SelectPicker,
} from "rsuite";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import API_InputOfferingRequestReagen from "@/pages/api/request_reagen/api_requestdata_list_request_data";
import ReagenManufactureApi from "@/pages/api/master_data/api_reagen_manufacture";

export default function InputOfferingRequestPage() {
  const [moduleName, setModuleName] = useState("");
  const [reagenDataOffering, setReagenDataOffering] = useState([]);
  const [props, setProps] = useState([]);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [sortColumn, setSortColumn] = useState();
  const [sortRequest, setSortRequest] = useState();

  const { HeaderCell, Cell, Column } = Table;

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [formErrors, setFormErrors] = useState({});

  const toaster = useToaster();

  const [reqOffering, setReqOffering] = useState([]);
  const [rowData, setRowData] = useState({});

  const [loading, setLoading] = useState(false);
  const [isButtonClicked, setIsButtonClicked] = useState(false);
  const [isButtonEditClicked, setIsButtonEditClicked] = useState(false);

  const [selectedIdReagenManufacture, setSelectedIdReagenManufacture] = useState(false);
  const [itemCodeInputed, setItemCodeInputed] = useState("")
  const [casNoInputed, setCasNoInputed] = useState("")
  const [haveCasNoInputed, setHaveCasNoInputed]= useState("")

  const [manufactureList, setManufactureList] = useState([])

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortRequest) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortRequest(sortRequest);
    }, 500);
  };

  const errorMessageStyle = {
    color: "red",
    fontSize: "12px",
    marginTop: "5px",
  };

  const handleSubmit = async (apiFunction) => {
    setIsButtonClicked(true);
    setLoading(true);
    const requiredFields = ["add_input_offer"];

    const errors = {};

    requiredFields.forEach((field) => {
      if (!formValue[field]) {
        errors[field] = `${field
          .split("_")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ")} is required.`;
      }
    });

    if (
      formValue.add_input_offer &&
      formValue.add_input_offer.some(
        (item) =>
          !item.vendor_name ||
          !item.price ||
          !item.link_attachments ||
          !item.remarks
      )
    ) {
      errors.add_input_offer = "All fields in input offer must be filled";
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      await apiFunction();
      setFormErrors({});
    } catch (error) {
      console.error("Error occurred while submitting:", error);
      setFormErrors({
        submitError:
          "An error occurred while submitting. Please try again later.",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGetAllApi = async () => {
    try {
      const reagenDataOffering =
        await API_InputOfferingRequestReagen().getAll();

      const filteredData = reagenDataOffering.data.filter(
        (item) => item.request_status != 0 
      );

      // const filteredData = reagenDataOffering.data

      // console.log("object",filteredData)

      setReagenDataOffering(filteredData || []);    
    } catch (error) {
      setReagenDataOffering([]);
    }
  };

  const [editFormValue, setEditFormValue] = useState([]);

  const fetchDetailData = async (selectedIdRequest) => {
    try {
      const resDetailData = await API_InputOfferingRequestReagen().getDetail({
        id_request_reagen: selectedIdRequest,
      });

      const data = resDetailData.data || [];

      // Initialize editFormValue with counts
      const updatedFormValue = data.map((item, index) => ({
        ...item,
        count: index + 1,
      }));

      setItemCodeInputed(updatedFormValue[0].item_code)
      setCasNoInputed(updatedFormValue[0].cas_no);
      setHaveCasNoInputed(updatedFormValue[0].cas_no);
      setEditFormValue(updatedFormValue);

      return data;
    } catch (error) {
      console.error("Error fetching detail data:", error);
      toaster.push(
        Messages("error", `Error: ${error.message}. Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
      return [];
    }
  };


  const handleEditDetail = async (selectedIdRequest) => {
    setIsButtonEditClicked(true);
    setLoading(true);
    console.log("clicked")
    try {
      const existingDetails = editFormValue.filter(
        (item) => item.id_detail_request
      );

      const detailOldData = existingDetails.map((item) => ({
        id_detail_request: item.id_detail_request,
        vendor_name: item.vendor_name,
        price: item.price,
        link_attachments: item.link_attachments,
        remarks: item.remarks,
        item_code:itemCodeInputed,
        updated_by: props.employee_id,
        updated_name: props.employee_id + " - " + props.employee_name,
        cas_no: casNoInputed,
        is_active: item.is_active
      }));

      const newDetails = editFormValue.filter(
        (item) => !item.id_detail_request
      );

      const detailNewData = newDetails.map((item) => ({
        vendor_name: item.vendor_name,
        price: item.price,
        link_attachments: item.link_attachments,
        remarks: item.remarks,
        created_by: item.created_by,
        item_code: itemCodeInputed,
        cas_no: casNoInputed,
      }));

      const requestBody = {
        id_request_reagen: selectedIdRequest,
        detail_old: detailOldData,
        detail_new: detailNewData,
        fu_raise: props.employee_id,
        created_by: props.employee_id,
      };

      console.log("request body ", requestBody)

      const updateResult = await API_InputOfferingRequestReagen().editDetailItemCode(
        requestBody
      );

      if (updateResult.status !== 200) {
        throw new Error("Error occurred while updating details.");
      }

      setShowEditModal(false);
      toaster.push(
        Messages("success", "Success editing and adding Request Reagen!"),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
      setEditFormValue([]);
      await handleGetAllApi();
    } catch (error) {
      console.error("Axios Error:", error);
      toaster.push(
        Messages("error", `Error: "${error.message}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    } finally {
      setLoading(false);
    }
  };


  const handleRemoveEditOffer = (index) => {
    const updatedInputOffer = editFormValue
      .filter((item, i) => i !== index)
      .map((item, i) => ({
        ...item,
        count: i + 1,
      }));

    setEditFormValue(updatedInputOffer);
  };

  const emptyFormValue = {
    id_request_reagen: null,
    id_manufacture:0,
    id_criteria_reagen:0,
    add_input_offer: [
      {
        id_detail_request: null,
        vendor_name: null,
        price: null,
        link_attachments: null,
        remarks: null,
        item_code: null,
        count: 1,
      },
    ],
    created_by: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);


  const handleUpdateVendorNameChange = (index, value) => {
    const updatedInputOffer = [...editFormValue];
    updatedInputOffer[index].vendor_name = value;
    setEditFormValue(updatedInputOffer);
  };

  const handleUpdatePriceChange = (index, value) => {
    const updatedInputOffer = [...editFormValue];
    updatedInputOffer[index].price = value;
    setEditFormValue(updatedInputOffer);
  };

  const handleUpdateLinkAttachments = (index, value) => {
    const updatedInputOffer = [...editFormValue];
    updatedInputOffer[index].link_attachments = value;
    setEditFormValue(updatedInputOffer);
  };

  const handleUpdateRemarks = (index, value) => {
    const updatedInputOffer = [...editFormValue];
    updatedInputOffer[index].remarks = value;
    setEditFormValue(updatedInputOffer);
  };

  const handleUpdateItemCode = (index, value) => {
    const updatedInputOffer = [...editFormValue];
    updatedInputOffer[index].item_code = value;
    setEditFormValue(updatedInputOffer);
  };

  const handleUpdateCasNo = (index, value) => {
    const updatedInputOffer = [...formValue.add_input_offer];
    updatedInputOffer[index].cas_no = value;
    setEditFormValue({
      ...editFormValue,
      add_input_offer: updatedInputOffer,
    });
  };

  const filteredData = reagenDataOffering
    .filter((rowData, i) => {
      const searchFields = [
        "id_request_reagen",
        "id_criteria_reagen",
        "reagen_name",
        "manufacture_desc",
        "item_list_no",
        "reagen_name",
        "catalogue",
        "msds",
        "cas_no",
        "submit_date",
        "submit_by",
        "raised_status",
        "raised_date",
        "raised_by",
        "fu_raise",
        "fu_date",
        "raise_remarks",
        "request_status",
        "created_dt",
        "created_by",
        "updated_dt",
        "updated_by",
        "deleted_dt",
        "deleted_by",
        "is_active",
      ];

      const matchesSearch = searchFields.some((field) =>
        rowData[field]
          ?.toString()
          .toLowerCase()
          .includes(searchKeyword.toLowerCase())
      );

      return matchesSearch;
    })

    .filter((v, i) => {
      // Pagination logic
      const start = limit * (page - 1);
      const end = start + limit;

      return i >= start && i < end;
    });

  const totalRowCount = searchKeyword
    ? filteredData.length
    : reagenDataOffering.length;

  const handleExportExcel = () => {
    try {
      const headerMapping = {
        id_request_reagen: "ID Request Reagen",
        // id_criteria_reagen: "ID Criteria Reagen",
        reagen_name: "Reagen Name",
        manufacture_desc: "Manufacture Description",
        item_list_no: "Item List No",
        catalogue: "Catalogue",
        msds: "MSDS",
        cas_no: "CAS No",
        submit_date: "Submit Date",
        submit_by: "Submit By",
        raise_remarks: "Raise Remarks",
        raised_by: "Raised By",
        raised_date: "Raised Date",
        raised_status: "Raised Status",
        fu_raise: "FU Raise",
        fu_date: "FU Date",
        request_status: "Request Status",
        created_dt: "Created Date",
        created_by: "Created By",
        updated_dt: "Updated Date",
        updated_by: "Updated By",
        deleted_dt: "Deleted Date",
        deleted_by: "Deleted By",
        is_active: "Is Active",
      };

      const formattedData = reagenDataOffering.map((item) => {
        const formattedItem = {};
        for (const key in item) {
          if (headerMapping[key]) {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
        return formattedItem;
      });

      const ws = XLSX.utils.json_to_sheet(formattedData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Reagen Data");

      const date = dateFormatterDash(new Date());
      XLSX.writeFile(wb, `InputOfferingReagen ${date}.xlsx`);

      toaster.push(
        Messages(
          "success",
          "Input Offering Reagen data file (.xlsx) downloaded successfully"
        ),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    } catch (error) {
      console.error("Error exporting Excel:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const getData = () => {
    if (sortColumn && sortRequest) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortRequest === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };


  const emptyDetailFormValue = {
    id_detail_request: null,
    vendor_name: null,
    price: null,
    link_attachments: null,
    remarks: null,
  };


  const fetchData = async () => {
    const res_detail_offer = await API_InputOfferingRequestReagen().getAll();
    setReqOffering(res_detail_offer.data || []);

    const manufacture_list = await ReagenManufactureApi().getAllActive()

    //setManufactureList(manufacture_list.data || []);
    setManufactureList(manufacture => [...manufacture,...manufacture_list.data || []]);
  };
  
  useEffect(() => {
    

    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("request_reagen/edit_request_reagen")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetAllApi();
      fetchData();
    }
  }, []);

  return (
    <>
      <div>
        <Head>
          <title>Input Offering Request Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        {/* Table */}
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Request Reagen</Breadcrumb.Item>
                  <Breadcrumb.Item active>
                    Input Offering Request Reagen
                  </Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              align="center"
              height={400}
              data={getData()}
              sortColumn={sortColumn}
              sortRequest={sortRequest}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={150} align="center" sortable resizable>
                <HeaderCell>Id Request Reagen</HeaderCell>
                <Cell dataKey="id_request_reagen" />
              </Column>
              {/* <Column width={150} align="center" sortable>
                <HeaderCell>ID Criteria Reagen</HeaderCell>
                <Cell dataKey="id_criteria_reagen" />
              </Column> */}
              <Column width={150} sortable resizable>
                <HeaderCell>Reagen Name</HeaderCell>
                <Cell dataKey="reagen_name" />
              </Column>
              <Column width={200} sortable resizable>
                <HeaderCell>Request Status</HeaderCell>
                <Cell align="left">
                  {(rowData) => {
                    let statusText = "";
                    let color = "";

                    switch (rowData.request_status) {
                      case 1:
                        statusText = "Purchasing Submit";
                        color = "green";
                        break;
                      case 2:
                        statusText = "Waiting for purchasing raised";
                        color = "red";
                        break;
                      case 3:
                        statusText = "Request fulfilled";
                        color = "green";
                        break;
                      default:
                        statusText = "Waiting for purchasing";
                        color = "red";
                        break;
                    }

                    return <span style={{ color }}>{statusText}</span>;
                  }}
                </Cell>
              </Column>
              <Column width={180} sortable resizable>
                <HeaderCell>Manufacture Description</HeaderCell>
                <Cell dataKey="manufacture_desc" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Item List No</HeaderCell>
                <Cell dataKey="item_list_no" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Catalogue</HeaderCell>
                <Cell dataKey="catalogue" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>MSDS</HeaderCell>
                <Cell dataKey="msds" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>CAS No</HeaderCell>
                <Cell dataKey="cas_no" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Submit Date</HeaderCell>
                <Cell dataKey="submit_date" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Submit - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.created_by} - {rowData.created_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Raise Remarks</HeaderCell>
                <Cell dataKey="raise_remarks" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Raised By</HeaderCell>
                <Cell dataKey="raised_by" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Raised Date</HeaderCell>
                <Cell dataKey="raised_date" />
              </Column>
              {/* <Column width={150} sortable resizable>
                <HeaderCell>Raised Status</HeaderCell>
                <Cell align="center">
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.raised_status === 1,
                      }}
                    >
                      {rowData.raised_status === 1
                        ? "Raised Request"
                        : "New Request"}
                    </span>
                  )}
                </Cell>
              </Column> */}
              <Column width={150} sortable resizable>
                <HeaderCell>FU Raise</HeaderCell>
                <Cell dataKey="fu_raise" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>FU Date</HeaderCell>
                <Cell dataKey="fu_date" />
              </Column>           
              <Column width={150} align="center" sortable resizable>
                <HeaderCell>Created Date</HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Created By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.created_by} - {rowData.created_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} align="center" sortable resizable>
                <HeaderCell>Updated Date</HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Updated By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.updated_by} - {rowData.updated_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} align="center" sortable resizable>
                <HeaderCell>Deleted Date</HeaderCell>
                <Cell dataKey="deleted_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Deleted By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.deleted_by} - {rowData.deleted_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={100} align="center" sortable resizable>
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={100} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell style={{ padding: "8px" }} align="center">
                  {(rowData) => (
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <div style={{ marginRight: "8px" }}>
                          <Button
                            appearance="link"
                            disabled={rowData.is_active === 0}
                            onClick={() => {
                              setFormErrors({});
                              setShowEditModal(true);
                              setIsButtonEditClicked(false);
                              setEditFormValue([{ count: 1 }]);
                              fetchDetailData(rowData.id_request_reagen);
                              setRowData(rowData)
                            }}
                          >
                            Edit
                          </Button>
                      </div>
                    </div>
                  )}
                </Cell>
              </Column>
              <Column width={120} fixed="right" align="center">
                <HeaderCell>Status</HeaderCell>
                <Cell style={{ padding: "8px" }} align="center">
                  {(rowData) => (
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <span
                        style={{
                          color: rowData.raised_status === 1,
                        }}
                      >
                        {rowData.raised_status === 1
                          ? "Raised Request"
                          : "New Request"}
                      </span>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>


        {/* Edit Modal Input Offer Reagen */}
        <Modal
          backdrop="static"
          open={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setEditFormValue([]);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Edit Offering Request Reagen</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {loading && (
              <Loader
                backdrop
                size="md"
                vertical
                content="Edit Data..."
                active={loading}
              />
            )}
            <Form fluid>
              <Form.Group>
              <>
                        <Form.ControlLabel>Reagen: <b>{rowData.reagen_name}</b></Form.ControlLabel>

                        <Form.ControlLabel>Brand: <b>{rowData.manufacture_desc}</b></Form.ControlLabel>
                        <Form.ControlLabel></Form.ControlLabel>

                        <br />
                        <Form.ControlLabel>item_code</Form.ControlLabel>
                          <Input
                          placeholder="Item_code"
                          value={itemCodeInputed || ""}
                          onChange={(e) => setItemCodeInputed(e)}
                          />
                          <br />
                          <br />
                      </>


                      {haveCasNoInputed != "" && (
                         <>
                         <Form.ControlLabel>Cas No</Form.ControlLabel>
                         <Input
                           placeholder="Cas_no"
                           value={casNoInputed || ""}
                           onChange={(e) => setCasNoInputed(e)}
                         />
                     </>
                      )}
                      <hr />
                <Panel bordered>
                  {editFormValue.map((detail, index) => (
                    <Panel
                      className="mb-4 rounded-lg p-2"
                      bordered
                      key={index}
                      shaded
                    >
                      <div className="flex flex-column gap-4">
                        <div>
                          <Form.ControlLabel>Vendor Name</Form.ControlLabel>
                          <Input
                            placeholder="Vendor Name"
                            value={detail.vendor_name || ""}
                            onChange={(e) =>
                              handleUpdateVendorNameChange(index, e)
                            }
                            disabled={true}
                          />
                        </div>
                        <div>
                          <Form.ControlLabel>Price</Form.ControlLabel>
                          <Input
                            placeholder="Price"
                            value={detail.price || ""}
                            onChange={(value) =>
                              handleUpdatePriceChange(index, parseInt(value))
                            }
                            disabled={true}
                          />
                        </div>
                        <div>
                          <Form.ControlLabel>
                            Link Attachments
                          </Form.ControlLabel>
                          <Input
                            placeholder="Link Attachments"
                            value={detail.link_attachments || ""}
                            onChange={(e) =>
                              handleUpdateLinkAttachments(index, e)
                            }
                            disabled={true}
                          />
                        </div>
                        <div>
                          <Form.ControlLabel>Remarks</Form.ControlLabel>
                          <Input
                            placeholder="Remarks"
                            value={detail.remarks || ""}
                            onChange={(e) => handleUpdateRemarks(index, e)}
                            disabled={true}
                          />
                        </div>
                        <div>
                          <Form.ControlLabel>Item Code</Form.ControlLabel>
                          <Input
                            placeholder="Item_code"
                            value={itemCodeInputed || ""}
                            onChange={(e) => handleUpdateItemCode(index, e)}
                            readOnly
                          />
                        </div>
                        <div>
                           <Form.ControlLabel>Cas No</Form.ControlLabel>
                           <Input
                             placeholder="Cas_no"
                             value={casNoInputed || ""}
                             onChange={(e) => {
                              //  console.log("item code", e
                              //  )
                              handleUpdateCasNo(index, e)
                             }}
                             readOnly
                           />
                         </div>
                        {!detail.id_detail_request && (
                          <Button
                            color="red"
                            appearance="primary"
                            onClick={() => handleRemoveEditOffer(index)}
                            hidden={editFormValue.length <= 1}
                          >
                            Remove
                          </Button>
                        )}
                        <Divider>{detail.count}</Divider>
                      </div>
                    </Panel>
                  ))}
                </Panel>
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setIsButtonEditClicked(false);
                setFormErrors({});
                setShowEditModal(false);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                console.log("here", isButtonEditClicked)
                if (!isButtonEditClicked) {
                  handleEditDetail(editFormValue[0].id_request_reagen);
                }
              }}
              appearance="primary"
              type="button"
              disabled={isButtonEditClicked}
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
