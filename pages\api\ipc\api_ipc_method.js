import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};


export default function ApiIpcMethod() {
  return {
    getAllIPCMethod: createApiFunction("get", "v2/ipc/ipc_method/list"),
    getAllActiveIPCMethod: createApiFunction("get", "v2/ipc/ipc_method/list-active"),
    createIPCMethod: createApiFunction("post", "v2/ipc/ipc_method/create"),
    editIPCMethod: createApiFunction("put", "v2/ipc/ipc_method/edit"),
    editStatusIPCMethod: createApiFunction("put", "v2/ipc/ipc_method/edit-status"),
  };
}
