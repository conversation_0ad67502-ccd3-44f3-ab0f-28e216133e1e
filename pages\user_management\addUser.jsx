import MainContent from "@/components/layout/MainContent";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import UseTestApi from "../api/userApi";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import { Dropdown } from "rsuite";
import departmentApi from "../api/departmentApi";

function AddUser({ allDepartment }) {
  const { PostUserTestApi } = UseTestApi();
  const router = useRouter();
  const MySwal = withReactContent(Swal);
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [employeeId, setEmployeeId] = useState("");
  const [employeeIdErrorMsg, setEmployeeIdErrorMsg] = useState("");
  const [isValidEmployeeId, setIsValidEmployeeId] = useState(true);
  const [employeeName, setEmployeeName] = useState("");
  const [email, setEmail] = useState("");
  const [password1, setPassword1] = useState("");
  const [password2, setPassword2] = useState("");
  const [isPasswordMatched, setIsPasswordMatched] = useState(true);
  const [isPasswordValid, setIsPasswordValid] = useState(true);
  const [division, setDivision] = useState("");
  const [department, setDepartment] = useState({});
  const [departmentOption, setDepartmentOption] = useState([]);
  const [isActive, setIsActive] = useState("1");
  const [jobTitle, setJobTitle] = useState('');

  // Handler
  function employeeIdHandler(event) {
    setEmployeeId(event.target.value);
  }
  function employeeNameHandler(event) {
    setEmployeeName(event.target.value);
  }
  function emailHandler(event) {
    setEmail(event.target.value);
  }
  function password1Handler(event) {
    setPassword1(event.target.value);
  }
  function password2Handler(event) {
    setPassword2(event.target.value);
  }
  function divisionHandler(event) {
    setDivision(event.target.value);
  }
  function isActiveHandler(event) {
    setIsActive(event.target.value);
  }
  function jobTitleHandler(event) {
    setJobTitle(event.target.value);
  }

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }

    if (
      allDepartment !== null &&
      allDepartment !== undefined &&
      allDepartment.length > 0
    ) {
      setDepartmentOption(allDepartment);
    }
  },[]);

  // Mengecek validitas employee_id
  useEffect(() => {
    // Mengecek jika karakternya tidak 9
    if (employeeId.length > 0) {
      if (employeeId.length !== 9) {
        setEmployeeIdErrorMsg("Employee ID must have 9 characters long.");
        setIsValidEmployeeId(false);
      } else {
        setIsValidEmployeeId(true);
      }
      return;
    }
    setIsValidEmployeeId(true);
    return;
  }, [employeeId]);

  // Mengecek kesamaan password
  useEffect(() => {
    if (password1.length > 0 || password2.length > 0) {
      if (password1 !== password2) {
        setIsPasswordMatched(false);
      } else {
        setIsPasswordMatched(true);
      }
      return;
    }
    setIsPasswordMatched(true);
    return;
  }, [password1, password2]);

  // Mengecek validitas password
  useEffect(() => {
    if (password1.length > 0) {
      const passwordToCheck = password1;
      var minimumChars = 6;
      var regularExpression =
        /^(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{6,16}$/;

      if (
        passwordToCheck.length < minimumChars ||
        !regularExpression.test(passwordToCheck)
      ) {
        setIsPasswordValid(false);
        return;
      }
      setIsPasswordValid(true);
      return;
    }
    setIsPasswordValid(true);
    return;
  }, [password1]);

  const addUserHandler = async (event) => {
    setIsFormDisabled(true);
    event.preventDefault();

    if (!isPasswordValid) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Invalid PASSWORD !",
      });
      setIsFormDisabled(false);
      return;
    }

    if (!isPasswordMatched) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Password is not the same !",
      });
      setIsFormDisabled(false);
      return;
    }

    if (
      department.Id_Setup === "" ||
      department.Id_Setup === null ||
      department.Id_Setup === undefined
    ) {
      setIsFormDisabled(false);
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please fill all required data !",
      });
      return;
    }

    // Mengubah nilai is_active menjadi integer
    let is_active = parseInt(isActive);

    let dataUser = {
      employee_id: employeeId,
      employee_name: employeeName,
      email: email,
      password: password1,
      division: division,
      department: parseInt(department.Id_Setup),
      is_active: is_active,
      job_title: jobTitle,
    };

    console.log(dataUser)

    // Send ke backend
    const { Data } = await PostUserTestApi(dataUser);
    if (Data) {
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "User data inserted successfully.",
        allowOutsideClick: false,
        timer: 2500,
      });
      router.push("/user_management");
    } else {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "User data insert FAILED.",
      });
      setIsFormDisabled(false);
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Add User</title>
        </Head>
      </div>
      <ContainerLayout
        title="User Management"
        customNavMenu={[
          { name: "Menu Management", route: "menu", icon: "faTableList" },
          { name: "Module Management", route: "module", icon: "faBook" },
          { name: "User Management", route: "user", icon: "faUserAlt" },
        ]}
      >
        <MainContent>
          <h4>Form Add User</h4>
          <div className="p-5">
            <form onSubmit={addUserHandler}>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Employee ID
                </label>
                <input
                  type="text"
                  className={`form-control ${
                    !isValidEmployeeId ? "is-invalid" : ""
                  }`}
                  id="employeeId"
                  value={employeeId}
                  onChange={employeeIdHandler}
                  autoFocus={true}
                  autoComplete="off"
                  aria-describedby="validationServer03Feedback"
                  required
                />
                {!isValidEmployeeId && (
                  <div
                    id="validationServer03Feedback"
                    className="invalid-feedback"
                  >
                    {employeeIdErrorMsg}
                  </div>
                )}
              </div>
              <div className="mb-3">
                <label htmlFor="employee_name" className="form-label">
                  Employee Name
                </label>
                <input
                  type="text"
                  autoComplete="off"
                  className="form-control"
                  id="employee_name"
                  aria-describedby="emailHelp"
                  value={employeeName}
                  onChange={employeeNameHandler}
                  required
                />
              </div>
              <div className="mb-3">
                <label htmlFor="email" className="form-label">
                  Email
                </label>
                <input
                  type="email"
                  className="form-control"
                  id="email"
                  autoComplete="off"
                  aria-describedby="emailHelp"
                  value={email}
                  onChange={emailHandler}
                  required
                />
              </div>
              <div className="mb-3">
                <label htmlFor="password1" className="form-label">
                  Password
                </label>
                <input
                  type="password"
                  className={`form-control ${
                    !isPasswordMatched || !isPasswordValid ? "is-invalid" : ""
                  }`}
                  id="password1"
                  value={password1}
                  onChange={password1Handler}
                  required
                />
              </div>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Re-type password
                </label>
                <input
                  type="password"
                  className={`form-control ${
                    !isPasswordMatched || !isPasswordValid ? "is-invalid" : ""
                  }`}
                  id="password2"
                  value={password2}
                  onChange={password2Handler}
                  aria-describedby="validationServer03Feedback"
                  required
                />
                <div style={{ color: "tomato" }}>
                  Password requirements :
                  <ul>
                    <li>6 characters minimum</li>
                    <li>
                      Contain minimum 1 uppercase character, 1 lowercase
                      character, number, and symbol (!@#$%^&*)
                    </li>
                  </ul>
                </div>
              </div>
              <div className="mb-3">
                <label htmlFor="division" className="form-label">
                  Division
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="division"
                  aria-describedby="emailHelp"
                  value={division}
                  onChange={divisionHandler}
                  autoComplete="off"
                  required
                />
              </div>
              <div className="mb-3">
                <label htmlFor="jobTitle" className="form-label">
                  Job Title
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="jobTitle"
                  aria-describedby="emailHelp"
                  value={jobTitle}
                  onChange={jobTitleHandler}
                  autoComplete="off"
                  required
                />
              </div>
              <div className="mb-3">
                <label>Department </label>
                <br />
                <Dropdown
                  title={
                    department.Department_Name !== undefined
                      ? department.Department_Name
                      : "-- Select Department --"
                  }
                  onSelect={(value) => setDepartment(value)}
                >
                  {/* <Dropdown.Item eventKey="">
                    -- Select Department --
                  </Dropdown.Item>
                  <Dropdown.Item eventKey="IT">IT</Dropdown.Item>
                  <Dropdown.Item eventKey="Finance">Finance</Dropdown.Item>
                  <Dropdown.Item eventKey="Digital Transformation">
                    Digital Transformation
                  </Dropdown.Item> */}
                  <Dropdown.Item eventKey={{}}>
                    -- Select Department --
                  </Dropdown.Item>
                  {departmentOption.length > 0 &&
                    departmentOption.map((department) => (
                      <Dropdown.Item
                        key={department.Id_Setup}
                        eventKey={department}
                      >
                        {department.Department_Name}
                      </Dropdown.Item>
                    ))}
                </Dropdown>
              </div>
              <label className="mb-2">Is_Active</label>
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="radio"
                  name="is_active"
                  id="is_active"
                  value="1"
                  checked={isActive === "1"}
                  onChange={isActiveHandler}
                />
                <label className="form-check-label" htmlFor="is_active">
                  Active
                </label>
              </div>
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="radio"
                  name="is_active"
                  id="non_active"
                  value="0"
                  checked={isActive === "0"}
                  onChange={isActiveHandler}
                />
                <label className="form-check-label mb-5" htmlFor="non_active">
                  Non-active
                </label>
              </div>

              {/* Button */}
              <button
                type="submit"
                disabled={
                  isFormDisabled || !isPasswordMatched || !isPasswordValid
                }
                className="btn btn-primary p-2"
              >
                Add User
              </button>
              <button
                type="button"
                disabled={isFormDisabled}
                className="btn btn-dark mx-2 p-2"
                onClick={() => router.back()}
              >
                Cancel
              </button>
            </form>
          </div>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export default AddUser;

export async function getServerSideProps() {
  const { GetAllDepartment } = departmentApi();

  const { data: departments } = await GetAllDepartment();

  let allDepartment = [];
  if (departments !== null && departments !== undefined) {
    allDepartment = departments;
  }

  return {
    props: {
      allDepartment,
    },
  };
}
