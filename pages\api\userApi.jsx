import axios from 'axios';

export default function UseTestApi() {
  // Get all user data
  const GetTestApi = async () => {
    let data = [];
    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/get/all`)
      .then((res) => {
        return (data = res.data);
      })
      .catch((err) => console.log(err));
    return data;
  };

  const GetAllUserOrderById = async () => {
    let data = [];
    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/get/allUserOrderById`)
      .then((res) => {
        return (data = res.data);
      })
      .catch((err) => console.log(err));
    return data;
  };

  //   Get user data by ID
  const GetByIdTestApi = async (id) => {
    const userId = {
      employee_id: id,
    };

    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/get/id`, userId)
      .then((res) => {
        return (data = res.data);
      })
      .catch((err) => console.log(`GAGAL`, err));
    return data;
  };

  //   Input user data
  const PostUserTestApi = async (newUserData) => {
    const userData = {
      employee_id: newUserData.employee_id,
      name: newUserData.employee_name,
      division: newUserData.division,
      department: newUserData.department,
      email: newUserData.email,
      password: newUserData.password,
      is_active: newUserData.is_active,
      job_title: newUserData.job_title,
    };
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/post/user`, userData)
      .then((res) => {
        return (data = res.data);
      })
      .catch((err) => console.log(err));

    return data;
  };

  //   edit user data
  const UpdateUserDataTestApi = async (updateData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/update/user`,
        updateData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };

  // edit user profile
  const UpdateProfileApi = async (updateData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/update/profile`,
        updateData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // login API
  const UserLoginApi = async (userLoginData) => {
    let data = null;
    let token = null;

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/login`, userLoginData)
      .then((res) => {
        data = res.data;
        token = res.token;
      })
      .catch((err) => {
        console.log(err);
        data = err.response.data;
      });
    return data;
  };

  // Ganti Password API
  const UpdatePasswordApi = async (dataPassword) => {
    let data;

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/update/password`,
        dataPassword
      )
      .then((res) => (data = res.data))
      .catch((err) => (data = err));

    return data;
  };

  // Forgot Password API
  const ForgotPasswordApi = async (reqData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/forgotpassword`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Reset Password API
  const ResetPasswordApi = async (reqData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/resetpassword`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };

  // Validate Token API
  const ValidateTokenAPI = async (reqData) => {
    let data = [];
    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/tokenvalidation`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };

  const GetUserAccessOld = async (reqData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/get/userAccessOld`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));
    return data;
  };

  // Setting UserAccess API
  const UserAccessApi = async (reqData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/set/userAccess`,
        reqData
      )
      .then((res) => (data = res.data))
      .catch((err) => console.log(err));

    return data;
  };


  // update first login API
  const UpdateFirstLogin = async (userLoginData) => {
    let data = null;
    let token = null;

    await axios
      .put(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/update/passwordfirstlogin`, userLoginData)
      .then((res) => {
        data = res.data;
        token = res.token;
      })
      .catch((err) => {
        console.log(err);
        data = err.response.data;
      });
    return data;
  };

    // login API
    const UserEresApi = async (userLoginData) => {
      let data = null;
      let token = null;
  
      await axios
        .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/user/eres`, userLoginData)
        .then((res) => {
          data = res.data;
          token = res.token;
        })
        .catch((err) => {
          console.log(err);
          data = err.response.data;
        });
      return data;
    };

  return {
    GetTestApi,
    GetAllUserOrderById,
    GetByIdTestApi,
    PostUserTestApi,
    UpdateUserDataTestApi,
    UserLoginApi,
    UpdatePasswordApi,
    UserAccessApi,
    ForgotPasswordApi,
    ValidateTokenAPI,
    ResetPasswordApi,
    UpdateProfileApi,
    GetUserAccessOld,
    UpdateFirstLogin,
    UserEresApi,
  };
}
