import { useRouter } from "next/router";
import MainContent from "@/components/layout/MainContent";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { Dropdown } from "rsuite";
import { useEffect, useState } from "react";
import MenuApi from "../api/menuApi";
import UseTestApi from "../api/userApi";
import Head from "next/head";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";

export default function UserAccessSetup({
  emp_id,
  emp_name,
  module_code,
  module_name,
  parent_menu_id,
  parent_menu_name,
  parent_menu_is_parent,
  parent_menu_link_code,
  childMenu,
  dataMenuOld,
}) {
  const router = useRouter();
  const { UserAccessApi } = UseTestApi();
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const MySwal = withReactContent(Swal);
  const [menuList, setMenuList] = useState([]);
  const [selectedMenu, setSelectedMenu] = useState([]);
  const [childMenuData, setChildMenuData] = useState([]);
  const [isSelectError, setIsSelectError] = useState(false);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  },[]);

  useEffect(() => {
    if (childMenu) {
      setChildMenuData(childMenu);
    }
    if (dataMenuOld) {
      const dataMenuOldString = dataMenuOld.map((item) => `${item}`);
      setSelectedMenu([...dataMenuOldString]);
    }
  }, [childMenu, dataMenuOld]);

  // handler checkbox
  const checkboxHandler = (event) => {
    const selectedItem = event.target.value;
    const isChecked = event.target.checked;

    if (isChecked) {
      // Menambahkan nilai ke array jika checkbox tercentang
      setSelectedMenu([...selectedMenu, selectedItem]);
    } else {
      // Menghapus nilai dari array jika checkbox tidak tercentang
      const updatedMenu = selectedMenu.filter((item) => item != selectedItem);
      setSelectedMenu(updatedMenu);
    }
  };

  // Handler awal checked
  const isChecked = (id) => {
    let result;
    for (let menuId of selectedMenu) {
      if (id === menuId) {
        result = true;
        break;
      } else {
        result = false;
      }
    }
    return result;
  };

  // handler submit
  const submitFormHandler = async (event) => {
    event.preventDefault();
    setIsFormDisabled(true);

    if (selectedMenu.length < 1) {
      setIsFormDisabled(false);
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please select a child menu !",
      });
      return;
    }

    // objek untuk dijadikan parameter request
    let reqData = {
      id_parent_menu: parseInt(parent_menu_id),
      employee_id: emp_id,
      module_code: parseInt(module_code),
      menu_struct: [],
    };

    // input menu_struct
    selectedMenu.map((item) => {
      reqData.menu_struct.push({
        Id_Parent_Menu: parseInt(parent_menu_id),
        Id_Child_Menu: parseInt(item),
      });
    });

    // request API
    const sendData = await UserAccessApi(reqData);

    // redirect jika berhasil
    if (sendData.Status === "OK") {
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "User Access updated successfully.",
        allowOutsideClick: false,
        timer: 2500,
      });
      router.push({
        pathname: "/user_management",
      });
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>User Access :: Child Menu Setup</title>
        </Head>
      </div>
      <ContainerLayout
        title="User Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <h4>Form User Access </h4>
          <h6></h6>
          <div className="p-5">
            <form onSubmit={submitFormHandler}>
              {isSelectError && (
                <div className="container bg-danger-subtle text-center rounded p-2">
                  <p>Anda belum memilih child elemen !</p>
                </div>
              )}
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Employee ID
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  value={emp_id}
                  id="employeeId"
                  aria-describedby="validationServer03Feedback"
                  disabled={true}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Employee Name
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  value={emp_name}
                  id="employeeName"
                  aria-describedby="validationServer03Feedback"
                  disabled={true}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Module Name
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  value={module_name}
                  id="moduleName"
                  aria-describedby="validationServer03Feedback"
                  disabled={true}
                />
              </div>

              <div className="row mb-3">
                <label>Pilih Parent menu : </label>
                <div className="col-sm-9">
                  <Dropdown title={parent_menu_name} disabled={true}></Dropdown>
                </div>
              </div>

              <div className="mb-3">
                <label>Pilih Child Menu : </label>
                <div classname="col-sm-9">
                  {childMenuData.length > 0
                    ? childMenuData.map((item) => (
                        <div>
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={isChecked(`${item.Id_Menu}`)}
                            value={`${item.Id_Menu}`}
                            id={`${item.Id_Menu}`}
                            onChange={checkboxHandler}
                          />
                          <label htmlFor={`${item.Id_Menu}`}>
                            {item.Menu_Name}
                          </label>
                        </div>
                      ))
                    : "No child menu found."}
                </div>
              </div>

              <button
                disabled={isFormDisabled}
                type="submit"
                className="btn btn-primary p-2"
              >
                Submit
              </button>
              <button
                type="button"
                disabled={isFormDisabled}
                className="btn btn-dark mx-2 p-2"
                onClick={() => router.back()}
              >
                Cancel
              </button>
            </form>
          </div>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  const {
    emp_id,
    emp_name,
    module_code,
    module_name,
    parent_menu_id,
    parent_menu_name,
    parent_menu_is_parent,
    parent_menu_link_code,
  } = query;

  const { GetUserAccessOld } = UseTestApi();
  const reqUserAccessOldData = {
    employee_id: emp_id,
    id_parent_menu: parseInt(parent_menu_id),
  };
  const { Data: menuOld } = await GetUserAccessOld(reqUserAccessOldData);
  let dataMenuOld = [];
  if (menuOld != undefined) {
    dataMenuOld = menuOld;
  }

  const { GetChildMenuDataApi } = MenuApi();
  const reqData = {
    parent_link_code: `%${parent_menu_link_code}/%`,
    module_code: parseInt(module_code),
  };
  const { Data: childMenuResult } = await GetChildMenuDataApi(reqData);
  let childMenu = [];
  if (childMenuResult != undefined) {
    childMenu = childMenuResult;
  }

  return {
    props: {
      emp_id,
      emp_name,
      module_code,
      module_name,
      parent_menu_id,
      parent_menu_name,
      parent_menu_is_parent,
      parent_menu_link_code,
      childMenu,
      dataMenuOld,
    },
  };
}
