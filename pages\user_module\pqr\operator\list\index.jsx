import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, Loader, RadioGroup, Radio, SelectPicker, Whisper, Tooltip } from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import EditIcon from "@rsuite/icons/Edit";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import { FormControl, FormControlLabel, FormGroup } from "@mui/material";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiMSCWeight from "@/pages/api/pqr/weight_msc/api_weight_msc";
import ApiRekonKaplet from "@/pages/api/pqr/rekon_kaplet/api_rekon_kaplet";
import ApiRekonFoil from "@/pages/api/pqr/rekon_foil/api_rekon_foil";
import ApiRekonSekunder from "@/pages/api/pqr/rekon_sekunder/api_rekon_sekunder";
import VisibleIcon from '@rsuite/icons/Visible';
import UnvisibleIcon from '@rsuite/icons/Unvisible';

export default function OperatorList() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_trans_header");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [transactionHeadersDataState, setTransactionHeadersDataState] = useState([]);
  const [ppiDataState, setPPIDataState] = useState([]);
  const [MSCDataState, setMSCDataState] = useState([]);
  const [rekonKapletDataState, setRekonKapletDataState] = useState([]);
  const [rekonFoilDataState, setRekonFoilDataState] = useState([]);
  const [rekonSekunderDataState, setRekonSekunderDataState] = useState([]);

  const emptyAddTransactionHeaderForm = {
    id_trans_header: null,
    id_ppi: null,
    batch_code: "",
    iot_desc: "",
    line_desc: "",
    remarks: "",
    wetmill: "N",
    status_transaction: 2,
    create_date: null,
    create_by: "",
    update_date: null,
    update_by: null,
    delete_date: null,
    delete_by: null,
  };

  const emptyEditTransactionHeaderForm = {
    id_trans_header: null,
    id_ppi: null,
    batch_code: "",
    iot_desc: "",
    line_desc: "",
    remarks: "",
    wetmill: "N",
    status_transaction: 2,
    create_date: null,
    create_by: "",
    update_date: null,
    update_by: null,
    delete_date: null,
    delete_by: null,
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [addTransactionHeaderForm, setAddTransactionHeaderForm] = useState(emptyAddTransactionHeaderForm);
  const [editTransactionHeaderForm, setEditTransactionHeaderForm] = useState(emptyEditTransactionHeaderForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});


  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = transactionHeadersDataState.filter((rowData, i) => {
    const searchFields = ["id_trans_header", "ppi_name", "batch_code", "iot_desc", "line_desc", "remarks", "wetmill", "status_transaction", "create_date", "create_by", "update_date", "update_by", "delete_date", "delete_by"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : transactionHeadersDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("pqr/operator"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      getAllFullyApproveTransactionHeader();
      getAllMSCData();
      getAllRekonKapletData();
      getAllRekonFoilData();
      getAllRekonSekunderData();
    }
  }, []);

  const getAllFullyApproveTransactionHeader = async () => {
    try {
      const res = await ApiTransactionHeader().getAllFullyApproveTransactionHeader();

      console.log("res", res);
      if (res.status === 200) {
        setTransactionHeadersDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };

  const getAllMSCData = async () => {
    try {
      const res = await ApiMSCWeight().getMSC();

      console.log("res", res);
      if (res.status === 200) {
        setMSCDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };

  const getAllRekonKapletData = async () => {
    try {
      const res = await ApiRekonKaplet().getAllRekonKaplet();

      console.log("res", res);
      if (res.status === 200) {
        setRekonKapletDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };
  const getAllRekonFoilData = async () => {
    try {
      const res = await ApiRekonFoil().getAllRekonFoil();

      console.log("res", res);
      if (res.status === 200) {
        setRekonFoilDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };

  const getAllRekonSekunderData = async () => {
    try {
      const res = await ApiRekonSekunder().getAllRekonSekunder();

      console.log("res", res);
      if (res.status === 200) {
        setRekonSekunderDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };


  return (
    <div>
      <div>
        <Head>
          <title>List E Release</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>E Release</Breadcrumb.Item>
                  <Breadcrumb.Item>List</Breadcrumb.Item>
                  <Breadcrumb.Item active>Pengisian E Release</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Pengisian E Release</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="cari" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn} autoHeight>
                <Column width={180} align="center" sortable fullText resizable>
                  <HeaderCell>ID Transaksi Header</HeaderCell>
                  <Cell dataKey="id_trans_header" />
                </Column>
                <Column width={180} sortable fullText resizable>
                  <HeaderCell align="center">Nama PPI</HeaderCell>
                  <Cell dataKey="ppi_name" />
                </Column>
                <Column width={180} sortable fullText resizable>
                  <HeaderCell align="center">Kode Batch</HeaderCell>
                  <Cell dataKey="batch_code" />
                </Column>
                {/* <Column width={250} sortable fullText>
                  <HeaderCell align="center">Deksripsi IOT</HeaderCell>
                  <Cell dataKey="iot_desc" />
                </Column>
                <Column width={250} sortable fullText>
                  <HeaderCell align="center">Deskripsi Line</HeaderCell>
                  <Cell dataKey="line_desc" />
                </Column> */}
                <Column width={250} sortable fullText resizable>
                  <HeaderCell align="center">Catatan</HeaderCell>
                  <Cell dataKey="remarks" />
                </Column>
                <Column width={250} align="center" sortable fullText resizable>
                  <HeaderCell>Wetmill</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      let statusText = "";
                      if (rowData.wetmill === "Y") {
                        statusText = "Yes";
                      } else if (rowData.wetmill === "N") {
                        statusText = "No";
                      }
                      return <>{statusText}</>;
                    }}
                  </Cell>
                </Column>
                <Column width={250} align="center" sortable fullText resizable>
                  <HeaderCell>Status Transaksi</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      let statusText = "";
                      if (rowData.status_transaction === 2) {
                        statusText = "Draft";
                      } else if (rowData.status_transaction === 1) {
                        statusText = "Done";
                      } else if (rowData.status_transaction === 0) {
                        statusText = "Dropped";
                      }
                      return <>{statusText}</>;
                    }}
                  </Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dibuat</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.delete_by}</>}</Cell>
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={180} sortable fullText resizable>
                  <HeaderCell align="center">Status Approval</HeaderCell>
                  <Cell dataKey="status_approval">{(rowData) => "Approved"}</Cell>
                </Column>
                <Column width={200} sortable fullText resizable>
                  <HeaderCell align="center">Disetujui Oleh</HeaderCell>
                  <Cell dataKey="approval_by">
                    {(rowData) => {
                      return rowData.approval_by || "-";
                    }}
                  </Cell>
                </Column>
                <Column width={200} sortable fullText resizable>
                  <HeaderCell align="center">Tanggal Disetujui</HeaderCell>
                  <Cell>{(rowData) => (rowData.approval_date ? new Date(rowData.approval_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={100} fixed="right" align="center">
                  <HeaderCell>Transaksi Detail</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            const idHeader = rowData.id_trans_header;
                            router.push(`/user_module/pqr/operator/list/pqr?IdHeader=${idHeader}`);
                          }}
                        >
                          <Whisper placement="top" trigger="hover" speaker={<Tooltip>Lihat Detail Transaksi Header</Tooltip>}>
                            <SearchIcon style={{ fontSize: "16px" }} />
                          </Whisper>
                        </Button>


                      </div>
                    )}
                  </Cell>
                </Column>

                <Column width={160} fixed="right" align="center">
                  <HeaderCell>Massa Siap Cetak</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            const idHeader = rowData.id_trans_header;
                            const hasMSC = MSCDataState.find(msc => msc.id_trans_header === rowData.id_trans_header);

                            if (hasMSC) {
                              router.push(`/user_module/pqr/operator/list/msc?IdHeader=${idHeader}&mode=edit`);
                            } else {
                              router.push(`/user_module/pqr/operator/list/msc?IdHeader=${idHeader}&mode=add`);
                            }
                          }}
                        >
                          {MSCDataState.find(msc => msc.id_trans_header === rowData.id_trans_header) ? (
                            <Whisper placement="top" trigger="hover" speaker={<Tooltip>Edit MSC</Tooltip>}>
                              <EditIcon />
                            </Whisper>
                          ) : (
                            <Whisper placement="top" trigger="hover" speaker={<Tooltip>Tambah MSC</Tooltip>}>
                              <PlusRoundIcon />
                            </Whisper>
                          )}
                        </Button>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            const idHeader = rowData.id_trans_header;
                            router.push(`/user_module/pqr/operator/list/msc?IdHeader=${idHeader}&mode=view`);
                          }}
                          disabled={!MSCDataState.find(msc => msc.id_trans_header === rowData.id_trans_header)}
                        >
                          <Whisper placement="top" trigger="hover" speaker={<Tooltip>Lihat MSC</Tooltip>}>
                            {MSCDataState.find(msc => msc.id_trans_header === rowData.id_trans_header) ? (
                              <VisibleIcon style={{ fontSize: "16px", color: "#1675e0" }} />
                            ) : (
                              <UnvisibleIcon style={{ fontSize: "16px", color: "#cccccc" }} />
                            )}
                          </Whisper>
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
                <Column width={160} fixed="right" align="center">
                  <HeaderCell>Rekonsiliasi Kaplet</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => {

                      const hasMSCData = MSCDataState.find(msc => msc.id_trans_header === rowData.id_trans_header);


                      const hasRekonKaplet = rekonKapletDataState.find(rkn => rkn.id_trans_header === rowData.id_trans_header);

                      return (
                        <div>
                          <Button
                            appearance="subtle"

                            disabled={!hasMSCData}
                            onClick={() => {
                              const idHeader = rowData.id_trans_header;
                              if (hasRekonKaplet) {
                                router.push(`/user_module/pqr/operator/list/rekon_kaplet_hasil_cetak?IdHeader=${idHeader}&mode=edit`);
                              } else {
                                router.push(`/user_module/pqr/operator/list/rekon_kaplet_hasil_cetak?IdHeader=${idHeader}&mode=add`);
                              }
                            }}
                          >
                            <Whisper
                              placement="top"
                              trigger="hover"
                              speaker={
                                <Tooltip>
                                  {/* 4. Tampilkan pesan yang berbeda pada tooltip jika disabled */}
                                  {!hasMSCData
                                    ? "Harap tambahkan data Massa Siap Cetak terlebih dahulu"
                                    : hasRekonKaplet
                                      ? "Edit Rekon Kaplet"
                                      : "Tambah Rekon Kaplet"
                                  }
                                </Tooltip>
                              }
                            >
                              {hasRekonKaplet ? <EditIcon /> : <PlusRoundIcon />}
                            </Whisper>
                          </Button>

                          <Button
                            appearance="subtle"
                            onClick={() => {
                              const idHeader = rowData.id_trans_header;
                              router.push(`/user_module/pqr/operator/list/rekon_kaplet_hasil_cetak?IdHeader=${idHeader}&mode=view`);
                            }}
                            disabled={!hasRekonKaplet}
                          >
                            <Whisper placement="top" trigger="hover" speaker={<Tooltip>Lihat Rekon Kaplet</Tooltip>}>
                              {hasRekonKaplet ? (
                                <VisibleIcon style={{ fontSize: "16px", color: "#1675e0" }} />
                              ) : (
                                <UnvisibleIcon style={{ fontSize: "16px", color: "#cccccc" }} />
                              )}
                            </Whisper>
                          </Button>
                        </div>
                      );
                    }}
                  </Cell>
                </Column>
                <Column width={160} fixed="right" align="center">
                  <HeaderCell>Rekon Foil</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            const idHeader = rowData.id_trans_header;
                            const hasRekonFoil = rekonFoilDataState.find(rfl => rfl.id_trans_header === rowData.id_trans_header);

                            if (hasRekonFoil) {
                              router.push(`/user_module/pqr/operator/list/rekon_foil?IdHeader=${idHeader}&mode=edit`);
                            } else {
                              router.push(`/user_module/pqr/operator/list/rekon_foil?IdHeader=${idHeader}&mode=add`);
                            }
                          }}
                        >
                          {rekonFoilDataState.find(rfl => rfl.id_trans_header === rowData.id_trans_header) ? (
                            <Whisper placement="top" trigger="hover" speaker={<Tooltip>Edit Rekon Foil</Tooltip>}>
                              <EditIcon />
                            </Whisper>
                          ) : (
                            <Whisper placement="top" trigger="hover" speaker={<Tooltip>Tambah Rekon Foil</Tooltip>}>
                              <PlusRoundIcon />
                            </Whisper>
                          )}
                        </Button>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            const idHeader = rowData.id_trans_header;
                            router.push(`/user_module/pqr/operator/list/rekon_foil?IdHeader=${idHeader}&mode=view`);
                          }}
                          disabled={!rekonFoilDataState.find(rfl => rfl.id_trans_header === rowData.id_trans_header)}
                        >
                          <Whisper placement="top" trigger="hover" speaker={<Tooltip>Lihat Rekon Foil</Tooltip>}>
                            {rekonFoilDataState.find(rfl => rfl.id_trans_header === rowData.id_trans_header) ? (
                              <VisibleIcon style={{ fontSize: "16px", color: "#1675e0" }} />
                            ) : (
                              <UnvisibleIcon style={{ fontSize: "16px", color: "#cccccc" }} />
                            )}
                          </Whisper>
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
                <Column width={160} fixed="right" align="center">
                  <HeaderCell>Rekon Sekunder</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            const idHeader = rowData.id_trans_header;
                            const hasRekonSekunder = rekonSekunderDataState.find(rsk => rsk.id_trans_header === rowData.id_trans_header);

                            if (hasRekonSekunder) {
                              router.push(`/user_module/pqr/operator/list/rekon_sekunder?IdHeader=${idHeader}&mode=edit`);
                            } else {
                              router.push(`/user_module/pqr/operator/list/rekon_sekunder?IdHeader=${idHeader}&mode=add`);
                            }
                          }}
                        >
                          {rekonSekunderDataState.find(rsk => rsk.id_trans_header === rowData.id_trans_header) ? (
                            <Whisper placement="top" trigger="hover" speaker={<Tooltip>Edit Rekon Sekunder</Tooltip>}>
                              <EditIcon />
                            </Whisper>
                          ) : (
                            <Whisper placement="top" trigger="hover" speaker={<Tooltip>Tambah Rekon Sekunder</Tooltip>}>
                              <PlusRoundIcon />
                            </Whisper>
                          )}
                        </Button>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            const idHeader = rowData.id_trans_header;
                            router.push(`/user_module/pqr/operator/list/rekon_sekunder?IdHeader=${idHeader}&mode=view`);
                          }}
                          disabled={!rekonSekunderDataState.find(rsk => rsk.id_trans_header === rowData.id_trans_header)}
                        >
                          <Whisper placement="top" trigger="hover" speaker={<Tooltip>Lihat Rekon Sekunder</Tooltip>}>
                            {rekonSekunderDataState.find(rsk => rsk.id_trans_header === rowData.id_trans_header) ? (
                              <VisibleIcon style={{ fontSize: "16px", color: "#1675e0" }} />
                            ) : (
                              <UnvisibleIcon style={{ fontSize: "16px", color: "#cccccc" }} />
                            )}
                          </Whisper>
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditTransactionHeaderForm(emptyEditTransactionHeaderForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Ubah Transaksi Header</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                    <SelectPicker
                      data={ppiDataState} // Menggunakan data PPI yang telah di-fetch
                      value={editTransactionHeaderForm.id_ppi} // Nilai yang dipilih untuk edit
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_ppi: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          id_ppi: undefined,
                        }));
                      }}
                      block
                      placeholder="Select PPI"
                      style={{ width: "100%" }}
                    />
                    {errorsEditForm.id_ppi && <p style={{ color: "red" }}>{errorsEditForm.id_ppi}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Batch Code</Form.ControlLabel>
                    <Form.Control
                      name="batch_code"
                      value={editTransactionHeaderForm.batch_code}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          batch_code: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          batch_code: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.batch_code && <p style={{ color: "red" }}>{errorsEditForm.batch_code}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Deskripsi IOT</Form.ControlLabel>
                    <Form.Control
                      name="iot_desc"
                      value={editTransactionHeaderForm.iot_desc}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          iot_desc: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          iot_desc: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.iot_desc && <p style={{ color: "red" }}>{errorsEditForm.iot_desc}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Deskripsi Line</Form.ControlLabel>
                    <Form.Control
                      name="line_desc"
                      value={editTransactionHeaderForm.line_desc}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          line_desc: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          line_desc: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.line_desc && <p style={{ color: "red" }}>{errorsEditForm.line_desc}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Remarks</Form.ControlLabel>
                    <Form.Control
                      name="remarks"
                      value={editTransactionHeaderForm.remarks}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          remarks: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          remarks: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.remarks && <p style={{ color: "red" }}>{errorsEditForm.remarks}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Wetmill</Form.ControlLabel>
                    <RadioGroup
                      name="wetmill"
                      inline
                      value={editTransactionHeaderForm.wetmill}
                      onChange={(value) => {
                        setEditTransactionHeaderForm((prevFormValue) => ({
                          ...prevFormValue,
                          wetmill: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          wetmill: undefined,
                        }));
                      }}
                    >
                      <Radio value="Y">Yes</Radio>
                      <Radio value="N">No</Radio>
                    </RadioGroup>
                    {errorsEditForm.wetmill && <p style={{ color: "red" }}>{errorsEditForm.wetmill}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditTransactionHeaderForm(emptyEditTransactionHeaderForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleEditTransactionHeaderApi();
                  }}
                  appearance="primary"
                  type="submit"
                >
                  Edit
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Editing Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
