import { useEffect, useState, } from "react";
import Head from "next/head";
import { Breadcrumb, Stack, Panel, Tag, Form, Grid, Row, Col, Table, Button, Pagination, Modal, Loader, IconButton, InputGroup, FlexboxGrid, Input, InputNumber, Checkbox, SelectPicker, useToaster, Notification } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiMasterdata_ppi from "@/pages/api/pqr/ppi/api_masterdata_ppi";
import ApiProduct from "@/pages/api/pqr/product/api_masterdata_product";
import ApiItemProduct from "@/pages/api/pqr/item_product/api_item_product";
import ApiRekonSekunder from "@/pages/api/pqr/rekon_sekunder/api_rekon_sekunder";
import ApiFoilPQR from "@/pages/api/pqr/foil/api_pqr_foil";

export default function RekonSekunderPage() {
    const toaster = useToaster();
    const [searchKeyword, setSearchKeyword] = useState("");
    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [idRouter, setIdRouter] = useState(null);
    const router = useRouter();
    const { IdHeader } = router.query;
    const [isEditMode, setIsEditMode] = useState(false);
    const [isViewMode, setIsViewMode] = useState(false);

    const emptyAddTransactionReconFoilForm = {
        id_sekunder_header: null,
        id_trans_header: null,
        remarks: null,
        stripping_machine: null,
        create_by: sessionAuth ? sessionAuth.employee_name : "",
        jumlah_produk_jadi_doos: null,
        jumlah_afkir_rendemen: null,
        jumlah_sample: null,
        jumlah_produk_jadi_kaplet: null,
        jumlah_tablet: null,
        jumlah_doos_akhir: null,
        id_foil_detail: [],
    };
    const [ppiData, setPpiData] = useState(null);
    const [productData, setProductData] = useState(null);
    const [formDataHeader, setformDataHeader] = useState({});
    const [listFoilItems, setListFoilItems] = useState([]);
    const [addTransactionReconFoilForm, setAddTransactionReconFoilForm] = useState(emptyAddTransactionReconFoilForm);
    const [isSubmitDisabled, setIsSubmitDisabled] = useState(true);
    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [addLoading, setAddLoading] = useState(false);
    const [loading, setLoading] = useState(false);



    const HandleGetDetailTransactionHeader = async (id_trans_header) => {
        try {
            const apiTransactionHeader = ApiTransactionHeader();
            const response = await apiTransactionHeader.getPPITransactionHeaderById({ id_trans_header: parseInt(id_trans_header) });
            if (response.status === 200) {
                const data = response.data;
                setformDataHeader({
                    id_trans_header: data.id_trans_header,
                    id_ppi: data.id_ppi,
                    ppi_name: data.ppi_name,
                    batch_code: data.batch_code,
                    iot_desc: data.iot_desc,
                    line_desc: data.line_desc,
                    remarks: data.remarks,
                    wetmill: data.wetmill,
                    status_transaction: data.status_transaction,
                    create_date: data.create_date ? new Date(data.create_date).toLocaleDateString("en-GB") : "-",
                    create_by: data.create_by || "-",
                    update_date: data.update_date ? new Date(data.update_date).toLocaleDateString("en-GB") : "-",
                    update_by: data.update_by || "-",
                    delete_date: data.delete_date ? new Date(data.delete_date).toLocaleDateString("en-GB") : "-",
                    delete_by: data.delete_by || "-",
                });

                if (data.id_ppi) {
                    const ppiResponse = await HandleGetPPIById(data.id_ppi);

                    if (ppiResponse) {
                        setPpiData(ppiResponse);
                        if (ppiResponse.id_product) {
                            const productResponse = await HandleGetProductById(ppiResponse.id_product);
                            if (productResponse) {
                                setProductData(productResponse);
                            }
                        }
                    }
                }

                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetPPIById = async (id_ppi) => {
        try {
            const api = ApiMasterdata_ppi();
            const response = await api.GetMasterPPIById({ id_ppi: parseInt(id_ppi) });

            if (response.status === 200) {
                const data = response.data;
                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetProductById = async (id_product) => {
        try {
            const api = ApiProduct();
            const response = await api.getProductById({ id_product: parseInt(id_product) });

            if (response.status === 200) {
                const data = response.data;
                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetAllItemProductApi = async () => {
        try {
            const res = await ApiItemProduct().getAllActiveItemProduct();
            if (res.status === 200) {
                const initialFormState = res.data.map(item => ({
                    ...item,
                    qty_std: productData.bobot_std,
                    material_awal: '',
                    fkm: '',
                    jumlah_material_terpakai: '',
                    jumlah_afkir: '',
                    jumlah_sisa: '',
                    jumlah_material_dimusnahkan: '',
                    jumlah_material_dikembalikan: ''
                }));
                setListFoilItems(initialFormState);
            } else {
                console.log("Error on GetAllItemProductApi: ", res.message);

            }
        } catch (error) {
            console.log("Error on catch GetAllItemProductApi: ", error.message);
        }
    };


    const HandleGetBatchCode = async (batch_number) => {
        try {
            const res = await ApiFoilPQR().getRekonSekunder({
                "batch_number":batch_number
            });
            if (res.status === 200) {
                const initialFormState = res.data.map(item => ({
                    ...item,
                    inventory_item_id: item.id_item,
                    item_code: item.item,
                    item_description: item.item_description,
                    qty_std: item.qty,
                    material_awal: '',
                    fkm: '',
                    jumlah_material_terpakai: '',
                    jumlah_afkir: '',
                    jumlah_sisa: '',
                    jumlah_material_dimusnahkan: '',
                    jumlah_material_dikembalikan: ''
                }));
                setListFoilItems(initialFormState);
            } else {
                console.log("Error on GetAllItemProductApi: ", res.message);

            }
        } catch (error) {
            console.log("Error on catch GetAllItemProductApi: ", error.message);
        }
    };


    const HandleGetRekonSekunderByIdTransHeader = async (id_trans_header) => {
        try {
            setLoading(true);
            const api = ApiRekonSekunder();

            const response = await api.getRekonSekunderByIdTransHeader({ id_trans_header: parseInt(id_trans_header) });

            if (response.status !== 200) {
                throw new Error(response.message || "Gagal mendapatkan data Rekonsiliasi Sekunder.");
            }

            const data = response.data;
            if (!data) {
                throw new Error("Data Rekonsiliasi Sekunder tidak ditemukan.");
            }
            setAddTransactionReconFoilForm(prev => ({
                ...prev,
                id_sekunder_header: data.id_sekunder_header,
                remarks: data.remarks,
                jumlah_produk_jadi_doos: data.jumlah_produk_jadi_doos,
                jumlah_afkir_rendemen: data.jumlah_afkir,
                jumlah_sample: data.jumlah_sample
            }));


            const itemProductResponse = await ApiItemProduct().getAllActiveItemProduct();

            const fetchedItems = data.rekon_sekunder_detail.map(detail => {
                return {
                    ...detail,
                    jumlah_afkir_item: detail.jumlah_afkir,
                    qty_std: parseInt(detail.qty_std)
                };
            });

            console.log("fetched Items", fetchedItems)

            setListFoilItems(fetchedItems);

        } catch (error) {
            console.error("Error dalam HandleGetRekonSekunderByIdTransHeader:", error);
            showNotification("error", error.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(()=>{
        console.log("listFoilItems",listFoilItems)
    },[listFoilItems])

    const isNumeric = (value) => {
        if (typeof value === 'number') return true;
        if (typeof value !== 'string') return false;
        return !isNaN(value) && !isNaN(parseFloat(value));
    }
    const handleItemInputChange = (index, fieldName, value) => {
        if (value.includes('-')) {
            return;
        }
        const updatedItems = [...listFoilItems];
        const currentItem = { ...updatedItems[index] };

        currentItem[fieldName] = value;

        const Z_val = currentItem.material_awal;
        const Z1_val = currentItem.fkm;
        const Z2_val = currentItem.jumlah_material_terpakai;
        const Z3_val = currentItem.jumlah_afkir_item;
        if (
            isNumeric(Z_val) &&
            isNumeric(Z1_val) &&
            isNumeric(Z2_val) &&
            isNumeric(Z3_val)
        ) {
            const Z = parseFloat(Z_val);
            const Z1 = parseFloat(Z1_val);
            const Z2 = parseFloat(Z2_val);
            const Z3 = parseFloat(Z3_val);

            const T = (Z + Z1) - (Z2 + Z3);
            currentItem.jumlah_sisa = parseFloat(T.toFixed(2));
        } else {
            currentItem.jumlah_sisa = `N/A`;
        }

        updatedItems[index] = currentItem;
        setListFoilItems(updatedItems);
    };

    useEffect(() => {
        const Y = parseFloat(addTransactionReconFoilForm.jumlah_produk_jadi_doos) || 0;
        const AB = parseFloat(addTransactionReconFoilForm.jumlah_afkir_rendemen) || 0;
        const AC = parseFloat(addTransactionReconFoilForm.jumlah_sample) || 0;

        const AA = Y * 100;
        const AD = AA + AB + AC;
        const finalDoos = AD / 100;

        setAddTransactionReconFoilForm(prev => ({
            ...prev,
            jumlah_produk_jadi_kaplet: parseFloat(AA.toFixed(2)),
            jumlah_tablet: parseFloat(AD.toFixed(2)),
            jumlah_doos_akhir: parseFloat(finalDoos.toFixed(2)),
        }));

    }, [
        addTransactionReconFoilForm.jumlah_produk_jadi_doos,
        addTransactionReconFoilForm.jumlah_afkir_rendemen,
        addTransactionReconFoilForm.jumlah_sample
    ]);
    const handleSubmit = async () => {
        setAddLoading(true);

        try {
            const commonRekonDetails = listFoilItems.map(item => ({
                id_item: item.id_item,
                item_code: item.item_code,
                item_description: item.item_description,
                qty_std: parseFloat(item.qty_std),
                material_awal: parseFloat(item.material_awal),
                fkm: parseFloat(item.fkm),
                jumlah_material_terpakai: parseFloat(item.jumlah_material_terpakai),
                jumlah_afkir_item: parseFloat(item.jumlah_afkir_item),
                jumlah_sisa: parseFloat(item.jumlah_sisa),
                jumlah_material_dimusnahkan: parseFloat(item.jumlah_material_dimusnahkan),
                jumlah_material_dikembalikan: parseFloat(item.jumlah_material_dikembalikan),
            }));

            if (isEditMode) {
                const updatedBy = sessionAuth ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}` : "";

                const payloadDetailRekon = listFoilItems.map(item => ({
                    id_sekunder_detail: item.id_sekunder_detail,
                    id_sekunder_header: addTransactionReconFoilForm.id_sekunder_header,
                    id_item: item.id_item,
                    item_code: item.item_code,
                    item_description: item.item_description,
                    material_awal: parseFloat(item.material_awal),
                    fkm: parseFloat(item.fkm),
                    jumlah_material_terpakai: parseFloat(item.jumlah_material_terpakai),
                    jumlah_afkir: parseFloat(item.jumlah_afkir_item),
                    jumlah_sisa: parseFloat(item.jumlah_sisa),
                    jumlah_material_dimusnahkan: parseFloat(item.jumlah_material_dimusnahkan),
                    jumlah_material_dikembalikan: parseFloat(item.jumlah_material_dikembalikan),
                    update_by: updatedBy,
                }));


                const finalPayloadUpdate = {
                    id_sekunder_header: addTransactionReconFoilForm.id_sekunder_header,
                    remarks: addTransactionReconFoilForm.remarks,
                    jumlah_produk_jadi_doos: parseFloat(addTransactionReconFoilForm.jumlah_produk_jadi_doos),
                    jumlah_afkir: parseFloat(addTransactionReconFoilForm.jumlah_afkir_rendemen),
                    jumlah_sample: parseFloat(addTransactionReconFoilForm.jumlah_sample),
                    jumlah_produk_jadi_kaplet: parseFloat(addTransactionReconFoilForm.jumlah_produk_jadi_kaplet),
                    jumlah_tablet: parseFloat(addTransactionReconFoilForm.jumlah_tablet),
                    jumlah_doos: parseFloat(addTransactionReconFoilForm.jumlah_doos_akhir),
                    update_by: updatedBy,
                    details_rekon: payloadDetailRekon,
                };

                console.log("Mengirim Payload UPDATE", finalPayloadUpdate);
                const res = await ApiRekonSekunder().updateRekonSekunder(finalPayloadUpdate);
                if (res.status === 200) {
                    showNotification("success", "Data Rekonsiliasi Sekunder Berhasil Diperbarui");
                    router.push(`/user_module/pqr/operator/list`);
                } else {
                    throw new Error(res.message || "Gagal Memperbarui data");
                }
            } else {
                const createdBy = sessionAuth ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}` : "";
                const payloadDetailRekon = listFoilItems.map(item => ({
                    id_item: item.id_item,
                    item_code: item.item_code,
                    item_description: item.item_description,
                    material_awal: parseFloat(item.material_awal),
                    fkm: parseFloat(item.fkm),
                    jumlah_material_terpakai: parseFloat(item.jumlah_material_terpakai),
                    jumlah_afkir: parseFloat(item.jumlah_afkir_item),
                    jumlah_sisa: parseFloat(item.jumlah_sisa),
                    jumlah_material_dimusnahkan: parseFloat(item.jumlah_material_dimusnahkan),
                    jumlah_material_dikembalikan: parseFloat(item.jumlah_material_dikembalikan),
                    create_by: createdBy,
                    qty_std: item.qty_std
                }));
                const finalPayloadCreate = {
                    id_trans_header: parseInt(IdHeader, 10),
                    remarks: addTransactionReconFoilForm.remarks,
                    jumlah_produk_jadi_doos: parseFloat(addTransactionReconFoilForm.jumlah_produk_jadi_doos) || 0,
                    jumlah_afkir: parseFloat(addTransactionReconFoilForm.jumlah_afkir_rendemen),
                    jumlah_sample: parseFloat(addTransactionReconFoilForm.jumlah_sample),
                    jumlah_produk_jadi_kaplet: parseFloat(addTransactionReconFoilForm.jumlah_produk_jadi_kaplet),
                    jumlah_tablet: parseFloat(addTransactionReconFoilForm.jumlah_tablet),
                    jumlah_doos: parseFloat(addTransactionReconFoilForm.jumlah_doos_akhir),
                    create_by: createdBy,
                    details_rekon: payloadDetailRekon
                };

                console.log("Mengirim Payload CREATE", finalPayloadCreate);
                const res = await ApiRekonSekunder().createRekonSekunder(finalPayloadCreate);
                if (res.status === 200) {
                    showNotification("success", "Data Rekonsiliasi Sekunder Berhasil Disimpan");
                    router.push(`/user_module/pqr/operator/list`);
                } else {
                    throw new Error(res.message || "Gagal Menyimpan data");
                }
            }
        } catch (error) {
            console.error("Terjadi kesalahan saat submit:", error);
            const modeText = isEditMode ? "Memperbarui" : "Menyimpan";
            showNotification("error", error.message || `Terjadi Kesalahan Saat ${modeText} Data`);
        } finally {
            setAddLoading(false);
        }
    };

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setSessionAuth(dataLogin);
        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else if (IdHeader) {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("pqr/operator")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }

            console.log("Router data:", IdHeader);
            setIdRouter(IdHeader);
            HandleGetDetailTransactionHeader(IdHeader);

            const { mode } = router.query;
            if (mode === 'edit' || mode === 'view') {
                setIsEditMode(mode === 'edit');
                setIsViewMode(mode === 'view');
                HandleGetRekonSekunderByIdTransHeader(IdHeader);
            } else {
                setIsEditMode(false);
                setIsViewMode(false);
                // if (productData) {
                //     HandleGetAllItemProductApi();
                // }
            }
        }
    }, [IdHeader, router]);

    useEffect(() => {

        if (productData && !isEditMode && !isViewMode) {
            HandleGetBatchCode(formDataHeader.batch_code);
        }


        // if (productData && (isEditMode || isViewMode) && listFoilItems.length > 0) {
        //     const updatedItems = listFoilItems.map(item => ({
        //         ...item,
        //         qty_std: productData.bobot_std
        //     }));
        //     setListFoilItems(updatedItems);
        // }
    }, [productData, isEditMode, isViewMode, formDataHeader]);

    useEffect(() => {
        const validateForm = () => {
            if (!addTransactionReconFoilForm.remarks) {
                return true;
            }
            if (!addTransactionReconFoilForm.jumlah_produk_jadi_doos) {
                return true;
            }
            if (!addTransactionReconFoilForm.jumlah_afkir_rendemen) {
                return true;
            }
            if (!addTransactionReconFoilForm.jumlah_sample) {
                return true;
            }
            for (const item of listFoilItems) {
                if (
                    String(item.material_awal).trim() === '' ||
                    String(item.fkm).trim() === '' ||
                    String(item.jumlah_material_terpakai).trim() === '' ||
                    String(item.jumlah_afkir_item).trim() === '' ||
                    String(item.jumlah_sisa).trim() === '' ||
                    String(item.jumlah_material_dimusnahkan).trim() === '' ||
                    String(item.jumlah_material_dikembalikan).trim() === ''
                ) {
                    return true;
                }
            }

            return false;
        };
        setIsSubmitDisabled(validateForm());
    }, [listFoilItems, addTransactionReconFoilForm]);


    const showNotification = (type, message, duration = 2000) => {
        toaster.push(
            <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
                <p>{message}</p>
            </Notification>,
            { placement: "topEnd", duration }
        );
    };

    return (
        <div>
            <div>
                <Head>
                    <title>Rekonsiliasi Sekunder</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>PQR</Breadcrumb.Item>
                                    <Breadcrumb.Item>List</Breadcrumb.Item>
                                    <Breadcrumb.Item >Transaction Header</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Rekon Kaplet</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <Form layout="vertical">
                                    <Grid fluid>
                                        <Row style={{ marginBottom: "16px" }}>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>ID Transaksi Header</Form.ControlLabel>
                                                    <Form.Control name="id_trans_header" value={formDataHeader.id_trans_header} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                                                    <Form.Control name="ppi_name" value={formDataHeader.ppi_name} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Batch Code</Form.ControlLabel>
                                                    <Form.Control name="batch_code" value={formDataHeader.batch_code} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Wetmill</Form.ControlLabel>
                                                    <Form.Control name="wetmill" value={formDataHeader.wetmill === "Y" ? "Yes" : "No"} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}></Row>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Remarks</Form.ControlLabel>
                                                        <Form.Control name="remarks" value={formDataHeader.remarks} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Status Transaksi</Form.ControlLabel>
                                                        <Form.Control name="status_transaction" value={formDataHeader.status_transaction === 2 ? "Draft" : formDataHeader.status_transaction === 1 ? "Done" : "Dropped"} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                                                        <Form.Control name="product_code" value={productData?.product_code ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>

                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}><Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Bobot Minimal</Form.ControlLabel>
                                                    <Form.Control name="bobot_min" value={productData?.bobot_min ?? '-'} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Maximal</Form.ControlLabel>
                                                        <Form.Control name="bobot_max" value={productData?.bobot_max ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Standar</Form.ControlLabel>
                                                        <Form.Control name="bobot_std" value={productData?.bobot_std ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Core Foil</Form.ControlLabel>
                                                        <Form.Control name="bobot_core_foil" value={productData?.bobot_core_foil ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}></Row>
                                            <Row style={{ marginBottom: "24px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                                                        <Form.Control name="create_date" value={formDataHeader.create_date} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                                                        <Form.Control name="created_by" value={formDataHeader.create_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                                                        <Form.Control name="update_date" value={formDataHeader.update_date} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                                                        <Form.Control name="update_by" value={formDataHeader.update_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                                                    <Form.Control name="delete_date" value={formDataHeader.delete_date} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                                                        <Form.Control name="delete_by" value={formDataHeader.delete_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </Row>
                                    </Grid>
                                </Form>
                            </Stack>
                        }
                    />
                    <Panel
                        bordered
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Rekonsiliasi Sekunder</h5>
                            </Stack>
                        }
                    >
                        {loading ? (
                            <FlexboxGrid justify="center" align="middle" style={{ padding: '40px 0' }}>
                                <Loader center size="lg" vertical content="Memuat data rekonsiliasi..." />
                            </FlexboxGrid>
                        ) : (
                            <Form fluid>

                                <Panel bordered className="mb-3" header={<Stack justifyContent="space-between"><h5>Rekonsiliasi</h5></Stack>}>
                                    {listFoilItems.map((item, index) => (
                                        <Panel key={`rekon-${item.id_item}`} bordered className="mb-3" header={<h5>Material - {item.item_code}</h5>}>
                                            <Form.Group>
                                                <Form.ControlLabel>QTY_STD</Form.ControlLabel>
                                                <Input
                                                    readOnly
                                                    style={{ background: '#f4f4f4' }}
                                                    value={item.qty_std}
                                                />
                                            </Form.Group>
                                            <FlexboxGrid className="mb-3 mt-3">
                                                {/* --- Kolom Kiri --- */}
                                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Material Awal</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="material_awal(kg)"
                                                                onWheel={(e) => e.target.blur()}
                                                                value={item.material_awal}
                                                                onChange={(value) => handleItemInputChange(index, 'material_awal', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>FKM</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="fkm(kg)"
                                                                onWheel={(e) => e.target.blur()}
                                                                value={item.fkm}
                                                                onChange={(value) => handleItemInputChange(index, 'fkm', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Jumlah material terpakai</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="jumlah material terpakai(kg)"
                                                                onWheel={(e) => e.target.blur()}
                                                                value={item.jumlah_material_terpakai}
                                                                onChange={(value) => handleItemInputChange(index, 'jumlah_material_terpakai', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Jumlah sisa</Form.ControlLabel>
                                                        <Input
                                                            readOnly
                                                            style={{ background: '#f4f4f4' }}
                                                            value={item.jumlah_sisa}
                                                            placeholder="N/A"
                                                        />
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                                <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Jumlah afkir</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="jumlah afkir(kg)"
                                                                onWheel={(e) => e.target.blur()}
                                                                value={item.jumlah_afkir_item}
                                                                onChange={(value) => handleItemInputChange(index, 'jumlah_afkir_item', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>

                                                    <Form.Group>
                                                        <Form.ControlLabel>Jumlah Material Dimusnahkan</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="jumlah_material_dimusnahkan(kg)"
                                                                onWheel={(e) => e.target.blur()}
                                                                value={item.jumlah_material_dimusnahkan}
                                                                onChange={(value) => handleItemInputChange(index, 'jumlah_material_dimusnahkan', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Jumlah Material Dikembalikan</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="jumlah_material_dikembalikan (kg)"
                                                                value={item.jumlah_material_dikembalikan}
                                                                onWheel={(e) => e.target.blur()}
                                                                onChange={(value) => handleItemInputChange(index, 'jumlah_material_dikembalikan', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                            </FlexboxGrid>
                                        </Panel>
                                    ))}
                                </Panel>
                                <h5>Rendemen</h5>
                                <FlexboxGrid className="mb-5 mt-3">
                                    <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                        <Form.Group>
                                            <Form.ControlLabel>Jumlah Produk jadi (Doos)</Form.ControlLabel>
                                            <InputGroup>
                                                <Input
                                                    type="number"
                                                    min={0}
                                                    placeholder="jumlah_produk_jadi_doos(kg)"
                                                    onWheel={(e) => e.target.blur()}
                                                    value={addTransactionReconFoilForm.jumlah_produk_jadi_doos}
                                                    onChange={(value) => {
                                                        setAddTransactionReconFoilForm((prev) => ({ ...prev, jumlah_produk_jadi_doos: value }));
                                                        setErrorsAddForm((prev) => ({ ...prev, jumlah_produk_jadi_doos: undefined }));
                                                    }}
                                                    readOnly={isViewMode}
                                                />
                                                <InputGroup.Addon>Kg</InputGroup.Addon>
                                            </InputGroup>
                                        </Form.Group>
                                        <Form.Group>
                                            <Form.ControlLabel>Jumlah Afkir</Form.ControlLabel>
                                            <InputGroup>
                                                <Input
                                                    type="number"
                                                    min={0}
                                                    placeholder="jumlah_afkir(kg)"
                                                    onWheel={(e) => e.target.blur()}
                                                    value={addTransactionReconFoilForm.jumlah_afkir_rendemen}
                                                    onChange={(value) => {
                                                        setAddTransactionReconFoilForm((prev) => ({ ...prev, jumlah_afkir_rendemen: value }));
                                                        setErrorsAddForm((prev) => ({ ...prev, jumlah_afkir_rendemen: undefined }));
                                                    }}
                                                    readOnly={isViewMode}
                                                />
                                                <InputGroup.Addon>Kg</InputGroup.Addon>
                                            </InputGroup>
                                        </Form.Group>
                                        <Form.Group>
                                            <Form.ControlLabel>Jumlah sample</Form.ControlLabel>
                                            <InputGroup>
                                                <Input
                                                    type="number"
                                                    min={0}
                                                    placeholder="jumlah sample(kg)"
                                                    onWheel={(e) => e.target.blur()}
                                                    value={addTransactionReconFoilForm.jumlah_sample}
                                                    onChange={(value) => {
                                                        setAddTransactionReconFoilForm((prev) => ({ ...prev, jumlah_sample: value }));
                                                        setErrorsAddForm((prev) => ({ ...prev, jumlah_sample: undefined }));
                                                    }}
                                                    readOnly={isViewMode}
                                                />
                                                <InputGroup.Addon>Kg</InputGroup.Addon>
                                            </InputGroup>
                                        </Form.Group>
                                    </FlexboxGrid.Item>
                                    <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                        <Form.Group>
                                            <Form.ControlLabel>Jumlah Produk Jadi (Kaplet)</Form.ControlLabel>
                                            <Input
                                                readOnly
                                                style={{ background: '#f4f4f4' }}
                                                value={addTransactionReconFoilForm.jumlah_produk_jadi_kaplet}
                                                placeholder="Jumlah Produk jadi (doos) * 100"
                                            />
                                        </Form.Group>
                                    </FlexboxGrid.Item>
                                </FlexboxGrid>
                                <h5>Rekonsiliasi Akhir Produk</h5>
                                <FlexboxGrid className="mb-3 mt-3">
                                    <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                        <Form.Group>
                                            <Form.ControlLabel>Jumlah Tablet</Form.ControlLabel>
                                            <InputGroup>
                                                <Input
                                                    readOnly
                                                    style={{ background: '#f4f4f4' }}
                                                    value={addTransactionReconFoilForm.jumlah_tablet}
                                                    placeholder="Jumlah Tablet"
                                                />
                                            </InputGroup>
                                        </Form.Group>
                                    </FlexboxGrid.Item>
                                    <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                        <Form.Group>
                                            <Form.ControlLabel>Jumlah ddos</Form.ControlLabel>
                                            <Input
                                                readOnly
                                                style={{ background: '#f4f4f4' }}
                                                value={addTransactionReconFoilForm.jumlah_doos_akhir}
                                                placeholder="Jumlah doos"
                                            />
                                        </Form.Group>
                                    </FlexboxGrid.Item>
                                </FlexboxGrid>
                                <Form.Group>
                                    <Form.ControlLabel>Keterangan</Form.ControlLabel>
                                    <Input
                                        as="textarea"
                                        rows={3}
                                        name="remarks"
                                        placeholder="Remarks"
                                        value={addTransactionReconFoilForm.remarks}
                                        onChange={(value) => {
                                            setAddTransactionReconFoilForm((prev) => ({ ...prev, remarks: value }));
                                            setErrorsAddForm((prev) => ({ ...prev, remarks: undefined }));
                                        }}
                                        readOnly={isViewMode}
                                    />
                                    {errorsAddForm.remarks && <p style={{ color: "red" }}>{errorsAddForm.remarks}</p>}
                                </Form.Group>

                                <Form.Group>
                                    <Stack justifyContent="end" spacing={10}>
                                        <Button onClick={() => router.back()} appearance="subtle" disabled={addLoading}>
                                            {isViewMode ? "Kembali" : "Batal"}
                                        </Button>
                                        {!isViewMode && (
                                            <Button appearance="primary" onClick={handleSubmit} disabled={isSubmitDisabled || addLoading} loading={addLoading}>
                                                {loading ? (isEditMode ? "Memperbarui..." : "Mengirim...") : (isEditMode ? "Update" : "Kirim")}
                                            </Button>
                                        )}
                                    </Stack>
                                </Form.Group>
                            </Form>
                        )}
                    </Panel>

                </div>
            </ContainerLayout >
        </div >
    );
}