import axios from 'axios';

export default function ApiTsMasterSpv() {



  const GetAllUnassignedSpv = async (inputData) => {
    let data = [];

    await axios
      .get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts_spv/all/unassigned`,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetAllSpv = async (inputData) => {
    let data = [];

    await axios
      .get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts_spv/all`,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const PostSpv = async (inputData) => {
    let data = [];
    console.log('[inputdata]', inputData)

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts_spv/create`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };


  const PutDeleteSpv = async (inputData) => {
    let data = [];
    console.log('[inputdata]', inputData)

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts_spv/delete`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };


  



  return {
    GetAllUnassignedSpv,
    GetAllSpv,
    PostSpv,
    PutDeleteSpv
  };
}