import { useEffect, useState } from "react";
import Head from "next/head";
import { <PERSON>readcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, Loader, RadioGroup, Radio, SelectPicker } from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import EditIcon from "@rsuite/icons/Edit";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import { FormControl, FormControlLabel, FormGroup } from "@mui/material";
import ApiActiveParameter from "@/pages/api/pqr/active_parameter/api_active_parameter";

export default function Index() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_trans_header");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [parameterHeadersDataState, setParameterHeadersDataState] = useState([]);

  const [isMapped, setIsMapped] = useState(null);

  const isMappedOptions = [
    { label: "Mapped", value: 1 },
    { label: "Not Mapped", value: 0 },
  ];

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = parameterHeadersDataState.filter((rowData, i) => {
    const searchFields = ["paramater_name", "step_name", "ppi_name", "id_ppi", "is_mapped"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });
  
  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    let filteredData = [...parameterHeadersDataState];
  
    if (isMapped !== null) {
      filteredData = filteredData.filter((rowData) => rowData.is_mapped === isMapped);
    }

    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : parameterHeadersDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
      setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("pqr"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandleGetAllActiveParameter();
    }
  }, []);

    const HandleGetAllActiveParameter = async () => {
      try {
        const res = await ApiActiveParameter().getAllActiveParameter();

        console.log("res", res);
        if (res.status === 200) {
          setParameterHeadersDataState(res.data);
        } else {
          console.log("error on GetAllApi ", res.message);
        }
      } catch (error) {
        console.log("error on catch GetAllApi", error);
      }
    };

  return (
    <div>
      <div>
        <Head>
          <title>Reporting Active Parameter</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="pt-2 m-4">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>E Release</Breadcrumb.Item>
                  <Breadcrumb.Item>Reporting E Release</Breadcrumb.Item>
                  <Breadcrumb.Item active>Active Parameter List</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>List Aktif Parameter</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="cari" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                  <SelectPicker
                      data={isMappedOptions}
                      value={isMapped}
                      onChange={setIsMapped}
                      searchable={false}
                      placeholder="Filter Is Mapped ?"
                      style={{ width: 230 }}
                  />
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn} autoHeight>
                <Column width={90} align="center" sortable fullText>
                  <HeaderCell>No</HeaderCell>
                  <Cell>
                    {(rowData, rowIndex) => (limit * (page - 1)) + rowIndex + 1}
                  </Cell>
                </Column>
                <Column width={180} sortable fullText>
                  <HeaderCell align="center">Nama PPI</HeaderCell>
                  <Cell dataKey="ppi_name" />
                </Column>
                <Column width={250} sortable fullText>
                  <HeaderCell align="center">Nama Step</HeaderCell>
                  <Cell dataKey="step_name" />
                </Column>
                <Column width={180} align="center" sortable fullText>
                  <HeaderCell>Nama Paramater</HeaderCell>
                  <Cell dataKey="paramater_name" />
                </Column>
                <Column width={180} align="center" sortable fullText>
                  <HeaderCell>Is Mapped</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      return rowData.is_mapped === 1 ? "Mapped" : "Not Mapped";
                    }}
                  </Cell>
                </Column>

                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          onClick={() => {
                            // const idHeader = rowData.id_trans_header;
                            router.push(`/user_module/pqr/masterdata/ppi/recipe?Id=36`);
                          }}
                        >
                          <SearchIcon style={{ fontSize: "16px" }} />
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
