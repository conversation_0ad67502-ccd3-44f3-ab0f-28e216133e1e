@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --max-width: 1100px;
  --border-radius: 12px;
  --font-mono: ui-monospace, Menlo, Monaco, "Cascadia Mono", "Segoe UI Mono",
    "Roboto Mono", "Oxygen Mono", "Ubuntu Monospace", "Source Code Pro",
    "Fira Mono", "Droid Sans Mono", "Courier New", monospace;

  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  --primary-glow: conic-gradient(
    from 180deg at 50% 50%,
    #16abff33 0deg,
    #0885ff33 55deg,
    #54d6ff33 120deg,
    #0071ff33 160deg,
    transparent 360deg
  );
  --secondary-glow: radial-gradient(
    rgba(255, 255, 255, 1),
    rgba(255, 255, 255, 0)
  );

  --tile-start-rgb: 239, 245, 249;
  --tile-end-rgb: 228, 232, 233;
  --tile-border: conic-gradient(
    #00000080,
    #00000040,
    #00000030,
    #00000020,
    #00000010,
    #00000010,
    #00000080
  );

  --callout-rgb: 238, 240, 241;
  --callout-border-rgb: 172, 175, 176;
  --card-rgb: 180, 185, 188;
  --card-border-rgb: 131, 134, 135;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
} 

:root,
.rs-theme-light {
  --rs-gray-50: #f7f7fa;
  --rs-gray-100: #f2f2f5;
  --rs-gray-200: #e5e5ea;
  --rs-gray-300: #d9d9d9;
  --rs-gray-400: #c5c6c7;
  --rs-gray-500: #a6a6a6;
  --rs-gray-600: #8e8e93;
  --rs-gray-700: #7a7a7a;
  --rs-gray-800: #575757;
  --rs-gray-900: #272c36;
  --rs-primary-50: #e0f3e3;
  --rs-primary-100: #c1e7c7;
  --rs-primary-200: #9ad9a4;
  --rs-primary-300: #72ca80;
  --rs-primary-400: #4abf5d;
  --rs-primary-500: #0f7141;
  --rs-primary-600: #0c6338;
  --rs-primary-700: #0a542e;
  --rs-primary-800: #084525;
  --rs-primary-900: #053518;
  --rs-blue-50: #e6f5ff;
  --rs-blue-100: #bae6ff;
  --rs-blue-200: #82cfff;
  --rs-blue-300: #33b5e5;
  --rs-blue-400: #0099cc;
  --rs-blue-500: #0077b3;
  --rs-blue-600: #006699;
  --rs-blue-700: #005580;
  --rs-blue-800: #003f5c;
  --rs-blue-900: #002233;
  --rs-yellow-50: #fffde7;
  --rs-yellow-100: #fff9c4;
  --rs-yellow-200: #fff59d;
  --rs-yellow-300: #fff176;
  --rs-yellow-400: #ffee58;
  --rs-yellow-500: #ffeb3b;
  --rs-yellow-600: #fdd835;
  --rs-yellow-700: #fbc02d;
  --rs-yellow-800: #f9a825;
  --rs-yellow-900: #f57f17;
  --rs-red-50: #ffebee;
  --rs-red-100: #ffcdd2;
  --rs-red-200: #ef9a9a;
  --rs-red-300: #e57373;
  --rs-red-400: #ef5350;
  --rs-red-500: #f44336;
  --rs-red-600: #e53935;
  --rs-red-700: #d32f2f;
  --rs-red-800: #c62828;
  --rs-red-900: #b71c1c;

  --rs-state-success: #4caf50;
  --rs-state-info: #2196f3;
  --rs-state-warning: #ffb300;
  --rs-state-error: #f44336;
  --rs-body: #fff;
  --rs-bg-success: #edfae1;
  --rs-bg-info: #e9f5fe;
  --rs-bg-warning: #fff9e6;
  --rs-bg-error: #fde9ef;
  --rs-text-link: #1675e0;
  --rs-text-link-hover: var(--rs-blue-600);
  --rs-text-link-active: var(--rs-blue-700);
  --rs-text-primary: #575757;
  --rs-text-secondary: #8e8e93;
  --rs-text-tertiary: #a6a6a6;
  --rs-text-heading: #272c36;
  --rs-text-inverse: #f7f7fa;
  --rs-text-heading-inverse: #fff;
  --rs-text-active: #0f7141;
  --rs-text-disabled: #c5c6c7;
  --rs-text-error: #f44336;
  --rs-border-primary: #e5e5ea;
  --rs-border-secondary: #f2f2f5;
  --rs-bg-card: #fff;
  --rs-bg-overlay: #fff;
  --rs-bg-well: #f7f7fa;
  --rs-bg-active: var(--rs-primary-500);
  --rs-bg-backdrop: rgba(39, 44, 54, 0.3);
  --rs-state-hover-bg: #f2faff;
  --rs-color-focus-ring: rgba(15, 113, 65, 0.25);
  --rs-state-focus-shadow: 0 0 0 3px rgba(15, 113, 65, 0.25);
  --rs-state-focus-outline: 3px solid rgba(15, 113, 65, 0.25);
  --rs-shadow-overlay: 0 4px 4px rgba(0, 0, 0, 0.12),
    0 0 10px rgba(0, 0, 0, 0.06);
  --rs-btn-primary-bg: var(--rs-primary-500);
  --rs-btn-primary-text: #fff;
  --rs-btn-primary-hover-bg: var(--rs-primary-600);
  --rs-btn-primary-active-bg: var(--rs-primary-700);
  --rs-btn-ghost-border: var(--rs-primary-500);
  --rs-btn-ghost-text: var(--rs-primary-500);
  --rs-btn-ghost-hover-border: var(--rs-primary-600);
  --rs-btn-ghost-hover-text: var(--rs-primary-600);
  --rs-btn-ghost-active-border: var(--rs-primary-700);
  --rs-btn-ghost-active-text: var(--rs-primary-700);
  --rs-btn-link-text: var(--rs-primary-500);
  --rs-btn-link-hover-text: var(--rs-primary-600);
  --rs-btn-link-active-text: var(--rs-primary-700);
  --rs-iconbtn-addon: var(--rs-primary-500);
  --rs-iconbtn-activated-addon: var(--rs-primary-700);
  --rs-iconbtn-pressed-addon: var(--rs-primary-700);
  --rs-iconbtn-primary-addon: var(--rs-primary-700);
  --rs-iconbtn-primary-activated-addon: var(--rs-primary-900);
  --rs-iconbtn-primary-pressed-addon: var(--rs-primary-900);
  --rs-divider-border: #e5e5ea;
  --rs-loader-ring: rgba(247, 247, 250, 0.8);
  --rs-loader-rotor: #a6a6a6;
  --rs-loader-backdrop: rgba(255, 255, 255, 0.9);
  --rs-message-success-header: var(--rs-text-heading);
  --rs-message-success-text: var(--rs-text-primary);
  --rs-message-success-icon: #4caf50;
  --rs-message-success-bg: #eeffed;
  --rs-message-info-header: var(--rs-text-heading);
  --rs-message-info-text: var(--rs-text-primary);
  --rs-message-info-icon: #2196f3;
  --rs-message-info-bg: #f0f9ff;
  --rs-message-warning-header: var(--rs-text-heading);
  --rs-message-warning-text: var(--rs-text-primary);
  --rs-message-warning-icon: #ffb300;
  --rs-message-warning-bg: #fffaf2;
  --rs-message-error-header: var(--rs-text-heading);
  --rs-message-error-text: var(--rs-text-primary);
  --rs-message-error-icon: #f44336;
  --rs-message-error-bg: #fff2f2;
  --rs-tooltip-bg: #272c36;
  --rs-tooltip-text: #fff;
  --rs-progress-bg: #e5e5ea;
  --rs-progress-bar: #3498ff;
  --rs-progress-bar-success: #4caf50;
  --rs-progress-bar-fail: #f44336;
  --rs-placeholder: #f2f2f5;
  --rs-placeholder-active: #e5e5ea;
  --rs-breadcrumb-item-active-text: var(--rs-gray-900);
  --rs-dropdown-divider: var(--rs-gray-200);
  --rs-dropdown-item-bg-hover: var(--rs-primary-50);
  --rs-dropdown-item-bg-active: var(--rs-primary-50);
  --rs-dropdown-item-text-active: var(--rs-primary-500);
  --rs-dropdown-header-text: var(--rs-gray-500);
  --rs-dropdown-shadow: 0 0 10px rgba(0, 0, 0, 0.06),
    0 4px 4px rgba(0, 0, 0, 0.12);
  --rs-menuitem-active-bg: var(--rs-primary-50);
  --rs-menuitem-active-text: var(--rs-primary-500);
  --rs-steps-border: #8e8e93;
  --rs-steps-state-finish: #3498ff;
  --rs-steps-border-state-finish: #3498ff;
  --rs-steps-state-wait: #8e8e93;
  --rs-steps-state-process: #3498ff;
  --rs-steps-state-error: #f44336;
  --rs-steps-border-state-error: #f44336;
  --rs-steps-icon-state-process: #3498ff;
  --rs-steps-icon-state-error: #f44336;
  --rs-navs-text: #8e8e93;
  --rs-navs-text-hover: #575757;
  --rs-navs-bg-hover: #e5e5ea;
  --rs-navs-text-active: #272c36;
  --rs-navs-bg-active: #e5e5ea;
  --rs-navs-tab-border: #d9d9d9;
  --rs-navs-subtle-border: #f7f7fa;
  --rs-navs-selected: var(--rs-primary-500);
  --rs-navbar-default-bg: #f7f7fa;
  --rs-navbar-default-text: #575757;
  --rs-navbar-default-selected-text: var(--rs-primary-500);
  --rs-navbar-default-hover-bg: var(--rs-primary-50);
  --rs-navbar-default-hover-text: var(--rs-primary-500);
  --rs-sidenav-default-bg: #fff;
  --rs-sidenav-default-text: var(--rs-gray-700);
  --rs-sidenav-default-selected-text: var(--rs-primary-500);
  --rs-sidenav-default-hover-bg: var(--rs-primary-50);
  --rs-sidenav-default-hover-text: var(--rs-primary-500);
  --rs-sidenav-default-footer-border: var(--rs-gray-200);
  --rs-input-bg: #fff;
  --rs-input-focus-border: var(--rs-primary-500);
  --rs-input-disabled-bg: var(--rs-gray-50);
  --rs-listbox-option-hover-bg: var(--rs-primary-50);
  --rs-listbox-option-hover-text: var(--rs-primary-500);
  --rs-listbox-option-selected-text: var(--rs-primary-500);
  --rs-listbox-option-selected-bg: var(--rs-primary-50);
  --rs-listbox-option-disabled-text: var(--rs-gray-500);
  --rs-listbox-option-disabled-selected-text: var(--rs-gray-500);
  --rs-checkbox-icon: #fff;
  --rs-checkbox-border: var(--rs-gray-300);
  --rs-checkbox-checked-bg: var(--rs-primary-500);
  --rs-checkbox-disabled-bg: var(--rs-gray-50);
  --rs-radio-marker: #fff;
  --rs-radio-border: var(--rs-gray-300);
  --rs-radio-checked-bg: var(--rs-primary-500);
  --rs-radio-disabled-bg: var(--rs-gray-50);
  --rs-rate-symbol: #8e8e93;
  --rs-rate-symbol-checked: #ffb300;
  --rs-toggle-bg: var(--rs-gray-200);
  --rs-toggle-thumb: #fff;
  --rs-toggle-loader-ring: rgba(247, 247, 250, 0.3);
  --rs-toggle-loader-rotor: #fff;
  --rs-toggle-hover-bg: #c5c6c7;
  --rs-toggle-disabled-bg: #f7f7fa;
  --rs-toggle-disabled-thumb: #fff;
  --rs-toggle-checked-bg: #3498ff;
  --rs-toggle-checked-thumb: #fff;
  --rs-toggle-checked-hover-bg: #2589f5;
  --rs-toggle-checked-disabled-bg: #cce9ff;
  --rs-toggle-checked-disabled-thumb: #fff;
  --rs-slider-bar: #f2f2f5;
  --rs-slider-hover-bar: #e5e5ea;
  --rs-slider-thumb-border: #3498ff;
  --rs-slider-thumb-bg: #fff;
  --rs-slider-thumb-hover-shadow: 0 0 0 8px rgba(52, 152, 255, 0.25);
  --rs-slider-progress: #3498ff;
  --rs-uploader-item-bg: #d9d9d9;
  --rs-uploader-item-hover-bg: #f7f7fa;
  --rs-uploader-overlay-bg: rgba(255, 255, 255, 0.8);
  --rs-uploader-dnd-bg: #fff;
  --rs-uploader-dnd-border: #e5e5ea;
  --rs-uploader-dnd-hover-border: #3498ff;
  --rs-avatar-bg: #d9d9d9;
  --rs-avatar-text: #fff;
  --rs-badge-bg: #f44336;
  --rs-badge-text: #fff;
  --rs-tag-bg: #f7f7fa;
  --rs-tag-close: #f44336;
  --rs-carousel-bg: #8e8e93;
  --rs-carousel-indicator: rgba(255, 255, 255, 0.4);
  --rs-carousel-indicator-hover: #fff;
  --rs-carousel-indicator-active: #3498ff;
  --rs-panel-shadow: 0 4px 4px rgba(0, 0, 0, 0.12), 0 0 10px rgba(0, 0, 0, 0.06);
  --rs-list-bg: #fff;
  --rs-list-border: var(--rs-gray-200);
  --rs-list-hover-bg: var(--rs-primary-50);
  --rs-list-placeholder-bg: rgba(242, 250, 255, 0.5);
  --rs-list-placeholder-border: #3498ff;
  --rs-timeline-indicator-bg: #d9d9d9;
  --rs-timeline-indicator-active-bg: #3498ff;
  --rs-table-shadow: rgba(9, 9, 9, 0.08);
  --rs-table-sort: var(--rs-primary-500);
  --rs-table-resize: var(--rs-primary-500);
  --rs-table-scrollbar-track: var(--rs-gray-200);
  --rs-table-scrollbar-thumb: var(--rs-gray-400);
  --rs-table-scrollbar-thumb-active: var(--rs-gray-500);
  --rs-table-scrollbar-vertical-track: var(--rs-gray-200);
  --rs-drawer-shadow: 0 4px 4px rgba(0, 0, 0, 0.12),
    0 0 10px rgba(0, 0, 0, 0.06);
  --rs-modal-shadow: 0 4px 4px rgba(0, 0, 0, 0.12), 0 0 10px rgba(0, 0, 0, 0.06);
  --rs-form-errormessage-text: #f44336;
  --rs-form-errormessage-bg: #fff;
  --rs-form-errormessage-border: #e5e5ea;
  --rs-picker-value: var(--rs-primary-500);
  --rs-picker-count-bg: var(--rs-primary-500);
  --rs-picker-count-text: #fff;
  --rs-calendar-today-bg: var(--rs-primary-500);
  --rs-calendar-today-text: #fff;
  --rs-calendar-range-bg: rgba(204, 233, 255, 0.5);
  --rs-calendar-time-unit-bg: #f7f7fa;
  --rs-calendar-date-selected-text: #fff;
  --rs-calendar-cell-selected-hover-bg: var(--rs-primary-600);
  --rs-popover-shadow: 0 1px 8px rgba(0, 0, 0, 0.12);
  --rs-btn-ghost-border: var(--rs-primary-500);
}
body, .rs-theme, .rs-theme * {
  font-family: Poppins, sans-serif !important;
}

.rs-sidenav-item-active {
  border-left: 2px solid var(--rs-primary-500);
}

.custom-input-number input {
  text-align: center;
}

.custom-input-number .rs-input-number-btn-group-vertical {
  display: none;
}
