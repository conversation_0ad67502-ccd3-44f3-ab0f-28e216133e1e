import MainContent from "@/components/layout/MainContent";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import ContainerLayout from "@/components/layout/ContainerLayout";
import MenuApi from "../api/menuApi";
import Head from "next/head";
import Module<PERSON><PERSON> from "../api/moduleApi";
import { Button, Stack, Form, Input, Radio, RadioGroup, SelectPicker } from "rsuite";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";

function EditMenu({ allModule, dataEdit, dataIdMenu }) {
  const router = useRouter();
  const { UpdateMenuApi, GetMenuByIdApi } = MenuApi();
  const MySwal = withReactContent(Swal);
  const [menuName, setMenuName] = useState("");
  const [isActive, setIsActive] = useState("");
  const [isParent, setIsParent] = useState("");
  const [menuLinkCode, setMenuLinkCode] = useState("");
  const [selectedMenu, setSelectedMenu] = useState([]);
  const [selectedModuleCode, setSelectedModuleCode] = useState(null);
  const [moduleCode, setModuleCode] = useState([]);
  const [createdby, setCreatedBy] = useState("");
  const [menuIcon, setMenuIcon] = useState("");
  const [isFormDisabled, setIsFormDisabled] = useState(false);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  },[]);

  useEffect(() => {
    setModuleCode(allModule);

    setMenuName(dataEdit.menuName);
    setIsActive(dataEdit.isActive);
    setIsParent(dataEdit.isParent);
    setMenuLinkCode(dataEdit.menuLinkCode);
    setCreatedBy(dataEdit.createdBy);
    setMenuIcon(dataEdit.menuIcon);
    setSelectedModuleCode(dataEdit.selectedModuleCode.Module_Code);
  }, [allModule, dataEdit]);

  // Handler
  function menuNameHandler(event) {
    setMenuName(event.target.value);
  }
  function isActiveHandler(event) {
    setIsActive(event.target.value);
  }
  function isParentHandler(event) {
    setIsParent(event.target.value);
  }
  function menuLinkCodeHandler(event) {
    setMenuLinkCode(event.target.value);
  }
  function moduleCodeHandler(event) {
    setModuleCode(event.target.value);
  }
  function createdbyHandler(event) {
    setCreatedBy(event.target.value);
  }
  function menuIconHandler(event) {
    setMenuIcon(event.target.value);
  }

  const updateMenuHandler = async (event) => {
    setIsFormDisabled(true);
    event.preventDefault();

    // Validation
    if (menuName == "" || menuIcon == "" || menuLinkCode == "" || selectedModuleCode == null || selectedModuleCode == undefined) {
      setIsFormDisabled(false);
      return MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please fill all required data !",
      });
    }

    // Mengubah nilai string menjadi integer
    let is_active = parseInt(isActive);
    let is_parent = parseInt(isParent);
    let module_code = parseInt(selectedModuleCode);

    let dataUser = {
      id_menu: parseInt(dataIdMenu),
      menu_name: menuName,
      is_active: is_active,
      is_parent: is_parent,
      menu_link_code: menuLinkCode,
      module_code: module_code,
      createdby: createdby,
      menu_icon: menuIcon,
    };

    console.log("Nilai datauser : ", dataUser);

    // Send ke backend
    const { Data: updateResult } = await UpdateMenuApi(dataUser);

    if (updateResult) {
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "Menu updated successfully.",
        allowOutsideClick: false,
        timer: 2500,
      });
      router.push("/menu_management");
    } else {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Update Menu FAILED !",
      });
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Edit Menu</title>
        </Head>
      </div>
      <ContainerLayout
        title="Menu Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <Stack style={{ marginBottom: '1.5rem' }}><p className="font-bold text-xl">Add Menu</p>
            <hr /></Stack>
          <Form fluid>
            <Form.Group>
              <Form.ControlLabel htmlFor="menu_name">Menu Name</Form.ControlLabel>
              <Form.Control
                id="menu_name"
                name="menu_name"
                accepter={Input}
                data={menuName}
                value={menuName}
                valueKey="menu_name"
                labelKey="menu_name"
                block
                required
                onChange={(value) => {
                  setMenuName(value);
                }}
              />
            </Form.Group>

            <Stack direction="row" spacing={50}>
              <Form.Group>
                <Form.ControlLabel>Is Active</Form.ControlLabel>
                <RadioGroup name="isActiveList">
                  <Radio value="1" checked onChange={value => setIsActive(value)}>Yes</Radio>
                  <Radio value="0" onChange={value => setIsActive(value)}>No</Radio>
                </RadioGroup>
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Role</Form.ControlLabel>
                <RadioGroup name="isParentList">
                  <Radio value="1" checked={isParent == "1" ? true : false} onChange={value => setIsParent(value)}>Parent</Radio>
                  <Radio value="0" checked={isParent == "0" ? true : false} onChange={value => setIsParent(value)}>Child</Radio>
                </RadioGroup>
              </Form.Group>
            </Stack>

            <Form.Group>
              <Form.ControlLabel htmlFor="menu_link_code">Menu Link Code</Form.ControlLabel>
              <Form.Control
                id="menu_link_code"
                name="menu_link_code"
                accepter={Input}
                data={menuLinkCode}
                value={menuLinkCode}
                valueKey="menu_link_code"
                labelKey="menu_link_code"
                block
                required
                onChange={(value) => {
                  setMenuLinkCode(value);
                }}
              />
            </Form.Group>

            <Form.Group>
              <Form.ControlLabel>Module Code</Form.ControlLabel>
              <Form.Control
                name="module_code"
                accepter={SelectPicker}
                value={selectedModuleCode}
                data={allModule}
                valueKey="Module_Code"
                labelKey="Module_Name"
                block
                onChange={(value) => {
                  setSelectedModuleCode(value);
                }}
              />
            </Form.Group>

            <Form.Group>
              <Form.ControlLabel htmlFor="menu_icon">Menu Icon</Form.ControlLabel>
              <Form.Control
                id="menu_icon"
                name="menu_icon"
                accepter={Input}
                data={menuIcon}
                value={menuIcon}
                valueKey="menu_icon"
                labelKey="menu_icon"
                block
                onChange={(value) => {
                  setMenuIcon(value);
                }}
              />
            </Form.Group>

            <Form.Group>
              <Stack direction="row" spacing={6}>
                <Button appearance="primary" disabled={isFormDisabled} onClick={updateMenuHandler} type="submit">
                  Submit
                </Button>
                <Button appearance="subtle" disabled={isFormDisabled} onClick={() => router.back()}>
                  Cancel
                </Button>
              </Stack>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export default EditMenu;

export async function getServerSideProps({ query }) {
  const { dataIdMenu } = query;

  const { GetModuleAll } = ModuleApi();
  const { GetMenuByIdApi } = MenuApi();

  const { Data: allModuleData } = await GetModuleAll();
  let allModule = [];
  if (allModuleData != undefined) {
    allModule = allModuleData;
  }

  const requestMenuData = {
    id_menu: parseInt(dataIdMenu),
  };
  const { Data: dataToEditResult } = await GetMenuByIdApi(requestMenuData);
  let dataToEdit = [];
  if (dataToEditResult != undefined) {
    dataToEdit = dataToEditResult;
  }

  const selectedModuleCode = dataToEdit[0].Module_Code;
  const selectedModuleCodeForMenu = allModule.filter(
    (item) => item.Module_Code === selectedModuleCode
  );

  let dataEdit = {
    menuName: dataToEdit[0].Menu_Name,
    isActive: `${dataToEdit[0].Is_active}`,
    isParent: `${dataToEdit[0].Is_parent}`,
    menuLinkCode: dataToEdit[0].Menu_Link_Code,
    createdBy: dataToEdit[0].Created_By,
    menuIcon: dataToEdit[0].Menu_Icon,
    selectedModuleCode: selectedModuleCodeForMenu[0],
  };

  return {
    props: {
      allModule,
      dataEdit,
      dataIdMenu,
    },
  };
}
