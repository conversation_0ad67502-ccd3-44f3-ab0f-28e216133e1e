import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import PdfRecipeComponent from "@/components/pqr/PDFApprovalPPIComponents";

pdfMake.vfs = pdfFonts.pdfMake.vfs;

export default function DetailPDF() {
  const router = useRouter();
  const { idPPI } = router.query;
  const [idRouter, setIdRouter] = useState(null);
  const [sessionAuth, setSessionAuth] = useState(null);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else if (idPPI) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("ipc")
      );
      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      setIdRouter(idPPI);
    }
  }, [router, idPPI]);
  

  return (
    <PdfRecipeComponent idPPI={idRouter} sessionAuth={sessionAuth}/>
  );
}
