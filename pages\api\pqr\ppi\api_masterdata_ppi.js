import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiMasterdata_ppi() {
  return {
    getAllMasterPPI: createApiFunction("get", "pqr/masterdata_ppi/list"),
    getAllActiveMasterPPI: createApiFunction("get", "pqr/masterdata_ppi/list-active"),
    getAllANeedApproveMasterPPI: createApiFunction("get", "pqr/masterdata_ppi/need-approve"),
    getAllFullyApproveMasterPPI: createApiFunction("get", "pqr/masterdata_ppi/fully-approve"),
    createMasterPPI: createApiFunction("post", "pqr/masterdata_ppi/create"),
    GetMasterPPIById: createApiFunction("post", "pqr/masterdata_ppi/id"),
    editMasterPPI: createApiFunction("put", "pqr/masterdata_ppi/edit"),
    editStatusMasterPPI: createApiFunction("put", "pqr/masterdata_ppi/edit-status"),
    editStatusSubmitPPI: createApiFunction("put", "pqr/masterdata_ppi/edit-status-submit"),
    editStatusApprovalPPI: createApiFunction("put", "pqr/masterdata_ppi/edit-approval"),

  };
}
