import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import MainContent from '@/components/layout/MainContent';
import { Dropdown, Button, Form, ButtonToolbar, Toggle } from 'rsuite';
import Head from 'next/head';
import withReactContent from 'sweetalert2-react-content';
import Swal from 'sweetalert2';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import UseTestApi from '@/pages/api/userApi';
import ReviewerApproverApi from '@/pages/api/reviewerApprover';

export default function EditReviewerApprover({ idSetup }) {
  const { GetAllRole, UpdateReviewerApprover, GetReviewerApproverById } =
    ReviewerApproverApi();
  const MySwal = withReactContent(Swal);
  const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
  const [reviewerApproverEditData, setReviewerApproverEditData] = useState([]);
  const [selectedUser, setSelectedUser] = useState({});
  const [employeeIdCreator, setEmployeeIdCreator] = useState('');
  const [moduleName, setModuleName] = useState('');
  const [employeeId, setEmployeeId] = useState('');
  const [employeeName, setEmployeeName] = useState('');
  const [departmentName, setDepartmentName] = useState('');
  const [allRole, setAllRole] = useState([]);
  const [selectedRole, setSelectedRole] = useState({});
  const [isActive, setIsActive] = useState(false);
  const router = useRouter();
  let path = 'masterdata/ReviewerApprover';
  // let {data} = router.query;
  // data ? (data = JSON.parse(data)) : data;
  const [breadcrumbsData, setBreadcrumbsData] = useState([]);

  const GetReviewerApproverByIdData = async (idData) => {
    const { data: reviewerApproverData } = await GetReviewerApproverById(
      idData,
    );

    if (reviewerApproverData !== null && reviewerApproverData !== undefined) {
      setReviewerApproverEditData(reviewerApproverData);
      setEmployeeId(reviewerApproverData[0].Employee_Id);
      setEmployeeName(reviewerApproverData[0].Employee_Name);
      setDepartmentName(reviewerApproverData[0].Department_Name);
      if (reviewerApproverData[0].Is_Active === 1) {
        setIsActive(true);
      } else {
        setIsActive(false);
      }
    }
  };

  const GetAllRoleData = async () => {
    const { data: roleData } = await GetAllRole();

    if (roleData !== null && roleData !== undefined && roleData) {
      setAllRole(roleData);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem('module_name');
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
      router.push('/');
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push('/dashboard');
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path),
    );

    if (validateUserAccess.length === 0) {
      router.push('/dashboard');
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ''
    ) {
      router.push('/dashboard');
      return;
    }

    const asPathWithoutQuery = router.asPath.split('?')[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split('/')
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      `Setup ${asPathNestedRoutes[1]}`,
      `Edit ${asPathNestedRoutes[2]}`,
    ];
    setBreadcrumbsData(routerBreadCrumbsData);

    setModuleName(moduleNameValue);

    setEmployeeIdCreator(dataLogin.employee_id);

    const requestDataId = {
      id_setup: parseInt(idSetup),
    };

    GetReviewerApproverByIdData(requestDataId);
    GetAllRoleData();
  }, []);

  useEffect(() => {
    if (reviewerApproverEditData.length > 0) {
      const existingSelectedRole = allRole.filter(
        (role) => role.Id_Role === reviewerApproverEditData[0].Role,
      );
      if (existingSelectedRole !== null && existingSelectedRole !== undefined) {
        setSelectedRole(existingSelectedRole[0]);
      }
    }
  }, [reviewerApproverEditData, allRole]);

  const selectedRoleChangeHandler = (value) => {
    setSelectedRole({ ...value });
  };

  //   submitHandler
  const submitHandler = async () => {
    if (selectedRole.Id_Role === '') {
      MySwal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Please fill all required data!',
      });
      return;
    } else {
      MySwal.showLoading();
      const isUserActive = isActive ? 1 : 0;

      const dataUpdate = {
        employee_id: employeeId,
        is_active: isUserActive,
        role_id: parseInt(selectedRole.Id_Role),
      };

      const { message, status } = await UpdateReviewerApprover(dataUpdate);

      if (status === 200) {
        setIsSubmitButtonDisabled(true);
        MySwal.fire({
          position: 'center',
          icon: 'success',
          title: message,
          showConfirmButton: false,
          timer: 2500,
        });
        router.push('/user_module/masterdata/ReviewerApprover');
      } else {
        MySwal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'Update FAILED.',
        });
      }
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Edit Review & Approver</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <MainContent>
          <ModuleContentHeader
            breadcrumbs={breadcrumbsData}
            module_name={moduleName}
          />

          <Form layout="horizontal" onSubmit={submitHandler}>
            <Form.Group>
              <Form.ControlLabel>Employee ID :</Form.ControlLabel>
              <Form.Control
                name="employee_name"
                type="text"
                value={employeeId !== '' ? employeeId : 'NOT Found'}
                disabled={true}
              />
            </Form.Group>
            <Form.Group controlId="employee_name">
              <Form.ControlLabel>Employee Name :</Form.ControlLabel>
              <Form.Control
                name="employee_name"
                type="text"
                value={employeeName !== '' ? employeeName : 'NOT Found'}
                disabled={true}
              />
            </Form.Group>
            <Form.Group controlId="department">
              <Form.ControlLabel>Department :</Form.ControlLabel>
              <Form.Control
                name="department"
                type="text"
                value={departmentName !== '' ? departmentName : 'NOT Found'}
                disabled={true}
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>Role :</Form.ControlLabel>
              <Dropdown
                title={
                  selectedRole.Role_Desc !== undefined &&
                  selectedRole.Role_Desc !== ''
                    ? selectedRole.Role_Desc
                    : '- Select Role -'
                }
                onSelect={selectedRoleChangeHandler}
              >
                <Dropdown.Item eventKey="">- Select Role -</Dropdown.Item>
                {allRole.length > 0 &&
                  allRole.map((role) => (
                    <Dropdown.Item key={role.Id_Role} eventKey={role}>
                      {role.Role_Desc}
                    </Dropdown.Item>
                  ))}
              </Dropdown>
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>Active</Form.ControlLabel>
              <Toggle
                checked={isActive}
                onChange={() => setIsActive((value) => !value)}
              />
            </Form.Group>

            <Form.Group>
              <ButtonToolbar>
                <Button
                  appearance="primary"
                  type="submit"
                  disabled={isSubmitButtonDisabled}
                >
                  Submit
                </Button>
                <Button appearance="default" onClick={() => router.back()}>
                  Cancel
                </Button>
              </ButtonToolbar>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  const { idSetup } = query;

  return {
    props: {
      idSetup,
    },
  };
}
