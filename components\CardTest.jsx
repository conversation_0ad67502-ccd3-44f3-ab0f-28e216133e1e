import { useEffect, useRef, useState } from 'react';
import { Input, InputNumber, TreePicker, Stack } from 'rsuite';
import ParameterApi from '@/pages/api/parameterApi';

export default function CardTest({ cardValueHandler }) {
  const [allParameterData, setAllParameterData] = useState([]);
  const [descValue, setDescValue] = useState('');
  const descRef = useRef('');
  const valueReferenceRef = useRef(0);
  const rangeMinRef = useRef(0);
  const rangeMaxref = useRef(0);
  const [absoluteValue, setAbsoluteValue] = useState();
  const [rangeMin, setRangeMin] = useState();
  const [rangeMax, setRangeMax] = useState();
  const [dataDetail, setDataDetail] = useState({});
  const [treePickerData, setTreePickerData] = useState([]);
  const [selectedParameter, setSelectedParameter] = useState({});

  const { GetAllParameter } = ParameterApi();

  const GetAllParameterData = async () => {
    const { data: parameterData } = await GetAllParameter();
    if (parameterData !== null && parameterData !== undefined) {
      let treePickerDataStore = [];
      parameterData.map((parameter) => {
        const data = {
          value: parameter.Id_Parameter,
          label: parameter.Parameter_Name,
        };
        treePickerDataStore.push(data);
      });

      setTreePickerData(treePickerDataStore);
      setAllParameterData(parameterData);
    }
  };

  const selectParameterHandler = (value) => {
    const parameter = allParameterData.filter(
      (item) => item.Id_Parameter === value,
    );

    setSelectedParameter({ ...parameter[0] });

    setIsDisabled(false);
  };

  useEffect(() => {
    GetAllParameterData();
  }, []);

  const dataDetailForm = {
    desc: descValue,
    value_reference: absoluteValue,
    min_value: rangeMin,
    max_value: rangeMax,
    id_parameter: selectedParameter.Id_Parameter,
  };

  // useEffect(() => {
  //   setDataDetail({ dataDetailForm });
  //   console.log(dataDetail);
  // }, []);

  const handleFormChange = (name, e) => {
    let data = dataDetailForm;
    data[name] = e.target.value;
    setDataDetail({ ...data });
  };
  const handleFormDesc = (v) => {
    let data = dataDetailForm;
    data.desc = v;
    setDataDetail(data);
  };

  useEffect(() => {
    let data = {
      desc: descValue,
      value_reference: absoluteValue,
      min_value: rangeMin,
      max_value: rangeMax,
      id_parameter: selectedParameter.Id_Parameter,
    };

    setDataDetail({
      ...data,
    });

    // cardValueHandler();
  }, [descValue, rangeMin, rangeMax, absoluteValue]);

  const [isDisabled, setIsDisabled] = useState(true);
  return (
    <Stack
      style={{
        padding: '0.5em',
        border: '0.1em solid #adadad',
        borderRadius: '5px 5px 5px 5px',
        marginBottom: '0.5em',
      }}
    >
      <Stack direction="column" alignItems="flex-start">
        <p>
          <strong>Parameter</strong>
        </p>
        <TreePicker
          data={treePickerData}
          defaultExpandAll
          onChange={selectParameterHandler}
          style={{ minWidth: 246 }}
        />
      </Stack>
      <Stack
        direction="column"
        alignItems="flex-start"
        style={{ marginLeft: '0.5em' }}
      >
        <p>
          <strong>Spesifikasi</strong>
        </p>
        {selectedParameter.Method_Desc == undefined && (
          <Input disabled={true} />
        )}
        {selectedParameter.Method_Desc !== undefined &&
          selectedParameter.Method_Desc !== null &&
          selectedParameter.Method_Desc.toLowerCase() === 'absolute' && (
            <InputNumber
              value={absoluteValue}
              onChange={(v, e) => handleFormChange('value_reference', e)}
              // onChange={(value) => {
              //   setAbsoluteValue(value);
              //   console.log('NIlai value : ', absoluteValue);
              // }}
              required
            />
          )}
        {selectedParameter.Method_Desc !== undefined &&
          selectedParameter.Method_Desc !== null &&
          selectedParameter.Method_Desc.toLowerCase() === 'description' && (
            <Input
              as="textarea"
              value={descValue}
              onChange={(v) => {
                // handleFormText('desc', e);
                console.log('Nilai v :', v);
                handleFormDesc(v);
                setDescValue(v);
              }}
              type="text"
              required
            />
          )}
        {selectedParameter.Method_Desc !== undefined &&
          selectedParameter.Method_Desc !== null &&
          selectedParameter.Method_Desc.toLowerCase() === 'range' && (
            <Stack>
              <InputNumber
                value={rangeMin}
                // onChange={(value) => {
                //   setRangeMin(value);
                //   console.log('NIlai value : ', value);
                // }}
                onChange={(v, e) => handleFormChange('min_value', e)}
                required
                style={{ maxWidth: '7em' }}
              />{' '}
              -{' '}
              <InputNumber
                value={rangeMax}
                // onChange={(value) => {
                //   setRangeMax(value);
                //   console.log('NIlai value : ', value);
                // }}
                onChange={(v, e) => handleFormChange('max_value', e)}
                required
                style={{ maxWidth: '7em' }}
              />
            </Stack>
          )}
      </Stack>
    </Stack>
  );
}
