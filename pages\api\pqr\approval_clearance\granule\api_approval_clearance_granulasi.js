import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](
    `${process.env.NEXT_PUBLIC_BASE_URL}/${url}`,
    data
  )
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiApprovalClearanceGranulasi() {
  return {
    PutUpdateApprovalClear: createApiFunction(
      "put",
      "pqr/pqr_approval_clearance_granulasi/update-approve"
    ),
    PutUpdateRevisiClear: createApiFunction(
        "put",
        "pqr/pqr_approval_clearance_granulasi/update-revisi"
      ),
  };
}
