import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";

import {
  <PERSON>ton,
  IconButton,
  Stack,
  Breadcrumb,
  Tag,
  Table,
  Panel,
  Input,
  InputGroup,
  Pagination,
  Divider,
  Modal,
  Form,
  Schema,
  useToaster,
  DatePicker,
  SelectPicker,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";

import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";

import API_TsMatrixCategoriesParameter from "@/pages/api/ts/api_ts-matrix-categories-parameter";
import API_TsParameter from "@/pages/api/ts/api_ts-parameter";
import API_TsMainCategories from "@/pages/api/ts/api_ts-main-categories";

export default function TsMatrixCategoriesParameter() {
  const router = useRouter();
  const { HeaderCell, Cell, Column } = Table;

  const [loading, setLoading] = useState(false);
  const [moduleName, setModuleName] = useState("");
  const [tsMatrixCategoriesParameterData, setTsMatrixCategoriesParameterData] =
    useState([]);
  const [tsParameterData, setTsParameterData] = useState([]);
  const [tsMainCategoriesData, setTsMainCategoriesData] = useState([]);

  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [showActivateModal, setShowActivateModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);

  const [
    selectedTsMatrixCategoriesParameter,
    setSelectedTsMatrixCategoriesParameter,
  ] = useState(null);
  const [
    selectedMatrixCategoriesParameterActive,
    setSelectedMatrixCategoriesParameterActive,
  ] = useState(null);

  const toaster = useToaster();

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [props, setProps] = useState([]);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    // console.log("dataLogin", dataLogin);
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("masterdata/TsMatrixCategoriesParameter")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      const fetchData = async () => {
        const _tsMatrixCategoriesParameter =
          await API_TsMatrixCategoriesParameter().getAllTsMatrixCategoriesParameter();
        setTsMatrixCategoriesParameterData(
          _tsMatrixCategoriesParameter.data || []
        );

        const _tsParameter = await API_TsParameter().getActiveParameterTS();
        setTsParameterData(_tsParameter.data || []);

        const _tsMainCategories =
          await API_TsMainCategories().getActiveTsMainCategories();
        setTsMainCategoriesData(_tsMainCategories.data || []);
        console.log("_tsMainCategories", _tsMainCategories);
      };

      fetchData();
    }
  }, []);

  const [formValue, setFormValue] = useState({
    id_categories: null,
    id_parameter: null,
    setup_date: "",
  });

  const requireInputRule = Schema.Types.StringType().isRequired(
    "This field is required."
  );

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id_matrix",
    "id_categories",
    "id_parameter",
    "is_active",
    "setup_date",
    "main_category_name",
    "parameter_name",
  ];

  const datas =
    tsMatrixCategoriesParameterData &&
    tsMatrixCategoriesParameterData.length > 0
      ? tsMatrixCategoriesParameterData
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .concat(
                item.is_active === 1
                  ? "Active"
                  : item.is_active === 0
                  ? "Inactive"
                  : null
              )
              .some((val) => val.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const dataSearchTsMatrixCategoriesParameter = tsMatrixCategoriesParameterData
    ? tsMatrixCategoriesParameterData.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];

  const handleDeletetsMatrixCategoriesParameter = async () => {
    setLoading(true);
    try {
      // Make the delete API call with the selectedTsMatrixCategoriesParameter
      let result =
        await API_TsMatrixCategoriesParameter().sfDeleteTsMatrixCategoriesParameter(
          {
            id_matrix: selectedTsMatrixCategoriesParameter,
          }
        );
      if (result?.status !== 200) {
        throw "Delete TS Matrix Categories Parameter failed";
      }

      setFormValue({ id_categories: null, id_parameter: null });

      // Fetch the updated TS Matrix Categories Parameter list
      const res =
        await API_TsMatrixCategoriesParameter().getAllTsMatrixCategoriesParameter();
      setTsMatrixCategoriesParameterData(res.data);

      // Close the delete modal
      setShowDeactivateModal(false);

      // Show the toast message
      toaster.push(
        Messages(
          "success",
          "Success deactivating TS Matrix Categories Parameter!"
        ),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    } catch (error) {
      console.error(
        "Error deactivating TS Matrix Categories Parameter:",
        error
      );
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
    setLoading(false);
  };

  const handleActivatetsMatrixCategoriesParameter = async () => {
    setLoading(true);
    try {
      // Make the activate API call with the selectedTsMatrixCategoriesParameter
      let result =
        await API_TsMatrixCategoriesParameter().sfUndeleteTsMatrixCategoriesParameter(
          {
            id_matrix: selectedTsMatrixCategoriesParameter,
          }
        );
      if (result?.status !== 200) {
        throw "Activate TS Matrix Categories Parameter failed";
      }

      setFormValue({ id_categories: null, id_parameter: null });

      // Fetch the updated TS Matrix Categories Parameter list
      const res =
        await API_TsMatrixCategoriesParameter().getAllTsMatrixCategoriesParameter();
      setTsMatrixCategoriesParameterData(res.data);

      // Close the activate modal
      setShowActivateModal(false);

      // Show the toast message
      toaster.push(
        Messages(
          "success",
          "Success activating TS Matrix Categories Parameter!"
        ),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    } catch (error) {
      console.error("Error activating TS Matrix Categories Parameter:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
    setLoading(false);
  };

  const handleAddtsMatrixCategoriesParameter = async () => {
    console.log("Add TS Matrix Categories Parameter with data:", formValue);

    setLoading(true);
    try {
      // Make the add API call with the formValue
      let result =
        await API_TsMatrixCategoriesParameter().addTsMatrixCategoriesParameter({
          ...formValue,
          is_active: 1,
        });

      if (result?.status !== 200) {
        throw "Add TS Matrix Categories Parameter failed";
      }

      // Fetch the updated TS Matrix Categories Parameter list
      const res =
        await API_TsMatrixCategoriesParameter().getAllTsMatrixCategoriesParameter();
      setTsMatrixCategoriesParameterData(res.data);

      // Close the add modal
      setShowAddModal(false);

      // Show the toast message
      toaster.push(
        Messages(
          "success",
          "Success adding a new TS Matrix Categories Parameter!"
        ),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );

      // Reset the form value
      setFormValue({
        id_categories: null,
        id_parameter: null,
        setup_date: "",
      });
    } catch (error) {
      console.error("Error adding TS Matrix Categories Parameter:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
    setLoading(false);
  };

  const handleUpdatetsMatrixCategoriesParameter = async () => {
    setLoading(true);
    try {
      console.log(
        "Update TS Matrix Categories Parameter with data:",
        formValue
      );
      // Make the update API call with the selectedTsMatrixCategoriesParameter and formValue
      let result =
        await API_TsMatrixCategoriesParameter().updateTsMatrixCategoriesParameter(
          {
            ...formValue,
            id_matrix: selectedTsMatrixCategoriesParameter,
            is_active: selectedMatrixCategoriesParameterActive,
          }
        );
      if (result?.status !== 200) {
        throw ("Update TS Matrix Categories Parameter failed", formValue);
      }

      // Fetch the updated TS Matrix Categories Parameter list
      const res =
        await API_TsMatrixCategoriesParameter().getAllTsMatrixCategoriesParameter();
      setTsMatrixCategoriesParameterData(res.data);

      // Close the update modal
      setShowUpdateModal(false);

      // Show the toast message
      toaster.push(
        Messages("success", "Success updating TS Matrix Categories Parameter!"),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );

      // Reset the form value
      setFormValue({
        id_categories: null,
        id_parameter: null,
        setup_date: "",
      });
    } catch (error) {
      console.error("Error updating TS Matrix Categories Parameter:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
    setLoading(false);
  };

  return (
    <>
      <div>
        <Head>
          <title>Master Data TS Matrix Categories Parameter</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Research and Development</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>
                    TS Matrix Categories Parameter
                  </Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <IconButton
                  icon={<PlusIcon />}
                  appearance="primary"
                  onClick={() => {
                    setShowAddModal(true);
                  }}
                >
                  Add
                </IconButton>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              data={datas}
              bordered
              cellBordered
              height={400}
              onRowClick={(rowData) => {
                console.log(rowData);
              }}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>

              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id_matrix" />
              </Column>

              <Column width={250} sortable resizable>
                <HeaderCell>Categories Name</HeaderCell>
                <Cell dataKey="id_categories">
                  {(rowData) => {
                    const mainCategoryNames = tsMainCategoriesData
                      .filter(
                        (item) => item.id_categories === rowData.id_categories
                      )
                      .map((item) => item.main_category_name);
                    return mainCategoryNames.join(", ");
                  }}
                </Cell>
              </Column>

              <Column width={250} sortable resizable>
                <HeaderCell>Parameter Name</HeaderCell>
                <Cell dataKey="id_parameter">
                  {(rowData) => {
                    const foundItem = tsParameterData.find(
                      (item) => item.id_parameter === rowData.id_parameter
                    );
                    return foundItem ? foundItem.parameter_name : "";
                  }}
                </Cell>
              </Column>

              <Column width={200} sortable resizable>
                <HeaderCell>Setup Date</HeaderCell>
                <Cell dataKey="setup_date" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Is Active?</HeaderCell>
                <Cell dataKey="is_active">
                  {(rowData) => (
                    <span>
                      {rowData.is_active === 1 ? (
                        <div className="flex items-center">
                          <div className="h-2.5 w-2.5 rounded-full bg-green-500 mr-2"></div>{" "}
                          Active
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <div className="h-2.5 w-2.5 rounded-full bg-red-500 mr-2"></div>{" "}
                          Inactive
                        </div>
                      )}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={150} fixed="right">
                <HeaderCell>Action</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span>
                      <a
                        className="cursor-pointer"
                        onClick={() => {
                          setSelectedTsMatrixCategoriesParameter(
                            rowData.id_matrix
                          );
                          setSelectedMatrixCategoriesParameterActive(
                            rowData.is_active
                          );
                          setFormValue({
                            id_categories: rowData.id_categories,
                            id_parameter: rowData.id_parameter,
                            setup_date: new Date(rowData.setup_date),
                          });
                          setShowUpdateModal(true);
                        }}
                      >
                        Edit
                      </a>
                      <Divider vertical />
                      {rowData.is_active === 1 ? (
                        <a
                          onClick={() => {
                            setSelectedTsMatrixCategoriesParameter(
                              rowData.id_matrix
                            );
                            setShowDeactivateModal(true);
                          }}
                          className="cursor-pointer text-red-500 hover:text-red-500"
                        >
                          Deactivate
                        </a>
                      ) : (
                        <a
                          onClick={() => {
                            setSelectedTsMatrixCategoriesParameter(
                              rowData.id_matrix
                            );
                            setShowActivateModal(true);
                          }}
                          className="cursor-pointer text-green-700 hover:text-green-700"
                        >
                          Activate
                        </a>
                      )}
                    </span>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                total={
                  searchKeyword
                    ? dataSearchTsMatrixCategoriesParameter.length
                    : tsMatrixCategoriesParameterData.length
                }
                limitOptions={[10, 30, 50]}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Deactivate Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showDeactivateModal}
          onClose={() => {
            setShowDeactivateModal(false);
            setFormValue({
              id_categories: null,
              id_parameter: null,
            });
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Info</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>
              Are you sure you want to deactivate this TS Matrix Categories?
            </p>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowDeactivateModal(false);
                setFormValue({
                  id_categories: null,
                  id_parameter: null,
                });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeletetsMatrixCategoriesParameter}
              color="red"
              appearance="primary"
              loading={loading}
            >
              Deactivate
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Activate Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showActivateModal}
          onClose={() => {
            setShowActivateModal(false);
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Info</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>Are you sure you want to activate this TS Matrix Categories?</p>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowActivateModal(false);
                setFormValue({
                  id_categories: null,
                  id_parameter: null,
                });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={handleActivatetsMatrixCategoriesParameter}
              appearance="primary"
              loading={loading}
            >
              Activate
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Add Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue({
              id_categories: null,
              id_parameter: null,
              setup_date: "",
            });
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Add TS Matrix Categories Parameter</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Categories Name</Form.ControlLabel>
                <Form.Control
                  name="id_categories"
                  accepter={SelectPicker}
                  data={tsMainCategoriesData}
                  valueKey="id_categories"
                  labelKey="main_category_name"
                  block
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_categories: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Parameter Name</Form.ControlLabel>
                <Form.Control
                  name="id_parameter"
                  accepter={SelectPicker}
                  data={tsParameterData}
                  valueKey="id_parameter"
                  labelKey="parameter_name"
                  block
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_parameter: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Setup Date</Form.ControlLabel>
                <Form.Control
                  name="Setup Date"
                  accepter={DatePicker}
                  block
                  onChange={(value) => {
                    setFormValue({ ...formValue, setup_date: value });
                  }}
                  required
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowAddModal(false);
                setFormValue({
                  id_categories: null,
                  id_parameter: null,
                });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (
                  formValue.id_categories === null ||
                  formValue.id_categories === ""
                ) {
                  toaster.push(
                    Messages("warning", "Categories Name cannot be empty"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                } else if (
                  formValue.id_parameter === null ||
                  formValue.id_parameter === ""
                ) {
                  toaster.push(
                    Messages("warning", "Parameter Name cannot be empty"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                }
                handleAddtsMatrixCategoriesParameter();
                setFormValue({
                  id_categories: null,
                  id_parameter: null,
                });
                console.log(
                  "Add TS Matrix Categories Parameter with data:",
                  formValue
                );
              }}
              appearance="primary"
              type="submit"
              loading={loading}
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Edit Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showUpdateModal}
          onClose={() => {
            setShowUpdateModal(false);
            setFormValue({
              id_categories: null,
              id_parameter: null,
            });
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Edit TS Matrix Categories Parameter</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Categories Name</Form.ControlLabel>
                <Form.Control
                  name="id_categories"
                  accepter={SelectPicker}
                  value={formValue.id_categories}
                  data={tsMainCategoriesData}
                  valueKey="id_categories"
                  labelKey="main_category_name"
                  block
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_categories: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Parameter Name</Form.ControlLabel>
                <Form.Control
                  name="id_parameter"
                  accepter={SelectPicker}
                  value={formValue.id_parameter}
                  data={tsParameterData}
                  valueKey="id_parameter"
                  labelKey="parameter_name"
                  block
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_parameter: value });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Setup Date</Form.ControlLabel>
                <Form.Control
                  name="Setup Date"
                  accepter={DatePicker}
                  block
                  value={formValue.setup_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, setup_date: value });
                  }}
                  required
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowUpdateModal(false);
                setFormValue({
                  id_categories: null,
                  id_parameter: null,
                  setup_date: "",
                });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (
                  formValue.id_categories === null ||
                  formValue.id_categories === ""
                ) {
                  toaster.push(
                    Messages("warning", "TS Categories Name cannot be empty"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                } else if (
                  formValue.id_parameter === null ||
                  formValue.id_parameter === ""
                ) {
                  toaster.push(
                    Messages(
                      "warning",
                      "TS Matrix Categories Parameter Code cannot be empty"
                    ),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                } else if (
                  formValue.setup_date === null ||
                  formValue.setup_date === ""
                ) {
                  toaster.push(
                    Messages("warning", "Setup Date cannot be empty"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                }
                handleUpdatetsMatrixCategoriesParameter();
                setFormValue({
                  id_categories: null,
                  id_parameter: null,
                  setup_date: "",
                });
                console.log(
                  "Update TS Matrix Categories Parameter with data:",
                  formValue
                );
              }}
              appearance="primary"
              type="submit"
              loading={loading}
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
