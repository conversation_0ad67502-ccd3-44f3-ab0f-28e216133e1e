import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";

import {
  Button,
  IconButton,
  Stack,
  Breadcrumb,
  Tag,
  Table,
  Panel,
  Input,
  InputGroup,
  Pagination,
  Divider,
  Modal,
  Form,
  Schema,
  useToaster,
  SelectPicker
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";

import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";

import API_TsParameter from "@/pages/api/ts/api_ts-parameter";
import ApiTsMasterSpv from "@/pages/api/ts/api_master_spv";

export default function SpvTs() {
  const router = useRouter();
  const { HeaderCell, Cell, Column } = Table;

  const [isLoading, setIsLoading] = useState(true); // Initialize as true
  const [moduleName, setModuleName] = useState("");
  const [parameterTsData, setParameterTsData] = useState([]);
  const [spvMasterData, setSpvMaterData]  = useState([]);
  const [spvUnassigned, setSpvUnassigned] = useState([])

  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [showActivateModal, setShowActivateModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);

  const [selectedParameterTsId, setSelectedParameterTsId] = useState(null);
  const [selectedParameterTsActive, setSelectedParameterTsActive] =
    useState(null);

    const [selectedTsSpvId, setSelectedTsSpvId] = useState(null);
    const [selectedTsSpvActive, setSelectedTsSpvActive] =
      useState(null);  

  const toaster = useToaster();

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [props, setProps] = useState([]);

  useEffect(() => {
   

    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setProps(dataLogin);
    const moduleNameValue = localStorage.getItem("module_name");

    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      fetchData();
    }
  }, []);

  const fetchData = async () => {
    const result = await API_TsParameter().getAllParameterTs();
    const resultMasterSpv = await ApiTsMasterSpv().GetAllSpv();
    const resultSpvUnassigned = await ApiTsMasterSpv().GetAllUnassignedSpv();


    setSpvMaterData(resultMasterSpv.data ? resultMasterSpv.data :[]);
    setSpvUnassigned(resultSpvUnassigned.data ? resultSpvUnassigned.data :[])

    // console.log("result data spv",resultMasterSpv.data)
    // console.log("result data",result.data)
    console.log("result unassigned", resultSpvUnassigned)



    setParameterTsData(result.data ? result.data : []);
    setIsLoading(false);
    // console.log("result", result);
  };

  const [formValue, setFormValue] = useState({
    employee_id: "",
    employee_name: "",
    created_by: "",
    updated_by: "",
    deleted_by: "",
    status_active: null,
  });

  const requireInputRule = Schema.Types.StringType().isRequired(
    "This field is required."
  );

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "employee_id",
    "employee_name",
    "job_title",
    "created_at",
    "created_by",
    "updated_at",
    "updated_by",
    "deleted_at",
    "deleted_by",
  ];

  const datas =
    parameterTsData && parameterTsData.length > 0
      ? parameterTsData
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .concat(
                item.is_active === 1
                  ? "Active"
                  : item.is_active === 0
                  ? "Inactive"
                  : null
              )
              .some((val) => val.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const dataSearchParameterTs = parameterTsData
    ? parameterTsData.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];


  const handleDeleteTsSpv = async () => {
    try {
      // Make the delete API call with the selectedParameterTsId
      let result = await ApiTsMasterSpv().PutDeleteSpv({
        employee_id: formValue.employee_id,
        updated_by: props.employee_id,
        status_active: 0,
      });
      if (result?.status !== 200) {
        throw "Delete TS Spv failed";
      }

      setFormValue({ employee_id: null, employee_name: null  });

      // Fetch the updated TS Parameter list
      fetchData()

      // Close the delete modal
      setShowDeactivateModal(false);

      // Show the toast message
      toaster.push(Messages("success", "Success deactivating TS Parameter!"), {
        placement: "topCenter",
        duration: 5000,
      });
    } catch (error) {
      // console.error("Error deactivating TS Parameter:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleActivateTsSpv = async () => {
    try {
      // Make the activate API call with the selectedParameterTsId
      let result = await ApiTsMasterSpv().PutDeleteSpv({
        employee_id: formValue.employee_id,
        updated_by: props.employee_id,
        status_active: 1,
      });
      if (result?.status !== 200) {
        throw "Activate TS Spv failed";
      }

      setFormValue({ employee_id: null, employee_name: null });

      fetchData()

      // Close the activate modal
      setShowActivateModal(false);

      // Show the toast message
      toaster.push(Messages("success", "Success activating TS Spv!"), {
        placement: "topCenter",
        duration: 5000,
      });
    } catch (error) {
      // console.error("Error activating TS Parameter:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };



  const handleAddTsSpv = async () => {
    // console.log("Add TS Parameter with data:", formValue);
    try {
      // Make the add API call with the formValue
      let result = await ApiTsMasterSpv().PostSpv({
        ...formValue,
        created_by: props.employee_id,
        is_active: 1,
      });

      if (result?.status !== 200) {
        throw "Add TS Spv  failed";
      }

      // Fetch the updated TS Parameter list
      fetchData()

      // Close the add modal
      setShowAddModal(false);

      // Show the toast message
      toaster.push(Messages("success", "Success adding a new TS Spv!"), {
        placement: "topCenter",
        duration: 5000,
      });

      // Reset the form value
      setFormValue({
        employee_id: "",
        employee_name:"",
        created_by: "",
        updated_by: "",
        deleted_by: "",
        status_active: null,
      });
    } catch (error) {
      // console.error("Error adding TS Parameter:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };


  return (
    <>
      <div>
        <Head>
          <title>Master Data TS Spv</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Research and Development</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>TS Spv</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <IconButton
                  icon={<PlusIcon />}
                  appearance="primary"
                  onClick={() => {
                    setShowAddModal(true);
                  }}
                >
                  Add
                </IconButton>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              data={spvMasterData}
              bordered
              cellBordered
              height={400}
              onRowClick={(rowData) => {
                // console.log(rowData);
              }}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>

              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id_role" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Employee Id</HeaderCell>
                <Cell dataKey="employee_id" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Employee Name</HeaderCell>
                <Cell dataKey="employee_name" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Job Title</HeaderCell>
                <Cell dataKey="job_title" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created At</HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell dataKey="created_by" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated At</HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Is Active?</HeaderCell>
                <Cell dataKey="status_active">
                  {(rowData) => (
                    <span>
                      {rowData.status_active === 1 ? (
                        <div className="flex items-center">
                          <div className="h-2.5 w-2.5 rounded-full bg-green-500 mr-2"></div>{" "}
                          Active
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <div className="h-2.5 w-2.5 rounded-full bg-red-500 mr-2"></div>{" "}
                          Inactive
                        </div>
                      )}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={150} fixed="right">
                <HeaderCell>Action</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span>
                      <Divider vertical />
                      {rowData.status_active === 1 ? (
                        <a
                          onClick={() => {
                            setSelectedTsSpvId(rowData.employee_id);
                            setFormValue({
                              employee_id: rowData.employee_id,
                              employee_name: rowData.employee_name,
                              status_active: rowData.status_active,
                            });
                            setShowDeactivateModal(true);
                          }}
                          className="cursor-pointer text-red-500 hover:text-red-500"
                        >
                          Deactivate
                        </a>
                      ) : (
                        <a
                          onClick={() => {
                            setSelectedTsSpvId(rowData.employee_id);
                            setFormValue({
                              employee_id: rowData.employee_id,
                              employee_name: rowData.employee_name,
                              status_active: rowData.status_active,
                            });
                            setShowActivateModal(true);
                          }}
                          className="cursor-pointer text-green-700 hover:text-green-700"
                        >
                          Activate
                        </a>
                      )}
                    </span>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                total={
                  searchKeyword
                    ? dataSearchParameterTs.length
                    : parameterTsData.length
                }
                limitOptions={[10, 30, 50]}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Deactivate Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showDeactivateModal}
          onClose={() => {
            setShowDeactivateModal(false);
            setFormValue({ 
              employee_id: null,
              employee_name: null});
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Deactivate TS Parameter</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>
              Are you sure you want to deactivate this TS Spv? <br />
              Spv Employee Id: <b>{formValue.employee_id}</b> <br />
              Spv Name: <b>{formValue.employee_name}</b>
            </p>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowDeactivateModal(false);
                setFormValue({ 
                  employee_id: null,
                  employee_name: null 
                });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteTsSpv}
              color="red"
              appearance="primary"
            >
              Deactivate
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Activate Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showActivateModal}
          onClose={() => {
            setShowActivateModal(false);
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Activate TS Parameter</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>
              Are you sure you want to activate this TS Parameter? <br />
              Employee Id: <b>{formValue.employee_id}</b> <br />
              Employee Name: <b>{formValue.employee_name}</b>
            </p>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowActivateModal(false);
                setFormValue({ employee_id: null, employee_name: null });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button onClick={handleActivateTsSpv} appearance="primary">
              Activate
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Add Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue({ 
              employee_id: "",
              created_by: "",
              updated_by: "",
              deleted_by: "",
              status_active: null,
            });
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Add TS Spv</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.Group>
                  <Form.ControlLabel>Employee Spv</Form.ControlLabel>
                  <Form.Control
                  name="employee_id"
                  accepter={SelectPicker}
                  value={formValue.employee_id}
                  data={spvUnassigned}
                  valueKey="employee_id"
                  labelKey="employee_name"
                  block
                  onChange={(value) => {
                    setFormValue({ ...formValue, employee_id: value });
                  }}
                />
                </Form.Group>                
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowAddModal(false);
                setFormValue({
                employee_id: "",
                created_by: "",
                updated_by: "",
                deleted_by: "",
                status_active: null, });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (
                  formValue.employee_id === null ||
                  formValue.employee_id === ""
                ) {
                  toaster.push(
                    Messages("warning", "Employee must be picked"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                } 
                handleAddTsSpv();
                setShowAddModal(false);
                setFormValue({
                  employee_id: "",
                  created_by: "",
                  updated_by: "",
                  deleted_by: "",
                  status_active: null, 
                });
                // console.log("Add TS Parameter with data:", formValue);
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>

      {/* {isLoading ? (
        <Loader
          center
          size="md"
          content="Loading..."
          backdrop
          speed="normal"
          vertical
        />
      ) : (
        <div></div>
      )} */}
    </>
  );
}
