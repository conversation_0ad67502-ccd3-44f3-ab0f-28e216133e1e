import { useRouter } from "next/router";
import { useEffect, useState } from "react";

import MainContent from "@/components/layout/MainContent";
import ModuleApi from "../api/moduleApi";
import {
  Stack,
  Table,
  Panel,
  Pagination,
  IconButton,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Head from "next/head";
import { paginate } from '@/utils/paginate';
import EditIcon from '@rsuite/icons/Edit';
import PlusIcon from '@rsuite/icons/Plus';

function ModuleManagement({ allModule }) {
  const [moduleData, setModuleData] = useState([]);
  const { HeaderCell, Cell, Column } = Table;
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const router = useRouter();

  // data to be displayed in the table
  var datas = paginate(moduleData, page, limit);

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  }, []);

  useEffect(() => {
    if (allModule) {
      setModuleData(allModule);
    }
  }, [allModule]);

  return (
    <>
      <Head>
        <title>Module Management</title>
      </Head>
      <ContainerLayout
        title="Module Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <div className="mb-2">
            <IconButton
              icon={<PlusIcon />}
              appearance="primary"
              onClick={() => router.push("/module_management/addModule")}
            >
              Add
            </IconButton>
          </div>

          {moduleData ? (
            <Panel>
              <Table
                bordered
                cellBordered
                height={400}
                data={datas}
                sortColumn={sortColumn}
                sortType={sortType}
                onSortColumn={handleSortColumn}
              >
                <Column width={60} align="center" fixed>
                  <HeaderCell>No</HeaderCell>
                  <Cell>
                    {(rowData, rowIndex) => {
                      return rowIndex + 1 + limit * (page - 1);
                    }}
                  </Cell>
                </Column>

                <Column width={230}>
                  <HeaderCell>Module Name</HeaderCell>
                  <Cell dataKey="Module_Name" />
                </Column>

                <Column width={150}>
                  <HeaderCell>Icon</HeaderCell>
                  <Cell dataKey="Module_Icon" />
                </Column>

                <Column width={150} fixed="right">
                  <HeaderCell>...</HeaderCell>

                  <Cell style={{ padding: "6px" }}>
                    {(rowData) => (
                      <Stack direction='row' justifyContent='center'>
                        {<IconButton size='md' icon={<EditIcon />} title='Edit' onClick={() => {
                          router.push({
                            pathname:
                              '/module_management/editModule',
                            query: { moduleCode: rowData.Module_Code },
                          });
                        }} />}
                      </Stack>
                    )}
                  </Cell>
                </Column>
              </Table>
              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager"]}
                  total={
                    moduleData.length
                  }
                  limitOptions={[10, 30, 50]}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
          ) : (
            <h1>No Modules Found.</h1>
          )}
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps() {
  const { GetModuleAll } = ModuleApi();

  const { Data: allModuleData } = await GetModuleAll();

  let allModule = [];

  if (allModuleData != undefined) {
    allModule = allModuleData;
  }

  return {
    props: {
      allModule,
    },
  };
}

export default ModuleManagement;
