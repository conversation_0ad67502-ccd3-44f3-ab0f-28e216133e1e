import ContainerLayout from '@/components/layout/ContainerLayout';
import ApiTsDetail from '@/pages/api/ts/api_ts_detail';
import Head from 'next/head';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react'
import { Breadcrumb, Button, Form, InputGroup, Panel, Stack, Uploader, Input, SelectPicker, DatePicker, Table, Modal, Pagination, toaster, Loader, AutoComplete, RadioGroup, Radio } from 'rsuite';
import ApiMasterLine from "@/pages/api/ts/api_ts_master_line";
import ApiTsdpTH from '@/pages/api/tsdp/transaction_h/api_tsdp_transaction_h';
import API_TsMasterLine from "@/pages/api/ts/api_ts_master_line";
import ApiTsdpTDBinder from '@/pages/api/tsdp/api_tsdp_td_binder';
import ApiTsdpTDFinalMix from '@/pages/api/tsdp/api_tsdp_td_final_mix';
import ApiTsdpTDAttachments from '@/pages/api/tsdp/api_tsdp_td_attachments';
import Messages from '@/components/Messages';


export default function AddFinalMix() {

    const router = useRouter();
    const [moduleName, setModuleName] = useState("");
    const [props, setProps] = useState([]);
    const [errors, setErrors] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    const emptyFormValue = {
        id_line: null,
        id_header_trans: null,
        line_type: null,
        sediaan_type: null,
        product_code: null,
        product_name: null,
        batch_no: null,
        production_scale: null,
        trial_focus: null,
        ppi_no: null,
        process_purpose: null,
        background: null,
        process_date: null,
        final_mix_date: null,
        final_mix_time_mix_1: null,
        final_mix_time_mix_2: null,
        ts_conclusion: null,
        ts_followup: null,
        bobot_granul: null,
        bobot_teoritis: null,
        rendemen: null,
        spv_employee_id: null,
        ts_detail_trans: [],
    }
    const [formValue, setFormValue] = useState(emptyFormValue);

    //dropdown
    const [dataLineAutomate, setDataLineAutomate] = useState([]);
    const [dataLineManual, setDataLineManual] = useState([]);
    const [dataBatch, setDataBatch] = useState([]);
    const [dataBatchAutoComplete, setDataBatchAutoComplete] = useState([]);
    const [dataProduct, setDataProduct] = useState([]);
    const [dataSpv, setDataSpv] = useState([]);

    const dataJenisSediaan = ['Kapsul', 'Tablet', 'Sirup'].map(
        item => ({ label: item, value: item })
    );

    const dataSkalaProduksi = ['Pilot', 'Commercial'].map(
        item => ({ label: item, value: item })
    );

    const dataFokusTrial = ['Carry Over', 'Diversifikasi', 'Others'].map(
        item => ({ label: item, value: item })
    );

    useEffect(() => {
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        const moduleNameValue = localStorage.getItem("module_name");

        setProps(dataLogin);
        setModuleName(moduleNameValue);


        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("tsdp/creation/binder")
            );

            if (validateUserAccess.length === 0) {
                // router.push("/dashboard");
                return;
            }
        }
    }, []);

    const handelGetManualMasterLineApi = async () => {
        try {
            const response = await API_TsMasterLine().getManualMasterLine();
            if (response.status === 200 && response.data) {
                const formatMasterLine = response.data.map((item) => ({
                    label: item.line_description,
                    value: item.id_line,
                }));
                setTsManualMasterLine(formatMasterLine);
            }
        } catch (error) {
            console.error("Error fetching data:", error);
        }
    };

    const fetchData = async () => {
        try {
            const resProduct = await ApiTsDetail().getAllOfProduct();
            const resSpv = await ApiTsDetail().getAllActiveSpv();
            const resLine = await ApiMasterLine().getAutomateMasterLine();
            const reslineManual = await API_TsMasterLine().getManualMasterLine();
            const resBatchCode = await ApiTsdpTH().getAllTsTransactionHeader();

            setDataProduct(resProduct.data ? resProduct.data : []);
            setDataSpv(resSpv.data ? resSpv.data : []);


            setDataLineAutomate(resLine.data ? resLine.data : []);


            if (reslineManual?.data) {
                const formatMasterLine = reslineManual.data.map((item) => ({
                    id_line: item.id_line,
                    line_description: item.line_description,
                    label: item.line_description,
                    value: item.id_line,
                }));
                setDataLineManual(formatMasterLine);
                console.log("Formatted Manual Line Data:", formatMasterLine);
            } else {
                setDataLineManual([]);
            }

            setDataBatch(resBatchCode.data ? resBatchCode.data : []);
            const batchCodeMapped = resBatchCode?.data?.map(item => item.batch_no);
            setDataBatchAutoComplete(batchCodeMapped ? batchCodeMapped : []);
        } catch (error) {
            console.error("Error fetching data:", error);
        }
    };

    useEffect(() => {
        fetchData();
    }, []);

    useEffect(() => {
        console.log("Line Type:", formValue.line_type);
        console.log("dataLineAutomate:", dataLineAutomate);
        console.log("dataLineManual:", dataLineManual);
    }, [formValue.line_type, dataLineAutomate, dataLineManual]);




    const [inputValue, setInputValue] = useState(formValue.batch_no || '');
    const [disabledNotFound, setDisabledNotFound] = useState(false);



    useEffect(() => {
        console.log("dataBatch", dataBatch);
        console.log("formValue", formValue);

        const matched = dataBatch.find(batch => batch.batch_no === formValue.batch_no);

        if (matched) {
            setFormValue(prev => ({
                ...prev,
                id_header_trans: matched.id_header_trans,
                id_line: matched.id_line,
                sediaan_type: matched.sediaan_type,
                product_code: matched.product_code,
                product_name: matched.product_name,
                production_scale: matched.production_scale,
                trial_focus: matched.trial_focus,
                ppi_no: matched.ppi_no,
                process_purpose: matched.process_purpose,
                background: matched.background,
                process_date: matched.process_date,
                line_type: matched.line_type,
            }));
            setDisabledNotFound(true)
        } else {
            setDisabledNotFound(false)
            setFormValue(prev => ({
                ...prev,
                id_line: null,
                sediaan_type: null,
                product_code: null,
                product_name: '',
                production_scale: null,
                trial_focus: null,
                ppi_no: '',
                process_purpose: '',
                background: '',
                process_date: null,
                line_type: null,
            }));
        }
    }, [inputValue]);

    const [formAttachments, setFormAttachments] = useState([]);

    const handleGetAttachmentsByType = (type) => {
        const attachments = formAttachments.filter((attachment) => attachment.type === type);
        return attachments?.map((attachment) => ({
            name: attachment?.data?.name,
            fileKey: attachment?.data?.fileKey,
            url: attachment?.data ? URL.createObjectURL(attachment?.data.blobFile) : '',
            size: attachment?.data?.blobFile?.size > 1024 * 1024
                ? `${(attachment?.data?.blobFile?.size / (1024 * 1024)).toFixed(2)}MB`
                : `${(attachment?.data?.blobFile?.size / 1024).toFixed(2)}KB`
        }));
    };

    const handleUploadAttachments = (file, type) => {
        setFormAttachments((prevAttachments) => [
            ...prevAttachments,
            {
                type,
                data: file
            },
        ]);
    };

    const handleRemoveAttachments = (file) => {
        setFormAttachments((prevAttachments) => {
            const newAttachments = [...prevAttachments];
            const index = newAttachments.findIndex((attachment) => attachment.data.fileKey === file.fileKey);
            if (index !== -1) {
                newAttachments.splice(index, 1);
            }
            return newAttachments;
        });
    };


    const handleAddManualApi = async () => {
        // If already submitting, prevent double submission
        if (isSubmitting) {
            return;
        }

        // Set submitting state to true
        setIsSubmitting(true);

        let error = false;
        const newErrors = {};

        const requiredFields = [
            { key: 'batch_no', label: 'Kode Batch' },
            { key: 'line_type', label: 'Line Type' },
            { key: 'id_line', label: 'Line' },
            { key: 'sediaan_type', label: 'Jenis Sediaan' },
            { key: 'product_code', label: 'Kode Produk' },
            { key: 'product_name', label: 'Nama Produk' },
            { key: 'production_scale', label: 'Skala Produksi' },
            { key: 'trial_focus', label: 'Fokus Trial' },
            { key: 'ppi_no', label: 'No PPI' },
            { key: 'process_purpose', label: 'Tujuan Proses' },
            { key: 'background', label: 'Background' },
            { key: 'process_date', label: 'Tanggal Proses' },
            { key: 'final_mix_date', label: 'Tanggal Finalmix' },
            { key: 'final_mix_time_mix_1', label: 'Waktu Aduk 1' },
            { key: 'final_mix_time_mix_2', label: 'Waktu Aduk 2' },
            { key: 'ts_conclusion', label: 'Kesimpulan' },
            { key: 'ts_followup', label: 'Tindak Lanjut' },
            { key: 'bobot_granul', label: 'Bobot Granul' },
            { key: 'bobot_teoritis', label: 'Bobot Teoritis' },
            { key: 'rendemen', label: 'Rendemen' },
            { key: 'spv_employee_id', label: 'Spv' }
        ];

        requiredFields.forEach(field => {
            if (field.key === 'line_type') {
                if (formValue[field.key] !== 0 && formValue[field.key] !== 1) {
                    error = true;
                    newErrors[field.key] = `${field.label} harus diisi`;
                }
            } else if (!formValue[field.key]) {
                error = true;
                newErrors[field.key] = `${field.label} harus diisi`;
            }
        });

        setErrors(newErrors);

        if (error) {
            toaster.push(
                Messages("error", "Harap isi semua field yang wajib diisi!"),
                { placement: "topCenter", duration: 5000 }
            );
            setIsSubmitting(false); // Reset submitting state
            return;
        }

        try {
            const payload = {
                id_header_trans: formValue.id_header_trans || 0,
                line_type: formValue.line_type,
                id_line: formValue.id_line,
                sediaan_type: formValue.sediaan_type,
                product_code: formValue.product_code,
                product_name: formValue.product_name,
                batch_no: formValue.batch_no,
                production_scale: formValue.production_scale,
                trial_focus: formValue.trial_focus,
                ppi_no: formValue.ppi_no,
                process_date: formValue.process_date,
                process_purpose: formValue.process_purpose,
                background: formValue.background,
                final_mix_date: formValue.final_mix_date,
                final_mix_time_mix_1: formValue.final_mix_time_mix_1,
                final_mix_time_mix_2: formValue.final_mix_time_mix_2,
                ts_conclusion: formValue.ts_conclusion,
                ts_followup: formValue.ts_followup,
                bobot_granul: formValue.bobot_granul,
                bobot_teoritis: formValue.bobot_teoritis,
                rendemen: formValue.rendemen,
                spv_employee_id: formValue.spv_employee_id,
                created_by: props.employee_id
            };

            // Step 1: Send transaction data (header + binder detail)
            const resHeader = await ApiTsdpTDFinalMix().postTsTransactionFinalMix(payload);

            if (resHeader.status === 200) {
                const id_header_trans = resHeader.id;
                let failedUpload = 0;

                // Step 2: Upload attachments
                for (const fileItem of formAttachments) {
                    if (fileItem.data !== undefined) {
                        const formData = new FormData();
                        formData.append("Files", fileItem.data.blobFile, fileItem.data.name);
                        formData.append("id_header_trans", id_header_trans);
                        formData.append("type", "Finalmix");
                        formData.append("path", "ts");
                        formData.append("created_by", props.employee_id);

                        const postRes = await ApiTsdpTDAttachments().postTsTransactionUpload(formData);
                        if (postRes.status !== 200) {
                            failedUpload++;
                        }
                    }
                }

                // Step 3: Handle upload result
                if (failedUpload === 0) {
                    toaster.push(
                        Messages("success", "Success Save Data!"),
                        { placement: "topCenter", duration: 5000 }
                    );

                    // setFormValue(emptyFormValue);
                    // setFormAttachments([]);
                    // setCurrentState(1);

                    // Reload page after successful save

                } else {
                    toaster.push(
                        Messages("error", "Error: Failed to upload attachments!"),
                        { placement: "topCenter", duration: 5000 }
                    );
                    setIsSubmitting(false); // Reset submitting state on error
                }
            } else {
                console.log("Error in postTsTransactionBinder:", resHeader.message);
                toaster.push(
                    Messages("error", `Error: ${resHeader.message || "Failed to save data"}!`),
                    { placement: "topCenter", duration: 5000 }
                );
                setIsSubmitting(false); // Reset submitting state on error
            }
        } catch (error) {
            console.log("Exception in handleAddManualApi:", error);
            toaster.push(
                Messages("error", "Error: Something went wrong. Please try again later!"),
                { placement: "topCenter", duration: 5000 }
            );
            setIsSubmitting(false); // Reset submitting state on error
        }
        window.location.reload();
    };


    return (
        <div>
            <div>
                <Head>
                    <title>TS Create Final mix</title>
                </Head>
            </div>


            <ContainerLayout title="User Module">
                <div className='m-4'>
                    <Breadcrumb className='mt-2'>
                        <Breadcrumb.Item href='/dashboard'>Dashboard</Breadcrumb.Item>
                        <Breadcrumb.Item>
                            {moduleName ? moduleName : "User Module"}
                        </Breadcrumb.Item>
                        <Breadcrumb.Item active>TS Create Final Mix</Breadcrumb.Item>
                    </Breadcrumb>


                    <Panel bordered className='mb-2'>
                        <Stack justifyContent='flex-start'>
                            <h3>TS Create Final Mix</h3>
                        </Stack>
                        <hr />
                        <hr />
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>
                                    Kode Batch <span className="text-red-500">*</span>
                                </Form.ControlLabel>

                                <AutoComplete
                                    data={dataBatchAutoComplete}
                                    value={inputValue}
                                    placeholder="Masukkan atau pilih kode batch"
                                    onChange={(value) => {
                                        setInputValue(value)
                                        setFormValue({ ...formValue, batch_no: value });
                                        if (value) {
                                            setErrors({ ...errors, batch_no: null });
                                        }
                                    }}
                                    style={{ width: '100%' }}
                                />
                                {errors.batch_no && <div className="text-red-500 text-sm mt-1">{errors.batch_no}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>
                                    Line Type <span className='text-red-500'>*</span>
                                </Form.ControlLabel>
                                <RadioGroup
                                    name="line_type"
                                    value={formValue.line_type}
                                    onChange={(value) => {

                                        const numericValue = Number(value);

                                        setFormValue(prev => ({
                                            ...prev,
                                            line_type: numericValue,
                                            id_line: null
                                        }));
                                        setErrors(prev => ({
                                            ...prev,
                                            line_type: null
                                        }));
                                    }}
                                    disabled={disabledNotFound}
                                >
                                    <Radio value={1}>Automate</Radio>
                                    <Radio value={0}>Manual</Radio>
                                </RadioGroup>
                                {errors.line_type && <div className="text-red-500 text-sm mt-1">{errors.line_type}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Line <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    name="id_line"
                                    accepter={SelectPicker}
                                    value={formValue.id_line}
                                    data={formValue.line_type === 1 ? dataLineAutomate : dataLineManual}
                                    valueKey="id_line"
                                    labelKey="line_description"
                                    block
                                    onChange={(value) => {
                                        console.log("Line selected:", value);
                                        setFormValue({ ...formValue, id_line: value });
                                        setErrors({ ...errors, id_line: null });
                                    }}
                                    disabled={disabledNotFound}
                                />
                                {errors.id_line && <div className="text-red-500 text-sm mt-1">{errors.id_line}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Jenis Sediaan <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    name="sediaan_type"
                                    accepter={SelectPicker}
                                    value={formValue.sediaan_type}
                                    data={dataJenisSediaan}
                                    valueKey="value"
                                    labelKey="label"
                                    block
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, sediaan_type: value });
                                        setErrors({ ...errors, sediaan_type: null });
                                    }}
                                    disabled={disabledNotFound}
                                />
                                {errors.sediaan_type && <div className="text-red-500 text-sm mt-1">{errors.sediaan_type}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Kode Produk <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    name="product_code"
                                    accepter={SelectPicker}
                                    value={formValue.product_code}
                                    data={dataProduct}
                                    valueKey="product_code"
                                    labelKey="product_code"
                                    block
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, product_code: value });
                                        setErrors({ ...errors, product_code: null });
                                    }}
                                    disabled={disabledNotFound}
                                />
                                {errors.product_code && <div className="text-red-500 text-sm mt-1">{errors.product_code}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Nama Produk <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    placeholder="product_name"
                                    value={formValue.product_name}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, product_name: value });
                                        setErrors({ ...errors, product_name: null });
                                    }}
                                    disabled={disabledNotFound}
                                />
                                {errors.product_name && <div className="text-red-500 text-sm mt-1">{errors.product_name}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Skala Produksi <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    name="production_scale"
                                    accepter={SelectPicker}
                                    value={formValue.production_scale}
                                    data={dataSkalaProduksi}
                                    valueKey="value"
                                    labelKey="value"
                                    block
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, production_scale: value });
                                        setErrors({ ...errors, production_scale: null });
                                    }}
                                    disabled={disabledNotFound}
                                />
                                {errors.production_scale && <div className="text-red-500 text-sm mt-1">{errors.production_scale}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Fokus Trial <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    name="trial_focus"
                                    accepter={SelectPicker}
                                    value={formValue.trial_focus}
                                    data={dataFokusTrial}
                                    valueKey="value"
                                    labelKey="value"
                                    block
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, trial_focus: value });
                                        setErrors({ ...errors, trial_focus: null });
                                    }}
                                    disabled={disabledNotFound}
                                />
                                {errors.trial_focus && <div className="text-red-500 text-sm mt-1">{errors.trial_focus}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>No PPI <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    placeholder="ppi_no"
                                    value={formValue.ppi_no}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, ppi_no: value });
                                        setErrors({ ...errors, ppi_no: null });
                                    }}
                                    disabled={disabledNotFound}
                                />
                                {errors.ppi_no && <div className="text-red-500 text-sm mt-1">{errors.ppi_no}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Tujuan Proses <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    placeholder="process_purpose"
                                    value={formValue.process_purpose}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, process_purpose: value });
                                        setErrors({ ...errors, process_purpose: null });
                                    }}
                                    disabled={disabledNotFound}
                                />
                                {errors.process_purpose && <div className="text-red-500 text-sm mt-1">{errors.process_purpose}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Background <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    placeholder="background"
                                    value={formValue.background}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, background: value });
                                        setErrors({ ...errors, background: null });
                                    }}
                                    disabled={disabledNotFound}
                                />
                                {errors.background && <div className="text-red-500 text-sm mt-1">{errors.background}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Tanggal Proses <span className='text-red-500'>*</span></Form.ControlLabel>
                                <DatePicker
                                    oneTap
                                    value={formValue.process_date ? new Date(formValue.process_date) : null}
                                    format="dd-MM-yyyy"
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, process_date: value });
                                        setErrors({ ...errors, process_date: null });
                                    }}
                                    block
                                    disabled={disabledNotFound}
                                    placeholder="Pilih tanggal"
                                />
                                {errors.process_date && <div className="text-red-500 text-sm mt-1">{errors.process_date}</div>}
                            </Form.Group>


                            <Form.Group>
                                <Form.ControlLabel>Tanggal Final mix <span className='text-red-500'>*</span></Form.ControlLabel>
                                <DatePicker
                                    oneTap
                                    value={formValue.final_mix_date}
                                    format="dd-MM-yyyy"
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, final_mix_date: value });
                                        setErrors({ ...errors, final_mix_date: null });
                                    }}
                                    block
                                />
                                {errors.final_mix_date && <div className="text-red-500 text-sm mt-1">{errors.final_mix_date}</div>}
                            </Form.Group>

                            <Form.Group>
                                <Form.ControlLabel>Waktu Aduk 1<span className='text-red-500'>*</span></Form.ControlLabel>
                                <InputGroup>
                                    <Form.Control
                                        placeholder="final_mix_time_mix_1"
                                        value={formValue.final_mix_time_mix_1}
                                        onChange={(value) => {
                                            setFormValue({ ...formValue, final_mix_time_mix_1: parseFloat(value) });
                                            setErrors({ ...errors, final_mix_time_mix_1: null });
                                        }}
                                        type="number"
                                    />
                                    <InputGroup.Addon>Menit</InputGroup.Addon>
                                </InputGroup>
                                {errors.final_mix_time_mix_1 && <div className="text-red-500 text-sm mt-1">{errors.final_mix_time_mix_1}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Waktu Aduk 2<span className='text-red-500'>*</span></Form.ControlLabel>
                                <InputGroup>
                                    <Form.Control
                                        placeholder="final_mix_time_mix_2"
                                        value={formValue.final_mix_time_mix_2}
                                        onChange={(value) => {
                                            setFormValue({ ...formValue, final_mix_time_mix_2: parseFloat(value) });
                                            setErrors({ ...errors, final_mix_time_mix_2: null });
                                        }}
                                        type="number"
                                    />
                                    <InputGroup.Addon>Menit</InputGroup.Addon>
                                </InputGroup>
                                {errors.final_mix_time_mix_2 && <div className="text-red-500 text-sm mt-1">{errors.final_mix_time_mix_2}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Kesimpulan <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    placeholder="ts_conclusion"
                                    value={formValue.ts_conclusion}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, ts_conclusion: value });
                                        setErrors({ ...errors, ts_conclusion: null });
                                    }}
                                />
                                {errors.ts_conclusion && <div className="text-red-500 text-sm mt-1">{errors.ts_conclusion}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Tindak Lanjut <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    placeholder="ts_followup"
                                    value={formValue.ts_followup}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, ts_followup: value });
                                        setErrors({ ...errors, ts_followup: null });
                                    }}
                                />
                                {errors.ts_followup && <div className="text-red-500 text-sm mt-1">{errors.ts_followup}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Bobot Granul<span className='text-red-500'>*</span></Form.ControlLabel>
                                <InputGroup>
                                    <Form.Control
                                        placeholder="bobot_granul"
                                        value={formValue.bobot_granul}
                                        onChange={(value) => {
                                            setFormValue({ ...formValue, bobot_granul: parseFloat(value) });
                                            setErrors({ ...errors, bobot_granul: null });
                                        }}
                                        type="number"
                                    />
                                    <InputGroup.Addon>Kg</InputGroup.Addon>
                                </InputGroup>
                                {errors.bobot_granul && <div className="text-red-500 text-sm mt-1">{errors.bobot_granul}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Bobot Teoritis<span className='text-red-500'>*</span></Form.ControlLabel>
                                <InputGroup>
                                    <Form.Control
                                        placeholder="bobot_teoritis"
                                        value={formValue.bobot_teoritis}
                                        onChange={(value) => {
                                            setFormValue({ ...formValue, bobot_teoritis: parseFloat(value) });
                                            setErrors({ ...errors, bobot_teoritis: null });
                                        }}
                                        type="number"
                                    />
                                    <InputGroup.Addon>Kg</InputGroup.Addon>
                                </InputGroup>
                                {errors.bobot_teoritis && <div className="text-red-500 text-sm mt-1">{errors.bobot_teoritis}</div>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Rendemen<span className='text-red-500'>*</span></Form.ControlLabel>
                                <InputGroup>
                                    <Form.Control
                                        placeholder="rendemen"
                                        value={formValue.rendemen}
                                        onChange={(value) => {
                                            setFormValue({ ...formValue, rendemen: parseFloat(value) });
                                            setErrors({ ...errors, rendemen: null });
                                        }}
                                        type="number"
                                    />
                                    <InputGroup.Addon>%</InputGroup.Addon>
                                </InputGroup>
                                {errors.rendemen && <div className="text-red-500 text-sm mt-1">{errors.rendemen}</div>}
                            </Form.Group>

                            <Form.Group>
                                <Form.ControlLabel>Lampiran</Form.ControlLabel>
                                <Uploader
                                    key={2}
                                    listType="picture-text"
                                    action=''
                                    defaultFileList={handleGetAttachmentsByType(2)}
                                    onUpload={(file) => handleUploadAttachments(file, 2)}
                                    onRemove={(file) => handleRemoveAttachments(file)}
                                    renderFileInfo={(file, fileElement) => {
                                        return (
                                            <>
                                                <span>File Name: {file.name}</span>
                                                <br />
                                                <span style={{ color: 'gray' }}>{file.size}</span>
                                            </>
                                        );
                                    }}
                                >
                                    <Button appearance='ghost'>Ambil Gambar</Button>
                                </Uploader>
                            </Form.Group>


                            <Form.Group>
                                <Form.ControlLabel>Spv <span className='text-red-500'>*</span></Form.ControlLabel>
                                <Form.Control
                                    name="spv_employee_id"
                                    accepter={SelectPicker}
                                    value={formValue.spv_employee_id}
                                    data={dataSpv}
                                    valueKey="employee_id"
                                    labelKey="employee_name"
                                    block
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, spv_employee_id: value });
                                        setErrors({ ...errors, spv_employee_id: null });
                                    }}
                                />
                                {errors.spv_employee_id && <div className="text-red-500 text-sm mt-1">{errors.spv_employee_id}</div>}
                            </Form.Group>
                        </Form>
                        <Button
                            appearance="primary"
                            style={{ backgroundColor: "#1fd306", marginTop: "5px" }}
                            onClick={handleAddManualApi}
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? <Loader content="Saving..." /> : "Save"}
                        </Button>
                    </Panel>
                </div>
            </ContainerLayout>
        </div>
    )
}
