import React, { useState, useEffect } from "react";
import {
  Table,
  Button,
  Modal,
  Divider,
  Pagination,
  Input,
  InputGroup,
  Stack,
  Form,
  useToaster
} from "rsuite";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";

import ApiTsReport from "@/pages/api/ts/api_ts-report";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import { useRouter } from 'next/router';

const { Column, HeaderCell, Cell } = Table;

export default function OverStockTable({stockData, employee_id}) {
  const router = useRouter()
  const [data, setData] = useState([]);
  const [dataFromRow, setDataFromRow] = useState({}); // data from row
  const [showDetail, setShowDetail] = useState(false);
 
  const [showAddKeyword, setShowAddKeyword] = useState(false);
  const [keywordForm, setKeywordForm] = useState([])
  const [keyWord, setKeyword] = useState('')

  const [showSearchKeyword, setShowSearchKeyword] = useState(false);
  const [searchKeywordForm, setSearchKeywordForm] = useState([])
  const [searchKeyWordValue, setSearchKeywordValue] = useState('')

  const toaster = useToaster();

  const fetchData = async () => {
    // const result = await ApiTsReport().getReport1();
    // console.log("props", props)
    setData(stockData ? stockData :[]);
  };

  useEffect(() => {
    fetchData();
  }, [stockData]);

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "item_code",
    "item_desc",
    "uom",
    "quantity",
    "overstock",
  ];

  const datas =
    data && data.length > 0
      ? data
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .some((val) => val.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const dataSearch = data?.filter((item) => {
    const str = searchKeyword.toLowerCase();
    return propertiesToFilter
      .map((property) => item[property] || "") // Use empty string as a default for undefined values
      .some((val) => val.toString().toLowerCase().includes(str));
  });

  // function to export data based on selected row, header and detail (2 sheets (header and detail) in 1 excel file))
  async function exportData(rowData) {
    const header = rowData;
    const detail = rowData.detail_trans;

    if (!detail || detail.length === 0) {
      alert("No detail data to export");
      return;
    }

    const headerData = [header];
    const detailData = detail;

    const headerWs = XLSX.utils.json_to_sheet(headerData);
    const detailWs = XLSX.utils.json_to_sheet(detailData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, headerWs, "Header");
    XLSX.utils.book_append_sheet(wb, detailWs, "Detail");

    const id_header_trans = header.id_header_trans;
    const date = new Date();
    const YYMMDD = `${date.getFullYear().toString().slice(-2)}${(
      date.getMonth() + 1
    )
      .toString()
      .padStart(2, "0")}${date.getDate().toString().padStart(2, "0")}`;

    XLSX.writeFile(
      wb,
      `ts-report-automate-line-${YYMMDD}-${id_header_trans}.xlsx`
    );

    await ApiTsReport().increasePrintAmount({
      id_header_trans: id_header_trans,
    });

    // refresh data
    const result = await ApiTsReport().getReport1();
    setData(result.data || []);
  }



  return (
    <>
      <Stack justifyContent="flex-end" direction="row" className="mb-4">
        <InputGroup inside>
          <InputGroup.Addon>
            <SearchIcon />
          </InputGroup.Addon>
          <Input
            placeholder="Search"
            value={searchKeyword}
            onChange={handleSearch}
          />
          <InputGroup.Addon
            onClick={() => {
              setSearchKeyword("");
              setPage(1);
            }}
            style={{
              display: searchKeyword ? "block" : "none",
              color: "red",
              cursor: "pointer",
            }}
          >
            <CloseOutlineIcon />
          </InputGroup.Addon>
        </InputGroup>
      </Stack>

      <Table
        bordered
        cellBordered
        rowKey="item_code"
        height={500}
        data={datas}
        sortColumn={sortColumn}
        sortType={sortType}
        onSortColumn={handleSortColumn}
      >
          <Column width={150} resizable sortable>
          <HeaderCell>Item Code</HeaderCell>
          <Cell dataKey="item_code" />
        </Column>

        <Column width={150} resizable sortable fullText>
          <HeaderCell>Item Description</HeaderCell>
          <Cell dataKey="item_desc" />
        </Column>

        <Column width={150} resizable sortable>
          <HeaderCell>Uom</HeaderCell>
          <Cell dataKey="uom" />
        </Column>

        <Column width={150} resizable sortable>
          <HeaderCell>Quantity</HeaderCell>
          <Cell dataKey="quantity" />
        </Column>

        {/* <Column resizable sortable>
          <HeaderCell>Overstock</HeaderCell>
          <Cell dataKey="overstock" />
        </Column> */}
       
      </Table>
      <div style={{ padding: 20 }}>
        <Pagination
          prev
          next
          first
          last
          ellipsis
          boundaryLinks
          maxButtons={5}
          size="xs"
          layout={["total", "-", "limit", "|", "pager", "skip"]}
          total={
            dataSearch && dataSearch.length > 0
              ? dataSearch.length
              : data.length
          }
          pages={Math.ceil(data.length / limit)}
          activePage={page}
          limitOptions={[10, 20, 30, 40, 50]}
          limit={limit}
          onSelect={(page) => setPage(page)}
          onChangeLimit={handleChangeLimit}
          onChangePage={handleChangeLimit}
        />
      </div>
    </>
  );
}
