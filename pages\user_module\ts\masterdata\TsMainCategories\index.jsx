import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";

import {
  Button,
  IconButton,
  Stack,
  Breadcrumb,
  Tag,
  Table,
  Panel,
  Input,
  InputGroup,
  Pagination,
  Divider,
  Modal,
  Form,
  Schema,
  useToaster,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";

import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";

import API_TsMainCategories from "@/pages/api/ts/api_ts-main-categories";
import { data } from "autoprefixer";

export default function TsMainCategories() {
  const router = useRouter();
  const { HeaderCell, Cell, Column } = Table;

  const [moduleName, setModuleName] = useState("");
  const [tsMainCategoriesData, setTsMainCategoriesData] = useState([]);

  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [showActivateModal, setShowActivateModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);

  const [selectedTsMainCategories, setSelectedTsMainCategories] =
    useState(null);
  const [selectedMainCategoriesActive, setSelectedMainCategoriesActive] =
    useState(null);

  const toaster = useToaster();

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [props, setProps] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      const result = await API_TsMainCategories().getAllTsMainCategories();
      setTsMainCategoriesData(result.data || []);
      // console.log("result", result);
    };

    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    // console.log("dataLogin", dataLogin);
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("masterdata/TsMainCategories")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      fetchData();
    }
  }, []);

  const [formValue, setFormValue] = useState({
    main_category_name: "",
    main_category_type: "",
    created_by: "",
    update_by: "",
    deleted_by: "",
    is_active: null,
  });

  const requireInputRule = Schema.Types.StringType().isRequired(
    "This field is required."
  );

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id_categories",
    "main_category_name",
    "main_category_type",
    "created_at",
    "created_by",
    "updated_at",
    "updated_by",
    "deleted_at",
    "deleted_by",
  ];

  const datas =
    tsMainCategoriesData && tsMainCategoriesData.length > 0
      ? tsMainCategoriesData
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .concat(
                item.is_active === 1
                  ? "Active"
                  : item.is_active === 0
                  ? "Inactive"
                  : null
              )
              .some((val) => val.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const dataSearchTsMainCategories = tsMainCategoriesData
    ? tsMainCategoriesData.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];

  const handleDeletetsMainCategories = async () => {
    try {
      // Make the delete API call with the selectedTsMainCategories
      let result = await API_TsMainCategories().sfDeleteTsMainCategories({
        id_categories: selectedTsMainCategories,
        deleted_by: props.employee_id,
      });
      if (result?.status !== 200) {
        throw "Delete TS Main Categories failed";
      }

      setFormValue({ main_category_name: null, main_category_type: null });

      // Fetch the updated TS Main Categories list
      const res = await API_TsMainCategories().getAllTsMainCategories();
      setTsMainCategoriesData(res.data);

      // Close the delete modal
      setShowDeactivateModal(false);

      // Show the toast message
      toaster.push(
        Messages("success", "Success deactivating TS Main Categories!"),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    } catch (error) {
      console.error("Error deactivating TS Main Categories:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleActivatetsMainCategories = async () => {
    try {
      // Make the activate API call with the selectedTsMainCategories
      let result = await API_TsMainCategories().updateTsMainCategories({
        ...formValue,
        id_categories: selectedTsMainCategories,
        updated_by: props.employee_id,
        is_active: 1,
      });
      if (result?.status !== 200) {
        throw "Activate TS Main Categories failed";
      }

      setFormValue({ main_category_name: null, main_category_type: null });

      // Fetch the updated TS Main Categories list
      const res = await API_TsMainCategories().getAllTsMainCategories();
      setTsMainCategoriesData(res.data);

      // Close the activate modal
      setShowActivateModal(false);

      // Show the toast message
      toaster.push(
        Messages("success", "Success activating TS Main Categories!"),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    } catch (error) {
      console.error("Error activating TS Main Categories:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleAddtsMainCategories = async () => {
    console.log("Add TS Main Categories with data:", formValue);
    try {
      // Make the add API call with the formValue
      let result = await API_TsMainCategories().addTsMainCategories({
        ...formValue,
        created_by: props.employee_id,
        is_active: 1,
      });

      if (result?.status !== 200) {
        throw "Add TS Main Categories failed";
      }

      // Fetch the updated TS Main Categories list
      const res = await API_TsMainCategories().getAllTsMainCategories();
      setTsMainCategoriesData(res.data);

      // Close the add modal
      setShowAddModal(false);

      // Show the toast message
      toaster.push(
        Messages("success", "Success adding a new TS Main Categories!"),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );

      // Reset the form value
      setFormValue({
        main_category_name: null,
        main_category_type: null,
      });
    } catch (error) {
      console.error("Error adding TS Main Categories:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleUpdatetsMainCategories = async () => {
    try {
      // Make the update API call with the selectedTsMainCategories and formValue
      let result = await API_TsMainCategories().updateTsMainCategories({
        ...formValue,
        id_categories: selectedTsMainCategories,
        updated_by: props.employee_id,
        is_active: selectedMainCategoriesActive,
      });
      if (result?.status !== 200) {
        throw "Update TS Main Categories failed";
      }

      // Fetch the updated TS Main Categories list
      const res = await API_TsMainCategories().getAllTsMainCategories();
      setTsMainCategoriesData(res.data);

      // Close the update modal
      setShowUpdateModal(false);

      // Show the toast message
      toaster.push(
        Messages("success", "Success updating TS Main Categories!"),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );

      // Reset the form value
      setFormValue({
        main_category_name: null,
        main_category_type: null,
      });
    } catch (error) {
      console.error("Error updating TS Main Categories:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Master Data TS Main Categories</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Research and Development</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>TS Main Categories</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <IconButton
                  icon={<PlusIcon />}
                  appearance="primary"
                  onClick={() => {
                    setShowAddModal(true);
                  }}
                >
                  Add
                </IconButton>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              data={datas}
              bordered
              cellBordered
              height={400}
              onRowClick={(rowData) => {
                console.log(rowData);
              }}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>

              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id_categories" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Main Categories Name</HeaderCell>
                <Cell dataKey="main_category_name" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Main Categories Type</HeaderCell>
                <Cell dataKey="main_category_type" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created At</HeaderCell>
                <Cell dataKey="created_at" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell dataKey="created_by" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated At</HeaderCell>
                <Cell dataKey="updated_at" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Deleted At</HeaderCell>
                <Cell dataKey="deleted_at" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Deleted By</HeaderCell>
                <Cell dataKey="deleted_by" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Is Active?</HeaderCell>
                <Cell dataKey="is_active">
                  {(rowData) => (
                    <span>
                      {rowData.is_active === 1 ? (
                        <div className="flex items-center">
                          <div className="h-2.5 w-2.5 rounded-full bg-green-500 mr-2"></div>{" "}
                          Active
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <div className="h-2.5 w-2.5 rounded-full bg-red-500 mr-2"></div>{" "}
                          Inactive
                        </div>
                      )}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={150} fixed="right">
                <HeaderCell>Action</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span>
                      <a
                        className="cursor-pointer"
                        onClick={() => {
                          setSelectedTsMainCategories(rowData.id_categories);
                          setSelectedMainCategoriesActive(rowData.is_active);
                          setFormValue({
                            main_category_name: rowData.main_category_name,
                            main_category_type: rowData.main_category_type,
                          });
                          setShowUpdateModal(true);
                        }}
                      >
                        Edit
                      </a>
                      <Divider vertical />
                      {rowData.is_active === 1 ? (
                        <a
                          onClick={() => {
                            setSelectedTsMainCategories(rowData.id_categories);
                            setFormValue({
                              main_category_name: rowData.main_category_name,
                              main_category_type: rowData.main_category_type,
                            });
                            setShowDeactivateModal(true);
                          }}
                          className="cursor-pointer text-red-500 hover:text-red-500"
                        >
                          Deactivate
                        </a>
                      ) : (
                        <a
                          onClick={() => {
                            setSelectedTsMainCategories(rowData.id_categories);
                            setFormValue({
                              main_category_name: rowData.main_category_name,
                              main_category_type: rowData.main_category_type,
                            });
                            setShowActivateModal(true);
                          }}
                          className="cursor-pointer text-green-700 hover:text-green-700"
                        >
                          Activate
                        </a>
                      )}
                    </span>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                total={
                  searchKeyword
                    ? dataSearchTsMainCategories.length
                    : tsMainCategoriesData.length
                }
                limitOptions={[10, 30, 50]}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Deactivate Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showDeactivateModal}
          onClose={() => {
            setShowDeactivateModal(false);
            setFormValue({
              main_category_name: null,
              main_category_type: null,
            });
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Deactivate TS Main Categories</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>
              Are you sure you want to deactivate this TS Main Categories?{" "}
              <br />
              Main Categories Name: <b>{formValue.main_category_name}</b> <br />
              Main Categories Type: <b>{formValue.main_category_type}</b>
            </p>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowDeactivateModal(false);
                setFormValue({
                  main_category_name: null,
                  main_category_type: null,
                });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeletetsMainCategories}
              color="red"
              appearance="primary"
            >
              Deactivate
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Activate Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showActivateModal}
          onClose={() => {
            setShowActivateModal(false);
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Activate TS Main Categories</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>
              Are you sure you want to activate this TS Main Categories? <br />
              Main Categories Name: <b>{formValue.main_category_name}</b> <br />
              Main Categories Type: <b>{formValue.main_category_type}</b>
            </p>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowActivateModal(false);
                setFormValue({
                  main_category_name: null,
                  main_category_type: null,
                });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={handleActivatetsMainCategories}
              appearance="primary"
            >
              Activate
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Add Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue({
              main_category_name: null,
              main_category_type: null,
            });
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Add TS Main Categories</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.Group>
                  <Form.ControlLabel>Main Categories Name</Form.ControlLabel>
                  <Form.Control
                    name="main_category_name"
                    value={formValue.main_category_name}
                    onChange={(value) => {
                      setFormValue({ ...formValue, main_category_name: value });
                    }}
                    rule={requireInputRule}
                    required
                  />
                </Form.Group>
                <Form.ControlLabel>Main Categories Type</Form.ControlLabel>
                <Form.Control
                  name="main_category_type"
                  value={formValue.main_category_type}
                  onChange={(value) => {
                    setFormValue({ ...formValue, main_category_type: value });
                  }}
                  rule={requireInputRule}
                  required
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowAddModal(false);
                setFormValue({
                  main_category_name: null,
                  main_category_type: null,
                });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (
                  formValue.main_category_name === null ||
                  formValue.main_category_name === ""
                ) {
                  toaster.push(
                    Messages("warning", "Main Categories Name cannot be empty"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                } else if (
                  formValue.main_category_type === null ||
                  formValue.main_category_type === ""
                ) {
                  toaster.push(
                    Messages("warning", "Main Categories Type cannot be empty"),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                }
                handleAddtsMainCategories();
                setShowAddModal(false);
                setFormValue({
                  main_category_name: null,
                  main_category_type: null,
                });
                console.log("Add TS Main Categories with data:", formValue);
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Edit Modal */}
        <Modal
          backdrop="static"
          role="alertdialog"
          open={showUpdateModal}
          onClose={() => {
            setShowUpdateModal(false);
            setFormValue({
              main_category_name: null,
              main_category_type: null,
            });
          }}
          size="xs"
        >
          <Modal.Header>
            <Modal.Title>Edit TS Main Categories</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Main Categories Name</Form.ControlLabel>
                <Form.Control
                  name="main_category_name"
                  value={formValue.main_category_name}
                  onChange={(value) => {
                    setFormValue({ ...formValue, main_category_name: value });
                  }}
                  rule={requireInputRule}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Main Categories Type</Form.ControlLabel>
                <Form.Control
                  name="main_category_type"
                  value={formValue.main_category_type}
                  onChange={(value) => {
                    setFormValue({ ...formValue, main_category_type: value });
                  }}
                  rule={requireInputRule}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowUpdateModal(false);
                setFormValue({
                  main_category_name: null,
                  main_category_type: null,
                });
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (
                  formValue.main_category_name === null ||
                  formValue.main_category_name === ""
                ) {
                  toaster.push(
                    Messages(
                      "warning",
                      "TS Main Categories Name cannot be empty"
                    ),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                } else if (
                  formValue.main_category_type === null ||
                  formValue.main_category_type === ""
                ) {
                  toaster.push(
                    Messages(
                      "warning",
                      "TS Main Categories Code cannot be empty"
                    ),
                    {
                      placement: "topEnd",
                      duration: 3000,
                    }
                  );
                  return;
                }
                handleUpdatetsMainCategories();
                setShowUpdateModal(false);
                setFormValue({
                  main_category_name: null,
                  main_category_type: null,
                });
                console.log("Update TS Main Categories with data:", formValue);
              }}
              appearance="primary"
              type="submit"
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
