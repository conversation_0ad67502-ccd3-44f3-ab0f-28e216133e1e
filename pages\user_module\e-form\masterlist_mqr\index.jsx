import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Button,
  IconButton,
  Stack,
  Breadcrumb,
  Tag,
  Table,
  Panel,
  Input,
  InputGroup,
  Pagination,
  Modal,
  useToaster,
  DatePicker,
  DateRangePicker,
  InputPicker,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";
import dateFormatterLong from "@/lib/function/date-formatter-long";
import dateTimeFormatter from "@/lib/function/date-time-formatter";
import dateFormatterDash from "@/lib/function/date-formatter-dash";

import API_EformMasterlistMqr from "@/pages/api/e_form/api_eform_masterlist_mqr";

export default function EFormMasterlistMqrPage() {
  const router = useRouter();
  const toaster = useToaster();
  const [props, setProps] = useState([]);
  const { HeaderCell, Cell, Column } = Table;

  const [moduleName, setModuleName] = useState("");
  const [data, setData] = useState([]);

  const [showModal, setShowModal] = useState(false);
  const [mode, setMode] = useState(null);

  const [selectedRow, setSelectedRow] = useState(null);

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const typeData = ["PM", "RM"].map((item) => ({ label: item, value: item }));

  useEffect(() => {
    const fetchData = async () => {
      const res_data = await API_EformMasterlistMqr().getAll();
      setData(res_data.data || []);
    };

    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");

    setProps(dataLogin);
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("e-form/masterlist_mqr")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      fetchData();
    }
  }, []);

  const emptyFormValue = {
    no_doc: null,
    material_code: null,
    material_name: null,
    manuf_name: null,
    manuf_country: null,
    supplier: null,
    date_review_from: null,
    date_review_to: null,
    remarks: null,
    publish_date: null,
    qualify_status: null,
    created_by: null,
    updated_by: null,
    type: null,
    status: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id_eform_masterlist_mqr",
    "no_doc",
    "material_code",
    "material_name",
    "no_pr",
    "manuf_name",
    "manuf_country",
    "supplier",
    "date_review_from",
    "date_review_to",
    "remarks",
    "publish_date",
    "qualify_status",
    "created_at",
    "created_by",
    "updated_at",
    "updated_by",
    "type",
    "status",
  ];

  const datas =
    data && data.length > 0
      ? data
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .concat(
                item.receipt_date
                  ? dateFormatterLong(item.receipt_date).toLowerCase()
                  : ""
              )
              .concat(
                item.item_arrival_date
                  ? dateFormatterLong(item.item_arrival_date).toLowerCase()
                  : ""
              )
              .concat(
                item.created_at
                  ? dateTimeFormatter(
                      item.created_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .concat(
                item.updated_at
                  ? dateTimeFormatter(
                      item.updated_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .some((val) => val?.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const searchData = data
    ? data.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];

  const handleOpenModal = (mode) => {
    setMode(mode);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setFormValue(emptyFormValue);
    setMode(null);
  };

  const handleRequired = () => {
    let required = ["date_review_from", "date_review_to", "publish_date"];
    let error = false;
    required.forEach((item) => {
      if (!formValue[item]) {
        error = true;
      }
    });
    return error;
  };

  const handleActions = async () => {
    if (handleRequired()) {
      toaster.push(Messages("error", "Fill in the required fields!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }

    try {
      let result;
      if (mode === "add") {
        result = await API_EformMasterlistMqr().add({
          ...formValue,
          created_by: props.employee_id,
        });
      } else if (mode === "edit") {
        result = await API_EformMasterlistMqr().edit({
          ...formValue,
          id_eform_masterlist_mqr: selectedRow,
          updated_by: props.employee_id,
        });
      }

      const res = await API_EformMasterlistMqr().getAll();
      setData(res.data);

      // Show the toast message
      toaster.push(
        Messages(
          "success",
          `Success ${
            mode === "add" ? "adding" : "editing"
          } E-Form Masterlist MQR!`
        ),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );

      handleCloseModal();
    } catch (error) {
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );
    }
  };

  const handleExportExcel = (data) => {
    if (!data || data.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }
    
    const formattedData = data.map((item) => ({
      ID: item.id_eform_masterlist_mqr,
      Type: item.type,
      "No. Document": item.no_doc,
      "Material Code": item.material_code,
      "Material Name": item.material_name,
      "Manufacture Name": item.manuf_name,
      "Manufacture Country": item.manuf_country,
      Supplier: item.supplier,
      "Date Review From": dateFormatterDash(item.date_review_from),
      "Date Review To": dateFormatterDash(item.date_review_to),
      Remarks: item.remarks,
      Status: item.status,
      "Publish Date": dateFormatterDash(item.publish_date),
      "Qualify Status": item.qualify_status,
      "Created At": dateTimeFormatter(item.created_at, "id-ID", "seconds"),
      "Created By": item.created_by,
      "Updated At": dateTimeFormatter(item.updated_at, "id-ID", "seconds"),
      "Updated By": item.updated_by,
    }));

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");
    const date = dateFormatterDash(new Date());
    const filename = `E-Form Masterlist MQR ${date}.xlsx`;
    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <Head>
        <title>E-Form Masterlist MQR</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>E-Form</Breadcrumb.Item>
                  <Breadcrumb.Item active>Masterlist MQR</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusIcon />}
                    appearance="primary"
                    onClick={() => handleOpenModal("add")}
                  >
                    Add
                  </IconButton>
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={() => handleExportExcel(data)}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="Search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              data={datas}
              bordered
              cellBordered
              autoHeight
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={60} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>

              <Column fullText width={60} sortable resizable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id_eform_masterlist_mqr" />
              </Column>

              <Column fullText width={80} sortable resizable>
                <HeaderCell>Type</HeaderCell>
                <Cell dataKey="type" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>No. Document</HeaderCell>
                <Cell dataKey="no_doc" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Material Code</HeaderCell>
                <Cell dataKey="material_code" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Material Name</HeaderCell>
                <Cell dataKey="material_name" />
              </Column>

              <Column fullText width={250} sortable resizable>
                <HeaderCell>Manufacture Name</HeaderCell>
                <Cell dataKey="manuf_name" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Manufacture Country</HeaderCell>
                <Cell dataKey="manuf_country" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Supplier</HeaderCell>
                <Cell dataKey="supplier" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Date Review From</HeaderCell>
                <Cell dataKey="date_review_from">
                  {(rowData) => {
                    return rowData?.date_review_from
                      ? dateFormatterLong(rowData?.date_review_from, "id-ID")
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Date Review To</HeaderCell>
                <Cell dataKey="date_review_to">
                  {(rowData) => {
                    return rowData?.date_review_to
                      ? dateFormatterLong(rowData?.date_review_to, "id-ID")
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Remarks</HeaderCell>
                <Cell dataKey="remarks" />
              </Column>

              <Column fullText width={100} sortable resizable>
                <HeaderCell>Status</HeaderCell>
                <Cell dataKey="status" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Publish Date</HeaderCell>
                <Cell dataKey="publish_date">
                  {(rowData) => {
                    return rowData?.publish_date
                      ? dateFormatterLong(rowData?.publish_date, "id-ID")
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Qualify Status</HeaderCell>
                <Cell dataKey="qualify_status" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Created At</HeaderCell>
                <Cell dataKey="created_at">
                  {(rowData) => {
                    return rowData?.created_at
                      ? dateTimeFormatter(
                          rowData?.created_at,
                          "id-ID",
                          "seconds"
                        )
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell dataKey="created_by" />
              </Column>

              <Column fullText width={150} sortable resizable>
                <HeaderCell>Updated At</HeaderCell>
                <Cell dataKey="updated_at">
                  {(rowData) => {
                    return rowData?.updated_at
                      ? dateTimeFormatter(
                          rowData?.updated_at,
                          "id-ID",
                          "seconds"
                        )
                      : "-";
                  }}
                </Cell>
              </Column>

              <Column fullText width={150} sortable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>

              <Column width={80} fixed="right" align="center">
                <HeaderCell>...</HeaderCell>
                <Cell>
                  {(rowData) => {
                    function handleEditAction() {
                      setSelectedRow(rowData.id_eform_masterlist_mqr);
                      setFormValue({
                        ...rowData,
                        date_review_from: rowData.date_review_from
                          ? new Date(rowData.date_review_from)
                          : null,
                        date_review_to: rowData.date_review_to
                          ? new Date(rowData.date_review_to)
                          : null,
                        publish_date: rowData.publish_date
                          ? new Date(rowData.publish_date)
                          : null,
                      });
                      setShowModal(true);
                      setMode("edit");
                    }
                    return (
                      <Button
                        onClick={handleEditAction}
                        appearance="link"
                        className="p-0"
                      >
                        Edit
                      </Button>
                    );
                  }}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                total={searchKeyword ? searchData.length : data.length}
                limitOptions={[10, 30, 50]}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        <Modal
          backdrop="static"
          open={showModal}
          overflow={false}
          onClose={() => handleCloseModal()}
        >
          <Modal.Header>
            <Modal.Title>
              {mode === "add" ? "Add" : "Edit"} E-Form Masterlist MQR
            </Modal.Title>
          </Modal.Header>
          <Modal.Body className="flex flex-col gap-3">
            <div className="flex justify-between gap-4 w-full">
              <div className="flex flex-col gap-3 w-full">
                <div>
                  <label>No. Document</label>
                  <Input
                    placeholder="No. Document"
                    value={formValue.no_doc}
                    onChange={(value) => {
                      setFormValue({ ...formValue, no_doc: value });
                    }}
                    maxLength={50}
                  />
                </div>

                <div>
                  <label>Material Code</label>
                  <Input
                    placeholder="Material Code"
                    value={formValue.material_code}
                    onChange={(value) => {
                      setFormValue({ ...formValue, material_code: value });
                    }}
                    maxLength={50}
                  />
                </div>

                <div>
                  <label>Material Name</label>
                  <Input
                    placeholder="Material Name"
                    value={formValue.material_name}
                    onChange={(value) => {
                      setFormValue({ ...formValue, material_name: value });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>Manufacture Name</label>
                  <Input
                    placeholder="Manufacture Name"
                    value={formValue.manuf_name}
                    onChange={(value) => {
                      setFormValue({ ...formValue, manuf_name: value });
                    }}
                    className="w-full"
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>Manufacture Country</label>
                  <Input
                    placeholder="Manufacture Country"
                    value={formValue.manuf_country}
                    onChange={(value) => {
                      setFormValue({ ...formValue, manuf_country: value });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>Status</label>
                  <Input
                    placeholder="Status"
                    value={formValue.status}
                    onChange={(value) => {
                      setFormValue({ ...formValue, status: value });
                    }}
                    maxLength={100}
                  />
                </div>
              </div>
              <div className="flex flex-col gap-3 w-full">
                <div>
                  <label>Supplier</label>
                  <Input
                    placeholder="Supplier"
                    value={formValue.supplier}
                    onChange={(value) => {
                      setFormValue({ ...formValue, supplier: value });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>
                    Date Review <span className="text-red-500">*</span>
                  </label>
                  <DateRangePicker
                    value={[
                      formValue?.date_review_from || "",
                      formValue?.date_review_to || "",
                    ]}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        date_review_from: value?.[0] || "",
                        date_review_to: value?.[1] || "",
                      });
                    }}
                    block
                  />
                </div>

                <div>
                  <label>
                    Publish Date <span className="text-red-500">*</span>
                  </label>
                  <DatePicker
                    appearance="default"
                    value={formValue.publish_date}
                    onChange={(value) => {
                      setFormValue({ ...formValue, publish_date: value });
                    }}
                    block
                    oneTap
                  />
                </div>

                <div>
                  <label>Qualify Status</label>
                  <Input
                    placeholder="Qualify Status"
                    value={formValue.qualify_status}
                    onChange={(value) => {
                      setFormValue({ ...formValue, qualify_status: value });
                    }}
                    maxLength={100}
                  />
                </div>

                <div>
                  <label>Type</label>
                  <InputPicker
                    value={formValue.type}
                    data={typeData}
                    onChange={(value) => {
                      setFormValue({ ...formValue, type: value });
                    }}
                    block
                  />
                </div>
              </div>
            </div>

            <div>
              <label>Remarks</label>
              <Input
                as="textarea"
                placeholder="Remarks"
                value={formValue.remarks}
                onChange={(value) => {
                  setFormValue({ ...formValue, remarks: value });
                }}
                maxLength={250}
              />
              {/* char counter */}
              <div className="flex justify-end">
                <span className="text-xs text-gray-400">
                  {formValue.remarks?.length || 0}/250
                </span>
              </div>
            </div>

            <div className="flex flex-col gap-2">
              <span className="text-red-500">* Required</span>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                handleCloseModal();
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleActions();
              }}
              appearance="primary"
            >
              {mode === "add" ? "Add" : "Edit"}
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
