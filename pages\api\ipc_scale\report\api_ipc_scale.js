import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiScaleHeader() {
  return {
    getScaleDetailWithStatistics: createApiFunction("post", "v2/ipc/ipc_scale/list-detail"),
    getAllScaleHeader: createApiFunction("get", "v2/ipc/ipc_scale/list"),
  };
}
