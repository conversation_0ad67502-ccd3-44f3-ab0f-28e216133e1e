import MainContent from "@/components/layout/MainContent";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import ModuleApi from "../api/moduleApi";
import Head from "next/head";
import { Dropdown } from "rsuite";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";

function UserAccess({ userModule, emp_id, emp_name, errorFetch }) {
  const MySwal = withReactContent(Swal);
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [selectedModule, setSelectedModule] = useState({});
  const [moduleData, setModuleData] = useState([]);
  const [moduleActiveId, setModuleActiveId] = useState("");
  const [moduleActiveName, setModuleActiveName] = useState("");
  const router = useRouter();

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
      return;
    }

    // Cek validasi access general administration
    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  }, []);

  useEffect(() => {
    // if (userModule.length > 0) {
    //   setModuleData(userModule);
    // }
    setModuleData(userModule);
    if (errorFetch) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: errorFetch,
      });
    }
  }, [userModule, errorFetch]);

  // useEffect set Module Name
  useEffect(() => {
    for (let item of moduleData) {
      if (item.Module_Code == moduleActiveId) {
        setModuleActiveName(item.Module_Name);
      }
    }
  }, [moduleActiveId]);

  // submit handler
  const submitUserAccess = (event) => {
    event.preventDefault();
    setIsFormDisabled(true);

    if (!selectedModule) {
      setIsFormDisabled(false);
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Please select a module !",
      });
      return;
    }

    const dataQuery = {
      emp_id: emp_id,
      emp_name: emp_name,
      module_code: selectedModule.Module_Code,
      module_name: selectedModule.Module_Name,
    };

    router.push({
      pathname: "/user_management/userAccessParentMenu",
      query: {
        ...dataQuery,
      },
    });
  };

  return (
    <>
      <div>
        <Head>
          <title>User Access</title>
        </Head>
      </div>
      <ContainerLayout
        title="Menu Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <MainContent>
          <h4>Form User Access</h4>
          <div className="p-5">
            <form onSubmit={submitUserAccess}>
              <div className="mb-3">
                <label htmlFor="validationServer03" className="form-label">
                  Employee ID
                </label>
                <input
                  type="text"
                  className={`form-control`}
                  id="employeeId"
                  value={emp_id}
                  aria-describedby="validationServer03Feedback"
                  disabled={true}
                />
              </div>
              <div>
                <label htmlFor="employee_name" className="form-label">
                  Employee Name
                </label>
                <input
                  type="text"
                  className="form-control mb-3"
                  id="employee_name"
                  value={emp_name}
                  aria-describedby="emailHelp"
                  disabled={true}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="select_module" className="form-label">
                  Select Module :
                </label>
                <div>
                  <Dropdown
                    title={
                      selectedModule.Module_Name
                        ? selectedModule.Module_Name
                        : "-- Select Module --"
                    }
                    onSelect={(value) => setSelectedModule(value)}
                  >
                    <Dropdown.Item eventKey="">
                      -- Select Module --
                    </Dropdown.Item>
                    {moduleData
                      ? moduleData.map((item) => (
                          <Dropdown.Item eventKey={item}>
                            {item.Module_Name}
                          </Dropdown.Item>
                        ))
                      : "No Module Found."}
                  </Dropdown>
                </div>
              </div>

              <button
                type="submit"
                disabled={selectedModule.Module_Code ? false : true}
                className="btn btn-primary p-2"
              >
                Next
              </button>
              <button
                type="button"
                disabled={isFormDisabled}
                className="btn btn-dark mx-2 p-2"
                onClick={() => router.back()}
              >
                Cancel
              </button>
            </form>
          </div>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  const { emp_id, emp_name } = query;

  const { GetModule } = ModuleApi();
  const userModuleData = await GetModule(emp_id);
  let userModule = [];
  let errorFetch = null;
  const { Data } = userModuleData;
  const { error } = userModuleData;

  if (Data) {
    userModule = Data;
  }

  if (error) {
    errorFetch = error;
  }

  return {
    props: {
      userModule,
      emp_id,
      emp_name,
      errorFetch,
    },
  };
}

export default UserAccess;
