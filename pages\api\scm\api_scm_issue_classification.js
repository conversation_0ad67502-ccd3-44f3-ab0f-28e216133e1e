import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/scm/scm_issue_classification/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};

export default function ScmDashboardApi(){
    return {
        getAll: createApiFunction("post", "get/all"),
        create: createApiFunction("post", "create"),
        updateStatus: createApiFunction("put", "update/status"),
        update: createApiFunction("put", "update"),
        getLotNumber: createApiFunction("post", "get/lot-number"),
    }
}