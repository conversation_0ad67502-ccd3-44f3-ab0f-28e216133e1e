import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiPQR() {
  return {
    getAllPQR: createApiFunction("get", "pqr/masterdata_parameter/list"),
    getAllActivePQR: createApiFunction("get", "pqr/masterdata_parameter/list-active"),
    createPQR: createApiFunction("post", "pqr/masterdata_parameter/create"),
    editPQR: createApiFunction("put", "pqr/masterdata_parameter/edit"),
    editStatusPQR: createApiFunction("put", "pqr/masterdata_parameter/edit-status"),
  };
}
