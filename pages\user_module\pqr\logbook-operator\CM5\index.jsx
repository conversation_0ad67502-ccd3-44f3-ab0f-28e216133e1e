import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  Loader,
  DatePicker,
  SelectPicker,
  useToaster,
  Notification,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import Api_logbook from "@/pages/api/pqr/logbook/api_logbook";
import { useRouter } from "next/router";

export default function LogbookOperatorCM5() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_activity");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const toaster = useToaster();

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [PQRsDataState, setPQRsDataState] = useState([]);

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const emptyAddActivityLogbookForm = {
    activity_type: "CM5",
    activity_start: null,
    activity: null,
    remarks: null,
  };

  const emptyEditActivityLogbookForm = {
    activity_type: "CM5",
    activity_start: null,
    activity: null,
    remarks: null,
    activity_end: null,
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [addActivityLogbookForm, setAddActivityLogbookForm] = useState(emptyAddActivityLogbookForm);
  const [editActivityLogbookForm, setEditActivityLogbookForm] = useState(emptyEditActivityLogbookForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = PQRsDataState.filter((rowData, i) => {
    const searchFields = ["id_activity", "activity_type", "activity", "activity_start", "activity_end", "create_date", "create_by", "update_date", "update_by", "delete_date", "delete_by", "is_active"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : PQRsDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/masterdata")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandleGetAllLogbook();
    }
  }, []);

  const HandleGetAllLogbook = async () => {
    try {
      const requestData = { activity_type: "CM5" };
      const res = await Api_logbook().GetAllLogbook(requestData);

      console.log("res", res);
      if (res.status === 200) {
        setPQRsDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };

  const HandleAddActivityLogbook = async () => {
    const errors = {};

    if (!addActivityLogbookForm.activity) {
      errors.activity = "Activity is required";
    }

    if (addActivityLogbookForm.activity === "other" && !addActivityLogbookForm.customActivity) {
      errors.customActivity = "Custom activity is required";
    }

    if (!addActivityLogbookForm.activity_start) {
      errors.activity_start = "Activity start is required";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }
    setLoading(true);
    try {
      const activityValue = addActivityLogbookForm.activity === "other"
        ? addActivityLogbookForm.customActivity
        : addActivityLogbookForm.activity;

      const res = await Api_logbook().PostLogbook({
        activity_type: addActivityLogbookForm.activity_type,
        activity: activityValue,
        remarks: addActivityLogbookForm.remarks,
        activity_start: addActivityLogbookForm.activity_start,
        create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        showNotification("success", "Berhasil menambah aktifitas Logbook");
        setAddActivityLogbookForm(emptyAddActivityLogbookForm);
        setShowAddModal(false);
        HandleGetAllLogbook();
      } else {
        console.log("Gagal menambah aktifitas Logbook", res.message);
        showNotification("error", "Gagal menambah aktifitas Logbook");
      }
    } catch (error) {
      console.log("Gagal menambah aktifitas Logbook", error);
      showNotification("error", "Gagal menambah aktifitas Logbook");
    } finally {
      setLoading(false);
    }
  };

  const HandleEditActivityLogbook = async () => {
    const errors = {};

    if (!editActivityLogbookForm.activity) {
      errors.activity = "Activity is required";
    }

    if (editActivityLogbookForm.activity === "other" && !editActivityLogbookForm.customActivity) {
      errors.customActivity = "Custom activity is required";
    }

    if (!editActivityLogbookForm.activity_start) {
      errors.activity_start = "Activity start is required";
    }

    if (!editActivityLogbookForm.activity_end) {
      errors.activity_end = "Activity end is required";
    } else if (new Date(editActivityLogbookForm.activity_end) < new Date(editActivityLogbookForm.activity_start)) {
      errors.activity_end = "Activity end cannot be before Activity start";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);
      return;
    }
    try {
      const activityValue = editActivityLogbookForm.activity === "other"
        ? editActivityLogbookForm.customActivity
        : editActivityLogbookForm.activity;

      const res = await Api_logbook().PutActivityLogbook({
        activity_type: editActivityLogbookForm.activity_type,
        activity: activityValue,
        remarks: editActivityLogbookForm.remarks,
        activity_start: editActivityLogbookForm.activity_start,
        activity_end: editActivityLogbookForm.activity_end,
        create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        HandleGetAllLogbook();
        setShowEditModal(false);
        showNotification("success", "Berhasil mengubah aktifitas Logbook");
      } else {
        console.log("error mengubah Aktifitas Logbook ", res.message);
        showNotification("error", "Gagal mengubah aktifitas Logbook");
      }
    } catch (error) {
      console.log("error mengubah Aktifitas Logbook ", error);
      showNotification("error", "Gagal mengubah aktifitas Logbook");
    }
  };

  const handleEditStatusActivityLogbook = async (id_activity, is_active) => {
    try {
      const res = await Api_logbook().PutStatusLogbook({
        id_activity,
        is_active,
        delete_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        console.log("Berhasil mengubah status aktifitas Logbook:", res.message);
        showNotification("success", "Berhasil mengubah status aktifitas Logbook");
        HandleGetAllLogbook();
      } else {
        console.log("Error on update status: ", res.message);
        showNotification("error", "Gagal mengubah status aktifitas Logbook");
      }
    } catch (error) {
      console.log("Gagal mengubah Status Aktifitas Logbook: ", error);
      showNotification("error", "Gagal mengubah status aktifitas Logbook");

    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Logbook CM5</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>Operator</Breadcrumb.Item>
                  <Breadcrumb.Item active>Logbook</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Halaman Logbook CM5</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >

              <Table
                bordered
                cellBordered
                height={400}
                data={getPaginatedData(getFilteredData(), limit, page)}
                sortColumn={sortColumn}
                sortType={sortType}
                onSortColumn={handleSortColumn}
                autoHeight
              >
                <Column width={120} align="center" sortable resizable fullText>
                  <HeaderCell>ID Aktifitas</HeaderCell>
                  <Cell dataKey="id_activity" />
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Aktifitas</HeaderCell>
                  <Cell dataKey="activity" />
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Catatan</HeaderCell>
                  <Cell dataKey="remarks" />
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dimulai</HeaderCell>
                  <Cell>{(rowData) => (rowData.activity_start ? new Date(rowData.activity_start).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Berakhir</HeaderCell>
                  <Cell>{(rowData) => (rowData.activity_end ? new Date(rowData.activity_end).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Pembuatan</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={150} sortable resizable fullText>
                  <HeaderCell align="center">Dibuat oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.delete_by}</>}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Approve</HeaderCell>
                  <Cell>{(rowData) => (rowData.approve_date ? new Date(rowData.approve_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Approve Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.approve_by}</>}</Cell>
                </Column>
                <Column width={100} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          disabled={!!rowData.approve_date || rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModal(true);
                            setEditActivityLogbookForm({
                              ...editActivityLogbookForm,
                              activity_type: rowData.activity_type,
                              activity: ["Cusu Rutin", "Cusu Berkala", "Cusu Operational"].includes(rowData.activity)
                                ? rowData.activity
                                : "other",
                              customActivity: rowData.activity,
                              remarks: rowData.remarks,
                              activity_start: rowData.activity_start,
                              activity_end: rowData.activity_end,
                              id_activity: rowData.id_activity,
                              update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                            });
                          }}
                        >
                          <EditIcon />
                        </Button>
                        <Button
                          appearance="subtle"
                          disabled={!!rowData.approve_date}
                          onClick={() => handleEditStatusActivityLogbook(rowData.id_activity, rowData.is_active)}>
                          {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>

            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                setShowAddModal(false);
                setAddActivityLogbookForm(emptyAddActivityLogbookForm);
                setErrorsAddForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Tambah Aktifitas Logbook</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Tipe Aktifitas</Form.ControlLabel>
                    <Form.Control
                      name="activity_type"
                      value="CM5"
                      disabled
                    />
                    {errorsAddForm.activity_type && <p style={{ color: "red" }}>{errorsAddForm.activity_type}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Aktifitas</Form.ControlLabel>
                    <SelectPicker
                      name="activity"
                      value={addActivityLogbookForm.activity}
                      placeholder="Pilih Aktifitas"
                      data={[
                        { label: "Cusu Rutin", value: "Cusu Rutin" },
                        { label: "Cusu Berkala", value: "Cusu Berkala" },
                        { label: "Cusu Operational", value: "Cusu Operational" },
                        { label: "Lainnya", value: "other" },
                      ]}
                      onChange={(value) => {
                        if (value === "other") {
                          setAddActivityLogbookForm((prevFormValue) => ({
                            ...prevFormValue,
                            activity: value,
                            customActivity: "",
                          }));
                        } else {
                          setAddActivityLogbookForm((prevFormValue) => ({
                            ...prevFormValue,
                            activity: value,
                          }));
                        }
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          activity: undefined,
                        }));
                      }}
                      style={{ width: "100%" }}
                    />
                    {addActivityLogbookForm.activity === "other" && (
                      <Input
                        placeholder="Masukkan Aktifitas"
                        value={addActivityLogbookForm.customActivity}
                        onChange={(value) => {
                          setAddActivityLogbookForm((prevFormValue) => ({
                            ...prevFormValue,
                            customActivity: value,
                          }));
                        }}
                        style={{ width: "100%", marginTop: 10 }}
                      />
                    )}
                    {errorsAddForm.activity && <p style={{ color: "red" }}>{errorsAddForm.activity}</p>}
                    {addActivityLogbookForm.activity === "other" && errorsAddForm.customActivity && (
                      <p style={{ color: "red" }}>{errorsAddForm.customActivity}</p>
                    )}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Catatan</Form.ControlLabel>
                    <Form.Control
                      name="remarks"
                      value={addActivityLogbookForm.remarks}
                      placeholder="Masukkan Catatan"
                      onChange={(value) => {
                        setAddActivityLogbookForm((prevFormValue) => ({
                          ...prevFormValue,
                          remarks: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          remarks: undefined,
                        }));
                      }}
                    />
                    {errorsAddForm.remarks && <p style={{ color: "red" }}>{errorsAddForm.remarks}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Tanggal Dimulai</Form.ControlLabel>
                    <DatePicker
                      name="activity_start"
                      format="dd/MM/yyyy"
                      placeholder="Pilih Tanggal Dimulai"
                      value={addActivityLogbookForm.activity_start}
                      onChange={(value) => {
                        setAddActivityLogbookForm((prevFormValue) => ({
                          ...prevFormValue,
                          activity_start: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          activity_start: undefined,
                        }));
                      }}
                      block
                    />
                    {errorsAddForm.activity_start && <p style={{ color: "red" }}>{errorsAddForm.activity_start}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddActivityLogbookForm(emptyAddActivityLogbookForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleAddActivityLogbook();
                  }}
                  appearance="primary"
                  type="submit"
                  loading={loading} // Menampilkan loading state pada tombol
                  disabled={loading} // Menonaktifkan tombol saat loading
                >
                  Tambah
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Adding Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditActivityLogbookForm(emptyEditActivityLogbookForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Ubah Aktifitas Logbook</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Tipe Aktifitas</Form.ControlLabel>
                    <Form.Control
                      name="activity_type"
                      value="CM5"
                      disabled
                    />
                    {errorsEditForm.activity_type && <p style={{ color: "red" }}>{errorsEditForm.activity_type}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Aktifitas</Form.ControlLabel>
                    <SelectPicker
                      name="activity"
                      value={editActivityLogbookForm.activity}
                      placeholder="Pilih Aktifitas"
                      data={[
                        { label: "Cusu Rutin", value: "Cusu Rutin" },
                        { label: "Cusu Berkala", value: "Cusu Berkala" },
                        { label: "Cusu Operational", value: "Cusu Operational" },
                        { label: "Lainnya", value: "other" },
                      ]}
                      onChange={(value) => {
                        if (value === "other") {
                          setEditActivityLogbookForm((prevFormValue) => ({
                            ...prevFormValue,
                            activity: value,
                            customActivity: "",
                          }));
                        } else {
                          setEditActivityLogbookForm((prevFormValue) => ({
                            ...prevFormValue,
                            activity: value,
                          }));
                        }
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          activity: undefined,
                        }));
                      }}
                      style={{ width: "100%" }}
                    />
                    {editActivityLogbookForm.activity === "other" && (
                      <Input
                        placeholder="Masukkan Aktifitas"
                        value={editActivityLogbookForm.customActivity}
                        onChange={(value) => {
                          setEditActivityLogbookForm((prevFormValue) => ({
                            ...prevFormValue,
                            customActivity: value,
                          }));
                        }}
                        style={{ width: "100%", marginTop: 10 }}
                      />
                    )}
                    {errorsEditForm.activity && <p style={{ color: "red" }}>{errorsEditForm.activity}</p>}
                    {editActivityLogbookForm.activity === "other" && errorsEditForm.customActivity && (
                      <p style={{ color: "red" }}>{errorsEditForm.customActivity}</p>
                    )}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Catatan</Form.ControlLabel>
                    <Form.Control
                      name="remarks"
                      value={editActivityLogbookForm.remarks}
                      onChange={(value) => {
                        setEditActivityLogbookForm((prevFormValue) => ({
                          ...prevFormValue,
                          remarks: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          remarks: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.remarks && <p style={{ color: "red" }}>{errorsEditForm.remarks}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Tanggal Dimulai</Form.ControlLabel>
                    <DatePicker
                      placeholder="Pilih Tanggal Mulai"
                      format="dd/MM/yyyy"
                      value={editActivityLogbookForm.activity_start ? new Date(editActivityLogbookForm.activity_start) : null}
                      onChange={(value) => {
                        const formattedDate = value ? value.toISOString().split("T")[0] + "T00:00:00Z" : null;
                        setEditActivityLogbookForm((prevFormValue) => ({
                          ...prevFormValue,
                          activity_start: formattedDate,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          activity_start: undefined,
                        }));
                      }}
                      block
                    />
                    {errorsEditForm.activity_start && <p style={{ color: "red" }}>{errorsEditForm.activity_start}</p>}
                  </Form.Group>

                  <Form.Group>
                    <Form.ControlLabel>Tanggal Selesai</Form.ControlLabel>
                    <DatePicker
                      placeholder="Pilih Tanggal Selesai"
                      format="dd/MM/yyyy"
                      value={editActivityLogbookForm.activity_end ? new Date(editActivityLogbookForm.activity_end) : null}
                      onChange={(value) => {
                        const formattedDate = value ? value.toISOString().split("T")[0] + "T00:00:00Z" : null;
                        setEditActivityLogbookForm((prevFormValue) => ({
                          ...prevFormValue,
                          activity_end: formattedDate,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          activity_end: undefined,
                        }));
                      }}
                      block
                    />
                    {errorsEditForm.activity_end && <p style={{ color: "red" }}>{errorsEditForm.activity_end}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditActivityLogbookForm(emptyEditActivityLogbookForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleEditActivityLogbook();
                  }}
                  appearance="primary"
                  type="submit"
                >
                  Ubah
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Editing Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
