import Head from "next/head";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "rsuite";
import Api_IPC_Scale_DASHBOARD from "@/pages/api/ipc_scale/api_ipc_scale_dashboard.js";
import ChartDataLabels from "chartjs-plugin-datalabels";
import CryptoJS from "crypto-js";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
} from "chart.js";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
  ChartDataLabels
);

import { Line } from "react-chartjs-2";
import { useRouter } from "next/router";

export default function PdfPreview({
  ssr_batch_code,
  ssr_selected_step,
  ssr_dashboarddata,
  ssr_reasondata,
}) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [stateShowDownload, setStateShowDownload] = useState(true);
  const [showWeight, setShowWeight] = useState(true);
  const [showThickness, setShowThickness] = useState(true);
  const [clickDownload, setClickDownload] = useState(false);
  const [sessionAuth, setSessionAuth] = useState(null);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    }
  }, []);

  let rowNumber = 1;

  const generatePDF = async () => {
    document.getElementById("headdownload").remove();

    // Delay 7 seconds before generating the PDF
    await new Promise((resolve) => setTimeout(resolve, 2000));

    const input = document.getElementById("printedDocument");
    const pdf = new jsPDF("p", "mm", "a4");

    const canvas = await html2canvas(input);
    const imageData = canvas.toDataURL("image/png");

    pdf.addImage(imageData, "PNG", 0, 0, 210, 297); // A4 size
    pdf.save(`Print Out IPC BatchCode ${ssr_batch_code}`);
  };

  const generatePDFWeight = async () => {
    setShowThickness(false);
  };

  useEffect(() => {
    if (!showThickness) {
      // Execute the PDF generation logic after showThickness is set to false
      generatePDF();
    }
  }, [showThickness]);

  const generatePDFThickness = async () => {
    setShowWeight(false);
  };

  useEffect(() => {
    if (!showWeight) {
      // Execute the PDF generation logic after showWeight is set to false
      generatePDF();
    }
  }, [showWeight]);

  useEffect(() => {
    // window.print()
    // if (stateClicked) {
    //   const status = !stateClicked
    //   setStateClicked(status)
    //   //document.getElementById("headdownload").append()
    // }
    // console.log("first")

    if (clickDownload) {
      setClickDownload(false);
      window.print();
    }

    // console.log(ssr_dashboarddata)
    setStateShowDownload(true);
  }, [stateShowDownload]);

  const prepareWeightChartData = (data) => {
    const labels = data.map((item) => item.info);
    const avg = data.map((item) => item.scale_avg_weight);
    const min = data.map((item) => item.scale_min_weight);
    const max = data.map((item) => item.scale_max_weight);

    return {
      labels,
      datasets: [
        {
          label: "Avg Weight",
          data: avg,
          backgroundColor: "#FF0000",
          borderColor: "#FF0000",
        },
        {
          label: "Max Weight",
          data: max,
          backgroundColor: "#0000FF",
          borderColor: "#0000FF",
        },
        {
          label: "Min Weight",
          data: min,
          backgroundColor: "#FFA500",
          borderColor: "#FFA500",
        },
      ],
    };
  };

  const LineWeightChart = ({ chartData }) => {
    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: "bottom",
        },
        title: {
          display: true,
          text: `${ssr_batch_code} IPC : Scale ${
            ssr_selected_step ? ssr_selected_step : ""
          }`,
          padding: {
            bottom: 50,
            top: 20,
          },
        },
        datalabels: {
          anchor: "end",
          align: "end",
          color: function (context) {
            return context.dataset.backgroundColor;
          },
          font: {
            weight: "bold",
          },
          formatter: (value, context) => {
            const formattedValue = value.toLocaleString();
            return formattedValue;
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return <Line data={chartData} options={options} />;
  };

  return (
    <>
      <Head>
        <title>
          Print out Reporting Measurement - In Process Control Scale{" "}
          {ssr_batch_code}
        </title>
      </Head>
      <div>
        {stateShowDownload && (
          <div
            id="headdownload"
            style={{
              width: "100%",
              padding: "1em",
              backgroundColor: "#2c2c30",
              boxShadow: "2px 2px 10px #6c6b75",
              position: "-webkit-sticky",
              position: "sticky",
              top: 0,
            }}>
            <Stack
              direction="horizontal"
              justifyContent="space-between"
              alignItems="center">
              {/* Left Section: Text */}
              <Stack>
                <p
                  style={{
                    color: "white",
                    fontSize: "1em",
                    textAlign: "left",
                  }}>
                  Print Out Reporting Measurement - In Process Control ID :{" "}
                  {ssr_batch_code}
                </p>
              </Stack>

              {/* Right Section: Buttons */}
              <Stack direction="vertical" spacing={4}>
                <Button title="Download" onClick={generatePDFWeight}>
                  Print PDF
                </Button>
                {/* <Button title="Download" onClick={generatePDFThickness}>
                  Print Thickness
                </Button>
                <Button title="Download" onClick={generatePDF}>
                  Print All
                </Button> */}
              </Stack>
            </Stack>
          </div>
        )}
        <div style={{ width: "100%", backgroundColor: "#65656b" }}>
          <div
            id="printedDocument"
            style={{
              width: "100%",
              backgroundColor: "white",
              margin: "auto",
              padding: "1.5em",
            }}>
            <div>
              <img
                src="/Logo_kalbe_detail.png"
                alt="Logo_kalbe_detail.png"
                style={{ width: 150 }}
              />
            </div>
            <div style={{ marginBottom: "2em", marginTop: "2em" }}>
              <p
                style={{
                  textAlign: "center",
                  fontWeight: "bold",
                  fontSize: "2em",
                }}>
                REPORTING MEASUREMENT {ssr_batch_code}
              </p>
            </div>
            <div>
              {showWeight && (
                <LineWeightChart
                  chartData={prepareWeightChartData(ssr_dashboarddata)}
                  plugins={[ChartDataLabels]}
                  loading={loading}
                />
              )}

              {/* <LineDiameterChart
                chartData={prepareDiameterChartData(ssr_dashboarddata)}
                plugins={[ChartDataLabels]}
                loading={loading}
              /> */}

              {/* {showThickness && <LineThicknessChart chartData={prepareThicknessChartData(ssr_dashboarddata)} plugins={[ChartDataLabels]} loading={loading} />} */}

              <br />
              {ssr_reasondata.length > 0 && (
                <div style={{ overflowX: "auto", width: "100%" }}>
                  <table style={{ width: "100%", borderCollapse: "collapse" }}>
                    <thead>
                      <tr>
                        <th
                          style={{
                            border: "1px solid #ddd",
                            padding: "8px",
                            textAlign: "center",
                          }}>
                          No
                        </th>
                        <th
                          style={{
                            border: "1px solid #ddd",
                            padding: "8px",
                            textAlign: "center",
                          }}>
                          Product Code
                        </th>
                        <th
                          style={{
                            border: "1px solid #ddd",
                            padding: "8px",
                            textAlign: "center",
                          }}>
                          Info
                        </th>
                        <th
                          style={{
                            border: "1px solid #ddd",
                            padding: "8px",
                            textAlign: "center",
                          }}>
                          Reason
                        </th>
                        <th
                          style={{
                            border: "1px solid #ddd",
                            padding: "8px",
                            textAlign: "center",
                          }}>
                          Operator
                        </th>
                        <th
                          style={{
                            border: "1px solid #ddd",
                            padding: "8px",
                            textAlign: "center",
                          }}>
                          Approval by
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {ssr_reasondata.map((rowData, index) => (
                        <tr key={index}>
                          <td
                            style={{
                              border: "1px solid #ddd",
                              padding: "8px",
                              textAlign: "center",
                            }}>
                            {index + 1}
                          </td>
                          <td
                            style={{
                              border: "1px solid #ddd",
                              padding: "8px",
                            }}>
                            {rowData.product_code}
                          </td>
                          <td
                            style={{
                              border: "1px solid #ddd",
                              padding: "8px",
                            }}>
                            {rowData.info}
                          </td>
                          <td
                            style={{
                              border: "1px solid #ddd",
                              padding: "8px",
                            }}>
                            {rowData.reason}
                          </td>
                          <td
                            style={{
                              border: "1px solid #ddd",
                              padding: "8px",
                            }}>
                            {rowData.operator_created}
                          </td>
                          <td
                            style={{
                              border: "1px solid #ddd",
                              padding: "8px",
                            }}>
                            {rowData.approve_details}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              <br />
              <p>{`Print Out By : ${sessionAuth?.employee_id} - ${
                sessionAuth?.employee_name
              } | Printed Date: ${new Date().toLocaleDateString("en-GB")}`}</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// !!IMPORTANT!!
export async function getServerSideProps(context) {
  const { getIpcScaleDashboard, getIpcScaleReason } = Api_IPC_Scale_DASHBOARD();
  const { query } = context;
  let { ssr_batch_code, ssr_selected_step } = query;

  ssr_batch_code = atob(ssr_batch_code);
  ssr_selected_step = atob(ssr_selected_step);

  const res = await getIpcScaleDashboard();
  const resReason = await getIpcScaleReason();

  let ssr_dashboarddata = [];
  let ssr_reasondata = [];

  // console.log("res", res)
  if (res.status == 200) {
    const filteredTransactions = res.data.filter((transaction) => {
      if (ssr_selected_step === "") {
        return transaction.product_code === ssr_batch_code;
      } else {
        return (
          transaction.product_code === ssr_batch_code &&
          transaction.info.slice(0, 2) === ssr_selected_step
        );
      }
    });
    ssr_dashboarddata = filteredTransactions;
  }

  if (resReason.status == 200) {
    const filteredTransactionsReason = resReason.data.filter((transaction) => {
      if (ssr_selected_step === "") {
        return transaction.product_code === ssr_batch_code;
      } else {
        return (
          transaction.product_code === ssr_batch_code &&
          transaction.info.slice(0, 2) === ssr_selected_step
        );
      }
    });
    ssr_reasondata = filteredTransactionsReason;
  }

  return {
    props: {
      ssr_batch_code,
      ssr_selected_step,
      ssr_dashboarddata,
      ssr_reasondata,
    },
  };
}
