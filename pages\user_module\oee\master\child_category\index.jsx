import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, Notification } from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiMasterChildCategory from "@/pages/api/oee/master_child_category/api_master_child_category";
import { useRouter } from "next/router";

export default function MasterChildCategoryPage() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_child_category");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const toaster = useToaster();
  const router = useRouter();

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [masterChildCategoryDataState, setMasterChildCategoryuDataState] = useState([]);

  const emptyMasterChildCategoryForm = {
    child_category_name: null,
  };

  const emptyEditMasterChildCategoryForm = {
    id_child_category: null,
    child_category_name: null,
    updated_by: null,
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [addLoading, setAddLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(null);

  const [addMasterChildCategoryForm, setAddMasterChildCategoryForm] = useState(emptyMasterChildCategoryForm);
  const [editMasterChildCategoryForm, setEditMasterChildCategoryForm] = useState(emptyMasterChildCategoryForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = masterChildCategoryDataState.filter((rowData, i) => {
    const searchFields = ["id_child_category", "child_category_name", "created_date", "created_by", "updated_date", "updated_by", "deleted_date", "deleted_by", "is_active"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : masterChildCategoryDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("oee/master/child_category"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandleGetAllMasterChildCategoryApi();
    }
  }, []);

  const HandleGetAllMasterChildCategoryApi = async () => {
    try {
      const res = await ApiMasterChildCategory().GetAllMasterChildCategory();

      console.log("res", res);
      if (res.status === 200) {
        setMasterChildCategoryuDataState(res.data);
      } else {
        console.log("error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Api", error);
    }
  };

  const HandleAddMasterChildCategoryApi = async () => {
    const errors = {};

    // Check each field and only set the error if it's empty

    if (!addMasterChildCategoryForm.child_category_name || addMasterChildCategoryForm.child_category_name.trim() === "") {
      errors.child_category_name = "child category name is required";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }
    try {
      setAddLoading(true);
      const res = await ApiMasterChildCategory().CreateMasterChildCategory({
        ...addMasterChildCategoryForm,
        created_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        setAddMasterChildCategoryForm(emptyMasterChildCategoryForm);
        setShowAddModal(false);
        await HandleGetAllMasterChildCategoryApi();
        showNotification("success", "Added successfully");
      } else {
        showNotification("error", "Failed to add data");
      }
    } catch (error) {
      showNotification("error", "Error adding data");
    } finally {
      setAddLoading(false);
    }
  };

  const HandleEditMasterChildCategoryApi = async () => {
    const errors = {};

    // Check each field and only set the error if it's empty
    if (!editMasterChildCategoryForm.child_category_name || editMasterChildCategoryForm.child_category_name.trim() === "") {
      errors.child_category_name = "Child Category name is required";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);

      return;
    }
    try {
      setEditLoading(true);
      const res = await ApiMasterChildCategory().EditMasterChildCategory({
        ...editMasterChildCategoryForm,
        updated_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        await HandleGetAllMasterChildCategoryApi();
        setShowEditModal(false);
        showNotification("success", "Data updated successfully");
      } else {
        showNotification("error", "Failed to update data");
      }
    } catch (error) {
      toaster.push({ message: "Error updating data", type: "error" });
    } finally {
      setEditLoading(false);
    }
  };

  const handleEditStatusMasterChildCategoryApi = async (id_child_category, is_active) => {
    try {
      setStatusLoading(id_child_category);
      const res = await ApiMasterChildCategory().EditStatusMasterChildCategory({
        id_child_category,
        is_active,
        deleted_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        await HandleGetAllMasterChildCategoryApi();
        toaster.push({
          message: `Status ${is_active === 1 ? "deactivated" : "activated"} successfully`,
          type: "success",
        });
      } else {
        toaster.push({ message: "Failed to update status", type: "error" });
      }
    } catch (error) {
      toaster.push({ message: "Error updating status", type: "error" });
    } finally {
      setStatusLoading(null);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Master Child Category Page</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>OEE</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>Master Child Category</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Master Child Category Page</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}
                    >
                      Add
                    </IconButton>
                  </div>

                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                <Column width={200} align="center" sortable fullText>
                  <HeaderCell>ID Child Category</HeaderCell>
                  <Cell dataKey="id_child_category" />
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Child Category Name</HeaderCell>
                  <Cell dataKey="child_category_name" />
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Created Date</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.created_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Created By</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.created_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Updated Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.updated_date ? new Date(rowData.updated_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Updated By</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.updated_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Delete Date</HeaderCell>
                  <Cell>{(rowData) => (rowData.deleted_date ? new Date(rowData.deleted_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Delete By</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.deleted_by}</>}</Cell>
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Active" : "Inactive"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Action</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModal(true);
                            setEditMasterChildCategoryForm({
                              ...editMasterChildCategoryForm,
                              child_category_name: rowData.child_category_name,
                              id_child_category: rowData.id_child_category,
                              updated_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                            });
                          }}
                        >
                          <EditIcon />
                        </Button>
                        <Button appearance="subtle" onClick={() => handleEditStatusMasterChildCategoryApi(rowData.id_child_category, rowData.is_active)}>
                          {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            {/* modal pop up untuk add master child category */}
            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                if (!addLoading) {
                  setShowAddModal(false);
                  setAddMasterChildCategoryForm(emptyMasterChildCategoryForm);
                  setErrorsAddForm({});
                }
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Add Master Child Category</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Child Category Name</Form.ControlLabel>
                    <Form.Control
                      name="child_category_name"
                      value={addMasterChildCategoryForm.child_category_name}
                      onChange={(value) => {
                        setAddMasterChildCategoryForm((prevFormValue) => ({
                          ...prevFormValue,
                          child_category_name: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          child_category_name: undefined,
                        }));
                      }}
                      disabled={addLoading}
                    />
                    {errorsAddForm.child_category_name && <p style={{ color: "red" }}>{errorsAddForm.child_category_name}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddMasterChildCategoryForm(emptyMasterChildCategoryForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle"
                  disabled={addLoading}
                >
                  Cancel
                </Button>
                <Button onClick={HandleAddMasterChildCategoryApi} appearance="primary" loading={addLoading} disabled={addLoading}>
                  Add
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal pop up untuk edit master child category */}
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditMasterChildCategoryForm(emptyEditMasterChildCategoryForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Edit Master Child Category</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Child Category Name</Form.ControlLabel>
                    <Form.Control
                      name="child_category_name"
                      value={editMasterChildCategoryForm.child_category_name}
                      onChange={(value) => {
                        setEditMasterChildCategoryForm((prevFormValue) => ({
                          ...prevFormValue,
                          child_category_name: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          child_category_name: undefined,
                        }));
                      }}
                      disabled={editLoading}
                    />
                    {errorsEditForm.child_category_name && <p style={{ color: "red" }}>{errorsEditForm.child_category_name}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditMasterChildCategoryForm(emptyMasterChildCategoryForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                  disabled={editLoading}
                >
                  Cancel
                </Button>
                <Button onClick={HandleEditMasterChildCategoryApi} appearance="primary" loading={editLoading} disabled={editLoading}>
                  Edit
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
