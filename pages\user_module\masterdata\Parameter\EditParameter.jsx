import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import MainContent from '@/components/layout/MainContent';
import { Dropdown, Button, Form, ButtonToolbar, Toggle } from 'rsuite';
import Head from 'next/head';
import withReactContent from 'sweetalert2-react-content';
import Swal from 'sweetalert2';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import ParameterApi from '@/pages/api/parameterApi';

export default function EditParameter({ idParameter }) {
  const MySwal = withReactContent(Swal);
  const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
  const [parameterName, setParameterName] = useState('');
  const [moduleName, setModuleName] = useState('');
  const [existingInputMethod, setExistingInputMethod] = useState('');
  const [allInputMethodData, setAllInputMethodData] = useState([]);
  const [selectedInputMethod, setSelectedInputMethod] = useState({});
  const [userDepartment, setUserDepartment] = useState('');
  const [isActive, setIsActive] = useState(false);
  const { GetAllInputMethod, GetParameterById } = ParameterApi();
  const router = useRouter();
  let path = 'masterdata/Parameter';
  let { data } = router.query;
  data ? (data = JSON.parse(data)) : data;
  const [breadcrumbsData, setBreadcrumbsData] = useState([]);
  const { UpdateParameter } = ParameterApi();

  const GetInputMethod = async () => {
    const { data: inputMethods } = await GetAllInputMethod();

    if (inputMethods !== null && inputMethods !== undefined) {
      setAllInputMethodData(inputMethods);
    }
  };

  const GetExistingParameter = async (id) => {
    const inputData = {
      id_parameter: parseInt(id),
    };
    const { data: existingData } = await GetParameterById(inputData);

    if (existingData !== null && existingData !== undefined) {
      const isActiveValue = existingData[0].Is_Active === 1 ? true : false;
      setParameterName(existingData[0].Parameter_Name);
      setIsActive(isActiveValue);
      setExistingInputMethod(existingData[0].Input_Method);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem('module_name');
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
      router.push('/');
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push('/dashboard');
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path),
    );
    if (validateUserAccess.length === 0) {
      router.push('/dashboard');
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ''
    ) {
      router.push('/dashboard');
      return;
    }

    const asPathWithoutQuery = router.asPath.split('?')[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split('/')
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      `Setup ${asPathNestedRoutes[1]}`,
      `Edit ${asPathNestedRoutes[2]}`,
    ];
    setBreadcrumbsData(routerBreadCrumbsData);

    setModuleName(moduleNameValue);

    setUserDepartment(dataLogin.department);

    GetInputMethod();
    GetExistingParameter(idParameter);
  }, []);

  useEffect(() => {
    if (allInputMethodData.length > 0) {
      if (
        existingInputMethod !== '' &&
        existingInputMethod !== null &&
        existingInputMethod !== undefined
      ) {
        const data = allInputMethodData.filter(
          (inputMethod) =>
            inputMethod.Id_Setup === parseInt(existingInputMethod),
        );
        setSelectedInputMethod({ ...data[0] });
      }
    }
  }, [existingInputMethod, allInputMethodData]);

  const selectInputMethodChangeHandler = (value) => {
    setSelectedInputMethod({ ...value });
  };

  //   submitHandler
  const submitHandler = async () => {
    if (
      selectedInputMethod.Id_Setup === null ||
      selectedInputMethod.Id_Setup === undefined ||
      selectedInputMethod.Id_Setup === ''
    ) {
      MySwal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Please fill all required data!',
      });
      return;
    } else {
      MySwal.showLoading();
      const isInstrumentActive = isActive ? 1 : 0;

      const dataInput = {
        id_parameter: parseInt(idParameter),
        parameter_name: parameterName,
        is_active: parseInt(isInstrumentActive),
        input_method: parseInt(selectedInputMethod.Id_Setup),
        department: parseInt(userDepartment),
      };

      const { message, status } = await UpdateParameter(dataInput);

      if (status === 200) {
        setIsSubmitButtonDisabled(true);
        MySwal.fire({
          position: 'center',
          icon: 'success',
          title: message,
          showConfirmButton: false,
          timer: 2500,
        });
        router.push('/user_module/masterdata/Parameter');
      } else {
        MySwal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'Parameter insert FAILED.',
        });
      }
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Add Parameter</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <MainContent>
          <ModuleContentHeader
            breadcrumbs={breadcrumbsData}
            module_name={moduleName}
          />

          <Form layout="horizontal" onSubmit={submitHandler}>
            <Form.Group controlId="instrument_name">
              <Form.ControlLabel>Parameter Name :</Form.ControlLabel>
              <Form.Control
                name="parameter_name"
                type="text"
                value={parameterName}
                onChange={(value) => setParameterName(value)}
                required
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>Cara Input :</Form.ControlLabel>
              <Dropdown
                title={
                  selectedInputMethod.Method_Desc
                    ? selectedInputMethod.Method_Desc
                    : '- Select Input Method -'
                }
                onSelect={selectInputMethodChangeHandler}
              >
                <Dropdown.Item eventKey="">
                  - Select Input Method -
                </Dropdown.Item>
                {allInputMethodData.length > 0 &&
                  allInputMethodData.map((inputMethod) => (
                    <Dropdown.Item
                      key={inputMethod.Id_Setup}
                      eventKey={inputMethod}
                    >
                      {inputMethod.Method_Desc}
                    </Dropdown.Item>
                  ))}
              </Dropdown>
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>Active</Form.ControlLabel>
              <Toggle
                checked={isActive}
                onChange={() => setIsActive((value) => !value)}
              />
            </Form.Group>

            <Form.Group>
              <ButtonToolbar>
                <Button
                  appearance="primary"
                  type="submit"
                  disabled={isSubmitButtonDisabled}
                >
                  Submit
                </Button>
                <Button appearance="default" onClick={() => router.back()}>
                  Cancel
                </Button>
              </ButtonToolbar>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps({ query }) {
  const { idParameter } = query;

  return {
    props: {
      idParameter,
    },
  };
}
