import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  try {
    const response = await axios[method](
      `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-main-categories/${url}`,
      data
    );
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default function TsMainCategories_API() {
  return {
    getAllTsMainCategories: createApiFunction("get", "get-all"),
    getActiveTsMainCategories: createApiFunction("get", "get-active"),
    addTsMainCategories: createApiFunction("post", "add"),
    updateTsMainCategories: createApiFunction("put", "update"),
    sfDeleteTsMainCategories: createApiFunction("put", "s-delete"),
  };
}
