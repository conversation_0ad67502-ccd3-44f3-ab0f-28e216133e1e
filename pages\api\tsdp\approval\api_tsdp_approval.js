import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_REAGEN}/ts/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiApproval() {
  return {
    putStatusApprovalSupervisor: createApiFunction("put", "transaction-h-approval/edit-status-approval-supervisor"),
    putStatusApprovalManager: createApiFunction("put", "transaction-h-approval/edit-status-approval-manager"),
    putStatusRejectedSupervisor: createApiFunction("put", "transaction-h-approval/edit-status-rejected-supervisor"),
    putStatusRejectedManager: createApiFunction("put", "transaction-h-approval/edit-status-rejected-manager"),
  };
}
