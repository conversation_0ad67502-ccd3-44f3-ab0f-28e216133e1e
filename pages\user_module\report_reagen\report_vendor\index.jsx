import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  useToaster,
  SelectPicker,
  DatePicker,
  DateRangePicker,
  Loader,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import { useRouter } from "next/router";
import ApiReportReagen from "@/pages/api/report_reagen/api_report_reagen";

export default function ReportIndirect() {
  const [moduleName, setModuleName] = useState("");
  const { HeaderCell, Cell, Column } = Table;  
  const [vendorData, setVendorData] = useState([]);
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const [props, setProps] = useState([]);
  const toaster = useToaster();
  const router = useRouter();
  const [dateRange, setDateRange] = useState([])
  const [maxVendor,setMaxVendor] = useState(0)
  

  
  const hanldeInitialApi = async () => {
    const res = await ApiReportReagen().getListVendorPrices();
    

    const transformData = res.data.report_vendor_header.map((item) => {
      const newItem = {
        reagen_name: item.reagen_name,
        manufacture_desc: item.manufacture_desc,
      };
    
      if (!item.vendor_detail || item.vendor_detail.length === 0) {
        // If vendor_detail is null or empty, set vendor_1 to "no vendor detail"
        newItem["vendor_1"] = "no vendor detail";
    
        // Fill remaining vendor_x fields with "-"
        for (let i = 2; i <= 10; i++) {
          newItem[`vendor_${i}`] = "-";
        }
      } else {
        // Loop through vendor_detail and construct vendor_x fields
        item.vendor_detail.forEach((vendor, index) => {
          const vendorKey = `vendor_${index + 1}`;
          newItem[vendorKey] = `Rp. ${vendor.price} - ${vendor.vendor_name}${vendor.highlight ? " true" : ""}`;
        });
    
        // Fill remaining vendor_x fields with "-"
        for (let i = item.vendor_detail.length + 1; i <= 10; i++) {
          newItem[`vendor_${i}`] = "-";
        }
      }
    
      return newItem;
    });
    



    // setVendorData(res.data.report_vendor_header ? res.data.report_vendor_header : []);
    console.log(transformData)
    setVendorData(transformData);
    setMaxVendor(res.data.max_vendor ? res.data.max_vendor :0)
  };

  

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

 

  const filteredData = vendorData
    .filter((rowData, i) => {
    const searchFields = [
      "reagen_name",
      "manufacture_desc",
      "vendor_1",
      "vendor_2",
      "vendor_3",
      "vendor_4",
      "vendor_5",
      "vendor_6",
      "vendor_7",
      "vendor_8",
      "vendor_9",
      "vendor_10",
    ];

    const matchesSearch = searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : vendorData.length;


  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("outbound_reagen/list_outbound_reagen")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      hanldeInitialApi();
    }
  }, []);

  const handleExportExcel = () => {
    if (vendorData.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }

   
    
    const headerMapping = {
      room_temperature: "room temperature",
      reagen_name: "reagen name",
      manufacture_desc: "manufacture desc",
      vendor_1: "vendor 1",
      vendor_2: "vendor 2",
      vendor_3: "vendor 3",
      vendor_4: "vendor 4",
      vendor_5: "vendor 5",
      vendor_6: "vendor 6",
      vendor_7: "vendor 7",
      vendor_8: "vendor 8",
      vendor_9: "vendor 9",
      vendor_10: "vendor 10",    
    };

    const formattedData = vendorData.map((item) => {
      const formattedItem = {};
      for (const key in item) {
        if (headerMapping[key]) {
          if (key === "is_active") {
            formattedItem[headerMapping[key]] =
              item[key] === 1 ? "Active" : "Inactive";
          } else if (key === "status") {
            formattedItem[headerMapping[key]] =
              item[key] === 0 ? "Not Realized" : "Realized";
          } else {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
      }
      return formattedItem;
    });

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const filename = `Indirect data${date}.xlsx`;

    XLSX.writeFile(wb, filename);
  };

  const removeTrueSuffix = (str) => {
    const suffix = " true";
    if (str.endsWith(suffix)) {
      return str.slice(0, -suffix.length);
    }
    return str;
  };
  

  return (
    <>
      <div>
        <Head>
          <title>Report Vendor Recap Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>{moduleName}</Breadcrumb.Item>
                  <Breadcrumb.Item>Report Reagen</Breadcrumb.Item>
                  <Breadcrumb.Item active>Report Vendor Recap</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70}  fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={200}  resizable={true}>
                <HeaderCell>Reagen name</HeaderCell>
                <Cell dataKey="reagen_name" />
              </Column>
              <Column width={200}  resizable={true}>
                <HeaderCell>Manufacture Desc</HeaderCell>
                <Cell dataKey="manufacture_desc" />
              </Column>
              
              
              <Column width={200}  resizable={true}>
                <HeaderCell>Vendor 1</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.vendor_1.includes(' true') ? "green" : "",
                      }}
                    >
                      {removeTrueSuffix(rowData.vendor_1)}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={200}  resizable={true}>
                <HeaderCell>Vendor 2</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.vendor_2.includes(' true') ? "red" : "",
                      }}
                    >
                      {removeTrueSuffix(rowData.vendor_2)}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={200}  resizable={true}>
                <HeaderCell>Vendor 3</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.vendor_3.includes(' true') ? "red" : "",
                      }}
                    >
                      {removeTrueSuffix(rowData.vendor_3)}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={200}  resizable={true}>
                <HeaderCell>Vendor 4</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.vendor_4.includes(' true') ? "red" : "",
                      }}
                    >
                      {removeTrueSuffix(rowData.vendor_4)}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={200}  resizable={true}>
                <HeaderCell>Vendor 5</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.vendor_5.includes(' true') ? "red" : "",
                      }}
                    >
                      {removeTrueSuffix(rowData.vendor_5)}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={200}  resizable={true}>
                <HeaderCell>Vendor 6</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.vendor_6.includes(' true') ? "red" : "",
                      }}
                    >
                      {removeTrueSuffix(rowData.vendor_6)}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={200}  resizable={true}>
                <HeaderCell>Vendor 7</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.vendor_7.includes(' true') ? "red" : "",
                      }}
                    >
                      {removeTrueSuffix(rowData.vendor_7)}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={200}  resizable={true}>
                <HeaderCell>Vendor 8</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.vendor_8.includes(' true') ? "red" : "",
                      }}
                    >
                      {removeTrueSuffix(rowData.vendor_8)}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={200}  resizable={true}>
                <HeaderCell>Vendor 9</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.vendor_9.includes(' true') ? "red" : "",
                      }}
                    >
                      {removeTrueSuffix(rowData.vendor_9)}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={200}  resizable={true}>
                <HeaderCell>Vendor 10</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.vendor_10.includes(' true') ? "red" : "",
                      }}
                    >
                      {removeTrueSuffix(rowData.vendor_10)}
                    </span>
                  )}
                </Cell>
              </Column>

            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>
    </>
  );
}
