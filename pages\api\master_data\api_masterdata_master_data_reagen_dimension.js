import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/reagen_dimension/master_reagen_dimension/${url}`,
        data
    )
        .then((res) => {return res.data})
        .catch((err) => {return err.response.data});
    return response;
};

export default function MasterDataMasterDataReagenApi(){
    return{
        getAll: createApiFunction("get", "get/all"),
        add: createApiFunction("post", "create"),
        edit: createApiFunction("put", "update"),
        getById: createApiFunction("get", "get/id"),
        editStatus: createApiFunction("put", "edit/status"),
        getAllActive: createApiFunction("get", "get/all/active"),
    }
}