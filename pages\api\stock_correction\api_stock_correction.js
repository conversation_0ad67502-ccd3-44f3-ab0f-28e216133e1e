import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/reagen/stock_correction/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};

export default function StockCorrectionApi(){
    return {
        getAll: createApiFunction("get", "get/all"),
        create: createApiFunction("post", "create"),
        getById: createApiFunction("get", "get/id"),
        editStatus: createApiFunction("put", "edit/status"),
        getActiveBySupervisor: createApiFunction("get", "get/active/supervisor"),
        editApprovalBySupervisor: createApiFunction("put", "approval/supervisor"),
        getActiveByManager: createApiFunction("get", "get/active/manager"),
        editApprovalByManager: createApiFunction("put", "approval/manager"),
        getApprovalDetailRequest: createApiFunction("post", "get/approval/detail/request"),
        getApprovalDetailCount: createApiFunction("post", "get/approval/detail/count"),
        getApprovalDetailRecovery: createApiFunction("post", "get/approval/detail/recovery"),
        getUsed: createApiFunction("post", "get/used"),
        getUsedByRack: createApiFunction("post", "get/used/rack"),
        getName: createApiFunction("get", "get/name"),
        getRack: createApiFunction("get", "get/rack"),
        createStaging: createApiFunction("post", "create/staging"),
        editStatusStaging: createApiFunction("put", "edit/status/staging"),
        editStatusStagingByLocator: createApiFunction("put", "edit/status/staging/locator"),
        editStatusLocator: createApiFunction("put", "edit/status/locator"),
        submitCorrection: createApiFunction("post","submit/correction")
    }
}