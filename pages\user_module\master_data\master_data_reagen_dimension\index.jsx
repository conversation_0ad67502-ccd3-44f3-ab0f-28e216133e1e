import { useEffect, useState } from "react";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Icon<PERSON>utton,
  Stack,
  Breadcrumb,
  Tag,
  Panel,
  Table,
  InputGroup,
  Input,
  Pagination,
  Button,
  Form,
  useToaster,
  Modal,
  ButtonGroup
} from "rsuite";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import API_MasterDataMasterDataReagenDimension from "@/pages/api/master_data/api_masterdata_master_data_reagen_dimension";
import { useRouter } from "next/router";

export default function MasterDataMasterDataReagenDimensionPage() {
  const [moduleName, setModuleName] = useState("");
  const [reagenDataDimension, setReagenDataDimension] = useState([]);
  const [props, setProps] = useState([]);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [sortColumn, setSortColumn] = useState();
  const [sortDimension, setSortDimension] = useState();

  const { HeaderCell, Cell, Column } = Table;

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const [formErrors, setFormErrors] = useState({});

  const toaster = useToaster();
  const router = useRouter();

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortDimension) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortDimension(sortDimension);
    }, 500);
  };

  const handleSubmit = (apiFunction) => {
    if (!formValue.dimension_desc) {
      setFormErrors({ dimension_desc: "Reagen Dimension Description is required." });
      return;
    }
    setFormErrors({});
    apiFunction();
  };

  const handleGetAllApi = async () => {
    try {
      const reagenDataDimension =
        await API_MasterDataMasterDataReagenDimension().getAll();
      console.log(reagenDataDimension);

      setReagenDataDimension(reagenDataDimension.data || []);
    } catch (error) {
      setReagenDataDimension([]);
    }
  };

  const emptyFormValue = {
    dimension_desc: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const handleEditDimension = async (selectedIdReagenDimension) => {
    try {
      const result = await API_MasterDataMasterDataReagenDimension().edit({
        ...formValue,
        id_dimension: selectedIdReagenDimension,
        updated_by: props.employee_id,
        updated_name: props.employee_id + "-" + props.employee_name,
      });

      if (result.status === 200) {
        handleGetAllApi();
        setShowEditModal(false);

        toaster.push(Messages("success", "Success editing Reagen Data Dimension!"), {
          placement: "topCenter",
          duration: 5000,
        });
        setFormValue(emptyFormValue);
      } else if (result.status === 400) {
        toaster.push(
          Messages(
            "error",
            `Error: "${result.message}". Please try again later!`
          ),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
      }
    } catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const filteredData = reagenDataDimension
    .filter((rowData, i) => {
      const searchFields = [
        "id_dimension",
        "dimension_desc",
        "created_dt",
        "created_by",
        "updated_dt",
        "updated_by",
        "deleted_dt",
        "deleted_by",
        "is_active",
      ];

      const matchesSearch = searchFields.some((field) =>
        rowData[field]
          ?.toString()
          .toLowerCase()
          .includes(searchKeyword.toLowerCase())
      );

      return matchesSearch;
    })
    
    const getPaginatedData = (filteredData, limit, page) => {
      const start = limit * (page - 1);
      const end = start + limit;
      return filteredData.slice(start, end);
  }

  const totalRowCount = searchKeyword
    ? filteredData.length
    : reagenDataDimension.length;
  const handleExportExcel = () => {
    try {
      const headerMapping = {
        id_dimension: "ID",
        dimension_desc: "Dimension Description",
        created_dt: "Created Date",
        created_by: "Created By",
        updated_dt: "Updated Date",
        updated_by: "Updated By",
        deleted_dt: "Deleted Date",
        deleted_by: "Deleted By",
        is_active: "Is Active",
      };

      const formattedData = reagenDataDimension.map((item) => {
        const formattedItem = {};
        for (const key in item) {
          if (headerMapping[key]) {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
        return formattedItem;
      });

      const ws = XLSX.utils.json_to_sheet(formattedData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Reagen Data");

      const date = dateFormatterDash(new Date());
      XLSX.writeFile(wb, `ReagenDataDimension ${date}.xlsx`);

      toaster.push(
        Messages("success", "Reagen data file (.xlsx) downloaded successfully"),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    } catch (error) {
      console.error("Error exporting Excel:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const getData = () => {
    if (sortColumn && sortDimension) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortDimension === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const handleAddReagen = async () => {
    try {
      const result = await API_MasterDataMasterDataReagenDimension().add({
        ...formValue,
        created_by: props.employee_id,
        created_name: props.employee_id + "-" + props.employee_name,
      });

      if (result.status === 200) {
        handleGetAllApi();
        setShowAddModal(false);

        toaster.push(
          Messages("success", "Success adding Master Data Reagen!"),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
      } else if (result.status === 400) {
        toaster.push(
          Messages(
            "error",
            `Error: "${result.message}". Please try again later!`
          ),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
      }
    } catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const [deleteFormValue, setDeleteFormValue] = useState({
    id_dimension: null,
    deleted_by: null,
    reason: "",
    dimension_desc:null,
  });

  const handleEditStatusReagen = async (selectedIdReagenDimension) => {
    console.log("selected id", selectedIdReagenDimension);
    await API_MasterDataMasterDataReagenDimension().editStatus({
      id_dimension: selectedIdReagenDimension,
      deleted_by: props.employee_id,
      reason: deleteFormValue.reason
    });
    handleGetAllApi();
    setShowDeleteModal(false);
    setDeleteFormValue({})
  };

  useEffect(() => {
    handleGetAllApi();

    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("master_data/master_data_reagen_category")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetAllApi();
    }
  }, []);

  const handleCategoryClick = () => {
    router.push("/user_module/master_data/master_data_reagen_category")
  }

  const handleDimensionClick = () => {
    router.push("/user_module/master_data/master_data_reagen_dimension")
  }

  const handleTypeClick = () => {
    router.push("/user_module/master_data/master_data_reagen_type")
  }

  const handleCriteriaClick = () => {
    router.push("/user_module/master_data/master_data_reagen_criteria")
  }

  return (
    <>
      <div>
        <Head>
          <title>Master Data Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>Reagen Dimension</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Reagen Dimension</h5>
                <Stack className="flex gap-2">
                  <ButtonGroup>
                    <Button
                      onClick={handleCategoryClick}
                      appearance="primary">
                      Reagen Category
                    </Button>
                    <Button
                      onClick={handleDimensionClick}
                      appearance="primary"
                      active>
                      Reagen Dimension
                    </Button>
                    <Button
                      appearance="primary"
                      onClick={handleTypeClick}>
                      Reagen Type
                    </Button>
                    <Button
                      appearance="primary"
                      onClick={handleCriteriaClick}>
                      Reagen Criteria
                    </Button>
                  </ButtonGroup>
                </Stack>
              </Stack>}>
          </Panel>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusRoundIcon />}
                    appearance="primary"
                    onClick={() => {
                      setShowAddModal(true);
                    }}
                  >
                    Add
                  </IconButton>

                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getData(), limit, page)}
              sortColumn={sortColumn}
              sortDimension={sortDimension}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id_dimension" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Dimension Description</HeaderCell>
                <Cell dataKey="dimension_desc" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Created Date</HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Created By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.created_by} - {rowData.created_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Updated Date</HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Updated By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.updated_by} - {rowData.updated_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Deleted Date</HeaderCell>
                <Cell dataKey="deleted_dt" />
              </Column>
              <Column width={350} align="center" sortable resizable>
                <HeaderCell>Deleted By - Name</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <>
                      {rowData.deleted_by} - {rowData.deleted_name}
                    </>
                  )}
                </Cell>
              </Column>
              <Column width={100} sortable resizable>
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={100} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell style={{ padding: "8px" }}>
                  {(rowData) => (
                    <div>
                      <Button
                        appearance="link"
                        disabled={rowData.is_active === 0}
                        onClick={() => {
                          setShowEditModal(true);
                          setFormValue(rowData);
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        appearance="subtle"
                        onClick={() => 
                        {  
                          if (rowData.is_active === 1){
                            setShowDeleteModal(true)
                            setDeleteFormValue({
                              ...deleteFormValue,
                              id_dimension: rowData.id_dimension,
                              deleted_by: props.employee_id,
                              reason:"",
                              dimension_desc: rowData.dimension_desc
                            })
                          } else{
                            handleEditStatusReagen(rowData.id_dimension)
                          }
                        }                                                                          
                        }
                      >
                        {rowData.is_active === 1 ? (
                          <TrashIcon style={{ fontSize: "16px" }} />
                        ) : (
                          <ReloadIcon style={{ fontSize: "16px" }} />
                        )}
                      </Button>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Add Modal Add Reagen Data Dimension*/}
        <Modal
          backdrop="static"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Add Master Reagen Data</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Dimension Description</Form.ControlLabel>
                <Form.Control
                  name="dimension_desc"
                  value={formValue.dimension_desc}
                  onChange={(value) => {
                    setFormValue((prevFormValue) => ({
                      ...prevFormValue,
                      dimension_desc: value,
                    }));
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      dimension_desc: undefined,
                    }));
                  }}
                  errorMessage={formErrors.dimension_desc}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                // Clear form errors immediately
                setFormErrors({});

                // Hide modal and reset form values
                setShowAddModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>

            <Button
              onClick={() => {
                handleSubmit(handleAddReagen);
              }}
              appearance="primary"
              dimension="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        {/* EDIT REAGEN DIMENSION */}
        <Modal
          backdrop="static"
          open={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Edit Reagen Dimension</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Dimension Description</Form.ControlLabel>
                <Form.Control
                  name="dimension_desc"
                  value={formValue.dimension_desc}
                  onChange={(value) => {
                    setFormValue({ ...formValue, dimension_desc: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      dimension_desc: undefined,
                    }));
                  }}
                  errorMessage={formErrors.dimension_desc}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setFormErrors({});
                setShowEditModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleSubmit(() => handleEditDimension(formValue.id_dimension));
              }}
              appearance="primary"
              dimension="submit"
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>

         {/* Delete Modal Dimension*/}
         <Modal
          backdrop="static"
          open={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setDeleteFormValue({});
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Delete Dimension {deleteFormValue.dimension_desc}</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Reason</Form.ControlLabel>
                <Form.Control
                  name="reason_desc"
                  value={deleteFormValue.reason}
                  onChange={(value) => {
                    setDeleteFormValue((prevFormValue) => ({
                      ...prevFormValue,
                      reason: value,
                    }));
                  }}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                // Clear form errors immediately
                setDeleteFormValue({});

                // Hide modal and reset form values
                setShowDeleteModal(false);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>

            <Button
              onClick={() => {
                // handleSubmit(handleAddReagen);
                handleEditStatusReagen(deleteFormValue.id_dimension);
              }}
              appearance="primary"
              dimension="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
