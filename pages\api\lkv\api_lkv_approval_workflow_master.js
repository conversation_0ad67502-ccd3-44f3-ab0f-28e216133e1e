import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/lkv/master/approval_workflow_master${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function LkvApprovalWorkflowMaster(){
    return{
        getAllApprovalWorkflowMaster: createApiFunction("get", "/get/all"),
        getAllApprovalWorkflowMasterActive : createApiFunction("get", "/get/all/active"),
    }
}