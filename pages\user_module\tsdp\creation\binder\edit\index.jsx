import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  InputGroup,
  Panel,
  Stack,
  Button,
  Form,
  useToaster,
  DatePicker,
  Uploader,
  Row,
  Col,
  RadioGroup,
  Radio,
  SelectPicker,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";

import ApiTsdpTDBinder from "@/pages/api/tsdp/api_tsdp_td_binder";

import { useRouter } from "next/router";
import Messages from "@/components/Messages";
import TrashIcon from "@rsuite/icons/Trash";
import ApiTsdpTH from "@/pages/api/tsdp/transaction_h/api_tsdp_transaction_h";
import ApiTsdpTDAttachments from "@/pages/api/tsdp/api_tsdp_td_attachments";
import ApiTsDetail from "@/pages/api/ts/api_ts_detail";

export default function EditBinder() {
  const router = useRouter();
  const toaster = useToaster();
  const [headerData, setHeaderData] = useState([]);
  const [formAttachments, setFormAttachments] = useState([]);
  const [idDetailTrans, setIdDetailTrans] = useState(0);
  const [sessionAuth, setSessionAuth] = useState(null);
  const [moduleName, setModuleName] = useState("");
  const [currentState, setCurrentState] = useState("Header");
  const [errorsBinderForm, setErrorsBinderForm] = useState({});

  // // Modal states
  // const emptyFormValue = {
  //   binder_date: "",
  //   binder_mix_amount: "",
  //   binder_mix_time: "",
  //   binder_remarks: "",
  //   updated_date: "",
  //   updated_by: "",
  //   id_detail_trans: "",
  // };

  const [binderForm, setBinderForm] = useState({});
  const [dataSpv, setDataSpv] = useState([]);

  const fetchDropdownData = async () => {
    try {
      const resSpv = await ApiTsDetail().getAllActiveSpv();
      setDataSpv(resSpv.data || []);
    } catch (error) {
      console.error("Error fetching dropdown data:", error);
    }
  };

  useEffect(() => {
    console.log("data spv = ", dataSpv);
  }, [dataSpv]);

  useEffect(() => {
    console.log("binderform = ", binderForm);
  }, [binderForm]);

  const fetchBinderData = async (binderId) => {
    try {
      const res = await ApiTsdpTDBinder().getTsTransactionBinderById({
        id_detail_trans: parseInt(binderId),
      });

      if (res.status === 200) {
        const fetchedData = res.data;
        setBinderForm(fetchedData);
        fetchTsdpHeaderData(fetchedData.id_header_trans);
        fetchAttachmentDataByID(fetchedData.id_header_trans);
      } else {
        console.error("Error fetching binder data: ", res.message);
      }
    } catch (error) {
      console.log("Error fetching binder data : ", error);
    }
  };

  const fetchAttachmentDataByID = async (binderId) => {
    try {
      const res = await ApiTsdpTDAttachments().getTsTransactionById({
        id_header_trans: parseInt(binderId),
        type: "Binder",
      });

      if (res.status === 200) {
        setFormAttachments(res.data);
      } else {
        console.error("Error fetching binder data: ", res.message);
      }
    } catch (error) {
      console.log("Error fetching binder data : ", error);
    }
  };

  const fetchTsdpHeaderData = async (idHeader) => {
    try {
      const res = await ApiTsdpTH().getTsTransHeaderById({
        id_header_trans: parseInt(idHeader),
      });

      if (res.status === 200) {
        setHeaderData(res.data);
        console.log("Binder data fetched succesfully", res.data);
      } else {
        console.error("Error fetching header data: ", res.message);
      }
    } catch (error) {
      console.log("Error fetching header data : ", error);
    }
  };

  const handleEditBinderData = async () => {
    try {
      const errors = {};
      const requiredFields = [
        "id_detail_trans",
        "binder_date",
        "binder_mix_amount",
        "binder_mix_time",
        "binder_remarks",
        "spv_employee_id",
      ];

      for (const field of requiredFields) {
        if (
          binderForm[field] === null ||
          (typeof binderForm[field] === "string" &&
            binderForm[field].trim() === "")
        ) {
          errors[field] = `Kolom ${field.replace(/_/g, " ")} harus diisi!`;
        }
      }

      console.log("error edit form form =", errorsBinderForm);

      if (Object.keys(errors).length > 0) {
        setErrorsBinderForm(errors);
        toaster.push(Messages("error", "Mohon isi semua kolom!"), {
          placement: "topCenter",
          duration: 2000,
        });
        return;
      }

      const requestPayload = {
        id_detail_trans: parseInt(idDetailTrans),
        binder_date: binderForm.binder_date,
        binder_mix_amount: parseFloat(binderForm.binder_mix_amount),
        binder_mix_time: parseFloat(binderForm.binder_mix_time),
        binder_remarks: binderForm.binder_remarks,
        spv_employee_id: binderForm.spv_employee_id,
        updated_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
        revised_remarks: binderForm.revised_remarks,
      };

      const res = await ApiTsdpTDBinder().updateTsTransactionBinder(
        requestPayload
      );

      if (res.status === 200) {
        // let failedUploads = 0;
        let failedUploads = 0;
        let failedStatusUpdates = 0;

        // --- UPDATE STATUS OF EXISTING ATTACHMENTS ---

        const originalAttachments = await ApiTsdpTDAttachments()
          .getTsTransactionById({
            id_detail_trans: parseInt(binderForm.id_detail_trans),
          })
          .then((res) => res.data || []);

        for (const updatedAttachment of formAttachments) {
          // Find the original state of this attachment
          const originalAttachment = originalAttachments.find(
            (orig) => orig.id_attachments === updatedAttachment.id_attachments
          );

          // if the updatedAttachment's status changed
          if (updatedAttachment.is_active === 0) {
            const payload = {
              deleted_by: `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`,
              id_attachments: updatedAttachment.id_attachments,
            };

            const statusRes = await ApiTsdpTDAttachments().softDeleteAttachment(
              payload
            );

            if (statusRes.status !== 200) {
              failedStatusUpdates++;
            }
          }
        }
        const newAttachments = formAttachments.filter(
          (file) => file.data !== undefined
        );
        console.log(
          `Found ${newAttachments.length} new attachments to upload.`
        );
        for (const fileItem of newAttachments) {
          console.log("masuk sini");
          const formData = new FormData();
          formData.append("Files", fileItem.data.blobFile, fileItem.data.name);
          formData.append("id_header_trans", headerData.id_header_trans);
          formData.append("type", "Binder");
          formData.append("path", "tsdp");
          formData.append("created_by", sessionAuth.employee_id);

          const postRes = await ApiTsdpTDAttachments().postTsTransactionUpload(
            formData
          );

          if (postRes.status !== 200) {
            failedUploads++;
          }
        }

        if (failedUploads > 0) {
          toaster.push(
            Messages("error", `${failedUploads} attachments failed to upload.`),
            { duration: 5000 }
          );
        } else {
          toaster.push(
            Messages(
              "success",
              "Binder data and attachments saved successfully!"
            ),
            { duration: 3000 }
          );
          router.push(`/user_module/tsdp/creation/binder/list`);
        }
      } else {
        toaster.push(Messages("error", "error edit binder data"), {
          placement: "topCenter",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("Error updating binder data: ", error);
      toaster.push(Messages("error", "Error updating binder data"), {
        placement: "topCenter",
        duration: 2000,
      });
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push("/");
    }
  }, []);

  useEffect(() => {
    console.log("header data = ", headerData);
  }, [headerData]);

  useEffect(() => {
    if (router.isReady) {
      console.log("router is ready", router.query);

      const binderDataId = router.query.data;

      if (binderDataId) {
        fetchBinderData(binderDataId);
        // fetchAttachmentDataByID(binderDataId);
        setIdDetailTrans(binderDataId);
      }
      fetchDropdownData();
    }
  }, [router.isReady, router.query]);

  const handleUploadAttachments = (file) => {
    setFormAttachments((prevAttachments) => [
      ...prevAttachments,
      {
        type: "Binder",
        data: file,
      },
    ]);
  };

  const handleRemoveAttachments = (fileKey) => {
    setFormAttachments((prevAttachments) =>
      prevAttachments.filter(
        (attachment) => attachment?.data?.fileKey !== fileKey
      )
    );
  };

  const handleActivateDeactivate = (idAttachment, isActive) => {
    setFormAttachments((prevAttachments) =>
      prevAttachments.map((attachment) => {
        if (attachment.id_attachments === idAttachment) {
          return { ...attachment, is_active: isActive ? 0 : 1 };
        }
        return attachment;
      })
    );
  };

  const getAttachmentsForDisplay = () => {
    const attachments = formAttachments || [];

    // Helper function to format bytes into KB or MB
    const formatFileSize = (bytes) => {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    return attachments.map((attachment) => {
      // For existing files from the database
      if (attachment.id_attachments) {
        const fileName = attachment.path.substring(
          attachment.path.lastIndexOf("/") + 1
        );
        return {
          name: fileName,
          url: `${process.env.NEXT_PUBLIC_STORAGE}/${attachment.path}`,
          id_attachments: attachment.id_attachments,
          is_active: attachment.is_active,
          size: null,
        };
      } else {
        return {
          name: attachment.data.name,
          url: URL.createObjectURL(attachment.data.blobFile),
          flag: "new",
          fileKey: attachment.data.fileKey,
          size: formatFileSize(attachment.data.blobFile.size),
        };
      }
    });
  };

  const attachmentsToDisplay = getAttachmentsForDisplay();

  const AttachmentDisplay = ({ attachment }) => (
    <Panel
      bordered
      className="mt-3 flex flex-col"
      style={{ width: 230, height: 350, position: "relative" }}
      shaded>
      <span style={{ flexGrow: 1 }}>
        <img
          src={attachment.url}
          alt={attachment.name}
          style={{
            maxWidth: 200,
            maxHeight: 250,
            objectFit: "contain",
            objectPosition: "center",
            display: "block",
            margin: "0 auto",
          }}
        />
        <p
          className="word-break mt-1"
          style={{ maxHeight: 20, overflow: "hidden" }}>
          File Name : {attachment.name}
        </p>
        {attachment.size && (
          <p style={{ color: "grey", fontSize: "12px", marginTop: "4px" }}>
            File Size : {attachment.size}
          </p>
        )}
      </span>
      {attachment.flag === "new" ? (
        <div style={{ position: "absolute", bottom: 10, right: 10 }}>
          <Button
            appearance="ghost"
            color="red"
            onClick={() => handleRemoveAttachments(attachment.fileKey)}>
            <TrashIcon />
          </Button>
        </div>
      ) : (
        <div style={{ position: "absolute", bottom: 10, left: 10, right: 10 }}>
          <Button
            appearance="ghost"
            size="sm"
            color={attachment.is_active === 0 ? "green" : "red"}
            onClick={() =>
              handleActivateDeactivate(
                attachment.id_attachments,
                attachment.is_active
              )
            }
            block>
            {attachment.is_active === 0 ? "Activate" : "Deactivate"}
          </Button>
        </div>
      )}
    </Panel>
  );

  return (
    <div>
      <Head>
        <title>TSDP Creation Binder Edit</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item>TSDP</Breadcrumb.Item>
            <Breadcrumb.Item>Creation</Breadcrumb.Item>
            <Breadcrumb.Item>Binder</Breadcrumb.Item>
            <Breadcrumb.Item active>Edit</Breadcrumb.Item>
          </Breadcrumb>

          <Panel bordered className="mb-2 mt-4">
            <Stack justifyContent="flex-start">
              <h3>TS Edit Binder</h3>
            </Stack>
            <hr className="my-4" />
            <hr className="my-4" />

            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Kode Batch</Form.ControlLabel>
                <Form.Control
                  name="batch_no"
                  value={headerData.batch_no || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Line Type</Form.ControlLabel>
                <RadioGroup
                  name="line_type"
                  value={headerData.line_type}
                  readOnly
                  disabled>
                  <Radio value={1}>Automate</Radio>
                  <Radio value={0}>Manual</Radio>
                </RadioGroup>
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Line</Form.ControlLabel>
                <Form.Control
                  name="id_line"
                  value={headerData.id_line || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Jenis Sediaan</Form.ControlLabel>
                <Form.Control
                  name="sediaan_type"
                  value={headerData.sediaan_type || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                <Form.Control
                  name="product_code"
                  value={headerData.product_code || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Nama Produk</Form.ControlLabel>
                <Form.Control
                  name="product_name"
                  value={headerData.product_name || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Skala Produksi</Form.ControlLabel>
                <Form.Control
                  name="production_scale"
                  value={headerData.production_scale || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Fokus Trial</Form.ControlLabel>
                <Form.Control
                  name="trial_focus"
                  value={headerData.trial_focus || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>No PPI</Form.ControlLabel>
                <Form.Control
                  name="ppi_no"
                  value={headerData.ppi_no || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Tujuan Proses</Form.ControlLabel>
                <Form.Control
                  name="process_purpose"
                  value={headerData.process_purpose || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Background</Form.ControlLabel>
                <Form.Control
                  name="background"
                  value={headerData.background || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Tanggal Proses</Form.ControlLabel>
                <Form.Control
                  name="process_date"
                  value={headerData.process_date?.split("T")[0] || "-"}
                  readOnly
                  disabled
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Tanggal Binder <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <DatePicker
                  block
                  oneTap
                  value={
                    binderForm.binder_date
                      ? new Date(binderForm.binder_date)
                      : null
                  }
                  format="dd-MM-yyyy"
                  onChange={(value) => {
                    setBinderForm((prevFormValue) => ({
                      ...prevFormValue,
                      binder_date: value,
                    }));
                    setErrorsBinderForm((prevErrors) => ({
                      ...prevErrors,
                      binder_date: undefined,
                    }));
                  }}
                  name="binder_date"
                />
                {errorsBinderForm.binder_date && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsBinderForm.binder_date}
                  </div>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Jumlah Pelarut <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="binder_mix_amount"
                    placeholder="binder_mix_amount"
                    value={binderForm.binder_mix_amount}
                    onChange={(value) => {
                      setBinderForm((prevFormValue) => ({
                        ...prevFormValue,
                        binder_mix_amount: value,
                      }));
                      setErrorsBinderForm((prevErrors) => ({
                        ...prevErrors,
                        binder_mix_amount: undefined,
                      }));
                    }}
                    type="number"
                  />
                </InputGroup>
                {errorsBinderForm.binder_mix_amount && (
                  <p className="absolute text-red-500">
                    {errorsBinderForm.binder_mix_amount}
                  </p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Waktu Aduk <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="binder_mix_time"
                    placeholder="binder_mix_time"
                    value={binderForm.binder_mix_time}
                    onChange={(value) => {
                      setBinderForm((prevFormValue) => ({
                        ...prevFormValue,
                        binder_mix_time: value,
                      }));
                      setErrorsBinderForm((prevErrors) => ({
                        ...prevErrors,
                        binder_mix_time: undefined,
                      }));
                    }}
                    type="number"
                  />
                </InputGroup>
                {errorsBinderForm.binder_mix_time && (
                  <p className="absolute text-red-500">
                    {errorsBinderForm.binder_mix_time}
                  </p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>
                  Binder Remarks <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <InputGroup>
                  <Form.Control
                    name="binder_remarks"
                    placeholder="binder_remarks"
                    value={binderForm.binder_remarks}
                    onChange={(value) => {
                      setBinderForm((prevFormValue) => ({
                        ...prevFormValue,
                        binder_remarks: value,
                      }));
                      setErrorsBinderForm((prevErrors) => ({
                        ...prevErrors,
                        binder_remarks: undefined,
                      }));
                    }}
                    type="text"
                  />
                </InputGroup>
                {errorsBinderForm.binder_remarks && (
                  <p className="absolute text-red-500">
                    {errorsBinderForm.binder_remarks}
                  </p>
                )}
              </Form.Group>
              {binderForm.status_approval === 0 && (
                <Form.Group>
                  <Form.ControlLabel>Revised Remarks</Form.ControlLabel>
                  <InputGroup
                    style={{
                      borderColor: "red",
                    }}>
                    <Form.Control
                      readOnly
                      name="revised_remarks"
                      placeholder="revised_remarks"
                      value={binderForm.revised_remarks || "-"}
                      onChange={(value) => {
                        setBinderForm((prevFormValue) => ({
                          ...prevFormValue,
                          revised_remarks: value,
                        }));
                        setErrorsBinderForm((prevErrors) => ({
                          ...prevErrors,
                          revised_remarks: undefined,
                        }));
                      }}
                      type="text"
                    />
                  </InputGroup>
                </Form.Group>
              )}
              <Form.Group>
                <Form.ControlLabel>Lampiran</Form.ControlLabel>
                <Uploader
                  listType="picture-text"
                  action=""
                  onUpload={handleUploadAttachments}
                  removable={false}
                  fileListVisible={false}>
                  <Button appearance="ghost">Ambil Gambar</Button>
                </Uploader>
                <Row>
                  {attachmentsToDisplay.map((attachment, index) => (
                    <Col md={5} sm={12} key={index} className="mr-5">
                      <AttachmentDisplay attachment={attachment} />
                    </Col>
                  ))}
                </Row>
              </Form.Group>
              {/* <Form.Group>
                <Form.ControlLabel>
                  Spv
                  <span className="text-red-500"> *</span>
                </Form.ControlLabel>
                <Form.Control
                  name="spv_employee_id"
                  value={spvName || "-"}
                  readOnly
                />
              </Form.Group> */}
              <Form.Group>
                <Form.ControlLabel>
                  Spv <span className="text-red-500">*</span>
                </Form.ControlLabel>
                <Form.Control
                  name="spv_employee_id"
                  accepter={SelectPicker}
                  value={binderForm.spv_employee_id}
                  data={dataSpv}
                  valueKey="employee_id"
                  labelKey="employee_name"
                  block
                  onChange={(value) => {
                    setBinderForm({ ...binderForm, spv_employee_id: value });
                    setErrorsBinderForm((prevErrors) => ({
                      ...prevErrors,
                      spv_employee_id: undefined,
                    }));
                  }}
                />
                {errorsBinderForm.spv_employee_id && (
                  <div className="text-red-500 text-sm mt-1">
                    {errorsBinderForm.spv_employee_id}
                  </div>
                )}
              </Form.Group>
            </Form>

            <Button
              appearance="primary"
              style={{ backgroundColor: "#1fd306", marginTop: "20px" }}
              onClick={handleEditBinderData}>
              Save
            </Button>
          </Panel>
        </div>
      </ContainerLayout>
    </div>
  );
}
