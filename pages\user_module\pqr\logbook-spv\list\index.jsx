import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, useToaster, Notification, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, Loader, RadioGroup, Radio, SelectPicker, Grid, Row, Col } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiSupervisorApproval from "@/pages/api/pqr/supervisor/api_supervisor";
import ApiLogbook from "@/pages/api/pqr/logbook/api_logbook";

export default function SupervisorListLogbook() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_trans_header");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const toaster = useToaster();
  const [loading, setLoading] = useState(false);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [transactionHeadersDataState, setTransactionHeadersDataState] = useState([]);
  const [remarks, setRemarks] = useState("");
  const [remarksError, setRemarksError] = useState(false);
  const [showRemarksModal, setShowRemarksModal] = useState(false);

  const [showTransactionDetailModal, setShowTransactionDetailModal] = useState(false);
  const [TransactionDetailDataState, setTransactionDetailDataState] = useState([]);
  const [selectedTransactionId, setSelectedTransactionId] = useState(null);
  const [password, setPassword] = useState("");

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = transactionHeadersDataState.filter((rowData, i) => {
    const searchFields = ["id_trans_header", "ppi_name", "batch_code", "iot_desc", "line_desc", "remarks", "wetmill", "status_transaction", "create_date", "create_by", "update_date", "update_by", "delete_date", "delete_by"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : transactionHeadersDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("pqr/logbook-spv"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandlegetAllNeedApproveLogbookApi();
    }
  }, []);

  const HandlegetAllNeedApproveLogbookApi = async () => {
    try {
      const res = await ApiLogbook().getAllLogbookNeedApprove();

      console.log("res", res);
      if (res.status === 200) {
        setTransactionHeadersDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };

  const HandleEditStatusApprovalApi = async (id_trans_header, status_approval, password) => {
    setLoading(true);
    try {
      const finalRemarks = status_approval === 1 ? "" : remarks;

      const res = await ApiSupervisorApproval().editApprovalStatus({
        id_trans_header,
        status_approval,
        revise_remarks: finalRemarks,
        approval_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
        employee_id: sessionAuth.employee_id,
        password: password,
      });

      if (res.status === 200) {
        showNotification("success", "Approval status updated successfully.");
        HandlegetAllNeedApproveTransactionHeaderApi();
        setShowTransactionDetailModal(false);
        setShowRemarksModal(false);
        setRemarks("");
        setPassword("");
      } else {
        console.error("Error on update status: ", res.message);
        if (res.message === "wrong username or password") {
          showNotification("error", "Wrong username or password. Please try again.");
          setShowTransactionDetailModal(true);
        } else {
          setRemarks("");
          showNotification("error", `Error updating approval status: ${res.message}`);
        }
      }
    } catch (error) {
      console.error("Error on update status: ", error.message);
      showNotification("error", `Error updating approval status: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const HandleGetTransactionHeaderDetail = async (id_trans_header) => {
    try {
      const res = await ApiTransactionHeader().getPPITransactionHeaderById({
        id_trans_header: id_trans_header,
      });

      if (res.status === 200) {
        console.log("Data received: ", res.data);
        setTransactionDetailDataState([res.data]);
      } else {
        console.log("Error on update status: ", res.message);
      }
    } catch (error) {
      console.log("Error on update status: ", error.message);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Logbook Supervisor Approval List</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>Logbook Supervisor</Breadcrumb.Item>
                  <Breadcrumb.Item active>list</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Supervisor Approval Logbook List</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    {/* <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        // setShowAddModal(true);
                        router.push(`/user_module/pqr/creation/list/addTransactionHeader`);
                      }}
                    >
                      Tambah
                    </IconButton> */}
                  </div>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="cari" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            ></Panel>
            <Modal
              backdrop="static"
              open={showRemarksModal}
              onClose={() => {
                setShowRemarksModal(false);
                setRemarks("");
                setPassword("");
              }}
              overflow={false}
              size="sm"
            >
              <Modal.Header>
                <Modal.Title>Isi Catatan Revisi</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <div className="form-group">
                  <label htmlFor="remarks"></label>
                  <textarea
                    id="remarks"
                    className="form-control"
                    placeholder="Masukkan Catatan revisi"
                    value={remarks}
                    onChange={(e) => {
                      setRemarks(e.target.value);
                      setRemarksError(false);
                    }}
                    style={{ width: "100%", minHeight: "100px" }}
                  />
                  {remarksError && <p style={{ color: "red" }}>Catatan wajib diisi.</p>}
                </div>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowRemarksModal(false);
                    setRemarks("");
                    setPassword("");
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  appearance="primary"
                  onClick={async () => {
                    if (!remarks.trim()) {
                      setRemarksError(true);
                      return;
                    }
                    if (!password.trim()) {
                      showNotification("error", "Password wajib diisi");
                      return;
                    }
                    setLoading(true);
                    await HandleEditStatusApprovalApi(selectedTransactionId, 0, password);
                    setLoading(false);
                    setShowRemarksModal(false);
                    setPassword("");
                  }}
                  loading={loading}
                  disabled={loading}
                >
                  Submit
                </Button>
              </Modal.Footer>
            </Modal>

            <Modal
              backdrop="static"
              open={showTransactionDetailModal}
              onClose={() => {
                setShowTransactionDetailModal(false);
                setPassword("");
                setRemarks("");
                setTransactionDetailDataState([]);
              }}
              overflow={false}
              size={"lg"}
            >
              <Modal.Header>
                <Modal.Title>Rincian Data Transaction</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Panel bordered bodyFill className="mb-3">
                  <Form layout="vertical">
                    <Grid fluid>
                      <Row style={{ marginBottom: "16px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>ID Transaksi Header</Form.ControlLabel>
                            <Form.Control name="id_trans_header" value={TransactionDetailDataState[0]?.id_trans_header || ""} style={{ width: "100%" }} readOnly />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                            <Form.Control name="ppi_name" value={TransactionDetailDataState[0]?.ppi_name || ""} style={{ width: "100%" }} readOnly />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Kode Batch</Form.ControlLabel>
                            <Form.Control name="batch_code" value={TransactionDetailDataState[0]?.batch_code || ""} style={{ width: "100%" }} readOnly />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Wetmill</Form.ControlLabel>
                            <Form.Control name="wetmill" value={TransactionDetailDataState[0]?.wetmill === "Y" ? "Yes" : "No"} style={{ width: "100%" }} readOnly />
                          </Form.Group>
                        </Col>
                      </Row>

                      <Row style={{ marginBottom: "16px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Catatan</Form.ControlLabel>
                            <Form.Control name="remarks" value={TransactionDetailDataState[0]?.remarks || ""} style={{ width: "100%" }} readOnly />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Status Transaksi</Form.ControlLabel>
                            <Form.Control
                              name="status_transaction"
                              value={TransactionDetailDataState[0]?.status_transaction === 2 ? "Draft" : TransactionDetailDataState[0]?.status_transaction === 1 ? "Done" : "Dropped"}
                              style={{ width: "100%" }}
                              readOnly
                            />
                          </Form.Group>
                        </Col>
                      </Row>

                      <Row style={{ marginBottom: "24px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                            <Form.Control name="create_date" value={TransactionDetailDataState[0]?.create_date ? new Date(TransactionDetailDataState[0].create_date).toLocaleDateString("en-GB") : ""} style={{ width: "100%" }} readOnly />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                            <Form.Control name="created_by" value={TransactionDetailDataState[0]?.create_by || ""} style={{ width: "100%" }} readOnly />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                            <Form.Control name="update_date" value={TransactionDetailDataState[0]?.update_date ? new Date(TransactionDetailDataState[0].update_date).toLocaleDateString("en-GB") : ""} style={{ width: "100%" }} readOnly />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                            <Form.Control name="update_by" value={TransactionDetailDataState[0]?.update_by || ""} style={{ width: "100%" }} readOnly />
                          </Form.Group>
                        </Col>
                      </Row>

                      <Row style={{ marginBottom: "16px" }}>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                            <Form.Control name="delete_date" value={TransactionDetailDataState[0]?.delete_date ? new Date(TransactionDetailDataState[0].delete_date).toLocaleDateString("en-GB") : ""} style={{ width: "100%" }} readOnly />
                          </Form.Group>
                        </Col>
                        <Col style={{ paddingRight: "16px" }}>
                          <Form.Group>
                            <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                            <Form.Control name="delete_by" value={TransactionDetailDataState[0]?.delete_by || ""} style={{ width: "100%" }} readOnly />
                          </Form.Group>
                        </Col>
                      </Row>
                    </Grid>
                  </Form>
                </Panel>
              </Modal.Body>
              <Modal.Footer>
                <div style={{ display: "flex", justifyContent: "flex-end", width: "100%" }}>
                  <InputGroup style={{ width: "150px", marginBottom: "16px" }}>
                    <Input type="password" placeholder="Masukan Password" value={password} onChange={(value) => setPassword(value)} />
                  </InputGroup>
                </div>
                <Button
                  appearance="primary"
                  color="red"
                  onClick={() => {
                    if (!password.trim()) {
                      showNotification("error", "Password wajib diisi");
                      return;
                    }
                    setShowTransactionDetailModal(false);
                    setShowRemarksModal(true);
                  }}
                  loading={loading}
                  disabled={loading || !password.trim()}
                >
                  Revisi
                </Button>
                <Button
                  appearance="primary"
                  color="green"
                  onClick={async () => {
                    if (!password.trim()) {
                      showNotification("error", "Password wajib diisi");
                      return;
                    }
                    setLoading(true);
                    await HandleEditStatusApprovalApi(selectedTransactionId, 1, password);
                    setPassword("");
                    setLoading(false);
                  }}
                  loading={loading}
                  disabled={loading || !password.trim()}
                >
                  Approve
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
