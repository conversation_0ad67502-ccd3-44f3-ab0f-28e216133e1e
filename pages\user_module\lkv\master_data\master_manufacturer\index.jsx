import React, { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  ButtonToolbar,
  Modal,
  Schema,
  Message,
  Form,
  useToaster,
  SelectPicker,
  DatePicker,
  Loader,
  InputNumber
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import EditIcon from "@rsuite/icons/Edit";
import LkvMasterManufacturer from "@/pages/api/lkv/api_lkv_master_manufacturer";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import XLSX from "xlsx";
import { useRouter } from "next/router";

const { StringType } = Schema.Types;

const model = Schema.Model({
  name: StringType().isRequired(),
  address: StringType().isRequired(),
  pic: StringType().isRequired(),
  phone: StringType().isRequired(),
  fax: StringType().isRequired(),
});

const initialData = {
  name: "",
  address: "",
  pic: "",
  phone: "",
  fax: "",
};

export default function Index() {
  const [moduleName, setModuleName] = useState("");
  const { HeaderCell, Cell, Column } = Table;
  const [loading, setLoading] = useState(false);
  const [masterData, setMasterData] = useState([]);
  const [formTitle, setFormTitle] = useState("");
  const [idData, setIdData] = useState();
  const [openForm, setOpenForm] = useState(false);
  const toaster = useToaster();
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const formRef = React.useRef();
  const [isBackendDown, setIsBackendDown] = useState(false);
  const [status, setStatus] = useState(0);
  const router = useRouter();
  const [props, setProps] = useState([]);
  const [formData, setFormData] = useState(initialData);
  const [manufacturer, setManufacturer] = useState("");

  const Messages = (type, text) => (
    <Message type={type} showIcon>
      {text}
    </Message>
  );

  // form

  const handleOpenForm = (title) => {
    setOpenForm(true);
    setFormTitle(title);
  };

  const handleForm = () => {
    if (formTitle === "Add") {
      if (!formRef.current.check()) {
        console.error("Form Error!!!");
        console.log("error di submit");
        return;
      }
      handleAddApi();
    } else if (formTitle === "Delete") {
      handleEditStatusApi();
    } else if (formTitle === "Active") {
      handleEditStatusApi();
    } else if (formTitle === "Update") {
      if (!formRef.current.check()) {
        console.error("Form Error");
        return;
      }
      handleEditApi();
    }
  };

  // pagination

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = masterData.filter((rowData, i) => {
    const searchFields = [
      "manufacturer_name",
      "manufacturer_pic",
      "manufacturer_address",
      "manufacturer_pic",
      "phone_number",
      "fax_number",
    ];

    const matchesSearch = searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );

    return matchesSearch;
  });

  const totalRowCount = searchKeyword ? filteredData.length : masterData.length;

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  // api

  const dummyUser = "system";

  const fetchMasterData = async () => {
    const data = await LkvMasterManufacturer().getAllMasterManufacturer();
    setMasterData(data.data ? data.data : []);
  };

  const handleAddApi = async () => {
    if (
      !formData.name ||
      !formData.address ||
      !formData.phone ||
      !formData.pic
    ) {
      console.log("some field are empty");
      return;
    }

    try {
      const data = await LkvMasterManufacturer().createMasterManufacturer({
        created_by: props.employee_id,
        name: formData.name,
        address: formData.address,
        pic: formData.pic,
        phone_number: formData.phone,
        fax_number: formData.fax,
      });

      if (data.status === 200) {
        toaster.push(Messages("success", "Success creating Data!"), {
          placement: "topCenter",
          duration: 5000,
        });

        setOpenForm(false);
        fetchMasterData();
      } else {
        console.log("error creating data", data.message);
        toaster.push(
          Messages("error", `Error: something error. Please try again later!`),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
      }

      setFormData(initialData);
    } catch (error) {
      toaster.push(
        Messages("error", `Error: something error Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
      setFormData(initialData);
    }
  };

  const handleEditApi = async () => {
    if (
      !formData.name ||
      !formData.address ||
      !formData.phone ||
      !formData.pic
    ) {
      console.log("some field are empty");
      return;
    }

    try {
      const data = await LkvMasterManufacturer().updateMasterManufacturer({
        id: idData,
        created_by: props.employee_id,
        name: formData.name,
        address: formData.address,
        pic: formData.pic,
        phone_number: formData.phone,
        fax_number: formData.fax,
      });

      if (data.status === 200) {
        toaster.push(Messages("success", "Success edit Data!"), {
          placement: "topCenter",
          duration: 5000,
        });

        setOpenForm(false);
        fetchMasterData();
      } else {
        console.log("error edit data", data.message);
        toaster.push(
          Messages("error", `Error: something error. Please try again later!`),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
      }

      setFormData(initialData);
    } catch (error) {
      toaster.push(
        Messages("error", `Error: something error Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
      setFormData(initialData);
    }
  };

  const handleEditStatusApi = async () => {
    try {
      const data = await LkvMasterManufacturer().updateStatusMasterManufacturer(
        {
          id: idData,
          is_active: status,
          created_by: props.employee_id,
        }
      );

      if (data.status === 200) {
        toaster.push(Messages("success", "Success update status!"), {
          placement: "topCenter",
          duration: 5000,
        });

        setOpenForm(false);
        fetchMasterData();
      } else {
        console.log("error update status data", data.message);
        toaster.push(
          Messages("error", `Error: something error. Please try again later!`),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
      }
    } catch (error) {
      toaster.push(
        Messages("error", `Error: something error Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("lkv/master_data/master_manufacturer")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      fetchMasterData();
    }
    // fetchMasterData();
  }, []);

  // excel

  const handleExportExcel = () => {
    if (masterData.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }

    const headerMapping = {
      id_manufacturer: "ID Master Manufacturer",
      manufacturer_address: "Address",
      manufacturer_pic: "pic",
      phone_number: "Phone Number",
      fax_number: "Fax Number",
      created_dt: "Created Date",
      created_by: "Created By",
      updated_dt: "Updated Date",
      updated_by: "Updated By",
      deleted_dt: "Deleted Date",
      deleted_by: "Deleted By",
      is_active: "Status",
    };

    const formattedData = masterData.map((item) => {
      const formattedItem = {};
      for (const key in item) {
        if (headerMapping[key]) {
          if (key === "is_active") {
            formattedItem[headerMapping[key]] =
              item[key] === 1 ? "Active" : "Inactive";
          } else {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
      }
      return formattedItem;
    });

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const filename = `LKV Master Manufacturer ${date}.xlsx`;

    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <div>
        <Head>
          <title>LKV Master Manufacturer</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Lkv</Breadcrumb.Item>
                  <Breadcrumb.Item active>Master Manufacturer</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
          bordered
          bodyFill
          className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusRoundIcon />}
                    appearance="primary"
                    onClick={() => handleOpenForm("Add")}
                  >
                    Add Manufacturer
                  </IconButton>
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
              height={400}
            >
              <Column width={70} align="left" fixed sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  ID
                </HeaderCell>
                <Cell dataKey="id_manufacturer" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Manufacturer
                </HeaderCell>
                <Cell dataKey="manufacturer_name" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Address
                </HeaderCell>
                <Cell dataKey="manufacturer_address" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  PIC
                </HeaderCell>
                <Cell dataKey="manufacturer_pic" />
              </Column>
              <Column width={150} align="left" resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Phone
                </HeaderCell>
                <Cell dataKey="phone_number" />
              </Column>
              <Column width={150} align="left" resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Fax
                </HeaderCell>
                <Cell dataKey="fax_number" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Created By
                </HeaderCell>
                <Cell dataKey="created_by" />
              </Column>
              <Column width={150} align="left" resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Created Date
                </HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Updated By
                </HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>
              <Column width={150} align="left" resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Updated Date
                </HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Delete By
                </HeaderCell>
                <Cell dataKey="deleted_by" />
              </Column>
              <Column width={150} align="left" resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Delete Date
                </HeaderCell>
                <Cell dataKey="deleted_dt" />
              </Column>
              <Column width={100} sortable resizable fixed="right">
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={150} resizable fixed="right">
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Operation
                </HeaderCell>

                <Cell style={{ padding: "6px" }} resizable>
                  {(rowData) => (
                    <div>
                      <ButtonToolbar>
                        <Button
                          onClick={() => {
                            setIdData(rowData.id_manufacturer),
                              setFormData({
                                name: rowData.manufacturer_name,
                                address: rowData.manufacturer_address,
                                pic: rowData.manufacturer_pic,
                                phone: rowData.phone_number,
                                fax: rowData.fax_number
                                  ? rowData.fax_number
                                  : "-",
                              }),
                              handleOpenForm("Update");
                          }}
                        >
                          <EditIcon style={{ color: "blue" }} />
                        </Button>
                        {rowData.is_active === 1 && (
                          <Button
                            onClick={() => {
                              handleOpenForm("Delete"),
                                setIdData(rowData.id_manufacturer);
                              setStatus(0);
                              setManufacturer(rowData.manufacturer_name);
                            }}
                          >
                            <TrashIcon style={{ color: "red" }} />
                          </Button>
                        )}
                        {rowData.is_active === 0 && (
                          <Button
                            onClick={() => {
                              handleOpenForm("Active"),
                                setIdData(rowData.id_manufacturer);
                              setStatus(1);
                              setManufacturer(rowData.manufacturer_name);
                            }}
                          >
                            <ReloadIcon style={{ color: "green" }} />
                          </Button>
                        )}
                      </ButtonToolbar>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
        <Modal
          open={openForm}
          backdrop="statis"
          onClose={() => {
            setOpenForm(false);
            setFormData(initialData);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>{formTitle}</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {formTitle === "Delete" && (
              <p>Are you sure to delete this "{manufacturer}" ?</p>
            )}
            {formTitle === "Active" && (
              <p>Are you sure to active this "{manufacturer}" ?</p>
            )}
            {formTitle !== "Delete" && formTitle !== "Active" && (
              <Form
                model={model}
                ref={formRef}
                formValue={formData}
                onChange={(formValue) => setFormData(formValue)}
                fluid
              >
                {/* <div style={{ display: "flex", flexWrap: "wrap", gap: "20px" }}>
                  <Form.Group controlId="name">
                    <Form.ControlLabel>Name</Form.ControlLabel>
                    <Form.Control
                      name="name"
                      placeholder="name"
                      style={{ width: 250 }}
                    />
                  </Form.Group>
                  <Form.Group controlId="address">
                    <Form.ControlLabel>Address</Form.ControlLabel>
                    <Form.Control
                      name="address"
                      placeholder="address"
                      style={{ width: 250 }}
                    />
                  </Form.Group>
                </div> */}
                <Form.Group controlId="name">
                  <Form.ControlLabel>Name</Form.ControlLabel>
                  <Form.Control name="name" placeholder="name" />
                </Form.Group>
                <Form.Group controlId="address">
                  <Form.ControlLabel>Address</Form.ControlLabel>
                  <Form.Control name="address" placeholder="address" />
                </Form.Group>
                <Form.Group controlId="pic">
                  <Form.ControlLabel>PIC</Form.ControlLabel>
                  <Form.Control name="pic" placeholder="pic" />
                </Form.Group>
                <Form.Group controlId="phone">
                  <Form.ControlLabel>Phone</Form.ControlLabel>
                  <Form.Control name="phone" placeholder="phone" accepter={InputNumber} />
                </Form.Group>
                <Form.Group controlId="fax">
                  <Form.ControlLabel>fax</Form.ControlLabel>
                  <Form.Control name="fax" placeholder="fax" />
                </Form.Group>
              </Form>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setOpenForm(false);
                setFormData(initialData);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button appearance="primary" onClick={handleForm}>
            {formTitle === "Delete"
        ? "Delete"
        : formTitle === "Update"
        ? "Edit"
        : formTitle === "Active"
        ? "Active"
        : "Add"}
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
