import { useEffect, useState } from "react";
import Head from "next/head"
import { <PERSON>read<PERSON>rumb, Panel, Stack, Tag, Button, Modal, Form, useToaster, Divider, Table, Pagination } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";
import { useRouter } from "next/router";
import API_StockCorrection from "@/pages/api/stock_correction/api_stock_correction";

export default function ApprovalCorrectionSupervisorPage() {

    const [moduleName, setModuleName] = useState("");
    const [requestCorrection, setRequestCorrection] = useState({});
    const [approvalCount, setApprovalCount] = useState([]);
    const [approvalDetail, setApprovalDetail] = useState([]);
    const [validationUserModal, setValidationUserModal] = useState(false);
    const [approvalStatus, setApprovalStatus] = useState(null);
    const [isActiveStatus, setIsActiveStatus] = useState(null);
    const [props, setProps] = useState([]);
    const toaster = useToaster();
    const router = useRouter();
    const [errors, setErrors] = useState({});
    const { HeaderCell, Cell, Column } = Table;
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [stagingStatus, setStagingStatus] = useState(0);
    const updateLocatorData = {
        status: null,
        located_flag: null,
        is_active: null,
        data: [
            {
                id_inbound_reagen_locator: null,
            }
        ]
    };
    const [updateLocatorStatus, setUpdateLocatorStatus] = useState(updateLocatorData);


    const updateLocatorDataValue = (approvalDetail) => {
        console.log("approval detail", approvalDetail)
        const idInboundReagenLocator = approvalDetail.map(item => ({
            id_inbound_reagen_locator: item.id_inbound_reagen_locator
        }));
        console.log("id inbound", idInboundReagenLocator)
        setUpdateLocatorStatus({
            ...updateLocatorStatus,
            data: idInboundReagenLocator,
        });
    };

    const handleGetActiveApi = async () => {
        const res = await API_StockCorrection().getActiveBySupervisor();
        setRequestCorrection(res.data ? res.data : {});
        handleGetApprovalCountApi(res?.data?.id_reagen_correction);
        handleGetApprovalDetailApi(res?.data?.id_reagen_correction);
        console.log("aktif", res.data)
    };

    const handleGetApprovalCountApi = async (selectedIdRequest) => {
        const res = await API_StockCorrection().getApprovalDetailCount({ id_reagen_correction: selectedIdRequest });
        setApprovalCount(res.data ? res.data : [])
    }

    const handleGetApprovalDetailApi = async (selectedIdRequest) => {
        const res = await API_StockCorrection().getApprovalDetailRequest({ id_reagen_correction: selectedIdRequest });
        setApprovalDetail(res.data ? res.data : [])
        console.log("approve detail", res.data)
    }

    const handleEditStatusStagingApi = async (idReagenCorrection, StagingStatus) => {
        await API_StockCorrection().editStatusStaging({
            id_reagen_correction: idReagenCorrection,
            status_staging: StagingStatus
        });
    };

    const handleEditStatusLocatorApi = async () => {
        console.log("update loc status manager", updateLocatorStatus)
        await API_StockCorrection().editStatusLocator({
            ...updateLocatorStatus
        });
    };

    const handleApproval = async (selectedIdRequest, approvalStatus, isActiveStatus) => {
        try {

            if (!requestCorrection.password) {
                setErrors({ password: 'Password is required.' });
                return;
            }
            setErrors({});

            const result = await API_StockCorrection().editApprovalBySupervisor({
                ...requestCorrection,
                id_reagen_correction: selectedIdRequest,
                approved_by: props.employee_id,
                status_correction: approvalStatus,
                is_active: isActiveStatus
            });

            console.log("status", result.status)

            if (result.status === 200) {
                handleGetActiveApi();

                if (approvalStatus === 3) {
                    toaster.push(Messages("success", "Stock Correction Approved"), {
                        placement: "topCenter",
                        duration: 5000,
                    });
                }else{
                    toaster.push(Messages("success", "Stock Correction Rejected"), {
                        placement: "topCenter",
                        duration: 5000,
                    });
                }
                setRequestCorrection({});
                setApprovalCount([]);
                setValidationUserModal(false);
            } else if (result.status === 400 && result.message === "crypto/bcrypt: hashedPassword is not the hash of the given password") {
                toaster.push(
                    Messages("error", `Error: Invalid Password!`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
            } else if (result.status === 400) {
                toaster.push(
                    Messages("error", `Error: "${result.message}". Please try again later!`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    };

    const handleRejectClick = () => {
        setValidationUserModal(true);
        setApprovalStatus(0);
        setIsActiveStatus(0);
        setStagingStatus(2);
        updateLocatorDataValue(approvalDetail);
        setUpdateLocatorStatus(prevState => ({
            ...prevState,
            status: 1,
            located_flag: "N",
            is_active: 1
        }));
    };

    const handleAcceptClick = () => {
        setValidationUserModal(true);
        setApprovalStatus(3);
        setIsActiveStatus(1);
        setStagingStatus(0);
        updateLocatorDataValue(approvalDetail);
        setUpdateLocatorStatus(prevState => ({
            ...prevState,
            status: 1,
            located_flag: "N",
            is_active: 1
        }));
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = approvalCount
        .filter((v, i) => {
            const start = limit * (page - 1);
            const end = start + limit;
            return i >= start && i < end;
        });


    const totalRowCount = approvalCount.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("stock_correction/approval_correction_supervisor")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            handleGetActiveApi();
        }
    }, []);

    // useEffect(() => {
    //     handleGetApprovalCountApi(requestCorrection.id_reagen_correction);
    //     handleGetApprovalDetailApi(requestCorrection.id_reagen_correction);
    // }, [requestCorrection])


    return (
        <>
            <div>
                <Head>
                    <title>Approval Correction Supervisor</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Quality</Breadcrumb.Item>
                                    <Breadcrumb.Item>Stock Correction</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Approval Correction Supervisor</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    {Object.keys(requestCorrection).length > 0 ? (
                        <Panel
                            bordered
                            bodyFill
                        >
                            <div className="mt-3 ml-5 mb-3">
                                <h3>Request Correction</h3>
                            </div>
                            <div className="ml-5 mb-4">
                                <p className="mb-2"><span className="fw-bold">Request ID : </span>{requestCorrection.id_reagen_correction}</p>
                                <p className="mb-2"><span className="fw-bold">Submit Date : </span>{requestCorrection.submitted_dt}</p>
                                <p className="mb-2"><span className="fw-bold">Submit By : </span>{requestCorrection.submitted_by} - {requestCorrection.submitted_name}</p>
                                <p className="mb-2"><span className="fw-bold">Remarks : </span>{requestCorrection.remarks}</p>
                            </div>
                            <div className="ml-5 mb-3">
                                <Button onClick={handleRejectClick} color="red" appearance="primary" className="mr-3">Reject</Button>
                                <Button onClick={handleAcceptClick} color="green" appearance="primary">Approve</Button>
                            </div>
                            <Divider></Divider>
                            <div className="ml-5 mb-3 mr-5">
                                <h5 className="mb-3">Correction Detail</h5>
                                <Table
                                    bordered
                                    cellBordered
                                    height={400}
                                    data={filteredData}
                                    sortColumn={sortColumn}
                                    sortType={sortType}
                                    onSortColumn={handleSortColumn}
                                >
                                    <Column width={70} align="center" fixed>
                                        <HeaderCell>No</HeaderCell>
                                        <Cell>
                                            {(rowData, rowIndex) => {
                                                return rowIndex + 1 + limit * (page - 1);
                                            }}
                                        </Cell>
                                    </Column>
                                    <Column width={200} align="center" sortable>
                                        <HeaderCell>Reagen Name</HeaderCell>
                                        <Cell dataKey="reagen_name" />
                                    </Column>
                                    <Column width={180} align="center" sortable>
                                        <HeaderCell>Expired Date</HeaderCell>
                                        <Cell>
                                            {(rowData) => new Date(rowData.expired_date).toLocaleDateString('en-GB')}
                                        </Cell>
                                    </Column>
                                    <Column width={180} align="center" sortable>
                                        <HeaderCell>Rack</HeaderCell>
                                        <Cell dataKey="rack_desc" />
                                    </Column>
                                    <Column width={180} align="center" sortable>
                                        <HeaderCell>Floor</HeaderCell>
                                        <Cell dataKey="floor_level_desc" />
                                    </Column>
                                    <Column width={180} sortable align="center">
                                        <HeaderCell>Row</HeaderCell>
                                        <Cell dataKey="row_level_desc" />
                                    </Column>
                                    <Column width={180} sortable align="center">
                                        <HeaderCell>Amount</HeaderCell>
                                        <Cell dataKey="count" />
                                    </Column>
                                </Table>
                                <div style={{ padding: 20 }}>
                                    <Pagination
                                        prev
                                        next
                                        first
                                        last
                                        ellipsis
                                        boundaryLinks
                                        maxButtons={5}
                                        size="xs"
                                        layout={["total", "-", "limit", "|", "pager", "skip"]}
                                        limitOptions={[10, 30, 50]}
                                        total={totalRowCount}
                                        limit={limit}
                                        activePage={page}
                                        onChangePage={setPage}
                                        onChangeLimit={handleChangeLimit}
                                    />
                                </div>
                            </div>
                        </Panel>
                    ) : (
                        <div className="flex justify-center items-center h-96">
                            <p className="text-lg">No Correction Request</p>
                        </div>
                    )}

                </div>

                <Modal
                    backdrop="static"
                    open={validationUserModal}
                    onClose={() => {
                        setValidationUserModal(false);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Input Credential</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Password</Form.ControlLabel>
                                <Form.Control
                                    type="password"
                                    name="password"
                                    value={requestCorrection.password || ''}
                                    onChange={(value) => {
                                        setRequestCorrection({ ...requestCorrection, password: value })
                                        setErrors((prevErrors) => ({
                                            ...prevErrors,
                                            password: null,
                                        }))
                                    }}
                                />
                                {errors.password && <p style={{ color: 'red' }}>{errors.password}</p>}
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setValidationUserModal(false);
                                setErrors({});
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleApproval(requestCorrection.id_reagen_correction, approvalStatus, isActiveStatus);
                                handleEditStatusStagingApi(requestCorrection.id_reagen_correction, stagingStatus);
                                handleEditStatusLocatorApi();
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Confirm
                        </Button>
                    </Modal.Footer>
                </Modal>

            </ContainerLayout>
        </>
    )
}
