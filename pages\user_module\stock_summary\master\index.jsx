import { useEffect, useState } from "react";
import Head from "next/head"
import { <PERSON><PERSON><PERSON>rumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, SelectPicker, ButtonGroup, InputNumber } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import API_MasterDataReagenCapRack from "@/pages/api/master_data/api_master_data_rc_rack";
import ApiStockSummaryOra from "@/pages/api/stock_summary/api_stock_summary";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash"
import { useRouter } from "next/router";

export default function MasterDataStockSummaryPage() {

    const [moduleName, setModuleName] = useState("");
    const { HeaderCell, Cell, Column } = Table;
    const [capRack, setCapRack] = useState([]);
    const [stockSummary, setStockSummary] = useState([])
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [props, setProps] = useState([]);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showAddModal, setShowAddModal] = useState(false);
    const [dataCriteria, setDataCriteria] = useState([]);
    const [oracleStock, setStockOracleStock] = useState([])
    const emptyFormValue = {
        created_by :"",
        item_code  :"",
        item_desc  :"",
        uom        :"",
        min_qty    :0.0,
        max_qty    :0.0,
        lead_time : 0,
    }
    const [formValue, setFormValue] = useState(emptyFormValue);
    const toaster = useToaster();
    const emptyErrorValue = {
        created_by :null,
        item_code  :null,
        item_desc  :null,
        uom        :null,
        min_qty    :null,
        max_qty    :null,
    }
    const [errors, setErrors] = useState(emptyErrorValue);
    const router = useRouter();

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const handleGetAllApi = async () => {

        try {
            const res = await ApiStockSummaryOra().getPgList();
            if (res.status === 200) {
                setStockSummary(res.data ? res.data : [])
            }else if (res.status ===400){
                console.log("error on response", res.message)
            }else {
                console.log("error on response", res.message)
            }
        } catch (error) {
            console.log("error data ", error)
        }


        try {
            const resOra = await ApiStockSummaryOra().getOcList();
            if (resOra.status === 200) {
                const transformedData = resOra.data ? resOra.data.map(item => ({
                   item_code: item.item_code,
                    item_desc: item.item_desc,
                    uom: item.uom,
                    quantity: 5525,
                    label: `${item.item_code} - ${item.item_desc}`,
                    status_kanban: item.status_kanban
                })) : [];
                setStockOracleStock(transformedData ? transformedData : [])
            }else if (resOra.status ===400){
                console.log("error on response", resOra.message)
            }else {
                console.log("error on response", resOra.message)
            }
        } catch (error) {
            console.log("error data ", error)
        }


        // const res = await API_MasterDataReagenCapRack().getAll();
        // console.log(res);
        // setCapRack(res.data ? res.data : []);
        // const resultCriteria = await API_MasterDataMasterDataReagenCriteria().getAllActive()
        // console.log("result ", resultCriteria)
        // setDataCriteria(resultCriteria.data ? resultCriteria.data : []);
    };

    const filteredData = stockSummary
        .filter((rowData, i) => {
            const searchFields = [
                "item_code",
                "item_description",
                "uom",
                "min_qty",
                "max_qty",
                "created_dt",
                "created_by",
                "is_active",
                "updated_dt",
                "updated_by",
                "deleted_dt",
                "deleted_by",
            ];

            const matchesSearch = searchFields.some((field) =>
                rowData[field]
                    ?.toString()
                    .toLowerCase()
                    .includes(searchKeyword.toLowerCase())
            );

            return matchesSearch;
        })

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    }

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : stockSummary.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("stock_summary/master")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            handleGetAllApi();
        }
    }, []);

    const handleEditStatusCapRack = async (selectedIdRack) => {
        await API_MasterDataReagenCapRack().editStatus({
            id_rack: selectedIdRack,
            deleted_by: props.employee_id,
        });
        handleGetAllApi();
    };

    const handleEditApi = async (selectedItemCode) => {

        if (parseFloat(formValue.min_qty) > parseFloat(formValue.max_qty)) {
            console.log("min is more than max")
            setErrors((prevErrors) => ({ ...prevErrors, max_qty: 'min is more than max' }));
            setFormValue(emptyFormValue);
            return
        }

        // if (formValue.min_qty === formValue.max_qty) {
        //     console.log("min is same as max")
        //     setErrors((prevErrors) => ({ ...prevErrors, max_qty: 'min is same as max' }));
        //     setFormValue(emptyFormValue);
        //     return
        // }

        try {
            const result = await ApiStockSummaryOra().updateStockList({
                updated_by: props.employee_id,
                item_code: formValue.item_code,
                min_qty: parseFloat(formValue.min_qty),
                max_qty:parseFloat(formValue.max_qty),  
                lead_time: parseInt(formValue.lead_time)              
            });

            if (result.status === 200) {
                handleGetAllApi();
                setShowEditModal(false);

                toaster.push(Messages("success", "Success edit stock summary!"), {
                    placement: "topCenter",
                    duration: 5000,
                });
                setFormValue(emptyFormValue);
            } else if (result.status === 400) {
                toaster.push(
                    Messages("error", `Error: "${result.message}". Please try again later!`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setFormValue(emptyFormValue);
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    };

    const handleAddApi = async () => {
        if (parseFloat(formValue.min_qty) > parseFloat(formValue.max_qty)) {
            console.log("min is more than max")
            setErrors((prevErrors) => ({ ...prevErrors, max_qty: 'min is more than max' }));
            setFormValue(emptyFormValue);
            return
        }

        // if (formValue.min_qty === formValue.max_qty) {
        //     console.log("min is same as max")
        //     setErrors((prevErrors) => ({ ...prevErrors, max_qty: 'min is same as max' }));
        //     setFormValue(emptyFormValue);
        //     return
        // }

        try {
            const result = await ApiStockSummaryOra().createStockList({
                created_by: props.employee_id,
                item_code: formValue.item_code,
                item_desc: formValue.item_desc,
                uom: formValue.uom,
                min_qty: parseFloat(formValue.min_qty),
                max_qty: parseFloat(formValue.max_qty),
                lead_time: parseInt(formValue.lead_time)
            });

            if (result.status === 200) {
                handleGetAllApi();
                setShowAddModal(false);

                toaster.push(
                    Messages("success", "Success adding Stock Summary!"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setFormValue(emptyFormValue);
            } else if (result.status === 400) {
                if (result.message.includes('duplicate')) {
                    toaster.push(
                    Messages("error", `Item code already inputted`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }      
                    )              
                }else{
                    toaster.push(
                        Messages("error", `Error: "${result.message}". Please try again later!`),
                        {
                            placement: "topCenter",
                            duration: 5000,
                        }
                    );
                }               
                setFormValue(emptyFormValue);
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    };

    const handleExportExcel = () => {

        if (stockSummary.length === 0) {
            toaster.push(Messages("error", "No data to export!"), {
                placement: "topCenter",
                duration: 5000,
            });
            return;
        }

        const headerMapping = {
            item_code:"item_code",
            item_description:"item_description",
            uom:"uom",
            min_qty:"min_qty",
            max_qty:"max_qty",
            created_dt:"created_dt",
            created_by:"created_by",
            is_active:"is_active",
            updated_dt:"updated_dt",
            updated_by:"updated_by",
            deleted_dt:"deleted_dt",
            deleted_by:"deleted_by",
        };

        const formattedData = stockSummary.map((item) => {
            const formattedItem = {};
            for (const key in item) {
                if (headerMapping[key]) {
                    if (key === 'is_active') {
                        formattedItem[headerMapping[key]] = item[key] === 1 ? 'Active' : 'Inactive';
                    } else {
                        formattedItem[headerMapping[key]] = item[key];
                    }
                }
            }
            return formattedItem;
        });

        const ws = XLSX.utils.json_to_sheet(formattedData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "data");

        const date = dateFormatterDash(new Date());
        const filename = `Master Data Reagen Stock Summary ${date}.xlsx`;

        XLSX.writeFile(wb, filename);
    }

    const handleSubmit = (apiFunction) => {
        //use usestate
        let check = true
        setErrors(emptyErrorValue);
        console.log(formValue)
        if (!formValue.item_code) {
            check = false
            setErrors((prevErrors) => ({ ...prevErrors, item_code: 'Please choose Item Code' }));
            //return;
        }

        // if (!formValue.min_qty) {
        //     check = false
        //     setErrors((prevErrors) => ({ ...prevErrors, min_qty: 'Min Qty must be inputed' }));
        //     //return;
        // }

        // if (!formValue.max_qty) {
        //     check = false
        //     setErrors((prevErrors) => ({ ...prevErrors, max_qty: 'Max Qty must be inputed' }));
        //     //return;
        // }
        if (!check) {
            return
        }
        setErrors(emptyErrorValue);
        apiFunction();
    };

    // const handleRackClick = () => {
    //     router.push("/user_module/master_data/master_rc_rack")
    // }

    // const handleFloorClick = () => {
    //     router.push("/user_module/master_data/master_rc_floor")
    // }

    // const handleRowClick = () => {
    //     router.push("/user_module/master_data/master_rc_row")
    // }


    const findStatusKanban =  (item_code)=>{
        if (oracleStock.length >0) {
            const kanban = oracleStock.find(item => item.item_code === item_code)
            return kanban?.status_kanban || "-"
        }
        return "-"
    }

    return (
        <>
            <div>
                <Head>
                    <title>Stock Summary</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>Stock Summary</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Mater Data</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Stock Summary </h5>
                                {/* <Stack className="flex gap-2">
                                    <ButtonGroup>
                                        <Button
                                            onClick={handleRackClick}
                                            appearance="primary"
                                            active>
                                            Master Rack
                                        </Button>
                                        <Button
                                            onClick={handleFloorClick}
                                            appearance="primary">
                                            Master Floor
                                        </Button>
                                        <Button
                                            appearance="primary"
                                            onClick={handleRowClick}>
                                            Master Row
                                        </Button>
                                    </ButtonGroup>
                                </Stack> */}
                            </Stack>}>
                    </Panel>

                    <Panel
                        bordered
                        bodyFill
                        header={
                            <Stack justifyContent="space-between">
                                <div className="flex gap-2">
                                    <IconButton
                                        icon={<PlusRoundIcon />}
                                        appearance="primary"
                                        onClick={() => {
                                            setShowAddModal(true);
                                        }}>
                                        Add
                                    </IconButton>
                                    <IconButton
                                        icon={<FileDownloadIcon />}
                                        appearance="primary"
                                        onClick={handleExportExcel}
                                    >
                                        Download (.xlsx)
                                    </IconButton>
                                </div>

                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="search"
                                        value={searchKeyword}
                                        onChange={handleSearch}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}>
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                        }
                    >
                        <Table
                            bordered
                            cellBordered
                            height={400}
                            data={getPaginatedData(getFilteredData(), limit, page)}
                            sortColumn={sortColumn}
                            sortType={sortType}
                            onSortColumn={handleSortColumn}
                        >
                            <Column width={70} align="center" fixed>
                                <HeaderCell>No</HeaderCell>
                                <Cell>
                                    {(rowData, rowIndex) => {
                                        return rowIndex + 1 + limit * (page - 1);
                                    }}
                                </Cell>
                            </Column>
                            <Column width={150} align="center" sortable resizable>
                                <HeaderCell>Item Code</HeaderCell>
                                <Cell dataKey="item_code" />
                            </Column>
                            <Column width={200} sortable resizable fullText>
                                <HeaderCell>Description</HeaderCell>
                                <Cell dataKey="item_description" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>UOM</HeaderCell>
                                <Cell dataKey="uom" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Min Qty</HeaderCell>
                                <Cell dataKey="min_qty" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Max Qty</HeaderCell>
                                <Cell dataKey="max_qty" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Lead Time in days</HeaderCell>
                                <Cell dataKey="lead_time" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Created Date</HeaderCell>
                                <Cell dataKey="created_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Created By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        `${rowData.created_by} - ${rowData.created_name}`
                                    )}
                                </Cell>
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Updated Date</HeaderCell>
                                <Cell dataKey="updated_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Updated By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        `${rowData.updated_by} - ${rowData.updated_name}`
                                    )}
                                </Cell>
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Deleted Date</HeaderCell>
                                <Cell dataKey="deleted_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Deleted By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        `${rowData.deleted_by} - ${rowData.deleted_name}`
                                    )}
                                </Cell>
                            </Column>
                            <Column width={100} sortable resizable>
                                <HeaderCell>Status</HeaderCell>
                                <Cell>
                                    {(rowData) => (
                                        <span
                                            style={{
                                                color: rowData.is_active === 1 ? "green" : "red",
                                            }}
                                        >
                                            {rowData.is_active === 1 ? "Active" : "Inactive"}
                                        </span>
                                    )}
                                </Cell>
                            </Column>
                            <Column width={120} fixed="right" align={"center"}>
                                <HeaderCell>Status Kanban</HeaderCell>
                                <Cell>
                                    {(rowData) => (
                                        <span
                                            style={{
                                                color: findStatusKanban( rowData.item_code)=== "Y" ? "green" : "red",
                                            }}
                                        >
                                            {findStatusKanban( rowData.item_code)}
                                        </span>
                                    )}
                                </Cell>
                            </Column>
                            <Column width={100} fixed="right" align="center">
                                <HeaderCell>Action</HeaderCell>
                                <Cell style={{ padding: "8px" }}>
                                    {(rowData) => (
                                        <div>
                                            <Button
                                                appearance="link"
                                                disabled={rowData.is_active === 0}
                                                onClick={() => {
                                                    setShowEditModal(true);
                                                    // setFormValue(rowData);
                                                    setFormValue({ 
                                                        ...formValue, 
                                                        item_code:  rowData.item_code, 
                                                        item_desc: rowData.item_description,
                                                        uom: rowData.uom,
                                                        min_qty:  rowData.min_qty,
                                                         max_qty:  rowData.max_qty,
                                                         lead_time: rowData.lead_time })
                                                }}
                                            >
                                                Edit
                                            </Button>
                                            {/* <Button
                                                appearance="subtle"
                                                onClick={() =>
                                                    handleEditStatusCapRack(rowData.id_rack)
                                                }
                                            >
                                                {rowData.is_active === 1 ? (
                                                    <TrashIcon style={{ fontSize: "16px" }} />
                                                ) : (
                                                    <ReloadIcon style={{ fontSize: "16px" }} />
                                                )}
                                            </Button> */}
                                        </div>
                                    )}
                                </Cell>
                            </Column>
                        </Table>
                        <div style={{ padding: 20 }}>
                            <Pagination
                                prev
                                next
                                first
                                last
                                ellipsis
                                boundaryLinks
                                maxButtons={5}
                                size="xs"
                                layout={["total", "-", "limit", "|", "pager", "skip"]}
                                limitOptions={[10, 30, 50]}
                                total={totalRowCount}
                                limit={limit}
                                activePage={page}
                                onChangePage={setPage}
                                onChangeLimit={handleChangeLimit}
                            />
                        </div>
                    </Panel>
                </div>

                {/* add modal */}
                <Modal
                    backdrop="static"
                    open={showAddModal}
                    onClose={() => {
                        setShowAddModal(false);
                        setFormValue(emptyFormValue);
                        setErrors(emptyErrorValue);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Add Stock Summary</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>                                
                                <Form.ControlLabel>Item Code</Form.ControlLabel>
                                <Form.Control
                                    name="item_code"
                                    accepter={SelectPicker}
                                    value={formValue.item_code}
                                    data={oracleStock}
                                    valueKey="item_code"
                                    labelKey="label"
                                    block
                                    onChange={(value) => {
                                        const item = oracleStock.find(data => data.item_code === value);
                                        setFormValue({ 
                                            ...formValue, 
                                            item_code:item.item_code, 
                                            uom:item.uom,
                                            item_desc:item.item_desc });
                                    }}
                                />
                                {errors.item_code && <p style={{ color: 'red' }}>{errors.item_code}</p>}
                                <Form.ControlLabel>Min Qty</Form.ControlLabel>
                                <Form.Control
                                    name="min_qty"
                                    value={formValue.min_qty}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, min_qty: value })
                                    }}
                                    accepter={InputNumber}
                                    min={0}
                                />
                                {errors.min_qty != undefined && <p style={{ color: 'red' }}>{errors.min_qty}</p>}
                                <Form.ControlLabel>Max Qty</Form.ControlLabel>
                                <Form.Control
                                    name="max_qty"
                                    value={formValue.max_qty}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, max_qty: value })
                                    }}
                                    accepter={InputNumber}
                                    min={0}
                                />
                                {errors.max_qty != undefined && <p style={{ color: 'red' }}>{errors.max_qty}</p>}
                                <Form.ControlLabel>Lead Time in Days</Form.ControlLabel>
                                <Form.Control
                                    name="lead_time"
                                    value={formValue.lead_time}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, lead_time: value })
                                    }}
                                    accepter={InputNumber}
                                    min={0}
                                    max={5}
                                />
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowAddModal(false);
                                setFormValue(emptyFormValue);
                                setErrors(emptyErrorValue);
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleSubmit(handleAddApi);
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Add
                        </Button>
                    </Modal.Footer>
                </Modal>

                {/* Edit Modal */}
                <Modal
                    backdrop="static"
                    open={showEditModal}
                    onClose={() => {
                        setShowEditModal(false);
                        setFormValue(emptyFormValue);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Edit Stock Summary</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Item Code: {formValue.item_code}</Form.ControlLabel>
                                <Form.ControlLabel>Item Code: {formValue.item_desc}</Form.ControlLabel>
                                <Form.ControlLabel>Item Code: {formValue.uom}</Form.ControlLabel>
                                <Form.ControlLabel>Min Qty</Form.ControlLabel>
                                <Form.Control
                                    name="min_qty"
                                    value={formValue.min_qty}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, min_qty: value })
                                    }}
                                    accepter={InputNumber}
                                    min={0}
                                />
                                {errors.min_qty != undefined && <p style={{ color: 'red' }}>{errors.min_qty}</p>}
                                <Form.ControlLabel>Max Qty</Form.ControlLabel>
                                <Form.Control
                                    name="max_qty"
                                    value={formValue.max_qty}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, max_qty: value })
                                    }}
                                    accepter={InputNumber}
                                    min={0}
                                />
                                {errors.max_qty != undefined && <p style={{ color: 'red' }}>{errors.max_qty}</p>}
                                <Form.ControlLabel>Lead Time in Days</Form.ControlLabel>
                                <Form.Control
                                    name="lead_time"
                                    value={formValue.lead_time}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, lead_time: value })
                                    }}
                                    accepter={InputNumber}
                                    min={0}
                                    max={5}
                                />
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowEditModal(false);
                                setFormValue(emptyFormValue);
                                setErrors(emptyErrorValue);
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleSubmit(() => handleEditApi(formValue.item_code));
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Edit
                        </Button>
                    </Modal.Footer>
                </Modal>
            </ContainerLayout>
        </>
    )
}
