import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiRekonFoil() {
  return {
    getAllRekonFoil: createApiFunction("get", "pqr/rekon_foil/list"),
    createRekonFoil: createApiFunction("post", "pqr/rekon_foil/create"),
    updateRekonFoil: createApiFunction("put", "pqr/rekon_foil/edit"),
    getRekonFoilByIdTransHeader: createApiFunction("post", "pqr/rekon_foil/id-trans-h"),
  };
}
