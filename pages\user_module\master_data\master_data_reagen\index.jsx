import { useEffect, useState } from "react";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Icon<PERSON>utton,
  Stack,
  Breadcrumb,
  Tag,
  Panel,
  Table,
  InputGroup,
  Input,
  Pagination,
  Button,
  Drawer,
  Form,
  useToaster,
  Modal,
  Toggle,
} from "rsuite";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import API_MasterDataMasterDataReagen from "@/pages/api/master_data/api_masterdata_master_data_reagen";
// import { validate_empty_columns } from "@/utils/validate";

export default function MasterDataMasterDataReagenPage() {
  const toaster = useToaster();

  const [moduleName, setModuleName] = useState("");
  const { HeaderCell, Cell, Column } = Table;
  const [limit, setLimit] = useState(10); //to limit the number of row in the table
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");

  const [reagenData, setReagenData] = useState([]);
  const [openWithHeader, setOpenWithHeader] = useState(false);
  const [selectedReagen, setSelectedReagen] = useState(null);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [props, setProps] = useState([]);
  const [formErrors, setFormErrors] = useState({});

  const emptyFormValue = {
    nama_reagen: null,
    no_katalog: null,
    manufacture: null,
    reagen_type: null,
    reagen_category: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleGetAllApi = async () => {
    const reagenData = await API_MasterDataMasterDataReagen().getAll();
    console.log(reagenData);
    setReagenData(reagenData.data.data);
  };

  const columnLabels = {
    nama_reagen: "Reagen Name",
    no_katalog: "No Katalog",
    manufacture: "Manufacture",
    reagen_type: "Reagen Type",
    reagen_category: "Reagen Category",
  };

  const handleEditApi = async (selectedIdReagen) => {
    try {
      const newFormErrors = validate_empty_columns(formValue, columnLabels);
      setFormErrors(newFormErrors);

      // Check if there are any errors in the form
      // If there is error it would not fetch data
      if (Object.keys(newFormErrors).length > 0) {
        console.log("Please fill in all required fields.");
        return;
      }

      const result = await API_MasterDataMasterDataReagen().edit({
        ...formValue,
        id_master_reagen: selectedIdReagen,
        updated_by: props.employee_id,
      });

      if (result.status === 200) {
        handleGetAllApi();
        setShowEditModal(false);

        toaster.push(Messages("success", "Success editing Reagen Data!"), {
          placement: "topCenter",
          duration: 5000,
        });
      } 
      setFormValue(emptyFormValue);
    } 
    catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);

      // Show an error toast message
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const filteredData = reagenData
    .filter((rowData, i) => {
      const searchFields = [
        "id_master_reagen",
        "nama_reagen",
        "no_katalog",
        "manufacture",
        "reagen_type",
        "reagen_category",
        "created_dt",
        "created_by",
        "updated_dt",
        "updated_by",
        "deleted_dt",
        "deleted_by",
        "is_active",
      ];

      const matchesSearch = searchFields.some((field) =>
        rowData[field]
          ?.toString()
          .toLowerCase()
          .includes(searchKeyword.toLowerCase())
      );

      return matchesSearch;
    })
    .filter((v, i) => {
      // Pagination logic
      const start = limit * (page - 1);
      const end = start + limit;

      return i >= start && i < end;
    });

  // Dynamically update the total row count based on the search results
  const totalRowCount = searchKeyword ? filteredData.length : reagenData.length;

  const handleExportExcel = () => {
    try {
      // Define a mapping between keys in reagenData and desired header names
      const headerMapping = {
        id_master_reagen: "ID",
        nama_reagen: "Reagen Name",
        no_katalog: "No Katalog",
        manufacture: "Manufacture",
        reagen_type: "Reagen Type",
        reagen_category: "Reagen Category",
        created_dt: "Created Date",
        created_by: "Created By",
        updated_dt: "Updated Date",
        updated_by: "Updated By",
        deleted_dt: "Deleted Date",
        deleted_by: "Deleted By",
      };

      // Create a new array with updated keys for headers
      const formattedData = reagenData.map((item) => {
        const formattedItem = {};
        for (const key in item) {
          if (headerMapping[key]) {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
        return formattedItem;
      });

      // Create a worksheet
      const ws = XLSX.utils.json_to_sheet(formattedData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Reagen Data");

      const date = dateFormatterDash(new Date());
      XLSX.writeFile(wb, `ReagenData ${date}.xlsx`);

      toaster.push(
        Messages("success", "Reagen data file (.xlsx) downloaded successfully"),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    } catch (error) {
      console.error("Error exporting Excel:", error);
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };

  const handleAddReagen = async () => {
    try {
      // Check each field for errors
      const newFormErrors = validate_empty_columns(formValue, columnLabels);
      setFormErrors(newFormErrors);
  
      // Check if there are any errors in the form
      // If there is error it would not fetch data
      if (Object.keys(newFormErrors).length > 0) {
        console.log("Please fill in all required fields.");
        return;
      }
  
      const result = await API_MasterDataMasterDataReagen().add({
        ...formValue,
        created_by: props.employee_id,
      });
  
      // Check the response status
      if (result.status === 200) {
        handleGetAllApi();
        setShowAddModal(false);
  
        toaster.push(
          Messages("success", "Success adding Master Data Reagen!"),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
      }
    } catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);
  
      // Show an error toast message
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
    }
  };
  

  const handleEditStatusReagen = async (selectedIdReagen) => {
    console.log("selected id", selectedIdReagen);
    await API_MasterDataMasterDataReagen().editStatus({
      id_master_reagen: selectedIdReagen,
      deleted_by: props.employee_id,
    });
    handleGetAllApi();
  };

  useEffect(() => {
    handleGetAllApi();

    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("master_data/master_data_reagen")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetAllApi();
    }
  }, []);

  const getData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  return (
    <>
      <div>
        <Head>
          <title>Master Data Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>Master Data Reagen</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusRoundIcon />}
                    appearance="primary"
                    onClick={() => {
                      setShowAddModal(true);
                    }}
                  >
                    Add
                  </IconButton>

                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getData()}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="id_master_reagen" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Name</HeaderCell>
                <Cell dataKey="nama_reagen" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Catalog Number</HeaderCell>
                <Cell dataKey="no_katalog" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Manufacture</HeaderCell>
                <Cell dataKey="manufacture" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Reagen Type</HeaderCell>
                <Cell dataKey="reagen_type" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Reagen Category</HeaderCell>
                <Cell dataKey="reagen_category" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Created Date</HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell dataKey="created_by" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Updated Date</HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Deleted Date</HeaderCell>
                <Cell dataKey="deleted_dt" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Deleted By</HeaderCell>
                <Cell dataKey="deleted_by" />
              </Column>
              <Column width={100} sortable resizable>
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={100} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell style={{ padding: "8px" }}>
                  {(rowData) => (
                    <div>
                      <Button
                        appearance="link"
                        disabled={rowData.is_active === 0}
                        onClick={() => {
                          setShowEditModal(true);
                          setFormValue(rowData);
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        appearance="subtle"
                        onClick={() =>
                          handleEditStatusReagen(rowData.id_master_reagen)
                        }
                      >
                        {rowData.is_active === 1 ? (
                          <TrashIcon style={{ fontSize: "16px" }} />
                        ) : (
                          <ReloadIcon style={{ fontSize: "16px" }} />
                        )}
                      </Button>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* Add Modal Add Reagen Data*/}
        <Modal
          backdrop="static"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Add Master Reagen Data</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Reagen Name</Form.ControlLabel>
                <Form.Control
                  name="nama_reagen"
                  value={formValue.nama_reagen}
                  onChange={(value) => {
                    setFormValue({ ...formValue, nama_reagen: value });
                    // Clear error when user starts typing
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      nama_reagen: undefined,
                    }));
                  }}
                  errorMessage={formErrors.nama_reagen}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>No Katalog</Form.ControlLabel>
                <Form.Control
                  name="no_katalog"
                  value={formValue.no_katalog}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_katalog: value });
                    // Clear error when user starts typing
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      no_katalog: undefined,
                    }));
                  }}
                  errorMessage={formErrors.no_katalog}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Manufacture</Form.ControlLabel>
                <Form.Control
                  name="manufacture"
                  value={formValue.manufacture}
                  onChange={(value) => {
                    setFormValue({ ...formValue, manufacture: value });
                    // Clear error when user starts typing
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      manufacture: undefined,
                    }));
                  }}
                  errorMessage={formErrors.manufacture}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Reagen Type</Form.ControlLabel>
                <Form.Control
                  name="reagen_type"
                  value={formValue.reagen_type}
                  onChange={(value) => {
                    setFormValue({ ...formValue, reagen_type: value });
                    // Clear error when user starts typing
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      reagen_type: undefined,
                    }));
                  }}
                  errorMessage={formErrors.reagen_type}
                />
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Reagen Category</Form.ControlLabel>
                <Form.Control
                  name="reagen_category"
                  value={formValue.reagen_category}
                  onChange={(value) => {
                    setFormValue({ ...formValue, reagen_category: value });
                    // Clear error when user starts typing
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      reagen_category: undefined,
                    }));
                  }}
                  errorMessage={formErrors.reagen_category}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                // Clear form errors immediately
                setFormErrors({});

                // Hide modal and reset form values
                setShowAddModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>

            <Button
              onClick={() => {
                handleAddReagen();
              }}
              appearance="primary"
              type="submit"
            >
              Add
            </Button>
          </Modal.Footer>
        </Modal>

        <Modal
          backdrop="static"
          open={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Edit Reagen</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Reagen Name</Form.ControlLabel>
                <Form.Control
                  name="nama_reagen"
                  value={formValue.nama_reagen}
                  onChange={(value) => {
                    setFormValue({ ...formValue, nama_reagen: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      nama_reagen: undefined,
                    }));
                  }}
                  errorMessage={formErrors.nama_reagen}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Catalog Number</Form.ControlLabel>
                <Form.Control
                  name="no_katalog"
                  value={formValue.no_katalog}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_katalog: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      no_katalog: undefined,
                    }));
                  }}
                  errorMessage={formErrors.no_katalog}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Manufacture</Form.ControlLabel>
                <Form.Control
                  name="manufacture"
                  value={formValue.manufacture}
                  onChange={(value) => {
                    setFormValue({ ...formValue, manufacture: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      manufacture: undefined,
                    }));
                  }}
                  errorMessage={formErrors.manufacture}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Reagen Type</Form.ControlLabel>
                <Form.Control
                  name="reagen_type"
                  value={formValue.reagen_type}
                  onChange={(value) => {
                    setFormValue({ ...formValue, reagen_type: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      reagen_type: undefined,
                    }));
                  }}
                  errorMessage={formErrors.reagen_type}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Reagen Category</Form.ControlLabel>
                <Form.Control
                  name="reagen_category"
                  value={formValue.reagen_category}
                  onChange={(value) => {
                    setFormValue({ ...formValue, reagen_category: value });
                    setFormErrors((prevErrors) => ({
                      ...prevErrors,
                      reagen_category: undefined,
                    }));
                  }}
                  errorMessage={formErrors.reagen_category}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setFormErrors({});
                setShowEditModal(false);
                setFormValue(emptyFormValue);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleEditApi(formValue.id_master_reagen);
              }}
              appearance="primary"
              type="submit"
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
