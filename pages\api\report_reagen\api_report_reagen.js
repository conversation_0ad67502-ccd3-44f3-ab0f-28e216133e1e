import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/report-reagen/${url}`,
        data
    )
        .then((res) => {return res.data})
        .catch((err) => {return err.response.data});
    return response;
};

export default function ApiReportReagen(){
    return{
        getListExpired: createApiFunction("post", "expired-stock/list"),
        getListVendor: createApiFunction("post", "vendor-recap/list"),
        getListVendorPrices: createApiFunction("post", "vendor-recap-prices/list"),
        getListStockOpname: createApiFunction("post", "stock-opname/list"),
    }
}