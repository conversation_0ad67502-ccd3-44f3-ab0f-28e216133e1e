import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";

import {
  But<PERSON>,
  Stack,
  Panel,
  Breadcrumb,
  Form,
  InputGroup,
  SelectPicker,
  DatePicker,
  useToaster,
  Uploader,
  Divider,
  Modal,
} from "rsuite";

import API_TsMatrixCategoriesParameter from "@/pages/api/ts/api_ts-matrix-categories-parameter";
import API_TsMasterLine from "@/pages/api/ts/api_ts_master_line";
import API_TsDetail from "@/pages/api/ts/api_ts_detail";

import ContainerLayout from "@/components/layout/ContainerLayout";
import ExpandOutlineIcon from "@rsuite/icons/ExpandOutline";
import Messages from "@/components/Messages";

export default function TsEditManual() {
  const router = useRouter();

  const [moduleName, setModuleName] = useState("");
  const [currentPanel, setCurrentPanel] = useState("header");
  const [tsMatrixCategories, setTsMatrixCategories] = useState([]);
  const [tsManualMasterLine, setTsManualMasterLine] = useState([]);
  const [uploadedImages, setUploadedImages] = useState([
    { panel: "Binder", files: [] },
    { panel: "Granulasi", files: [] },
    { panel: "Pengeringan", files: [] },
  ]);

  const [modalGranulasi, setModalGranulasi] = useState(false);
  const [modalPengeringan, setModalPengeringan] = useState(false);
  const [modalPengayakan, setModalPengayakan] = useState(false);

  const [props, setProps] = useState([]);
  const [dataSpv, setDataSpv] = useState([]);
  const [dataProduct, setDataProduct] = useState([]);
  const toaster = useToaster();

  const [granulasiDetail, setGranulasiDetail] = useState([]);
  const [pengeringanDetail, setPengeringanDetail] = useState([]);
  const [pengayakanDetail, setPengayakanDetail] = useState([]);

  const [editIndex, setEditIndex] = useState(null);
  const handleEditDetail = (index, type) => {
    let detail;

    if (type === "granulasi") {
      detail = granulasiDetail[index];
      setModalGranulasi(true);

      setFormDetailValue((prev) => ({
        ...prev,
        id_matrix1: detail.id_matrix,
        id_categories1: detail.id_categories,
        value1: detail.value,
      }));
    } else if (type === "pengeringan") {
      detail = pengeringanDetail[index];
      setModalPengeringan(true);

      setFormDetailValue((prev) => ({
        ...prev,
        id_matrix2: detail.id_matrix,
        id_categories2: detail.id_categories,
        value2: detail.value,
      }));
    } else if (type === "pengayakan") {
      detail = pengayakanDetail[index];
      setModalPengayakan(true);

      setFormDetailValue((prev) => ({
        ...prev,
        id_matrix3: detail.id_matrix,
        id_categories3: detail.id_categories,
        value3: detail.value,
      }));
    }

    setEditIndex(index);
  };

  const handleDetailGranulasi = () => {
    const selectedMatrix = tsMatrixCategories.find(
      (item) => item.value === formDetailValue.id_matrix1
    );

    const newDetail = {
      id_matrix: formDetailValue.id_matrix1,
      matrix_label: selectedMatrix?.label,
      id_categories: selectedMatrix?.id_categories || null,
      value: formDetailValue.value1,
    };

    setGranulasiDetail((prevDetails) => [...prevDetails, newDetail]);
  };
  console.log("ini detail granulasi", granulasiDetail);

  const handleDetailPengeringan = () => {
    const selectedMatrix = tsMatrixCategories.find(
      (item) => item.value === formDetailValue.id_matrix2
    );

    const newDetail = {
      id_matrix: formDetailValue.id_matrix2,
      matrix_label: selectedMatrix?.label,
      id_categories: selectedMatrix?.id_categories || null,
      value: formDetailValue.value2,
    };

    setPengeringanDetail((prevDetails) => [...prevDetails, newDetail]);
  };

  console.log("ini detail pengeringan", pengeringanDetail);

  const handleDetailPengayakan = () => {
    const selectedMatrix = tsMatrixCategories.find(
      (item) => item.value === formDetailValue.id_matrix3
    );

    const newDetail = {
      id_matrix: formDetailValue.id_matrix3,
      matrix_label: selectedMatrix?.label,
      id_categories: selectedMatrix?.id_categories || null,
      value: formDetailValue.value3,
    };

    setPengayakanDetail((prevDetails) => [...prevDetails, newDetail]);
  };

  console.log("ini detail pengayakan", pengayakanDetail);

  const updatePanelImages = (panel, files) => {
    setUploadedImages((prevImages) =>
      prevImages.map((item) =>
        item.panel === panel ? { ...item, files } : item
      )
    );
  };

  const getPanelImages = (panel) => {
    return uploadedImages.find((item) => item.panel === panel)?.files || [];
  };

  const handleRemoveDetail = (indexToRemove, type) => {
    if (type === "granulasi") {
      setGranulasiDetail((prevDetails) =>
        prevDetails.filter((_, index) => index !== indexToRemove)
      );
    } else if (type === "pengeringan") {
      setPengeringanDetail((prevDetails) =>
        prevDetails.filter((_, index) => index !== indexToRemove)
      );
    } else if (type === "pengayakan") {
      setPengayakanDetail((prevDetails) =>
        prevDetails.filter((_, index) => index !== indexToRemove)
      );
    }
  };

  const emptyDetailForm = {
    id_matrix1: null,
    id_categories1: null,
    value1: null,

    id_matrix2: null,
    id_categories2: null,
    value2: null,

    id_matrix3: null,
    id_categories3: null,
    value3: null,
  };

  const [formDetailValue, setFormDetailValue] = useState(emptyDetailForm);

  const emptyForm = {
    id_line: null,
    sediaan_type: null,
    product_code: null,
    product_name: null,
    batch_no: null,
    production_scale: null,
    trial_focus: null,
    ppi_no: null,
    process_purpose: null,
    background: null,
    process_date: null,
    binder_date: null,
    binder_mix_amount: null,
    binder_mix_time: null,
    binder_remarks: null,
    granule_date: null,
    granule_ampere: null,
    granule_power: null,
    granule_remarks: null,
    drying_date: null,
    drying_lod: null,
    drying_product_temp: null,
    drying_exhaust_temp: null,
    drying_remarks: null,
    sifting_date: null,
    sifting_screen_quadro: null,
    sifting_bin_tumbler: null,
    sifting_impeller_speed_1: null,
    sifting_impeller_speed_2: null,
    sifting_remarks: null,
    final_mix_date: null,
    final_mix_time_mix_1: null,
    final_mix_time_mix_2: null,
    ts_conclusion: null,
    ts_followup: null,
    bobot_granul: null,
    bobot_teoritis: null,
    rendemen: null,
    discussion: null,
    analyzed_data: null,
    spv_employee_id: null,
    ts_detail_trans1: [],
    // ts_attachments1: [],
  };
  const [formValue, setFormValue] = useState(emptyForm);

  const handleAddManualApi = async () => {
    try {
      const ts_detail_trans1 = [
        ...granulasiDetail,
        ...pengeringanDetail,
        ...pengayakanDetail,
      ].filter((detail) => detail.id_matrix && detail.value);

      const headerPayload = {
        ...formValue,
        ts_created_by: props.employee_id,
        ts_detail_trans1,
      };

      const headerResponse =
        await API_TsDetail().postHeaderDetailAttachmentsManual(headerPayload);

      if (headerResponse.status === 200) {
        const id_header_trans1 = headerResponse.dataReturn.id_header_trans1;

        const uploadImages = async (images, id_header_trans1) => {
          if (!Array.isArray(images) || images.length === 0) return [];

          const uploadedPaths = await Promise.all(
            images.map(async (file) => {
              if (!file) return null;

              const formData = new FormData();
              formData.append("id_header_trans1", id_header_trans1);
              formData.append("type", file.currentPanel);
              formData.append("path", "ts");
              formData.append("created_by", props.employee_id);
              formData.set("Files", file.blobFile, file.name);

              try {
                const response = await API_TsDetail().postAttachmentManual(
                  formData
                );

                if (response.status === 200) {
                  const fileNameUUID = response.data.fileName;
                  return {
                    type: file.currentPanel,
                    path: `storage/ts/${id_header_trans1}/${fileNameUUID}`,
                  };
                } else {
                  console.error("Error uploading file:", response.data);
                }
              } catch (error) {
                console.error("Error uploading file:", error);
              }
              return null;
            })
          );

          return uploadedPaths.filter(Boolean);
        };

        await Promise.all(
          uploadedImages.map((panel) =>
            uploadImages(panel.files, id_header_trans1)
          )
        ).then((paths) => paths.flat());
        window.location.reload();

        toaster.push(Messages("success", "Success posting data!"), {
          placement: "topCenter",
          duration: 5000,
        });
        setFormValue(emptyForm);
        setFormDetailValue(emptyDetailForm);
        setUploadedImages([]);
        setGranulasiDetail([]);
        setPengeringanDetail([]);
        setPengayakanDetail([]);
      } else {
        toaster.push(
          Messages(
            "error",
            `Error creating header: "${headerResponse.message}". Please try again later!`
          ),
          { placement: "topCenter", duration: 5000 }
        );
      }
    } catch (error) {
      console.error("Axios error:", error);
      toaster.push(
        Messages("error", `Error: "${error.message}". Please try again later!`),
        { placement: "topCenter", duration: 5000 }
      );
    }
  };

  const SaveButton = () => (
    <Button
      appearance="primary"
      style={{ backgroundColor: "#1fd306" }}
      onClick={handleAddManualApi}
    >
      Save
    </Button>
  );

  const handleGetTsMatrixCategoriesApi = async () => {
    try {
      const response =
        await API_TsMatrixCategoriesParameter().getAllActiveTsMatrixCategoriesParameter();

      if (response.status === 200 && response.data) {
        const formattedData = response.data.map((item) => ({
          label: item.parameter_name,
          value: item.id_matrix,
          id_categories: item.id_categories,
          main_category_name: item.main_category_name,
        }));

        setTsMatrixCategories(formattedData);
      }
    } catch (error) {
      console.error("Error fetching matrix categories:", error);
    }
  };

  const handelGetManualMasterLineApi = async () => {
    try {
      const response = await API_TsMasterLine().getManualMasterLine();
      if (response.status === 200 && response.data) {
        const formatMasterLine = response.data.map((item) => ({
          label: item.line_description,
          value: item.id_line,
        }));
        setTsManualMasterLine(formatMasterLine);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("masterdata/TsMainCategories")
      );
      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetTsMatrixCategoriesApi();
      handelGetManualMasterLineApi();
      fetchData();
    }
  }, []);

  const fetchData = async () => {
    const resultSpv = await API_TsDetail().getAllActiveSpv();

    const resultProd = await API_TsDetail().getAllOfProduct();

    setDataSpv(resultSpv.data ? resultSpv.data : {});
    setDataProduct(resultProd.data ? resultProd.data : {});
  };

  const dataSkalaProduksi = ["Pilot", "Commercial"].map((item) => ({
    label: item,
    value: item,
  }));

  const dataJenisSediaan = ["Kapsul", "Tablet", "Sirup"].map((item) => ({
    label: item,
    value: item,
  }));

  const dataFokusTrial = ["Carry Over", "Diversifikasi", "Others"].map(
    (item) => ({ label: item, value: item })
  );

  const dataScreenQuadro = ["10", "20", "30"].map((item) => ({
    label: item,
    value: item,
  }));

  const dataBinTumbler = ["10", "20", "30"].map((item) => ({
    label: item,
    value: item,
  }));

  return (
    <>
      <div>
        <Head>
          <title>Ts Create Manual</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item active>
              {moduleName ? moduleName : "User Module"}
            </Breadcrumb.Item>
          </Breadcrumb>
          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h3>TS Create Manual Line</h3>
            </Stack>
          </Panel>

          <Form fluid>
            {currentPanel === "header" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between" className="mb-4">
                  <h4>Header</h4>
                </Stack>

                <Form.Group>
                  <Form.ControlLabel>
                    Line<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="id_line"
                    accepter={SelectPicker}
                    data={tsManualMasterLine}
                    valueKey="value"
                    labelKey="label"
                    block
                    value={formValue.id_line || ""}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        id_line: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Jenis Sediaan<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="sediaan_type"
                    accepter={SelectPicker}
                    data={dataJenisSediaan}
                    valueKey="label"
                    labelKey="value"
                    block
                    value={formValue.sediaan_type || ""}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        sediaan_type: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Kode Produk<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="product_code"
                    accepter={SelectPicker}
                    data={dataProduct}
                    valueKey="product_code"
                    labelKey="product_code"
                    block
                    value={formValue.product_code || ""}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        product_code: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Nama Produk<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="product_name"
                    value={formValue.product_name}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        product_name: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Kode Batch<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    // placeholder="batch_no"
                    value={formValue.batch_no}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        batch_no: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Skala Produk<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="production_scale"
                    accepter={SelectPicker}
                    value={formValue.production_scale}
                    data={dataSkalaProduksi}
                    valueKey="value"
                    labelKey="value"
                    block
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        production_scale: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Fokus Trial<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="trial_focus"
                    accepter={SelectPicker}
                    value={formValue.trial_focus}
                    data={dataFokusTrial}
                    valueKey="value"
                    labelKey="value"
                    block
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        trial_focus: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    No PPI<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    // placeholder="ppi_no"
                    value={formValue.ppi_no}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        ppi_no: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Tujuan Proses<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    // placeholder="process_purpose"
                    value={formValue.process_purpose}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        process_purpose: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Background<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    // placeholder="background"
                    value={formValue.background}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        background: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Tanggal Proses<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <DatePicker
                    oneTap
                    format="dd-MM-yyyy"
                    placeholder="Select Date"
                    value={formValue.process_date}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        process_date: value || null,
                      });
                    }}
                    block
                  />
                </Form.Group>

                <Stack justifyContent="end" className="mt-4">
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("binder")}
                  >
                    Lanjut Tahap Binder
                  </Button>
                </Stack>
                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}
            {currentPanel === "binder" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between" className="mb-4">
                  <h4>Binder</h4>
                </Stack>

                <Form.Group>
                  <Form.ControlLabel>
                    Tanggal Binder<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <DatePicker
                    oneTap
                    defaultValue={null}
                    format="dd-MM-yyyy"
                    placeholder="Select Date"
                    value={formValue.binder_date}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        binder_date: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Jumlah Pelarut<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="binder_mix_amount"
                      value={formValue.binder_mix_amount}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          binder_mix_amount: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> L</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Waktu Aduk<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="binder_mix_time"
                      value={formValue.binder_mix_time}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          binder_mix_time: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> Menit</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Binder Remarks<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="binder_remarks"
                    value={formValue.binder_remarks}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        binder_remarks: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Lampiran Binder<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Uploader
                    action=""
                    listType="picture-text"
                    defaultFileList={getPanelImages("Binder")}
                    appearance="ghost"
                    onChange={(fileList) => {
                      const updatedFileList = fileList.map((file) => ({
                        ...file,
                        currentPanel: "Binder",
                      }));
                      updatePanelImages("Binder", updatedFileList);
                    }}
                    onPreview={(file) => {
                      if (file.url || file.blobFile) {
                        const imageUrl =
                          file.url || URL.createObjectURL(file.blobFile);
                        const previewWindow = window.open(imageUrl, "_blank");
                        previewWindow?.focus();
                      } else {
                        toaster.push(
                          Messages("error", "Unable to preview file!"),
                          {
                            placement: "topCenter",
                          }
                        );
                      }
                    }}
                    renderFileInfo={(file) => <span>{file.name}</span>}
                  >
                    <Button>Ambil Gambar</Button>
                  </Uploader>
                </Form.Group>

                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("header")}
                  >
                    Kembali ke Tahap Header
                  </Button>
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("granulasi")}
                  >
                    Lanjut Tahap Granulasi
                  </Button>
                </Stack>
                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}
            {currentPanel === "granulasi" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between">
                  <h4 className="mb-4">Granulasi</h4>
                </Stack>
                <Form.Group>
                  <Form.ControlLabel>
                    Tanggal Granulasi<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <DatePicker
                    oneTap
                    format="dd-MM-yyyy"
                    placeholder="Select Date"
                    value={formValue.granule_date}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        granule_date: value,
                      });
                    }}
                    block
                  />
                </Form.Group>
                <Form.Group>
                  <Form.ControlLabel>
                    Ampere<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="granule_ampere"
                      value={formValue.granule_ampere}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          granule_ampere: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> A</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>
                <Form.Group>
                  <Form.ControlLabel>
                    Power<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="granule_power"
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          granule_power: parseFloat(value),
                        });
                      }}
                      value={formValue.granule_power}
                      type="number"
                    />
                    <InputGroup.Addon>
                      Kwh<span className="text-red-500">*</span>
                    </InputGroup.Addon>
                  </InputGroup>
                </Form.Group>
                <Form.Group>
                  <Form.ControlLabel>
                    Granulasi Remarks<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="granule_remarks"
                    value={formValue.granule_remarks}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        granule_remarks: value,
                      });
                    }}
                  />
                </Form.Group>
                <Form.Group>
                  <Form.ControlLabel>
                    Lampiran Granulasi<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Uploader
                    action=""
                    listType="picture-text"
                    defaultFileList={getPanelImages("Granulasi")}
                    appearance="ghost"
                    onChange={(fileList) => {
                      const updatedFileList = fileList.map((file) => ({
                        ...file,
                        currentPanel: "Granulasi",
                      }));
                      updatePanelImages("Granulasi", updatedFileList);
                    }}
                    renderFileInfo={(file) => <span>{file.name}</span>}
                  >
                    <Button>Ambil Gambar</Button>
                  </Uploader>
                </Form.Group>
                <Divider></Divider>
                <Form.Group>
                  <Form.ControlLabel>
                    <h4 className="mb-4">Detail Granulasi</h4>
                  </Form.ControlLabel>
                  <Button
                    appearance="primary"
                    startIcon={<ExpandOutlineIcon />}
                    onClick={() => setModalGranulasi(true)}
                  >
                    Tambah Detail Granulasi
                  </Button>
                </Form.Group>

                <Panel bordered>
                  {granulasiDetail.length > 0 ? (
                    granulasiDetail.map((detail, index) => (
                      <div key={index}>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                          }}
                        >
                          <div>
                            <p>Matrix: {detail.matrix_label}</p>
                            <p>Value: {detail.value}</p>
                          </div>
                          <div style={{ display: "flex", gap: "8px" }}>
                            <Button
                              appearance="primary"
                              color="blue"
                              size="xs"
                              onClick={() =>
                                handleEditDetail(index, "granulasi")
                              }
                            >
                              Edit
                            </Button>

                            <Button
                              appearance="primary"
                              color="red"
                              size="xs"
                              onClick={() =>
                                handleRemoveDetail(index, "granulasi")
                              }
                            >
                              Remove
                            </Button>
                          </div>
                        </div>
                        {index < granulasiDetail.length - 1 && (
                          <Divider style={{ margin: "10px 0" }} />
                        )}
                      </div>
                    ))
                  ) : (
                    <p>No Detail Added</p>
                  )}
                </Panel>

                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("binder")}
                  >
                    Kembali ke Tahap Binder
                  </Button>
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("pengeringan")}
                  >
                    Lanjut Tahap Pengeringan
                  </Button>
                </Stack>
                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}
            {currentPanel === "pengeringan" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between">
                  <h4 className="mb-4">Pengeringan</h4>
                </Stack>

                <Form.Group>
                  <Form.ControlLabel>
                    Tanggal Pengeringan<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <DatePicker
                    oneTap
                    format="dd-MM-yyyy"
                    placeholder="Select Date"
                    value={formValue.drying_date}
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        drying_date: value,
                      });
                    }}
                    block
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    LOD<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="drying_lod"
                      value={formValue.drying_lod}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          drying_lod: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> %</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Product Temp<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="drying_product_temp"
                      value={formValue.drying_product_temp}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          drying_product_temp: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> C</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Exhaust Temp<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="drying_exhaust_temp"
                      value={formValue.drying_exhaust_temp}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          drying_exhaust_temp: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> C</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Pengeringan Remarks<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    // placeholder="drying_remarks"
                    value={formValue.drying_remarks}
                    onChange={(value) => {
                      setFormValue({ ...formValue, drying_remarks: value });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Lampiran Pengeringan<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Uploader
                    action=""
                    listType="picture-text"
                    defaultFileList={getPanelImages("Pengeringan")}
                    appearance="ghost"
                    onChange={(fileList) => {
                      const updatedFileList = fileList.map((file) => ({
                        ...file,
                        currentPanel: "Pengeringan",
                      }));
                      updatePanelImages("Pengeringan", updatedFileList);
                    }}
                    renderFileInfo={(file) => <span>{file.name}</span>}
                  >
                    <Button>Ambil Gambar</Button>
                  </Uploader>
                </Form.Group>

                <Divider></Divider>

                <Form.Group>
                  <Form.ControlLabel>
                    <h4 className="mb-4">Detail Pengeringan</h4>
                  </Form.ControlLabel>
                  <Button
                    appearance="primary"
                    startIcon={<ExpandOutlineIcon />}
                    onClick={() => setModalPengeringan(true)}
                  >
                    Tambah Detail Pengeringan
                  </Button>
                </Form.Group>

                <Panel bordered>
                  {pengeringanDetail.length > 0 ? (
                    pengeringanDetail.map((detail, index) => (
                      <div key={index}>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                          }}
                        >
                          <div>
                            <p>Matrix: {detail.matrix_label}</p>
                            <p>Value: {detail.value}</p>
                          </div>
                          <div style={{ display: "flex", gap: "8px" }}>
                            <Button
                              appearance="primary"
                              color="blue"
                              size="xs"
                              onClick={() =>
                                handleEditDetail(index, "pengeringan")
                              }
                            >
                              Edit
                            </Button>

                            <Button
                              appearance="primary"
                              color="red"
                              size="xs"
                              onClick={() =>
                                handleRemoveDetail(index, "pengeringan")
                              }
                            >
                              Remove
                            </Button>
                          </div>
                        </div>
                        {index < pengeringanDetail.length - 1 && (
                          <Divider style={{ margin: "10px 0" }} />
                        )}
                      </div>
                    ))
                  ) : (
                    <p>No Detail Added</p>
                  )}
                </Panel>

                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("granulasi")}
                  >
                    Kembali ke Tahap Granulasi
                  </Button>
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("pengayakan")}
                  >
                    Lanjut Tahap Pengayakan
                  </Button>
                </Stack>

                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}
            {currentPanel === "pengayakan" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between">
                  <h4 className="mb-4">Pengayakan</h4>
                </Stack>

                <Form.Group>
                  <Form.ControlLabel>
                    Tanggal Pengayakan<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <DatePicker
                    oneTap
                    format="dd-MM-yyyy"
                    placeholder="Select Date"
                    value={formValue.sifting_date}
                    onChange={(value) => {
                      setFormValue({ ...formValue, sifting_date: value });
                    }}
                    block
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Screen Quadro<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="sifting_screen_quadro"
                    accepter={SelectPicker}
                    value={formValue.sifting_screen_quadro}
                    data={dataScreenQuadro}
                    valueKey="value"
                    labelKey="value"
                    block
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        sifting_screen_quadro: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Bin Tumbler<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="sifting_bin_tumbler"
                    accepter={SelectPicker}
                    value={formValue.sifting_bin_tumbler}
                    data={dataBinTumbler}
                    valueKey="value"
                    labelKey="value"
                    block
                    onChange={(value) => {
                      setFormValue({
                        ...formValue,
                        sifting_bin_tumbler: value,
                      });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Impeller Speed Quadro 1
                    <span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="sifting_impeller_speed_1"
                      value={formValue.sifting_impeller_speed_1}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          sifting_impeller_speed_1: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> RPM</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Impeller Speed Quadro 2
                    <span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="sifting_impeller_speed_2"
                      value={formValue.sifting_impeller_speed_2}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          sifting_impeller_speed_2: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> RPM</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Pengayakan Remarks<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    // placeholder="sifting_remarks"
                    value={formValue.sifting_remarks}
                    onChange={(value) => {
                      setFormValue({ ...formValue, sifting_remarks: value });
                    }}
                  />
                </Form.Group>

                <Divider></Divider>

                <Form.Group>
                  <Form.ControlLabel>
                    <h4 className="mb-4">Detail Pengayakan</h4>
                  </Form.ControlLabel>
                  <Button
                    appearance="primary"
                    startIcon={<ExpandOutlineIcon />}
                    onClick={() => setModalPengayakan(true)}
                  >
                    Tambah Detail Pengayakan
                  </Button>
                </Form.Group>

                <Panel bordered>
                  {pengayakanDetail.length > 0 ? (
                    pengayakanDetail.map((detail, index) => (
                      <div key={index}>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                          }}
                        >
                          <div>
                            <p>ID Matrix: {detail.id_matrix}</p>
                            <p>Matrix: {detail.matrix_label}</p>
                            <p>Value: {detail.value}</p>
                          </div>
                          <div style={{ display: "flex", gap: "8px" }}>
                            <Button
                              appearance="primary"
                              color="blue"
                              size="xs"
                              onClick={() =>
                                handleEditDetail(index, "pengayakan")
                              }
                            >
                              Edit
                            </Button>
                            <Button
                              appearance="primary"
                              color="red"
                              size="xs"
                              onClick={() =>
                                handleRemoveDetail(index, "pengayakan")
                              }
                            >
                              Remove
                            </Button>
                          </div>
                        </div>
                        {index < pengayakanDetail.length - 1 && (
                          <Divider style={{ margin: "10px 0" }} />
                        )}
                      </div>
                    ))
                  ) : (
                    <p>No Detail Added</p>
                  )}
                </Panel>

                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("pengeringan")}
                  >
                    Kembali ke Tahap Pengeringan
                  </Button>
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("finalmix")}
                  >
                    Lanjut Tahap Final Mix
                  </Button>
                </Stack>

                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}
            {currentPanel === "finalmix" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between">
                  <h5 className="mb-4">Final Mix</h5>
                </Stack>

                <Form.Group>
                  <Form.ControlLabel>
                    Tanggal Final Mix<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <DatePicker
                    oneTap
                    format="dd-MM-yyyy"
                    placeholder="Select Date"
                    value={formValue.final_mix_date}
                    onChange={(value) => {
                      setFormValue({ ...formValue, final_mix_date: value });
                    }}
                    block
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Waktu Aduk 1<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="final_mix_time_mix_1"
                      value={formValue.final_mix_time_mix_1}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          final_mix_time_mix_1: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> menit</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Waktu Aduk 2<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="final_mix_time_mix_2"
                      value={formValue.final_mix_time_mix_2}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          final_mix_time_mix_2: parseFloat(value),
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> menit</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Kesimpulan<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    // placeholder="ts_conclusion"
                    value={formValue.ts_conclusion}
                    onChange={(value) => {
                      setFormValue({ ...formValue, ts_conclusion: value });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Tindak Lanjut<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    // placeholder="ts_followup"
                    value={formValue.ts_followup}
                    onChange={(value) => {
                      setFormValue({ ...formValue, ts_followup: value });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Bobot Granul<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="bobot_granul"
                      value={formValue.bobot_granul}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          bobot_granul: parseFloat(value),
                          rendemen:
                            (parseFloat(value) / formValue.bobot_teoritis) *
                            100,
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> kg</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Bobot Teoritis<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="bobot_teoritis"
                      value={formValue.bobot_teoritis}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          bobot_teoritis: parseFloat(value),
                          rendemen:
                            (formValue.bobot_granul / parseFloat(value)) * 100,
                        });
                      }}
                      type="number"
                    />
                    <InputGroup.Addon> kg</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Rendemen<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <InputGroup>
                    <Form.Control
                      // placeholder="rendemen"
                      value={formValue.rendemen}
                      onChange={(value) => {
                        setFormValue({
                          ...formValue,
                          rendemen: parseFloat(value),
                        });
                      }}
                      type="number"
                      readOnly={true}
                    />
                    <InputGroup.Addon> %</InputGroup.Addon>
                  </InputGroup>
                </Form.Group>

                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("pengayakan")}
                  >
                    Kembali ke Tahap Pengayakan
                  </Button>
                  <Button
                    appearance="primary"
                    onClick={() => setCurrentPanel("analisadata")}
                  >
                    Lanjut Tahap Analisa Data
                  </Button>
                </Stack>

                <Stack justifyContent="end" className="mt-2">
                  <SaveButton />
                </Stack>
              </Panel>
            )}
            {currentPanel === "analisadata" && (
              <Panel bordered className="mb-2">
                <Stack justifyContent="space-between">
                  <h5 className="mb-4">Analisa Data</h5>
                </Stack>

                <Form.Group>
                  <Form.ControlLabel>
                    Diskusi<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    // placeholder="discussion"
                    value={formValue.discussion || ""}
                    onChange={(value) => {
                      setFormValue({ ...formValue, discussion: value || "" });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Analisa Data<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    // placeholder="analyzed_data"
                    value={formValue.analyzed_data || ""}
                    onChange={(value) => {
                      setFormValue({ ...formValue, analyzed_data: value });
                    }}
                  />
                </Form.Group>

                <Form.Group>
                  <Form.ControlLabel>
                    Spv<span className="text-red-500">*</span>
                  </Form.ControlLabel>
                  <Form.Control
                    name="spv_employee_id"
                    accepter={SelectPicker}
                    value={formValue.spv_employee_id}
                    data={dataSpv}
                    valueKey="employee_id"
                    labelKey="employee_name"
                    block
                    disabled={
                      Object.entries(formValue).some(
                        ([key, value]) =>
                          key !== "spv_employee_id" && (!value || value === "")
                      ) ||
                      !granulasiDetail ||
                      granulasiDetail === "" ||
                      !pengeringanDetail ||
                      pengeringanDetail === "" ||
                      !pengayakanDetail ||
                      pengayakanDetail === ""
                    }
                    onChange={(value) => {
                      setFormValue({ ...formValue, spv_employee_id: value });
                    }}
                  />
                </Form.Group>

                <Stack justifyContent="space-between" className="mt-4">
                  <Button
                    appearance="default"
                    onClick={() => setCurrentPanel("finalmix")}
                  >
                    Kembali ke Tahap Final Mix
                  </Button>
                  <Button
                    appearance="primary"
                    style={{ backgroundColor: "#1fd306" }}
                    onClick={handleAddManualApi}
                  >
                    Save
                  </Button>
                </Stack>
              </Panel>
            )}
          </Form>
        </div>

        {/* Modal Granulasi */}
        <Modal open={modalGranulasi} onClose={() => setModalGranulasi(false)}>
          <Modal.Header>
            <Modal.Title>Detail Granulasi</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Matrix</Form.ControlLabel>
                <Form.Control
                  name="id_matrix1"
                  accepter={SelectPicker}
                  data={tsMatrixCategories.filter(
                    (item) => item.main_category_name === "Granulasi"
                  )}
                  valueKey="value"
                  labelKey="label"
                  block
                  value={formDetailValue.id_matrix1 || ""}
                  onChange={(value) => {
                    const selectedMatrix = tsMatrixCategories.find(
                      (item) => item.value === value
                    );

                    const selectedCategory =
                      selectedMatrix?.id_categories || null;

                    setFormDetailValue({
                      ...formDetailValue,
                      id_matrix1: value,
                      id_categories1: selectedCategory,
                    });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Value</Form.ControlLabel>
                <Form.Control
                  name="value1"
                  value={formDetailValue.value1 || ""}
                  onChange={(value) =>
                    setFormDetailValue({ ...formDetailValue, value1: value })
                  }
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              appearance="primary"
              disabled={!formDetailValue.id_matrix1}
              onClick={() => {
                if (editIndex !== null) {
                  setGranulasiDetail((prevDetails) => {
                    const updatedDetails = [...prevDetails];
                    updatedDetails[editIndex] = {
                      id_matrix: formDetailValue.id_matrix1,
                      matrix_label: tsMatrixCategories.find(
                        (item) => item.value === formDetailValue.id_matrix1
                      )?.label,
                      value: formDetailValue.value1,
                      id_categories: formDetailValue.id_categories1,
                    };
                    return updatedDetails;
                  });
                  setEditIndex(null); 
                } else {
                  handleDetailGranulasi(); 
                }
                setModalGranulasi(false);
                setFormDetailValue(emptyDetailForm); 
              }}
            >
              Save
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Modal Pengeringan */}
        <Modal
          open={modalPengeringan}
          onClose={() => setModalPengeringan(false)}
        >
          <Modal.Header>
            <Modal.Title>Detail Pengeringan</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Matrix</Form.ControlLabel>
                <Form.Control
                  name="id_matrix2"
                  accepter={SelectPicker}
                  data={tsMatrixCategories.filter(
                    (item) => item.main_category_name === "Pengeringan"
                  )}
                  valueKey="value"
                  labelKey="label"
                  block
                  value={formDetailValue.id_matrix2 || ""}
                  onChange={(value) => {
                    const selectedMatrix = tsMatrixCategories.find(
                      (item) => item.value === value
                    );

                    const selectedCategory =
                      selectedMatrix?.id_categories || null;

                    setFormDetailValue({
                      ...formDetailValue,
                      id_matrix2: value,
                      id_categories2: selectedCategory,
                    });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Value</Form.ControlLabel>
                <Form.Control
                  name="value2"
                  value={formDetailValue.value2 || ""}
                  onChange={(value) =>
                    setFormDetailValue({ ...formDetailValue, value2: value })
                  }
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              appearance="primary"
              disabled={!formDetailValue.id_matrix2}
              onClick={() => {
                if (editIndex !== null) {
                  setPengeringanDetail((prevDetails) => {
                    const updatedDetails = [...prevDetails];
                    updatedDetails[editIndex] = {
                      id_matrix: formDetailValue.id_matrix2,
                      matrix_label: tsMatrixCategories.find(
                        (item) => item.value === formDetailValue.id_matrix2
                      )?.label,
                      value: formDetailValue.value2,
                      id_categories: formDetailValue.id_categories2,
                    };
                    return updatedDetails;
                  });
                  setEditIndex(null);
                } else {
                  handleDetailPengeringan();
                }
                setModalPengeringan(false);
                setFormDetailValue(emptyDetailForm);
              }}
            >
              Save
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Modal Pengayakan */}
        <Modal open={modalPengayakan} onClose={() => setModalPengayakan(false)}>
          <Modal.Header>
            <Modal.Title>Detail Pengayakan</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Matrix</Form.ControlLabel>
                <Form.Control
                  name="id_matrix3"
                  accepter={SelectPicker}
                  data={tsMatrixCategories.filter(
                    (item) => item.main_category_name === "Pengayakan"
                  )}
                  valueKey="value"
                  labelKey="label"
                  block
                  value={formDetailValue.id_matrix3 || ""}
                  onChange={(value) => {
                    const selectedMatrix = tsMatrixCategories.find(
                      (item) => item.value === value
                    );

                    const selectedCategory =
                      selectedMatrix?.id_categories || null;

                    setFormDetailValue({
                      ...formDetailValue,
                      id_matrix3: value,
                      id_categories3: selectedCategory,
                    });
                  }}
                />
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Value</Form.ControlLabel>
                <Form.Control
                  name="value3"
                  value={formDetailValue.value3 || ""}
                  onChange={(value) =>
                    setFormDetailValue({ ...formDetailValue, value3: value })
                  }
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              appearance="primary"
              disabled={!formDetailValue.id_matrix3}
              onClick={() => {
                if (editIndex !== null) {
                  setPengayakanDetail((prevDetails) => {
                    const updatedDetails = [...prevDetails];
                    updatedDetails[editIndex] = {
                      id_matrix: formDetailValue.id_matrix3,
                      matrix_label: tsMatrixCategories.find(
                        (item) => item.value === formDetailValue.id_matrix3
                      )?.label,
                      value: formDetailValue.value3,
                      id_categories: formDetailValue.id_categories3,
                    };
                    return updatedDetails;
                  });
                  setEditIndex(null);
                } else {
                  handleDetailPengayakan();
                }
                setModalPengayakan(false);
                setFormDetailValue(emptyDetailForm);
              }}
            >
              Save
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps(context) {
  const { query } = context;
  const data = query.data || "";

  return {
    props: {
      data,
    },
  };
}
