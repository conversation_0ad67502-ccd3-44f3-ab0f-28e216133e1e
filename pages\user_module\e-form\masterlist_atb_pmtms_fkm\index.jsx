import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import Head from "next/head";
import {
  But<PERSON>,
  Stack,
  Breadcrumb,
  Panel,
  ButtonGroup,
  Placeholder,
  Tag,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";

const Loading = () => <Placeholder.Graph active />;
const ErrorFallback = () => <div>Failed to load component.</div>;

const MasterlistAtbPmtmsFkm = () => {
  const router = useRouter();
  const [moduleName, setModuleName] = useState("");
  const [step, setStep] = useState(0);

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");

    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("e-form/masterlist_atb_pmtms_fkm" || "e-form")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
    }
  }, []);

  const TableComponent = dynamic(
    () => {
      return step === 0
        ? import("@/components/table/atb_pmtms_table")
        : import("@/components/table/capa_table");
    },
    {
      loading: Loading,
      ssr: false, // Disable server-side rendering for dynamic import
      fallback: <ErrorFallback />,
    }
  );

  return (
    <>
      <Head>
        <title>E-Form Masterlist TB PMTMS FKM</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <div className="flex justify-between">
            <div>
              <Breadcrumb>
                <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
                <Breadcrumb.Item href="/user_module?module_code=4&module_name=Quality">
                  Quality
                </Breadcrumb.Item>
                <Breadcrumb.Item>E-Form</Breadcrumb.Item>
                <Breadcrumb.Item active>
                  E-Form Masterlist TB, PMTMS, FKM
                </Breadcrumb.Item>
              </Breadcrumb>
            </div>

            <div>
              <Tag color="green">Module: {moduleName}</Tag>
            </div>
          </div>

          <Panel
            bordered
            bodyFill
            className="my-4"
            header={
              <Stack justifyContent="space-between">
                <div>
                  <h5>E-Form Masterlist ATB, PMTMS, FKM</h5>
                </div>
                <ButtonGroup>
                  <Button
                    onClick={() => {
                      setStep(0);
                    }}
                    active={step === 0}
                    appearance="primary"
                  >
                    ATB PMTMS
                  </Button>
                  <Button
                    onClick={() => {
                      setStep(1);
                    }}
                    active={step === 1}
                    appearance="primary"
                  >
                    CAPA
                  </Button>
                </ButtonGroup>
              </Stack>
            }
          ></Panel>

          <React.Suspense fallback={<Loading />}>
            <TableComponent />
          </React.Suspense>
        </div>
      </ContainerLayout>
    </>
  );
};

export default MasterlistAtbPmtmsFkm;
