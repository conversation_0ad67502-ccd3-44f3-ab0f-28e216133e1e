import { useState, useEffect } from "react";

import MenuApi from "../api/menuApi";

export default function UserModuleData(props) {
  console.log(props)
  const [data, setData] = useState();
  const { GetUserMenu } = MenuApi();

  // Get User Menu Data
  const GetUserMenuData = async () => {
    console.log(props)
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    let userData = {
      employee_id: dataLogin.employee_id,
      module_code: parseInt(props.moduleCode),
    };
    console.log(userData)
    let datas = await GetUserMenu(userData);
    const { Data } = datas;
    console.log("first")
    console.log(Data)
    if (Data) {
      console.log("sini bro")
      setData(Data);
    }else{
      console.log("ko disini")
    }
  };

  // useEffect(() => {
  //   const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
  //   if (!dataLogin) {
  //     router.push("/");
  //   }
  // }, []);

  // useEffect getUserMenuData
  useEffect(() => {
    GetUserMenuData();
  }, []);

  // useeffect sendData to Handler
  useEffect(() => {
    console.log("kirim data")
    console.log(data)
    if (data) {
      // Membuat array baru dengan menambahkan section untuk pembagian menu
      let index = 0;
      let item;
      let indexSection = 1;
      let resultData = [];
      for (index; index < data.length; index++) {
        item = data[index];
        if (index === 0) {
          item.section = indexSection;
          resultData.push(item);
          continue;
        }
        if (item.Is_Parent !== 1) {
          item.section = indexSection;
          resultData.push(item);
        } else {
          indexSection += 1;
          item.section = indexSection;
          resultData.push(item);
        }
      }

      props.handler(resultData);
    }
  }, [data]);
}
