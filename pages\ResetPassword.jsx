import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import styles from "../components/LoginForm.module.css";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import UseTestApi from "./api/userApi";
import Head from "next/head";

export default function ResetPassword({ id, token }) {
  const router = useRouter();
  const MySwal = withReactContent(Swal);
  const [isPageShown, setIsPageShown] = useState(false);
  const [password1, setPassword1] = useState("");
  const [password1IsInvalid, setPassword1IsInvalid] = useState(false);
  const [isPasswordMatched, setIsPasswordMatched] = useState(true);
  const [isPasswordValid, setIsPasswordValid] = useState(true);
  const [password1InvalidMessage, setPassword1InvalidMessage] = useState("");
  const [password2, setPassword2] = useState("");
  const [password2IsInvalid, setPassword2IsInvalid] = useState(false);
  const [password2InvalidMessage, setPassword2InvalidMessage] = useState("");
  const [buttonDisabled, setButtonDisabled] = useState(true);
  const { ResetPasswordApi } = UseTestApi();

  const password1ChangeHandler = (event) => {
    setPassword1(event.target.value);
  };

  const password2ChangeHandler = (event) => {
    setPassword2(event.target.value);
  };

  useEffect(() => {
    if (id && token) {
      setIsPageShown(true);
    } else {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Reset Token is NOT Valid !",
        timer: 3000,
        allowOutsideClick: false,
      });
      setIsPageShown(false);
      router.push("/");
    }
  }, []);

  // Mengecek kesamaan password
  useEffect(() => {
    if (password1.length > 0 || password2.length > 0) {
      if (password1 !== password2) {
        setIsPasswordMatched(false);
      } else {
        setIsPasswordMatched(true);
      }
      return;
    }
    setIsPasswordMatched(true);
    return;
  }, [password1, password2]);

  // Mengecek validitas password
  useEffect(() => {
    if (password1.length > 0) {
      const passwordToCheck = password1;
      var minimumChars = 6;
      var regularExpression =
        /^(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{6,16}$/;

      if (
        passwordToCheck.length < minimumChars ||
        !regularExpression.test(passwordToCheck)
      ) {
        setIsPasswordValid(false);
        return;
      }
      setIsPasswordValid(true);
      return;
    }
    setIsPasswordValid(true);
    return;
  }, [password1]);

  const submitHandler = async (event) => {
    event.preventDefault();

    if (!isPasswordValid) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Invalid PASSWORD !",
      });
      setIsFormDisabled(false);
      return;
    }

    if (!isPasswordMatched) {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Password is not the same !",
      });
      setIsFormDisabled(false);
      return;
    }

    const reqData = {
      employee_id: id,
      reset_token: parseInt(token),
      new_password: password1,
    };

    const { Data: resetPasswordResult } = await ResetPasswordApi(reqData);

    if (resetPasswordResult) {
      router.push("/");
      MySwal.fire({
        position: "center",
        icon: "success",
        title: "Success !",
        text: "Your new password has been set.",
        allowOutsideClick: false,
        showConfirmButton: false,
        timer: 5000,
      });
    } else {
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "Something went wrong !",
      });
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Reset Password</title>
        </Head>
      </div>
      {isPageShown && (
        <div className="container-fluid d-flex align-items-center justify-content-center h-100 mb-5">
          <div className={`card mt-5 p-5 shadow-lg ${styles.customCardWidth}`}>
            <div className="text-center card-title">
              <h3>Reset Password</h3>
            </div>
            <form className="p-5" onSubmit={submitHandler}>
              <div className="mb-3 has-validation">
                <label
                  htmlFor="exampleInputPassword1"
                  className="form-label"
                  style={{ fontSize: "0.8em" }}
                >
                  New Password
                </label>
                <input
                  type="password"
                  className={`form-control ${
                    !isPasswordMatched || !isPasswordValid ? "is-invalid" : ""
                  }`}
                  id="exampleInputPassword1"
                  aria-describedby="emailHelp"
                  value={password1}
                  onChange={password1ChangeHandler}
                  autoComplete="off"
                  autoFocus={true}
                  required
                />
                {password1IsInvalid && (
                  <div className="invalid-feedback">
                    {password1InvalidMessage}
                  </div>
                )}
              </div>
              <div className="mb-3 has-validation">
                <label
                  htmlFor="exampleInputPassword2"
                  className="form-label"
                  style={{ fontSize: "0.8em" }}
                >
                  Re-type New Password
                </label>
                <input
                  type="password"
                  className={`form-control ${
                    !isPasswordMatched || !isPasswordValid ? "is-invalid" : ""
                  }`}
                  id="exampleInputPassword2"
                  aria-describedby="emailHelp"
                  value={password2}
                  onChange={password2ChangeHandler}
                  autoComplete="off"
                  required
                />
                {/* {password2IsInvalid && (
                  <div className="invalid-feedback">
                    {password2InvalidMessage}
                  </div>
                )} */}
                <div style={{ color: "tomato" }}>
                  Password requirements :
                  <ul>
                    <li>6 characters minimum</li>
                    <li>
                      Contain minimum 1 uppercase character, 1 lowercase
                      character, number, and symbol (!@#$%^&*)
                    </li>
                  </ul>
                </div>
              </div>

              <div className="d-grid gap-2">
                <button
                  type="submit"
                  disabled={isPasswordMatched && isPasswordValid ? false : true}
                  className="btn btn-primary"
                >
                  Change Password
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
}

export async function getServerSideProps(context) {
  const { query } = context;
  const { employee_id, reset_token } = query;
  const { ValidateTokenAPI } = UseTestApi();

  const reqData = {
    employee_id,
    reset_token: parseInt(reset_token),
  };

  const { Data: dataValidation } = await ValidateTokenAPI(reqData);

  if (dataValidation) {
    return {
      props: {
        id: employee_id,
        token: reset_token,
      },
    };
  } else {
    return {
      props: {
        id: null,
        token: null,
      },
    };
  }
}
