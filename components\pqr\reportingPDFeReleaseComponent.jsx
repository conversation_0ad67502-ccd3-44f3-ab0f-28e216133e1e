import { useEffect, useState } from "react";
import Head from "next/head";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "rsuite";
import { faFileDownload } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import ApiReporting from "@/pages/api/pqr/reporting_list/api_reporting";
import ApiStep from "@/pages/api/pqr/step/api_masterdata_step";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";

pdfMake.vfs = pdfFonts.pdfMake.vfs;

export default function PdfReportingOperationalComponent({ idTransHeader, sessionAuth }) {
    const [headerData, setHeaderData] = useState({});
    const [groupedDetailData, setGroupedDetailData] = useState([]);
    const [orderedSteps, setOrderedSteps] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    const handleGetAllSteps = async (id_ppi) => {
        try {
            const res = await ApiStep().getAllActiveStepPPI({ id_ppi: parseInt(id_ppi) });
            if (res.status === 200 && Array.isArray(res.data)) {

                const sortedSteps = res.data.sort((a, b) => a.order_no - b.order_no);
                setOrderedSteps(sortedSteps);
            } else {
                console.error("Gagal mengambil daftar step:", res.message);
            }
        } catch (error) {
            console.error("Terjadi error saat mengambil daftar step:", error);
        }
    };


    const handleGetTransactionData = async (id_trans_header) => {
        setIsLoading(true);
        try {
            const apiReporting = ApiReporting();
            const response = await apiReporting.getPPITransactionHeaderDetail({
                id_trans_header: parseInt(id_trans_header),
            });

            if (response.status === 200 && response.data) {
                const data = response.data;

                setHeaderData({
                    batch_code: data.batch_code,
                    ppi_name: data.ppi_name,
                    wetmill: data.wetmill === 'Y' ? 'YES' : 'NO',
                    pembuatan: `${data.header_create_by} pada ${formatDate(data.header_create_date)}`,
                    persetujuan: data.approval_by ? `${data.approval_by} pada ${formatDate(data.approval_date)}` : "-",
                });
                setGroupedDetailData(data.detail || []);
                if (data.id_ppi) {
                    await handleGetAllSteps(data.id_ppi);
                }

            } else {
                console.error("Gagal mengambil data detail transaksi:", response.message);
            }
        } catch (error) {
            console.error("Terjadi error saat mengambil data detail transaksi:", error);
        } finally {
            setIsLoading(false);
        }
    };


    useEffect(() => {
        if (idTransHeader) {
            handleGetTransactionData(idTransHeader);
        }
    }, [idTransHeader]);


    const formatDate = (dateStr) => {
        if (!dateStr) return "-";
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return "-";

        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();

        return `${day}-${month}-${year}`;
    };


    const getSyaratValue = (param) => {
        if (!param) return "-";
        switch (param.binding_type) {
            case 1: return `${param.min_value} - ${param.max_value}`;
            case 2: return param.absolute_value?.toString();
            case 3: return param.description_value;
            case 0: return param.set_point_value;
            default: return "-";
        }
    };


    const getNilaiActualValue = (param) => {
        if (!param) return "-";

        let value = (param.binding_type === 0 && param.set_point_flag === 1)
            ? param.set_point_value
            : param.actual_value;

        const numericValue = parseFloat(value);

        if (!isNaN(numericValue)) {
            return Math.round(numericValue * 100) / 100;
        }

        return value;
    };



    const generatePdf = (action) => {
        if (isLoading || groupedDetailData.length === 0) {
            alert("Data belum siap atau tidak ditemukan untuk membuat PDF.");
            return;
        }

        let pdfContent = [];
        const now = new Date();
        const providedBy = sessionAuth?.employee_name;
        const providedDate = formatDate(now.toISOString());

        pdfContent.push({
            stack: [
                { text: 'LAPORAN E-RELEASE', style: 'mainTitle', alignment: 'center' },
                { text: 'Hasil Perekaman Operasional', style: 'subTitle', alignment: 'center' },
            ],
            margin: [0, 0, 0, 20]
        });

        pdfContent.push({
            style: 'infoSection',
            table: {
                widths: ['auto', 'auto', '*'],
                body: [
                    ['Batch Code', ':', { text: headerData.batch_code, bold: true }],
                    ['PPI', ':', { text: headerData.ppi_name, bold: true }],
                    ['Keterangan Wetmill', ':', { text: headerData.wetmill, bold: true }],
                    ['Keterangan Pembuatan', ':', { text: headerData.pembuatan, bold: true }],
                    ['Keterangan Persetujuan', ':', { text: headerData.persetujuan, bold: true }],
                    [{ text: `Dokumen ini di sediakan oleh ${providedBy} pada ${providedDate}`, colSpan: 3, fontSize: 9 }, '', '']]
            },
            layout: 'noBorders'
        });

        orderedSteps.forEach(step => {
            const transactionStepData = groupedDetailData.find(d => d.step_name === step.step_name);

            if (transactionStepData && transactionStepData.detail_step && transactionStepData.detail_step.length > 0) {
                pdfContent.push({ text: transactionStepData.step_name, style: 'stepHeader' });

                const sortedParams = transactionStepData.detail_step.sort((a, b) => a.id_recipe_param - b.id_recipe_param);

                const tableBody = [
                    [
                        { text: "No", style: "tableHeader" },
                        { text: "Nama Parameter", style: "tableHeader" },
                        { text: "Syarat", style: "tableHeader" },
                        { text: "Nilai Actual", style: "tableHeader" },
                        { text: "Hasil Pengukuran", style: "tableHeader" },
                        { text: "Dilakukan oleh", style: "tableHeader" },
                    ],
                    ...sortedParams.map((param, index) => [
                        { text: index + 1, style: "tableCell", alignment: 'center' },
                        { text: param.parameter_name, style: "tableCell" },
                        { text: getSyaratValue(param), style: "tableCell", alignment: 'center' },
                        { text: getNilaiActualValue(param), style: "tableCell", alignment: 'center' },
                        { text: param.result, style: "tableCell", alignment: 'center' },
                        { text: `${param.detail_create_by} pada ${formatDate(param.detail_create_date)}`, style: "tableCell" },
                    ])
                ];

                pdfContent.push({
                    table: {
                        headerRows: 1,
                        widths: ['auto', '*', 'auto', 'auto', 'auto', '*'],
                        body: tableBody,
                    },
                    layout: {
                        hLineWidth: (i, node) => (i === 0 || i === node.table.body.length || i === 1) ? 1 : 0.5,
                        vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0.5,
                        hLineColor: () => '#000000',
                        vLineColor: () => '#000000',
                        paddingTop: () => 4,
                        paddingBottom: () => 4,
                    }
                });
            }
        });



        const docDefinition = {
            pageSize: 'A4',
            pageMargins: [40, 40, 40, 60],
            header: {
                stack: [
                    { text: 'LAPORAN e-Release', style: 'mainTitle', alignment: 'center' },
                    { text: 'Hasil Perekaman Operasional', style: 'subTitle', alignment: 'center' },
                ],
                margin: [40, 40, 40, 10]
            },
            footer: (currentPage, pageCount) => ({
                columns: [
                    { text: `Dokumen ini di sediakan oleh ${providedBy} pada ${providedDate}`, alignment: 'left', style: 'footer' },
                    { text: `Halaman ${currentPage} dari ${pageCount}`, alignment: 'right', style: 'footer' }
                ],
                margin: [40, 10, 40, 0]
            }),
            content: pdfContent,
            styles: {
                mainTitle: { fontSize: 16, bold: true, margin: [0, 0, 0, 5] },
                infoSection: { fontSize: 10, margin: [0, 0, 0, 10] },
                stepHeader: { fontSize: 11, bold: true, margin: [0, 20, 0, 10] },
                tableHeader: { bold: true, fontSize: 9, color: 'black', alignment: 'center' },
                tableCell: { fontSize: 9 },
                footer: { fontSize: 8, italics: true }
            },
        };

        if (action === "Download") {
            pdfMake.createPdf(docDefinition).download(`Laporan_Operasional_${headerData.batch_code}.pdf`);
        } else {
            pdfMake.createPdf(docDefinition).open();
        }
    };

    return (
        <div
            style={{
                width: "100%",
                padding: "1em",
                backgroundColor: "#2c2c30",
                position: "sticky",
                top: 0,
                zIndex: 1000
            }}
        >
            <Head>
                <title>Laporan Operasional</title>
            </Head>
            <Stack justifyContent="space-between">
                <p style={{ color: "white", fontSize: "1em" }}>
                    Laporan Operasional - Transaksi ID: {idTransHeader}
                </p>
                <Stack>
                    <Button onClick={() => generatePdf("preview")} disabled={isLoading} style={{ marginRight: "5px" }}>
                        {isLoading ? "Memuat Data..." : "Preview"}
                    </Button>
                    <Button onClick={() => generatePdf("Download")} disabled={isLoading}>
                        <FontAwesomeIcon icon={faFileDownload} style={{ fontSize: 15, marginRight: '5px' }} />
                        {isLoading ? "Memuat..." : "Download"}
                    </Button>
                </Stack>
            </Stack>
        </div>
    );
}
