import axios from 'axios';

export default function ParameterApi() {
  const GetAllParameter = async () => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/parameter/getAll`)
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response?.data;
        console.log(err);
      });

    return data;
  };

  const GetParameterById = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/parameter/getById`,
        inputData
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetAllParameterByDepartment = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/parameter/getByDepartmentId`,
        inputData
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetAllInputMethod = async () => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/parameter/getAllInputMethod`
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const InsertParameter = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/parameter/insertParameter`,
        inputData
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const UpdateParameter = async (inputData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/parameter/updateParameter`,
        inputData
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  return {
    GetAllParameter,
    GetParameterById,
    GetAllInputMethod,
    GetAllParameterByDepartment,
    InsertParameter,
    UpdateParameter,
  };
}
