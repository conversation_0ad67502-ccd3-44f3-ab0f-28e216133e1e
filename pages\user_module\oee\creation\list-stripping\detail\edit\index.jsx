import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, Form, Panel, Stack, Tag, Button, SelectPicker, Col, FlexboxGrid, InputNumber, Notification, useToaster, Checkbox, Modal, InputGroup, Table, Pagination, Loader, Toggle } from "rsuite";
import SearchIcon from "@rsuite/icons/Search";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";
import { Trash as TrashIcon } from "@rsuite/icons";
import CheckIcon from "@rsuite/icons/Check";
import ContainerLayout from "@/components/layout/ContainerLayout";

import ApiMachineOeeHeaderStripping from "@/pages/api/oee/machine_oee_stripping/api_machine_oee_stripping";
import ApiOeeCategory from "@/pages/api/oee/oee_category/api_oee_category";
import ApiMachineOeeDetail from "@/pages/api/oee/machine_oee_detail/api_machine_oee_detail"; // Import API Detail
import { useRouter } from "next/router";

export default function index() {
  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [idRouter, setIdRouter] = useState(null);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [loading, setLoading] = useState(false);

  const [machineStrippingHeaderDataState, setMachineStrippingHeaderDataState] = useState([]);
  const [dataDetailByIdHeader, setDataDetailByIdHeader] = useState([]);

  const toaster = useToaster();

  const router = useRouter();
  const { Id } = router.query;

  // State untuk data OEE Category
  const [plannedCategoryData, setPlannedCategoryData] = useState([]);
  const [unplannedCategoryData, setUnplannedCategoryData] = useState([]);

  const [updateFields, setUpdateFields] = useState([]);
  const [addFields, setAddFields] = useState([
    {
      order_number: 1,
      category_type: null,
      category_name: null,
      child_category_name: null,
      duration: null,
      remarks: "",
      is_active: true,
    },
  ]);

  const { HeaderCell, Cell, Column } = Table;
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("");
  const [sortType, setSortType] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = dataDetailByIdHeader.filter((rowData, i) => {
    const searchFields = ["category_name", "child_category_name", "category_type", "duration_detail", "remarks", "created_date", "created_by", "updated_date", "updated_by", "deleted_date", "deleted_by", "is_active"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : dataDetailByIdHeader.length;

  const formatDateTimeCustom = (isoDateString) => {
    if (!isoDateString) return "-";
    const date = new Date(isoDateString);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  const formatDateTimeCustomNoIso = (isoDateString) => {
    if (!isoDateString) return "-";
    const [datePart, timePart] = isoDateString.split("T");
    const [year, month, day] = datePart.split("-");
    const [hours, minutes] = timePart.split(":");

    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  const formatTimeRange = (startTime, endTime, duration) => {
    const [startDate, startTimeOnly] = startTime.split(" ");
    const [endDate, endTimeOnly] = endTime.split(" ");

    if (startDate === endDate) {
      // Same day: Show only the time range
      return `${startDate} ${startTimeOnly} - ${endTimeOnly} (${duration} menit)`;
    }

    // Different days: Include the date for both
    return `${startDate} ${startTimeOnly} - ${endDate} ${endTimeOnly} (${duration} menit)`;
  };

  // Data Type (Planned/Unplanned)
  const typeData = [
    { label: "Planned", value: "P" },
    { label: "Unplanned", value: "U" },
  ];

  const addAddField = () => {
    const newField = {
      order_number: addFields.length + 1,
      category_type: null,
      category_name: null,
      child_category_name: null,
      duration: null,
      remarks: "",
      is_active: true,
    };
    setAddFields([...addFields, newField]);
  };

  const removeAddField = () => {
    if (addFields.length > 1) {
      const newData = [...addFields];
      newData.pop();
      setAddFields(newData);
    }
  };

  const deleteAddField = (index) => {
    if (addFields.length > 1) {
      const dataInputField = [...addFields];
      dataInputField.splice(index, 1);
      setAddFields(dataInputField);
    }
  };

  // Fungsi untuk memetakan data API ke updateFields
  const mapApiDataToUpdateFields = (apiData) => {
    return apiData.map((item, index) => ({
      id_oee_detail: item.id_oee_detail, // Pastikan ini ada
      order_number: index + 1,
      category_type: item.category_type,
      category_name: item.category_name,
      child_category_name: item.child_category_name,
      duration: item.duration_detail,
      remarks: item.remarks,
      is_active: item.is_active === 1,
    }));
  };

  const handleUpdateChange = (index, field, value) => {
    let data = [...updateFields];
    data[index][field] = field === "duration" ? Number(value) : value;

    // Jika ada perubahan tipe kategori
    if (field === "category_type") {
      data[index].category_name = null;
      data[index].child_category_name = null;
    }

    // Jika ada perubahan nama kategori
    if (field === "category_name") {
      data[index].child_category_name = null;
    }

    setUpdateFields(data);
  };

  const handleAddChange = (index, field, value) => {
    let data = [...addFields];
    data[index][field] = field === "duration" ? Number(value) : value;

    // Reset category_name & child_category_name jika category_type diubah
    if (field === "category_type") {
      data[index].category_name = null;
      data[index].child_category_name = null;
    }

    // Reset child_category_name jika category_name diubah
    if (field === "category_name") {
      data[index].child_category_name = null;
    }

    setAddFields(data);
  };

  const HandleGetTransactionHeaderMachine = async (id_oee_header) => {
    try {
      const apiTransactionHeader = ApiMachineOeeHeaderStripping();
      const response = await apiTransactionHeader.GetMachineOeeListStrippingById({ id_oee_header: parseInt(id_oee_header) });
      if (response.status === 200) {
        const data = response.data;
        setMachineStrippingHeaderDataState({
          id_oee_header: data.id_oee_header,
          id_machine_oee: data.id_machine_oee,
          machine_name: data.machine_name,
          start_time: data.start_time,
          end_time: data.end_time,
          duration: data.duration,
          created_date: data.created_date || "-",
          created_by: data.created_by || "-",
          updated_date: data.update_date || "-",
          updated_by: data.update_by || "-",
          deleted_date: data.delete_date || "-",
          deleted_by: data.delete_by || "-",
          is_active: data.is_active,
        });
        return data;
      } else {
        console.error("Failed to fetch detail data");
        return null;
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
      return null;
    }
  };

  const HandleGetAllOeeCategory = async () => {
    try {
      const res = await ApiOeeCategory().GetAllOeeCategory();
      if (res.status === 200) {
        const { planned_category, unplanned_category } = res.data;

        // Proses planned_category
        const plannedCategoryDataProcessed = planned_category.map((item) => ({
          ...item,
          label: item.category_name,
          value: item.category_name,
          childData: item.child_category.map((child) => ({
            ...child,
            label: child.child_category_name,
            value: child.child_category_name,
          })),
        }));

        // Proses unplanned_category
        const unplannedCategoryDataProcessed = unplanned_category.map((item) => ({
          ...item,
          label: item.category_name,
          value: item.category_name,
          childData: item.child_category.map((child) => ({
            ...child,
            label: child.child_category_name,
            value: child.child_category_name,
          })),
        }));

        setPlannedCategoryData(plannedCategoryDataProcessed);
        setUnplannedCategoryData(unplannedCategoryDataProcessed);
      } else {
        console.log("Error fetching main categories:", res.message);
      }
    } catch (error) {
      console.log("Error on catch Get All Api", error);
    }
  };

  const HandleGetOeeDetail = async (id_oee_header) => {
    try {
      console.log("Fetching OEE Detail for ID:", id_oee_header);
      const api = ApiMachineOeeDetail();
      const response = await api.GetMachineOeeDetailByIdHeader({ id_oee_header: parseInt(id_oee_header) });
      console.log("API Response:", response);

      if (response.status === 200) {
        console.log("Response Data:", response.data);
        // Set the dataDetailByIdHeader state with the fetched data
        setDataDetailByIdHeader(response.data);

        // Pemetaan data API ke updateFields
        const mappedUpdateFields = mapApiDataToUpdateFields(response.data);
        setUpdateFields(mappedUpdateFields);
      } else {
        console.error("Failed to fetch detail data");
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
    }
  };

  const HandleUpdateTransactionDetailMachine = async () => {
    const errors = {};
    let hasError = false;
    const errorMessages = [];

    // Validasi untuk updateFields
    updateFields.forEach((input, index) => {
      if (input.category_type) {
        if (!input.category_name) {
          hasError = true;
          errors[`category_name_update_${index}`] = "Nama aktivitas harus dipilih.";
          errorMessages.push(`Nama aktivitas harus dipilih pada baris update ${index + 1}.`);
        }

        if (!input.child_category_name) {
          hasError = true;
          errors[`child_category_name_update_${index}`] = "Detail aktivitas harus dipilih.";
          errorMessages.push(`Detail aktivitas harus dipilih pada baris update ${index + 1}.`);
        }

        if (Number(input.duration) <= 0 || isNaN(Number(input.duration))) {
          hasError = true;
          errors[`duration_update_${index}`] = "Durasi harus lebih dari nol.";
          errorMessages.push(`Durasi harus lebih dari nol pada baris update ${index + 1}.`);
        }
      }
      // Jika category_type tidak dipilih di updateFields, kemungkinan baris tersebut
      // sudah existing data dan di-update menjadi kosong. Jika Anda ingin mengharuskan
      // baris tetap valid, bisa ditambahkan logika serupa.
    });

    // Validasi untuk addFields
    addFields.forEach((input, index) => {
      // Jika planned/unplanned (category_type) tidak dipilih, tidak wajib diisi
      // Berarti tidak ada validasi error jika semua kosong
      // Namun jika category_type dipilih, maka wajib isi yang lain
      if (input.category_type) {
        if (!input.category_name) {
          hasError = true;
          errors[`category_name_add_${index}`] = "Nama aktivitas harus dipilih.";
          errorMessages.push(`Nama aktivitas harus dipilih pada baris tambah ${index + 1}.`);
        }

        if (!input.child_category_name) {
          hasError = true;
          errors[`child_category_name_add_${index}`] = "Detail aktivitas harus dipilih.";
          errorMessages.push(`Detail aktivitas harus dipilih pada baris tambah ${index + 1}.`);
        }

        if (Number(input.duration) <= 0 || isNaN(Number(input.duration))) {
          hasError = true;
          errors[`duration_add_${index}`] = "Durasi harus lebih dari nol.";
          errorMessages.push(`Durasi harus lebih dari nol pada baris tambah ${index + 1}.`);
        }
      }
      // Jika category_type tidak ada, maka baris ini dianggap optional.
      // Tidak perlu validasi karena nanti akan difilter ketika proses payload.
    });

    // Validasi total durasi
    const totalDetailDuration = [...updateFields, ...addFields].filter((item) => item.is_active === true).reduce((sum, item) => sum + (parseFloat(item.duration) || 0), 0);

    const headerDuration = Number(machineStrippingHeaderDataState.duration) || 0;

    if (totalDetailDuration > headerDuration) {
      hasError = true;
      errors["totalDuration"] = "Durasi detail tidak melebihi total durasi stop";
      errorMessages.push("Durasi detail tidak melebihi total durasi stop");
    }

    // Set semua error ke state
    setErrorsAddForm(errors);

    // Jika ada error, tampilkan notifikasi dan hentikan proses
    if (hasError) {
      toaster.push(
        <Notification type="error" header="Error">
          {errorMessages.map((msg, idx) => (
            <li key={idx}>{msg}</li>
          ))}
        </Notification>,
        { placement: "topEnd" }
      );
      return; // Hentikan eksekusi jika terdapat error
    }

    try {
      setLoading(true);

      const id_oee_header = parseInt(router.query.Id, 10);
      const created_by = `${sessionAuth.employee_id} - ${sessionAuth.employee_name}`;

      // old_data
      const old_data = updateFields
        .map((input) => {
          if (!input.category_type && !input.category_name && !input.child_category_name) {
            return null;
          }

          const mainCategory = input.category_type === "P" ? plannedCategoryData.find((item) => item.category_name === input.category_name) : unplannedCategoryData.find((item) => item.category_name === input.category_name);

          const childCategory = mainCategory?.childData.find((child) => child.value === input.child_category_name);

          return {
            id_oee_detail: input.id_oee_detail,
            id_oee_header,
            id_binding_category: childCategory?.id_binding_category || null,
            category_name: mainCategory?.label || null,
            child_category_name: childCategory?.label || null,
            category_type: mainCategory?.category_type || null,
            duration_detail: Number(input.duration) || 0,
            remarks: input.remarks || "",
            updated_by: created_by,
            is_active: input.is_active ? 1 : 0,
          };
        })
        .filter(Boolean);

      // new_data
      const new_data = addFields
        .map((input) => {
          // Jika tidak ada category_type dan field lain, baris ini tidak diproses
          if (!input.category_type && !input.category_name && !input.child_category_name) {
            return null;
          }

          const mainCategory = input.category_type === "P" ? plannedCategoryData.find((item) => item.value === input.category_name) : unplannedCategoryData.find((item) => item.value === input.category_name);

          const childCategory = mainCategory?.childData.find((child) => child.value === input.child_category_name);

          return {
            id_oee_header,
            id_binding_category: childCategory?.id_binding_category || null,
            category_name: mainCategory?.label || null,
            child_category_name: childCategory?.label || null,
            category_type: mainCategory?.category_type || null,
            duration_detail: Number(input.duration) || 0,
            remarks: input.remarks || "",
            created_by,
          };
        })
        .filter(Boolean);

      const payload = { id_oee_header, old_data, new_data, updated_by: created_by };
      const res = await ApiMachineOeeDetail().UpdateBatchMachineOeeDetail(payload);

      if (res.status === 200) {
        setUpdateFields([]);
        setAddFields([
          {
            order_number: 1,
            category_type: null,
            category_name: null,
            child_category_name: null,
            duration: null,
            remarks: "",
            is_active: true,
          },
        ]);
        setErrorsAddForm({});
        toaster.push(
          <Notification type="success" header="Success">
            Data berhasil disimpan dan diperbarui.
          </Notification>,
          { placement: "topEnd" }
        );
        await HandleGetOeeDetail(id_oee_header);
      } else {
        toaster.push(
          <Notification type="error" header="Error">
            Terjadi kesalahan saat menyimpan data.
          </Notification>,
          { placement: "topEnd" }
        );
      }
    } catch (error) {
      console.error("Error saat memperbarui atau menambahkan detail transaksi:", error);
      toaster.push(
        <Notification type="error" header="Error">
          Terjadi kesalahan ketika menyimpan data.
        </Notification>,
        { placement: "topEnd" }
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else if (Id) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("oee/creation/list-stripping"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      const parsedId = parseInt(Id, 10);
      setIdRouter(parsedId);
      HandleGetOeeDetail(parsedId);
      HandleGetTransactionHeaderMachine(parsedId);
      HandleGetAllOeeCategory();
    }
  }, [router, Id]);

  return (
    <div>
      <Head>
        <title>Detail Edit HM1 Stripping</title>
      </Head>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <Breadcrumb>
                <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                <Breadcrumb.Item>Production</Breadcrumb.Item>
                <Breadcrumb.Item>OEE</Breadcrumb.Item>
                <Breadcrumb.Item>Creation</Breadcrumb.Item>
                <Breadcrumb.Item>List</Breadcrumb.Item>
                <Breadcrumb.Item>HM1 Stripping</Breadcrumb.Item>
                <Breadcrumb.Item active>Edit Detail</Breadcrumb.Item>
              </Breadcrumb>
            </Stack.Item>
            <Stack.Item>
              <Tag color="green">Module: {moduleName}</Tag>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-4"
            header={
              <Form fluid>
                <FlexboxGrid className="mb-4">
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.ControlLabel>Nama Mesin</Form.ControlLabel>
                    <Form.Control name="machine_name" value={"HM1 Stripping"} readOnly className="mb-3" />
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Waktu Stop</Form.ControlLabel>
                      <Form.Control
                        name="time_range"
                        value={
                          machineStrippingHeaderDataState.start_time && machineStrippingHeaderDataState.end_time && machineStrippingHeaderDataState.duration
                            ? formatTimeRange(formatDateTimeCustomNoIso(machineStrippingHeaderDataState.start_time), formatDateTimeCustomNoIso(machineStrippingHeaderDataState.end_time), machineStrippingHeaderDataState.duration)
                            : "-"
                        }
                        readOnly
                        className="mb-3"
                      />
                    </Form.Group>
                  </FlexboxGrid.Item>
                </FlexboxGrid>
                {/* 
                <FlexboxGrid className="mb-4">
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                      <Form.Control name="created_date" value={formatDateTimeCustom(machineStrippingHeaderDataState.created_date || "-")} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                </FlexboxGrid>
                <FlexboxGrid className="mb-4">
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Waktu Mulai</Form.ControlLabel>
                      <Form.Control name="start_time" value={machineStrippingHeaderDataState.start_time ? formatDateTimeCustomNoIso(machineStrippingHeaderDataState.start_time) : "-"} readOnly className="mb-3" />
                      <Form.ControlLabel>Durasi (Menit)</Form.ControlLabel>
                      <Form.Control name="duration" value={machineStrippingHeaderDataState.duration || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Waktu Selesai</Form.ControlLabel>
                      <Form.Control name="end_time" value={machineStrippingHeaderDataState.end_time ? formatDateTimeCustomNoIso(machineStrippingHeaderDataState.end_time) : "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                </FlexboxGrid> */}

                {/* <FlexboxGrid>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Nama Mesin</Form.ControlLabel>
                      <Form.Control name="machine_name" value={machineStrippingHeaderDataState.machine_name || "-"} readOnly className="mb-3" />
                      <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                      <Form.Control name="updated_date" value={machineStrippingHeaderDataState.updated_date || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                      <Form.Control name="created_by" value={machineStrippingHeaderDataState.created_by || "-"} readOnly className="mb-3" />
                      <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                      <Form.Control name="updated_by" value={machineStrippingHeaderDataState.updated_by || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                      <Form.Control name="deleted_date" value={machineStrippingHeaderDataState.deleted_date || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                      <Form.Control name="deleted_by" value={machineStrippingHeaderDataState.deleted_by || "-"} readOnly className="mb-3" />
                    </Form.Group>
                  </FlexboxGrid.Item>
                </FlexboxGrid> */}
              </Form>
            }
          ></Panel>

          {/* Panel Add Transaction Detail Machine */}
          <Panel bordered>
            <Form.Group controlId="UpdateTransactionDetailMachine">
              <FlexboxGrid justify="space-between" align="middle" className="mb-2">
                <FlexboxGrid.Item>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Perbarui Detail Transaksi Mesin:</Form.ControlLabel>
                </FlexboxGrid.Item>
                <FlexboxGrid>
                  <FlexboxGrid.Item>
                    <Button appearance="primary" onClick={HandleUpdateTransactionDetailMachine} loading={loading} color="green">
                      Submit
                    </Button>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item style={{ paddingLeft: 5 }}>
                    <Button appearance="primary" onClick={() => setShowDetailModal(true)}>
                      Lihat Rincian
                    </Button>
                  </FlexboxGrid.Item>
                </FlexboxGrid>
              </FlexboxGrid>
              {updateFields.map((input, index) => (
                <FlexboxGrid
                  key={index}
                  style={{
                    padding: "0.5em",
                    border: "0.1em solid #adadad",
                    borderRadius: "5px",
                    marginBottom: "0.5em",
                  }}
                  align="middle"
                  justify="start"
                  gutter={10}
                  className="mb-4"
                >
                  {/* Tipe Aktivitas */}
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Tipe Aktivitas</Form.ControlLabel>
                      <SelectPicker
                        placement='auto'
                        data={typeData}
                        value={input.category_type}
                        onChange={(value) => handleUpdateChange(index, "category_type", value)}
                        style={{ width: "100%" }}
                        placeholder="Pilih Tipe"
                        // searchable={false}
                        errorMessage={errorsAddForm[`category_type_${index}`]}
                        disabled={!input.is_active}
                      />
                    </Form.Group>
                  </FlexboxGrid.Item>

                  {/* Nama Aktivitas */}
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Nama Aktivitas</Form.ControlLabel>
                      <SelectPicker
                        placement='auto'
                        data={input.category_type === "P" ? plannedCategoryData : unplannedCategoryData}
                        value={input.category_name}
                        onChange={(value) => handleUpdateChange(index, "category_name", value)}
                        style={{ width: "100%" }}
                        placeholder="Pilih Aktivitas"
                        // searchable={false}
                        disabled={!input.category_type || !input.is_active}
                        errorMessage={errorsAddForm[`category_name_${index}`]}
                      />
                    </Form.Group>
                  </FlexboxGrid.Item>

                  {/* Detail Aktivitas */}
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Detail Aktivitas</Form.ControlLabel>
                      <SelectPicker
                        placement='auto'
                        data={input.category_type === "P" ? plannedCategoryData.find((item) => item.value === input.category_name)?.childData || [] : unplannedCategoryData.find((item) => item.value === input.category_name)?.childData || []}
                        value={input.child_category_name}
                        onChange={(value) => handleUpdateChange(index, "child_category_name", value)}
                        style={{ width: "100%" }}
                        placeholder="Pilih Detail"
                        // searchable={false}
                        disabled={!input.category_name || !input.is_active}
                        errorMessage={errorsAddForm[`child_category_name_${index}`]}
                      />
                    </Form.Group>
                  </FlexboxGrid.Item>

                  {/* Durasi */}
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Durasi</Form.ControlLabel>
                      <InputNumber
                        value={input.duration}
                        onChange={(value) => handleUpdateChange(index, "duration", value)}
                        style={{ width: "100%" }}
                        placeholder="Durasi"
                        min={0}
                        errorMessage={errorsAddForm[`duration_${index}`]}
                        disabled={!input.is_active}
                      />
                    </Form.Group>
                  </FlexboxGrid.Item>

                  {/* Catatan */}
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Catatan</Form.ControlLabel>
                      <Input value={input.remarks} onChange={(value) => handleUpdateChange(index, "remarks", value)} style={{ width: "100%" }} placeholder="Remarks" disabled={!input.is_active} />
                    </Form.Group>
                  </FlexboxGrid.Item>
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24}>
                    <Form.Group>
                      <Stack>
                        <Form.ControlLabel>Status</Form.ControlLabel>
                      </Stack>
                      {/* <Checkbox
                        checked={input.is_active}
                        onChange={(value, checked) => {
                          handleUpdateChange(index, 'is_active', checked); // Update state lokal
                        }}
                      /> */}
                      <Toggle
                        size="lg"
                        checkedChildren={<CheckIcon />}
                        unCheckedChildren={<TrashIcon />}
                        defaultChecked={input.is_active}
                        onChange={(value) => {
                          handleUpdateChange(index, "is_active", value); // Update state lokal
                        }}
                      />
                    </Form.Group>
                  </FlexboxGrid.Item>
                </FlexboxGrid>
              ))}
            </Form.Group>

            <Form.Group controlId="AddTransactionDetailMachine">
              <FlexboxGrid justify="space-between" align="middle" className="mb-2">
                <FlexboxGrid.Item>
                  <Form.ControlLabel style={{ fontWeight: "bold" }}>Tambahkan Detail Transaksi Mesin:</Form.ControlLabel>
                </FlexboxGrid.Item>
              </FlexboxGrid>
              {addFields.map((input, index) => (
                <FlexboxGrid
                  key={index}
                  style={{
                    padding: "0.5em",
                    border: "0.1em solid #adadad",
                    borderRadius: "5px",
                    marginBottom: "0.5em",
                  }}
                  align="middle"
                  justify="start"
                  gutter={10}
                >
                  {/* Tipe Aktivitas */}
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Tipe Aktivitas</Form.ControlLabel>
                      <SelectPicker
                        placement='auto'
                        data={typeData}
                        value={input.category_type}
                        onChange={(value) => handleAddChange(index, "category_type", value)}
                        style={{ width: "100%" }}
                        placeholder="Pilih Tipe"
                        // searchable={false}
                        errorMessage={errorsAddForm[`category_type_add_${index}`]}
                      />
                    </Form.Group>
                  </FlexboxGrid.Item>

                  {/* Nama Aktivitas */}
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Nama Aktivitas</Form.ControlLabel>
                      <SelectPicker
                        placement='auto'
                        data={input.category_type === "P" ? plannedCategoryData : unplannedCategoryData}
                        value={input.category_name}
                        onChange={(value) => handleAddChange(index, "category_name", value)}
                        style={{ width: "100%" }}
                        placeholder="Pilih Aktivitas"
                        // searchable={false}
                        disabled={!input.category_type}
                        errorMessage={errorsAddForm[`category_name_add_${index}`]}
                      />
                    </Form.Group>
                  </FlexboxGrid.Item>

                  {/* Detail Aktivitas */}
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Detail Aktivitas</Form.ControlLabel>
                      <SelectPicker
                        placement='auto'
                        data={input.category_type === "P" ? plannedCategoryData.find((item) => item.value === input.category_name)?.childData || [] : unplannedCategoryData.find((item) => item.value === input.category_name)?.childData || []}
                        value={input.child_category_name}
                        onChange={(value) => handleAddChange(index, "child_category_name", value)}
                        style={{ width: "100%" }}
                        placeholder="Pilih Detail"
                        // searchable={false}
                        disabled={!input.category_name}
                        errorMessage={errorsAddForm[`child_category_name_add_${index}`]}
                      />
                    </Form.Group>
                  </FlexboxGrid.Item>

                  {/* Durasi */}
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Durasi</Form.ControlLabel>
                      <InputNumber value={input.duration} onChange={(value) => handleAddChange(index, "duration", value)} style={{ width: "100%" }} placeholder="Durasi" min={0} errorMessage={errorsAddForm[`duration_add_${index}`]} />
                    </Form.Group>
                  </FlexboxGrid.Item>

                  {/* Catatan */}
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24}>
                    <Form.Group>
                      <Form.ControlLabel>Catatan</Form.ControlLabel>
                      <Input value={input.remarks} onChange={(value) => handleAddChange(index, "remarks", value)} style={{ width: "100%" }} placeholder="Remarks" />
                    </Form.Group>
                  </FlexboxGrid.Item>

                  {/* Tombol Hapus */}
                  <FlexboxGrid.Item as={Col} colspan={4} lg={4} md={4} sm={24} xs={24} style={{ paddingLeft: "1em", display: "flex", alignItems: "flex-end" }}>
                    <IconButton icon={<TrashIcon />} color="red" appearance="primary" circle onClick={() => deleteAddField(index)} title="Hapus baris ini" />
                  </FlexboxGrid.Item>
                </FlexboxGrid>
              ))}

              {/* Tombol Tambah & Kurang Baris untuk Add Section */}
              <FlexboxGrid style={{ marginTop: "0.5em" }} gutter={10}>
                <FlexboxGrid.Item style={{ paddingRight: "0.5em" }}>
                  <IconButton icon={<FontAwesomeIcon icon={faPlus} />} color="green" appearance="primary" circle onClick={addAddField} title="Tambah Baris" />
                </FlexboxGrid.Item>
                <FlexboxGrid.Item>
                  <IconButton icon={<FontAwesomeIcon icon={faMinus} />} color="red" appearance="primary" circle onClick={removeAddField} title="Kurangi Baris" />
                </FlexboxGrid.Item>
              </FlexboxGrid>


            </Form.Group>
          </Panel>
        </div>

        <Modal open={showDetailModal} onClose={() => setShowDetailModal(false)} size="lg">
          <Modal.Header>
            <Stack justifyContent="space-between" alignItems="center">
              <Modal.Title>Detail Transaksi Mesin</Modal.Title>
              {/* Pencarian */}
              <InputGroup inside>
                <InputGroup.Addon>
                  <SearchIcon />
                </InputGroup.Addon>
                <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                <InputGroup.Addon
                  onClick={() => {
                    setSearchKeyword("");
                    setPage(1);
                  }}
                  style={{
                    display: searchKeyword ? "block" : "none",
                    color: "red",
                    cursor: "pointer",
                  }}
                ></InputGroup.Addon>
              </InputGroup>
            </Stack>
          </Modal.Header>
          <Modal.Body>
            {loading ? (
              <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                <Loader size="sm" content="Loading..." />
              </div>
            ) : (
              <Table bordered cellBordered height={600} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                <Column width={70} align="center" fixed>
                  <HeaderCell>No</HeaderCell>
                  <Cell>{(rowData, rowIndex) => rowIndex + 1 + limit * (page - 1)}</Cell>
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Kategori</HeaderCell>
                  <Cell dataKey="category_name" />
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Sub Kategori</HeaderCell>
                  <Cell dataKey="child_category_name" />
                </Column>
                <Column width={120} sortable fullText>
                  <HeaderCell align="center">Tipe Kategori</HeaderCell>
                  <Cell>
                    {(rowData) => {
                      switch (rowData.category_type) {
                        case "P":
                          return "Planned";
                        case "U":
                          return "Unplanned";
                        default:
                          return rowData.category_type; // Menampilkan nilai asli jika tidak 'P' atau 'U'
                      }
                    }}
                  </Cell>
                </Column>
                <Column width={120} sortable fullText>
                  <HeaderCell align="center">Durasi (Menit)</HeaderCell>
                  <Cell dataKey="duration_detail" align="center" />
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Info Tambahan</HeaderCell>
                  <Cell dataKey="remarks" />
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Dibuat Tanggal</HeaderCell>
                  <Cell>{(rowData) => formatDateTimeCustom(rowData.created_date)}</Cell>
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                  <Cell dataKey="created_by" />
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Diperbarui Tanggal</HeaderCell>
                  <Cell>{(rowData) => formatDateTimeCustom(rowData.updated_date)}</Cell>
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell dataKey="updated_by" />
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Dihapus Tanggal</HeaderCell>
                  <Cell>{(rowData) => formatDateTimeCustom(rowData.deleted_date)}</Cell>
                </Column>
                <Column width={200} sortable fullText>
                  <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                  <Cell dataKey="deleted_by" />
                </Column>
              </Table>
            )}

            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button appearance="primary" onClick={() => setShowDetailModal(false)}>
              Tutup
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </div>
  );
}
