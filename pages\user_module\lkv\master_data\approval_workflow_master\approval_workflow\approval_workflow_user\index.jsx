import React, { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  ButtonToolbar,
  Modal,
  Schema,
  Message,
  Form,
  useToaster,
  SelectPicker,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import EditIcon from "@rsuite/icons/Edit";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import LkvApprovalWorkflowUser from "@/pages/api/lkv/api_lkv_approval_workflow_user";
import UseTestApi from "@/pages/api/userApi";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import XLSX from "xlsx";
import { useRouter } from "next/router";

const { StringType } = Schema.Types;

const model = Schema.Model({
  employee_id: StringType().isRequired(),
});

const initialData = {
  employee_id: "",
};

export default function Index() {
  const [moduleName, setModuleName] = useState("");
  const { HeaderCell, Cell, Column } = Table;
  const [loading, setLoading] = useState(false);
  const [masterData, setMasterData] = useState([]);
  const [formTitle, setFormTitle] = useState("");
  const [idData, setIdData] = useState();
  const [openForm, setOpenForm] = useState(false);
  const toaster = useToaster();
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const formRef = React.useRef();
  const [formData, setFormData] = useState(initialData);
  const [isBackendDown, setIsBackendDown] = useState(false);
  const [status, setStatus] = useState(0);
  const [userData, setUserData] = useState([]);
  const router = useRouter();
  const [props, setProps] = useState([]);

  const Messages = (type, text) => (
    <Message type={type} showIcon>
      {text}
    </Message>
  );

  const { Id, name } = router.query;

  // form
  const handleOpenForm = (title) => {
    setOpenForm(true);
    setFormTitle(title);
  };

  const handleForm = () => {
    if (formTitle === "Update") {
      handleUpdateUserWorkflow();
    }
  };

  // pagination

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = masterData.filter((rowData, i) => {
    const searchFields = [
      "employee_id",
      "urutan",
      "created_by",
      "updated_by",
      "deleted_by",
    ];

    const matchesSearch = searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );

    return matchesSearch;
  });

  const totalRowCount = searchKeyword ? filteredData.length : masterData.length;

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  // api

  const fetchMasterData = async () => {
    const data = await LkvApprovalWorkflowUser().getAllApprovalWorkFlotUserAwid(
      {
        id: Id,
      }
    );
    setMasterData(data.data ? data.data : []);
  };

  const fecthUserData = async () => {
    const data = await UseTestApi().GetTestApi();
    setUserData(data.Data ? data.Data : []);
  };

  const handleUpdateUserWorkflow = async () => {
    if (!formData.employee_id) {
      console.log("some field are empty");
      return;
    }

    try {
      const data = await LkvApprovalWorkflowUser().updatedApprovalWorkflowUser({
        id: idData,
        employee_id: formData.employee_id,
        created_by: props.employee_id,
      });

      if (data.status === 200) {
        toaster.push(Messages("success", "Success updating Data!"), {
          placement: "topCenter",
          duration: 5000,
        });

        setOpenForm(false);
        fetchMasterData();
      } else {
        console.log("error updating data", data.message);
        toaster.push(
          Messages("error", `Error: something error. Please try again later!`),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
      }

      setFormData(initialData);
    } catch (error) {
      console.error("Error creating data:", error);
      toaster.push(
        Messages("error", `Error: something error Please try again later!`),
        {
          placement: "topCenter",
          duration: 5000,
        }
      );
      setFormData(initialData);
    }
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("lkv/master_data/approval_workflow_master")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      fetchMasterData();
      fecthUserData();
    }

    // fetchMasterData();
  }, [Id]);

  // excel

  const handleExportExcel = () => {
    if (masterData.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }

    const headerMapping = {
      awuid: "ID Approval Workflow User LKV",
      employee_id: "Employee ID",
      urutan: "urutan",
      created_dt: "Created Date",
      created_by: "Created By",
      updated_dt: "Updated Date",
      updated_by: "Updated By",
      deleted_dt: "Deleted Date",
      deleted_by: "Deleted By",
      is_active: "Status",
    };

    const formattedData = masterData.map((item) => {
      const formattedItem = {};
      for (const key in item) {
        if (headerMapping[key]) {
          if (key === "is_active") {
            formattedItem[headerMapping[key]] =
              item[key] === 1 ? "Active" : "Inactive";
          } else {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
      }
      return formattedItem;
    });

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const filename = `LKV Approval Workflow User ${Id} ${date}.xlsx`;

    XLSX.writeFile(wb, filename);
  };

  // options
  const optionsUser = userData.map((item) => ({
    value: item.Employee_Id,
    label: item.Name,
  }));

  return (
    <>
      <div>
        <Head>
          <title>LKV Approval Workflow Master</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Lkv</Breadcrumb.Item>
                  <Breadcrumb.Item active>
                    LKV Approval Workflow User
                  </Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <div>
                <div style={{ marginBottom: "15px" }}>
                  <Form style={{ display: "flex", gap: "10px" }}>
                    <Form.Group>
                      <Form.ControlLabel>Workflow Master</Form.ControlLabel>
                      <Form.Control name="name" readOnly value={name} />
                    </Form.Group>
                  </Form>
                </div>
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<FileDownloadIcon />}
                      appearance="primary"
                      onClick={handleExportExcel}
                    >
                      Download (.xlsx)
                    </IconButton>
                  </div>

                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input
                      placeholder="search"
                      value={searchKeyword}
                      onChange={handleSearch}
                    />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              </div>
            }
          >
            <Table
              bordered
              cellBordered
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
              height={400}
            >
              <Column width={100} align="left" fixed sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  ID
                </HeaderCell>
                <Cell dataKey="awuid" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Employee
                </HeaderCell>
                <Cell dataKey="employee" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Urutan
                </HeaderCell>
                <Cell dataKey="urutan" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Created By
                </HeaderCell>
                <Cell dataKey="created_by" />
              </Column>
              <Column width={150} align="left" resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Created Date
                </HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Updated By
                </HeaderCell>
                <Cell dataKey="updated_by" />
              </Column>
              <Column width={150} align="left" resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Updated Date
                </HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>
              <Column width={150} align="left" fullText resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Delete By
                </HeaderCell>
                <Cell dataKey="deleted_by" />
              </Column>
              <Column width={150} align="center" resizable sortable>
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Delete Date
                </HeaderCell>
                <Cell dataKey="deleted_dt" />
              </Column>
              <Column width={100} sortable resizable fixed="right">
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={150} resizable fixed="right">
                <HeaderCell style={{ fontWeight: "bold", color: "black" }}>
                  Operation
                </HeaderCell>

                <Cell style={{ padding: "6px" }} resizable>
                  {(rowData) => (
                    <div>
                      <ButtonToolbar>
                        <Button
                          onClick={() => {
                            handleOpenForm("Update");
                            setIdData(rowData.awuid);
                            setFormData({
                              employee_id: rowData.employee_id,
                            });
                          }}
                        >
                          <EditIcon style={{ color: "blue" }} />
                        </Button>
                      </ButtonToolbar>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
        <Modal
          open={openForm}
          backdrop="statis"
          onClose={() => {
            setOpenForm(false);
            setFormData(initialData);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Edit Workflow User</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form
              model={model}
              ref={formRef}
              formValue={formData}
              onChange={(formValue) => setFormData(formValue)}
              fluid
            >
              <Form.Group controlId="employee_id">
                <Form.ControlLabel>Employee</Form.ControlLabel>
                <Form.Control
                  name="employee_id"
                  accepter={SelectPicker}
                  data={optionsUser}
                  block
                  placeholder="select employee"
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setOpenForm(false);
                setFormData(initialData);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button appearance="primary" onClick={handleForm}>
              Edit
            </Button>
          </Modal.Footer>
        </Modal>
      </ContainerLayout>
    </>
  );
}
