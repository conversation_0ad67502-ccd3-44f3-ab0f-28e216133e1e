import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  useToaster,
  SelectPicker,
  DatePicker,
  Loader,
  Row,
  Col,
  InputNumber,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import { useRouter } from "next/router";
import API_InboundReagen from "@/pages/api/inbound_reagen/api_inbound_reagen";
import API_MasterDataMappingReagen from "@/pages/api/master_data/api_masterdata_master_data_mapping_reagen";
import { isBefore, startOfToday } from "date-fns";
import API_MasterDataReagenManufacture from "@/pages/api/master_data/api_reagen_manufacture";

export default function ListInboundReagenPage() {
  const [moduleName, setModuleName] = useState("");
  const { HeaderCell, Cell, Column } = Table;
  const [inbound, setInbound] = useState([]);
  const [mapping, setMapping] = useState([]);
  const [rack, setRack] = useState([]);
  const [detail, setDetail] = useState([]);
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const [props, setProps] = useState([]);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const emptyFormValue = {
    id_criteria_reagen: null,
    room_temperature: null,
    batch_no: null,
    expired_date: null,
    amount: null,
    id_criteria: null,
    id_dimension: null,
    id_type: null,
    id_manufacture: null,
  };
  const [formValue, setFormValue] = useState(emptyFormValue);
  const [temperatureList] = useState([
    { value: "<25°C", label: "<25°C" },
    { value: "25-30°C", label: "25-30°C" },
    { value: "2-8°C", label: "2-8°C" },
  ]);
  const toaster = useToaster();
  const [errors, setErrors] = useState({});
  const router = useRouter();
  const [overInput, setOverInput] = useState(0);
  const [showRack, setShowRack] = useState(false);
  const [rackValues, setRackValues] = useState({});
  const [isAmountDisabled, setIsAmountDisabled] = useState(false);
  const [amountErrorMessage, setAmountErrorMessage] = useState("");
  const [isAmountReset, setIsAmountReset] = useState(false);

  const [brandData, SetBrandData] = useState([])

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const handleGetAllApi = async () => {
    const res = await API_InboundReagen().getAll();
    console.log(res);
    setInbound(res.data ? res.data : []);
  };

  const handleGetBrand = async ()=>{
    try {
      const res = await API_MasterDataReagenManufacture().getAllActive()

      if (res.status === 200) {
        console.log("datwa check", res.data)

      //   {
      //     "id_manufacture": 9,
      //     "manufacture_desc": "Kanto",
      //     "created_dt": "14/08/2024 06:08:11",
      //     "created_by": "230400505",
      //     "created_name": "Dennis Senjaya",
      //     "updated_dt": "15/08/2024 03:08:35",
      //     "updated_by": "230500770",
      //     "updated_name": "Muhamad Irsyad Rafi",
      //     "deleted_dt": "",
      //     "deleted_by": "",
      //     "deleted_name": "",
      //     "is_active": 1
      // }

      
        SetBrandData(res.data || [])
      }else{
        console.log(res.messages)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const filteredData = inbound.filter((rowData, i) => {
    const searchFields = [
      "id_inbound_reagen",
      "id_criteria_reagen",
      "room_temperature",
      "batch_no",
      "expired_date",
      "amount",
      "item_list_no",
      "reagen_name",
      "catalogue",
      "msds",
      "cas_no",
      "created_dt",
      "created_by",
      "created_name",
      "updated_dt",
      "updated_by",
      "updated_name",
      "deleted_dt",
      "deleted_by",
      "deleted_name",
      "is_active",
    ];

    const matchesSearch = searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : inbound.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("inbound_reagen/list_inbound_reagen")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      handleGetAllApi();
      fetchMasterData();
      handleGetBrand();
      handleGetRackApi();
    }
  }, []);

  const handleEditStatusInbound = async (selectedIdInbound) => {
    await API_InboundReagen().editStatus({
      id_inbound_reagen: selectedIdInbound,
      deleted_by: props.employee_id,
    });
    handleGetAllApi();
  };

  const handleGetDetailApi = async (selectedIdInbound) => {
    try {
      const result = await API_InboundReagen().getDetail({
        id_inbound_reagen: selectedIdInbound,
      });

      if (result.status === 200) {
        setDetail(result.data);
      } else {
        console.error("Error fetching details:", result.message);
      }
    } catch (error) {
      console.error("Error fetching details:", error);
    }
  };

  const handleEditApi = async (selectedIdInbound) => {
    try {
      const result = await API_InboundReagen().edit({
        ...formValue,
        id_inbound_reagen: selectedIdInbound,
        updated_by: props.employee_id,
      });

      if (result.status === 200) {
        handleGetAllApi();
        setShowEditModal(false);

        toaster.push(Messages("success", "Success editing Inbound Data!"), {
          placement: "topCenter",
          duration: 5000,
        });
        setFormValue(emptyFormValue);
      } else if (result.status === 400) {
        toaster.push(
          Messages(
            "error",
            `Error: "${result.message}". Please try again later!`
          ),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
      }
    } catch (error) {
      // Handle Axios errors
      console.error("Axios Error:", error);
      toaster.push(Messages("error", `Error: Please try again later!`), {
        placement: "topCenter",
        duration: 5000,
      });
    }
  };

  const fetchMasterData = async () => {
    const res_map = await API_MasterDataMappingReagen().getAllActiveInbound();
    setMapping(res_map.data ? res_map.data : []);
  };

  // const handleAddApi = async () => {
  //   try {
  //     const currentIndex = mapping.findIndex(
  //       (item) => item.id_criteria_reagen === formValue.id_criteria_reagen
  //     );
  //     const result = await API_InboundReagen().create({
  //       ...formValue,
  //       created_by: props.employee_id,
  //       id_criteria: mapping[currentIndex].id_criteria,
  //       id_dimension: mapping[currentIndex].id_dimension,
  //     });

  //     setDetail(result.data ? result.data : []);
  //     setOverInput(result.overInput ? result.overInput : 0);

  //     if (result.status === 200) {
  //       handleGetAllApi();
  //       setShowAddModal(false);
  //       setShowDetailModal(true);

  //       toaster.push(Messages("success", "Success adding Inbound Reagen!"), {
  //         placement: "topCenter",
  //         duration: 5000,
  //       });
  //       setFormValue(emptyFormValue);
  //       setShowRack(false);
  //     } else if (result.status === 400) {
  //       toaster.push(
  //         Messages(
  //           "error",
  //           `Error: "${result.message}". Please try again later!`
  //         ),
  //         {
  //           placement: "topCenter",
  //           duration: 5000,
  //         }
  //       );
  //       setFormValue(emptyFormValue);
  //       setShowAddModal(false);
  //       setShowRack(false);
  //     }
  //   } catch (error) {
  //     console.error("Axios Error:", error);
  //     toaster.push(Messages("error", `Error: Please try again later!`), {
  //       placement: "topCenter",
  //       duration: 5000,
  //     });
  //     setShowRack(false);
  //   }
  // };

  const handleAddApi = async () => {
    try {
      const detailInbound = Object.entries(rackValues).flatMap(
        ([id_rack, floors]) =>
          Object.entries(floors).flatMap(([floor_level_desc, rows]) =>
            Object.entries(rows).map(([row_level_desc, stored_amount]) => {
              const matchingRack = rack.find(
                (r) =>
                  r.id_rack === parseInt(id_rack) &&
                  r.floor_level_desc === floor_level_desc &&
                  r.row_level_desc === row_level_desc
              );

              const id_capacity = matchingRack
                ? matchingRack.id_capacity
                : null;

              return {
                id_capacity,
                created_by: props.employee_id,
                stored_amount,
              };
            })
          )
      );

      const currentIndex = mapping.findIndex(
        (item) => item.id_criteria_reagen === formValue.id_criteria_reagen
      );

      const payload = {
        id_criteria_reagen: formValue.id_criteria_reagen,
        room_temperature: formValue.room_temperature,
        batch_no: formValue.batch_no,
        expired_date: formValue.expired_date,
        amount: formValue.amount,
        created_by: props.employee_id,
        id_criteria: mapping[currentIndex].id_criteria,
        id_type: mapping[currentIndex].id_type,
        detail_inbound: detailInbound,
        id_manufacture: formValue.id_manufacture,
        created_name: props.employee_id + " - " + props.employee_name,
      };

      const result = await API_InboundReagen().createManual(payload);

      if (result.status === 200) {
        const { data, overInput, id_inbound_reagen } = result;

        setDetail(data);
        setOverInput(overInput || 0);
        handleGetAllApi();
        setShowAddModal(false);
        setShowDetailModal(true);

        await handleGetDetailApi(id_inbound_reagen);

        toaster.push(Messages("success", "Success adding Inbound Reagen!"), {
          placement: "topCenter",
          duration: 5000,
        });

        setFormValue(emptyFormValue);
        setShowRack(false);
        setRackValues({});
        setIsAmountDisabled(false);
      } else if (result.status === 400) {
        toaster.push(
          Messages(
            "error",
            `Error: "${result.message}". Please try again later!`
          ),
          {
            placement: "topCenter",
            duration: 5000,
          }
        );
        setFormValue(emptyFormValue);
        setShowAddModal(false);
        setShowRack(false);
      }
    } catch (error) {
      console.error("Axios Error:", error);
      toaster.push(Messages("error", `Error: Please try again later!`), {
        placement: "topCenter",
        duration: 5000,
      });
      setShowRack(false);
    }
  };

  const handleExportExcel = () => {
    if (inbound.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }

    const headerMapping = {
      id_inbound_reagen: "ID Inbound Reagen",
      id_criteria_reagen: "ID Criteria Reagen",
      room_temperature: "Room Temperature",
      batch_no: "Batch Number",
      expired_date: "Expired Date",
      amount: "Amount",
      item_list_no: "Item List Number",
      reagen_name: "Reagen Name",
      catalogue: "Catalogue",
      msds: "MSDS",
      cas_no: "CAS Number",
      created_dt: "Created Date",
      created_by: "Created By",
      created_name: "Created Name",
      updated_dt: "Updated Date",
      updated_by: "Updated By",
      updated_name: "Updated Name",
      deleted_dt: "Deleted Date",
      deleted_by: "Deleted By",
      deleted_name: "Deleted Name",
      is_active: "Status",
    };

    const formattedData = inbound.map((item) => {
      const formattedItem = {};
      for (const key in item) {
        if (headerMapping[key]) {
          if (key === "is_active") {
            formattedItem[headerMapping[key]] =
              item[key] === 1 ? "Active" : "Inactive";
          } else {
            formattedItem[headerMapping[key]] = item[key];
          }
        }
      }
      return formattedItem;
    });

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const filename = `Inbound Reagen ${date}.xlsx`;

    XLSX.writeFile(wb, filename);
  };

  const handleSubmit = async (apiFunction) => {
    setLoading(true);
    try {
      if (!formValue.id_criteria_reagen) {
        setErrors({ id_criteria_reagen: "Criteria Reagen must be selected." });
        return;
      }
      if (!formValue.room_temperature) {
        setErrors({ room_temperature: "Room Temperature must be selected." });
        return;
      }
      if (!formValue.batch_no) {
        setErrors({ batch_no: "Batch Number is required." });
        return;
      }
      if (!formValue.expired_date) {
        setErrors({ expired_date: "Expired Date is required." });
        return;
      }
      if (!formValue.amount) {
        setErrors({ amount: "Amount is required." });
        return;
      }
      if (formValue.amount > 2147483647) {
        setErrors({ amount: "Amount exceeds the maximum value." });
        return;
      }
      if (!formValue.id_manufacture){
        setErrors({id_manufacture: "Required Brand Input"})
        return
      }
      setErrors({});
      await apiFunction();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleReagenChange = async (value) => {
    const selectedReagen = mapping.find(
      (item) => item.id_criteria_reagen === value
    );
    const typeValue = selectedReagen ? selectedReagen.id_type : null;
    const criteriaValue = selectedReagen ? selectedReagen.id_criteria : null;

    setFormValue({
      ...formValue,
      id_criteria_reagen: value,
      id_type: typeValue,
      id_criteria: criteriaValue,
    });

    setErrors((prevErrors) => ({
      ...prevErrors,
      id_criteria_reagen: null,
      id_type: null,
      id_criteria: null,
    }));

    setShowRack(false);

    if (typeValue && criteriaValue) {
      await handleGetRackApi(criteriaValue, typeValue);
    }
  };

  const handleGetRackApi = async () => {
    const { id_criteria, id_type } = formValue;

    if (!id_criteria || !id_type) {
      setErrors((prevErrors) => ({
        ...prevErrors,
      }));
      return;
    }

    try {
      const res = await API_InboundReagen().getRack({ id_criteria, id_type });
      const rackData = res.data ? res.data : [];

      if (rackData.length > 0) {
        const totalSpaceAvailable = rackData.reduce(
          (acc, curr) => acc + curr.space_available,
          0
        );

        if (formValue.amount > totalSpaceAvailable) {
          setFormValue({ ...formValue, amount: totalSpaceAvailable });
          setAmountErrorMessage(
            `Amount exceeds available space, adjusted to ${totalSpaceAvailable}`
          );
        } else {
          setAmountErrorMessage("");
        }

        setRack(rackData);
        setShowRack(true);
        setIsAmountDisabled(true);
        setIsAmountReset(false);
      } else {
        setErrors((prevErrors) => ({
          ...prevErrors,
          amount: "No racks available for this reagen.",
        }));
        setIsAmountReset(false);
      }
    } catch (error) {
      console.error("Error fetching rack data:", error);
    }
  };

  const getTotalAddedAmount = () => {
    return Object.values(rackValues).reduce((total, rack) => {
      return (
        total +
        Object.values(rack).reduce((subTotal, floor) => {
          return (
            subTotal +
            Object.values(floor).reduce(
              (rowTotal, amount) => rowTotal + amount,
              0
            )
          );
        }, 0)
      );
    }, 0);
  };

  const handleMinus = (id_rack, floor_level_desc, row_level_desc) => {
    setRackValues((prevValues) => {
      const newValues = { ...prevValues };
      newValues[id_rack] = newValues[id_rack] || {};
      newValues[id_rack][floor_level_desc] =
        newValues[id_rack][floor_level_desc] || {};
      newValues[id_rack][floor_level_desc][row_level_desc] = Math.max(
        (newValues[id_rack][floor_level_desc][row_level_desc] || 0) - 1,
        0
      );
      return newValues;
    });
  };

  const handlePlus = (
    id_rack,
    floor_level_desc,
    row_level_desc,
    space_available
  ) => {
    setRackValues((prevValues) => {
      const newValues = { ...prevValues };
      const totalAddedAmount = getTotalAddedAmount();
      if (totalAddedAmount < formValue.amount) {
        newValues[id_rack] = newValues[id_rack] || {};
        newValues[id_rack][floor_level_desc] =
          newValues[id_rack][floor_level_desc] || {};
        const currentValue =
          newValues[id_rack][floor_level_desc][row_level_desc] || 0;
        if (currentValue < space_available) {
          newValues[id_rack][floor_level_desc][row_level_desc] =
            currentValue + 1;
        }
      }
      return newValues;
    });
  };

  const handleChange = (
    value,
    id_rack,
    floor_level_desc,
    row_level_desc,
    space_available
  ) => {
    const formattedValue = value
      ? Math.min(parseInt(value, 10), space_available)
      : 0;

    setRackValues((prevValues) => {
      const newValues = { ...prevValues };
      const totalAddedAmount = getTotalAddedAmount();
      const currentValue =
        newValues[id_rack]?.[floor_level_desc]?.[row_level_desc] || 0;
      const maxValueAllowed = Math.min(
        space_available,
        formValue.amount - (totalAddedAmount - currentValue)
      );

      newValues[id_rack] = newValues[id_rack] || {};
      newValues[id_rack][floor_level_desc] =
        newValues[id_rack][floor_level_desc] || {};
      newValues[id_rack][floor_level_desc][row_level_desc] = Math.min(
        formattedValue,
        maxValueAllowed
      );
      return newValues;
    });
  };

  const handleResetAmount = () => {
    setFormValue((prevFormValue) => ({
      ...prevFormValue,
      amount: 0,
    }));
    setRackValues({});
    setShowRack(false);
    setIsAmountDisabled(false);
  };
  

  return (
    <>
      <div>
        <Head>
          <title>Inbound Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Quality</Breadcrumb.Item>
                  <Breadcrumb.Item>Inbound Reagen</Breadcrumb.Item>
                  <Breadcrumb.Item active>List Inbound Reagen</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <IconButton
                    icon={<PlusRoundIcon />}
                    appearance="primary"
                    onClick={() => {
                      setShowAddModal(true);
                    }}
                  >
                    Add
                  </IconButton>
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={150} align="center" sortable>
                <HeaderCell>ID Inbound Reagen</HeaderCell>
                <Cell dataKey="id_inbound_reagen" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Reagen Name</HeaderCell>
                <Cell dataKey="reagen_name" />
              </Column>

              <Column width={150} sortable resizable>
                <HeaderCell>Brand Name</HeaderCell>
                <Cell dataKey="manufacture_desc" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Expired Date</HeaderCell>
                <Cell dataKey="expired_date" />
              </Column>
              <Column width={150} align="center" sortable>
                <HeaderCell>Batch No</HeaderCell>
                <Cell dataKey="batch_no" />
              </Column>
              <Column width={150} sortable align="center">
                <HeaderCell>Item List Number</HeaderCell>
                <Cell dataKey="item_list_no" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Catalogue</HeaderCell>
                <Cell dataKey="catalogue" />
              </Column>
              <Column width={100} align="center" sortable>
                <HeaderCell>Amount</HeaderCell>
                <Cell dataKey="amount" />
              </Column>
              <Column width={150} align="center" sortable>
                <HeaderCell>Storage Temperature</HeaderCell>
                <Cell dataKey="room_temperature" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>MSDS</HeaderCell>
                <Cell dataKey="msds" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>CAS Number</HeaderCell>
                <Cell dataKey="cas_no" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Created Date</HeaderCell>
                <Cell dataKey="created_dt" />
              </Column>
              <Column width={250} sortable resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell>
                  {(rowData) =>
                    `${rowData.created_by} - ${rowData.created_name}`
                  }
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Updated Date</HeaderCell>
                <Cell dataKey="updated_dt" />
              </Column>
              <Column width={250} sortable resizable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell>
                  {(rowData) =>
                    `${rowData.updated_by} - ${rowData.updated_name}`
                  }
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Deleted Date</HeaderCell>
                <Cell dataKey="deleted_dt" />
              </Column>
              <Column width={250} sortable resizable>
                <HeaderCell>Deleted By</HeaderCell>
                <Cell>
                  {(rowData) =>
                    `${rowData.deleted_by} - ${rowData.deleted_name}`
                  }
                </Cell>
              </Column>
              <Column width={100} sortable resizable>
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}
                    >
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>
              <Column width={200} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell style={{ padding: "8px" }}>
                  {(rowData) => (
                    <div>
                      <Button
                        appearance="link"
                        disabled={rowData.is_active === 0}
                        onClick={() => {
                          setShowEditModal(true);
                          setFormValue(rowData);
                        }}
                      >
                        Edit
                      </Button>
                      {/* <Button
                                                appearance="subtle"
                                                onClick={() =>
                                                    handleEditStatusInbound(rowData.id_inbound)
                                                }
                                            >
                                                {rowData.is_active === 1 ? (
                                                    <TrashIcon style={{ fontSize: "16px" }} />
                                                ) : (
                                                    <ReloadIcon style={{ fontSize: "16px" }} />
                            )}
                        </Button> */}
                      <Button
                        appearance="link"
                        onClick={() => {
                          setShowDetailModal(true);
                          handleGetDetailApi(rowData.id_inbound_reagen);
                        }}
                      >
                        Detail
                      </Button>
                      <Button
                        appearance="link"
                        onClick={() => {
                          router.push(`/user_module/inbound_reagen/list_inbound_reagen/list-print?id_inbound_reagen=${rowData.id_inbound_reagen}`)
                        }}
                      >
                        Print
                      </Button>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>

        {/* add modal */}
        <Modal
          backdrop="static"
          open={showAddModal}
          onClose={() => {
            setShowAddModal(false);
            setFormValue(emptyFormValue);
            setShowRack(false);
            setRackValues({});
            setAmountErrorMessage("");
            setIsAmountDisabled(false);
            setIsAmountReset(false);
          }}
          overflow={false}
          size="lg"
        >
          <Modal.Header>
            <Modal.Title>Add Inbound Reagen</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              <Form.Group>
                <Form.ControlLabel>Criteria Reagen</Form.ControlLabel>
                <SelectPicker
                  name="id_criteria_reagen"
                  value={formValue.id_criteria_reagen}
                  block
                  data={mapping}
                  valueKey="id_criteria_reagen"
                  labelKey="reagen_name"
                  onChange={handleReagenChange}
                />
                {errors.id_criteria_reagen && (
                  <p style={{ color: "red" }}>{errors.id_criteria_reagen}</p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Brand</Form.ControlLabel>
                <SelectPicker
                  name="id_manufacture"
                  value={formValue.id_manufacture}
                  block
                  data={brandData}
                  valueKey="id_manufacture"
                  labelKey="manufacture_desc"
                  onChange={(value)=>{
                    setFormValue({...formValue, id_manufacture:value})
                  }}
                />
                {errors.id_manufacture && (
                  <p style={{ color: "red" }}>{errors.id_manufacture}</p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Storage Temperature</Form.ControlLabel>
                <SelectPicker
                  name="room_temperature"
                  value={formValue.room_temperature}
                  block
                  valueKey="value"
                  labelKey="label"
                  data={temperatureList}
                  onChange={(value) => {
                    setFormValue({ ...formValue, room_temperature: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      room_temperature: null,
                    }));
                  }}
                />
                {errors.room_temperature && (
                  <p style={{ color: "red" }}>{errors.room_temperature}</p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Reagen Type</Form.ControlLabel>
                <SelectPicker
                  name="id_type"
                  value={formValue.id_type}
                  block
                  readOnly
                  data={mapping}
                  valueKey="id_type"
                  labelKey="type_desc"
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_type: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      id_type: null,
                    }));
                  }}
                />
                {errors.id_type && (
                  <p style={{ color: "red" }}>{errors.id_type}</p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Reagen Criteria</Form.ControlLabel>
                <SelectPicker
                  name="id_criteria"
                  value={formValue.id_criteria}
                  block
                  readOnly
                  data={mapping}
                  valueKey="id_criteria"
                  labelKey="criteria_desc"
                  onChange={(value) => {
                    setFormValue({ ...formValue, id_criteria: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      id_criteria: null,
                    }));
                  }}
                />
                {errors.id_criteria && (
                  <p style={{ color: "red" }}>{errors.id_criteria}</p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Batch Number</Form.ControlLabel>
                <Form.Control
                  name="batch_no"
                  value={formValue.batch_no || ""}
                  onChange={(value) => {
                    setFormValue({ ...formValue, batch_no: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      batch_no: null,
                    }));
                  }}
                />
                {errors.batch_no && (
                  <p style={{ color: "red" }}>{errors.batch_no}</p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Expired Date</Form.ControlLabel>
                <DatePicker
                  name="expired_date"
                  value={formValue.expired_date}
                  defaultValue={new Date()}
                  shouldDisableDate={(date) => isBefore(date, startOfToday())}
                  block
                  format="dd-MM-yyyy"
                  onChange={(value) => {
                    setFormValue({ ...formValue, expired_date: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      expired_date: null,
                    }));
                  }}
                />
                {errors.expired_date && (
                  <p style={{ color: "red" }}>{errors.expired_date}</p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Amount</Form.ControlLabel>
                <Form.Control
                  name="amount"
                  value={formValue.amount || ""}
                  onChange={(value) => {
                    if (!isAmountDisabled) {
                      setFormValue({ ...formValue, amount: parseInt(value) });
                      setErrors((prevErrors) => ({
                        ...prevErrors,
                        amount: null,
                      }));
                    }
                  }}
                  disabled={isAmountDisabled}
                />
                {amountErrorMessage && (
                  <p style={{ color: "red" }}>{amountErrorMessage}</p>
                )}
              </Form.Group>

              <Form.Group>
                <Form.ControlLabel>Rack Description</Form.ControlLabel>
                {errors.amount && (
                  <p style={{ color: "red" }}>{errors.amount}</p>
                )}
                <Row>
                  {showRack &&
                    rack.map((item) => {
                      const rackValue =
                        rackValues[item.id_rack]?.[item.floor_level_desc]?.[
                          item.row_level_desc
                        ] || 0;
                      const isFull = rackValue >= item.space_available;
                      const isAllocated = rackValue > 0;

                      return (
                        <Col
                          key={`${item.id_rack}-${item.floor_level_desc}-${item.row_level_desc}`}
                          md={8}
                          sm={12}
                          style={{ marginBottom: "16px" }}
                        >
                          <Panel
                            bordered
                            header={item.rack_desc}
                            style={{
                              backgroundColor: isFull
                                ? "#ffcccc"
                                : isAllocated
                                ? "#d4edda"
                                : "white",
                            }}
                          >
                            <div style={{ marginBottom: "8px" }}>
                              <strong> Max Capacity:</strong> {item.capacity}
                            </div>
                            <div style={{ marginBottom: "8px" }}>
                              <strong>Space Available:</strong>{" "}
                              {item.space_available}
                            </div>
                            <div style={{ marginBottom: "8px" }}>
                              <strong>Floor Level:</strong>{" "}
                              {item.floor_level_desc}
                            </div>
                            <div style={{ marginBottom: "8px" }}>
                              <strong>Row Level:</strong> {item.row_level_desc}
                            </div>
                            <InputGroup>
                              <InputGroup.Button
                                onClick={() =>
                                  handleMinus(
                                    item.id_rack,
                                    item.floor_level_desc,
                                    item.row_level_desc
                                  )
                                }
                                disabled={rackValue <= 0}
                              >
                                -
                              </InputGroup.Button>
                              <InputNumber
                                className="custom-input-number"
                                value={rackValue}
                                onChange={(value) =>
                                  handleChange(
                                    value,
                                    item.id_rack,
                                    item.floor_level_desc,
                                    item.row_level_desc,
                                    item.space_available
                                  )
                                }
                                min={0}
                                max={Math.min(
                                  item.space_available,
                                  formValue.amount
                                )}
                                format="number"
                              />
                              <InputGroup.Button
                                onClick={() =>
                                  handlePlus(
                                    item.id_rack,
                                    item.floor_level_desc,
                                    item.row_level_desc,
                                    item.space_available
                                  )
                                }
                                disabled={
                                  rackValue >= item.space_available ||
                                  getTotalAddedAmount() >= formValue.amount
                                }
                              >
                                +
                              </InputGroup.Button>
                            </InputGroup>
                          </Panel>
                        </Col>
                      );
                    })}
                </Row>
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowAddModal(false);
                setFormValue(emptyFormValue);
                setErrors({});
                setShowRack(false);
                setRackValues({});
                setIsAmountDisabled(false);
              }}
              appearance="subtle"
            >
              Cancel
            </Button>

            {!showRack ? (
              <Button onClick={handleGetRackApi} appearance="primary">
                Get Rack
              </Button>
            ) : (
              <>
                <Button
                  onClick={handleResetAmount}
                  appearance="default"
                  style={{ backgroundColor: "#ea4545", color: "white" }}
                >
                  Reset Amount
                </Button>

                <Button
                  onClick={() => {
                    handleSubmit(handleAddApi);
                  }}
                  appearance="primary"
                  type="submit"
                >
                  Add
                </Button>
              </>
            )}

            {loading && (
              <Loader
                backdrop
                size="md"
                vertical
                content="Adding Data..."
                active={loading}
              />
            )}
          </Modal.Footer>
        </Modal>

        {/* Edit Modal */}
        <Modal
          backdrop="static"
          open={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setFormValue(emptyFormValue);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Edit Inbound Reagen</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form fluid>
              {/* <Form.Group>
                                <Form.ControlLabel>Criteria Reagen</Form.ControlLabel>
                                <Form.Control
                                    name="id_criteria_reagen"
                                    value={formValue.id_criteria_reagen}
                                    accepter={SelectPicker}
                                    block
                                    data={mapping}
                                    valueKey="id_criteria_reagen"
                                    labelKey="reagen_name" 
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, id_criteria_reagen: value })
                                        setErrors((prevErrors) => ({
                                            ...prevErrors,
                                            id_criteria_reagen: null,
                                        }))
                                    }}
                                />
                                {errors.id_criteria_reagen && <p style={{ color: 'red' }}>{errors.id_criteria_reagen}</p>}
                </Form.Group> */}
              <Form.Group>
                <Form.ControlLabel>Storage Temperature</Form.ControlLabel>
                <SelectPicker
                  name="room_temperature"
                  value={formValue.room_temperature}
                  block
                  valueKey="value"
                  labelKey="label"
                  data={temperatureList}
                  onChange={(value) => {
                    setFormValue({ ...formValue, room_temperature: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      room_temperature: null,
                    }));
                  }}
                />
                {errors.room_temperature && (
                  <p style={{ color: "red" }}>{errors.room_temperature}</p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Batch Number</Form.ControlLabel>
                <Form.Control
                  name="batch_no"
                  value={formValue.batch_no || ""}
                  onChange={(value) => {
                    setFormValue({ ...formValue, batch_no: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      batch_no: null,
                    }));
                  }}
                />
                {errors.batch_no && (
                  <p style={{ color: "red" }}>{errors.batch_no}</p>
                )}
              </Form.Group>
              <Form.Group>
                <Form.ControlLabel>Expired Date</Form.ControlLabel>
                <DatePicker
                  name="expired_date"
                  value={new Date(formValue.expired_date)}
                  defaultValue={new Date(formValue.expired_date)}
                  shouldDisableDate={(date) => isBefore(date, startOfToday())}
                  block
                  format="dd-MM-yyyy"
                  onChange={(value) => {
                    setFormValue({ ...formValue, expired_date: value });
                    setErrors((prevErrors) => ({
                      ...prevErrors,
                      expired_date: null,
                    }));
                  }}
                />
                {errors.expired_date && (
                  <p style={{ color: "red" }}>{errors.expired_date}</p>
                )}
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              onClick={() => {
                setShowEditModal(false);
                setFormValue(emptyFormValue);
                setErrors({});
              }}
              appearance="subtle"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleSubmit(() => handleEditApi(formValue.id_inbound_reagen));
              }}
              appearance="primary"
              type="submit"
            >
              Edit
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Detail Modal */}
        <Modal
          backdrop="static"
          open={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setOverInput(0);
          }}
          overflow={false}
        >
          <Modal.Header>
            <Modal.Title>Inbound Reagen Detail</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {overInput === 0 ? (
              <p>No Over Input</p>
            ) : (
              <p>{overInput} Rejected due to unavailable capacity</p>
            )}

            {Array.isArray(detail) && detail.length > 0 ? (
              detail.map((item, index) => (
                <div key={index}>
                  <Panel bordered className="mb-3">
                    <p className="mb-2">
                      <span className="fw-bold">ID Rack: </span>
                      {item.id_rack}
                    </p>
                    <p className="mb-2">
                      <span className="fw-bold">Rack Description: </span>
                      {item.rack_desc}
                    </p>
                    <p className="mb-2">
                      <span className="fw-bold">Floor Level: </span>
                      {item.floor_level_desc}
                    </p>
                    <p className="mb-2">
                      <span className="fw-bold">Row Level: </span>
                      {item.row_level_desc}
                    </p>
                    <p className="mb-2">
                      <span className="fw-bold">Stored Amount: </span>
                      {item.stored_amount}
                    </p>
                  </Panel>
                </div>
              ))
            ) : (
              <p>No details available</p>
            )}
          </Modal.Body>
        </Modal>
      </ContainerLayout>
    </>
  );
}
