import { useEffect, useState } from "react";
import Head from "next/head"
import { <PERSON><PERSON><PERSON>rumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, useToaster, Checkbox, ButtonGroup } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import ReloadIcon from '@rsuite/icons/Reload';
import Messages from "@/components/Messages";
import { useRouter } from "next/router";
import API_StockCorrection from "@/pages/api/stock_correction/api_stock_correction";

export default function EditCorrectionDetailPage() {

    const [moduleName, setModuleName] = useState("");
    const { HeaderCell, Cell, Column } = Table;
    const [activeCorrectionId, setActiveCorrectionId] = useState({});
    const [detailRequest, setDetailRequest] = useState([]);
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [props, setProps] = useState([]);
    const [recoverLocator, setRecoverLocator] = useState([]);
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);
    const toaster = useToaster();
    const router = useRouter();

    const updateStagingValue = {
        status_staging: null,
        data: [
            {
                id_inbound_reagen_locator: null,
            }
        ]
    };
    const [updateStagingStatus, setUpdateStagingStatus] = useState(updateStagingValue);

    const updateStagingDataValue = (recoverLocator) => {
        const idInboundReagenLocator = recoverLocator.map(item => ({
            id_inbound_reagen_locator: item.id_inbound_reagen_locator
        }));
        setUpdateStagingStatus({
            ...updateStagingStatus,
            data: idInboundReagenLocator,
        });
    };

    const handleEditStagingStatusApi = async () => {
        try {
            const result = await API_StockCorrection().editStatusStagingByLocator({
                ...updateStagingStatus,
                status_staging: 2
            });

            console.log("result", result)

            if (result.status === 200) {
                setShowConfirmationModal(false);
                setRecoverLocator([]);
                setUpdateStagingStatus(updateStagingValue);
                handleGetActiveCorrection();

                toaster.push(
                    Messages("success", "Success recover Reagen!"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
            } else if (result.status === 400) {
                toaster.push(
                    Messages("error", `Error: "There is no Active Correction Request"`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setShowConfirmationModal(false);
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    }

    const handleGetActiveCorrection = async () => {
        const res = await API_StockCorrection().getActiveBySupervisor();
        setActiveCorrectionId(res.data ? res.data : {})
        console.log("active id", res.data)
    }

    const handleGetApprovalRecoveryApi = async (activeCorrectionId) => {
        const res = await API_StockCorrection().getApprovalDetailRecovery({ id_reagen_correction: activeCorrectionId });
        setDetailRequest(res.data ? res.data : [])
    }

    const handleSearch = (value) => {
        setSearchKeyword(value);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = detailRequest
        .filter((rowData, i) => {
            const searchFields = [
                "id_inbound_reagen_locator",
                "reagen_name",
                "rack_desc",
                "floor_level_desc",
                "row_level_desc",
            ];

            const matchesSearch = searchFields.some((field) =>
                rowData[field]
                    ?.toString()
                    .toLowerCase()
                    .includes(searchKeyword.toLowerCase())
            );
            return matchesSearch;
        })

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    }

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? getFilteredData().length : detailRequest.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("stock_correction/reagen_staging")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            handleGetActiveCorrection();
        }
    }, []);

    useEffect(() => {
        handleGetApprovalRecoveryApi(activeCorrectionId.id_reagen_correction);
    }, [activeCorrectionId]);

    const handleReagenNameClick = () => {
        router.push("/user_module/stock_correction/reagen_staging")
    }

    const handleRackDescriptionClick = () => {
        router.push("/user_module/stock_correction/reagen_staging_by_rack")
    }

    const handleViewDetailClick = () => {
        router.push("/user_module/stock_correction/edit_correction_detail")
    }

    return (
        <>
            <div>
                <Head>
                    <title>Edit Detail Correction</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Quality</Breadcrumb.Item>
                                    <Breadcrumb.Item>Stock Correction</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Edit Correction Detail</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Edit Correction Detail</h5>
                                <Stack className="flex gap-2">
                                    <ButtonGroup>
                                        <Button
                                            onClick={handleReagenNameClick}
                                            appearance="primary">
                                            Search by Reagen Name
                                        </Button>
                                        <Button
                                            onClick={handleRackDescriptionClick}
                                            appearance="primary">
                                            Search by Rack Description
                                        </Button>
                                        <Button
                                            appearance="primary"
                                            onClick={handleViewDetailClick}
                                            active>
                                            View Detail
                                        </Button>
                                    </ButtonGroup>
                                </Stack>
                            </Stack>}>
                    </Panel>

                    <Panel
                        bordered
                        bodyFill
                        header={
                            <Stack justifyContent="space-between">
                                <IconButton
                                    icon={<ReloadIcon />}
                                    appearance="primary"
                                    onClick={() => {
                                        updateStagingDataValue(recoverLocator);
                                        setShowConfirmationModal(true);
                                    }}>
                                    Recover
                                </IconButton>

                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="search"
                                        value={searchKeyword}
                                        onChange={handleSearch}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}>
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                        }
                    >
                        <Table
                            bordered
                            cellBordered
                            height={400}
                            data={getPaginatedData(getFilteredData(), limit, page)}
                            sortColumn={sortColumn}
                            sortType={sortType}
                            onSortColumn={handleSortColumn}
                        >
                            <Column width={70} align="center" fixed>
                                <HeaderCell>No</HeaderCell>
                                <Cell>
                                    {(rowData, rowIndex) => {
                                        return rowIndex + 1 + limit * (page - 1);
                                    }}
                                </Cell>
                            </Column>
                            <Column width={140} align="center" sortable>
                                <HeaderCell>ID Locator</HeaderCell>
                                <Cell dataKey="id_inbound_reagen_locator" />
                            </Column>
                            <Column width={220} align="center" sortable>
                                <HeaderCell>Reagen Name</HeaderCell>
                                <Cell dataKey="reagen_name" />
                            </Column>
                            <Column width={220} align="center" sortable>
                                <HeaderCell>Expired Date</HeaderCell>
                                <Cell>
                                    {(rowData) => new Date(rowData.expired_date).toLocaleDateString('en-GB')}
                                </Cell>
                            </Column>
                            <Column width={220} align="center" sortable>
                                <HeaderCell>Rack</HeaderCell>
                                <Cell dataKey="rack_desc" />
                            </Column>
                            <Column width={120} align="center" sortable>
                                <HeaderCell>Floor</HeaderCell>
                                <Cell dataKey="floor_level_desc" />
                            </Column>
                            <Column width={120} align="center" sortable>
                                <HeaderCell>Row</HeaderCell>
                                <Cell dataKey="row_level_desc" />
                            </Column>
                            <Column width={120} fixed="right" align="center">
                                <HeaderCell>Select</HeaderCell>
                                <Cell style={{ padding: "8px" }}>
                                    {(rowData) => (
                                        <div>
                                            <Checkbox
                                                checked={recoverLocator.includes(rowData)}
                                                onChange={() => {
                                                    if (recoverLocator.includes(rowData)) {
                                                        setRecoverLocator(prevRecoverLocator =>
                                                            prevRecoverLocator.filter(item => item !== rowData)
                                                        );
                                                    } else {
                                                        setRecoverLocator(prevRecoverLocator => [...prevRecoverLocator, rowData]);
                                                    }
                                                }}
                                            />
                                        </div>
                                    )}
                                </Cell>
                            </Column>
                        </Table>
                        <div style={{ padding: 20 }}>
                            <Pagination
                                prev
                                next
                                first
                                last
                                ellipsis
                                boundaryLinks
                                maxButtons={5}
                                size="xs"
                                layout={["total", "-", "limit", "|", "pager", "skip"]}
                                limitOptions={[10, 30, 50]}
                                total={totalRowCount}
                                limit={limit}
                                activePage={page}
                                onChangePage={setPage}
                                onChangeLimit={handleChangeLimit}
                            />
                        </div>
                    </Panel>
                </div>

                <Modal
                    backdrop="static"
                    open={showConfirmationModal}
                    onClose={() => setShowConfirmationModal(false)}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Recover Confirmation</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        {recoverLocator.length > 0 ? (
                            recoverLocator.map((item) => (
                                <div key={item.id_inbound_reagen_locator}>
                                    <Panel bordered className="mb-3">
                                        <p className="mb-2"><span className="fw-bold">ID Inbound Reagen Locator : </span>{item.id_inbound_reagen_locator}</p>
                                        <p className="mb-2"><span className="fw-bold">Reagen Name : </span>{item.reagen_name}</p>
                                        <p className="mb-2"><span className="fw-bold">Rack : </span>{item.rack_desc}</p>
                                        <p className="mb-2"><span className="fw-bold">Floor : </span>{item.floor_level_desc}</p>
                                        <p className="mb-2"><span className="fw-bold">Row : </span>{item.row_level_desc}</p>
                                    </Panel>
                                </div>
                            ))
                        ) :
                            (
                                <p>No Locator Selected</p>
                            )
                        }
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowConfirmationModal(false)
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            disabled={recoverLocator.length === 0}
                            onClick={() => {
                                handleEditStagingStatusApi();
                                setShowConfirmationModal(false);
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Recover
                        </Button>
                    </Modal.Footer>
                </Modal>

            </ContainerLayout>
        </>
    )
}
