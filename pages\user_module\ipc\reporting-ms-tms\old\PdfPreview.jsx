import ipcApi from "@/pages/api/ipcApi";
import { faDownload, faFileDownload } from "@fortawesome/free-solid-svg-icons";
import Head from "next/head";
import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { But<PERSON>, Stack } from "rsuite";
import API_IPC from "@/pages/api/api_ipc";
import ChartDataLabels from "chartjs-plugin-datalabels";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
} from "chart.js";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
  ChartDataLabels
);

import { Line } from "react-chartjs-2";

export default function PdfPreview({
  ssr_batch_code,
  ssr_dashboarddata,
}) {
  const [loading, setLoading] = useState(false);

  const [stateShowDownload, setStateShowDownload] = useState(true);
  const [clickDownload, setClickDownload] = useState(false);
  let rowNumber = 1;

  const generatePDF = async () => {
    const input = document.getElementById("printedDocument");
    const pdf = new jsPDF("p", "mm", "a4");

    const canvas = await html2canvas(input);
    const imageData = canvas.toDataURL("image/png");

    pdf.addImage(imageData, "PNG", 0, 0, 210, 297); // A4 size
    pdf.save(
      `Print Out IPC BatchCode ${ssr_batch_code}`
    );
    // setClickDownload(true)
    // setStateShowDownload(false)
   document.getElementById("headdownload").remove()
   //console.log(status)    
  };

    useEffect(() => {
      // window.print()
      // if (stateClicked) {
      //   const status = !stateClicked
      //   setStateClicked(status)
      //   //document.getElementById("headdownload").append()
      // }
      // console.log("first")
      
      if (clickDownload) {
        setClickDownload(false)
        window.print()          
      }

      // console.log(ssr_dashboarddata)
      setStateShowDownload(true)
    }, [stateShowDownload]);
    


    const prepareHardnessChartData = (data) => {
      const labels = data.map((item) => item.time);
      const avg = data.map((item) => item.AVG_H);
      const max = data.map((item) => item.MAX_H);
      const min = data.map((item) => item.MIN_H);
  
      return {
        labels,
        datasets: [
          {
            label: "Avg Hardness",
            data: avg,
            backgroundColor: "#FF0000",
            borderColor: "#FF0000",
          },
          {
            label: "Max Hardness",
            data: max,
            backgroundColor: "#0000FF",
            borderColor: "#0000FF",
          },
          {
            label: "Min Hardness",
            data: min,
            backgroundColor: "#FFA500",
            borderColor: "#FFA500",
          },
        ],
      };
    };
  
    const LineHardnessChart = ({ chartData }) => {
      const options = {
        responsive: true,
        plugins: {
          legend: {
            position: "top",
          },
          title: {
            display: true,
            text: `${ssr_batch_code} IPC TRIAL: HARDNESS`
          },
          datalabels: {
            anchor: "end",
            align: "end",
            color: function (context) {
              return context.dataset.backgroundColor;
            },
            font: {
              weight: "bold",
            },
            formatter: (value, context) => {
              const formattedValue = value.toLocaleString();
              return formattedValue;
            },
          },
        },
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      };
  
      return <Line data={chartData} options={options} />;
    };
  
    const prepareDiameterChartData = (data) => {
      const labels = data.map((item) => item.time);
      const avg = data.map((item) => item.AVG_D);
      const max = data.map((item) => item.MAX_D);
      const min = data.map((item) => item.MIN_D);
  
      return {
        labels,
        datasets: [
          {
            label: "Avg Diameter",
            data: avg,
            backgroundColor: "#FF0000",
            borderColor: "#FF0000",
          },
          {
            label: "Max Diameter",
            data: max,
            backgroundColor: "#0000FF",
            borderColor: "#0000FF",
          },
          {
            label: "Min Diameter",
            data: min,
            backgroundColor: "#FFA500",
            borderColor: "#FFA500",
          },
        ],
      };
    };
  
    const LineDiameterChart = ({ chartData }) => {
      const options = {
        responsive: true,
        plugins: {
          legend: {
            position: "top",
          },
          title: {
            display: true,
            text: `${ssr_batch_code} IPC TRIAL: DIAMETER`
          },
          datalabels: {
            anchor: "end",
            align: "end",
            color: function (context) {
              return context.dataset.backgroundColor;
            },
            font: {
              weight: "bold",
            },
            formatter: (value, context) => {
              const formattedValue = value.toLocaleString();
              return formattedValue;
            },
          },
        },
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      };
  
      return <Line data={chartData} options={options} />;
    };
    
    const prepareThicknessChartData = (data) => {
      const labels = data.map((item) => item.time);
      const avg = data.map((item) => item.AVG_T);
      const max = data.map((item) => item.MAX_T);
      const min = data.map((item) => item.MIN_T);
  
      return {
        labels,
        datasets: [
          {
            label: "Avg Thickness",
            data: avg,
            backgroundColor: "#FF0000",
            borderColor: "#FF0000",
          },
          {
            label: "Max Thickness",
            data: max,
            backgroundColor: "#0000FF",
            borderColor: "#0000FF",
          },
          {
            label: "Min Thickness",
            data: min,
            backgroundColor: "#FFA500",
            borderColor: "#FFA500",
          },
        ],
      };
    };
  
    const LineThicknessChart = ({ chartData }) => {
      const options = {
        responsive: true,
        plugins: {
          legend: {
            position: "top",
          },
          title: {
            display: true,
            text: `${ssr_batch_code} IPC TRIAL: THICKNESS`
          },
          datalabels: {
            anchor: "end",
            align: "end",
            color: function (context) {
              return context.dataset.backgroundColor;
            },
            font: {
              weight: "bold",
            },
            formatter: (value, context) => {
              const formattedValue = value.toLocaleString();
              return formattedValue;
            },
          },
        },
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      };
  
      return <Line data={chartData} options={options} />;
    };
  


  return (
    <>
      <Head>
        <title>
          Print out Reporting Measurement - In Process Control {ssr_batch_code}
        </title>
      </Head>
      <div>
      {stateShowDownload &&(
        <div
          id="headdownload"
          style={{
            width: "100%",
            padding: "1em",
            backgroundColor: "#2c2c30",
            boxShadow: "2px 2px 10px #6c6b75",
            position: "-webkit-sticky",
            position: "sticky",
            top: 0,
          }}
        >
          <Stack justifyContent="space-between">
            <Stack>
              <p style={{ color: "white", fontSize: "1em" }}>
                Print Out Reporting Measurement - In Process Control ID :{ssr_batch_code}
              </p>
            </Stack>
            <Stack>
              <Button title="Download" onClick={generatePDF}>
                <FontAwesomeIcon
                  icon={faFileDownload}
                  style={{ fontSize: 15 }}
                />
              </Button>
            </Stack>
          </Stack>
        </div>
        )}
        <div style={{ width: "100%", backgroundColor: "#65656b" }}>
          <div
            id="printedDocument"
            style={{
              width: "100%",
              backgroundColor: "white",
              margin: "auto",
              padding: "1.5em",
            }}
          >
            <div>
              <img
                src="/Logo_kalbe_detail.png"
                alt="Logo_kalbe_detail.png"
                style={{ width: 150 }}
              />
            </div>
            <div style={{ marginBottom: "2em", marginTop: "2em" }}>
              <p
                style={{
                  textAlign: "center",
                  fontWeight: "bold",
                  fontSize: "2em",
                }}
              >
                REPORTING MEASUREMENT {ssr_batch_code}
              </p>
            </div>
            <div>
            <LineHardnessChart
                chartData={prepareHardnessChartData(ssr_dashboarddata)}
                plugins={[ChartDataLabels]}
                loading={loading}
              />

              {/* <LineDiameterChart
                chartData={prepareDiameterChartData(ssr_dashboarddata)}
                plugins={[ChartDataLabels]}
                loading={loading}
              /> */}

              <LineThicknessChart
                chartData={prepareThicknessChartData(ssr_dashboarddata)}
                plugins={[ChartDataLabels]}
                loading={loading}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export async function getServerSideProps(context) {

  const {getIpcDashboard} = API_IPC()
  const { query } = context;
  const { ssr_batch_code } = query;



  const res = await getIpcDashboard()

  let ssr_dashboarddata = []
  // console.log("res", res)
  if (res.status == 200) {
    const filteredTransactions = res.data.filter(transaction => transaction.product_code === ssr_batch_code);
    ssr_dashboarddata = filteredTransactions
  }

  

  

  return {
    props: {
      ssr_batch_code,
      ssr_dashboarddata,
    },
  };
}
