import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import MainContent from '@/components/layout/MainContent';
import { Dropdown, Button, Form, ButtonToolbar, Toggle } from 'rsuite';
import Head from 'next/head';
import withReactContent from 'sweetalert2-react-content';
import Swal from 'sweetalert2';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import RejectReasonApi from '@/pages/api/rejectReasonApi';

export default function AddRejectReason() {
  const MySwal = withReactContent(Swal);
  const [isSubmitButtonDisabled, setIsSubmitButtonDisabled] = useState(false);
  const [reasonDesc, setReasonDesc] = useState('');
  const [moduleName, setModuleName] = useState('');
  const [employeeId, setEmployeeId] = useState('');
  const [isActive, setIsActive] = useState(false);
  const router = useRouter();
  let path = 'masterdata/RejectReason';
  let { data } = router.query;
  data ? (data = JSON.parse(data)) : data;
  const [breadcrumbsData, setBreadcrumbsData] = useState([]);
  const { InsertRejectReason } = RejectReasonApi();

  useEffect(() => {
    const moduleNameValue = localStorage.getItem('module_name');
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
      router.push('/');
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      router.push('/dashboard');
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path),
    );
    if (validateUserAccess.length === 0) {
      router.push('/dashboard');
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ''
    ) {
      router.push('/dashboard');
      return;
    }

    const pathUrl = router.asPath;
    const asPathNestedRoutes = pathUrl.split('/').filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      asPathNestedRoutes[1],
      asPathNestedRoutes[2],
      asPathNestedRoutes[3],
    ];
    const capitalizeFirstLetter = (str) => {
      return str.charAt(0).toUpperCase() + str.slice(1);
    };
    const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
      const words = item.split(/(?=[A-Z])/);
      return words.map((word) => capitalizeFirstLetter(word)).join(' ');
    });

    setBreadcrumbsData(breadCrumbsResult);
    setModuleName(moduleNameValue);
    setEmployeeId(dataLogin.employee_id);
  }, []);

  //   submitHandler
  const submitHandler = async () => {
    if (reasonDesc === '') {
      // modalHandler("alert", "Please fill all required data !");
      MySwal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Please fill all required data!',
      });
      return;
    } else {
      MySwal.showLoading();

      const dataInput = {
        reason_desc: reasonDesc,
        is_active: isActive ? 1 : 0,
        created_by: employeeId,
      };

      const { data, message, status } = await InsertRejectReason(dataInput);

      if (status === 200) {
        setIsSubmitButtonDisabled(true);
        MySwal.fire({
          position: 'center',
          icon: 'success',
          title: message,
          showConfirmButton: false,
          timer: 2500,
        });
        router.push('/user_module/masterdata/RejectReason');
      } else {
        MySwal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'Reject Reason insert FAILED.',
        });
      }
    }
  };

  return (
    <>
      <div>
        <Head>
          <title>Add Reject Reason</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <MainContent>
          <ModuleContentHeader
            breadcrumbs={breadcrumbsData}
            module_name={moduleName}
          />

          <Form layout="horizontal" onSubmit={submitHandler}>
            <Form.Group controlId="reasonDesc">
              <Form.ControlLabel>Reason Desc :</Form.ControlLabel>
              <Form.Control
                name="reasonDesc"
                type="text"
                value={reasonDesc}
                onChange={(value) => setReasonDesc(value)}
                required
              />
            </Form.Group>
            <Form.Group>
              <Form.ControlLabel>Active</Form.ControlLabel>
              <Toggle
                checked={isActive}
                onChange={() => setIsActive((value) => !value)}
              />
            </Form.Group>

            <Form.Group>
              <ButtonToolbar>
                <Button
                  appearance="primary"
                  type="submit"
                  disabled={isSubmitButtonDisabled}
                >
                  Submit
                </Button>
                <Button appearance="default" onClick={() => router.back()}>
                  Cancel
                </Button>
              </ButtonToolbar>
            </Form.Group>
          </Form>
        </MainContent>
      </ContainerLayout>
    </>
  );
}
