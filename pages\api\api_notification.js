import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  try {
    const response = await axios[method](
      `${process.env.NEXT_PUBLIC_BASE_URL}/v2/sakaplant-notification/${url}`,
      data
    );
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default function TsParameter_API() {
  return {
    getNotificationByEmployeeId: createApiFunction(
      "post",
      "get-notification-by-employee-id"
    ),
    countNotificationByEmployeeId: createApiFunction(
      "post",
      "count-notification-by-employee-id"
    ),
    readNotification: createApiFunction("post", "read-notification"),
    readAllNotification: createApiFunction("post", "read-all-notification"),
  };
}
