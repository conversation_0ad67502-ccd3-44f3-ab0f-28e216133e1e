import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  try {
    const response = await axios[method](
      `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-parameter/${url}`,
      data
    );
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default function TsParameter_API() {
  return {
    getAllParameterTs: createApiFunction("get", "get-all"),
    getActiveParameterTS: createApiFunction("get", "get-active"),
    addParameterTs: createApiFunction("post", "add"),
    updateParameterTs: createApiFunction("put", "update"),
    sfDeleteParameterTs: createApiFunction("put", "s-delete"),
  };
}

// import axios from "axios";

// export default function ParameterTS_API() {
//   const getAllParameterTs = async () => {
//     let payloadData = [];

//     await axios
//       .get(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/mobile-api/ts-main-categories/get-all`)
//       .then((res) => {
//         payloadData = res;
//         return payloadData;
//       })
//       .catch((err) => {
//         return err.response;
//       });
//     return payloadData;
//   };

//   const getActiveParameterTS = async () => {
//     let payloadData = [];

//     await axios
//       .get(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/mobile-api/ts-main-categories/get-active`)
//       .then((res) => {
//         payloadData = res;
//         return payloadData;
//       })
//       .catch((err) => {
//         return err.response;
//       });
//     return payloadData;
//   };

//   const addParameterTs = async (data) => {
//     let payloadData = [];

//     await axios
//       .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/mobile-api/ts-main-categories/add`, data)
//       .then((res) => {
//         payloadData = res;
//         return payloadData;
//       })
//       .catch((err) => {
//         return err.response;
//       });
//     return payloadData;
//   };

//   const updateParameterTs = async (data) => {
//     let payloadData = [];

//     await axios
//       .put(`${process.env.NEXT_PUBLIC_PIMS_SERVICE}/mobile-api/ts-main-categories/update`, data)
//       .then((res) => {
//         payloadData = res;
//         return payloadData;
//       })
//       .catch((err) => {
//         console.log(err);
//         return err.response;
//       });
//     return payloadData;
//   };

//   const sfDeleteParameterTs = async (data) => {
//     let payloadData = [];

//     await axios
//       .put(
//         `${process.env.NEXT_PUBLIC_PIMS_SERVICE}/mobile-api/ts-main-categories/s-delete`,
//         data
//       )
//       .then((res) => {
//         payloadData = res;
//         return payloadData;
//       })
//       .catch((err) => {
//         console.log(err);
//         return err.response;
//       });
//     return payloadData;
//   };

//   return {
//     getAllParameterTs,
//     getActiveParameterTS,
//     addParameterTs,
//     updateParameterTs,
//     sfDeleteParameterTs,
//   };
// }
