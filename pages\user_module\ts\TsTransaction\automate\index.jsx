import ContainerLayout from '@/components/layout/ContainerLayout';
import Head from 'next/head';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react'
import { <PERSON>readcrumb, Button, Form, InputGroup, Panel, Stack, Uploader, Input, SelectPicker, DatePicker, Table, Modal, Pagination, toaster, Loader } from 'rsuite';
import ApiTsDetail from "@/pages/api/ts/api_ts_detail";
import ApiMasterLine from "@/pages/api/ts/api_ts_master_line";
import Messages from '@/components/Messages';
import ApiTsHeader from '@/pages/api/ts/api_ts_header';
import ApiAttachments from '@/pages/api/ts/api_attachments';

function TsAutomate() {

    const router = useRouter();
    const [moduleName, setModuleName] = useState("");
    const [props, setProps] = useState([]);
    const { Header<PERSON>ell, Cell, Column } = Table;
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();

    const [currentState, setCurrentState] = useState(1);

    const emptyFormValue = {
        id_line: null,
        sediaan_type: null,
        product_code: null,
        product_name: null,
        batch_no: null,
        production_scale: null,
        trial_focus: null,
        ppi_no: null,
        process_purpose: null,
        background: null,
        process_date: null,
        binder_date: null,
        binder_mix_amount: null,
        binder_mix_time: null,
        binder_remarks: null,
        granule_date: null,
        granule_ampere: null,
        granule_power: null,
        granule_remarks: null,
        drying_date: null,
        drying_lod: null,
        drying_product_temp: null,
        drying_exhaust_temp: null,
        drying_remarks: null,
        sifting_date: null,
        sifting_screen_quadro: null,
        sifting_bin_tumbler: null,
        sifting_impeller_speed_1: null,
        sifting_impeller_speed_2: null,
        sifting_remarks: null,
        final_mix_date: null,
        final_mix_time_mix_1: null,
        final_mix_time_mix_2: null,
        ts_conclusion: null,
        ts_followup: null,
        bobot_granul: null,
        bobot_teoritis: null,
        rendemen: null,
        discussion: null,
        analyzed_data: null,
        spv_employee_id: null,
        ts_detail_trans: [],
    }
    const [formValue, setFormValue] = useState(emptyFormValue);
    const [formAttachments, setFormAttachments] = useState([]);

    const [dataProduct, setDataProduct] = useState([]);
    const [dataSpv, setDataSpv] = useState([]);
    const [dataDetail, setDataDetail] = useState([]);
    const [dataLine, setDataLine] = useState([]);
    const [selectedApiType, setSelectedApiType] = useState(null);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedDetail, setSelectedDetail] = useState(null);
    const [showSelectedDetailModal, setShowSelectedDetailModal] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        const moduleNameValue = localStorage.getItem("module_name");

        setProps(dataLogin);
        setModuleName(moduleNameValue);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("masterdata/TsMainCategories")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
        }
    }, []);

    const mapCurrentstate = {
        1: "Header",
        2: "Binder",
        3: "Granulasi",
        4: "Pengeringan",
        5: "Pengayakan",
        6: "Final Mix",
        7: "Analisis Data"
    }

    const handleNextState = (direction) => {
        if (direction === ">") {
            setCurrentState(currentState + 1);
        } else if (direction === "<") {
            setCurrentState(currentState - 1);
        }
    }

    const fetchData = async () => {
        const resProduct = await ApiTsDetail().getAllOfProduct()
        const resSpv = await ApiTsDetail().getAllActiveSpv()
        const resAllDetail = await ApiTsDetail().getAllDetail()
        const resLine = await ApiMasterLine().getAutomateMasterLine()

        setDataProduct(resProduct.data ? resProduct.data : [])
        setDataSpv(resSpv.data ? resSpv.data : [])
        setDataLine(resLine.data ? resLine.data : [])
        setDataDetail(resAllDetail.data ? resAllDetail.data : [])

        // const formatDataDetail = resAllDetail.data.map((item) => {
        //     const typeMap = {
        //         "PMA granulasi": "Granulasi",
        //         "EPH": "Pengeringan",
        //         "FBD": "Pengayakan"
        //     };

        //     return {
        //         ...item,
        //         type_api: typeMap[item.type_api] || item.type_api
        //     }

        // })
        // setDataDetail(formatDataDetail)
    }

    useEffect(() => {
        fetchData();
    }, [])

    const getDetailApiData = async (type) => {
        const res = await ApiTsDetail().getApiData({
            type_api: type
        })

        if (res.status === 200) {
            setFormValue((prevFormValue) => {
                const existingDetail = prevFormValue.ts_detail_trans || [];
                const typeExists = existingDetail.some((detail) => detail.type === formatType(currentState));

                return {
                    ...prevFormValue,
                    ts_detail_trans: typeExists
                        ? existingDetail
                        : [
                            ...existingDetail,
                            {
                                type: formatType(currentState),
                                data: res.data,
                            }
                        ]
                }
            })
            setShowDetailModal(false)
        }
    }

    const dataJenisSediaan = ['Kapsul', 'Tablet', 'Sirup'].map(
        item => ({ label: item, value: item })
    );

    const dataSkalaProduksi = ['Pilot', 'Commercial'].map(
        item => ({ label: item, value: item })
    );

    const dataFokusTrial = ['Carry Over', 'Diversifikasi', 'Others'].map(
        item => ({ label: item, value: item })
    );

    const dataScreenQuadro = ['10', '20', '30'].map(
        item => ({ label: item, value: item })
    );

    const dataBinTumbler = ['10', '20', '30'].map(
        item => ({ label: item, value: item })
    );

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const getPaginatedData = (currentDataDetail, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return currentDataDetail.slice(start, end);
    }

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return formValue.ts_detail_trans.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return formValue.ts_detail_trans
    };

    const totalRowCount = formValue.ts_detail_trans.length;

    const formatType = (type) => {
        switch (type) {
            case 2:
                return "Binder";
            case 3:
                return "Granulasi";
            case 4:
                return "Pengeringan";
            case 5:
                return "Pengayakan";
            default:
                return "";
        }
    }

    const handleUploadAttachments = (file, type) => {
        setFormAttachments((prevAttachments) => [
            ...prevAttachments,
            {
                type,
                data: file
            },
        ]);
    };

    const handleRemoveAttachments = (file) => {
        setFormAttachments((prevAttachments) => {
            const newAttachments = [...prevAttachments];
            const index = newAttachments.findIndex((attachment) => attachment.data.fileKey === file.fileKey);
            if (index !== -1) {
                newAttachments.splice(index, 1);
            }
            return newAttachments;
        });
    };

    const handleRemoveDetail = (index) => {
        setFormValue((prevFormValue) => {
            const existingDetail = prevFormValue.ts_detail_trans || [];
            existingDetail.splice(index, 1);
            return { ...prevFormValue, ts_detail_trans: existingDetail };
        });
    };

    const DetailTable = () => {
        return (
            <Panel bordered bodyFill className='mt-3'>
                <Table
                    bordered
                    cellBordered
                    height={250}
                    data={getPaginatedData(getFilteredData(formatType(currentState)), limit, page)}
                    sortColumn={sortColumn}
                    sortType={sortType}
                    onSortColumn={handleSortColumn}
                >
                    <Column width={70} align="center">
                        <HeaderCell>No</HeaderCell>
                        <Cell>
                            {(rowData, rowIndex) => (page - 1) * limit + rowIndex + 1}
                        </Cell>
                    </Column>
                    <Column flexGrow={2} align='center'>
                        <HeaderCell>Type</HeaderCell>
                        <Cell dataKey="type" />
                    </Column>
                    <Column flexGrow={1}>
                        <HeaderCell align='center'>Action</HeaderCell>
                        <Cell style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                            {(rowData, rowIndex) => (
                                <div className='space-x-2'>
                                    <Button
                                        appearance="ghost"
                                        size="xs"
                                        onClick={() => {
                                            setSelectedDetail(rowData);
                                            setShowSelectedDetailModal(true);
                                        }}
                                    >
                                        View Detail
                                    </Button>
                                    <Button
                                        appearance="ghost"
                                        color='red'
                                        size="xs"
                                        onClick={() => handleRemoveDetail(rowIndex)}
                                    >
                                        Remove
                                    </Button>
                                </div>
                            )}
                        </Cell>
                    </Column>
                </Table>
                <div style={{ padding: 20 }}>
                    <Pagination
                        prev
                        next
                        first
                        last
                        ellipsis
                        boundaryLinks
                        maxButtons={5}
                        size="xs"
                        layout={["total", "-", "limit", "|", "pager", "skip"]}
                        limitOptions={[10, 30, 50]}
                        total={totalRowCount}
                        limit={limit}
                        activePage={page}
                        onChangePage={setPage}
                        onChangeLimit={handleChangeLimit}
                    />
                </div>
            </Panel>
        )
    }

    const handleGetAttachmentsByType = (type) => {
        const attachments = formAttachments.filter((attachment) => attachment.type === type);
        return attachments?.map((attachment) => ({
            name: attachment?.data?.name,
            fileKey: attachment?.data?.fileKey,
            url: attachment?.data ? URL.createObjectURL(attachment?.data.blobFile) : '',
            size: attachment?.data?.blobFile?.size > 1024 * 1024
                ? `${(attachment?.data?.blobFile?.size / (1024 * 1024)).toFixed(2)}MB`
                : `${(attachment?.data?.blobFile?.size / 1024).toFixed(2)}KB`
        }));
    };

    const AddTsHeaderTransApi = async () => {
        setIsLoading(true);
        try {
            const res = await ApiTsHeader().addTsHeader(formValue)
            if (res.status === 200) {
                let failedUpload = 0;
                for (const fileItem of formAttachments) {
                    if (fileItem.data !== undefined) {
                        const formData = new FormData();
                        formData.append("Files", fileItem.data.blobFile, fileItem.data.name);
                        formData.append("id_header_trans", res.data);
                        formData.append("type", fileItem.type);
                        formData.append("path", "ts");
                        formData.append("created_by", props.employee_id);

                        const postRes = await ApiTsDetail().postAttachmentAuto(formData)
                        if (postRes.status !== 200) {
                            failedUpload++;
                        }
                    }
                }
                if (failedUpload === 0) {
                    toaster.push(
                        Messages("success", `Success Save Data!`),
                        {
                            placement: "topCenter",
                            duration: 5000
                        }
                    )
                    setFormValue(emptyFormValue);
                    setFormAttachments([]);
                    setCurrentState(1);
                } else {
                    toaster.push(
                        Messages("error", `Error: Failed to upload attachments!`),
                        {
                            placement: "topCenter",
                            duration: 5000
                        }
                    );
                }
            } else {
                toaster.push(
                    Messages("error", `Error: something error. ${res.message}!`),
                    {
                        placement: "topCenter",
                        duration: 5000
                    }
                );
            }
        } catch (error) {
            console.log("Axios Error", error)
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000
                }
            )
        } finally {
            setIsLoading(false);
        }
    }

    const validateSelectSpv = () => {
        const isValid = Object.keys(formValue).filter(key => key !== 'spv_employee_id')
            .every(key => formValue[key] !== null && formValue[key] !== '');
        return isValid;
    };

    const validateAddDetail = () => {
        const isValid = formValue.ts_detail_trans.find((detail) => detail.type === formatType(currentState));
        return isValid;
    }

    return (
        <>
            <div>
                <Head>
                    <title>TS Create Automate Line</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className='m-4'>
                    {
                        isLoading ? (
                            <Loader backdrop size="md" content="Saving data..." />
                        ) : (
                            <>
                                <Breadcrumb className='mt-2'>
                                    <Breadcrumb.Item href='/dashboard'>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>
                                        {moduleName ? moduleName : "User Module"}
                                    </Breadcrumb.Item>
                                    <Breadcrumb.Item active>TS Create Automate Line</Breadcrumb.Item>
                                </Breadcrumb>
                                <Panel bordered className='mb-2'>
                                    <Stack justifyContent='flex-start'>
                                        <h3>TS Create Automate Line</h3>
                                    </Stack>
                                </Panel>
                                <Panel bordered className='mb-2'>
                                    <div>
                                        <h5>{mapCurrentstate[currentState]}</h5>

                                        <div className='my-4'>
                                            <Form fluid>
                                                {currentState === 1 && (
                                                    <>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Line <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                name="id_line"
                                                                accepter={SelectPicker}
                                                                value={formValue.id_line}
                                                                data={dataLine}
                                                                valueKey="id_line"
                                                                labelKey="line_description"
                                                                block
                                                                onChange={(value) => setFormValue({ ...formValue, id_line: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Jenis Sediaan <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                name="sediaan_type"
                                                                accepter={SelectPicker}
                                                                value={formValue.sediaan_type}
                                                                data={dataJenisSediaan}
                                                                valueKey="value"
                                                                labelKey="label"
                                                                block
                                                                onChange={(value) => setFormValue({ ...formValue, sediaan_type: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Kode Produk <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                name="product_code"
                                                                accepter={SelectPicker}
                                                                value={formValue.product_code}
                                                                data={dataProduct}
                                                                valueKey="product_code"
                                                                labelKey="product_code"
                                                                block
                                                                onChange={(value) => setFormValue({ ...formValue, product_code: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Nama Produk <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="product_name"
                                                                value={formValue.product_name}
                                                                onChange={(value) => setFormValue({ ...formValue, product_name: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Kode Batch <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="batch_no"
                                                                value={formValue.batch_no}
                                                                onChange={(value) => setFormValue({ ...formValue, batch_no: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Skala Produksi <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                name="production_scale"
                                                                accepter={SelectPicker}
                                                                value={formValue.production_scale}
                                                                data={dataSkalaProduksi}
                                                                valueKey="value"
                                                                labelKey="value"
                                                                block
                                                                onChange={(value) => setFormValue({ ...formValue, production_scale: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Fokus Trial <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                name="trial_focus"
                                                                accepter={SelectPicker}
                                                                value={formValue.trial_focus}
                                                                data={dataFokusTrial}
                                                                valueKey="value"
                                                                labelKey="value"
                                                                block
                                                                onChange={(value) => setFormValue({ ...formValue, trial_focus: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>No PPI <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="ppi_no"
                                                                value={formValue.ppi_no}
                                                                onChange={(value) => setFormValue({ ...formValue, ppi_no: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Tujuan Proses <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="process_purpose"
                                                                value={formValue.process_purpose}
                                                                onChange={(value) => setFormValue({ ...formValue, process_purpose: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Background <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="background"
                                                                value={formValue.background}
                                                                onChange={(value) => setFormValue({ ...formValue, background: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Tanggal Proses <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <DatePicker
                                                                oneTap
                                                                value={formValue.process_date}
                                                                format="dd-MM-yyyy"
                                                                onChange={(value) => setFormValue({ ...formValue, process_date: value })}
                                                                block
                                                            />
                                                        </Form.Group>
                                                    </>
                                                )}
                                                {currentState === 2 && (
                                                    <>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Tanggal Binder <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <DatePicker
                                                                oneTap
                                                                value={formValue.binder_date}
                                                                format="dd-MM-yyyy"
                                                                onChange={(value) => setFormValue({ ...formValue, binder_date: value })}
                                                                block
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Jumlah Pelarut <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <InputGroup>
                                                                <Form.Control
                                                                    placeholder="binder_mix_amount"
                                                                    value={formValue.binder_mix_amount}
                                                                    onChange={(value) => setFormValue({ ...formValue, binder_mix_amount: parseFloat(value) })}
                                                                    type="number"
                                                                />
                                                                <InputGroup.Addon>L</InputGroup.Addon>
                                                            </InputGroup>
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Waktu Aduk <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <InputGroup>
                                                                <Form.Control
                                                                    placeholder="binder_mix_time"
                                                                    value={formValue.binder_mix_time}
                                                                    onChange={(value) => setFormValue({ ...formValue, binder_mix_time: parseFloat(value) })}
                                                                    type="number"
                                                                />
                                                                <InputGroup.Addon>Menit</InputGroup.Addon>
                                                            </InputGroup>
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Binder Remarks <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="binder_remarks"
                                                                value={formValue.binder_remarks}
                                                                onChange={(value) => setFormValue({ ...formValue, binder_remarks: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Lampiran</Form.ControlLabel>
                                                            <Uploader
                                                                key={formatType(currentState)}
                                                                listType="picture-text"
                                                                action=''
                                                                defaultFileList={handleGetAttachmentsByType(formatType(currentState))}
                                                                onUpload={(file) => handleUploadAttachments(file, formatType(currentState))}
                                                                onRemove={(file) => handleRemoveAttachments(file)}
                                                                renderFileInfo={(file, fileElement) => {
                                                                    return (
                                                                        <>
                                                                            <span>File Name: {file.name}</span>
                                                                            <br />
                                                                            <span style={{ color: 'gray' }}>{file.size}</span>
                                                                        </>
                                                                    );
                                                                }}
                                                            >
                                                                <Button appearance='ghost'>Ambil Gambar</Button>
                                                            </Uploader>
                                                        </Form.Group>
                                                    </>
                                                )}
                                                {currentState === 3 && (
                                                    <>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Tanggal Granulasi <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <DatePicker
                                                                oneTap
                                                                value={formValue.granule_date}
                                                                format="dd-MM-yyyy"
                                                                onChange={(value) => setFormValue({ ...formValue, granule_date: value })}
                                                                block
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Ampere <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <InputGroup>
                                                                <Form.Control
                                                                    placeholder="granule_ampere"
                                                                    value={formValue.granule_ampere}
                                                                    onChange={(value) => setFormValue({ ...formValue, granule_ampere: parseFloat(value) })}
                                                                    type="number"
                                                                />
                                                                <InputGroup.Addon>A</InputGroup.Addon>
                                                            </InputGroup>
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Power <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <InputGroup>
                                                                <Form.Control
                                                                    placeholder="granule_power"
                                                                    value={formValue.granule_power}
                                                                    onChange={(value) => setFormValue({ ...formValue, granule_power: parseFloat(value) })}
                                                                    type="number"
                                                                />
                                                                <InputGroup.Addon>Kwh</InputGroup.Addon>
                                                            </InputGroup>
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Granulasi Remarks <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="granule_remarks"
                                                                value={formValue.granule_remarks}
                                                                onChange={(value) => setFormValue({ ...formValue, granule_remarks: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Lampiran</Form.ControlLabel>
                                                            <Uploader
                                                                key={formatType(currentState)}
                                                                listType="picture-text"
                                                                action=''
                                                                defaultFileList={handleGetAttachmentsByType(formatType(currentState))}
                                                                onUpload={(file) => handleUploadAttachments(file, formatType(currentState))}
                                                                onRemove={(file) => handleRemoveAttachments(file)}
                                                                renderFileInfo={(file, fileElement) => {
                                                                    return (
                                                                        <>
                                                                            <span>File Name: {file.name}</span>
                                                                            <br />
                                                                            <span style={{ color: 'gray' }}>{file.size}</span>
                                                                        </>
                                                                    );
                                                                }}
                                                            >
                                                                <Button appearance='ghost'>Ambil Gambar</Button>
                                                            </Uploader>
                                                        </Form.Group>
                                                        <Button
                                                            appearance='primary'
                                                            onClick={() => setShowDetailModal(true)}
                                                            disabled={validateAddDetail()}
                                                        >+ Data Granulasi</Button>
                                                        <DetailTable />
                                                    </>
                                                )}
                                                {currentState === 4 && (
                                                    <>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Tanggal Pengeringan <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <DatePicker
                                                                oneTap
                                                                value={formValue.drying_date}
                                                                format="dd-MM-yyyy"
                                                                onChange={(value) => setFormValue({ ...formValue, drying_date: value })}
                                                                block
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>LOD <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <InputGroup>
                                                                <Form.Control
                                                                    placeholder="drying_lod"
                                                                    value={formValue.drying_lod}
                                                                    onChange={(value) => setFormValue({ ...formValue, drying_lod: parseFloat(value) })}
                                                                    type="number"
                                                                />
                                                                <InputGroup.Addon>%</InputGroup.Addon>
                                                            </InputGroup>
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Product Temp <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <InputGroup>
                                                                <Form.Control
                                                                    placeholder="drying_product_temp"
                                                                    value={formValue.drying_product_temp}
                                                                    onChange={(value) => setFormValue({ ...formValue, drying_product_temp: parseFloat(value) })}
                                                                    type="number"
                                                                />
                                                                <InputGroup.Addon>C</InputGroup.Addon>
                                                            </InputGroup>
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Exhaust Temp <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <InputGroup>
                                                                <Form.Control
                                                                    placeholder="drying_exhaust_temp"
                                                                    value={formValue.drying_exhaust_temp}
                                                                    onChange={(value) => setFormValue({ ...formValue, drying_exhaust_temp: parseFloat(value) })}
                                                                    type="number"
                                                                />
                                                                <InputGroup.Addon>C</InputGroup.Addon>
                                                            </InputGroup>
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Pengeringan Remarks <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="drying_remarks"
                                                                value={formValue.drying_remarks}
                                                                onChange={(value) => setFormValue({ ...formValue, drying_remarks: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Lampiran</Form.ControlLabel>
                                                            <Uploader
                                                                key={formatType(currentState)}
                                                                listType="picture-text"
                                                                action=''
                                                                defaultFileList={handleGetAttachmentsByType(formatType(currentState))}
                                                                onUpload={(file) => handleUploadAttachments(file, formatType(currentState))}
                                                                onRemove={(file) => handleRemoveAttachments(file)}
                                                                renderFileInfo={(file, fileElement) => {
                                                                    return (
                                                                        <>
                                                                            <span>File Name: {file.name}</span>
                                                                            <br />
                                                                            <span style={{ color: 'gray' }}>{file.size}</span>
                                                                        </>
                                                                    );
                                                                }}
                                                            >
                                                                <Button appearance='ghost'>Ambil Gambar</Button>
                                                            </Uploader>
                                                        </Form.Group>
                                                        <Button
                                                            appearance='primary'
                                                            onClick={() => setShowDetailModal(true)}
                                                            disabled={validateAddDetail()}
                                                        >+ Data Pengeringan</Button>
                                                        <DetailTable />
                                                    </>
                                                )}
                                                {currentState === 5 && (
                                                    <>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Tanggal Pengayakan <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <DatePicker
                                                                oneTap
                                                                value={formValue.sifting_date}
                                                                format="dd-MM-yyyy"
                                                                onChange={(value) => setFormValue({ ...formValue, sifting_date: value })}
                                                                block
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Screen Quadro <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                name="sifting_screen_quadro"
                                                                accepter={SelectPicker}
                                                                value={formValue.sifting_screen_quadro}
                                                                data={dataScreenQuadro}
                                                                valueKey="value"
                                                                labelKey="value"
                                                                block
                                                                onChange={(value) => setFormValue({ ...formValue, sifting_screen_quadro: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Bin Tumbler <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                name="sifting_bin_tumbler"
                                                                accepter={SelectPicker}
                                                                value={formValue.sifting_bin_tumbler}
                                                                data={dataBinTumbler}
                                                                valueKey="value"
                                                                labelKey="value"
                                                                block
                                                                onChange={(value) => setFormValue({ ...formValue, sifting_bin_tumbler: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Impeller Speed Quadro 1 <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <InputGroup>
                                                                <Form.Control
                                                                    placeholder="sifting_impeller_speed_1"
                                                                    value={formValue.sifting_impeller_speed_1}
                                                                    onChange={(value) => setFormValue({ ...formValue, sifting_impeller_speed_1: parseFloat(value) })}
                                                                    type="number"
                                                                />
                                                                <InputGroup.Addon>RPM</InputGroup.Addon>
                                                            </InputGroup>
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Impeller Speed Quadro 2 <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <InputGroup>
                                                                <Form.Control
                                                                    placeholder="sifting_impeller_speed_2"
                                                                    value={formValue.sifting_impeller_speed_2}
                                                                    onChange={(value) => setFormValue({ ...formValue, sifting_impeller_speed_2: parseFloat(value) })}
                                                                    type="number"
                                                                />
                                                                <InputGroup.Addon>RPM</InputGroup.Addon>
                                                            </InputGroup>
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Pengayakan Remarks <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="sifting_remarks"
                                                                value={formValue.sifting_remarks}
                                                                onChange={(value) => setFormValue({ ...formValue, sifting_remarks: value })}
                                                            />
                                                        </Form.Group>
                                                        <Button
                                                            appearance='primary'
                                                            onClick={() => setShowDetailModal(true)}
                                                            disabled={validateAddDetail()}
                                                        >+ Data Pengayakan</Button>
                                                        <DetailTable />
                                                    </>
                                                )}
                                                {currentState === 6 && (
                                                    <>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Tanggal Final Mix <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <DatePicker
                                                                oneTap
                                                                value={formValue.final_mix_date}
                                                                format="dd-MM-yyyy"
                                                                onChange={(value) => setFormValue({ ...formValue, final_mix_date: value })}
                                                                block
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Waktu Aduk 1 <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <InputGroup>
                                                                <Form.Control
                                                                    placeholder="final_mix_time_mix_1"
                                                                    value={formValue.final_mix_time_mix_1}
                                                                    onChange={(value) => setFormValue({ ...formValue, final_mix_time_mix_1: parseFloat(value) })}
                                                                    type="number"
                                                                />
                                                                <InputGroup.Addon>Menit</InputGroup.Addon>
                                                            </InputGroup>
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Waktu Aduk 2 <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <InputGroup>
                                                                <Form.Control
                                                                    placeholder="final_mix_time_mix_2"
                                                                    value={formValue.final_mix_time_mix_2}
                                                                    onChange={(value) => setFormValue({ ...formValue, final_mix_time_mix_2: parseFloat(value) })}
                                                                    type="number"
                                                                />
                                                                <InputGroup.Addon>Menit</InputGroup.Addon>
                                                            </InputGroup>
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Kesimpulan <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="ts_conclusion"
                                                                value={formValue.ts_conclusion}
                                                                onChange={(value) => setFormValue({ ...formValue, ts_conclusion: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Tindak Lanjut <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="ts_followup"
                                                                value={formValue.ts_followup}
                                                                onChange={(value) => setFormValue({ ...formValue, ts_followup: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Bobot Granul <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="bobot_granul"
                                                                value={formValue.bobot_granul}
                                                                onChange={(value) => setFormValue({ ...formValue, bobot_granul: parseFloat(value) })}
                                                                type="number"
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Bobot Teoritis <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="bobot_teoritis"
                                                                value={formValue.bobot_teoritis}
                                                                onChange={(value) => setFormValue({ ...formValue, bobot_teoritis: parseFloat(value) })}
                                                                type="number"
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Rendemen <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="rendemen"
                                                                value={formValue.rendemen}
                                                                onChange={(value) => setFormValue({ ...formValue, rendemen: parseFloat(value) })}
                                                                type="number"
                                                            />
                                                        </Form.Group>
                                                    </>
                                                )}
                                                {currentState === 7 && (
                                                    <>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Diskusi <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="discussion"
                                                                value={formValue.discussion}
                                                                onChange={(value) => setFormValue({ ...formValue, discussion: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Analisis Data <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                placeholder="analyzed_data"
                                                                value={formValue.analyzed_data}
                                                                onChange={(value) => setFormValue({ ...formValue, analyzed_data: value })}
                                                            />
                                                        </Form.Group>
                                                        <Form.Group>
                                                            <Form.ControlLabel>Spv <span className='text-red-500'>*</span></Form.ControlLabel>
                                                            <Form.Control
                                                                name="spv_employee_id"
                                                                accepter={SelectPicker}
                                                                value={formValue.spv_employee_id}
                                                                data={dataSpv}
                                                                valueKey="employee_id"
                                                                labelKey="employee_name"
                                                                block
                                                                onChange={(value) => setFormValue({ ...formValue, spv_employee_id: value })}
                                                                disabled={!validateSelectSpv()}
                                                            />
                                                            {
                                                                !validateSelectSpv() && (
                                                                    <Form.HelpText>All fields are required to select supervisor</Form.HelpText>
                                                                )
                                                            }
                                                        </Form.Group>
                                                    </>
                                                )}
                                            </Form>
                                        </div>

                                        <Stack justifyContent='space-between' className='mb-2'>
                                            {
                                                currentState !== 1 ? (
                                                    <Button onClick={() => handleNextState("<")}>Kembali ke Tahap {mapCurrentstate[currentState - 1]}</Button>
                                                ) : (
                                                    <div>
                                                    </div>
                                                )
                                            }
                                            {
                                                currentState !== 7 && (
                                                    <Button onClick={() => handleNextState(">")} appearance='primary'>Lanjut Tahap {mapCurrentstate[currentState + 1]}</Button>
                                                )
                                            }
                                        </Stack>
                                        <Stack justifyContent='end'>
                                            <Button appearance='primary' color='green' onClick={() => AddTsHeaderTransApi()}>Save</Button>
                                        </Stack>
                                    </div>
                                </Panel>
                            </>
                        )
                    }
                </div>

                <Modal
                    backdrop="static"
                    open={showDetailModal}
                    onClose={() => {
                        setShowDetailModal(false);
                        setSelectedApiType(null);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Detail {mapCurrentstate[currentState]}</Modal.Title>
                        <Modal.Body>
                            <SelectPicker
                                name="selected_api_type"
                                value={selectedApiType}
                                data={dataDetail}
                                valueKey="type_api"
                                labelKey="type_api"
                                block
                                onChange={(value) => setSelectedApiType(value)}
                            />
                        </Modal.Body>
                        <Modal.Footer>
                            <Button
                                onClick={() => {
                                    setShowDetailModal(false);
                                    setSelectedApiType(null);
                                }}
                                appearance='subtle'
                            >
                                Cancel
                            </Button>
                            <Button
                                onClick={() => {
                                    getDetailApiData(selectedApiType);
                                    setSelectedApiType(null);
                                }}
                                appearance='primary'
                            >
                                Add
                            </Button>
                        </Modal.Footer>
                    </Modal.Header>
                </Modal>

                <Modal
                    backdrop="static"
                    open={showSelectedDetailModal}
                    onClose={() => {
                        setShowSelectedDetailModal(false);
                        setSelectedDetail(null);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>{selectedDetail?.type} Data in JSON Format</Modal.Title>
                        <Modal.Body>
                            <Modal.Body>
                                <pre>
                                    {selectedDetail?.data && JSON.stringify(JSON.parse(selectedDetail?.data), null, 2)}
                                </pre>
                            </Modal.Body>
                        </Modal.Body>
                    </Modal.Header>
                </Modal>
            </ContainerLayout>
        </>
    )
}

export default TsAutomate