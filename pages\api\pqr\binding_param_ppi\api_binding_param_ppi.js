import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiBindingParamPpi() {
  return {
    getAllBindingParamPpi: createApiFunction("get", "pqr/pqr_binding_param_ppi/list"),
    getAllActiveBindingParamPpi: createApiFunction("get", "pqr/pqr_binding_param_ppi/list-active"),
    getIdBindingParamPpi: createApiFunction("post", "pqr/pqr_binding_param_ppi/id"),
    getIdPpiActiveBindingParamPpi: createApiFunction("post", "pqr/pqr_binding_param_ppi/detail-ppi"),
    getIdPpiActiveBindingParamPpiWetmillN: createApiFunction("post", "pqr/pqr_binding_param_ppi/wetmill_n"),
    getIdPpiActiveBindingParamPpiWetmillY: createApiFunction("post", "pqr/pqr_binding_param_ppi/wetmill_y"),
    createBindingParamPpi: createApiFunction("post", "pqr/pqr_binding_param_ppi/create"),
    BatchcreateBindingParamPpi: createApiFunction("post", "pqr/pqr_binding_param_ppi/create-batch"),
    copyAllWetmillParamPpi: createApiFunction("post", "pqr/pqr_binding_param_ppi/create-copy"),
    editBindingParamPpi: createApiFunction("put", "pqr/pqr_binding_param_ppi/edit"),
    editStatusBindingParamPpi: createApiFunction("put", "pqr/pqr_binding_param_ppi/edit-status"),
  };
}
