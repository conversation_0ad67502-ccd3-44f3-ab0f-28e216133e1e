import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_REAGEN}/v2/ipc/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function Api_IPC_Scale_DASHBOARD() {
  return {
    getIpcScaleReportExcel: createApiFunction("post", "ipc_scale/dashboard/get-report-excel"),
    getIpcScaleDashboard: createApiFunction("get", "ipc_scale/dashboard/get-report-dashboard"),
    getIpcScaleReason: createApiFunction("get", "ipc_scale/dashboard/get-report-reason"),
  };
}
