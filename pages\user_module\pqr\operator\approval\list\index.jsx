import { useEffect, useState } from "react";
import Head from "next/head";
import { <PERSON>readcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, Loader, RadioGroup, Radio, SelectPicker, Whisper, Tooltip } from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiMSCWeight from "@/pages/api/pqr/weight_msc/api_weight_msc";



export default function NeedApprovalWeightMSCList() {
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState("id_trans_header");
    const [sortType, setSortType] = useState("asc");
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const router = useRouter();
    const [loading, setLoading] = useState(false);

    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [MSCDataState, setMSCDataState] = useState([]);


    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = MSCDataState.filter((rowData, i) => {
        const searchFields = ["id_weight_msc", "id_trans_header", "batch_code", "remarks", "approve_status", "create_date", "create_by", "update_date", "update_by", "delete_date", "delete_by", "approval_by", "approval_date", "is_active"];

        const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

        return matchesSearch;
    });

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    };

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : MSCDataState.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setSessionAuth(dataLogin);
        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("pqr/operator"));

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            getNeedApproveMSCData();

        }
    }, []);



    const getNeedApproveMSCData = async () => {
        try {
            const res = await ApiMSCWeight().getNeedApproveMSC();

            console.log("res", res);
            if (res.status === 200) {
                setMSCDataState(res.data);
            } else {
                console.log("error on GetAllApi ", res.message);
            }
        } catch (error) {
            console.log("error on catch GetAllApi", error);
        }
    };


    return (
        <div>
            <div>
                <Head>
                    <title>List Persetujuan Berat MSC</title>
                </Head>
            </div>
            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>E Release</Breadcrumb.Item>
                                    <Breadcrumb.Item>Operator</Breadcrumb.Item>
                                    <Breadcrumb.Item>Approval</Breadcrumb.Item>
                                    <Breadcrumb.Item active>List Approval</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Persetujuan Berat MSC</h5>
                            </Stack>
                        }
                    ></Panel>
                    <div>
                        <Panel
                            bordered
                            bodyFill
                            header={
                                <Stack justifyContent="space-between">
                                    <InputGroup inside>
                                        <InputGroup.Addon>
                                            <SearchIcon />
                                        </InputGroup.Addon>
                                        <Input placeholder="cari" value={searchKeyword} onChange={handleSearch} />
                                        <InputGroup.Addon
                                            onClick={() => {
                                                setSearchKeyword("");
                                                setPage(1);
                                            }}
                                            style={{
                                                display: searchKeyword ? "block" : "none",
                                                color: "red",
                                                cursor: "pointer",
                                            }}
                                        >
                                            <CloseOutlineIcon />
                                        </InputGroup.Addon>
                                    </InputGroup>
                                </Stack>
                            }
                        >
                            <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn} autoHeight>
                                <Column width={70} align="center" sortable fullText resizable>
                                    <HeaderCell>ID Berat MSC</HeaderCell>
                                    <Cell dataKey="id_weight_msc" />
                                </Column>
                                <Column width={70} align="center" sortable fullText resizable>
                                    <HeaderCell>ID Transaksi Header</HeaderCell>
                                    <Cell dataKey="id_trans_header" />
                                </Column>

                                <Column width={180} sortable fullText resizable>
                                    <HeaderCell align="center">Kode Batch</HeaderCell>
                                    <Cell dataKey="batch_code" />
                                </Column>

                                <Column width={250} sortable fullText resizable>
                                    <HeaderCell align="center">Catatan</HeaderCell>
                                    <Cell dataKey="remarks" />
                                </Column>

                                <Column width={100} align="center" sortable fullText resizable>
                                    <HeaderCell>Status Persetujuan</HeaderCell>
                                    <Cell>
                                        {(rowData) => {
                                            let statusText = "";
                                            if (rowData.approve_status === 2) {
                                                statusText = "Ongoing";
                                            } else if (rowData.approve_status === 1) {
                                                statusText = "Done";
                                            } else if (rowData.approve_status === 0) {
                                                statusText = "Dropped";
                                            }
                                            return <>{statusText}</>;
                                        }}
                                    </Cell>
                                </Column>
                                <Column width={175} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Dibuat</HeaderCell>
                                    <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                                </Column>
                                <Column width={175} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Diperbarui</HeaderCell>
                                    <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                                </Column>
                                <Column width={175} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Dihapus</HeaderCell>
                                    <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.delete_by}</>}</Cell>
                                </Column>
                                <Column width={200} sortable fullText resizable>
                                    <HeaderCell align="center">Disetujui Oleh</HeaderCell>
                                    <Cell dataKey="approve_by">
                                        {(rowData) => {
                                            return rowData.approval_by || "-";
                                        }}
                                    </Cell>
                                </Column>
                                <Column width={200} sortable fullText resizable>
                                    <HeaderCell align="center">Tanggal Disetujui</HeaderCell>
                                    <Cell>{(rowData) => (rowData.approve_date ? new Date(rowData.approval_date).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={120} sortable resizable align="center" fullText>
                                    <HeaderCell>Status</HeaderCell>
                                    <Cell>
                                        {(rowData) => (
                                            <span
                                                style={{
                                                    color: rowData.is_active === 1 ? "green" : "red",
                                                }}
                                            >
                                                {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                                            </span>
                                        )}
                                    </Cell>
                                </Column>
                                <Column width={100} fixed="right" align="center">
                                    <HeaderCell>Aksi</HeaderCell>
                                    <Cell style={{ padding: "8px" }}>
                                        {(rowData) => (
                                            <div>
                                                <Button
                                                    appearance="subtle"
                                                    onClick={() => {
                                                        const idHeader = rowData.id_trans_header;
                                                        router.push(`/user_module/pqr/operator/list/pqr?IdHeader=${idHeader}`);
                                                    }}
                                                >
                                                    <Whisper placement="top" trigger="hover" speaker={<Tooltip>Lihat Detail Transaksi Header</Tooltip>}>
                                                        <SearchIcon style={{ fontSize: "16px" }} />
                                                    </Whisper>
                                                </Button>


                                            </div>
                                        )}
                                    </Cell>
                                </Column>


                            </Table>

                            <div style={{ padding: 20 }}>
                                <Pagination
                                    prev
                                    next
                                    first
                                    last
                                    ellipsis
                                    boundaryLinks
                                    maxButtons={5}
                                    size="xs"
                                    layout={["total", "-", "limit", "|", "pager", "skip"]}
                                    limitOptions={[10, 30, 50]}
                                    total={totalRowCount}
                                    limit={limit}
                                    activePage={page}
                                    onChangePage={setPage}
                                    onChangeLimit={handleChangeLimit}
                                />
                            </div>
                        </Panel>

                    </div>
                </div>
            </ContainerLayout>
        </div>
    );
}
