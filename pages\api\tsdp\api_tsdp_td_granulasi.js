import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](
    `${process.env.NEXT_PUBLIC_REAGEN}/ts/${url}`,
    data
  )
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiTsdpTDGranulasi() {
  return {
    getAllTsTransactionGranulasi: createApiFunction(
      "get",
      "transaction-d-granulasi/list"
    ),
    getTsTransactionGranulasiById: createApiFunction(
      "post",
      "transaction-d-granulasi/id"
    ),
    postTsTransactionGranulasi: createApiFunction(
      "post",
      "transaction-d-granulasi/create"
    ),
    postTsTransactionGranulasiAuto: createApiFunction(
      "post",
      "transaction-d-granulasi/create-auto"
    ),
    UpdateTsTransactionGranulasi: createApiFunction(
      "put",
      "transaction-d-granulasi/edit"
    ),
    UpdateStatusTsTransactionGranulasi: createApiFunction(
      "put",
      "transaction-d-granulasi/edit-status"
    ),
  };
}
