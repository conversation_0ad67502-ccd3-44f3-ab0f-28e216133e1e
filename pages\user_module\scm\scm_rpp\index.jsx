import ContainerLayout from '@/components/layout/ContainerLayout'
import Head from 'next/head'
import React, { useEffect, useState } from 'react'
import { Breadcrumb, Button, Form, IconButton, Input, InputGroup, Modal, Pagination, Panel, SelectPicker, Stack, Table, Tag, toaster, Uploader } from 'rsuite'
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import EditIcon from '@rsuite/icons/Edit';
import API_ScmDashboard from "@/pages/api/scm/api_scm_dashboard";
import Messages from '@/components/Messages';
import FileUploadIcon from '@rsuite/icons/FileUpload';
import * as XLSX from "xlsx";
import ScmDashboardRPPApi from '@/pages/api/scm/api_scm_rpp';
import { useRouter } from 'next/router';

export default function InputDashboardPage() {

    const [props, setProps] = useState([]);
    const [moduleName, setModuleName] = useState("");
    const { HeaderCell, Cell, Column } = Table;
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);

    const router = useRouter()

    const [dashboardDropdown, setDashboardDropdown] = useState([{
        "table_name": "scm_db_of1_dashboard_target_rpp",
        "module_name": "OF1 RPP Target",
    }]);
    const [selectedDashboard, setSelectedDashboard] = useState("scm_db_of1_dashboard_target_rpp");
    const [selectDashboardError, setSelectDashboardError] = useState('');
    const [columnNames, setColumnNames] = useState([]);

    const [fileList, setFileList] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [readError, setReadError] = useState('');

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("scm/scm_rpp")
            );
            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            // handleGetAllActiveDashboardApi();
        }
    }, []);

    const handleGetAllActiveDashboardApi = async () => {
        const res = await API_ScmDashboard().getAllActive();

        if (res.status === 200) {
            setDashboardDropdown(res.data ? res.data : []);
        }
    }

    const handleGetColumnNamesApi = async () => {
        const res = await API_ScmDashboard().getAllColumn({
            table_name: selectedDashboard
        });

        if (res.status === 200) {
            setColumnNames(res.data ? res.data : []);
        }
    }

    useEffect(() => {
        setColumnNames([]);
        handleGetColumnNamesApi();
    }, [selectedDashboard]);

    const handleDownloadTemplate = () => {

        if (!selectedDashboard) {
            setSelectDashboardError('Please select dashboard first');
            return;
        }

        const aoaColumn = [columnNames]

        const workSheet = XLSX.utils.aoa_to_sheet(aoaColumn);

        const workBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workBook, workSheet, "Data");

        const formattedFileName = selectedDashboard.toLowerCase().replace(" ", "_");
        const fileName = `${formattedFileName}_dashboard_template.xlsx`;
        XLSX.writeFile(workBook, fileName);
    }

    const handleUploadDashboardApi = async () => {
        try {
            const res = await ScmDashboardRPPApi().uploadDashboardRpp({
                data: tableData,
                created_by: props.employee_id
            });

            if (res.status === 200) {
                toaster.push(Messages("success", res.message), {
                    placement: "topCenter",
                    duration: 5000,
                });

                setFileList([]);
                setTableData([]);
            } else {
                toaster.push(Messages("error", res.message), {
                    placement: "topCenter",
                    duration: 5000,
                });
            }
        } catch (error) {
            console.log("error : ", error);
            toaster.push(Messages("error", `Error: something error, please try again later!`), {
                placement: "topCenter",
                duration: 5000,
            });
        }
    }

    const handleFileChange = () => {
        if (!fileList.length) return;
        const file = fileList[0].blobFile;
        setReadError('');
        setTableData([]);

        const reader = new FileReader();

        reader.onload = (e) => {
            const data = e.target.result;
            let workbook = null;
            try {
                workbook = XLSX.read(data, { type: "binary" });
            } catch (error) {
                console.error(error);
                setReadError('Failed to read file, please ensure the file is in .xlsx format');
                return;
            }

            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const json = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            if (!json.length) {
                setReadError('File is empty');
                return;
            }

            const headerRow = json[0];
            if (headerRow.length !== columnNames.length || !headerRow.every((header, index) => header === columnNames[index])) {
                setReadError(`Dashboard do not match the template format: ${columnNames.join(", ")}`);
                return;
            }

            const dataRows = json.slice(1);
            if (dataRows.length < 1 || !dataRows.some((row) => row.some((cell) => cell))) {
                setReadError('Dashboard data cannot be empty');
                return;
            }

            const formattedData = dataRows.map((row) => {
                const record = {};
                headerRow.forEach((header, index) => {
                    if (header === "amount") {
                        record[header] = row[index] !== undefined && row[index] !== null ? parseFloat(row[index]) || 0 : 0;
                    } else {
                        record[header] = row[index] !== undefined && row[index] !== null ? String(row[index]) : "";
                    }
                });
                return record;
            });
            setTableData(formattedData);
        };

        reader.readAsArrayBuffer(file);
    };

    useEffect(() => {
        handleFileChange();
    }, [fileList]);

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    }

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return tableData.slice(start, end);
    }

    const totalRowCount = tableData.length;

    return (
        <>
            <div>
                <Head>
                    <title>RPP Dashboard</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className='m-4 pt-2'>
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Quality</Breadcrumb.Item>
                                    <Breadcrumb.Item>SCM</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Input Dashboard</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                    >
                        <div className='m-4'>
                            <Form className='mb-3'>
                                <Form.Group>
                                    <Form.ControlLabel>Module Name</Form.ControlLabel>
                                    <Form.Control
                                        name='module_name'
                                        placeholder={"Select Module Name"}
                                        accepter={SelectPicker}
                                        data={dashboardDropdown}
                                        value={selectedDashboard}
                                        valueKey='table_name'
                                        labelKey='module_name'
                                        disabled
                                        block
                                        onChange={(value) => {
                                            setSelectedDashboard(value);
                                            setSelectDashboardError('');
                                        }}
                                        style={{ minWidth: "150%" }}
                                        errorMessage={selectDashboardError}
                                    />
                                </Form.Group>
                            </Form>

                            <Uploader
                                draggable
                                autoUpload={false}
                                disabled={!selectedDashboard}
                                fileList={fileList}
                                onChange={(newFileList) => setFileList(newFileList.slice(-1))}
                                accept='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            >
                                <div style={{ height: 200, display: "flex", justifyContent: "center", alignItems: "center" }}>
                                    <div style={{ display: "flex", flexDirection: "column", alignItems: "center", rowGap: "10px" }}>
                                        <FileUploadIcon className='text-3xl' />
                                        <span>Click or Drag files to this area to upload</span>
                                    </div>
                                </div>
                            </Uploader>

                            {readError && <p style={{ color: 'red', marginTop: '10px' }}>{readError}</p>}

                            {
                                tableData.length > 0 && (
                                    <>
                                        <Table bordered cellBordered height={300} data={getPaginatedData(tableData, limit, page)} style={{ marginTop: 10 }}>
                                            <Column width={70} align="center" resizable>
                                                <HeaderCell>No</HeaderCell>
                                                <Cell>{(rowData, index) => index + 1 + (page - 1) * limit}</Cell>
                                            </Column>
                                            {Object.keys(tableData[0]).map((key) => (
                                                <Column key={key} width={200} align="center" resizable>
                                                    <HeaderCell>{key}</HeaderCell>
                                                    <Cell dataKey={key} />
                                                </Column>
                                            ))}
                                        </Table>
                                        <div style={{ padding: 20 }}>
                                            <Pagination
                                                prev
                                                next
                                                first
                                                last
                                                ellipsis
                                                boundaryLinks
                                                maxButtons={5}
                                                size="xs"
                                                layout={["total", "-", "limit", "|", "pager", "skip"]}
                                                limitOptions={[10, 30, 50]}
                                                total={totalRowCount}
                                                limit={limit}
                                                activePage={page}
                                                onChangePage={setPage}
                                                onChangeLimit={handleChangeLimit}
                                            />
                                        </div>
                                    </>
                                )
                            }


                            <div
                                style={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    marginTop: 20,
                                }}
                            >
                                <Button
                                    appearance='ghost'
                                    onClick={handleDownloadTemplate}
                                >
                                    Download Template
                                </Button>
                                <Button
                                    appearance='primary'
                                    disabled={!fileList.length}
                                    onClick={handleUploadDashboardApi}
                                >
                                    Upload
                                </Button>
                            </div>
                        </div>
                    </Panel>
                </div>
            </ContainerLayout>
        </>
    )
}
