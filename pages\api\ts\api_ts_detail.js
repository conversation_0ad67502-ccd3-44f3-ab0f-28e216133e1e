import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  try {
    // /mobile-api/ts-dashboard/get-dashboard1
    const response = await axios[method](
      `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/${url}`,
      data
    )
      .then((res) => {
        return res.data;
      })
      .catch((err) => {
        return err.response.data;
      });
    return response;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default function ApiTsDetail() {
  return {
    // Automate Line
    getDetailAuto: createApiFunction("post", "ts-dashboard/auto/detail"),
    putDetailAuto: createApiFunction("put", "ts-header-trans/update"),
    postAttachmentAuto: createApiFunction("post", "ts-header-trans/upload"),
    postDetailAuto: createApiFunction("post", "ts-detail-trans/add"),

    // Manual Line
    getDetailManual: createApiFunction("post", "ts-dashboard/manual/detail"),
    putDetailManual: createApiFunction("put", "ts-header-trans1/update"),
    postAttachmentManual: createApiFunction("post", "ts-header-trans1/upload"),

    getAllActiveSpv: createApiFunction("get", "ts_spv/all/active"),
    getAllOfProduct: createApiFunction("get", "of1-master-bs/get-all"),

    getAllDetail: createApiFunction("get", "ts-api/get-all"),
    getApiData: createApiFunction("post", "ts-api/get-data"),

    postHeaderDetailAttachmentsManual: createApiFunction(
      "post",
      "ts-header-trans1/add-header-detail-attacthments"
    ),
  };
}
