import { useEffect, useState } from "react";
import styles from "./LoginForm.module.css";
import { useRouter } from "next/router";
// import { signIn, useSession } from "next-auth/react";
import imageStyle from "../styles/Images.module.css";

import UseTestApi from "@/pages/api/userApi";
import {
  hideLoadingAnimation,
  loadingAnimationNoClick,
} from "./SweetAlertLoading";
import withReactContent from "sweetalert2-react-content";
import Swal from "sweetalert2";
import { aicrypt } from "@/lib/aicrypt/aiCrypt";

export default function LoginPage({ setupPageIndex }) {
  const router = useRouter();
  // const session = useSession();
  const [isFormDisabled, setIsFormDisabled] = useState(false);
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const [employeeId, setEmployeeId] = useState("");
  const [password, setPassword] = useState("");
  const [isInputInvalid, setIsInputInvalid] = useState(false);
  const [isFirstLogin, setIsFirstLogin] = useState(false);
  const MySwal = withReactContent(Swal);

  const { UserLoginApi } = UseTestApi();

  // Email handler
  const employeeIdHandler = (event) => {
    setEmployeeId(event.target.value);
  };

  // Password handler
  const passwordHandler = (event) => {
    setPassword(event.target.value);
  };

  // Sign in handler
  const signinHandler = async (event) => {
    loadingAnimationNoClick();
    event.preventDefault();

    setIsFormDisabled(true);

    const reqData = {
      employee_id: employeeId,
      password: password,
    };

    const loginData = await UserLoginApi(reqData);
    const { Data, Token, FirstLogin } = loginData;
    const { error } = loginData;
    if (Data) {
      setIsFormDisabled(true);
      const dataLoginUser = {
        employee_id: Data[0].Employee_Id,
        employee_name: Data[0].Name,
        email: Data[0].Email,
        menu_link_code: Data[0].Menu_Link_Code,
        module_code: Data[0].Module_Code,
        department: Data[0].Id_Department,
        token: Token,
      };
      if (!FirstLogin) {
        Date.prototype.addDate = function (h) {
          this.setDate(this.getDate() + h);
          this.setHours(0);
          this.setMinutes(0);
          this.setSeconds(0);
          return this;
        };
        var getDate = new Date().addDate(1);

        const loginSession ={
          expire: getDate
        }
  
        const encyrpted =  await aicrypt( JSON.stringify(loginSession));
        localStorage.setItem("session", encyrpted);
        localStorage.setItem("dataLoginUser", JSON.stringify(dataLoginUser));
        MySwal.fire({
          allowOutsideClick: false,
          position: "center",
          icon: "success",
          title: "Login Success !",
          text: "Wait a second, you're being redirected...",
          showConfirmButton: false,
          timer: 2500,
        });
        router.push("/dashboard");
        return;
      }else{
        MySwal.fire({
          allowOutsideClick: false,
          position: "center",
          icon: "success",
          title: "Login Success",
          text: "Wait a second, you're being redirected to change your password...",
          showConfirmButton: false,
          timer: 2500,
        });
        router.push(`/user_profile/changePassword?data=${dataLoginUser.employee_id}`);
      }
      
    }

    if (error) {
      MySwal.fire({
        icon: "error",
        title: "Login Failed !",
        text: error,
      });
      setIsFormDisabled(false);
      return;
    }
    // if (!loginData) {
    //   setIsFormDisabled(false);
    //   // setIsInputInvalid(true);
    //   MySwal.fire({
    //     icon: "error",
    //     title: "Login Failed !",
    //     text: "Check again your Employee ID and Password.",
    //   });
    // } else {
    //   const { Data } = loginData;
    //   const dataLoginUser = {
    //     employee_id: Data[0].Employee_Id,
    //     employee_name: Data[0].Name,
    //   };
    //   localStorage.setItem("dataLoginUser", JSON.stringify(dataLoginUser));
    //   MySwal.fire({
    //     allowOutsideClick: false,
    //     position: "center",
    //     icon: "success",
    //     title: "Login Success !",
    //     text: "Wait a second, you're being redirected...",
    //     showConfirmButton: false,
    //     timer: 2500,
    //   });
    //   router.push("/dashboard");
    // }
  };

  return (
    <div className="container-fluid d-flex align-items-center justify-content-center h-100 mb-5">
      <div className={`card mt-5 p-5 shadow-lg ${styles.customCardWidth}`}>
        <div className="text-center card-title">
          <div className="mb-5">
            <img src="/Logo_kalbe_detail.png" className={imageStyle.image_header} />
          </div>
          <h3>Login</h3>
        </div>
        {isInputInvalid && (
          <div className="bg-danger-subtle text-center p-3 text-danger rounded">
            Password Salah !
          </div>
        )}
        <form className="px-5" onSubmit={signinHandler}>
          <div className="mb-3 has-validation">
            <label
              htmlFor="exa`mpleInputEmail1"
              className="form-label"
              style={{ fontSize: "0.8em" }}
            >
              Employee ID
            </label>
            <input
              type="text"
              className="form-control"
              id="exampleInputEmail1"
              aria-describedby="emailHelp"
              value={employeeId}
              onChange={employeeIdHandler}
              autoComplete="off"
              autoFocus={true}
              disabled={isFormDisabled}
              required
            />
            <div className="invalid-feedback">Error message</div>
          </div>

          <div className="mb-3">
            <label
              htmlFor="exampleInputPassword1"
              className="form-label"
              style={{ fontSize: "0.8em" }}
            >
              Password
            </label>
            <input
              type="password"
              className="form-control"
              id="exampleInputPassword1"
              value={password}
              onChange={passwordHandler}
              disabled={isFormDisabled}
              required
            />
          </div>

          <button
            type="submit"
            disabled={isFormDisabled}
            className="btn btn-primary"
          >
            Sign in
          </button>
        </form>
        <div className="px-5 pt-2">
          <button
            className="btn btn-link"
            onClick={() => router.push("/ForgotPassword")}
          >
            Forgot password ?
          </button>
        </div>
      </div>
    </div>
  );
}
