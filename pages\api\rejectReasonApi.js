import axios from 'axios';

export default function RejectReasonApi() {
  const GetAllRejectReason = async () => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/rejectReason/getAll`)
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetAllRejectReasonActive = async () => {
    let data = [];

    await axios
      .post(`${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/rejectReason/getAllActive`)
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const GetRejectReasonById = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/rejectReason/getById`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const InsertRejectReason = async (inputData) => {
    let data = [];

    await axios
      .post(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/rejectReason/postRejectReason`,
        inputData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  const UpdateRejectReason = async (updateData) => {
    let data = [];

    await axios
      .put(
        `${process.env.NEXT_PUBLIC_PIMS_SERVICE_2}/rejectReason/putRejectReason`,
        updateData,
      )
      .then((res) => (data = res.data))
      .catch((err) => {
        data = err.response.data;
        console.log(err);
      });

    return data;
  };

  return {
    GetAllRejectReason,
    GetAllRejectReasonActive,
    GetRejectReasonById,
    InsertRejectReason,
    UpdateRejectReason,
  };
}
