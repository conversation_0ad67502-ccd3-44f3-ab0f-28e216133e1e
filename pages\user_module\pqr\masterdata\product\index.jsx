import { useEffect, useState } from "react";
import Head from "next/head";
import {
    Breadcrumb,
    IconButton,
    Input,
    InputGroup,
    Pagination,
    Panel,
    Stack,
    Table,
    Tag,
    Button,
    Modal,
    Form,
    useToaster,
    Notification,
    ButtonGroup,
    Loader,
    DatePicker,
    RadioGroup,
    Radio,
    InputNumber
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiStep from "@/pages/api/pqr/step/api_masterdata_step";
import ApiProduct from "@/pages/api/pqr/product/api_masterdata_product";
import { useRouter } from "next/router";

export default function MasterdataProduct() {
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState("id_product");
    const [sortType, setSortType] = useState("asc");
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const toaster = useToaster();
    const router = useRouter();
    const [loading, setLoading] = useState(false);

    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [productDataState, setProductDataState] = useState([])

    const emptyAddProductForm = {
        product_code: null,
        bobot_min: null,
        bobot_max: null,
        bobot_std: null,
        bobot_core_foil: null,
        is_active: 1,
    };

    const emptyEditProductForm = {
        product_code: null,
        bobot_min: null,
        bobot_max: null,
        bobot_std: null,
        bobot_core_foil: null,
        is_active: 1,
    };

    const [showAddModal, setShowAddModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [addProductForm, setAddProductForm] = useState(emptyAddProductForm);
    const [editProductForm, setEditProductForm] = useState(emptyEditProductForm);
    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [errorsEditForm, setErrorsEditForm] = useState({});

    const showNotification = (type, message, duration = 2000) => {
        toaster.push(
            <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
                <p>{message}</p>
            </Notification>,
            { placement: "topEnd", duration }
        );
    };

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = productDataState.filter((rowData) => {
        const searchFields = ["id_product", "product_code", "bobot_min", "bobot_max", "bobot_std", "bobot_core_foil", "is_active"];
        const matchesSearch = searchFields.some((field) =>
            rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase())
        );
        return matchesSearch;
    });

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    };

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : productDataState.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setSessionAuth(dataLogin);

        if (!dataLogin) {
            router.push("/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("pqr/")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }

            HandleGetAllProductApi();
        }
    }, []);

    const HandleGetAllProductApi = async () => {
        try {
            const res = await ApiProduct().getAllProduct();
            if (res.status === 200) {
                setProductDataState(res.data);
            } else {
                console.log("Error on GetAllProductApi: ", res.message);
            }
        } catch (error) {
            console.log("Error on catch GetAllProductApi: ", error.message);
        }
    };

    const HandleAddProductApi = async () => {
        const errors = {};
        if (!addProductForm.product_code) {
            errors.product_code = "Kode Produk Wajib Diisi!";
        }
        if (!addProductForm.bobot_min) {
            errors.bobot_min = "Bobot Min Wajib Diisi!";
        }
        if (!addProductForm.bobot_max) {
            errors.bobot_max = "Bobot Max Wajib Diisi!";
        }
        if (!addProductForm.bobot_std) {
            errors.bobot_std = "Bobot STD Wajib Diisi!";
        }
        if (!addProductForm.bobot_core_foil) {
            errors.bobot_core_foil = "Bobot Core Foil Wajib Diisi!";
        }


        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }

        setLoading(true);
        try {
            const res = await ApiProduct().createProduct({
                ...addProductForm,
                create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
            });

            if (res.status === 200) {
                setAddProductForm(emptyAddProductForm);
                setShowAddModal(false);
                HandleGetAllProductApi();
                showNotification("success", "Kode Produk Berhasil Ditambahkan");
            } else {
                console.log("Error on AddProductApi: ", res.message);
                showNotification("error", "Kode Produk Gagal Ditambahkan");
            }
        } catch (error) {
            console.log("Error on AddProductApi: ", error.message);
            showNotification("error", "gagal menambah data");
        } finally {
            setLoading(false);

        }
    };

    const HandleEditProductApi = async () => {
        const errors = {};
        if (!editProductForm.product_code) {
            errors.product_code = "Kode Produk Wajib Diisi!";
        }
        if (!editProductForm.bobot_min) {
            errors.bobot_min = "Bobot Min Wajib Diisi!";
        }
        if (!editProductForm.bobot_max) {
            errors.bobot_max = "Bobot Max Wajib Diisi!";
        }
        if (!editProductForm.bobot_std) {
            errors.bobot_std = "Bobot STD Wajib Diisi!";
        }
        if (!editProductForm.bobot_core_foil) {
            errors.bobot_core_foil = "Bobot Core Foil Wajib Diisi!";
        }

        if (Object.keys(errors).length > 0) {
            setErrorsEditForm(errors);
            return;
        }
        setLoading(true);

        try {
            const res = await ApiProduct().editProduct({
                ...editProductForm,
                update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
            });

            if (res.status === 200) {
                HandleGetAllProductApi();
                showNotification("success", "Kode Produk Berhasil Diubah");
                setShowEditModal(false);
            } else {
                console.log("Error on EditProductApi: ", res.message);
                showNotification("error", "gagal mengubah data");
            }
        } catch (error) {
            console.log("Error on EditProductApi: ", error.message);
            showNotification("error", "gagal mengubah data");
        }
        finally {
            setLoading(false);
        }
    };

    const handleEditStatusProductApi = async (id_product, is_active) => {
        try {
            const res = await ApiProduct().editStatusProduct({
                id_product,
                is_active,
                delete_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
            });

            if (res.status === 200) {
                HandleGetAllProductApi();
                showNotification("success", "Status Berhasil Diubah");
            } else {
                console.log("Error on update status: ", res.message);
                showNotification("error", "gagal mengubah status");
            }
        } catch (error) {
            console.log("Error on update status: ", error.message);
            showNotification("error", "gagal mengubah status");
        }
    };

    return (
        <div>
            <div>
                <Head>
                    <title>Master Data Produk</title>
                </Head>
            </div>
            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>E Release</Breadcrumb.Item>
                                    <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Halaman Produk</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Halaman Produk</h5>
                            </Stack>
                        }
                    ></Panel>
                    <div>
                        <Panel
                            bordered
                            bodyFill
                            header={
                                <Stack justifyContent="space-between">
                                    <div className="flex gap-2">
                                        <IconButton
                                            icon={<PlusRoundIcon />}
                                            appearance="primary"
                                            onClick={() => {
                                                setShowAddModal(true);
                                            }}
                                        >
                                            Tambah
                                        </IconButton>
                                    </div>
                                    <InputGroup inside>
                                        <InputGroup.Addon>
                                            <SearchIcon />
                                        </InputGroup.Addon>
                                        <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                                        <InputGroup.Addon
                                            onClick={() => {
                                                setSearchKeyword("");
                                                setPage(1);
                                            }}
                                            style={{
                                                display: searchKeyword ? "block" : "none",
                                                color: "red",
                                                cursor: "pointer",
                                            }}
                                        >
                                            <CloseOutlineIcon />
                                        </InputGroup.Addon>
                                    </InputGroup>
                                </Stack>
                            }
                        >

                            <Table
                                bordered
                                cellBordered
                                height={400}
                                data={getPaginatedData(getFilteredData(), limit, page)}
                                sortColumn={sortColumn}
                                sortType={sortType}
                                onSortColumn={handleSortColumn}
                            >
                                <Column width={120} align="center" sortable fullText resizable>
                                    <HeaderCell>ID Product</HeaderCell>
                                    <Cell dataKey="id_product" />
                                </Column>
                                <Column width={150} sortable fullText resizable>
                                    <HeaderCell align="center">Kode Produk</HeaderCell>
                                    <Cell dataKey="product_code" />
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Bobot Min</HeaderCell>
                                    <Cell dataKey="bobot_min" />
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Bobot Max</HeaderCell>
                                    <Cell dataKey="bobot_max" />
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Bobot STD</HeaderCell>
                                    <Cell dataKey="bobot_std" />
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Bobot Core Foil</HeaderCell>
                                    <Cell dataKey="bobot_core_foil" />
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Pembuatan</HeaderCell>
                                    <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dibuat oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Diperbarui</HeaderCell>
                                    <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                                </Column>
                                <Column width={150} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Dihapus</HeaderCell>
                                    <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.delete_by}</>}</Cell>
                                </Column>
                                <Column width={100} sortable resizable align="center" fullText>
                                    <HeaderCell>Status</HeaderCell>
                                    <Cell>
                                        {(rowData) => (
                                            <span
                                                style={{
                                                    color: rowData.is_active === 1 ? "green" : "red",
                                                }}
                                            >
                                                {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                                            </span>
                                        )}
                                    </Cell>
                                </Column>
                                <Column width={120} fixed="right" align="center">
                                    <HeaderCell>Aksi</HeaderCell>
                                    <Cell style={{ padding: "8px" }}>
                                        {(rowData) => (
                                            <div>
                                                <Button
                                                    appearance="subtle"
                                                    disabled={rowData.is_active === 0}
                                                    onClick={() => {
                                                        setShowEditModal(true);
                                                        setEditProductForm({
                                                            ...editProductForm,
                                                            product_code: rowData.product_code,
                                                            bobot_min: rowData.bobot_min,
                                                            bobot_max: rowData.bobot_max,
                                                            bobot_std: rowData.bobot_std,
                                                            bobot_core_foil: rowData.bobot_core_foil,
                                                            id_product: rowData.id_product,
                                                            update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                                                        });
                                                    }}
                                                >
                                                    <EditIcon />
                                                </Button>
                                                <Button appearance="subtle" onClick={() => handleEditStatusProductApi(rowData.id_product, rowData.is_active)}>
                                                    {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                                                </Button>
                                            </div>
                                        )}
                                    </Cell>
                                </Column>
                            </Table>

                            <div style={{ padding: 20 }}>
                                <Pagination
                                    prev
                                    next
                                    first
                                    last
                                    ellipsis
                                    boundaryLinks
                                    maxButtons={5}
                                    size="xs"
                                    layout={["total", "-", "limit", "|", "pager", "skip"]}
                                    limitOptions={[10, 30, 50]}
                                    total={totalRowCount}
                                    limit={limit}
                                    activePage={page}
                                    onChangePage={setPage}
                                    onChangeLimit={handleChangeLimit}
                                />
                            </div>
                        </Panel>

                        <Modal
                            backdrop="static"
                            open={showAddModal}
                            onClose={() => {
                                setShowAddModal(false);
                                setAddProductForm(emptyAddProductForm);
                                setErrorsAddForm({});
                            }}
                            overflow={false}
                        >
                            <Modal.Header>
                                <Modal.Title>Tambah Kode Produk</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <Form fluid>
                                    <Form.Group>
                                        <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                                        <Form.Control
                                            name="product_code"
                                            value={addProductForm.product_code}
                                            onChange={(value) => {
                                                setAddProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    product_code: value,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    product_code: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsAddForm.product_code && <p style={{ color: "red" }}>{errorsAddForm.product_code}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Minimal</Form.ControlLabel>
                                        <Form.Control
                                            name="bobot_min"
                                            value={addProductForm.bobot_min}
                                            onChange={(value) => {
                                                setAddProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_min: parseFloat(value) || 0,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_min: undefined,
                                                }));
                                            }}
                                            accepter={InputNumber}
                                            step={0.01}
                                            precision={2}
                                        />
                                        {errorsAddForm.bobot_min && <p style={{ color: "red" }}>{errorsAddForm.bobot_min}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Maximal</Form.ControlLabel>
                                        <Form.Control
                                            name="bobot_max"
                                            value={addProductForm.bobot_max}
                                            onChange={(value) => {
                                                setAddProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_max: parseFloat(value) || 0,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_max: undefined,
                                                }));
                                            }}
                                            accepter={InputNumber}
                                            step={0.01}
                                            precision={2}
                                        />
                                        {errorsAddForm.bobot_max && <p style={{ color: "red" }}>{errorsAddForm.bobot_max}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot STD</Form.ControlLabel>
                                        <Form.Control
                                            name="bobot_std"
                                            value={addProductForm.bobot_std}
                                            onChange={(value) => {
                                                setAddProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_std: parseFloat(value) || 0,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_std: undefined,
                                                }));
                                            }}
                                            accepter={InputNumber}
                                            step={0.01}
                                            precision={2}
                                        />
                                        {errorsAddForm.bobot_std && <p style={{ color: "red" }}>{errorsAddForm.bobot_std}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Core Foil</Form.ControlLabel>
                                        <Form.Control
                                            name="bobot_core_foil"
                                            value={addProductForm.bobot_core_foil}
                                            onChange={(value) => {
                                                setAddProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_core_foil: parseFloat(value) || 0,
                                                }));
                                                setErrorsAddForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_core_foil: undefined,
                                                }));
                                            }}
                                            accepter={InputNumber}
                                            step={0.01}
                                            precision={2}
                                        />
                                        {errorsAddForm.bobot_core_foil && <p style={{ color: "red" }}>{errorsAddForm.bobot_core_foil}</p>}
                                    </Form.Group>
                                </Form>
                            </Modal.Body>
                            <Modal.Footer>
                                <Button
                                    onClick={() => {
                                        setShowAddModal(false);
                                        setAddProductForm(emptyAddProductForm);
                                        setErrorsAddForm({});
                                    }}
                                    appearance="subtle"
                                >
                                    Batal
                                </Button>
                                <Button
                                    onClick={() => {
                                        HandleAddProductApi();
                                    }}
                                    appearance="primary"
                                    type="submit"
                                    loading={loading}
                                    disabled={loading}
                                >
                                    Tambah
                                </Button>
                                {loading && <Loader backdrop size="md" vertical content="Adding Data..." active={loading} />}
                            </Modal.Footer>
                        </Modal>
                        <Modal
                            backdrop="static"
                            open={showEditModal}
                            onClose={() => {
                                setShowEditModal(false);
                                setEditProductForm(emptyEditProductForm);
                                setErrorsEditForm({});
                            }}
                            overflow={false}
                        >
                            <Modal.Header>
                                <Modal.Title>Ubah Kode Produk</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <Form fluid>
                                    <Form.Group>
                                        <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                                        <Form.Control
                                            name="product_code"
                                            value={editProductForm.product_code}
                                            onChange={(value) => {
                                                setEditProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    product_code: value,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    product_code: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsEditForm.product_code && <p style={{ color: "red" }}>{errorsEditForm.product_code}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Minimal</Form.ControlLabel>
                                        <Form.Control
                                            name="bobot_min"
                                            value={editProductForm.bobot_min}
                                            onChange={(value) => {
                                                setEditProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_min: parseFloat(value) || 0,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_min: undefined,
                                                }));
                                            }}
                                            accepter={InputNumber}
                                            step={0.01}
                                            precision={2}
                                        />
                                        {errorsEditForm.bobot_min && <p style={{ color: "red" }}>{errorsEditForm.bobot_min}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Maximal</Form.ControlLabel>
                                        <Form.Control
                                            name="bobot_max"
                                            value={editProductForm.bobot_max}
                                            onChange={(value) => {
                                                setEditProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_max: parseFloat(value) || 0,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_max: undefined,
                                                }));
                                            }}
                                            accepter={InputNumber}
                                            step={0.01}
                                            precision={2}
                                        />
                                        {errorsEditForm.bobot_max && <p style={{ color: "red" }}>{errorsEditForm.bobot_max}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot STD</Form.ControlLabel>
                                        <Form.Control
                                            name="bobot_std"
                                            value={editProductForm.bobot_std}
                                            onChange={(value) => {
                                                setEditProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_std: parseFloat(value) || 0,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_std: undefined,
                                                }));
                                            }}
                                            accepter={InputNumber}
                                            step={0.01}
                                            precision={2}
                                        />
                                        {errorsEditForm.bobot_std && <p style={{ color: "red" }}>{errorsEditForm.bobot_std}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Bobot Core Foil</Form.ControlLabel>
                                        <Form.Control
                                            name="bobot_core_foil"
                                            value={editProductForm.bobot_core_foil}
                                            onChange={(value) => {
                                                setEditProductForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    bobot_core_foil: parseFloat(value) || 0,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    bobot_core_foil: undefined,
                                                }));
                                            }}
                                            accepter={InputNumber}
                                            step={0.01}
                                            precision={2}
                                        />
                                        {errorsEditForm.bobot_core_foil && <p style={{ color: "red" }}>{errorsEditForm.bobot_core_foil}</p>}
                                    </Form.Group>
                                </Form>
                            </Modal.Body>
                            <Modal.Footer>
                                <Button
                                    onClick={() => {
                                        setShowEditModal(false);
                                        setEditProductForm(emptyEditProductForm);
                                        setErrorsEditForm({});
                                    }}
                                    appearance="subtle"
                                >
                                    Batal
                                </Button>
                                <Button
                                    onClick={() => {
                                        HandleEditProductApi();
                                    }}
                                    appearance="primary"
                                    type="submit"
                                    loading={loading}
                                    disabled={loading}
                                >
                                    Simpan
                                </Button>
                                {loading && <Loader backdrop size="md" vertical content="Editing Data..." active={loading} />}
                            </Modal.Footer>
                        </Modal>
                    </div>
                </div>
            </ContainerLayout>
        </div>
    );

}
