import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  try {
    const response = await axios[method](
      `${process.env.NEXT_PUBLIC_BASE_URL}/mobile-api/ts-matrix-categories-parameter/${url}`,
      data
    );
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export default function API_TsMatrixCategoriesParameter() {
  return {
    getAllTsMatrixCategoriesParameter: createApiFunction("get", "get-all"),
    addTsMatrixCategoriesParameter: createApiFunction("post", "add"),
    updateTsMatrixCategoriesParameter: createApiFunction("put", "update"),
    sfDeleteTsMatrixCategoriesParameter: createApiFunction("put", "s-delete"),
    sfUndeleteTsMatrixCategoriesParameter: createApiFunction("put", "s-undelete"),
  };
}