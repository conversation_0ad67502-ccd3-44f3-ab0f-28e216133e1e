import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/reagen/inbound/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};

const createApiFunctionDownload = (method, url) => async (data) => {
    try {
        const response = await axios({
            method: method,
            url: `${process.env.NEXT_PUBLIC_REAGEN}/v2/reagen/inbound/${url}`,
            data: data,
            responseType: 'blob', // This ensures the response is treated as a blob (binary data like PDF)
        });

        // Create a Blob URL for the file and trigger download
        const blob = new Blob([response.data], { type: response.headers['content-type'] });
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `${data.reagen_name}_${data.id_inbound_reagen_locator}.png`; // You can dynamically set the file name if needed
        document.body.appendChild(link);
        link.click();
        link.remove();

        return { success: true };
    } catch (err) {
        return {
            success: false,
            message: err.response?.data || 'Error downloading the file',
        };
    }
};

export default function InboundReagenApi(){
    return {
        getAll: createApiFunction("get", "get/all"),
        create: createApiFunction("post", "create"),
        createManual: createApiFunction("post", "create/manual"),
        getById: createApiFunction("get", "get/id"),
        getAllActive: createApiFunction("get", "get/all/active"),
        getAllActiveById: createApiFunction("get", "get/all/active/id"),
        edit: createApiFunction("put", "edit"),
        editStatus: createApiFunction("put", "edit/status"),
        getDetail: createApiFunction("post", "get/detail"),
        getRack: createApiFunction("post", "get/rack"),
        getListPrint : createApiFunction("post", "list/print"),
        getPrint : createApiFunctionDownload("post", "print"),
    }
}