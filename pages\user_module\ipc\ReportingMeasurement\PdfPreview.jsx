import ipcApi from "@/pages/api/ipcApi";
import { faDownload, faFileDownload } from "@fortawesome/free-solid-svg-icons";
import Head from "next/head";
import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { But<PERSON>, Stack } from "rsuite";
// import jsPDF from "jspdf";
// import html2canvas from "html2canvas";

export default function PdfPreview({
  idTransactionH,
  reportMeasurementDetails,
  dateFormattedHeaderData,
  product_code,
  station,
  step,
  max_data,
  min_data,
  avg_data,
  diff_data,
}) {
  const [stateShowDownload, setStateShowDownload] = useState(true);
  const [clickDownload, setClickDownload] = useState(false);
  let rowNumber = 1;

  const generatePDF = async () => {
    // const input = document.getElementById("printedDocument");
    // const pdf = new jsPDF("p", "mm", "a4");

    // const canvas = await html2canvas(input);
    // const imageData = canvas.toDataURL("image/png");

    // pdf.addImage(imageData, "PNG", 0, 0, 210, 297); // A4 size
    // pdf.save(
    //   `Print Out Reporting Measurement - In Process Control ID [${idTransactionH}]`
    // );
    setClickDownload(true)
    setStateShowDownload(false)
   // document.getElementById("headdownload").remove()
   //console.log(status)    
  };

    useEffect(() => {
      // window.print()
      // if (stateClicked) {
      //   const status = !stateClicked
      //   setStateClicked(status)
      //   //document.getElementById("headdownload").append()
      // }
      // console.log("first")
      
      if (clickDownload) {
        setClickDownload(false)
        window.print()          
      }

      setStateShowDownload(true)
    }, [stateShowDownload]);
  


  return (
    <>
      <Head>
        <title>
          Print out Reporting Measurement - In Process Control ID{" "}
          {idTransactionH}
        </title>
      </Head>
      <div>
      {stateShowDownload &&(
        <div
          id="headdownload"
          style={{
            width: "100%",
            padding: "1em",
            backgroundColor: "#2c2c30",
            boxShadow: "2px 2px 10px #6c6b75",
            position: "-webkit-sticky",
            position: "sticky",
            top: 0,
          }}
        >
          <Stack justifyContent="space-between">
            <Stack>
              <p style={{ color: "white", fontSize: "1em" }}>
                Print Out Reporting Measurement - In Process Control ID :{" "}
                {idTransactionH}
              </p>
            </Stack>
            <Stack>
              <Button title="Download" onClick={generatePDF}>
                <FontAwesomeIcon
                  icon={faFileDownload}
                  style={{ fontSize: 15 }}
                />
              </Button>
            </Stack>
          </Stack>
        </div>
        )}
        <div style={{ width: "100%", backgroundColor: "#65656b" }}>
          <div
            id="printedDocument"
            style={{
              width: "100%",
              backgroundColor: "white",
              margin: "auto",
              padding: "1.5em",
            }}
          >
            <div>
              <img
                src="/Logo_kalbe_detail.png"
                alt="Logo_kalbe_detail.png"
                style={{ width: 150 }}
              />
            </div>
            <div style={{ marginBottom: "2em", marginTop: "2em" }}>
              <p
                style={{
                  textAlign: "center",
                  fontWeight: "bold",
                  fontSize: "2em",
                }}
              >
                REPORTING MEASUREMENT
              </p>
            </div>
            <div style={{ marginBottom: "1em" }}>
              <table>
                <tbody>
                  <tr>
                    <td style={{ width: "8em" }}>Operator Name</td>
                    <td style={{ width: "6em" }}>:</td>
                    <td>
                      <strong>
                        {dateFormattedHeaderData[0].Employee_Name}
                      </strong>
                    </td>
                  </tr>
                  <tr>
                    <td>Date Checked</td>
                    <td>:</td>
                    <td>
                      <strong>{dateFormattedHeaderData[0].Created_Date}</strong>
                    </td>
                  </tr>
                  <tr>
                    <td>Product Code</td>
                    <td>:</td>
                    <td>
                    <strong>{product_code}</strong>
                    </td>
                  </tr>
                  <tr>
                    <td>Station</td>
                    <td>:</td>
                    <td>
                      <strong>{station}</strong>
                    </td>
                  </tr>
                  <tr>
                    <td>Step</td>
                    <td>:</td>
                    <td>
                      <strong>{step}</strong>
                    </td>
                  </tr>
                  <tr>
                    <td>Amount TMS</td>
                    <td>:</td>
                    <td>
                      <strong>{dateFormattedHeaderData[0].Amount_TMS}</strong>
                    </td>
                  </tr>
                  <tr>
                    <td>Amount MS</td>
                    <td>:</td>
                    <td>
                      <strong>{dateFormattedHeaderData[0].Amount_MS}</strong>
                    </td>
                  </tr>
                  {/* <tr>
                    <td>Start time - End Time</td>
                    <td>:</td>
                    <td>
                      <strong>{dateFormattedHeaderData[0].Start_Date} - {dateFormattedHeaderData[0].End_Date} in {dateFormattedHeaderData[0].Time_differ}</strong>
                    </td>
                  </tr> */}
                </tbody>
              </table>
            </div>
            <div>
              {reportMeasurementDetails && (
                <table
                  className="table"
                  style={{ width: "100%", textAlign: "center" }}
                >
                  <thead>
                    <tr>
                    <th style={{ width: "5%" }}>No</th>
                    <th style={{ width: "5%" }}>Hardness</th>
                    <th style={{ width: "5%" }}>Thickness</th>
                    <th style={{ width: "5%" }}>Diameter</th>
                    <th style={{ width: "5%" }}>Time</th>
                    <th style={{ width: "5%" }}>Status</th>
                    <th style={{ width: "70%" }}>Reason</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportMeasurementDetails.map((item) => {
                      return (
                        <tr key={rowNumber}>
                          <td>{rowNumber++}</td>
                          <td
                            style={{
                              color:
                                item.Hardness_Status === "TMS"
                                  ? "red"
                                  : "black",
                            }}
                          >
                            {item.Hardness_Value}
                          </td>
                          <td
                            style={{
                              color:
                                item.Thickness_Status === "TMS"
                                  ? "red"
                                  : "black",
                            }}
                          >
                            {item.Thickness_Value}
                          </td>
                          <td
                            style={{
                              color:
                                item.Diameter_Status === "TMS"
                                  ? "red"
                                  : "black",
                            }}
                          >
                            {item.Diameter_Value}
                          </td>
                          <td>{item.Time_insert}</td>
                          <td>{item.Overall_Status}</td>                          
                          <td>{item.Reason === "N/A" ? "-" : item.Reason}</td>
                        </tr>
                      );
                    })}
                    <tr key={rowNumber++}>
                          <td><strong>Max</strong></td>
                          <td>
                            {max_data.max_hardness}
                          </td>
                          <td>
                            {max_data.max_thickness}
                          </td>
                          <td>
                            {max_data.max_diameter}
                          </td>
                          <td>-</td>
                          <td>-</td>
                    </tr>
                    <tr key={rowNumber++}>
                          <td><strong>Min</strong></td>
                          <td>
                            {min_data.min_hardness}
                          </td>
                          <td>
                            {min_data.min_thickness}
                          </td>
                          <td>
                            {min_data.min_diameter}
                          </td>
                          <td>-</td>
                          <td>-</td>
                    </tr>
                    <tr key={rowNumber++}>
                          <td><strong>Average</strong></td>
                          <td>
                            {avg_data.avg_hardness}
                          </td>
                          <td>
                            {avg_data.avg_thickness}
                          </td>
                          <td>
                            {avg_data.avg_diameter}
                          </td>
                          <td>-</td>
                          <td>-</td>
                    </tr>
                    <tr key={rowNumber++}>
                          <td><strong>Difference</strong></td>
                          <td>
                            {diff_data.diff_hardness}
                          </td>
                          <td>
                            {diff_data.diff_thickness}
                          </td>
                          <td>
                            {diff_data.diff_diameter}
                          </td>
                          <td>-</td>
                          <td>-</td>
                    </tr>
                  </tbody>
                </table>
              )}
            </div>
            <div style={{ marginTop: "1em", marginBottom: "2em" }}>
              <table>
                <tbody>
                  <tr>
                    <td >Operated By</td>
                    <td  style={{paddingLeft:"3em", paddingRight:"1em"}}>:</td>
                    <td>
                      <strong>
                        {dateFormattedHeaderData[0].Employee_Name} at{" "}
                        {dateFormattedHeaderData[0].Created_Date}
                      </strong>
                    </td>
                  </tr>
                  <tr>
                    <td >Reviewed By and Approved By</td>
                    <td style={{paddingLeft:"3em", paddingRight:"1em"}}>:</td>
     
                    <td>
                      <strong>
                        {dateFormattedHeaderData[0].Approve_By ? `${dateFormattedHeaderData[0].Approve_By} at ${dateFormattedHeaderData[0].Approve_Date}` : 'NA'}
                      </strong>
                    </td>
                  </tr>
                  <tr>
                    <td >This document is printed by refer to SOP Document no SOP-PR-O061.00</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export async function getServerSideProps(context) {
  const { GetReportingMeasurementDetails, GetReportingMeasurementById } =
    ipcApi();
  const { query } = context;
  const { idTransactionH } = query;

  const dataReqHeader = {
    id_transaction: parseInt(idTransactionH),
  };
  const { Data: reportMeasuermentHeaderData } =
    await GetReportingMeasurementById(dataReqHeader);

  let dateFormattedHeaderData = [];
  if (reportMeasuermentHeaderData != undefined) {
    dateFormattedHeaderData = reportMeasuermentHeaderData.map((item) => {
      const initialApproveDate = item.Approve_Date;
      const initialCreatedDate = item.Created_Date;
      // const formattedApproveDate = initialApproveDate.split("T")[0];
      // const formattedCreatedDate = initialCreatedDate.split("T")[0];
      const formattedApproveDate = initialApproveDate;
      const formattedCreatedDate = initialCreatedDate;
      return {
        ...item,
        Approve_Date: formattedApproveDate,
        Created_Date: formattedCreatedDate,
      };
    });
  }

  const dataReqDetail = {
    id_transaction_h: parseInt(idTransactionH),
  };
  const { 
    Data: reportMeasurementDetailData, 
    Product_code: product_code, 
    Station: station, 
    Step: step ,
    max_data: max_data,
		min_data: min_data,
		avg_data: avg_data,
		diff_data: diff_data,
  } =
    await GetReportingMeasurementDetails(dataReqDetail);

  let reportMeasurementDetails = [];
  if (reportMeasurementDetailData) {
    reportMeasurementDetails = reportMeasurementDetailData;
  }

  return {
    props: {
      idTransactionH,
      reportMeasurementDetails,
      dateFormattedHeaderData,
      product_code,
      station, 
      step,
      max_data,
      min_data,
      avg_data,
      diff_data,
    },
  };
}
