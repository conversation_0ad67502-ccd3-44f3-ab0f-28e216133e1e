import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  useToaster,
  Notification,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  Loader,
  RadioGroup,
  Radio,
  SelectPicker,
  Grid,
  Row,
  Col,
  Card,
  Placeholder,
  FlexboxGrid,
  Whisper,
  Tooltip,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";

import ApiLogbook from "@/pages/api/pqr/logbook/api_logbook";

export default function SupervisorListLogbookCM1() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const toaster = useToaster();
  const [loading, setLoading] = useState(false);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [activityDataState, setActivityDetailDataState] = useState([]);
  const [password, setPassword] = useState("");
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [showActivityDetailModal, setShowActivityDetailModal] = useState(false);
  const [hoveredCard, setHoveredCard] = useState(null);

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = activityDataState.filter((rowData, i) => {
    const searchFields = ["id_activity", "activity_type", "activity_start", "activity", "remarks", "activity_end", "create_date", "create_by", "update_date", "update_by", "delete_date", "delete_by", "is_active"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : activityDataState.length;

  const formatDateTimeCustomNoIso = (isoDateString) => {
    if (!isoDateString) return "-";
    const [datePart, timePart] = isoDateString.split("T");
    const [year, month, day] = datePart.split("-");
    const [hours, minutes] = timePart.split(":");

    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("pqr/logbook-spv"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandlegetAllNeedApproveLogbookApi();
    }
  }, []);

  const HandleEditStatusLogbookApprovalApi = async (id_activity, password) => {
    setLoading(true);
    try {
      const res = await ApiLogbook().PutApproveLogbook({
        id_activity,
        approve_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
        employee_id: sessionAuth.employee_id,
        password: password,
      });

      if (res.status === 200) {
        setActivityDetailDataState((prevData) => prevData.filter((activity) => activity.id_activity !== id_activity));
        await HandlegetAllNeedApproveLogbookApi("CM1");
        console.log("Updated activityDataState: ", activityDataState);
        setShowActivityDetailModal(false);
        setSelectedActivity(null);
        setPassword("");
        showNotification("success", "Berhasil approve aktifitas Logbook.");
      } else {
        if (res.message === "wrong username or password") {
          console.log("Gagal mengubah status Logbook: ", res.message);
          showNotification("error", "Password Salah");
          setPassword("");
        } else {
          console.log("Gagal mengubah status Logbook: ", res.message);
          showNotification("error", `Approval gagal: ${res.message}`);
        }
      }
    } catch (error) {
      console.log("Gagal mengubah status Logbook: ", error);
      showNotification("error", `Approval error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const HandlegetAllNeedApproveLogbookApi = async (activity_type) => {
    try {
      const res = await ApiLogbook().GetAllNeedApproveLogbook({
        activity_type: "CM1",
      });

      if (res.status === 200) {
        console.log("Data received: ", res.data);
        if (res.data && res.data.length > 0) {
          setActivityDetailDataState(res.data);
        } else {
          setActivityDetailDataState([]);
        }
      } else {
        console.log("Error on update status: ", res.message);
      }
    } catch (error) {
      console.log("Error on update status: ", error.message);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Logbook Supervisor Approval CM1 List</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>Logbook Supervisor</Breadcrumb.Item>
                  <Breadcrumb.Item active>CM1</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Approval Supervisor Logbook CM1</h5>
              </Stack>
            }
          ></Panel>
          {/* content section */}
          <div>
            <Row gutter={16}>
              {activityDataState.length > 0 ? (
                activityDataState.map((activity) => (
                  <Col key={activity.id_activity} xs={24} sm={12} md={8} lg={6} className="mb-3">
                    <Panel
                      bordered
                      onMouseEnter={() => setHoveredCard(activity.id_activity)}
                      onMouseLeave={() => setHoveredCard(null)}
                      style={{
                        width: "100%",
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "space-between",
                        boxShadow: hoveredCard === activity.id_activity ? "0 6px 12px rgba(0,0,0,0.15)" : "0 4px 8px rgba(0,0,0,0.1)",
                        transition: "all 0.3s ease",
                      }}
                      header={`${activity.activity_type} - ${activity.activity}`}
                      bodyFill
                    >
                      <div className="p-3">
                        <div className="mb-2">
                          <strong>Mulai:</strong> {formatDateTimeCustomNoIso(activity.activity_start)}
                        </div>
                        <div className="mb-2">
                          <strong>Selesai :</strong> {formatDateTimeCustomNoIso(activity.activity_end)}
                        </div>
                        <div
                          className="mb-2"
                          style={{
                            overflow: "hidden",
                            whiteSpace: "nowrap",
                          }}
                        >
                          <strong>Catatan :</strong>
                          {activity.remarks && (activity.remarks.length > 20 ? `${activity.remarks.substring(0, 20)}...` : activity.remarks)}
                        </div>

                        <Stack justifyContent="space-between" alignItems="center" className="mt-5">
                          <div></div>
                          <Button
                            appearance="primary"
                            onClick={() => {
                              setSelectedActivity(activity);
                              setShowActivityDetailModal(true);
                            }}
                          >
                            Approve
                          </Button>
                        </Stack>
                      </div>
                    </Panel>
                  </Col>
                ))
              ) : (
                <div style={{ textAlign: "center", margin: "20px" }}>
                  <h5>Tidak ada data logbook yang tersedia.</h5>
                </div>
              )}
            </Row>

            {/* Activity Detail Modal */}

            <Modal
              backdrop="static"
              open={showActivityDetailModal}
              onClose={() => {
                setShowActivityDetailModal(false);
                setSelectedActivity(null);
                setPassword("");
              }}
              size="md"
            >
              <Modal.Header>
                <Modal.Title>Detail Aktivitas Logbook</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                {selectedActivity && (
                  <Form fluid>
                    <FlexboxGrid>
                      {/* Section 1 */}
                      <FlexboxGrid.Item colspan={12} style={{ paddingRight: "10px", paddingBottom: "15px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Tipe Aktivitas</Form.ControlLabel>
                          <Form.Control name="activity_type" value={selectedActivity?.activity_type || "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Mulai</Form.ControlLabel>
                          <Form.Control name="activity_start" value={selectedActivity?.activity_start ? formatDateTimeCustomNoIso(selectedActivity.activity_start) : "-"} readOnly />
                        </Form.Group>
                        <Form.Group controlId="textarea">
                          <Form.ControlLabel>Catatan</Form.ControlLabel>
                          <Input
                            as="textarea"
                            name="remarks"
                            value={selectedActivity?.remarks || "-"}
                            readOnly
                            style={{
                              resize: "none",
                              height: "120px",
                              overflowY: "auto",
                              overflowX: "hidden",
                              whiteSpace: "pre-wrap",
                              wordBreak: "break-all",
                              wordWrap: "break-word",
                              width: "100%",
                              maxWidth: "100%",
                              padding: "8px",
                              boxSizing: "border-box",
                            }}
                          />
                        </Form.Group>
                      </FlexboxGrid.Item>

                      {/* Section 2 */}
                      <FlexboxGrid.Item colspan={12} style={{ paddingBottom: "15px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Aktivitas</Form.ControlLabel>
                          <Form.Control name="activity" value={selectedActivity.activity || "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Selesai</Form.ControlLabel>
                          <Form.Control name="activity_end" value={selectedActivity?.activity_end ? formatDateTimeCustomNoIso(selectedActivity.activity_end) : "-"} readOnly />
                        </Form.Group>
                      </FlexboxGrid.Item>
                    </FlexboxGrid>

                    {/* Section 3 - Additional Information */}
                    <FlexboxGrid>
                      <FlexboxGrid.Item colspan={12} style={{ paddingRight: "10px", paddingBottom: "15px", paddingTop: "15px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Create Date</Form.ControlLabel>
                          <Form.Control name="create_date" value={selectedActivity?.create_date ? new Date(selectedActivity.create_date).toLocaleDateString("en-GB") : "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Update Date</Form.ControlLabel>
                          <Form.Control name="update_date" value={selectedActivity?.update_date ? new Date(selectedActivity.update_date).toLocaleDateString("en-GB") : "-"} readOnly />
                        </Form.Group>
                      </FlexboxGrid.Item>

                      <FlexboxGrid.Item colspan={12} style={{ paddingRight: "10px", paddingBottom: "15px", paddingTop: "15px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Create By</Form.ControlLabel>
                          <Form.Control name="create_by" value={selectedActivity?.create_by || "-"} readOnly />
                        </Form.Group>
                        <Form.Group>
                          <Form.ControlLabel>Update By</Form.ControlLabel>
                          <Form.Control name="update_by" value={selectedActivity?.update_by || "-"} readOnly />
                        </Form.Group>
                      </FlexboxGrid.Item>
                    </FlexboxGrid>

                    <FlexboxGrid>
                      <FlexboxGrid.Item colspan={12} style={{ paddingRight: "10px", paddingBottom: "15px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Delete Date</Form.ControlLabel>
                          <Form.Control name="delete_date" value={selectedActivity?.delete_date ? new Date(selectedActivity.delete_date).toLocaleDateString("en-GB") : "-"} readOnly />
                        </Form.Group>
                      </FlexboxGrid.Item>

                      <FlexboxGrid.Item colspan={12} style={{ paddingBottom: "15px" }}>
                        <Form.Group>
                          <Form.ControlLabel>Delete By</Form.ControlLabel>
                          <Form.Control name="delete_by" value={selectedActivity?.delete_by || "-"} readOnly />
                        </Form.Group>
                      </FlexboxGrid.Item>
                    </FlexboxGrid>
                  </Form>
                )}
              </Modal.Body>
              <Modal.Footer>
                <div style={{ display: "flex", justifyContent: "flex-end", width: "100%" }}>
                  <InputGroup style={{ width: "150px", marginBottom: "16px", marginTop: "35px" }}>
                    <Whisper placement="top" trigger="focus" speaker={<Tooltip>Masukkan password untuk proses approval</Tooltip>}>
                      <Input type="password" placeholder="Masukan Password" value={password} onChange={(value) => setPassword(value)} />
                    </Whisper>
                  </InputGroup>
                </div>
                <Button
                  appearance="subtle"
                  onClick={() => {
                    setShowActivityDetailModal(false);
                    setSelectedActivity(null);
                    setPassword("");
                  }}
                >
                  Cancel
                </Button>
                <Button
                  appearance="primary"
                  onClick={async () => {
                    setLoading(true);
                    await HandleEditStatusLogbookApprovalApi(selectedActivity.id_activity, password);
                    setLoading(false);
                  }}
                  loading={loading}
                  disabled={loading || !password.trim()}
                >
                  Submit
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
