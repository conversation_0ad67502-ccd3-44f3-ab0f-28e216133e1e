import { useEffect, useState } from "react";
import Head from "next/head"
import { <PERSON><PERSON><PERSON><PERSON>b, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash"
import { useRouter } from "next/router";
import API_StockCorrection from "@/pages/api/stock_correction/api_stock_correction";

export default function RequestCorrectionPage() {

    const [moduleName, setModuleName] = useState("");
    const { HeaderCell, Cell, Column } = Table;
    const [requestCorrection, setRequestCorrection] = useState([]);
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [props, setProps] = useState([]);
    const [showAddModal, setShowAddModal] = useState(false);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [approvalDetail, setApprovalDetail] = useState([]);
    const emptyFormValue = {
        remarks: null,
    }
    const [formValue, setFormValue] = useState(emptyFormValue);
    const toaster = useToaster();
    const [errors, setErrors] = useState({});
    const router = useRouter();

    const handleGetAllApi = async () => {
        const res = await API_StockCorrection().getAll();
        console.log(res);
        setRequestCorrection(res.data ? res.data : []);
    };

    const handleGetApprovalDetailApi = async (selectedIdRequest) => {
        const res = await API_StockCorrection().getApprovalDetailRequest({ id_reagen_correction: selectedIdRequest });
        setApprovalDetail(res.data ? res.data : [])
    }

    const handleAddApi = async () => {
        try {
            const result = await API_StockCorrection().create({
                ...formValue,
                submitted_by: props.employee_id,
            });

            if (result.status === 200) {
                handleGetAllApi();
                setShowAddModal(false);

                toaster.push(
                    Messages("success", "Success adding Request Correction!"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setFormValue(emptyFormValue);
            } else if (result.status === 400) {
                toaster.push(
                    Messages("error", `Error: "${result.message}". Please try again later!`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setFormValue(emptyFormValue);
                setShowAddModal(false);
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    };

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = requestCorrection
        .filter((rowData, i) => {
            const searchFields = [
                "id_reagen_correction",
                "submitted_dt",
                "submitted_by",
                "submitted_name",
                "spv_approved_dt",
                "spv_approved_by",
                "spv_approved_name",
                "mgr_approved_dt",
                "mgr_approved_by",
                "mgr_approved_name",
                "status_correction",
                "remarks",
                "conclusion",
                "is_active",
            ];

            const matchesSearch = searchFields.some((field) =>
                rowData[field]
                    ?.toString()
                    .toLowerCase()
                    .includes(searchKeyword.toLowerCase())
            );

            return matchesSearch;
        })

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    }

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : requestCorrection.length;

    const handleExportExcel = () => {

        if (inbound.length === 0) {
            toaster.push(Messages("error", "No data to export!"), {
                placement: "topCenter",
                duration: 5000,
            });
            return;
        }

        const headerMapping = {
            id_reagen_correction: "ID",
            submitted_dt: "Submitted Date",
            submitted_by: "Submitted By",
            submitted_name: "Submitted Name",
            spv_approved_dt: "Supervisor Approved Date",
            spv_approved_by: "Supervisor Approved By",
            spv_approved_name: "Superviser Approved Name",
            mgr_approved_dt: "Manager Approved Date",
            mgr_approved_by: "Manager Approved By",
            mgr_approved_name: "Manager Approved Name",
            status_correction: "Approval Status",
            remarks: "Remarks",
            conclusion: "Conclusion",
            is_active: "Status",
        };

        const formattedData = requestCorrection.map((item) => {
            const formattedItem = {};
            for (const key in item) {
                if (headerMapping[key]) {
                    if (key === 'is_active') {
                        formattedItem[headerMapping[key]] = item[key] === 1 ? 'Active' : 'Inactive';
                    } else {
                        formattedItem[headerMapping[key]] = item[key];
                    }
                }
            }
            return formattedItem;
        });

        const ws = XLSX.utils.json_to_sheet(formattedData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "data");

        const date = dateFormatterDash(new Date());
        const filename = `Request Correction ${date}.xlsx`;

        XLSX.writeFile(wb, filename);
    }

    const handleSubmit = async (apiFunction) => {
        if (!formValue.remarks) {
            setErrors({ remarks: 'Remarks is required.' });
            return;
        }
        setErrors({});
        await apiFunction();
    };

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("stock_correction/request_correction")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            handleGetAllApi();
        }
    }, []);

    return (
        <>
            <div>
                <Head>
                    <title>Request Correction</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Quality</Breadcrumb.Item>
                                    <Breadcrumb.Item>Stock Correction</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Request Correction</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                        header={
                            <Stack justifyContent="space-between">
                                <div className="flex gap-2">
                                    {/* buka untuk menampilkan add */}
                                    {/* <IconButton
                                        icon={<PlusRoundIcon />}
                                        appearance="primary"
                                        onClick={() => {
                                            setShowAddModal(true);
                                        }}>
                                        Add
                                    </IconButton> */}
                                    <IconButton
                                        icon={<FileDownloadIcon />}
                                        appearance="primary"
                                        onClick={handleExportExcel}
                                    >
                                        Download (.xlsx)
                                    </IconButton>
                                </div>

                                <InputGroup inside>
                                    <InputGroup.Addon>
                                        <SearchIcon />
                                    </InputGroup.Addon>
                                    <Input
                                        placeholder="search"
                                        value={searchKeyword}
                                        onChange={handleSearch}
                                    />
                                    <InputGroup.Addon
                                        onClick={() => {
                                            setSearchKeyword("");
                                            setPage(1);
                                        }}
                                        style={{
                                            display: searchKeyword ? "block" : "none",
                                            color: "red",
                                            cursor: "pointer",
                                        }}>
                                        <CloseOutlineIcon />
                                    </InputGroup.Addon>
                                </InputGroup>
                            </Stack>
                        }
                    >
                        <Table
                            bordered
                            cellBordered
                            height={400}
                            data={getPaginatedData(getFilteredData(), limit, page)}
                            sortColumn={sortColumn}
                            sortType={sortType}
                            onSortColumn={handleSortColumn}
                        >
                            <Column width={70} align="center" fixed>
                                <HeaderCell>No</HeaderCell>
                                <Cell>
                                    {(rowData, rowIndex) => {
                                        return rowIndex + 1 + limit * (page - 1);
                                    }}
                                </Cell>
                            </Column>
                            <Column width={150} align="center" sortable>
                                <HeaderCell>ID Reagen Correction</HeaderCell>
                                <Cell dataKey="id_reagen_correction" />
                            </Column>
                            <Column width={180} sortable resizable>
                                <HeaderCell>Approval Status</HeaderCell>
                                <Cell>
                                    {(rowData) => (
                                        <span
                                            style={{
                                                color: rowData.status_correction === 1 ? "green" : (rowData.status_correction === 0 ? "red" : "gray"),
                                            }}
                                        >
                                            {rowData.status_correction === 1 ? "Approved" : (rowData.status_correction === 0 ? "Rejected" : (rowData.status_correction === 2 ? "On-Going Supervisor" : "On-Going Manager"))}
                                        </span>
                                    )}
                                </Cell>
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Remarks</HeaderCell>
                                <Cell dataKey="remarks" />
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Submit Date</HeaderCell>
                                <Cell dataKey="submitted_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Submit By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        `${rowData.submitted_by} - ${rowData.submitted_name}`
                                    )}
                                </Cell>
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Supervisor Approved Date</HeaderCell>
                                <Cell dataKey="spv_approved_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Supervisor Approved By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        rowData.spv_approved_by ? `${rowData.spv_approved_by} - ${rowData.spv_approved_name}` : ""
                                    )}
                                </Cell>
                            </Column>
                            <Column width={150} sortable resizable>
                                <HeaderCell>Manager Approved Date</HeaderCell>
                                <Cell dataKey="mgr_approved_dt" />
                            </Column>
                            <Column width={250} sortable resizable>
                                <HeaderCell>Manager Approved By</HeaderCell>
                                <Cell>
                                    {rowData => (
                                        rowData.mgr_approved_by ? `${rowData.mgr_approved_by} - ${rowData.mgr_approved_name}` : ""
                                    )}
                                </Cell>
                            </Column>
                            {/* <Column width={150} sortable resizable>
                                <HeaderCell>Conclusion</HeaderCell>
                                <Cell dataKey="Conclusion" />
                            </Column> */}
                            <Column width={100} sortable resizable>
                                <HeaderCell>Status</HeaderCell>
                                <Cell>
                                    {(rowData) => (
                                        <span
                                            style={{
                                                color: rowData.is_active === 1 ? "green" : "red",
                                            }}
                                        >
                                            {rowData.is_active === 1 ? "Active" : "Inactive"}
                                        </span>
                                    )}
                                </Cell>
                            </Column>
                            <Column width={80} fixed="right" align="center">
                                <HeaderCell>Action</HeaderCell>
                                <Cell style={{ padding: "8px" }}>
                                    {(rowData) => (
                                        <div>
                                            {/* <Button
                                                appearance="subtle"
                                                onClick={() =>
                                                    handleEditStatusCorrection(rowData.id_reagen_correction)
                                                }
                                            >
                                                {rowData.status_correction === 2 ? (
                                                    rowData.is_active === 1 ? (
                                                        <TrashIcon style={{ fontSize: "16px" }} />
                                                    ) : (
                                                        <ReloadIcon style={{ fontSize: "16px" }} />
                                                    )
                                                ): null}
                                            </Button> */}
                                            <Button
                                                appearance="link"
                                                onClick={() => {
                                                    setShowDetailModal(true);
                                                    handleGetApprovalDetailApi(rowData.id_reagen_correction)
                                                }}
                                            >
                                                Detail
                                            </Button>
                                        </div>
                                    )}
                                </Cell>
                            </Column>
                        </Table>
                        <div style={{ padding: 20 }}>
                            <Pagination
                                prev
                                next
                                first
                                last
                                ellipsis
                                boundaryLinks
                                maxButtons={5}
                                size="xs"
                                layout={["total", "-", "limit", "|", "pager", "skip"]}
                                limitOptions={[10, 30, 50]}
                                total={totalRowCount}
                                limit={limit}
                                activePage={page}
                                onChangePage={setPage}
                                onChangeLimit={handleChangeLimit}
                            />
                        </div>
                    </Panel>
                </div>

                {/* add modal */}
                <Modal
                    backdrop="static"
                    open={showAddModal}
                    onClose={() => {
                        setShowAddModal(false);
                        setFormValue(emptyFormValue);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Add Request Correction</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Remarks</Form.ControlLabel>
                                <Form.Control
                                    name="remarks"
                                    value={formValue.remarks || ''}
                                    onChange={(value) => {
                                        setFormValue({ ...formValue, remarks: value })
                                        setErrors((prevErrors) => ({
                                            ...prevErrors,
                                            remarks: null,
                                        }))
                                    }}
                                />
                                {errors.remarks && <p style={{ color: 'red' }}>{errors.remarks}</p>}
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowAddModal(false);
                                setFormValue(emptyFormValue);
                                setErrors({});
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleSubmit(handleAddApi);
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Add
                        </Button>
                    </Modal.Footer>
                </Modal>

                {/* Detail Modal */}
                <Modal
                    backdrop="static"
                    open={showDetailModal}
                    onClose={() => {
                        setShowDetailModal(false);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Inbound Reagen Detail</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        {approvalDetail.map((item) => (
                            <div key={item.id_inbound_reagen_locator}>
                                <Panel bordered className="mb-3">
                                    <p className="mb-2"><span className="fw-bold">ID Inbound Reagen Locator : </span>{item.id_inbound_reagen_locator}</p>
                                    <p className="mb-2"><span className="fw-bold">Reagen Name : </span>{item.reagen_name}</p>
                                    <p className="mb-2"><span className="fw-bold">Expired Date : </span>{new Date(item.expired_date).toLocaleDateString('en-GB')}</p>
                                    <p className="mb-2"><span className="fw-bold">Rack : </span>{item.rack_desc}</p>
                                    <p className="mb-2"><span className="fw-bold">Floor : </span>{item.floor_level_desc}</p>
                                    <p className="mb-2"><span className="fw-bold">Row : </span>{item.row_level_desc}</p>
                                </Panel>
                            </div>
                        ))}
                    </Modal.Body>
                </Modal>

            </ContainerLayout>
        </>
    )
}
