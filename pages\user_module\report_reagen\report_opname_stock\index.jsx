import { useEffect, useState } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  useToaster,
  SelectPicker,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import XLSX from "xlsx";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import { useRouter } from "next/router";
import ApiReportReagen from "@/pages/api/report_reagen/api_report_reagen";
import API_MasterDataMasterDataReagenCriteria from "@/pages/api/master_data/api_masterdata_master_data_reagen_criteria";
import API_MasterDataMasterDataReagenDimension from "@/pages/api/master_data/api_masterdata_master_data_reagen_dimension";
import API_MasterDataMasterDataReagenCategory from "@/pages/api/master_data/api_masterdata_master_data_reagen_category";

export default function ReportStockOpname() {
  const [moduleName, setModuleName] = useState("");
  const { HeaderCell, Cell, Column } = Table;
  const [expiredStockData, setExpiredStockData] = useState([]);
  const [stockOpnameSession, setStockOpnameSession] = useState([]);
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const [props, setProps] = useState([]);
  const toaster = useToaster();
  const router = useRouter();
  const [dateRange, setDateRange] = useState([])
  const [mapCriteria, setMapCriteria] = useState([]);
  const [mapDimension, setMapDimension] = useState([]);
  const [mapCategory, setMapCategory] = useState([]);
  const [formValue, setFormValue] = useState({
    id_criteria: null,
    id_category: null,
    id_dimension: null,
  })

  const handleDateRange = async (date) => {
    if (date != '') {
      let yyyy = date.getFullYear();
      let mm = date.getMonth() + 1;


      const res = await ApiReportReagen().getListExpired({
        "month": mm,
        "year": yyyy
      });
      console.log(res);
      setExpiredStockData(res.data ? res.data : []);
    } else {
      hanldeInitialApi()
    }
  }


  const GetStockOpnameReport = async () => {
    try {
      const res = await ApiReportReagen().getListStockOpname({
        id_criteria: formValue.id_criteria,
        id_dimension: formValue.id_dimension,
        id_category: formValue.id_category,
      })

      if (res.status == 200) {
        setStockOpnameSession(res.data || [])
      } else {
        console.log(res.message)
        setStockOpnameSession([])
      }

    } catch (error) {
      console.error(err)
      setStockOpnameSession([])
    }
  }

  const GetMasterData = async () => {
    const res_map_criteria =
      await API_MasterDataMasterDataReagenCriteria().getAll();
    setMapCriteria(res_map_criteria.data || []);

    const res_map_dimension =
      await API_MasterDataMasterDataReagenDimension().getAll();
    setMapDimension(res_map_dimension.data || []);

    const res_map_category =
      await API_MasterDataMasterDataReagenCategory().getAll();
    setMapCategory(res_map_category.data || []);
  }

  const hanldeInitialApi = async () => {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // Months are zero-based, so add 1

    console.log(new Date())

    const res = await ApiReportReagen().getListExpired({
      "month": currentMonth,
      "year": currentYear
    });
    console.log(res);
    setExpiredStockData(res.data ? res.data : []);
  };



  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    console.log("object", sortColumn, sortType);
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };



  const filteredData = stockOpnameSession
    .filter((rowData, i) => {
      const searchFields = [
        "reagen_name",
        "expired_date"
      ];

      const matchesSearch = searchFields.some((field) =>
        rowData[field]
          ?.toString()
          .toLowerCase()
          .includes(searchKeyword.toLowerCase())
      );

      return matchesSearch;
    });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
  
        // Convert "expired_date" to Date objects for correct sorting
        if (sortColumn === "expired_date") {
          console.log(sortColumn)
         const parseDate = (dateStr) => {
            if (!dateStr) return null; // Handle null or undefined values
          
            // Check if the format is ISO (e.g., 2025-04-12T00:00:00Z)
            if (dateStr.includes("T")) {
              return new Date(dateStr);
            }
          
            // Handle dd/mm/yyyy format
            const [day, month, year] = dateStr.split("/").map(Number);
            return new Date(year, month - 1, day); // JS Date months are 0-based
          };
          
  
          x = parseDate(x);
          y = parseDate(y);
        } else {
          // Handle non-date sorting (strings, numbers, etc.)
          if (typeof x === "string") x = x.toLowerCase(); // Sort case-insensitively
          if (typeof y === "string") y = y.toLowerCase();
        }
  
        if (sortType === "asc") {
          return x > y ? 1 : x < y ? -1 : 0;
        } else {
          return x < y ? 1 : x > y ? -1 : 0;
        }
      });
    }
    return filteredData;
  };
  

  const totalRowCount = searchKeyword ? filteredData.length : expiredStockData.length;


  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("report_reagen/report_opname_stock")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      // GetStockOpnameReport();
      GetMasterData();
      // hanldeInitialApi();
    }
  }, []);

  const handleExportExcel = () => {
    if (stockOpnameSession.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topCenter",
        duration: 5000,
      });
      return;
    }



    const headerMapping = {
      room_temperature: "room temperature",
      reagen_name: "reagen name",
      expired_date: "expired date",
      total_stock: "total stock",
    };

    const formattedData = stockOpnameSession

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const date = dateFormatterDash(new Date());
    const filename = `Indirect data${date}.xlsx`;

    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <div>
        <Head>
          <title>Report Stock Reagen</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>{moduleName}</Breadcrumb.Item>
                  <Breadcrumb.Item>Report Reagen</Breadcrumb.Item>
                  <Breadcrumb.Item active>Report Stock</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                  <SelectPicker
                    placeholder="criteria"
                    name="id_criteria"
                    value={formValue.id_criteria}
                    onChange={(value) => {
                      setFormValue({ ...formValue, id_criteria: value });
                      // setFormErrors((prevErrors) => ({
                      //   ...prevErrors,
                      //   id_criteria: null,
                      // }));
                    }}
                    block
                    data={mapCriteria}
                    labelKey="criteria_desc"
                    valueKey="id_criteria"
                  />
                  {/* {formErrors.id_criteria && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.id_criteria}
                  </div>
                )} */}

                  <SelectPicker
                    placeholder="dimension"
                    name="id_dimension"
                    value={formValue.id_dimension}
                    onChange={(value) => {
                      setFormValue({ ...formValue, id_dimension: value });
                      // setFormErrors((prevErrors) => ({
                      //   ...prevErrors,
                      //   id_dimension: undefined,
                      // }));
                    }}
                    block
                    data={mapDimension}
                    labelKey="dimension_desc"
                    valueKey="id_dimension"
                  />
                  {/* {formErrors.id_dimension && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.id_dimension}
                  </div>
                )} */}

                  <SelectPicker
                    placeholder="category"
                    name="id_category"
                    value={formValue.id_category}
                    onChange={(value) => {
                      setFormValue({ ...formValue, id_category: value });
                      // setFormErrors((prevErrors) => ({
                      //   ...prevErrors,
                      //   id_category: undefined,
                      // }));
                    }}
                    block
                    data={mapCategory}
                    labelKey="category_desc"
                    valueKey="id_category"
                  />
                  {/* {formErrors.id_category && (
                  <div
                    style={errorMessageStyle}>
                    {formErrors.id_category}
                  </div>
                )} */}

                  <Button appearance="primary" onClick={() => {
                    GetStockOpnameReport()
                  }}>
                    Get Data
                  </Button>
                  <IconButton
                    icon={<FileDownloadIcon />}
                    appearance="primary"
                    onClick={handleExportExcel}
                  >
                    Download (.xlsx)
                  </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getFilteredData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Reagen name</HeaderCell>
                <Cell dataKey="reagen_name" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Catalogue name</HeaderCell>
                <Cell dataKey="catalogue" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>{"Opened (Outbound)"}</HeaderCell>
                <Cell dataKey="opened" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>{"Opened (Correction)"}</HeaderCell>
                <Cell dataKey="corrected" />
              </Column>
              <Column width={150} align="center" resizable={true}>
                <HeaderCell>Not Opened</HeaderCell>
                <Cell dataKey="not_opened">
                  {(rowData) => (
                    <span
                      style={{
                        color: (() => {
                          const notOpened = rowData.not_opened;
                          if (notOpened <= 1) {
                            return "rgb(220 38 38)"; // Soft red
                          } else if (notOpened === 2 || notOpened === 3) {
                            return "rgb(234 88 12)"; // Soft orange
                          } else if (notOpened === 4 || notOpened === 5) {
                            return "rgb(202 138 4)"; // Soft yellow
                          } else if (notOpened >= 6) {
                            return "rgb(22 163 74)"; // Soft green
                          }
                        })(),
                      }}
                    >
                      {rowData.not_opened}
                    </span>
                  )}

                </Cell>
              </Column>
              <Column width={150} align="center" resizable={true} sortable>
                <HeaderCell>Expired Date</HeaderCell>
                <Cell dataKey="expired_date">
                  {(rowData) => {
                      const dateObj = new Date(rowData.expired_date);
                      const formattedDate = dateObj
                        .toLocaleDateString("en-GB", {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        })
                        .replace(/\//g, "-");

                      return (
                        <span
                          style={{
                            color: dateObj <= new Date() ? "red" : "green",
                          }}
                        >
                          {formattedDate}
                        </span>
                      );
                    }}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>
    </>
  );
}
