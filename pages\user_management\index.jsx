import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import {
  IconButton,
  Stack,
  Table,
  Panel,
  Pagination,
  Popover,
  Whisper,
  Dropdown,
  InputGroup,
  Input,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import MoreIcon from "@rsuite/icons/legacy/More";
import ContainerLayout from "@/components/layout/ContainerLayout";
import API_User from "@/pages/api/userApi";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";

function UserManagement({ dataUser }) {
  const router = useRouter();
  const [users, setUsers] = useState([]);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);

  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const filteredData = users.filter((item) => {
    const searchFields = [
      "Employee_Id",
      "Name",
      "Email",
      "Division",
      "Department",
      "Job_title",
    ];
    const keyword = searchKeyword.trim().toLowerCase();

    return searchFields.some((field) =>
      item[field]?.toLowerCase().includes(keyword)
    );
  });

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (!dataLogin) {
      router.push("/");
    }

    const generalAdministrationAccessValidation = dataLogin.module_code.filter(
      (item) => item === 10
    );
    if (generalAdministrationAccessValidation.length === 0) {
      router.push("/dashboard");
      return;
    }
  }, []);

  useEffect(() => {
    if (dataUser) {
      setUsers(dataUser);
    }
    console.log("data user: ", dataUser);
  }, [dataUser]);

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleChangePage = (page) => {
    setPage(page);
  };

  const paginatedUsers = users.slice((page - 1) * limit, page * limit);

  const renderMenu = (rowData, { onClose, left, top, className }, ref) => {
    const handleSelect = (eventKey) => {
      onClose();
      console.log(eventKey);
      // Handle different actions based on eventKey
      switch (eventKey) {
        case "edit":
          router.push(
            `/user_management/editUser/?userId=${rowData.Employee_Id}`
          );
          break;
        case "mapping":
          router.push({
            pathname: "/user_management/userMapping",
            query: { emp_id: rowData.Employee_Id, emp_name: rowData.Name },
          });
          break;
        case "access":
          router.push({
            pathname: "/user_management/userAccess",
            query: { emp_id: rowData.Employee_Id, emp_name: rowData.Name },
          });
          break;
        default:
          break;
      }
    };
    return (
      <Popover ref={ref} className={className} style={{ left, top }} full>
        <Dropdown.Menu onSelect={handleSelect}>
          <Dropdown.Item eventKey="edit">Edit User</Dropdown.Item>
          <Dropdown.Item eventKey="mapping">User Mapping</Dropdown.Item>
          <Dropdown.Item eventKey="access">User Access</Dropdown.Item>
        </Dropdown.Menu>
      </Popover>
    );
  };

  const ActionCell = ({ rowData, dataKey, ...props }) => {
    return (
      <Cell {...props} className="link-group">
        <Whisper
          placement="auto"
          trigger="click"
          speaker={renderMenu.bind(null, rowData)}
        >
          <IconButton icon={<MoreIcon />} />
        </Whisper>
      </Cell>
    );
  };

  return (
    <>
      <Head>
        <title>User Management</title>
      </Head>

      <ContainerLayout
        title="User Management"
        customNavMenu={[
          {
            name: "Menu Management",
            route: "menu_management",
            icon: "faTableList",
          },
          {
            name: "Module Management",
            route: "module_management",
            icon: "faBook",
          },
          {
            name: "User Management",
            route: "user_management",
            icon: "faUserAlt",
          },
          {
            name: "Department Management",
            route: "department_management",
            icon: "faBuilding",
          },
        ]}
      >
        <div className="m-4 pt-2">
          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <IconButton
                  icon={<PlusIcon />}
                  appearance="primary"
                  onClick={() => router.push("/user_management/addUser")}
                >
                  Add New User
                </IconButton>

                <div style={{ marginLeft: "auto" }}>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input
                      placeholder="Search"
                      value={searchKeyword}
                      onChange={handleSearch}
                    />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </div>
              </Stack>
            }
          >
            {filteredData.length > 0 ? (
              <>
                <Table
                  data={filteredData.slice((page - 1) * limit, page * limit)} 
                  bordered
                  cellBordered
                  height={450}
                  wordWrap="break-word"
                  onRowClick={(rowData) => console.log(rowData)}
                >
                  <Column width={70} align="center" fixed>
                    <HeaderCell>No</HeaderCell>
                    <Cell>
                      {(rowData, rowIndex) => {
                        return rowIndex + 1 + limit * (page - 1);
                      }}
                    </Cell>
                  </Column>

                  <Column width={100} align="center">
                    <HeaderCell>Employee ID</HeaderCell>
                    <Cell dataKey="Employee_Id" />
                  </Column>

                  <Column width={200} resizable>
                    <HeaderCell>Name</HeaderCell>
                    <Cell dataKey="Name" />
                  </Column>

                  <Column width={200} resizable>
                    <HeaderCell>Email</HeaderCell>
                    <Cell dataKey="Email" />
                  </Column>

                  <Column width={200} resizable>
                    <HeaderCell>Division</HeaderCell>
                    <Cell dataKey="Division" />
                  </Column>

                  <Column width={200} resizable>
                    <HeaderCell>Department</HeaderCell>
                    <Cell dataKey="Department" />
                  </Column>

                  <Column width={200} resizable>
                    <HeaderCell>Job Title</HeaderCell>
                    <Cell dataKey="Job_title" />
                  </Column>

                  <Column width={150}>
                    <HeaderCell>Is Active?</HeaderCell>
                    <Cell dataKey="Is_Active">
                      {(rowData) => (
                        <span>
                          {rowData.Is_active === 1 ? (
                            <div className="flex items-center">
                              <div className="h-2.5 w-2.5 rounded-full bg-green-500 mr-2"></div>{" "}
                              Active
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <div className="h-2.5 w-2.5 rounded-full bg-red-500 mr-2"></div>{" "}
                              Inactive
                            </div>
                          )}
                        </span>
                      )}
                    </Cell>
                  </Column>

                  <Column width={100} fixed="right" align="center">
                    <HeaderCell>
                      <MoreIcon />
                    </HeaderCell>
                    <ActionCell dataKey="Employee_Id" />
                  </Column>
                </Table>
                <div style={{ padding: 20 }}>
                  <Pagination
                    prev
                    next
                    first
                    last
                    ellipsis
                    boundaryLinks
                    maxButtons={5}
                    size="xs"
                    layout={["total", "-", "limit", "|", "pager", "skip"]}
                    total={filteredData.length} 
                    limitOptions={[10, 30, 50]}
                    limit={limit}
                    activePage={page}
                    onChangePage={handleChangePage}
                    onChangeLimit={handleChangeLimit}
                  />
                </div>
              </>
            ) : (
              <div style={{ padding: 20, textAlign: "center" }}>
                <span>No results found</span>
              </div>
            )}
          </Panel>
        </div>
      </ContainerLayout>
    </>
  );
}

export async function getServerSideProps() {
  const { GetTestApi } = API_User();
  const { Data: users } = await GetTestApi();

  const dataUser = users || [];

  return {
    props: {
      dataUser,
    },
  };
}

export default UserManagement;
