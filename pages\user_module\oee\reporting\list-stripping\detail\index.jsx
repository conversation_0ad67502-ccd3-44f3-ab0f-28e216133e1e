import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, Notification, Loader, SelectPicker, RadioGroup, Radio, InputNumber, FlexboxGrid, Grid, Col } from "rsuite";

import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";
import SearchIcon from "@rsuite/icons/Search";
//import api section
import ApiMachineOeeHeaderStripping from "@/pages/api/oee/machine_oee_stripping/api_machine_oee_stripping";
import ApiMachineOeeDetail from "@/pages/api/oee/machine_oee_detail/api_machine_oee_detail";

export default function DetailReportingStripping() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const toaster = useToaster();

  const [moduleName, setModuleName] = useState("");
  const [dataTableVisible, setDataTableVisible] = useState(false);
  const [reportingListStrippingDetail, setReportingListStrippingDetail] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [addLoading, setAddLoading] = useState(false);
  const [errorsAddForm, setErrorsAddForm] = useState({});

  const [idHeaderData, setIdHeaderData] = useState([]);

  const [sessionAuth, setSessionAuth] = useState(null);

  const [idRouter, setIdRouter] = useState(null);

  const [loading, setLoading] = useState(false);

  const router = useRouter();
  const { Id } = router.query;

  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleShowModal = async () => {
    setShowModal(true);
    setDataTableVisible(true);
    if (Id) {
      await HandleGetOeeDetail(Id);
    }
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = idHeaderData.filter((rowData, i) => {
    const searchFields = ["category_name", "child_category_name", "category_type", "duration_detail", "remarks", "created_date", "created_by", "updated_date", "updated_by", "deleted_date", "deleted_by", "is_active"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const formatDateTimeCustomNoIso = (isoDateString) => {
    if (!isoDateString) return "-";
    const [datePart, timePart] = isoDateString.split("T");
    const [year, month, day] = datePart.split("-");
    const [hours, minutes] = timePart.split(":");

    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  const formatDateTimeCustom = (isoDateString) => {
    if (!isoDateString) return "-";
    const date = new Date(isoDateString);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${day}-${month}-${year} ${hours}:${minutes}`;
  };

  const formatTimeRange = (startTime, endTime, duration) => {
    const [startDate, startTimeOnly] = startTime.split(" ");
    const [endDate, endTimeOnly] = endTime.split(" ");

    if (startDate === endDate) {
      // Same day: Show only the time range
      return `${startDate} ${startTimeOnly} - ${endTimeOnly} (${duration} menit)`;
    }

    // Different days: Include the date for both
    return `${startDate} ${startTimeOnly} - ${endDate} ${endTimeOnly} (${duration} menit)`;
  };

  const totalRowCount = searchKeyword ? filteredData.length : idHeaderData.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push("/");
    } else if (Id) {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("oee/reporting/list-stripping"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      setIdRouter(Id);
      HandleGetAllActiveHeaderStrippingById(Id);
    }
  }, [Id]);

  const HandleGetAllActiveHeaderStrippingById = async (id_oee_header) => {
    try {
      const apiTransactionHeader = ApiMachineOeeHeaderStripping();
      const response = await apiTransactionHeader.GetMachineOeeListStrippingById({ id_oee_header: parseInt(id_oee_header) });
      if (response.status === 200) {
        const data = response.data;
        setReportingListStrippingDetail({
          id_oee_header: data.id_oee_header,
          id_machine_oee: data.id_machine_oee,
          machine_name: data.machine_name,
          start_time: data.start_time,
          end_time: data.end_time,
          duration: data.duration,
          created_date: data.created_date || "-",
          created_by: data.created_by || "-",
          updated_date: data.updated_date,
          updated_by: data.updated_by || "-",
          deleted_date: data.deleted_date || "-",
          deleted_by: data.deleted_by || "-",
          is_active: data.is_active,
        });
        return data;
      } else {
        console.error("Failed to fetch detail data");
        return null;
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
      return null;
    }
  };

  const HandleGetOeeDetail = async (id_oee_header) => {
    try {
      console.log("Fetching OEE Detail for ID:", id_oee_header);
      const api = ApiMachineOeeDetail();
      const response = await api.GetMachineOeeDetailByIdHeader({ id_oee_header: parseInt(id_oee_header) });
      console.log("API Response:", response);

      if (response.status === 200) {
        console.log("Response Data:", response.data);
        // Set the idHeaderData state with the fetched data
        setIdHeaderData(response.data);
      } else {
        console.error("Failed to fetch detail data");
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
    }
  };

  return (
    <div>
      <Head>
        <title>Rincian Laporan</title>
      </Head>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>OEE</Breadcrumb.Item>
                  <Breadcrumb.Item>Reporting</Breadcrumb.Item>
                  <Breadcrumb.Item>List Stripping</Breadcrumb.Item>
                  <Breadcrumb.Item active>Detail</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Rincian Laporan Stripping {idRouter}</h5>
              </Stack>
            }
          />

          {/* Detail Section */}
          <Panel bordered>
            <Form fluid>
              <FlexboxGrid className="mb-4">
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.Group>
                    <Form.ControlLabel>ID Kategori Utama</Form.ControlLabel>
                    <Form.Control name="id_oee_header" value={reportingListStrippingDetail.id_oee_header || "-"} readOnly className="mb-3" />
                  </Form.Group>
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.Group>
                    <Form.ControlLabel>Waktu Stop</Form.ControlLabel>
                    <Form.Control
                      name="time_range"
                      value={
                        reportingListStrippingDetail.start_time && reportingListStrippingDetail.end_time && reportingListStrippingDetail.duration
                          ? formatTimeRange(formatDateTimeCustomNoIso(reportingListStrippingDetail.start_time), formatDateTimeCustomNoIso(reportingListStrippingDetail.end_time), reportingListStrippingDetail.duration)
                          : "-"
                      }
                      readOnly
                      className="mb-3"
                    />
                  </Form.Group>
                </FlexboxGrid.Item>
              </FlexboxGrid>
              <FlexboxGrid className="mb-4"></FlexboxGrid>
              <FlexboxGrid>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.Group>
                    <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                    <Form.Control name="created_date" value={formatDateTimeCustom(reportingListStrippingDetail.created_date) || "-"} readOnly className="mb-3" />
                    <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                    <Form.Control name="updated_date" value={formatDateTimeCustom(reportingListStrippingDetail.updated_date) || "-"} readOnly className="mb-3" />
                  </Form.Group>
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.Group>
                    <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                    <Form.Control name="created_by" value={reportingListStrippingDetail.created_by || "-"} readOnly className="mb-3" />
                    <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                    <Form.Control name="updated_by" value={reportingListStrippingDetail.updated_by || "-"} readOnly className="mb-3" />
                  </Form.Group>
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.Group>
                    <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                    <Form.Control name="deleted_date" value={reportingListStrippingDetail.deleted_date || "-"} readOnly className="mb-3" />
                  </Form.Group>
                </FlexboxGrid.Item>
                <FlexboxGrid.Item as={Col} colspan={24} lg={12} md={12} sm={24} xs={24}>
                  <Form.Group>
                    <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                    <Form.Control name="deleted_by" value={reportingListStrippingDetail.deleted_by || "-"} readOnly className="mb-3" />
                  </Form.Group>
                </FlexboxGrid.Item>
              </FlexboxGrid>
            </Form>
            <Button appearance="primary" onClick={handleShowModal} className="mt-3">
              Lihat Rincian
            </Button>
          </Panel>
          <div>
            <Modal open={dataTableVisible} onClose={() => setDataTableVisible(false)} size="lg">
              <Modal.Header>
                <Stack justifyContent="space-between" alignItems="center">
                  <Modal.Title>Detail Transaksi Mesin</Modal.Title>
                  {/* Pencarian */}
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    ></InputGroup.Addon>
                  </InputGroup>
                </Stack>
              </Modal.Header>
              <Modal.Body>
                {loading ? (
                  <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                    <Loader size="sm" content="Loading..." />
                  </div>
                ) : (
                  <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                    <Column width={70} align="center" fixed>
                      <HeaderCell>No</HeaderCell>
                      <Cell>{(rowData, rowIndex) => rowIndex + 1 + limit * (page - 1)}</Cell>
                    </Column>
                    <Column width={200} sortable fullText>
                      <HeaderCell align="center">Kategori</HeaderCell>
                      <Cell dataKey="category_name" />
                    </Column>
                    <Column width={200} sortable fullText>
                      <HeaderCell align="center">Sub Kategori</HeaderCell>
                      <Cell dataKey="child_category_name" />
                    </Column>
                    <Column width={120} sortable fullText>
                      <HeaderCell align="center">Tipe Kategori</HeaderCell>
                      <Cell>{(rowData) => (rowData.category_type === "U" ? "Unplanned" : rowData.category_type === "P" ? "Planned" : rowData.category_type)}</Cell>
                    </Column>

                    <Column width={120} sortable fullText>
                      <HeaderCell align="center">Durasi (Menit)</HeaderCell>
                      <Cell dataKey="duration_detail" align="center" />
                    </Column>
                    <Column width={200} sortable fullText>
                      <HeaderCell align="center">Info Tambahan</HeaderCell>
                      <Cell dataKey="remarks" />
                    </Column>
                    <Column width={200} sortable fullText>
                      <HeaderCell align="center">Dibuat Tanggal</HeaderCell>
                      <Cell>{(rowData) => formatDateTimeCustom(rowData.created_date)}</Cell>
                    </Column>
                    <Column width={200} sortable fullText>
                      <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                      <Cell dataKey="created_by" />
                    </Column>
                    <Column width={200} sortable fullText>
                      <HeaderCell align="center">Diperbarui Tanggal</HeaderCell>
                      <Cell>{(rowData) => formatDateTimeCustom(rowData.updated_date)}</Cell>
                    </Column>
                    <Column width={200} sortable fullText>
                      <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                      <Cell dataKey="updated_by" />
                    </Column>
                    <Column width={200} sortable fullText>
                      <HeaderCell align="center">Dihapus Tanggal</HeaderCell>
                      <Cell>{(rowData) => formatDateTimeCustom(rowData.deleted_date)}</Cell>
                    </Column>
                    <Column width={200} sortable fullText>
                      <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                      <Cell dataKey="deleted_by" />
                    </Column>
                  </Table>
                )}

                <div style={{ padding: 20 }}>
                  <Pagination
                    prev
                    next
                    first
                    last
                    ellipsis
                    boundaryLinks
                    maxButtons={5}
                    size="xs"
                    layout={["total", "-", "limit", "|", "pager", "skip"]}
                    limitOptions={[10, 30, 50]}
                    total={totalRowCount}
                    limit={limit}
                    activePage={page}
                    onChangePage={setPage}
                    onChangeLimit={handleChangeLimit}
                  />
                </div>
              </Modal.Body>
              <Modal.Footer>
                <Button appearance="primary" onClick={() => setDataTableVisible(false)}>
                  Tutup
                </Button>
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
