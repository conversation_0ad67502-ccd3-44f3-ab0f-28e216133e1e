import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import { DateRangePicker, Stack, Panel, Button, Breadcrumb, SelectPicker, DatePicker,Table, } from "rsuite";

import API_IPC from "@/pages/api/api_ipc";
import XLSX from "xlsx";

import ContainerLayout from "@/components/layout/ContainerLayout";

import ChartDataLabels from "chartjs-plugin-datalabels";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Legend,
  ChartDataLabels
);

import { Line } from "react-chartjs-2";

export default function ReportingMsTms() {
  const router = useRouter();
  const [moduleName, setModuleName] = useState("");
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const { HeaderCell, Cell, Column } = Table;
  // Calculate the date for 30 days ago
  const thirtyDaysAgo = new Date(new Date().setDate(new Date().getDate() - 30));

  // Set the start date to 30 days ago and end date to the current date
  const [startDate, setStartDate] = useState(thirtyDaysAgo);
  const [endDate, setEndDate] = useState(new Date());

  const [stateDashboardData, setStateDashboardData] = useState([])

  const [selectedBatchCode, setSelectedBathCode] = useState("")

  const [stateFilteredDashboardData, setStateFilteredDashboardData] = useState([])
  const [stateFilteredDashboardReasonData, setStateFilteredDashboardReasonData] = useState([])

  const [dateForm, setDateForm] = useState(null)

  const [stateReasonData, setStateReasonData] = useState([])

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    const moduleNameValue = localStorage.getItem("module_name");
    setModuleName(moduleNameValue);

    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("ipc/reporting-ms-tms")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
    }
    setStartDate(thirtyDaysAgo);
    setEndDate(new Date());

    handleFetchData();
  }, []);

  const handleFetchData = async () => {
    setLoading(true);
    const data = await API_IPC().getIpcReportExcel({
      start_date: startDate,
      end_date: endDate,
    });
    console.log("data", data || []);
    setData(data.data || []);
    setLoading(false);

    const ipcDashboard = await API_IPC().getIpcDashboard()
    console.log("ipc data", ipcDashboard)

    setStateDashboardData(ipcDashboard.data || [])    

    const resReason = await API_IPC().getIpcReason()

    setStateReasonData(resReason.data || [])
  };

  const formatDateToShort = (date) => {
    if (!(date instanceof Date)) {
      date = new Date(date);
    }
    let month = "" + (date.getMonth() + 1);
    let day = "" + date.getDate();
    const year = date.getFullYear();

    if (month.length < 2) month = "0" + month;
    if (day.length < 2) day = "0" + day;

    return [year, month, day].join("-");
  };

  const exportFile = (data) => {
    // Map data to a new array
    const formattedData = data.map((item) => ({
      Date: item.formatted_date,
      "TMS Count": item.tms_count,
      "MS Count": item.ms_count,
    }));

    // Generate worksheet from the new array
    const ws = XLSX.utils.json_to_sheet(formattedData);

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");

    const formattedStartDate = formatDateToShort(startDate);
    const formattedEndDate = formatDateToShort(endDate);
    const fileName =
      "reporting-ms-tms" +
      " " +
      formattedStartDate +
      " to " +
      formattedEndDate +
      ".xlsx";

    XLSX.writeFile(wb, fileName);
  };

  const prepareChartData = (data) => {
    const labels = data.map((item) => item.formatted_date);
    const msCounts = data.map((item) => item.ms_count);
    const tmsCounts = data.map((item) => item.tms_count);

    return {
      labels,
      datasets: [
        {
          label: "MS Count",
          data: msCounts,
          backgroundColor: "#0f7141",
        },
        {
          label: "TMS Count",
          data: tmsCounts,
          backgroundColor: "#d32f2f",
        },
      ],
    };
  };

  const LineChart = ({ chartData }) => {
    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: "right",
        },
        title: {
          display: true,
          text: "MS and TMS Counts Over Time",
        },
        datalabels: {
          anchor: "end",
          align: "end",
          color: function (context) {
            return context.dataset.backgroundColor;
          },
          font: {
            weight: "bold",
          },
          formatter: (value, context) => {
            const formattedValue = value.toLocaleString();
            return formattedValue;
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return <Line data={chartData} options={options} />;
  };
  

  useEffect(()=>{
    console.log("dorp down set", stateDashboardData)
  },[stateDashboardData])


  useEffect(()=>{

    const selectedProductCode = selectedBatchCode; // Example selected product code

    const filteredTransactions = stateDashboardData.filter(transaction => transaction.product_code === selectedProductCode);

    const filteredReason = stateReasonData.filter(transaction => transaction.product_code === selectedProductCode);

    setStateFilteredDashboardData(filteredTransactions)
    setStateFilteredDashboardReasonData(filteredReason)
    console.log("filter", filteredTransactions);
  }, [selectedBatchCode])

  const droppedDownData = (data)=>{

    if (dateForm) {
      const filteredData = data.filter(item => {
        const itemDate = new Date(item.created_date);
        return itemDate.toDateString() === dateForm.toDateString();
      });
      // console.log("filtered data", filteredData)
      const uniqueProductCodes = [...new Set(filteredData.map(item => item.product_code))];
      // const uniqueProductCodes = [...new Set(data.map(item => item.product_code))];

      const result = uniqueProductCodes.map(code => ({ label: code, value: code }));

      return result
    }else{
      return []
    }
    
  }


  // AVG_D
  // MAX_D
  // MIN_D
  
  // AVG_H
  // MAX_H
  // MIN_H

  // AVG_T
  // MAX_T
  // MIN_T
  
  
  
  const prepareHardnessChartData = (data) => {
    const labels = data.map((item) => item.info + ' ' + item.status_ipc);
    const avg = data.map((item) => item.AVG_H);
    const max = data.map((item) => item.SPEC_MAX_H);
    const min = data.map((item) => item.SPEC_MIN_H);

    return {
      labels,
      datasets: [
        {
          label: "Avg Hardness",
          data: avg,
          backgroundColor: "#FF0000",
          borderColor: "#FF0000",
        },
        {
          label: "Max Spec Hardness",
          data: max,
          backgroundColor: "#0000FF",
          borderColor: "#0000FF",
        },
        {
          label: "Min Spec Hardness",
          data: min,
          backgroundColor: "#FFA500",
          borderColor: "#FFA500",
        },
      ],
    };
  };

  const LineHardnessChart = ({ chartData }) => {
    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: "right",
        },
        title: {
          display: true,
          text: `${selectedBatchCode} IPC: HARDNESS`,
           padding: { 
              bottom: 50, 
              top: 20,
          },
        },
        datalabels: {
          anchor: "end",
          align: "end",
          color: function (context) {
            return context.dataset.backgroundColor;
          },
          font: {
            weight: "bold",
          },
          formatter: (value, context) => {
            const formattedValue = value.toLocaleString();
            return formattedValue;
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return <Line data={chartData} options={options} />;
  };

  const prepareDiameterChartData = (data) => {
    const labels = data.map((item) => item.info + ' ' + item.status_ipc);
    const avg = data.map((item) => item.AVG_D);
    const max = data.map((item) => item.SPEC_MAX_D);
    const min = data.map((item) => item.SPEC_MIN_D);

    return {
      labels,
      datasets: [
        {
          label: "Avg Diameter",
          data: avg,
          backgroundColor: "#FF0000",
          borderColor: "#FF0000",
        },
        {
          label: "Max Spec Diameter",
          data: max,
          backgroundColor: "#0000FF",
          borderColor: "#0000FF",
        },
        {
          label: "Min Spec Diameter",
          data: min,
          backgroundColor: "#FFA500",
          borderColor: "#FFA500",
        },
      ],
    };
  };

  const LineDiameterChart = ({ chartData }) => {
    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: "right",
        },
        title: {
          display: true,
          text: `${selectedBatchCode} IPC: DIAMETER`,
           padding: { 
              bottom: 50, 
              top: 20,
          },
        },
        datalabels: {
          anchor: "end",
          align: "end",
          color: function (context) {
            return context.dataset.backgroundColor;
          },
          font: {
            weight: "bold",
          },
          formatter: (value, context) => {
            const formattedValue = value.toLocaleString();
            return formattedValue;
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return <Line data={chartData} options={options} />;
  };
  
  const prepareThicknessChartData = (data) => {
    const labels = data.map((item) => item.info + ' ' + item.status_ipc);
    const avg = data.map((item) => item.AVG_T);
    const max = data.map((item) => item.SPEC_MAX_T);
    const min = data.map((item) => item.SPEC_MIN_T);

    return {
      labels,
      datasets: [
        {
          label: "Avg Thickness",
          data: avg,
          backgroundColor: "#FF0000",
          borderColor: "#FF0000",
        },
        {
          label: "Max Spec Thickness",
          data: max,
          backgroundColor: "#0000FF",
          borderColor: "#0000FF",
        },
        {
          label: "Min Spec Thickness",
          data: min,
          backgroundColor: "#FFA500",
          borderColor: "#FFA500",
        },
      ],
    };
  };

  const LineThicknessChart = ({ chartData }) => {
    const options = {
      responsive: true,
      plugins: {
        legend: {
          position: "right",
        },
        title: {
          display: true,
          text: `${selectedBatchCode} IPC: THICKNESS`,
           padding: { 
              bottom: 50, 
              top: 20,
          },
        },
        datalabels: {
          anchor: "end",
          align: "end",
          color: function (context) {
            return context.dataset.backgroundColor;
          },
          font: {
            weight: "bold",
          },
          formatter: (value, context) => {
            const formattedValue = value.toLocaleString();
            return formattedValue;
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };

    return <Line data={chartData} options={options} />;
  };

  // const DateChange = (value) =>{
      
  // }

  return (
    <>
      <div>
        <Head>
          <title>Reporting MS and TMS</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4">
          <Breadcrumb className="mt-2">
            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
            <Breadcrumb.Item active>
              {moduleName ? moduleName : "User Module"} / Reporting MS and TMS
            </Breadcrumb.Item>
          </Breadcrumb>
          <Panel bordered className="mb-2">
            <Stack justifyContent="space-between">
              <h4>Reporting MS and TMS</h4>
            </Stack>
          </Panel>
          <div>
            <Panel bordered>
              <Stack  className="gap-2">
                <Stack.Item>
                <DatePicker
                    value={dateForm}
                    onChange={(value) => {
                      setDateForm(value)
                        // setFilterParams({...filterParams, filter_date:value})
                        console.log(value)
                    }
                    }
                  />
                {/* {errors.id_criteria_reagen && <p style={{ color: 'red' }}>{errors.id_criteria_reagen}</p>} */}
                </Stack.Item>
                <Stack.Item>
                <SelectPicker
                    name="batch code"
                    label="batch code"
                    value={selectedBatchCode}
                    block
                    data={droppedDownData(stateDashboardData)}
                    // valueKey="value"
                    // labelKey="label"
                    onChange={(value) => {
                        setSelectedBathCode(value)
                    }}
                />
                {/* {errors.id_criteria_reagen && <p style={{ color: 'red' }}>{errors.id_criteria_reagen}</p>} */}
                </Stack.Item>
                {/* <Stack.Item>
                  <p>Select date: </p>
                </Stack.Item>
                <Stack.Item>
                  <DateRangePicker
                    appearance="default"
                    value={[startDate, endDate]}
                    onChange={(value) => {
                      if (value === null) {
                        setStartDate(new Date());
                        setEndDate(new Date());
                        setData([]);
                      } else {
                        setStartDate(value?.[0]);
                        setEndDate(value?.[1]);
                      }
                    }}
                  />
                </Stack.Item>
                <Stack.Item>
                  <Button
                    appearance="primary"
                    onClick={() => handleFetchData()}
                  >
                    Fetch Data
                  </Button>
                </Stack.Item> */}
                <Stack.Item>
                  <Button
                    appearance="primary"
                    onClick={() => {
                      router.push(`/user_module/ipc/reporting-ms-tms/PdfPreview?ssr_batch_code=${selectedBatchCode}`)
                      // exportFile(data)
                    }}
                    // disabled={data.length <= 0}
                  >
                    Export to PDF
                  </Button>
                </Stack.Item>
              </Stack>

              {/* <LineChart
                chartData={prepareChartData(data)}
                plugins={[ChartDataLabels]}
                loading={loading}
              /> */}

              <LineHardnessChart
                chartData={prepareHardnessChartData(stateFilteredDashboardData)}
                plugins={[ChartDataLabels]}
                loading={loading}
              />

              {/* <LineDiameterChart
                chartData={prepareDiameterChartData(stateFilteredDashboardData)}
                plugins={[ChartDataLabels]}
                loading={loading}
              /> */}

              {/* <LineThicknessChart
                chartData={prepareThicknessChartData(stateFilteredDashboardData)}
                plugins={[ChartDataLabels]}
                loading={loading}
              /> */}

              <Table
              bordered
              cellBordered
              autoHeight
              data={stateFilteredDashboardReasonData}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 ;
                  }}
                </Cell>
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Product Code</HeaderCell>
                <Cell dataKey="product_code" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Info</HeaderCell>
                <Cell dataKey="info" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Reason</HeaderCell>
                <Cell dataKey="reason" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Operator</HeaderCell>
                <Cell dataKey="operator_created" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Approval by</HeaderCell>
                <Cell dataKey="approve_details" />
              </Column>
            </Table>
            </Panel>
          </div>
        </div>
      </ContainerLayout>
    </>
  );
}
