import { useEffect, useState } from "react";
import XLSX from "xlsx";
import {
  Button,
  IconButton,
  Stack,
  Table,
  Panel,
  Input,
  InputGroup,
  Pagination,
  Modal,
  useToaster,
  DatePicker,
  InputPicker,
} from "rsuite";
import PlusIcon from "@rsuite/icons/Plus";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import Messages from "@/components/Messages";
import dateTimeFormatter from "@/lib/function/date-time-formatter";
import dateFormatterDash from "@/lib/function/date-formatter-dash";

import API_MasterlistCapa from "@/pages/api/e_form/api_eform_masterlist_capa";

export default function CapaTable() {
  const toaster = useToaster();
  const [props, setProps] = useState([]);
  const { HeaderCell, Cell, Column } = Table;

  const [data, setData] = useState([]);

  const [showModal, setShowModal] = useState(false);
  const [mode, setMode] = useState(null);

  const [selectedRow, setSelectedRow] = useState(null);

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const typeData = ["PM", "RM"].map((item) => ({ label: item, value: item }));
  const statusData = ["Open", "Close"].map((item) => ({
    label: item,
    value: item,
  }));

  useEffect(() => {
    const fetchData = async () => {
      const res_data = await API_MasterlistCapa().getAll();
      setData(res_data.data || []);
    };

    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));

    setProps(dataLogin);

    fetchData();
  }, []);

  const emptyFormValue = {
    type: null,
    month: null,
    no_ptpp: null,
    resources: null,
    material_code: null,
    material_name: null,
    suppliers: null,
    ptpp_send_date: null,
    ptpp_answer_deadline: null,
    answer_date: null,
    root_cause: null,
    corrective_action: null,
    preventive_action: null,
    enhancement_target_deadline: null,
    status: null,
    verification_proof: null,
    created_by: null,
    updated_by: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
  };

  const [searchKeyword, setSearchKeyword] = useState("");

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1); // reset the page to 1
  };

  const propertiesToFilter = [
    "id",
    "type",
    "month",
    "no_ptpp",
    "resources",
    "material_code",
    "material_name",
    "suppliers",
    "ptpp_send_date",
    "ptpp_answer_deadline",
    "answer_date",
    "root_cause",
    "corrective_action",
    "preventive_action",
    "enhancement_target_deadline",
    "status",
    "verification_proof",
    "created_at",
    "created_by",
    "updated_at",
    "updated_by",
  ];

  const datas =
    data && data.length > 0
      ? data
          .filter((item) => {
            const str = searchKeyword.toLowerCase();
            return propertiesToFilter
              .map((property) => item[property] || "") // Use empty string as a default for undefined values
              .concat(
                item.month ? dateFormatterDash(item.month).toLowerCase() : ""
              )
              .concat(
                item.ptpp_send_date
                  ? dateFormatterDash(item.ptpp_send_date).toLowerCase()
                  : ""
              )
              .concat(
                item.ptpp_answer_deadline
                  ? dateFormatterDash(item.ptpp_answer_deadline).toLowerCase()
                  : ""
              )
              .concat(
                item.answer_date
                  ? dateFormatterDash(item.answer_date).toLowerCase()
                  : ""
              )
              .concat(
                item.enhancement_target_deadline
                  ? dateFormatterDash(
                      item.enhancement_target_deadline
                    ).toLowerCase()
                  : ""
              )
              .concat(
                item.created_at
                  ? dateTimeFormatter(
                      item.created_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .concat(
                item.updated_at
                  ? dateTimeFormatter(
                      item.updated_at,
                      "id-ID",
                      "seconds"
                    ).toLowerCase()
                  : ""
              )
              .some((val) => val?.toString().toLowerCase().includes(str));
          })
          .sort((a, b) => {
            if (sortColumn && sortType) {
              let x = a[sortColumn];
              let y = b[sortColumn];
              if (typeof x === "string") {
                x = x.charCodeAt();
              }
              if (typeof y === "string") {
                y = y.charCodeAt();
              }
              if (sortType === "asc") {
                return x - y;
              } else {
                return y - x;
              }
            }
            return 0;
          })
          .filter(
            (item, index) => index >= limit * (page - 1) && index < limit * page
          )
      : [];

  const searchData = data
    ? data.filter((item) => {
        const str = searchKeyword.toLowerCase();
        return propertiesToFilter
          .map((property) => item[property] || "") // Use empty string as a default for undefined values
          .some((val) => val.toString().toLowerCase().includes(str));
      })
    : [];

  const handleOpenModal = (mode) => {
    setMode(mode);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setFormValue(emptyFormValue);
    setMode(null);
  };

  const handleRequired = () => {
    let required = [
      "month",
      "ptpp_send_date",
      "ptpp_answer_deadline",
      "answer_date",
    ];
    let error = false;
    required.forEach((item) => {
      if (!formValue[item]) {
        error = true;
      }
    });
    return error;
  };

  const handleActions = async () => {
    if (handleRequired()) {
      toaster.push(Messages("error", "Fill in the required fields!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }

    try {
      let result;
      if (mode === "add") {
        result = await API_MasterlistCapa().add({
          ...formValue,
          created_by: props.employee_id,
        });
      } else if (mode === "edit") {
        result = await API_MasterlistCapa().edit({
          ...formValue,
          id: selectedRow,
          updated_by: props.employee_id,
        });
      }

      const res = await API_MasterlistCapa().getAll();
      setData(res.data);

      // Show the toast message
      toaster.push(
        Messages(
          "success",
          `Success ${
            mode === "add" ? "adding" : "editing"
          } E-Form Masterlist CAPA!`
        ),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );

      handleCloseModal();
    } catch (error) {
      toaster.push(
        Messages("error", `Error: "${error}". Please try again later!`),
        {
          placement: "topEnd",
          duration: 5000,
        }
      );
    }
  };

  const handleExportExcel = (data) => {
    if (!data || data.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }

    const formattedData = data.map((item) => ({
      ID: item.id,
      Type: item.type,
      Date: dateTimeFormatter(item.month, "id-ID"),
      "No. PTPP": item.no_ptpp,
      "No.": item.resources,
      "Material Code": item.material_code,
      "Material Name": item.protocol_title,
      Suppliers: item.suppliers,
      "PTPP Send Date": dateTimeFormatter(item.ptpp_send_date, "id-ID"),
      "PTPP Answer Deadline": dateTimeFormatter(
        item.ptpp_answer_deadline,
        "id-ID"
      ),
      "Answer Date": dateTimeFormatter(item.answer_date, "id-ID"),
      "Root Caused": item.root_cause,
      "Corrective Action": item.corrective_action,
      "Preventive Action": item.preventive_action,
      "Enhancement Deadline": item.enhancement_target_deadline,
      Status: item.status,
      "Verification Proof": item.verification_proof,
      "Created At": dateTimeFormatter(item.created_at, "id-ID", "seconds"),
      "Created By": item.created_by,
      "Updated At": dateTimeFormatter(item.updated_at, "id-ID", "seconds"),
      "Updated By": item.updated_by,
    }));

    const ws = XLSX.utils.json_to_sheet(formattedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");
    const date = dateFormatterDash(new Date());
    const filename = `E-Form Masterlist CAPA ${date}.xlsx`;
    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <Panel
        bordered
        bodyFill
        header={
          <Stack justifyContent="space-between">
            <div className="flex gap-2">
              <IconButton
                icon={<PlusIcon />}
                appearance="primary"
                onClick={() => handleOpenModal("add")}
              >
                Add
              </IconButton>
              <IconButton
                icon={<FileDownloadIcon />}
                appearance="primary"
                onClick={() => handleExportExcel(data)}
              >
                Download (.xlsx)
              </IconButton>
            </div>

            <InputGroup inside>
              <InputGroup.Addon>
                <SearchIcon />
              </InputGroup.Addon>
              <Input
                placeholder="Search"
                value={searchKeyword}
                onChange={handleSearch}
              />
              <InputGroup.Addon
                onClick={() => {
                  setSearchKeyword("");
                  setPage(1);
                }}
                style={{
                  display: searchKeyword ? "block" : "none",
                  color: "red",
                  cursor: "pointer",
                }}
              >
                <CloseOutlineIcon />
              </InputGroup.Addon>
            </InputGroup>
          </Stack>
        }
      >
        <Table
          data={datas}
          bordered
          cellBordered
          autoHeight
          sortColumn={sortColumn}
          sortType={sortType}
          onSortColumn={handleSortColumn}
          onRowClick={(data) => {
            console.log(data);
          }}
        >
          <Column fullText width={60} sortable resizable>
            <HeaderCell>ID</HeaderCell>
            <Cell dataKey="id" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Type</HeaderCell>
            <Cell dataKey="type" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Date</HeaderCell>
            <Cell dataKey="month">
              {(rowData) => {
                return rowData?.month
                  ? dateFormatterDash(rowData?.month, "id-ID")
                  : "-";
              }}
            </Cell>
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>No. PTPP</HeaderCell>
            <Cell dataKey="no_ptpp" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Resources</HeaderCell>
            <Cell dataKey="resources" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Material Code</HeaderCell>
            <Cell dataKey="material_code" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Material Name</HeaderCell>
            <Cell dataKey="material_name" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Suppliers</HeaderCell>
            <Cell dataKey="suppliers" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>PTPP Send Date</HeaderCell>
            <Cell dataKey="ptpp_send_date">
              {(rowData) => {
                return rowData?.ptpp_send_date
                  ? dateFormatterDash(rowData?.ptpp_send_date)
                  : "-";
              }}
            </Cell>
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>PTPP Answer Deadline</HeaderCell>
            <Cell dataKey="ptpp_answer_deadline">
              {(rowData) => {
                return rowData?.ptpp_answer_deadline
                  ? dateFormatterDash(rowData?.ptpp_answer_deadline)
                  : "-";
              }}
            </Cell>
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Answer Date</HeaderCell>
            <Cell dataKey="answer_date">
              {(rowData) => {
                return rowData?.answer_date
                  ? dateFormatterDash(rowData?.answer_date)
                  : "-";
              }}
            </Cell>
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Root Caused</HeaderCell>
            <Cell dataKey="root_cause" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Corrective Action</HeaderCell>
            <Cell dataKey="corrective_action" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Preventive Action</HeaderCell>
            <Cell dataKey="preventive_action" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Enhancement Deadline</HeaderCell>
            <Cell dataKey="enhancement_target_deadline">
              {(rowData) => {
                return rowData?.enhancement_target_deadline
                  ? dateFormatterDash(rowData?.enhancement_target_deadline)
                  : "-";
              }}
            </Cell>
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Status</HeaderCell>
            <Cell dataKey="status" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Verification Proof</HeaderCell>
            <Cell dataKey="verification_proof" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Created At</HeaderCell>
            <Cell dataKey="created_at">
              {(rowData) => {
                return rowData?.created_at
                  ? dateTimeFormatter(rowData?.created_at, "id-ID", "seconds")
                  : "-";
              }}
            </Cell>
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Created By</HeaderCell>
            <Cell dataKey="created_by" />
          </Column>

          <Column fullText width={150} sortable resizable>
            <HeaderCell>Updated At</HeaderCell>
            <Cell dataKey="updated_at">
              {(rowData) => {
                return rowData?.updated_at
                  ? dateTimeFormatter(rowData?.updated_at, "id-ID", "seconds")
                  : "-";
              }}
            </Cell>
          </Column>

          <Column fullText width={150} sortable>
            <HeaderCell>Updated By</HeaderCell>
            <Cell dataKey="updated_by" />
          </Column>

          <Column width={80} fixed="right" align="center">
            <HeaderCell>...</HeaderCell>
            <Cell>
              {(rowData) => {
                function handleEditAction() {
                  setSelectedRow(rowData.id);
                  setFormValue({
                    ...rowData,
                    month: rowData.month ? new Date(rowData.month) : null,
                    ptpp_send_date: rowData.ptpp_send_date
                      ? new Date(rowData.ptpp_send_date)
                      : null,
                    ptpp_answer_deadline: rowData.ptpp_answer_deadline
                      ? new Date(rowData.ptpp_answer_deadline)
                      : null,
                    answer_date: rowData.answer_date
                      ? new Date(rowData.answer_date)
                      : null,
                    enhancement_target_deadline:
                      rowData.enhancement_target_deadline
                        ? new Date(rowData.enhancement_target_deadline)
                        : null,
                  });
                  setShowModal(true);
                  setMode("edit");
                }
                return (
                  <Button
                    onClick={handleEditAction}
                    appearance="link"
                    className="p-0"
                  >
                    Edit
                  </Button>
                );
              }}
            </Cell>
          </Column>
        </Table>
        <div style={{ padding: 20 }}>
          <Pagination
            prev
            next
            first
            last
            ellipsis
            boundaryLinks
            maxButtons={5}
            size="xs"
            layout={["total", "-", "limit", "|", "pager", "skip"]}
            total={searchKeyword ? searchData.length : data.length}
            limitOptions={[10, 30, 50]}
            limit={limit}
            activePage={page}
            onChangePage={setPage}
            onChangeLimit={handleChangeLimit}
          />
        </div>
      </Panel>

      <Modal
        backdrop="static"
        open={showModal}
        overflow={false}
        onClose={() => handleCloseModal()}
      >
        <Modal.Header>
          <Modal.Title>
            {mode === "add" ? "Add" : "Edit"} E-Form Masterlist CAPA
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="flex flex-col gap-3">
          <div className="flex justify-between gap-4 w-full">
            <div className="flex flex-col gap-3 w-full">
              <div>
                <label>Type</label>
                <InputPicker
                  data={typeData}
                  value={formValue.type}
                  onChange={(value) => {
                    setFormValue({ ...formValue, type: value });
                  }}
                  block
                />
              </div>

              <div>
                <label>
                  Date <span className="text-red-500">*</span>
                </label>
                <DatePicker
                  value={formValue.month}
                  onChange={(value) => {
                    setFormValue({ ...formValue, month: value });
                  }}
                  block
                  oneTap
                />
              </div>

              <div>
                <label>No. PTPP</label>
                <Input
                  value={formValue.no_ptpp}
                  onChange={(value) => {
                    setFormValue({ ...formValue, no_ptpp: value });
                  }}
                  maxLength={50}
                />
              </div>

              <div>
                <label>Resources</label>
                <Input
                  value={formValue.resources}
                  onChange={(value) => {
                    setFormValue({ ...formValue, resources: value });
                  }}
                  maxLength={100}
                />
              </div>

              <div>
                <label>Material Code</label>
                <Input
                  value={formValue.material_code}
                  onChange={(value) => {
                    setFormValue({ ...formValue, material_code: value });
                  }}
                  maxLength={100}
                />
              </div>

              <div>
                <label>Material Name</label>
                <Input
                  value={formValue.material_name}
                  onChange={(value) => {
                    setFormValue({ ...formValue, material_name: value });
                  }}
                  as="textarea"
                  maxLength={150}
                />
              </div>

              <div>
                <label>Suppliers</label>
                <Input
                  value={formValue.suppliers}
                  onChange={(value) => {
                    setFormValue({
                      ...formValue,
                      suppliers: value,
                    });
                  }}
                  maxLength={250}
                />
              </div>

              <div>
                <label>
                  PTPP Send Date <span className="text-red-500">*</span>
                </label>
                <DatePicker
                  value={formValue.ptpp_send_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, ptpp_send_date: value });
                  }}
                  block
                  oneTap
                />
              </div>

              <div>
                <label>
                  PTPP Answer Deadline <span className="text-red-500">*</span>
                </label>
                <DatePicker
                  value={formValue.ptpp_answer_deadline}
                  onChange={(value) => {
                    setFormValue({ ...formValue, ptpp_answer_deadline: value });
                  }}
                  block
                  oneTap
                />
              </div>
            </div>

            <div className="flex flex-col gap-3 w-full">
              <div>
                <label>
                  Answer Date <span className="text-red-500">*</span>
                </label>
                <DatePicker
                  value={formValue.answer_date}
                  onChange={(value) => {
                    setFormValue({ ...formValue, answer_date: value });
                  }}
                  block
                  oneTap
                />
              </div>

              <div>
                <label>Root Caused</label>
                <Input
                  value={formValue.root_cause}
                  onChange={(value) => {
                    setFormValue({
                      ...formValue,
                      root_cause: value,
                    });
                  }}
                  as="textarea"
                />
              </div>

              <div>
                <label>Corrective Action</label>
                <Input
                  value={formValue.corrective_action}
                  onChange={(value) => {
                    setFormValue({ ...formValue, corrective_action: value });
                  }}
                  as="textarea"
                />
              </div>

              <div>
                <label>Preventive Action</label>
                <Input
                  value={formValue.preventive_action}
                  onChange={(value) => {
                    setFormValue({ ...formValue, preventive_action: value });
                  }}
                  as="textarea"
                />
              </div>

              <div>
                <label>
                  Enhancement Deadline <span className="text-red-500">*</span>
                </label>
                <DatePicker
                  value={formValue.enhancement_target_deadline}
                  onChange={(value) => {
                    setFormValue({
                      ...formValue,
                      enhancement_target_deadline: value,
                    });
                  }}
                  block
                  oneTap
                />
              </div>

              <div>
                <label>Status</label>
                <InputPicker
                  data={statusData}
                  value={formValue.status}
                  onChange={(value) => {
                    setFormValue({ ...formValue, status: value });
                  }}
                  block
                />
              </div>

              <div>
                <label>Verification Proof</label>
                <Input
                  value={formValue.verification_proof}
                  onChange={(value) => {
                    setFormValue({ ...formValue, verification_proof: value });
                  }}
                  as="textarea"
                  maxLength={250}
                />
                {/* char counter */}
                <div className="flex justify-end">
                  <span className="text-xs text-gray-400">
                    {formValue.verification_proof?.length || 0}/250
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <span className="text-red-500">* Required</span>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            onClick={() => {
              handleCloseModal();
            }}
            appearance="subtle"
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              handleActions();
            }}
            appearance="primary"
          >
            {mode === "add" ? "Add" : "Edit"}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}
