import { useEffect, useState } from "react";
import Head from "next/head";
import XLSX from "xlsx";
import {
  Icon<PERSON><PERSON>on,
  Stack,
  Breadcrumb,
  Tag,
  Panel,
  Table,
  InputGroup,
  Input,
  Pagination,
  Button,
  Form,
  useToaster,
  Modal,
  ButtonGroup,
  SelectPicker,
  DatePicker
} from "rsuite";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import Messages from "@/components/Messages";
import dateFormatterDash from "@/lib/function/date-formatter-dash";
import API_MasterDataMasterDataReagenType from "@/pages/api/master_data/api_masterdata_master_data_reagen_type";
import { useRouter } from "next/router";
import ipcApi from "@/pages/api/ipcApi";

export default function MasterDataMasterDataReagenTypePage() {
  const [moduleName, setModuleName] = useState("");
  const [props, setProps] = useState([]);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const { HeaderCell, Cell, Column } = Table;

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [formErrors, setFormErrors] = useState({});

  const toaster = useToaster();
  const router = useRouter();
  const {
    GetReportingMeasurement,
    GetAllReportingMeasurement,
    GetProductCodeForEntry,
  } = ipcApi();

  const [selectedDate, setSelectedDate] = useState(null);
  const [reportData, setReportData] = useState([]);
  const [fixReportData, setFixReportData] = useState([]);
  const [productCodes, setProductCodes] = useState([]);
  const [batchCodes, setBatchCodes] = useState([]);
  const [filterParams, setFilterParams] = useState({
    product_code:null,
    batch_code:null,
    filter_date:null
  })


  const GetReportingData = async (dateData) => {
    const reqData = {
      date: dateData,
    };

    const { Data } = await GetReportingMeasurement(reqData);

    if (Data) {
      const dateFormattedData = Data.map((item) => {
        const initialApproveDate = item.Approve_Date;
        const initialCreatedDate = item.Created_Date;
        // const formattedApproveDate = initialApproveDate.split("T")[0];
        // const formattedCreatedDate = initialCreatedDate.split("T")[0];
        const formattedApproveDate = initialApproveDate
        const formattedCreatedDate = initialCreatedDate
        return {
          ...item,
          Approve_Date: formattedApproveDate,
          Created_Date: formattedCreatedDate,
        };
      });
      const distinctProductCodes = [...new Set(dateFormattedData.map(transaction => transaction.Product_Code))]
            .map(code => ({ label: code, value: code }));
      //   const distinctBatchCodes = [...new Set(dateFormattedData.map(transaction => transaction.Batch_code))]
      //       .map(code => ({ label: code, value: code }));
    
      // setProductCodes(distinctProductCodes);
      setBatchCodes(distinctProductCodes);
      setReportData(dateFormattedData);
      setFixReportData(dateFormattedData);
    } else {
      // Jika data tidak ada
      setReportData([]);
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "NO DATA FOUND  !",
      });
    }
  };

  const GetAllReportingData = async () => {


    const { Data } = await GetAllReportingMeasurement();    

    

    if (Data) {
      const dateFormattedData = Data.map((item) => {
        const initialApproveDate = item.Approve_Date;
        const initialCreatedDate = item.Created_Date;
        const formattedApproveDate = initialApproveDate.split("T")[0];
        const formattedCreatedDate = initialCreatedDate.split("T")[0];
        return {
          ...item,
          Approve_Date: formattedApproveDate,
          Created_Date: formattedCreatedDate,
        };
      });
      const distinctProductCodes = [...new Set(dateFormattedData.map(transaction => transaction.Product_Code))]
      .map(code => ({ label: code, value: code }));
        // const distinctBatchCodes = [...new Set(dateFormattedData.map(transaction => transaction.Batch_code))]
        //     .map(code => ({ label: code, value: code }));

        // setProductCodes(distinctProductCodes);
        setBatchCodes(distinctProductCodes);
        setReportData(dateFormattedData);
        setFixReportData(dateFormattedData);
    } else {
      // Jika data tidak ada
      setReportData([]);
      MySwal.fire({
        icon: "error",
        title: "Oops...",
        text: "NO DATA FOUND  !",
      });
    }
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const handleSubmit = (apiFunction) => {
    if (!formValue.type_desc) {
      setFormErrors({ type_desc: "Reagen Type Description is required." });
      return;
    }
    setFormErrors({});
    apiFunction();
  };


  const emptyFormValue = {
    type_desc: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  const filteredData = reportData
    .filter((rowData, i) => {
      const searchFields = [
        'Id_Transaction_H', 
        'Approve_By',       
        'Approve_Date',     
        'Amount_MS',        
        'Amount_TMS',       
        'Created_Date',     
        'Product_Code',     
        'Employee_Name',    
        'Start_Date',       
        'End_Date',         
        'Time_differ',      
        'Batch_code',       
      ];

      const matchesSearch = searchFields.some((field) =>
        rowData[field]
          ?.toString()
          .toLowerCase()
          .includes(searchKeyword.toLowerCase())
      );

      return matchesSearch;
    })

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  }

  const totalRowCount = searchKeyword
    ? filteredData.length
    : reportData.length;

  const getData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };



  useEffect(() => {

    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("ipc/ReportingMeasurement")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      GetAllReportingData();
      GetAllActiveProductCode()
    }
    GetAllReportingData();
    GetAllActiveProductCode()
  }, []);

  const GetAllActiveProductCode = async ()=>{
    const {Data} = await GetProductCodeForEntry();
    if (Data) {

        console.log("produc code s" ,Data)

        const distinctProductCodes = Data.map(code => ({ label: code.Product_Code, value: code.Product_Code }))

        console.log(distinctProductCodes)
        // const distinctProductCodes = [...new Set(dateFormattedData.map(transaction => transaction.Product_Code))]
        //       .map(code => ({ label: code, value: code }));
      setProductCodes(distinctProductCodes);
    }
  }

  const formatDateToDDMMYYYY = (dateStr) => {
    const date = new Date(dateStr);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
};

  useEffect(()=>{
    // const filteredData = reportData.filter(item => {
    //     const isProductCodeMatch = !filterParams.product_code || item.Product_Code === filterParams.product_code;
    //     const isBatchCodeMatch = !filterParams.batch_code || item.Batch_code === filterParams.batch_code;
    //     const isDateMatch = !filterParams.filter_date || new Date(item.Created_Date).getTime() === new Date(filterParams.filter_date).getTime();
    
    //     return isProductCodeMatch && isBatchCodeMatch && isDateMatch;
    // });
  
    
    // Format filter date
    const formattedFilterDate = filterParams.filter_date ? formatDateToDDMMYYYY(filterParams.filter_date) : null;
    
    // Filter function
    const filteredData = reportData.filter(item => {
        const isProductCodeMatch = !filterParams.product_code || item.Product_Code.includes(filterParams.product_code);
        const isBatchCodeMatch = !filterParams.batch_code || item.Product_Code === filterParams.batch_code;
        const formattedItemDate = item.Created_Date.split(' ')[0]; // Use only the date part in "dd-mm-yyyy" format
        const isDateMatch = !formattedFilterDate || formattedItemDate === formattedFilterDate;
    
        return isProductCodeMatch && isBatchCodeMatch && isDateMatch;
    });

    if (filterParams.batch_code || filterParams.product_code || filterParams.filter_date) {
      setReportData(filteredData)  
    }else{
      setReportData(fixReportData)
    }
    
  },[filterParams])
  

  const viewHandler = async (reqData) => {
    // router.push({
    //   pathname: "/user_module/ipc/ReportingMeasurement/PdfPreview",
    //   query: {
    //     id_transaction_h: parseInt(reqData),
    //   },
    // });
    // const url = `${
    //   process.env.NEXT_PUBLIC_PIMS_FE
    // }/user_module/ipc/ReportingMeasurement/PdfPreview?idTransactionH=${parseInt(
    //   reqData
    // )}`;
    const url = `${
      process.env.NEXT_PUBLIC_PIMS_FE
    }/user_module/ipc/ReportingMeasurement/pdf?idTransactionH=${parseInt(
      reqData
    )}`;
    window.open(url, "_blank");
  };

  return (
    <>
      <div>
        <Head>
          <title>Reporting Measurement</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item active>Report Measurement</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Report  Measurement</h5>
              </Stack>}>
          </Panel>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                <SelectPicker
                    name="product code"
                    label="product Code"
                    value={filterParams.product_code}
                    block
                    data={productCodes}
                    valueKey="value"
                    labelKey="label"
                    onChange={(value) => {
                        setFilterParams({...filterParams, product_code:value})
                    }}
                />
                <br />
                <SelectPicker
                    name="batch code"
                    label="Batch Code"
                    value={filterParams.batch_code}
                    block
                    data={batchCodes}
                    valueKey="value"
                    labelKey="label"
                    onChange={(value) => {
                        setFilterParams({...filterParams, batch_code:value})
                    }}
                />
                <br/>
                <DatePicker
                    value={filterParams.filter_date}
                    onChange={(value) => {
                        setFilterParams({...filterParams, filter_date:value})
                    }
                    }
                  />
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              <Column width={70} align="center" sortable>
                <HeaderCell>ID</HeaderCell>
                <Cell dataKey="Id_Transaction_H" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Product Code</HeaderCell>
                <Cell dataKey="Product_Code" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Batch Code</HeaderCell>
                <Cell dataKey="Batch_code" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Step</HeaderCell>
                <Cell dataKey="Step" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Station</HeaderCell>
                <Cell dataKey="Station" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Amount MS Date</HeaderCell>
                <Cell dataKey="Amount_MS" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Amount TMS Date</HeaderCell>
                <Cell dataKey="Amount_TMS" />
              </Column>              
              <Column width={150} sortable resizable>
                <HeaderCell>Created Date</HeaderCell>
                <Cell dataKey="Start_Date" />
              </Column>
              {/* <Column width={150} sortable resizable>
                <HeaderCell>Approved Date</HeaderCell>
                <Cell dataKey="Approve_Date" />
              </Column> */}
              <Column width={150} sortable resizable>
                <HeaderCell>Operator Name</HeaderCell>
                <Cell dataKey="Employee_Name" />
              </Column>
              <Column width={100} fixed="right" align="center">
                <HeaderCell>Action</HeaderCell>
                <Cell style={{ padding: "8px" }}>
                  {(rowData) => (
                    <div>
                      <Button
                        appearance="link"
                        disabled={rowData.is_active === 0}
                        onClick={() => {
                            viewHandler(rowData.Id_Transaction_H)
                        }}
                      >
                        Download PDF
                      </Button>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>
    </>
  );
}
