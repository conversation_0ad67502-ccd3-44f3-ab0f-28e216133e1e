import NextAuth, { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import UseTestApi from "../userApi";

const { UserLoginApi } = UseTestApi();

const authOptions = {
  session: {
    strategy: "jwt",
  },
  providers: [
    CredentialsProvider({
      name: "credentials",
      type: "credentials",
      credentials: {},
      async authorize(credentials) {
        const { employeeId, password } = credentials;

        const login = await User<PERSON>ogin<PERSON><PERSON>({
          employeeId: employeeId,
          password: password,
        });

        if (login.Employee_Id) {
          const user = {
            emp_id: login.Employee_Id,
            emp_name: login.Name,
            emp_modules: login.Module_Code,
          };
          return user;
        } else {
          return null;
        }
      },
    }),
  ],
  callbacks: {
    session({ session, token }) {
      return {
        ...session,
        user: {
          ...session.user,
          emp_id: token.emp_id,
          emp_name: token.emp_name,
          emp_modules: token.emp_modules,
        },
      };
    },
    jwt({ token, user }) {
      if (user) {
        return {
          ...token,
          emp_id: user.emp_id,
          emp_name: user.emp_name,
          emp_modules: user.emp_modules,
        };
      }
      return token;
    },
  },
  pages: { signIn: "/index.js" },
};

export default NextAuth(authOptions);
