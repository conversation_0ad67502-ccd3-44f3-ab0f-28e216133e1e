import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/ipc/${url}`,
        data
    )
        .then((res) => { return res.data })
        .catch((err) => { return err.response.data });
    return response;
};


export default function APIIpcProductMethod(){
    return{
        getAllDetailProductMethod: createApiFunction("post", "ipc_ma/method/get-all"),
        getDetailProductMethod: createApiFunction("post", "ipc_ma/method/get-detail"),
        createProductMethod: createApiFunction("post", "ipc_ma/method/create"),
        updateProductMethod: createApiFunction("post", "ipc_ma/method/update"),
        deleteProductMethod: createApiFunction("post", "ipc_ma/method/delete"),
    }
}