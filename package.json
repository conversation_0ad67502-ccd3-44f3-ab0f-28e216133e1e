{"name": "system-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@mui/material": "^5.13.5", "@react-pdf-viewer/core": "^3.12.0", "axios": "^1.4.0", "bootstrap": "^5.3.0", "chart.js": "^4.4.1", "chartjs-plugin-datalabels": "^2.2.0", "file-saver": "^2.0.5", "html-to-pdfmake": "^2.5.12", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.31", "next": "13.4.4", "next-auth": "^4.22.1", "pdfjs-dist": "^3.4.120", "pdfkit-table": "^0.1.99", "pdfmake": "^0.2.10", "react": "18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "18.2.0", "react-icons": "^4.10.1", "react-jsx-parser": "^1.29.0", "react-quill": "^2.0.0", "rsuite": "^5.36.0", "sweetalert2": "^11.7.20", "sweetalert2-react-content": "^5.0.7", "uuid": "^9.0.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.0/xlsx-0.20.0.tgz"}, "devDependencies": {"autoprefixer": "^10.4.15", "postcss": "^8.4.28", "tailwindcss": "^3.3.3"}}