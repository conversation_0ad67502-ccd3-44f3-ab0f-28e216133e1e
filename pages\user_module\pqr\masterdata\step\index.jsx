import { useEffect, useState } from "react";
import Head from "next/head";
import {
  <PERSON>readcrumb,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Panel,
  Stack,
  Table,
  Tag,
  Button,
  Modal,
  Form,
  useToaster,
  ButtonGroup,
  Loader,
  DatePicker,
  RadioGroup,
  Radio,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import ApiStep from "@/pages/api/pqr/step/api_masterdata_step";
import { useRouter } from "next/router";

export default function MasterdataStep() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_step");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [stepsDataState, setStepsDataState] = useState([]);

  const emptyAddStepForm = {
    step_name: null,
    is_active: 1,
  };

  const emptyEditStepForm = {
    step_name: null,
    is_active: 1,
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [addStepForm, setAddStepForm] = useState(emptyAddStepForm);
  const [editStepForm, setEditStepForm] = useState(emptyEditStepForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = stepsDataState.filter((rowData) => {
    const searchFields = ["id_step", "step_name", "is_active"];
    const matchesSearch = searchFields.some((field) =>
      rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase())
    );
    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : stepsDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);

    if (!dataLogin) {
      router.push("/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("pqr/")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }

      HandleGetAllStepApi();
    }
  }, []);

  const HandleGetAllStepApi = async () => {
    try {
      const res = await ApiStep().getAllStep();
      if (res.status === 200) {
        setStepsDataState(res.data);
      } else {
        console.log("Error on GetAllStepApi: ", res.message);
      }
    } catch (error) {
      console.log("Error on catch GetAllStepApi: ", error.message);
    }
  };

  const HandleAddStepApi = async () => {
    const errors = {};
    if (!addStepForm.step_name) {
      errors.step_name = "Nama Step Wajib Diisi!";
    }

    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }

    setLoading(true);
    try {
      const res = await ApiStep().createStep({
        ...addStepForm,
        create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        setAddStepForm(emptyAddStepForm);
        setShowAddModal(false);
        HandleGetAllStepApi();
      } else {
        console.log("Error on AddStepApi: ", res.message);
      }
    } catch (error) {
      console.log("Error on AddStepApi: ", error.message);
    } finally {
      setLoading(false);
    }
  };

  const HandleEditStepApi = async () => {
    const errors = {};
    if (!editStepForm.step_name) {
      errors.step_name = "Nama Step Wajib Diisi!";
    }

    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);
      return;
    }

    try {
      const res = await ApiStep().editStep({
        ...editStepForm,
        update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        HandleGetAllStepApi();
        setShowEditModal(false);
      } else {
        console.log("Error on EditStepApi: ", res.message);
      }
    } catch (error) {
      console.log("Error on EditStepApi: ", error.message);
    }
  };

  const handleEditStatusStepApi = async (id_step, is_active) => {
    try {
      const res = await ApiStep().editStatusStep({
        id_step,
        is_active,
        delete_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        HandleGetAllStepApi();
      } else {
        console.log("Error on update status: ", res.message);
      }
    } catch (error) {
      console.log("Error on update status: ", error.message);
    }
  };

  return (
    <div>
      <div>
        <Head>
          <title>Master Data Step</title>
        </Head>
      </div>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>E Release</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>Halaman Step</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>
          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Halaman Step</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>
                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >

              <Table
                bordered
                cellBordered
                height={400}
                data={getPaginatedData(getFilteredData(), limit, page)}
                sortColumn={sortColumn}
                sortType={sortType}
                onSortColumn={handleSortColumn}
                autoHeight
              >
                <Column width={120} align="center" sortable fullText resizable>
                  <HeaderCell>ID Step</HeaderCell>
                  <Cell dataKey="id_step" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Nama Step</HeaderCell>
                  <Cell dataKey="step_name" />
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Pembuatan</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dibuat oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                </Column>
                <Column width={150} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>{(rowData) => (rowData.delete_date ? new Date(rowData.delete_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.delete_by}</>}</Cell>
                </Column>
                <Column width={100} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => (
                      <div>
                        <Button
                          appearance="subtle"
                          disabled={rowData.is_active === 0}
                          onClick={() => {
                            setShowEditModal(true);
                            setEditStepForm({
                              ...editStepForm,
                              step_name: rowData.step_name,
                              id_step: rowData.id_step,
                              update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                            });
                          }}
                        >
                          <EditIcon />
                        </Button>
                        <Button appearance="subtle" onClick={() => handleEditStatusStepApi(rowData.id_step, rowData.is_active)}>
                          {rowData.is_active === 1 ? <TrashIcon style={{ fontSize: "16px" }} /> : <ReloadIcon style={{ fontSize: "16px" }} />}
                        </Button>
                      </div>
                    )}
                  </Cell>
                </Column>
              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>

            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                setShowAddModal(false);
                setAddStepForm(emptyAddStepForm);
                setErrorsAddForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Tambahkan Step</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama Step</Form.ControlLabel>
                    <Form.Control
                      name="step_name"
                      value={addStepForm.step_name}
                      onChange={(value) => {
                        setAddStepForm((prevFormValue) => ({
                          ...prevFormValue,
                          step_name: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          step_name: undefined,
                        }));
                      }}
                    />
                    {errorsAddForm.step_name && <p style={{ color: "red" }}>{errorsAddForm.step_name}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddStepForm(emptyAddStepForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleAddStepApi();
                  }}
                  appearance="primary"
                  type="submit"
                  loading={loading} // Menampilkan loading state pada tombol
                  disabled={loading} // Menonaktifkan tombol saat loading
                >
                  Tambah
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Adding Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditStepForm(emptyEditStepForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Ubah Step</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama Step</Form.ControlLabel>
                    <Form.Control
                      name="step_name"
                      value={editStepForm.step_name}
                      onChange={(value) => {
                        setEditStepForm((prevFormValue) => ({
                          ...prevFormValue,
                          step_name: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          step_name: undefined,
                        }));
                      }}
                    />
                    {errorsEditForm.step_name && <p style={{ color: "red" }}>{errorsEditForm.step_name}</p>}
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditStepForm(emptyEditStepForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                >
                  Batal
                </Button>
                <Button
                  onClick={() => {
                    HandleEditStepApi();
                  }}
                  appearance="primary"
                  type="submit"
                >
                  Simpan
                </Button>
                {loading && <Loader backdrop size="md" vertical content="Editing Data..." active={loading} />}
              </Modal.Footer>
            </Modal>
          </div>
        </div>
      </ContainerLayout>
    </div>
  );

}