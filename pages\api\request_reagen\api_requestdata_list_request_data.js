import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
    const response = await axios[method](
        `${process.env.NEXT_PUBLIC_REAGEN}/v2/reagen_request/master_reagen_request/${url}`,
        data
    )
        .then((res) => {return res.data})
        .catch((err) => {return err.response.data});
    return response;
};

export default function MasterDataMasterDataReagenApi(){
    return{
        add: createApiFunction("post", "create"),
        getAll: createApiFunction("get", "get/all"),
        getById: createApiFunction("get", "get/id"),
        editStatus: createApiFunction("put", "edit/status"),
        edit: createApiFunction("post", "edit"),
        getDetail: createApiFunction("post", "get/id/detail"),


        getAllDetail: createApiFunction("get", "get/detail"),
        addDetail: createApiFunction("post", "create/request"),
        editDetail: createApiFunction("post", "edit/detail"),
        editDetailItemCode: createApiFunction("post", "edit/detail/item-code"),
        getRequestStatus: createApiFunction("get", "get/status"),
        editRaise: createApiFunction("post", "edit/raise"),
    }
}