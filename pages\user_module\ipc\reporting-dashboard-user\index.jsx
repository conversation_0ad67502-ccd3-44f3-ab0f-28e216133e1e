import { useEffect, useState } from "react";
import Head from "next/head";
import XLSX from "xlsx";
import {
  I<PERSON><PERSON>utton,
  Stack,
  Breadcrumb,
  Tag,
  Panel,
  Table,
  InputGroup,
  Input,
  Pagination,
  Button,
  Form,
  useToaster,
  Modal,
  ButtonGroup,
  SelectPicker,
  DateRangePicker
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import ipcApi from "@/pages/api/ipcApi";
import IPC_API from "@/pages/api/api_ipc";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import Messages from "@/components/Messages";
import dateTimeFormatter from "@/lib/function/date-time-formatter";
import dateFormatterDash from "@/lib/function/date-formatter-dash";

export default function MasterDataMasterDataReagenTypePage() {
  const [moduleName, setModuleName] = useState("");
  const [props, setProps] = useState([]);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();

  const { HeaderCell, Cell, Column } = Table;

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [formErrors, setFormErrors] = useState({});

  const toaster = useToaster();
  const router = useRouter();
  const {
    GetProductCodeForEntry,
  } = ipcApi();

  const [selectedDate, setSelectedDate] = useState(null);
  const [reportData, setReportData] = useState([]);
  const [fixReportData, setFixReportData] = useState([]);
  const [productCodes, setProductCodes] = useState([]);
  const [dashboardData, setDashboradData] = useState([])
  const [filterParams, setFilterParams] = useState({
    product_code:null,
    batch_code:null,
    filter_date:null
  })

  const thirtyDaysAgo = new Date(new Date().setDate(new Date().getDate() - 30));

  // Set the start date to 30 days ago and end date to the current date
  const [startDate, setStartDate] = useState(thirtyDaysAgo);
  const [endDate, setEndDate] = useState(new Date());

  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const getDashboardUser = async ()=>{
    try {
        const res = await IPC_API().getIpcUserDashboard({
            start_time: formatDate(startDate),
            end_time: formatDate(endDate)
        })

        if (res.status === 200) {
            console.log("res", res)
            setDashboradData(res.data || [])
        }else{
            console.log("err", res.message)
        }
        
    } catch (error) {
        console.log(error)
    }
  }

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };


  const emptyFormValue = {
    type_desc: null,
  };

  const [formValue, setFormValue] = useState(emptyFormValue);

  
  const filteredData = dashboardData
    .filter((rowData, i) => {
      const searchFields = [
            "name",
            "created_date2",
            "created_time2",
            "step",
            "station",
            "product_code",
            "hardness",
            "thickness",
            "status",     
      ];

      const matchesSearch = searchFields.some((field) =>
        rowData[field]
          ?.toString()
          .toLowerCase()
          .includes(searchKeyword.toLowerCase())
      );

      return matchesSearch;
    })

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  }

  const totalRowCount = searchKeyword
    ? filteredData.length
    : reportData.length;

  const getData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };



  useEffect(() => {

    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setProps(dataLogin);

    
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
        item.includes("ipc/reporting-dashboard-user")
      );

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      getDashboardUser()
    }
    
  }, []);


  const formatDateToDDMMYYYY = (dateStr) => {
    const date = new Date(dateStr);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
};

  useEffect(()=>{
    // const filteredData = reportData.filter(item => {
    //     const isProductCodeMatch = !filterParams.product_code || item.Product_Code === filterParams.product_code;
    //     const isBatchCodeMatch = !filterParams.batch_code || item.Batch_code === filterParams.batch_code;
    //     const isDateMatch = !filterParams.filter_date || new Date(item.Created_Date).getTime() === new Date(filterParams.filter_date).getTime();
    
    //     return isProductCodeMatch && isBatchCodeMatch && isDateMatch;
    // });
  
    
    // Format filter date
    const formattedFilterDate = filterParams.filter_date ? formatDateToDDMMYYYY(filterParams.filter_date) : null;
    
    // Filter function
    const filteredData = reportData.filter(item => {
        const isProductCodeMatch = !filterParams.product_code || item.Product_Code.includes(filterParams.product_code);
        const isBatchCodeMatch = !filterParams.batch_code || item.Product_Code === filterParams.batch_code;
        const formattedItemDate = item.Created_Date.split(' ')[0]; // Use only the date part in "dd-mm-yyyy" format
        const isDateMatch = !formattedFilterDate || formattedItemDate === formattedFilterDate;
    
        return isProductCodeMatch && isBatchCodeMatch && isDateMatch;
    });

    if (filterParams.batch_code || filterParams.product_code || filterParams.filter_date) {
      setReportData(filteredData)  
    }else{
      setReportData(fixReportData)
    }
    
  },[filterParams])
  

  const viewHandler = async (reqData) => {
    // router.push({
    //   pathname: "/user_module/ipc/ReportingMeasurement/PdfPreview",
    //   query: {
    //     id_transaction_h: parseInt(reqData),
    //   },
    // });
    // const url = `${
    //   process.env.NEXT_PUBLIC_PIMS_FE
    // }/user_module/ipc/ReportingMeasurement/PdfPreview?idTransactionH=${parseInt(
    //   reqData
    // )}`;
    const url = `${
      process.env.NEXT_PUBLIC_PIMS_FE
    }/user_module/ipc/ReportingMeasurement/pdf?idTransactionH=${parseInt(
      reqData
    )}`;
    window.open(url, "_blank");
  };


  useEffect(()=>{
    getDashboardUser()
  },[startDate, endDate])


  const handleExportExcel = () => {
    if (!dashboardData || dashboardData.length === 0) {
      toaster.push(Messages("error", "No data to export!"), {
        placement: "topEnd",
        duration: 5000,
      });
      return;
    }


    const ws = XLSX.utils.json_to_sheet(dashboardData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "data");
    const date = dateFormatterDash(new Date());
    const filename = `Report IPC ${date}.xlsx`;
    XLSX.writeFile(wb, filename);
  };

  return (
    <>
      <div>
        <Head>
          <title>Reporting Measurement</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item active>Report Measurement</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Report  Measurement</h5>
              </Stack>}>
          </Panel>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between">
                <div className="flex gap-2">
                <br />
                <br/>
                <DateRangePicker
                    value={[
                      startDate || "",
                      endDate || "",
                    ]}
                    onChange={(value) => {
                        setStartDate(value?.[0] || "") 
                        setEndDate(value?.[1] || "")
                    }}
                    block
                  />
                   <IconButton
                icon={<FileDownloadIcon />}
                appearance="primary"
                onClick={() => handleExportExcel()}
              >
                Download (.xlsx)
              </IconButton>
                </div>

                <InputGroup inside>
                  <InputGroup.Addon>
                    <SearchIcon />
                  </InputGroup.Addon>
                  <Input
                    placeholder="search"
                    value={searchKeyword}
                    onChange={handleSearch}
                  />
                  <InputGroup.Addon
                    onClick={() => {
                      setSearchKeyword("");
                      setPage(1);
                    }}
                    style={{
                      display: searchKeyword ? "block" : "none",
                      color: "red",
                      cursor: "pointer",
                    }}
                  >
                    <CloseOutlineIcon />
                  </InputGroup.Addon>
                </InputGroup>
              </Stack>
            }
          >
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(getData(), limit, page)}
              sortColumn={sortColumn}
              sortType={sortType}
              onSortColumn={handleSortColumn}
              wordWrap
            >
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => {
                    return rowIndex + 1 + limit * (page - 1);
                  }}
                </Cell>
              </Column>
              {/* "name": "Muhamad Irsyad Rafi",
            "created_date2": "2024-07-15",
            "created_time2": "01:39",
            "step": "AW",
            "station": "ST1",
            "product_code": "STMXGF00003",
            "hardness": "11",
            "thickness": "5.5",
            "status": "MS" */}
              <Column width={200} align="center" sortable>
                <HeaderCell>Name</HeaderCell>
                <Cell dataKey="name" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Start Date </HeaderCell>
                <Cell dataKey="created_date2" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Start Time</HeaderCell>
                <Cell dataKey="created_time2" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>Step</HeaderCell>
                <Cell dataKey="step" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>product_code</HeaderCell>
                <Cell dataKey="product_code" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>hardness</HeaderCell>
                <Cell dataKey="hardness" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>thickness</HeaderCell>
                <Cell dataKey="thickness" />
              </Column>
              <Column width={150} sortable resizable>
                <HeaderCell>status</HeaderCell>
                <Cell dataKey="status" />
              </Column>
            </Table>
            <div style={{ padding: 20 }}>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={handleChangeLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>
    </>
  );
}
