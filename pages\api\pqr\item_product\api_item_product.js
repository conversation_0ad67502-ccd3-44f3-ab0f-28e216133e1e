import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiItemProduct() {
  return {
    getAllItemProduct: createApiFunction("get", "pqr/masterdata_item_product/list"),
    getAllActiveItemProduct: createApiFunction("get", "pqr/masterdata_item_product/list-active"),
    createItemProduct: createApiFunction("post", "pqr/masterdata_item_product/create"),
    getItemProductById: createApiFunction("post", "pqr/masterdata_item_product/id"),
    editItemProduct: createApiFunction("put", "pqr/masterdata_item_product/edit"),
    editStatusItemProduct: createApiFunction("put", "pqr/masterdata_item_product/edit-status"),

  };
}
