import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiProduct() {
  return {
    getAllProduct: createApiFunction("get", "pqr/masterdata_product/list"),
    getAllActiveProduct: createApiFunction("get", "pqr/masterdata_product/list-active"),
    createProduct: createApiFunction("post", "pqr/masterdata_product/create"),
    getProductById: createApiFunction("post", "pqr/masterdata_product/id"),
    editProduct: createApiFunction("put", "pqr/masterdata_product/edit"),
    editStatusProduct: createApiFunction("put", "pqr/masterdata_product/edit-status"),
  };
}
