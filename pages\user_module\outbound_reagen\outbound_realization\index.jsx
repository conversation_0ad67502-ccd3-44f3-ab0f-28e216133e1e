import { useEffect, useState } from "react";
import Head from "next/head"
import { <PERSON>readcrumb, Panel, Stack, Tag, Button, Modal, Form, useToaster, Table, Divider, Pagination, Input, InputGroup } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import Messages from "@/components/Messages";
import { useRouter } from "next/router";
import SearchIcon from '@rsuite/icons/Search';
import API_OutboundReagen from "@/pages/api/outbound_reagen/api_outbound_reagen";

export default function ApprovalCorrectionManagerPage() {

    const [moduleName, setModuleName] = useState("");
    const [locatorDetail, setLocatorDetail] = useState({});
    const [detailModal, setDetailModal] = useState(false);
    const [idInboundLocator, setIdInboundLocator] = useState(null);
    const [approvalStatus, setApprovalStatus] = useState(null);
    const [isActiveStatus, setIsActiveStatus] = useState(null);
    const [props, setProps] = useState([]);
    const toaster = useToaster();
    const router = useRouter();
    const [errors, setErrors] = useState({});
    const { HeaderCell, Cell, Column } = Table;
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [stagingStatus, setStagingStatus] = useState(0);

    const handleGetOutboundRealization = async (idInboundLocator) => {
        try {
            const res = await API_OutboundReagen().getOutboundRealization({ id_inbound_reagen_locator: idInboundLocator });
            console.log("outbound realization", res);
            setLocatorDetail(res.data ? res.data : []);

            if (res.status === 200) {
                setDetailModal(true);
            } else if (res.status === 400) {
                setDetailModal(false);
                toaster.push(
                    Messages("error", `Error: Invalid ID Inbound Reagen Locator!`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
            }
        } catch (error) {
            // Handle Axios errors
            setDetailModal(false);
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
        }
    }

    const handleOutboundRealization = async (idInboundLocator) => {
        try {
            const res = await API_OutboundReagen().outboundRealization({
                id_inbound_reagen_locator: idInboundLocator,
                realization_by: props.employee_id
            });

            if (res.status === 200) {
                toaster.push(
                    Messages("success", "Success Create Outbound Realization!"),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setDetailModal(false);
            } else if (res.status === 400) {
                toaster.push(
                    Messages("error", `Error: "${res.message}". Please try again later!`),
                    {
                        placement: "topCenter",
                        duration: 5000,
                    }
                );
                setDetailModal(false);
            }
        } catch (error) {
            // Handle Axios errors
            console.error("Axios Error:", error);
            toaster.push(
                Messages("error", `Error: Please try again later!`),
                {
                    placement: "topCenter",
                    duration: 5000,
                }
            );
            setDetailModal(false);
        }
    }

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setProps(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("outbound_reagen/outbound_realization")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
        }
    }, []);

    useEffect(() => {
        console.log("id lcoator", idInboundLocator)
    }, [idInboundLocator]);

    return (
        <>
            <div>
                <Head>
                    <title>Outbound Realization</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Quality</Breadcrumb.Item>
                                    <Breadcrumb.Item>Outbound Reagen</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Outbound Realization</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel bordered
                        bodyFill>
                        <h3 className="text-center mt-5">Outbound Realization</h3>
                        <div className="w-50 mx-auto my-28">
                            <p className="mb-2 font-bold">ID Outbound Reagen Locator :</p>
                            <InputGroup inside>
                                <InputGroup.Addon>
                                    <SearchIcon />
                                </InputGroup.Addon>
                                <Input
                                    type="number"
                                    value={idInboundLocator}
                                    onChange={(value) => setIdInboundLocator(parseInt(value))}
                                />
                            </InputGroup>
                            <div className="mt-3 flex justify-end">
                                <Button appearance="primary" disabled={!idInboundLocator} onClick={() => { handleGetOutboundRealization(idInboundLocator); }}>Checkout</Button>
                            </div>
                        </div>
                    </Panel>

                </div>

                <Modal
                    backdrop="static"
                    open={detailModal}
                    onClose={() => {
                        setDetailModal(false);
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Outbound Realization</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Panel bordered className="mb-3">
                            <p className="mb-2"><span className="fw-bold">ID Outbound Reagen Locator : </span>{locatorDetail.id_inbound_reagen_locator}</p>
                            <p className="mb-2"><span className="fw-bold">Reagen Name : </span>{locatorDetail.reagen_name}</p>
                            <p className="mb-2"><span className="fw-bold">Expired Date : </span>{new Date(locatorDetail.expired_date).toLocaleDateString('en-GB')}</p>
                            <p className="mb-2"><span className="fw-bold">Rack : </span>{locatorDetail.rack_desc}</p>
                            <p className="mb-2"><span className="fw-bold">Floor : </span>{locatorDetail.floor_level_desc}</p>
                            <p className="mb-2"><span className="fw-bold">Row : </span>{locatorDetail.row_level_desc}</p>
                        </Panel>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setDetailModal(false);
                                setErrors({});
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                handleOutboundRealization(locatorDetail.id_inbound_reagen_locator)
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Confirm
                        </Button>
                    </Modal.Footer>
                </Modal>

            </ContainerLayout>
        </>
    )
}
