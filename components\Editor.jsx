import dynamic from 'next/dynamic';
import { useState } from 'react';

const QuillNoSSRWrapper = dynamic(import('react-quill'), {
  ssr: false,
  loading: () => <p>Loading ...</p>,
});

const modules = {
  toolbar: [
    // [{ header: '1' }, { header: '2' }, { font: [] }],
    // [{ size: [] }],
    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
    [
      { list: 'ordered' },
      { list: 'bullet' },
      { indent: '-1' },
      { indent: '+1' },
    ],
    // ['link', 'image', 'video'],
    // ['clean'],
  ],
  clipboard: {
    // toggle to add extra line breaks when pasting HTML:
    matchVisual: false,
  },
};
/*
 * Quill editor formats
 * See https://quilljs.com/docs/formats/
 */
const formats = [
  'header',
  'font',
  'size',
  'bold',
  'italic',
  'underline',
  'strike',
  'blockquote',
  'list',
  'bullet',
  'indent',
  'link',
  'image',
  'video',
];

export default function Editor({
  contentValue,
  isReadOnly,
  valueHandler = null,
}) {
  const quillHandler = (content,x,source, editor) => {
    console.log('[content]',content)
    console.log('[x]',x)
    console.log('[source]',source)
    console.log('[editor]',editor)
    console.log('[editor]',editor.getText())
    if (valueHandler !== null) {
      valueHandler(content);
    }
  };

  return (
    <>
      <QuillNoSSRWrapper
        modules={modules}
        formats={formats}
        theme="snow"
        onChange={quillHandler}
        style={{ minWidth: '50rem' }}
        value={contentValue}
        readOnly={isReadOnly}
      />
    </>
  );
}
