import {useRouter} from 'next/router';
import {useEffect, useState} from 'react';
import MainContent from '@/components/layout/MainContent';
import {<PERSON><PERSON>, Stack, Loader} from 'rsuite';
import Table from '@/components/Table';
import ipcApi from '@/pages/api/ipcApi';
import Head from 'next/head';
import ContainerLayout from '@/components/layout/ContainerLayout';
import ModuleContentHeader from '@/components/ModuleContentHeader';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';

export default function ProductCodeData(
  {
    // allProductCode,
    // parentMenuName,
    // childMenuName,
  }
) {
  const [allProductCodeData, setAllProductCodeData] = useState([]);
  const [moduleName, setModuleName] = useState('');
  const router = useRouter();
  let path = 'masterdata/ProductCode';
  var [breadcrumbsData, setBreadcrumbsData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const MySwal = withReactContent(Swal);
  const {GetAllProductCode} = ipcApi();

  //const breadcrumbsData = [moduleName, parentMenuName, childMenuName];

  const getDataProductCode = async () => {
    setIsLoading(true);
    const {Data: productCode} = await GetAllProductCode();

    if (productCode.length > 0) {
      const setUpProductCodeData = productCode.map((item) => {
        const data = {
          id_setup: item.Id_Setup,
          product_code: item.Product_Code,
          hardness_range: `${item.Hardness_Min} - ${item.Hardness_Max} ${item.Hardness_UoM}`,
          diameter_range: `${item.Diameter_Min} - ${item.Diameter_Max} ${item.Diameter_UoM}`,
          thickness_range: `${item.Thickness_Min} - ${item.Thickness_Max} ${item.Thickness_UoM}`,
          start_date: item.Start_Date,
          end_date: item.End_Date,
          ma_range : `${item.Ma_min_weight} - ${item.Ma_max_weight} %`,
        };
        return data;
      });

      setAllProductCodeData(setUpProductCodeData);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem('dataLoginUser'));
    const moduleNameValue = localStorage.getItem('module_name');
    if (dataLogin === null || dataLogin === undefined || dataLogin === '') {
      MySwal.close();
      router.push('/');
      return;
    }

    // validate access for this page
    if (!dataLogin.menu_link_code) {
      MySwal.close();
      router.push('/dashboard');
      return;
    }
    const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
      item.includes(path)
    );
    if (validateUserAccess.length === 0) {
      MySwal.close();
      router.push('/dashboard');
      return;
    }

    if (
      moduleNameValue === null ||
      moduleNameValue === undefined ||
      moduleNameValue === ''
    ) {
      router.push('/dashboard');
      return;
    }
    const asPathWithoutQuery = router.asPath.split('?')[0];
    const asPathNestedRoutes = asPathWithoutQuery
      .split('/')
      .filter((v) => v.length);
    let routerBreadCrumbsData = [
      moduleNameValue,
      `Setup ${asPathNestedRoutes[1]}`,
      asPathNestedRoutes[2],
    ];
    setBreadcrumbsData(routerBreadCrumbsData);

    setModuleName(moduleNameValue);

    const capitalizeFirstLetter = (str) => {
      return str.charAt(0).toUpperCase() + str.slice(1);
    };
    const breadCrumbsResult = routerBreadCrumbsData.map((item) => {
      const words = item.split(/(?=[A-Z])/);
      return words.map((word) => capitalizeFirstLetter(word)).join(' ');
    });
    setBreadcrumbsData(breadCrumbsResult);
    setModuleName(moduleNameValue);

    // Get all product Code data

    // if (allProductCode.length > 0) {
    //   const setUpProductCodeData = allProductCode.map((item) => {
    //     const data = {
    //       id_setup: item.Id_Setup,
    //       product_code: item.Product_Code,
    //       hardness_range: `${item.Hardness_Min} - ${item.Hardness_Max} ${item.Hardness_UoM}`,
    //       diameter_range: `${item.Diameter_Min} - ${item.Diameter_Max} ${item.Diameter_UoM}`,
    //       thickness_range: `${item.Thickness_Min} - ${item.Thickness_Max} ${item.Thickness_UoM}`,
    //       start_date: item.Start_Date,
    //       end_date: item.End_Date,
    //     };
    //     return data;
    //   });

    //   setAllProductCodeData(setUpProductCodeData);
    // }
    getDataProductCode();
  }, []);

  return (
    <>
      <div>
        <Head>
          <title>Master Data Product Code</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <MainContent>
          <ModuleContentHeader
            breadcrumbs={breadcrumbsData}
            module_name={moduleName}
          />

          <Stack spacing={6} direction="column" alignItems="flex-start">
            <Button
              appearance="primary"
              onClick={() =>
                router.push(
                  '/user_module/masterdata/ProductCode/AddProductCode'
                )
              }
            >
              New Product Code
            </Button>

            {/* {allProductCodeData.length > 0 ? (
              <Table allProductCodeData={allProductCodeData} />
            ) : (
              <h4>No Product Code Data FOUND.</h4>
            )} */}
            {allProductCodeData.length === 0 && isLoading === true && (
              <Loader />
            )}
            {allProductCodeData.length === 0 && isLoading === false && (
              <h4>No Product Code Data FOUND.</h4>
            )}
            {allProductCodeData.length > 0 && (
              <Table allProductCodeData={allProductCodeData} />
            )}
          </Stack>
        </MainContent>
      </ContainerLayout>
    </>
  );
}

// export async function getServerSideProps({ query }) {
//   const { GetAllProductCode } = ipcApi();

//   const { Data: productCode } = await GetAllProductCode();

//   //const { parentMenuName, childMenuName } = query;
//   let allProductCode = [];
//   if (productCode != undefined) {
//     allProductCode = productCode;
//   }

//   return {
//     props: {
//       allProductCode,
//       // parentMenuName,
//       // childMenuName,
//     },
//   };
// }
