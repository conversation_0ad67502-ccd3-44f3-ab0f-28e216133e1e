import { useSession } from "next-auth/react";
import MainContent from "./layout/MainContent";
import ModuleButton from "./ModuleButton";
import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import * as faIcon from "@fortawesome/free-solid-svg-icons";
import ModuleApi from "@/pages/api/moduleApi";

export default function UserModuleContainer(props) {
  // const session = useSession();
  const [dataModule, setDataModule] = useState([]);
  const [buttonIsDisabled, setButtonIsDisabled] = useState(false);
  const { GetModule } = ModuleApi();

  //   Fungsi untuk fetcing user module
  const getModuleData = async (id) => {
    let data = await GetModule(id);
    const { Data } = data;
    setDataModule(Data);
  };

  const buttonDisabledHandler = () => {
    setButtonIsDisabled(true);
  };

  useEffect(() => {
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    if (dataLogin) {
      getModuleData(dataLogin.employee_id);
    }
  }, []);

  return (
    <MainContent>
      <h5>Select a module :</h5>
      {dataModule ? (
        dataModule.map((item) => (
          <ModuleButton
            key={item.Module_Code}
            id={item.Module_Code}
            target={item.Module_Name}
            icon={item.Module_Icon}
            isDisabled={buttonIsDisabled}
            buttonDisabledHandler={buttonDisabledHandler}
          >
            <FontAwesomeIcon
              icon={faIcon[`${item.Module_Icon}`]}
              style={{ fontSize: 15 }}
            />
            {item.Module_Name}
          </ModuleButton>
        ))
      ) : (
        <h1>No Module Found.</h1>
      )}
    </MainContent>
  );
}
