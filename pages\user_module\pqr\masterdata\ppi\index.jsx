import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, Notification, SelectPicker, Tooltip, Whisper, FlexboxGrid, RadioGroup, Radio } from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import FileDownloadIcon from "@rsuite/icons/FileDownload";

//import api
import ApiMasterdata_ppi from "@/pages/api/pqr/ppi/api_masterdata_ppi";
import ApiProduct from "@/pages/api/pqr/product/api_masterdata_product";
import { useRouter } from "next/router";

export default function MasterDataPpiPage() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_ppi");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const toaster = useToaster();
  const router = useRouter();

  const [moduleName, setModuleName] = useState("");
  const [sessionAuth, setSessionAuth] = useState(null);
  const [masterPPIDataState, setMasterPPIDataState] = useState([]);
  const [masterPPIActiveDataState, setMasterPPIActiveDataState] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showActivateModal, setShowActivateModal] = useState(false);



  const emptyMasterPPIForm = {
    ppi_name: null,
    product_code: null,
    id_ppi_copy: 0,
    wetmill: null,
    id_product: null,
  };

  const emptyEditMasterPPIForm = {
    id_ppi: null,
    ppi_name: null,
    product_code: null,
    wetmill: null,
    id_product: null,
  };


  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [addLoading, setAddLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(null);


  const [addMasterPPIForm, setAddMasterPPIForm] = useState(emptyMasterPPIForm);
  const [editMasterPPIForm, setEditMasterPPIForm] = useState(emptyMasterPPIForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedPpiId, setSelectedPpiId] = useState(null);
  const [deleteReason, setDeleteReason] = useState("");
  const [password, setPassword] = useState("");
  const [productCodeDataState, setProductCodeDataState] = useState([]);


  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = masterPPIDataState.filter((rowData, i) => {
    const searchFields = ["id_ppi", "ppi_name", "product_code", "approval_by", "approval_date", "created_date", "created_by", "updated_date", "updated_by", "deleted_date", "deleted_by", "is_active"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : masterPPIDataState.length;

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue);
    setSessionAuth(dataLogin);
    if (!dataLogin) {
      router.push(dataLogin ? "/dashboard" : "/");
    } else {
      const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("pqr/masterdata"));

      if (validateUserAccess.length === 0) {
        router.push("/dashboard");
        return;
      }
      HandleGetAllMasterPPIApi();
      HandleGetAllApiProduct()
    }
  }, []);

  const handleOpenDeleteModal = (id_ppi) => {
    setSelectedPpiId(id_ppi);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedPpiId) {
      await handleEditStatusMasterPPIApi(selectedPpiId,
        1,
        deleteReason,
        password
      );

      setShowDeleteModal(false);
      setSelectedPpiId(null);
      setDeleteReason("");
      setPassword("");
    }
  };

  const handleOpenActivateModal = (id_ppi) => {
    setSelectedPpiId(id_ppi);
    setShowActivateModal(true);
    setPassword("");
  };

  // const handleConfirmActivate = async () => {
  //   if (selectedPpiId) {
  //     await handleEditStatusMasterPPIApi(selectedPpiId, 0, "", password);
  //     setShowActivateModal(false);
  //     setSelectedPpiId(null);
  //     setPassword("");
  //   }
  // };



  const HandleGetAllMasterPPIApi = async () => {
    try {
      const res = await ApiMasterdata_ppi().getAllMasterPPI();

      console.log("res", res);
      if (res.status === 200) {
        setMasterPPIDataState(res.data);
        setMasterPPIActiveDataState(res.data);
      } else {
        console.log("error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Api", error);
    }
  };
  const HandleGetAllApiProduct = async () => {
    try {
      const res = await ApiProduct().getAllActiveProduct();
      if (res.status === 200) {
        const options = res.data.map((prd) => ({
          label: prd.product_code,
          value: prd.product_code,
          id_product: prd.id_product,
        }));
        setProductCodeDataState(options);
      } else {
        console.log("Gagal Mengambil data Kode Produk", res.message);
      }
    } catch (error) {
      console.log("Gagal Mengambil data Kode Produk", error);
    }
  };

  const HandleAddMasterPPIApi = async () => {
    const errors = {};

    // Check each field and only set the error if it's empty

    if (!addMasterPPIForm.ppi_name || addMasterPPIForm.ppi_name.trim() === "") {
      errors.ppi_name = "Nama PPI Wajib Diisi!";
    }
    if (!addMasterPPIForm.product_code) {
      errors.product_code = "Kode Produk Wajib Diisi!";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }
    try {
      setAddLoading(true);

      const res = await ApiMasterdata_ppi().createMasterPPI({
        ...addMasterPPIForm,
        created_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        setAddMasterPPIForm(emptyMasterPPIForm);
        setShowAddModal(false);
        await HandleGetAllMasterPPIApi();
        showNotification("success", "PPI baru berhasil Dibuat");
        const newIdPPI = res.id;
        router.push(`/user_module/pqr/masterdata/ppi/recipe?Id=${newIdPPI}`);
      } else if (res.status === 400) {
        if (res.message === "source PPI does not exist") {
          showNotification("error", "Gagal menambahkan PPI karena sumber tidak ditemukan.");
        } else if (res.message === "no bindings found to copy") {
          showNotification("error", "Gagal menambahkan PPI karena parameter copy kosong.");
        } else {
          showNotification("error", "Gagal menambahkan PPI karena gagal menyalin parameter");
        }
      } else {
        console.log("gagal menambah data", res.message);
        showNotification("error", "gagal menambah data");
      }
    } catch (error) {
      console.log("error gagal menambah data ", error);
      showNotification("error", "gagal menambah data");
    } finally {
      setAddLoading(false);
    }
  };

  const HandleEditMasterPPIApi = async () => {
    const errors = {};

    if (!editMasterPPIForm.ppi_name || editMasterPPIForm.ppi_name.trim() === "") {
      errors.ppi_name = "Nama PPI Wajib Diisi!";
    }
    if (!editMasterPPIForm.product_code) {
      errors.product_code = "Kode Produk Wajib Diisi!";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);

      return;
    }
    try {
      setEditLoading(true);

      const res = await ApiMasterdata_ppi().editMasterPPI({
        ...editMasterPPIForm,
        updated_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
      });

      if (res.status === 200) {
        await HandleGetAllMasterPPIApi();
        setShowEditModal(false);
        showNotification("success", "Data Berhasil diedit");
      } else {
        console.log("gagal edit data ", res.message);
        showNotification("error", "gagal mengedit data");
      }
    } catch (error) {
      console.log("gagal edit data ", error);
      toaster.push({ message: "gagal mengedit data", type: "error" });
    } finally {
      setEditLoading(false);
    }
  };

  const handleEditStatusMasterPPIApi = async (id_ppi, is_active, deleteReason, password) => {
    try {
      setLoading(true);
      const res = await ApiMasterdata_ppi().editStatusMasterPPI({
        id_ppi,
        is_active,
        delete_remarks: deleteReason,
        password: password,
        deleted_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
        employee_id: sessionAuth.employee_id,
      });


      if (res.status === 200) {

        showNotification("success", "Data PPI berhasil dihapus");
        await HandleGetAllMasterPPIApi();
        setDeleteReason("");
        setPassword("");
      } else {
        console.error("Error on update status: ", res.message);
        if (res.message === "wrong username or password") {
          showNotification("error", "Username atau password salah");
          setShowApprovalModal(true);
        } else {
          setDeleteReason("");
          setPassword("");
          console.log("gagal update status ", res.message);
          showNotification("error", `Gagal Update Status`);
        }
      }
    } catch (error) {
      console.log("gagal update status ", error);
      toaster.push({ message: "Gagal Update Status", type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const viewHandler = async (idPPI) => {
    const url = `${process.env.NEXT_PUBLIC_PIMS_FE}/user_module/pqr/masterdata/approval/pdf?idPPI=${parseInt(idPPI)}`;
    window.open(url, "_blank");
  };


  return (
    <div>
      <div>
        <Head>
          <title>Master PPI Page</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>Production</Breadcrumb.Item>
                  <Breadcrumb.Item>PQR</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>PPI</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Halaman Master PPI</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>

                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                <Column width={80} align="center" sortable fullText resizable>
                  <HeaderCell>ID PPI</HeaderCell>
                  <Cell dataKey="id_ppi" />
                </Column>
                <Column width={200} sortable fullText resizable>
                  <HeaderCell align="center">Nama PPI</HeaderCell>
                  <Cell dataKey="ppi_name" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Kode Produk</HeaderCell>
                  <Cell dataKey="product_code" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Wetmill</HeaderCell>
                  <Cell dataKey="wetmill" />
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Pembuatan</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.created_date).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dibuat oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.created_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.updated_date ? new Date(rowData.updated_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.updated_by}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>{(rowData) => (rowData.deleted_date ? new Date(rowData.deleted_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.deleted_by}</>}</Cell>
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.is_active === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Disetujui Oleh</HeaderCell>
                  <Cell dataKey="approval_by" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Disetujui Tanggal</HeaderCell>
                  <Cell>{(rowData) => (rowData.approval_date ? new Date(rowData.approval_date).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={120} fixed="right" align="center" >
                  <HeaderCell>Status Persetujuan</HeaderCell>
                  <Cell>
                    {rowData => {
                      const statusMap = {
                        3: { label: "Draft", color: "blue" },
                        2: { label: "Daftar Tunggu", color: "violet" },
                        1: { label: "Disetujui", color: "green" },
                        0: { label: "Revisi", color: "yellow" }
                      };

                      const isDeleted = rowData?.is_active === 0;
                      const status = isDeleted ? { label: "Dihapus", color: "red" } : statusMap[rowData?.status_approval] || { label: "Unknown", color: "black" };

                      let tooltipContent = null;
                      if (isDeleted) {
                        tooltipContent = rowData?.delete_remarks;
                      } else if (rowData?.status_approval === 0) {
                        tooltipContent = rowData?.revise_remarks;
                      }

                      return tooltipContent ? (
                        <Whisper placement="top" trigger="hover" speaker={<Tooltip>{tooltipContent}</Tooltip>}>
                          <Tag color={status.color}>{status.label}</Tag>
                        </Whisper>
                      ) : (
                        <Tag color={status.color}>{status.label}</Tag>
                      );
                    }}
                  </Cell>
                </Column>

                <Column width={150} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => {

                      return (
                        <div>
                          <Button
                            appearance="subtle"
                            disabled={rowData.status_approval === 1 || rowData.is_active === 0}
                            onClick={() => {
                              setShowEditModal(true);
                              setEditMasterPPIForm({
                                ...editMasterPPIForm,
                                id_ppi: rowData.id_ppi,
                                ppi_name: rowData.ppi_name,
                                product_code: rowData.product_code,
                                updated_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                                wetmill: rowData.wetmill,
                              });
                            }}
                          >
                            <EditIcon />
                          </Button>

                          <Button
                            appearance="subtle"
                            onClick={() => {
                              const id_ppi = rowData.id_ppi;

                              if (rowData.status_approval === 1 || rowData.is_active === 0) {
                                router.push(`/user_module/pqr/masterdata/ppi/pageRecipeDetail?Id=${id_ppi}`);
                              } else {
                                router.push(`/user_module/pqr/masterdata/ppi/recipe?Id=${id_ppi}`);
                              }
                            }}
                          >
                            <SearchIcon style={{ fontSize: "16px" }} />
                          </Button>

                          <Button
                            appearance="subtle"
                            onClick={() => viewHandler(rowData.id_ppi)}
                          >
                            <FileDownloadIcon />
                          </Button>


                          <Button
                            appearance="subtle"
                            disabled={!(rowData.status_approval === 3 || rowData.status_approval === 1 || rowData.status_approval === 0) || rowData.is_active === 0}
                            onClick={() => handleOpenDeleteModal(rowData.id_ppi)}
                          >
                            <TrashIcon style={{ fontSize: "16px" }} />
                          </Button>

                        </div>
                      );
                    }}
                  </Cell>
                </Column>


              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            {/* modal pop up untuk add ppi */}
            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                if (!addLoading) {
                  setShowAddModal(false);
                  setAddMasterPPIForm(emptyMasterPPIForm);
                  setErrorsAddForm({});
                }
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Tambah Master PPI</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                    <Form.Control
                      name="ppi_name"
                      value={addMasterPPIForm.ppi_name}
                      onChange={(value) => {
                        setAddMasterPPIForm((prevFormValue) => ({
                          ...prevFormValue,
                          ppi_name: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          ppi_name: undefined,
                        }));
                      }}
                      disabled={addLoading}
                    />
                    {errorsAddForm.ppi_name && <p style={{ color: "red" }}>{errorsAddForm.ppi_name}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                    <SelectPicker
                      data={productCodeDataState}
                      value={addMasterPPIForm.product_code}
                      onChange={(value) => {
                        const selectedProduct = productCodeDataState.find(item => item.value === value);

                        setAddMasterPPIForm((prevFormValue) => ({
                          ...prevFormValue,
                          product_code: value,
                          id_product: selectedProduct ? selectedProduct.id_product : null,
                        }));

                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          product_code: undefined,
                        }));
                      }}
                      style={{ width: "100%" }}
                      placeholder="Pilih"
                      disabled={addLoading}
                    />
                    {errorsAddForm.product_code && <p style={{ color: "red" }}>{errorsAddForm.product_code}</p>}
                  </Form.Group>
                  <FlexboxGrid>
                    <FlexboxGrid.Item colspan={8}>
                      <Form.Group>
                        <Form.ControlLabel>Wetmill</Form.ControlLabel>
                        <RadioGroup
                          name="wetmill"
                          inline
                          value={addMasterPPIForm.wetmill}
                          onChange={(value) => {
                            setAddMasterPPIForm((prevFormValue) => ({
                              ...prevFormValue,
                              wetmill: value,
                            }));
                            setErrorsAddForm((prevErrors) => ({
                              ...prevErrors,
                              wetmill: undefined,
                            }));
                          }}
                        >
                          <Radio value="Y">Yes</Radio>
                          <Radio value="N">No</Radio>
                        </RadioGroup>
                        {errorsAddForm.wetmill && <p style={{ color: "red" }}>{errorsAddForm.wetmill}</p>}
                      </Form.Group>
                    </FlexboxGrid.Item>
                  </FlexboxGrid>
                  <Form.Group>
                    <Form.ControlLabel>Menyalin Dari PPI yang Ada (Optional)</Form.ControlLabel>
                    <SelectPicker
                      data={masterPPIActiveDataState.map((ppi) => ({
                        label: `${ppi.ppi_name} - ${ppi.is_active === 0 ? "Inactive" :
                          ppi.status_approval === 3 ? "Draft" :
                            ppi.status_approval === 2 ? "Waiting Approval" :
                              ppi.status_approval === 1 ? "Approved" :
                                "Revised"
                          }`,
                        value: ppi.id_ppi
                      }))}
                      style={{ width: "100%" }}
                      onChange={(value) => {
                        setAddMasterPPIForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_ppi_copy: value,
                        }));
                      }}
                      placeholder="Pilih PPI yang Ada"
                    />
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddMasterPPIForm(emptyMasterPPIForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle"
                  disabled={addLoading}
                >
                  Batal
                </Button>
                <Button onClick={HandleAddMasterPPIApi} appearance="primary" loading={addLoading} disabled={addLoading}>
                  Tambah
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal pop up untuk edit ppi */}
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditMasterPPIForm(emptyEditMasterPPIForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Ubah Master PPI</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                    <Form.Control
                      name="ppi_name"
                      value={editMasterPPIForm.ppi_name}
                      onChange={(value) => {
                        setEditMasterPPIForm((prevFormValue) => ({
                          ...prevFormValue,
                          ppi_name: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          ppi_name: undefined,
                        }));
                      }}
                      disabled={editLoading}
                    />
                    {errorsEditForm.ppi_name && <p style={{ color: "red" }}>{errorsEditForm.ppi_name}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                    <SelectPicker
                      data={productCodeDataState}
                      value={editMasterPPIForm.product_code}
                      onChange={(value) => {
                        const selectedProduct = productCodeDataState.find(item => item.value === value);

                        setEditMasterPPIForm((prevFormValue) => ({
                          ...prevFormValue,
                          product_code: value,
                          id_product: selectedProduct ? selectedProduct.id_product : null,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          product_code: undefined,
                        }));
                      }}
                      style={{ width: "100%" }}
                      disabled={editLoading}
                    />
                    {errorsEditForm.product_code && <p style={{ color: "red" }}>{errorsEditForm.product_code}</p>}
                  </Form.Group>
                  <FlexboxGrid>
                    <FlexboxGrid.Item colspan={8}>
                      <Form.Group>
                        <Form.ControlLabel>Wetmill</Form.ControlLabel>
                        <RadioGroup
                          name="wetmill"
                          inline
                          value={editMasterPPIForm.wetmill}
                          onChange={(value) => {
                            setEditMasterPPIForm((prevFormValue) => ({
                              ...prevFormValue,
                              wetmill: value,
                            }));
                            setErrorsEditForm((prevErrors) => ({
                              ...prevErrors,
                              wetmill: undefined,
                            }));
                          }}
                        >
                          <Radio value="Y">Yes</Radio>
                          <Radio value="N">No</Radio>
                        </RadioGroup>
                        {errorsEditForm.wetmill && <p style={{ color: "red" }}>{errorsEditForm.wetmill}</p>}
                      </Form.Group>
                    </FlexboxGrid.Item>
                  </FlexboxGrid>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditMasterPPIForm(emptyMasterPPIForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                  disabled={editLoading}
                >
                  Batal
                </Button>
                <Button onClick={HandleEditMasterPPIApi} appearance="primary" loading={editLoading} disabled={editLoading}>
                  Simpan
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal konfirmasi hapus */}
            <Modal
              backdrop="static"
              open={showDeleteModal}
              onClose={() => {
                setShowDeleteModal(false)
                setDeleteReason("")
                setPassword("");
              }}
            >
              <Modal.Header>
                <Modal.Title>Konfirmasi Hapus</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <div style={{ marginBottom: '20px', color: 'red' }}>
                  <strong>Perhatian:</strong> Resep yang sudah dihapus tidak akan bisa dipakai kembali
                </div>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Alasan Penghapusan <span style={{ color: 'red' }}>*</span></Form.ControlLabel>
                    <Form.Control
                      name="delete_remarks"
                      value={deleteReason}
                      onChange={(value) => setDeleteReason(value)}
                      disabled={editLoading}
                    />
                    {!deleteReason && <p style={{ color: "red" }}>Alasan penghapusan harus diisi</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Password</Form.ControlLabel>
                    <InputGroup inside style={{ maxWidth: "300px" }}>
                      <Input
                        type="password"
                        placeholder="Masukkan Password"
                        value={password}
                        onChange={(value) => setPassword(value)}
                      />
                    </InputGroup>
                  </Form.Group>
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedPpiId(null)
                  setDeleteReason("")
                  setPassword("");
                }} appearance="subtle">
                  Batal
                </Button>
                <Button onClick={handleConfirmDelete} appearance="primary" color="red" disabled={!deleteReason || !password.trim()} loading={loading}>
                  Hapus
                </Button>
              </Modal.Footer>
            </Modal>



          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}
