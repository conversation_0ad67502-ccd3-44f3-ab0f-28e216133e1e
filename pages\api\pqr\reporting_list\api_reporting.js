import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](
    `${process.env.NEXT_PUBLIC_BASE_URL}/${url}`,
    data
  )
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiReporting() {
  return {
    getPPITransactionHeaderDetail: createApiFunction(
      "post",
      "pqr/reporting/ppi_byIdHeaderDetail"
    ),
  };
}
